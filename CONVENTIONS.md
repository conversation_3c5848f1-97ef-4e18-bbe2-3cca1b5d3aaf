# 约定

[TOC]

## 版本迭代

无论是功能、问题、重构、优化等，都以分支的形式推进（开发、各个环境发布），默认的情况下

- 测试环境: `dev`
- 预发布: `pre`
- 正式环境: `tag`

如果有涉及到需要拉取新分支的情况下，需要遵循下面的规则，并且发布后删除分支

- ✨ 功能: `feat/1.5.0`, `feat/points`
- 🐛 问题: `fix/1.5.1`, `fix/oom`
- 📦 重构: `refactor/1.5.1`, `refactor/comments`
- 🚀 优化: `perf/1.5.1`, `perf/lcp`

## 命名

- 文件和目录的命名统一采用：[kebab-case](https://en.wiktionary.org/wiki/kebab_case)
- 普通变量（非组件）的命名采用：[snake_case](https://en.wikipedia.org/wiki/Snake_case) ~~或者 [camelCase](https://en.wikipedia.org/wiki/Camel_case)~~
- 常量：`SNAKE_CASE`
- 类型变量：[PascalCase](https://en.wiktionary.org/wiki/Pascal_case)

## 与重构同学的协作流程

- 重构同学与开发同学共用一份代码
- 重构开发完成之后记录到变更文档，并告知开发
