import path from "path";
import fs from "fs";
import fg from "fast-glob";
import chalk from "chalk";
import { log } from "console";

/**
 * @description 校验路径是否存在
 */
const checkExist = (track) => fs.existsSync(track);

/**
 * @description 确保某个目录存在
 *
 * @param   {[type]}  target_dir  [target_dir description]
 *
 * @return  {[type]}             [return description]
 */
const ensureDirectoryExists = (target_dir) => {
  if (!checkExist(target_dir)) fs.mkdirSync(target_dir, { recursive: true });
};

/**
 * @description 提取 content_delete_reason_xx 格式的国际化词条，汇总为 json 文件, 以便在当前项目和 CMS 中使用
 */
const extractDeleteReasonOptions = async () => {
  const source_dir_pattern = "packages/static/js/i18n/merged/src/locales/lang/*.ts";
  const target_dir = "packages/nikke/public/static/configs";
  ensureDirectoryExists(target_dir);
  const target_file = path.join(target_dir, "delete_reason_options.json");

  if (checkExist(target_file)) {
    fs.unlinkSync(target_file);
  }

  const options = new Map();
  const supported_langs = new Set();

  const files = await fg(source_dir_pattern);
  const content = files.map((file) => {
    const content = fs.readFileSync(file, "utf8").replace("export default ", "");
    /** 全部国际化词条的对象 */
    const data = eval(`globalThis.a = ${content}`);

    const lang = file.match(/locales\/lang\/(.*)\.ts/)?.[1];
    supported_langs.add(lang);

    /** 遍历全部国际化词条，提取 content_delete_reason_xx 格式的词条 */
    for (const key in data) {
      if (key.startsWith("content_delete_reason_")) {
        const value = +key.match(/content_delete_reason_(\d+)/)?.[1];
        if (value || value === 0) {
          if (options.has(value)) {
            options.get(value)[lang] = data[key];
          } else {
            options.set(value, { [lang]: data[key] });
          }
        }
      }
    }
  });
  const result = {
    key: "delete_reason_options",
    description: "delete reason options",
    supported_langs: Array.from(supported_langs),
    options: Array.from(options.entries()).map(([value, i18n]) => ({
      value: +value,
      text: i18n.en,
      i18n_key: `content_delete_reason_${value}`,
      i18n,
    })),
  };
  fs.writeFileSync(target_file, JSON.stringify(result, null, 2));
  log(chalk.green("packages/nikke/public/static/configs/delete_reason_options.json 文件已生成"));
};

export { extractDeleteReasonOptions };
