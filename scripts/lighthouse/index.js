import puppeteer from "puppeteer";
import lighthouse from "lighthouse";
import { URL, fileURLToPath } from "url";
import fs from "fs";
import { join, dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const run = async (options) => {
  const { url, report_filename } = options;
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  await page.setViewport({
    isMobile: true,
    width: 390,
    height: 884,
  });

  // Navigate to the page to be tested
  const { href } = new URL(url);
  await page.goto(href);

  // Run Lighthouse test
  const { lhr } = await lighthouse(href, {
    port: new URL(browser.wsEndpoint()).port,
    output: "json",
    formFactor: "mobile",
    extraHeaders: {
      Cookies: `game_login_game=0; lip_login_game=0; OptanonAlertBoxClosed=2024-11-06T12:47:10.284Z; game_gameid=29080; game_openid=9114975017832856527; game_channelid=131; game_user_name=Player_RPOLHCAw; game_uid=548561948003407; game_adult_status=1; lip_openid=13296967573017103387; lip_channelid=131; lip_token=28ce1d80b8a5ef0203ce36cb5a3b7875fe556ce2; lip_gameid=30004; lip_ticket=b82b1c52f1721c93e55da0d0d02417ee; game_token=972f6911ee357605885d1cbb7e575a75fdc4350e; OptanonConsent=isGpcEnabled=0&datestamp=Tue+Dec+03+2024+10%3A49%3A46+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202409.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=981060aa-393f-4f25-9f73-01262c7ea4ce&interactionCount=2&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0004%3A1&AwaitingReconsent=false&intType=1&geolocation=HK%3B`,
    },
    emulatedUserAgent:
      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    screenEmulation: {
      mobile: true,
      deviceScaleFactor: 1,
      disabled: false,
      width: 390,
      height: 884,
    },
  });

  // Output the Lighthouse report to a file
  const report_path = join(__dirname, report_filename);
  fs.writeFileSync(report_path, JSON.stringify(lhr, null, 2));

  console.log(`Lighthouse report saved to ${report_path}`);

  await browser.close();
};

run({
  url: `https://pre.blablalink.com/?lang=en&gameid=29080`,
  report_filename: `./reports/lighthouse-report-${Date.now()}.json`,
});
