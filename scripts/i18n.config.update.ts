import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from "puppeteer";
import chalk from "chalk";
import fs from "fs/promises";
import path from "path";

// 类型定义 ==============================================

interface TokenResponse {
  access_token: string;
  user_id: string;
  expires_in?: number;
  refresh_token?: string;
}

interface I18nConfig {
  TENCENT_DOC: {
    AccessToken: string;
    OpenId: string;
  };
  [key: string]: any; // 允许其他配置字段
}

// 常量定义 ==============================================
const I18N_CONFIG_NAME = "i18n.config.json";
const CLIENT_ID = "b477e838b4c24d0db4c399931332b48e";
const CLIENT_SECRET = "83DSgO7GZEHb4CA79OGpx7xywix0tCMI";

// 工具函数 ==============================================
const sleep = (ms: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, ms));

// 主流程 ==============================================
async function main(): Promise<void> {
  try {
    await checkConfigFile();

    const { browser, page } = await initBrowser();

    try {
      const code = await handleOAuthFlow(page);
      const tokenData = await getAccessToken(page, code);
      await updateConfigFile(tokenData);
    } finally {
      await safeCloseBrowser(browser);
    }
  } catch (error) {
    handleError(error as Error);
  }
}

// 功能模块 ==============================================

async function checkConfigFile() {
  if (!(await checkIsConfigExists())) {
    throw new Error(`Missing i18n configuration file (${I18N_CONFIG_NAME})`);
  }
}

async function initBrowser(): Promise<{ browser: Browser; page: Page }> {
  console.log(chalk.blue("🚀 Launching browser..."));
  const browser = await puppeteer.launch({
    headless: false,
    args: ["--window-size=1200,1000"],
    defaultViewport: {
      width: 1200,
      height: 1000,
      deviceScaleFactor: 1,
    },
  });

  const page = await browser.newPage();
  return { browser, page };
}

async function handleOAuthFlow(page: Page): Promise<string> {
  const AUTH_URL = `https://docs.qq.com/oauth/v2/authorize?client_id=${CLIENT_ID}&redirect_uri=https://docs.qq.com&response_type=code&scope=all&state=STATE`;

  console.log(chalk.blue("🔑 Starting OAuth flow..."));
  await page.goto(AUTH_URL);

  await handleAgreement(page);
  await waitForLogin(page);
  return extractAuthCode(page);
}

async function handleAgreement(page: Page): Promise<void> {
  try {
    console.log(chalk.blue("📝 Handling user agreement..."));
    await page.waitForSelector(".agreement-container", {
      timeout: 120_000,
    });

    await page.click(".agreement-container input");
    await sleep(200);
    await page.click(".agree-button > button");
  } catch (error) {
    throw new Error(`Agreement handling failed: ${(error as Error).message}`);
  }
}

async function waitForLogin(page: Page): Promise<void> {
  console.log(chalk.blue("👤 Waiting for user login..."));
  try {
    await page.waitForSelector("#account-avatar-container", {
      timeout: 300_000,
    });
  } catch (error) {
    throw new Error("Login timeout. Please try again.");
  }
}

function extractAuthCode(page: Page): string {
  const url = new URL(page.url());
  const code = url.searchParams.get("code");

  if (!code) {
    throw new Error("Authorization code not found in URL");
  }

  console.log(chalk.green(`🔐 Authorization code obtained: ${chalk.yellow(code)}`));
  return code;
}

async function getAccessToken(page: Page, code: string): Promise<TokenResponse> {
  const TOKEN_URL = `https://docs.qq.com/oauth/v2/token?client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}&redirect_uri=https://docs.qq.com&grant_type=authorization_code&code=${code}`;

  console.log(chalk.blue("🔄 Exchanging for access token..."));
  await page.goto(TOKEN_URL, { waitUntil: "networkidle0" });

  const response = await parseJSONResponse(page);
  validateTokenResponse(response);

  console.log(chalk.green(`🔑 Access token obtained: ${chalk.yellow(response.access_token)}`));
  return response;
}

async function parseJSONResponse(page: Page): Promise<TokenResponse> {
  const responseText = await page.$eval("pre", (el) => el.textContent || "");

  try {
    return JSON.parse(responseText) as TokenResponse;
  } catch (error) {
    throw new Error(`Invalid JSON response: ${responseText}`);
  }
}

function validateTokenResponse(
  response: Partial<TokenResponse>
): asserts response is TokenResponse {
  const requiredFields: (keyof TokenResponse)[] = ["access_token", "user_id"];
  const missingFields = requiredFields.filter((field) => !response[field]);

  if (missingFields.length > 0) {
    throw new Error(`Missing required fields in response: ${missingFields.join(", ")}`);
  }
}

async function updateConfigFile(tokenData: TokenResponse): Promise<void> {
  console.log(chalk.blue("📁 Updating config file..."));

  const configPath = path.join(__dirname, I18N_CONFIG_NAME);
  const config = await readConfigFile(configPath);

  config.TENCENT_DOC = {
    ...config.TENCENT_DOC,
    AccessToken: tokenData.access_token,
    OpenId: tokenData.user_id,
  };

  await fs.writeFile(configPath, JSON.stringify(config, null, 2));
  console.log(chalk.green("🔄 Config file updated successfully"));
}

// 辅助函数 ==============================================

async function readConfigFile(configPath: string): Promise<I18nConfig> {
  try {
    const content = await fs.readFile(configPath, "utf8");
    return JSON.parse(content) as I18nConfig;
  } catch (error) {
    throw new Error(`Failed to read config file: ${(error as Error).message}`);
  }
}

async function safeCloseBrowser(browser: Browser): Promise<void> {
  try {
    if (browser && !browser.isConnected()) return;
    await browser.close();
    console.log(chalk.blue("🌐 Browser closed"));
  } catch (error) {
    console.warn("Warning: Failed to close browser properly");
  }
}

function handleError(error: Error): never {
  console.error(chalk.red(`\n❌ Error: ${error.message}`));
  if (error.stack) {
    console.error(chalk.gray(error.stack));
  }
  process.exit(1);
}

// 需要实现的工具函数（原JS中的未实现函数）
async function checkIsConfigExists() {
  // 实际实现需要检查文件是否存在
  try {
    await fs.access(path.join(__dirname, I18N_CONFIG_NAME));
    return true;
  } catch {
    return false;
  }
}

// 启动程序
main().catch(handleError);
