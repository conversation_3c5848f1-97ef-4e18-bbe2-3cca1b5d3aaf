// 清理 pnpm-lock.yaml、根目录的 node_modules、packages/**/node_modules

import fs from "fs/promises";
import path from "path";

const resolvePath = (name) => path.join(process.cwd(), name);

const isDirectory = async (dir_path) => {
  let bool = false;
  try {
    const stats = await fs.stat(dir_path);
    bool = stats.isDirectory();
  } catch (error) {}
  return bool;
};

const onDelete = async (dir_path) => fs.rm(dir_path, { recursive: true });

const clean = async () => {
  // 清理 pnpm-lock.yaml
  // const lock_file_path = resolvePath("pnpm-lock.yaml");
  // if (lock_file_path) {
  //   await onDelete(lock_file_path);
  // }

  // 删除当前目录下的 packages/**/node_modules 目录
  const packages_path = resolvePath("packages");
  if (await isDirectory(packages_path)) {
    const packages = await fs.readdir(packages_path);
    for (const package_name of packages) {
      const ignore_dirs = [".DS_Store"];
      if (ignore_dirs.includes(package_name)) {
        // do nothing
        continue;
      }
      const package_node_modules_path = path.join(packages_path, package_name, "node_modules");
      if (await isDirectory(package_node_modules_path)) {
        await onDelete(package_node_modules_path);
      }
    }
  }

  // 删除当前目录下的 node_modules 目录
  // const node_modules_path = resolvePath("node_modules");
  // if (await isDirectory(node_modules_path)) {
  //   await onDelete(node_modules_path);
  // }
};

clean().catch((err) => {
  console.error(err);
  process.exit(1);
});
