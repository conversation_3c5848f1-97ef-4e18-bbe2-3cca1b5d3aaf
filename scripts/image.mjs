import fs from "fs/promises";
import path from "path";
import sharp from "sharp";
import chalk from "chalk";
import { parseArgs } from "node:util";

const { argv } = process;
const TYPE = {
  src: "src",
  dist: "dist",
};

const type = argv.slice(2).pop() === TYPE.dist ? TYPE.dist : TYPE.src;
const log = console.log;
const APP_NAME = process.env.APP_NAME || "nikke";
const directory_path =
  type === TYPE.dist
    ? path.join(__dirname, `../packages/${APP_NAME}/dist`)
    : path.join(__dirname, `../packages/${APP_NAME}/src`);
const ignored_directories = new Set([".git", "node_modules", "dist", ".vscode"]);
const compare_results = {};

const checkPathExists = async (path) => {
  try {
    await fs.stat(path);
    return true;
  } catch (error) {
    if (error.code === "ENOENT") {
      return false; // 文件或目录不存在
    }
    throw error; // 其他错误抛出
  }
};

const processDirectory = async (dir) => {
  try {
    log(`[processDirectory] dir`, dir);

    const base_dir = path.basename(dir);
    if (type === TYPE.src && ignored_directories.has(base_dir)) {
      return;
    }

    const files = await fs.readdir(dir);

    for (const file of files) {
      const file_path = path.join(dir, file);
      const stats = await fs.stat(file_path);

      if (stats.isDirectory()) {
        // 如果是目录，递归处理
        await processDirectory(file_path);
      } else {
        // 如果是文件，检查文件扩展名
        const ext = path.extname(file).toLowerCase();
        if (
          // exclude
          ![".gif", ".svg", ".webp", ".avif"].includes(ext) &&
          // include
          [".jpg", ".jpeg", ".png"].includes(ext)
        ) {
          await convertToWebP(file_path);
        }
      }
    }
  } catch (error) {
    console.error(`Error processing directory ${dir}: ${error}`);
  }
};

// 转换图像到 WebP
const convertToWebP = async (file_path) => {
  try {
    const output_file_path = file_path.replace(/\.(jpg|jpeg|png)$/, ".webp");
    const is_exist_webp = await checkPathExists(output_file_path);

    // 如果要看到文件体积对比，这里可以注销
    if (is_exist_webp) {
      return;
    }

    const original_size = (await fs.stat(file_path)).size; // 获取原始文件大小
    await sharp(file_path).webp({ quality: 80 }).toFile(output_file_path);
    const webp_size = (await fs.stat(output_file_path)).size; // 获取转换后的文件大小

    compare_results[file_path] = {
      original: original_size,
      webp: webp_size,
    };

    log(`Converted ${chalk.red(file_path)} to ${chalk.green(output_file_path)}`);
  } catch (error) {
    console.error(`Error converting file ${file_path}: ${error}`);
  }
};

// 写入结果到 JSON 文件
const writeResultsToFile = async (file_path) => {
  try {
    const json = JSON.stringify(compare_results, null, 2);
    await fs.writeFile(file_path, json);
    console.log(`Results written to ${file_path}`);
  } catch (error) {
    console.error(`Error writing compare_results to file: ${error}`);
  }
};

(async () => {
  await processDirectory(directory_path);
  await writeResultsToFile(path.join(__dirname, `compare-with-webp-results.${Date.now()}.json`));
})();
