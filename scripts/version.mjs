/**
 * @description 版本管理脚本，eg: pnpm bump -v 1.9.0，会修改根目录和所有子应用的 package.json 的 version 字段并且打上 tag 提交和推送到远程，一般是在发布前执行。
 *
 * 只能通过 zx 来运行: @link https://github.com/google/zx
 * 需求地址: @link https://tapd.woa.com/QQGroupTalk/prong/tasks/view/1010019121074965082
 */

import { $ } from "zx";
import { promises as fs } from "fs";
import path from "path";
import fg from "fast-glob";
import chalk from "chalk";

const log = console.log;

/**
 * @description Parse the command-line arguments and return an object containing the options.
 * @param {string[]} args - The command-line arguments to parse.
 * @returns {Object} options - An object containing the parsed options.
 */
const parseArgs = (args) => {
  const options = {};
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg.startsWith("-")) {
      const is_version_arg = arg === "-v" || arg === "--version";
      const next_arg = args[i + 1];
      const is_next_arg_valid = next_arg && !next_arg.startsWith("-");

      if (is_version_arg && is_next_arg_valid) {
        options.version = next_arg;
        i++;
      } else if (is_version_arg) {
        log(
          chalk.bgRed.bold(`Please provide a version number, e.g., -v 1.8.5 or --version 1.8.5. \n`)
        );
        process.exit(1);
      }
    }
  }
  return options;
};

/**
 * @description Update the version number in the package.json files located in the root directory and packages/_/directories.
 * @param {string} version - The new version number to set in the package.json files.
 */
const updatePackageJsonVersions = async (version) => {
  const package_json_files = ["package.json", ...fg.sync("packages/*/package.json")];

  for (const package_json_file of package_json_files) {
    const package_json_path = path.resolve(package_json_file);
    const package_json = JSON.parse(await fs.readFile(package_json_path, "utf-8"));
    package_json.version = version;
    await fs.writeFile(package_json_path, JSON.stringify(package_json, null, 2) + "\n");
  }
};

/**
 * @description Check if the Git working directory is clean. If not, display an error message and exit the script.
 */
const checkGitStatus = async () => {
  const git_status_output = await $`git status --porcelain`;
  if (git_status_output.stdout.trim() !== "") {
    log(
      chalk.bgRed.bold(
        `Git working directory not clean. Please commit or stash your changes before running the script. \n`
      )
    );
    process.exit(1);
  }
};

/**
 * @description Check if the specified version already exists as a Git tag. If so, display an error message and exit the script.
 * @param {string} version - The version to check for existence as a Git tag.
 */
const checkIfTagExists = async (version) => {
  const git_tag_output = await $`git tag -l v${version}`;
  if (git_tag_output.stdout.trim() !== "") {
    log(
      chalk.bgRed.bold(`The tag v${version} already exists. Please choose a different version. \n`)
    );
    process.exit(1);
  }
};

const main = async () => {
  await checkGitStatus();
  const { version } = parseArgs(process.argv.slice(2));

  await checkIfTagExists(version);

  log(chalk.yellow(`Updating version to ${version}`));

  await updatePackageJsonVersions(version);
  await $`git add .`;
  await $`git commit -m 'chore: 🚢 bump v${version}'`;
  await $`git tag v${version}`;
  await $`git push`;
  await $`git push origin v${version}`;
};

main();
