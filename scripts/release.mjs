/**
 * @description 发布脚本，根据蓝盾流水线传递进入的相关参数进行构建发布
 *
 * 只能通过 zx 来运行: @link https://github.com/google/zx
 */

import { $ } from "zx";

const APP_NAME = process.env.APP_NAME || "nikke";
const PACKAGE_DIR = `packages/${APP_NAME}`;
const RELEASE_ENV = process.env.RELEASE_ENV || "test";

console.log("APP_NAME:    ", APP_NAME);
console.log("PACKAGE_DIR: ", PACKAGE_DIR);
console.log("RELEASE_ENV: ", RELEASE_ENV);

async function build() {
  if (APP_NAME) {
    cd(PACKAGE_DIR);
    await $`pnpm build:${RELEASE_ENV}`;
    return;
  }
  throw Error("APP_NAME is Empty");
}

build().catch((err) => {
  console.error(err);
  process.exit(1);
});
