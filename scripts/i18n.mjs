/**
 * @description 更新本地多语言文件
 *
 * zx 技术: @link https://github.com/google/zx
 * 多语言配置文件地址: @link https://docs.qq.com/sheet/DV2x1b2h6WXpiQXV1?tab=geiq5d
 * 需求地址: @link https://tapd.woa.com/QQGroupTalk/prong/tasks/view/1010019121074964878
 */

import { $, fs } from "zx";
import { resolve } from "node:path";
import chalk from "chalk";
import fg from "fast-glob";
import { extractDeleteReasonOptions } from "./extract-static-configs.mjs";

const log = console.log;
const SHEET_ID = "DYkpnWUxBZ0ltdE5y";

const TRACK_SHEET_TAB_MAP = {
  "packages/static/js/i18n/nikke": "BB08J2",
};

const I18N_CONFIG_NAME = "i18n.config.json";
const TRACKS = Object.keys(TRACK_SHEET_TAB_MAP);
const DEFAULT_LANG_CONTENT = `export default {}`;

// 检查 i18n 工具与配置文件是否存在，如果不存在则退出
await checkI18nToolAndConfig();

// 当前目录下的 i18n.config.json 目录
const i18n_config_data = fs.readFileSync(resolve(__dirname, I18N_CONFIG_NAME), "utf8");

/**
 * @description 校验路径是否合法
 */
const checkTrack = (track) => track && TRACKS.some((dir) => dir === track);

/**
 * @description 校验路径是否存在
 */
const checkExist = (track) => fs.existsSync(track);

/**
 * @description 检查是否缺失语言文件，有的话就生成补齐
 */
const patchMissingLangFile = (track) => {
  const local_config = JSON.parse(i18n_config_data)?.LOCAL_CONFIG;

  for (let i = 0, len = local_config.length; i < len; i++) {
    const item = local_config[i];
    const lang_track = `${track}/${item.path.split("./")[1]}`;
    if (checkExist(lang_track)) {
      log(chalk.yellow(`[${lang_track}] is exist. it will be skipped.`));
      continue;
    }

    log(chalk.green(`[${lang_track}] will be created.`));
    fs.writeFileSync(resolve(__dirname, `../${lang_track}`), DEFAULT_LANG_CONTENT);
  }

  log("\r");
};

/**
 * @description write the i18n config file
 */
const writeI18nConfig = (track) => {
  const i18n_config_track = resolve(__dirname, `../${track}/${I18N_CONFIG_NAME}`);

  if (checkExist(i18n_config_track)) {
    log(chalk.yellow(`[${track}/${I18N_CONFIG_NAME}] is not empty. it will be overwritten.`));
    // delete the i18n config file
    fs.unlinkSync(i18n_config_track);
  }
  // write the i18n config file
  fs.writeFileSync(i18n_config_track, i18n_config_data);
};

/**
 * @description 清楚某个目录下的所有文件
 *
 * @param   {[type]}  taget_dir  [taget_dir description]
 *
 * @return  {[type]}             [return description]
 */
const cleanFilesUnderDirectory = (taget_dir) => {
  // 同步读取目标目录下的所有文件
  const files = fs.readdirSync(taget_dir);
  // 遍历文件并同步删除
  files.forEach((file) => {
    const file_path = path.join(taget_dir, file);
    fs.unlinkSync(file_path);
  });
};

/**
 * @description 确保某个目录存在
 *
 * @param   {[type]}  taget_dir  [taget_dir description]
 *
 * @return  {[type]}             [return description]
 */
const ensureDirectoryExists = (taget_dir) => {
  if (!checkExist(taget_dir)) fs.mkdirSync(taget_dir, { recursive: true });
};

/**
 * @description 拉取多语言文件
 *
 * @param   {[type]}  track  [track description]
 *
 * @return  {[type]}         [return description]
 */
const run = async (track) => {
  if (!checkTrack(track)) {
    return;
  }

  writeI18nConfig(track);
  patchMissingLangFile(track);
  const sheet_tab = TRACK_SHEET_TAB_MAP[track];

  log(chalk.blue("SHEET_ID: ") + chalk.green(SHEET_ID));
  log(chalk.blue("sheet_tab: ") + chalk.green(sheet_tab));
  log("\r");

  try {
    await $`
      cd ${track} &&
      pwd ${track} &&
      i18n-cli -v &&
      i18n-cli translate -e ${SHEET_ID} -s ${sheet_tab} &&
      i18n-cli pull:replace -e ${SHEET_ID} -s ${sheet_tab} &&
      prettier ./src/locales/lang/* --write
    `;
  } catch (error) {
    log(chalk.red(error));
  }
};

/**
 * @description 格式化多语言
 *
 * @return  {[type]}  [return description]
 */
const format = async () => {
  try {
    await $`
      cd ./packages/static/js/i18n &&
      pwd ./ &&
      prettier ./* --write
    `;
  } catch (error) {
    log(chalk.red(error));
  }
};

const findSameKeys = (o1, o2) => {
  const result = [];
  const { file_name: o1_file_name, data: o1_data } = o1;
  const { file_name: o2_file_name, data: o2_data } = o2;

  for (const key in o1_data) {
    if (o2_data[key] && o2_data[key] !== o1_data[key]) {
      result.push({ key, [o1_file_name]: o1_data[key], [o2_file_name]: o2_data[key] });
    }
  }

  return result;
};

/**
 * @description 把所有的应用的语言文件合并到一个文件中
 *
 * @return  {[type]}  [return description]
 */
const merged = async () => {
  // 定义源目录和目标目录
  const source_dir_pattern = "packages/static/js/i18n/*/src/locales/lang/*";
  const ignore_dirs = ["**/videos/**", "**/news/**"];

  const target_dir = "packages/static/js/i18n/merged/src/locales/lang";
  ensureDirectoryExists(target_dir);
  cleanFilesUnderDirectory(target_dir);

  const objectify = (str) => {
    const regex = /{[\s\S]*}/;
    return eval(`(${str.match(regex)?.[0]})`);
  };

  const same_key_result = [];
  const same_key_file_path = "packages/static/js/i18n/merged/same_key_result.txt";

  // 查找所有匹配的文件
  fg(source_dir_pattern, { ignore: ignore_dirs }).then(async (files) => {
    const sort_order = ["share-components", "ugc", "app", "points", "common"];
    const sorted_files = files.sort((a, b) => {
      const a_order = sort_order.findIndex((dir) => a.includes(dir));
      const b_order = sort_order.findIndex((dir) => b.includes(dir));

      if (a_order === b_order) {
        return a.localeCompare(b);
      }
      return a_order - b_order;
    });

    // 遍历所有匹配的文件
    sorted_files.forEach((source_file) => {
      // 获取文件名（如 zh.ts）
      const language = path.basename(source_file);
      const regex = /{[\s\S]*}/;

      // 读取源文件内容并解析为 JavaScript 对象
      const source_content_raw = fs.readFileSync(source_file, "utf8");

      // 读取目标文件（如果存在）
      const target_file = path.join(target_dir, language);
      if (!checkExist(target_file)) {
        fs.writeFileSync(target_file, DEFAULT_LANG_CONTENT);
      }

      let target_content_raw = fs.readFileSync(target_file, "utf8");

      same_key_result.push(
        ...findSameKeys(
          {
            file_name: target_file,
            data: objectify(target_content_raw),
          },
          {
            file_name: source_file,
            data: objectify(source_content_raw),
          }
        )
      );

      // 合并内容
      const merged_content = Object.assign(
        {},
        objectify(target_content_raw),
        objectify(source_content_raw)
      );

      // 将合并的内容转换为 TypeScript 格式并写入目标文件
      const merged_content_string = `export default ${JSON.stringify(merged_content, null, 2)};`;
      fs.writeFileSync(target_file, merged_content_string, "utf8");
    });

    // 把相同 key 的结果写到本地文件
    fs.writeFileSync(same_key_file_path, JSON.stringify(same_key_result, null, 2));
  });
};

/** 检查 i18n 工具是否安装 */
async function checkIsI18nToolInstalled() {
  try {
    const res = await $`i18n-cli -v`.quiet();
    return true;
  } catch (error) {
    return false;
  }
}

/** 检查 i18n 配置文件是否存在 */
function checkIsConfigExists() {
  return fs.existsSync(resolve(__dirname, I18N_CONFIG_NAME));
}

/** 检查 i18n 工具与配置文件是否存在，如果不存在则退出 */
async function checkI18nToolAndConfig() {
  if (!(await checkIsI18nToolInstalled())) {
    log(
      chalk.red(
        "Error: The @tencent/i18n-cli is not installed globally. Please install it using the following command: "
      ) + chalk.yellow("npm install -g @tencent/i18n-cli")
    );
    process.exit(1);
  }
  if (!checkIsConfigExists()) {
    log(
      chalk.red("Error: Missing i18n configuration file (") +
        chalk.yellow(I18N_CONFIG_NAME) +
        chalk.red("). Please contact the main developer.")
    );
    process.exit(1);
  }
}

(async () => {
  await Promise.all(TRACKS.map(run));
  await merged();
  await format();
  await extractDeleteReasonOptions();
})();
