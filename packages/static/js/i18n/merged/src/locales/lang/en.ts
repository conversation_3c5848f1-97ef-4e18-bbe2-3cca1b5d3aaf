export default {
  purchase_limit_day: "Daily",
  purchase_limit_week: "Weekly",
  purchase_limit_month: "Monthly",
  redeem_deatil: "Redeem Detail",
  records: "Records",
  server: "Server",
  choose_server: "Select Server",
  character: "Character",
  choose_character: "Select Character",
  choose_character_tips: "Please select a game character",
  choose_region: "Select Region",
  all: "All",
  gain: "Earned",
  redeemed: "Redeemed",
  redeem: "Redeem",
  overdue: "Expired",
  redeemed_x: "Redeemed {0}",
  failed_to_redeem_tips: "Failed to redeem {0}, refunding Coins...",
  expired: "Expired",
  gift: "Gift",
  participate_activity: "Participate in activities",
  lucky_draw_reward: "Total Lucky Draw Rewards",
  out_post: "Outpost",
  nikke_art: "NikkeArt",
  creator_hub: "Creator Hub",
  event: "Event",
  follow: "Follow",
  is_following: "Following",
  please_check_in_tomorrow: "Sign in again tomorrow.",
  check_in_tomorrow: "Sign in tomorrow to get another {0}Coins!",
  play_games_tips: "Log in to the game to earn {0} Coins every day",
  task_tips: "About the Quests",
  play_game_tomorrow: "Log in with your LEVEL INFINITE PASS account tomorrow to receive {0} Coins!",
  please_play_game_tomorrow:
    "Log in with your LEVEL INFINITE PASS account tomorrow to receive more Coins!",
  check_in_fail: "Sign In Failed",
  check_in_suc: "Sign In Success",
  game_login: "Play Game",
  confirm: "Confirm",
  check_in: "Sign In",
  daily_quests: "Daily Quests",
  available: "Available",
  total: "Total Spent",
  points_no_enouch: "Not enough coins.",
  back: "Back",
  login: "Log In",
  logout: "Log Out",
  privacy: "Privacy Policy",
  cookies: "Cookie Policy",
  contact_us: "CONTACT US",
  privacyLink: "https://nikke-en.com/privacypolicy/",
  cookiesLink: "https://nikke-en.com/cookiepolicy/",
  rareness: "Rarity",
  team: "Squad",
  attr_code: "Code",
  element: "Code",
  weapon: "Weapon",
  position: "Class",
  class: "Class",
  coporation: "Manufacturer",
  coperation: "Manufacturer",
  brust_stage: "Burst",
  weapontype: "Weapon",
  login_button: "Login Now",
  nav_nikke_list: "Nikkepedia",
  nav_coming: "Coming soon",
  nav_gatcha: "Gacha Record",
  nav_main: "Homepage",
  nav_scene_list: "Campaign List",
  switch_role: "Switch Character",
  select_server_title: "Server Selection",
  select_server_desc: "Please select a server.",
  select_server_no_role: "Character not found in the server.",
  select_server_loading: "Searching",
  select_server_nextstep: "Next",
  select_role_title: "Character Selection",
  select_role_desc: "Please confirm your nickname.",
  select_role_confirm: "Confirm",
  select_role_reselect_server: "Please select another aserver.",
  main_firstbind_tip1: "Get Recruit Voucher ×1 the first time you link a character.",
  main_firstbind_tip2: "-",
  main_basic_info: "Basic Info",
  main_remain_defense: "Remaining Attempts",
  main_rookie_battle: "Rookie Arena",
  main_everyday_freetime: "Daily Free Attempts",
  main_special_battle: "SP Arena",
  main_stage: "Campaign",
  main_tower: "Towers",
  main_layer: "Floor",
  main_power_nikke: "Nikke with the Highest Power",
  main_power_total: "Total Power",
  main_nikke_num: "NIKKEs",
  main_avatar_frame: "Avatar Frames",
  main_costume: "Costumes",
  main_last_login: "Last Online",
  main_outpost: "Outpost Info",
  main_material: "My Resources",
  main_collapse: "Collapse",
  main_expand: "Expand",
  elysion: "Elysion",
  attacker: "Attacker",
  tetra: "Tetra",
  defender: "Defender",
  pilgrim: "Pilgrim",
  supporter: "Supporter",
  missilis: "Missilis",
  abnormal: "Abnormal",
  elysion_level: "Elysion",
  attacker_level: "Attacker",
  tetra_level: "Tetra",
  defender_level: "Defender",
  pilgrim_level: "Pilgrim",
  supporter_level: "Supporter",
  missilis_level: "Missilis",
  missills_level: "Missilis",
  sychro_level: "Synchro Level",
  abnormal_level: "Abnormal",
  recyle_level: "Recycling Room Level",
  character_type_fire: "Fire",
  character_type_electronic: "Electric",
  character_type_water: "Water",
  character_type_iron: "Iron",
  character_type_wind: "Wind",
  ar: "AR",
  mg: "MG",
  smg: "SMG",
  sg: "SG",
  sr: "SR",
  rl: "RL",
  filter: "Filter",
  close: "Close",
  sort: "Sort",
  nikke_list_tab_all: "Nikkepedia",
  nikke_list_tab_player: "My NIKKEs",
  nikke_power: "Power",
  nikke_level: "Level",
  nikke_break: "Limit Break",
  nikke_attract_level: "Bond Level",
  nikke_rarity: "Rarity",
  scene_tab_main: "Main Story",
  scene_tab_event: "Brief Encounter",
  scene_tab_archive: "Archives",
  scene_unlock_tip: "The story is still locked",
  cv_key_en: "English",
  cv_key_ko: "Korean",
  cv_key_ja: "Japanese",
  use_material_upgrade_nikke: "You can use materials to upgrade NIKKEs",
  fight_attr: "Power",
  hp_attr: "HP",
  atk_attr: "ATK",
  def_attr: "DEF",
  upgrade_material: "Upgrade Materials",
  upgrade_core_material: "Core Enhancement Materials",
  equipment: "Equipment",
  no_effects: "No Effects",
  skill: "Skill",
  attract_scene: "Nikke Stories",
  attractive_level_unlock: "Unlocks at Bond Lv.  {level}",
  dialogue: "Character Lines",
  game_name: "GODDESS OF VICTORY：NIKKE",
  game_type: "RPG Shooter",
  game_os: "iOS/Android",
  equip_effect: "Change Equipment Effects",
  equip_stat: "这里理论上是直接显示T1-T9以及overload的，应该没有对装备星级的文字描述",
  no_effect: "No Effects",
  no_equip: "No Equipment",
  max_ammo: "Ammo Capacity",
  reload_time: "Reload Time",
  op_type: "Control Mode",
  buff_detail: "Attribute",
  debuff: "Debuff",
  game_year: "Release：2022",
  dialogue_hint: "This is the VO gallery of {chapter_name}.",
  chapter: "Chapter.{num}",
  default: "Default",
  enter_battle_hint: "The effect will be enabled when entering a battle.",
  filter_name_placeholder: "Search Nikke name",
  clear_btn: "Clear",
  archive_tip: "Story Events can be viewed.",
  server_jp: "Japan",
  server_na: "NA",
  server_global: "Global",
  server_kr: "Korea",
  server_sea: "SEA",
  date_attemp_time: "Today's Participation Count",
  no_data: "No data available",
  data_sync_tip:
    "ShiftyPad is in testing with these limitations:\n1) After game version updates, ShiftyPad needs 0.5-1 day to sync new resources.\n2) In-game data changes need 1 hour for synchronization.\n3) Nikke's combat power may exhibit minor discrepancies.\n4) By default, other users cannot view your [My Resources] and [My Nikkes]. You can configure this by clicking the shield-shaped icon in the upper right corner.\nWe appreciate your understanding as we work on further optimizations!",
  notice: "Note",
  networkError: "Network Error",
  choice: "Selection",
  help_tip:
    "This is SHIFTYPAD. You can use the Ark Assistant system in the SHIFTYPAD to search for information you need, but please refrain from disclosing your personal information to others. Hope this will be of help to you!",
  license_agreement: "License Agreement",
  input_type_up: "Charge",
  input_type_down: "Normal",
  ar_desc: "Well-balanced weapon suitable in all kinds of situations.",
  smg_desc: "Its extreme rapid fire makes up for its relatively short range.",
  sg_desc: "Fires multiple shots at once and is extremely powerful in close range.",
  sr_desc:
    "High accuracy shooting from long range makes it perfect for targeting specific enemy parts.",
  rl_desc: "Deals damage with a wide area of effect, great for attacking groups of enemies.",
  mg_desc:
    "Its high ammo capacity and rapid-firing feature make it perfect for dealing with swarming enemies.",
  game_gold: "Credit",
  character_exp: "Battle Data Set",
  character_exp_2: "Core Dust",
  game_outpost_lobby: "Lobby",
  game_outpost_forepost: "Outpost",
  game_outpost_grow: "Obtain and Upgrade",
  game_outpost_wild: "Field",
  game_outpost_battle: "Battle",
  html_title: "Welcome to SHIFTYPAD!",
  order_pending: "Delivering",
  order_error: "Error",
  order_complete: "Completed",
  order_closed: "Order closed",
  order_not_begin: "Not enough coins or stock, order has not been fulfilled",
  deduct_points_err: "Please refresh the page or contact customer service.",
  send_commodity_err: "Please refresh the page or contact customer service.",
  gift_package_distribution_completed: "Please check your in-game Mail for the item redeemed.",
  deduct_points_failed_has_rollback: "Coins has been refunded.",
  send_commodity_failed_has_rollback: "Coins has been refunded.",
  rollback_points_error: "Please contact customer service.",
  send_zero_price_commodity_err: "0-Coin item delivery error",
  role_not_found: "Character not found",
  purchase_limit: "Limited time offer",
  purchase_user_limit: "The personal limit has been reached.",
  share: "Share",
  success_redeemed_tips:
    "Please confirm order status and check your in-game mail to claim your reward.",
  order_status: "Status",
  trading_hours: "Time",
  consumption_points: "Total Spent",
  coin: "Coins",
  trade_name: "Item",
  order_no: "Order Number",
  redeemed_check_mailbox:
    "Please confirm order status and check your in-game mail to claim your reward.",
  failed_redeemed: "Failed to redeem",
  success_redeemed: "Redeem Success",
  redeem_rewards: "Redeem Rewards",
  loading_role: "Loading",
  reward: "Reward",
  following: "Following",
  follower: "Followers",
  his_following: "Following",
  his_follower: "Followers",
  edit_nickname_tips: "Please don't use your real name",
  game_tag: "Game Tag",
  graphics: "Image",
  graphics_and_text: "Post",
  video: "Video",
  publish: "Post",
  title: "Title ",
  show_me_posts_desc: "Show My Posts on Profile Page",
  show_me_collection_desc: "Show My Favorites on Profile Page",
  show_me_fans_desc: "Show My Followings on Profile Page",
  show_me_game_card_desc: "Show My Followers on Profile Page",
  receive_tweet_emial_desc: "I agree to receive emails about the latest news",
  msg_comment_notify_desc: "New Comments/ Replies",
  msg_like_notify_desc: "New Likes",
  msg_follow_notify_desc: "New Followers",
  msg_system_notify_desc: "System Notifications",
  msg_activity_notify_desc: "Event Notifications",
  max_image_limit_size_tips: "Maximum upload: {0}",
  file_upload_failed: "File upload failed",
  manage: "Manage",
  home_links: "Link",
  links_manage: "Third-Party Link Management",
  personal_setting: "Profile",
  ads_setting: "Promotions",
  more: "More",
  please_paste_the_link_here: "Enter link",
  sure: "Sure",
  loading: "Loading",
  video_parse_failed: "Failed. Please enter a Youtube or TikTok link.",
  bind_game_character_tips: "Link a game character.",
  bind_lip_tips:
    "Please link a game character with your LEVEL INFINITE PASS account to keep redeeming coins through the event.",
  bind_lip_title: "Link Accounts",
  my_posts: "Posts",
  my_comments: "Comments",
  my_favorites: "Favorites",
  his_posts: "Posts",
  his_comments: "Comments",
  his_favorites: "Favorites",
  delete_post: "Delete Post",
  delete_comment: "Delete Comment",
  post_successfully: "Post Success",
  nickname: "Nickname",
  signature: "Signature",
  abandon_the_post_title: "Give up editing the post?",
  abandon_the_post_content: "If you abandon now, the post will be lost",
  abandon_the_post_confirm_text: "Continue Editing",
  abandon: "Abandon",
  alert: "Note",
  content_will_no_be_saved: "The current content will not be saved",
  keep_the_current_content: "Keep the current content in the new section?",
  keep: "Keep",
  replace_video: "Replace the Video",
  create_post: "Create a Post",
  support: "Support",
  video_link: "Video Link",
  replace_the_video: "Replace the Video",
  replace_the_video_content:
    "All content will be removed after deletion, and the video URLs will be replaced.",
  cancel: "Cancel",
  insert_picture: "Insert Image",
  picture: "Image",
  camera: "Camera",
  editor_default_placeholder: "Please enter a description ",
  required: "Required",
  reach_comment_list_bottom: "All comments displayed",
  comment: "Comment",
  reply: "Replies",
  send: "Send",
  notification_setting: "Notification Settings",
  related_to_me: "About Me",
  notifications: "Notifications",
  delete_successfully: "Deleted",
  are_you_sure_to_delete: "This will be permanently deleted. Do you want to confirm?",
  delete: "Delete",
  select_reason_for_reporting: "Reason for report",
  unfollowed: "Unfollow",
  are_you_sure_to_unfollow: "Are you sure you want to unfollow this account?",
  keep_follow: "Keep Following",
  unfollow: "Unfollow",
  post: "Post",
  logging_out: "Logging out",
  could_not_find_any_results: "Sorry, no results found",
  link_required: "Link (Required)",
  description_optional: "Description (Optional)",
  min_before: "minutes ago",
  hour_before: "hours ago",
  posted: "Posted",
  there_is_not_anything: "Nothing to show here.",
  come_to_grab: "Be the first to share your thoughts!",
  first_comment: "First comment",
  poster_does_not_reply: "The contributor has not replied yet.",
  hot: "Popular",
  latest: "Recent",
  share_my_opinion: "Share your thoughts...",
  please_enter: "Please Enter",
  battle_hard: "Campaign (Hard)",
  battle_normal: "Campaign (Normal)",
  author: "Author",
  user_status: "Personal Status",
  recommend: "Recommended",
  first_time_binding_a_role_can_earn: "Get Recruit Voucher ×1 the first time you link a character.",
  bind: "Link",
  not_used_recently: "Not used recently",
  insert_link: "Insert Link",
  search_more_topics: "Search Topics",
  posts: "Posts",
  all_regions: "All Regions",
  comment_successfully: "Comment Success",
  original_text: "Original article",
  all_comments: "All Comments",
  reporting: "Report",
  editing: "Edit",
  move_to: "Move the post to",
  actions: "Option",
  advertise: "Advertising",
  suspected_of_swiping_credit_card: "Spamming",
  suspected_of_uncivilized_behavior: "Inappropriate behavior",
  suspected_of_spreading_false_information: "Spreading false information",
  suspected_trading_or_gaming_services: "Providing inappropriate trading or other services",
  others: "Other",
  module: "Module",
  area: "Area",
  move_contents_to: "Move the post to",
  post_detail: "Post Detail",
  report_successfully: "Report Success",
  error_video_platform_parse_tips: "Failed. Please enter a Youtube or TikTok link.",
  exceed_max_topic_tips: "Select up to {0} topics",
  move_successfully: "Move Success",
  clear_all: "Clear All",
  invalid_link: "Invalid Link",
  enter_valid_url_tips: "Please enter a valid URL",
  enter_channel_url_tips: "Please enter the link for the corresponding channel",
  select_publishing_section: "Select the Section",
  select_publishing_area: "Select the region",
  select_publishing_lang: "Select the Language",
  show_my_posts: "Show My Posts on Profile Page",
  show_my_collection: "Show My Favorites on Profile Page",
  show_my_follow: "Show My Followings on Profile Page",
  show_my_fans: "Show My Followers on Profile Page",
  receive_tweet_email: "Receive Promotion Emails",
  account_setting: "Account Settings",
  account_management: "Account Management",
  private_setting: "Privacy Settings",
  language_setting: "Language Settings",
  about: "About {0}",
  terms_of_service: "{0} Terms of Service",
  privacy_policy: "{0} Privacy Policy",
  li_pass_terms_of_service: "LEVEL INFINITE PASS Terms of Service",
  li_pass_privacy_policy: "LEVEL INFINITE PASS Privacy Agreement",
  nikke_channel: "NIKKE Official Server",
  li_pass_channel: "LI Pass Official Server",
  nikke_official: "NIKKE Official X",
  li_pass_official: "LEVEL INFINITE PASS Official X",
  nikke_official_group: "NIKKE Official Facebook Group",
  li_pass_official_group: "LEVEL INFINITE PASS Official Facebook Group",
  audit_username: "Nickname under review",
  audit_remark: "Signature under review",
  basic_info: "Basic Info",
  account_links: "Link Account",
  my_account: "My Account",
  country: "Country/Region",
  compose_comment_img_limit_tips: "Can only insert one image",
  reward_center: "Reward Center",
  sign_in_failed: "Sign In Failed",
  sign_in_failed_tips: "You have already signed in today.",
  edit_my_account: "Edit My Profile",
  edit_nickname: "Edit Nickname",
  save: "Save",
  edit_signature: "Edit Signature",
  login_banner_tips: "Register and log in for the first time to receive gift packs",
  battle: "Campaign",
  normal_tag: "NORMAL",
  hard_tag: "HARD",
  select_role_to_get_reward: "Select your game character and get LEVEL INFINITE PASS gifts!",
  api_code_1001009: "Mission completed.",
  api_code_1100010: "Redemption limit exceeded.",
  api_code_1100006: "Cannot be redeemed yet.",
  api_code_1100007: "Can no longer be redeemed.",
  api_code_1102001: "Not enough coins.",
  api_code_err_network: "Network error",
  api_code_1200002: "Failed to verify news content.",
  api_code_1200003: "Failed to create review.",
  api_code_1200004: "Failed to update review.",
  api_code_1200005: "HTTP request returned an empty object.",
  api_code_1200006: "HTTP request returned an error.",
  api_code_1200007: "HTTP request returned an error status code.",
  api_code_1200008: "x-common-params is empty",
  api_code_1200009: "Failed to deserialize x-common-params",
  api_code_1200010: "Failed to parse rich text.",
  api_code_1200011: "Failed to publish. Title exceeds 50 characters.",
  api_code_1200012: "Failed to publish. Content exceeds 10,000 characters.",
  api_code_1200013: "Failed to publish. Content exceeds 1,000 characters.",
  api_code_1200014: "Image size exceeds the limit.",
  api_code_1200015: "Rich text content contains illegal images",
  api_code_1200016: "Failed to retrieve black list and white list.",
  api_code_1200017: "Failed to publish, got blacklisted",
  api_code_1200018: "Posts too frequent. Please try again later.",
  api_code_1200019: "Posts too frequent. Please try again later.",
  api_code_1200020: "Posts too frequent. Please try again later.",
  api_code_1200021: "Failed to retrieve the main content of the rich text.",
  api_code_1200022: "The post does not exist",
  api_code_1200023: "Failed to post.",
  api_code_1200024: "Failed to retrieve post details.",
  api_code_1200025: "Failed to delete post.",
  api_code_1200026: "Failed to retrieve user data.",
  api_code_1200027: "Not authorized to delete post.",
  api_code_1200037: "Updating post in ES failed after cms request",
  api_code_1200038: "Official account missing",
  api_code_1200039: "cms failed to notify the deletion of messages under information",
  api_code_1200051: "The release time needs to be at least 15 minutes later than the current time",
  api_code_1200052: "Not authorized.",
  api_code_1200053: "Error updating post visibility.",
  api_code_1200054: "Error retrieving post approval information.",
  api_code_1200055: "Error retrieving post content.",
  api_code_1200056: "Error getting the dynamic list on the homepage",
  api_code_1200057: "Failed to retrieve topic.",
  api_code_1200058: "Failed to create topic favorites.",
  api_code_1200059: "Failed to delete topic favorites.",
  api_code_1200060: "Failed to retrieve user's article favorites status.",
  api_code_1200061: "Information not reviewed does not want to allow collection",
  api_code_1200062: "Failed to create news favorite.",
  api_code_1200063: "Failed to cancel news favorite.",
  api_code_1200064: "User add/remove favorite actions are too frequent.",
  api_code_1200065: "Failed to retrieve favorited post list.",
  api_code_1200066: "Failed to retrieve comment status.",
  api_code_1200067: "Failed to retrieve news like status.",
  api_code_1200068: "Failed to like user.",
  api_code_1200069: "Failed to unlike.",
  api_code_1200070: "User like/unlike actions are too frequent.",
  api_code_1200071: "Failed to create topic.",
  api_code_1200072: "Failed to create topic review flow",
  api_code_1200073: "Failed to retrieve user's follow list.",
  api_code_1200074: "User not found.",
  api_code_1200075: "Failed to retrieve user's follow list.",
  api_code_1200076: "Failed to follow user.",
  api_code_1200077: "Failed to unfollow.",
  api_code_1200078: "User follow/unfollow actions are too frequent.",
  api_code_1200079: "Failed to retrieve title.",
  api_code_1200080: "Message daily limit reached.",
  api_code_1200081: "Failed to retrieve user's first login points message",
  api_code_1200082: "Failed to add new message.",
  api_code_1200083: "Failed to retrieve article list.",
  api_code_1200084: "Failed to retrieve comment list.",
  api_code_1200085: "Incorrect report type",
  api_code_1200086: "Cannot report yourself.",
  api_code_1200087: "Failed to create report data.",
  api_code_1200088: "Comment limit per minute reached.",
  api_code_1200089: "Comment limit per hour reached.",
  api_code_1200090: "Daily comment limit reached.",
  api_code_1200091: "Failed to post comment.",
  api_code_1200092: "Failed to post comment. This post is under review.",
  api_code_1200093: "Exceed maximum number of comments",
  api_code_1200094: "Failed to reply.",
  api_code_1200095: "Failed to reply. This post is under review.",
  api_code_1200096: "Information not reviewed when creating a reply to a reply",
  api_code_1200097: "Comment under review. Cannot be liked at the moment.",
  api_code_1200098: "Failed to retrieve comment likes.",
  api_code_1200099: "Failed to like comment.",
  api_code_1200100: "Failed to unlike comment.",
  api_code_1200101: "Like limit for comments reached.",
  api_code_1200102: "No comments.",
  api_code_1200103: "Parameter error.",
  api_code_1200104: "Failed to retrieve white list.",
  api_code_1200105: "Failed to retrieve blocked list and white list reviews.",
  api_code_1200106: "Failed to retrieve user information.",
  api_code_1200107: "Failed to retrieve user first registration status.",
  api_code_1200108: "Failed to update user privacy agreement.",
  api_code_1200109: "Failed to retrieve title.",
  api_code_1200110: "Random nickname reaches the maximum limit",
  api_code_1200111: "Failed to create user nickname.",
  api_code_1200112: "Failed to retrieve avatar list.",
  api_code_1200113: "User registration failed.",
  api_code_1200114: "Failed to update user information.",
  api_code_1200115: "Failed to update user nickname.",
  api_code_1200116: "Nickname review is missing the corresponding OpenID.",
  api_code_1200117: "New nickname under review.",
  api_code_1200118: "User signature missing corresponding OpenID.",
  api_code_1200119: "New signature under review.",
  api_code_1200120: "Nickname already exists.",
  api_code_1200121: "Nickname exceeds length limit.",
  api_code_1200122: "Nickname does not meet requirements.",
  api_code_1200123: "User is underage.",
  api_code_1200124: "Failed to retrieve review data list.",
  api_code_1200125: "Failed to update user information.",
  api_code_1200126: "Failed to update user signature.",
  api_code_1200127: "Link error.",
  api_code_1200128: "Avatar link parsing error.",
  api_code_1200129: "Error getting the list of games linked to the avatar",
  api_code_1200130: "Failed to retrieve comment content.",
  api_code_1200131: "Failed to retrieve reply list.",
  api_code_1200132: "Failed to deserialize the JSON of the internal message",
  api_code_1200133: "Failed to serialize internal message to json",
  api_code_1200134: "Failed to write the message into the queue",
  api_code_1200135: "Failed to retrieve cache for internal messages",
  api_code_1200136: "Failed to retrieve internal message information",
  api_code_1200137: "Failed to create user internal message",
  api_code_1200138: "Failed to change the status of the internal message",
  api_code_1200139: "Push type does not exist.",
  api_code_1200140:
    "Failed to change the status of importing the user number package for internal messages",
  api_code_1200141: "Table name does not exist",
  api_code_1200142: "Failed to retrieve all users",
  api_code_1200143: "Failed to read the internal signal code package",
  api_code_1200144: "Failed to download file.",
  api_code_1200145: "In-site message kafka write failed",
  api_code_1200146: "Failed to update post like count.",
  api_code_1200147: "An exception occurred while obtaining COS configuration information",
  api_code_1200148: "Failed to retrieve message list.",
  api_code_1200149: "User privacy switch deserialization switch failed",
  api_code_1200150: "Failed to retrieve user privacy policy switch",
  api_code_1200151: "User privacy policy switch type is invalid",
  api_code_1200155: "Video link parsing error",
  api_code_1200156: "Failed to retrieve video address",
  api_code_1200157: "Failed to parse video address",
  api_code_1200158: "Failed to retrieve Kong configuration",
  api_code_1200159: "An error occurred while retrieving the emoji list.",
  api_code_1200160: "Failed to retrieve the list of emoji associations",
  api_code_1200161: "Failed to set user status.",
  api_code_1200162: "Failed to re-follow user.",
  api_code_1200163: "Failed to re-favorite the post.",
  api_code_1200164: "Failed to parse post like information.",
  api_code_1200165: "Failed to retrieve post like information.",
  api_code_1200166: "Failed to update muted user information.",
  api_code_1200167: "Failed to delete muted user information.",
  api_code_1200168: "Failed to update administrator user information.",
  api_code_1200169: "Failed to delete administrator user information.",
  api_code_1200170: "Failed to update certified user information.",
  api_code_1200171: "Failed to delete verified user information.",
  api_code_1200172: "Duplicate report.",
  api_code_1200173: "Failed to update post content.",
  api_code_1200174: "Failed to update report content",
  api_code_1200175: "Failed to update user review data record.",
  api_code_1200176: "Failed to move post.",
  api_code_1200177: "Language not supported",
  api_code_1200178: "Failed to retrieve user NIKKE game information.",
  api_code_1200179: "Player's privacy settings don't allow for game card to be viewed.",
  api_code_1200180: "Failed to set NIKKE game tag.",
  api_code_1200181: "Text and image post selected. Upload an image first.",
  api_code_1200182: "Failed to update post like count.",
  api_code_1200183: "Comment review completed.",
  api_code_1200184: "Post review completed.",
  api_code_1200185: "Failed to retrieve section.",
  api_code_1200186: "Failed to convert event to json",
  api_code_1200187: "Invalid pagination cursor",
  api_code_1200188: "Invalid pagination cursor",
  api_code_1200189: "Abnormal mapping of all information in internal messages",
  api_code_1200190: "Error retrieving activity list.",
  api_code_1200191: "Error retrieving multilingual activity list.",
  api_code_1200192: "Error getting activity inserted into list",
  api_code_1200193: "Error fetching AIGC translation.",
  api_code_1200194: "Error parsing AIGC translation response.",
  api_code_1200195: "Error retrieving translation cache.",
  api_code_1200196: "Number of source and translated text lines mismatch.",
  api_code_1200197: "Failed to retrieve section multilingual mapping.",
  api_code_1200198: "Cannot forward unapproved post.",
  api_code_210002: "This account did not participate in the test",
  api_code_1200199: "Avatar pendant setting failed",
  api_code_1200200: "Failed to parse user avatar pendant list",
  api_code_1200201: "Failed to retrieve user avatar badge list",
  api_code_1200202: "Avatar pendant setting failed",
  api_code_1200203: "Profile pendant not obtained",
  api_code_1200204: "Failed to retrieve avatar pendant",
  show_my_comment: "Show My Comments on Profile Page",
  edit_link: "Edit Link",
  edit_avatar: "Change Avatar",
  modify_username: "Edit Nickname",
  modify_successfully: "Change Success",
  additional_information: "More Info",
  have_agree_agreement: "I have read and agreed to the following agreement.",
  sign_successfully: "Sign completed",
  load_content_error: "Failed to load content.",
  comments: "Comments",
  new_follower: "New Followers",
  received_like: "Likes",
  loaded_comments: "All comments displayed",
  loaded_replies: "All replies displayed.",
  violation_tips: "You have been banned for violating community guidelines.",
  history: "Search History",
  view_faqs: "Check the <a class='{0}'>FAQ</a> for more information",
  faq: "FAQ",
  user_already_set_private: "The player has set this page as private.",
  please_choose_region: "Select Region",
  sold_out: "Sold out",
  nikke_area_29080: "JP/KR/NA/SEA/Global",
  nikke_area_29157: "HK/MC/TW",
  nikke_end_user_license_agreement: "NIKKE End User License Agreement",
  nikke_privacy_policy: "NIKKE Privacy Policy",
  share_to: "Share to",
  add_to_home_screen: "Add to Desktop Shortcut",
  add_to_home_screen_to_find_us_easily: "Add Blabla Link to desktop shortcut for quick access.",
  open_in_browser: "Open in browser",
  just_tap_share: 'Just tap "Share"',
  "then_add to_home_screen": 'then "Add to Desktop Shortcut"',
  no_roles: "No characters",
  this_project_is_under_construction: "This project is under construction...",
  please_rotate_your_device_for_better_display: "Please rotate your device for better display",
  query_data_ad: "Convenient query [Nikke game data]",
  compaign_list: "Campaign List",
  copy_failed: "Copy failed",
  share_channel: "Share to {channel}",
  share_failed: "Share failed",
  bind_lip_first: "Please link your LI Pass account first.",
  receive_gitf_success: "Gift sent",
  bound_get_gift: "Link to get gifts",
  channel_bounded: "Account linked",
  link_channel: "Link",
  view_all: "View all",
  bind_role_bonus_hint: "Get {0} the first time you link a character.",
  please_login: "Please login",
  view_game_data: "View game data",
  copy_link: "Copy Link",
  system_share: "System",
  copy_link_success: "Copied",
  bind_role_success: "Character Linked",
  intl_sdk_fail: "Network error, unable to log in.",
  action_frequency_tips: "Too many attempts, please try again later",
  view_original_article: "View Original",
  browser_not_support:
    "The current platform does not support this operation, please try another one",
  openid_is_bound:
    "This LEVEL INFINITE PASS account has already been linked with a game character. Please try again with another account.",
  shiftyspad_root_share:
    "A NIKKE-exclusive tool providing you access to your game data at any time. #Blabla Link",
  shiftys_spad: "SHIFTYPAD",
  edit_user_link_tips:
    "Add an external link. It will be displayed on your personal profile and visible to other users.",
  communityguidelines: "Community Guidelines",
  opensource_statement: "Open Source Software Statement",
  notification_message_type_2: "commented on your post",
  notification_message_type_3: "replied to your post comment",
  notification_message_type_5: "liked your post",
  notification_message_type_6: "followed you",
  notification_message_type_7: 'Your post "{0}" has been deleted by the administrator due to "{1}"',
  notification_message_type_8:
    'Your comment "{0}" has been deleted by the administrator due to "{1}"',
  notification_message_type_9: "liked your post comment",
  notification_message_type_10: "liked your post comment",
  notification_message_type_11:
    'Due to "{1}", your nickname review has not passed, please modify it in a timely manner',
  notification_message_type_12:
    'Due to "{1}", your signature review has not been approved, please make timely modifications',
  notification_message_type_13:
    'Due to "{1}", the content of the post you published did not pass the review. Please modify it and republish.',
  notification_message_type_14:
    'Your comment "{0}" has been deleted by the administrator due to "{1}"',
  notification_message_type_18:
    'Due to "{1}", your nickname review has not passed, please modify it in a timely manner',
  notification_message_type_19:
    'Due to "{1}", your signature review has not been approved, please make timely modifications',
  notification_message_type_20: "{0}",
  notification_message_type_21:
    'The comment/post you reported, "{0}", has been verified and deleted.',
  notification_message_type_22:
    'Due to "{1}", you have been muted by the system for {0} days. If you have any questions, please contact us via email. Feedback email: {2}',
  content_deleted: "Content deleted.",
  content_under_review: "Content under review.",
  app_name: "Blabla Link",
  page_content_not_found: "Page content missing.",
  translate: "Translate",
  view_original: "View Original",
  all_events: "All Events",
  all_categories: "All Categories",
  login_to_see_more: "Log in to view more content.",
  translating: "Translating...",
  add_new_language_version: "Multi-Language",
  scheduled_release: "Schedule",
  period_of_validity: "Validity Period",
  avatar_frame_locked: "Unavailable",
  edit_avatarframe: "Avatar Frame",
  permanent_valid: "Permanent",
  to_get: "Obtain Now",
  wear: "Equip",
  demount: "Remove",
  edit_post: "Edit Post",
  year: "Year(s)",
  month: "Month(s)",
  day: "Day(s)",
  hour: "Hour(s)",
  minute: "Minute(s)",
  second: "Second(s)",
  to_edit_avatar_frame: "Change Avatar Frame",
  file_type_not_support: "File type not supported. Supported file types: {0}",
  unrelease: "Draft",
  share_shiftyspad_text:
    "A NIKKE-exclusive tool providing you access to your game data at any time. #BlablaLink",
  share_topic_text:
    "Join Blabla Link to let your voice be heard in captivating NIKKE discussions. #{0}",
  share_points_text: "Claim loads of game perks in the official NIKKE game community. #BlablaLink",
  share_others_text:
    "The official NIKKE game community, tools, perks, and news at your fingertips. #BlablaLink",
  system_notifications: "System",
  redeem_successful: "Redeemed.",
  redeem_successful_pop_content:
    "Please confirm order status and go to in-game Mail to claim rewards.",
  shiftyspad_private_setting: "SHIFTYPAD Privacy Settings",
  shiftyspad_settings_tips: "In SHIFTYPAD, show my {0}.",
  add_to_screen_title: "Create {0} Shortcut",
  add_to_screen_step_1: "Step 1/3: Tap {0} on the {1} home page.",
  add_to_screen_step_2: "Step 2/3: Select {0} and confirm.",
  add_to_screen_step_3: "Step 3/3: Tap {0} and open {1}.",
  add_to_screen_step_1_game: "Step 1/3: Tap {0} on the {1} home page, then select {2}.",
  add_to_screen_step_1_game_andorid:
    "Step 1/3: Tap {0} on the {1} home page, then select Google Chrome.",
  add_to_screen_step_2_game: "Step 2/3: Tap {0} on {1}, then select {2} and confirm.",
  desktop_shortcut: "Desktop Shortcut",
  cdk_redemption: "Redeem CDK Code",
  redemption_record: "Redemption Log",
  success: "Success",
  failed: "Failed",
  main_daily_info: "Daily News",
  none_disclosure_agreement: "{0} Non-Disclosure Agreement",
  synthesis: "General",
  topic: "Topics",
  user: "User",
  api_code_1302001: "Unable to identify game error code.",
  api_code_1302002: "Parameter error.",
  api_code_1302003: "Invalid redemption code.",
  api_code_1302004: "Failed to acquire GameID parameter.",
  api_code_1302005: "Failed to acquire OpenID parameter.",
  api_code_1302006: "Failed to acquire RoleID parameter.",
  api_code_1302007: "System request failed. Please try again.",
  api_code_1302008: "Not authorized to redeem.",
  api_code_1302009: "Not within the event period.",
  api_code_1302010:
    "Event only available for users on specified servers. Users from other servers cannot redeem.",
  api_code_1302011:
    "Event only available for users in specified countries or regions. Users from other countries or regions cannot redeem.",
  api_code_1302012:
    "Event only available for users on specified platforms (iOS, Android, etc.). Users on other platforms cannot redeem.",
  api_code_1302013: "MRMS bundle failed to send.",
  api_code_1302014: "CDK Code already used by other user.",
  api_code_1302015: "CDK Code does not exist.",
  api_code_1302016: "You already used this CDK code.",
  api_code_1302017: "CDK Code or user redemption limit reached.",
  api_code_1302018:
    "Requests too frequent. You are currently using this CDK code. Please try again later.",
  api_code_1302019: "Network error. Please try again later.",
  please_link_character: "Please link your character.",
  show_my_game_card: "Show My Game Card (Basic Info) on Profile Page",
  please_enter_cdk: "Enter redemption code",
  cdk_too_long: "Redemption code cannot be longer than 32 characters.",
  share_cdk_text: "Check out the convenient NIKKE redemption tool at Blabla Link.",
  notification_message_type_22_reason_00: "Other reasons",
  notification_message_type_22_reason_01:
    "Posting repetitive content or content unrelated to NIKKE",
  notification_message_type_22_reason_02:
    "Exhibiting disruptive behavior (such as personal attacks, trolling, or unfounded accusations)",
  notification_message_type_22_reason_03:
    "Posting illegal, violent, pornographic, or other malicious content",
  notification_message_type_22_reason_04:
    "Posting contents that disrupt the game balance (such as bug exploits, cheats, or modifications)",
  notification_message_type_22_reason_05:
    "Impersonating official staff, administrators or other users",
  notification_message_type_22_reason_06: "Violating account nickname or personal profile rules",
  notification_message_type_22_reason_07:
    "Posting unpacked content from the game package, or spreading unconfirmed or fabricated information",
  notification_message_type_22_reason_08:
    "Posting information about account transfers, selling or buying accounts, account valuation, or leveling services",
  notification_message_type_22_reason_09: "Posting advertisements or promotional content",
  notification_message_type_22_reason_10:
    "Violating rules regarding original content and reposting",
  jump_outside_title_tips: "External Site",
  jump_outside_warning_tips:
    "You are being redirected to an external website, and we cannot be held responsible for the security and the content of external websites.",
  warning: "Warning",
  account_not_bind_game_role_title: "Failed to add Card.",
  account_not_bind_game_role_tips: "Please link an in-game character before adding a Card.",
  account_not_bind_game_role_confirm_btn_text: "Link a Character",
  request: "Requests",
  api_code_1200235: "Unable to send a NIKKE friend request to yourself.",
  api_code_1302101: "No characters found.",
  api_code_1302102: "The character is not on this server.",
  api_code_1302103: "API call failed. Invalid return.",
  api_code_1302104: "Unable to send more than 30 friend requests.",
  api_code_1302105: "Player is unable to accept more than 30 friend requests.",
  api_code_1302106: "Player's friend list is full.",
  api_code_1302107: "Request already sent.",
  api_code_1302108: "User not found.",
  api_code_1302109: "Failed to add friend.",
  comment_decorations: "Comment Decorations",
  personalized: "Personalization",
  content_delete_reason_1: "Posting spam content (repetitive or unrelated to NIKKE)",
  content_delete_reason_2:
    "Disrupting community harmony (swearing, personal attacks, trolling, baseless accusations, etc.)",
  content_delete_reason_10:
    "Sharing pornographic content (explicit or implied sexual behavior, excessive exposure of private parts, overly revealing clothing, etc.)",
  content_delete_reason_3:
    "Posting inappropriate content (violence, gore, grotesque, bizarre, illegal activities, promotion of self-harm or suicide, or other malicious content).",
  content_delete_reason_4: "Exploiting bugs, cheats, or modifications that disrupt game balance",
  content_delete_reason_5:
    "Posting unpacked content from the game package, or spreading unconfirmed or fabricated information",
  content_delete_reason_6:
    "Posting information about account transfers, selling or buying accounts, account valuation, or leveling services",
  content_delete_reason_7: "Posting advertisements or promotional content",
  content_delete_reason_8: "Violating community rules regarding original content and reposting",
  content_delete_reason_9: "Other violations",
  select_reason_for_delete: "Reason for Deletion",
  garbage_content: "Posting spam content (repetitive or unrelated to NIKKE)",
  community_disruption:
    "Disrupting community harmony (swearing, personal attacks, trolling, baseless accusations, etc.)",
  illegal_content:
    "Posting inappropriate content (violence, gore, grotesque, bizarre, illegal activities, promotion of self-harm or suicide, or other malicious content).",
  crack_and_modify_game_balance:
    "Exploiting bugs, cheats, or modifications that disrupt game balance",
  unpack_content_information_violation:
    "Posting unpacked content from the game package, or spreading unconfirmed or fabricated information",
  account_transaction:
    "Posting information about account transfers, selling or buying accounts, account valuation, or leveling services",
  advertising_behavior: "Posting advertisements or promotional content",
  community_original_or_reprint_violation:
    "Violating community rules regarding original content and reposting",
  other_violations: "Other violations",
  friend_card_request_successfully: "Request sent. Waiting for player to respond.",
  hi_commander: "Hi, Commander!",
  had_add_friend_card: "Friend's card added.",
  invalid_login_token_tips: "Your session has expired, please log in again.",
  reset_filter: "Reset",
  only_see_obtained_nikke: "Only show obtained NIKKEs",
  voice_mode: "Voice Mode",
  onboarding_mission: "Onboarding Mission",
  task_signup_and_signin: "Register/ log in to Blabla Link",
  task_bind_lip: "Link your LI Pass account",
  task_visite_shiftypad: "Check & link SHIFTYPAD",
  task_add_to_desktop: "Add Blabla Link to Desktop Shortcut",
  task_follow_nikke: "Follow the Official Accounts",
  daily_mission: "Daily Mission",
  already_received: "Claimed",
  to_complete: "Go",
  player_set_game_card_to_be_private: "Player has set their game cards to private.",
  equipment_buff_data: "Equipment Stats",
  equip_effect_overview: "Equipment Effects",
  api_code_1200262: "Failed to translate (service error or timed out)",
  equip_effect_now: "Effect not obtained",
  api_code_1302116: "Player is already a friend.",
  api_code_101: "Service error or timed out.",
  api_code_102: "Service error or timed out.",
  follow_all: "Follow All",
  all_mission_finished_tips: "All the Onboarding Missions have been completed.",
  item_received_tips: "The item has been sent via in-game mail.",
  nikke_power_hint:
    "Please note that due to the differences between the web version and the client version, Nikke stats and power may be slightly different (no more than 10). This difference cannot be resolved at the moment, but we are working on a solution. Thank you for your understanding.",
  receive: "CLAIM",
  need_bind_lip_first:
    "Unable to claim Coins or redeem rewards before linking LI PASS. Tap here to link.",
  api_code_1300013: "Pack has been claimed.",
  blabla_task_center: "Blabla Missions",
  api_code_1200280: "Topic not found.",
  default_error_tips: "The page is too busy, please try again later.",
  api_code_1200261: "Failed to retrieve login status, please reload the page.",
  api_code_212000: "Too many actions, please try again later.",
  api_code_212001: "Too many actions, please try again later.",
  add_to_screen_on_mobile_tips:
    "Please open this in the browser on your mobile device, then tap Share to add the shortcut to the home screen.",
  add_to_screen_on_mobile_first:
    "Complete this quest by launching Blabla Link from the shortcut on home screen.",
  log_out_switch_region: "Log Out and Change Server",
  switch_server_to_login: "If you need to change to the {0} server, please {1}.",
  add_to_screen_finished: "Added.",
  are_you_sure_to_log_out: "Log out?",
  api_code_1100036: "Server incorrect.",
  api_code_1302020: "Too many errors, please wait 72 hours before using the CDK code again.",
  api_code_1302120: "The feature is under maintenance, please try again later.",
  view: "View",
  goto_creatorhub_registre_page_tips:
    "After registering with {0}, please return to Blabla Link and link the account.",
  goto_creatorhub_forgot_page_tips:
    "After recovering your password from {0}, please return to Blabla Link and link the account.",
  one_click_post: "One-click Sync",
  repost: "Repost",
  published_on: "Published on {0}",
  auto_sync: "Auto-Sync",
  my_submissions: "My Submissions",
  recent_events: "Recent Events",
  link_creatorhub_tips: "Link your CreatorHub account with Blabla Link",
  on_going: "In progress",
  creatorhub_not_bind_list_tips:
    "After linking, you can sync your contributions on CreatorHub to Blabla Link.",
  turn_on_auto_sync_title: "Enable Auto-Sync",
  turn_on_auto_sync_content:
    "After enabling, the new posts submitted to CreatorHub will also be posted to your Blabla Link.",
  api_code_1200303: "CreatorHub not linked.",
  api_code_1200304: "CreatorHub interface error.",
  api_code_1200305: "CreatorHub synchronization modification interface error.",
  api_code_1200306: "Failed to link CreatorHub.",
  api_code_1200307: "This CreatorHub account has already been linked.",
  api_code_1200308: "CreatorHub account not found.",
  api_code_1200309: "This CreatorHub account is under review.",
  api_code_1200310: "CreatorHub account error.",
  api_code_1200311: "Platform not supported.",
  api_code_1200312: "Blabla Link account linked.",
  api_code_1200313: "Invalid token.",
  api_code_1200314: "Account verification failed.",
  api_code_1200315:
    "This account has been frozen, unable to sync. Please link again if the account has been unfrozen.",
  plat_outpost_desc: "General Gaming, Discussion, Sharing",
  plat_nikkeart_desc: "Fan Art Sharing",
  plat_guides_desc: "NIKKE Game Guides",
  plat_official_desc: "Official News and Events",
  creatorhub_account_status_0: "Account registration incomplete.",
  creatorhub_account_status_1: "Account under review.",
  creatorhub_account_status_2: "Account status normal.",
  creatorhub_account_status_3: "Account review failed.",
  creatorhub_account_status_4: "Account has been frozen. Failed to sync posts.",
  creatorhub_account_status_5: "Account has been frozen. Failed to sync posts.",
  creatorhub_account_status_6: "Account has been banned. Unable to sync posts.",
  creatorhub_account_status_7: "Account can only be used for withdrawal. Unable to sync.",
  api_code_1200316: "Please log in again and complete identity verification before proceeding.",
  api_code_1200319: "This user has been muted.",
  creatorhub_account_bind_not_verify_tips:
    "This account has not been verified. Please go to CreatorHub and complete verification before returning to Blabla Link and linking the account.",
  turn_on: "Activate",
  next_time: "Later",
  api_code_1200322: "Account has been frozen.",
  top_comment: "Pin to Top",
  bottom_comment: "Pin to Bottom",
  cancel_topping: "Remove Top Pin",
  cancel_bottoming: "Remove Bottom Pin",
  copy_comment_id: "Copy Comment ID",
  comment_id_copied: "Comment ID copied.",
  sure_to_top_comment: "Pin this comment at the top when sorted by popularity?",
  sure_to_bottom_comment: "Pin this comment at the bottom when sorted by popularity?",
  sure_to_cancel_toping: "Remove the comment from being pinned to the top?",
  sure_to_cancel_bottoming: "Remove the comment from being pinned to the bottom?",
  successfully: "Success.",
  api_code_1200325: "Action failed. Unable to pin comment to the top or bottom.",
  api_code_1200326: "Action failed. No authorization.",
  api_code_1200327:
    "Action failed. The post or comment is not currently pinned to the top or bottom.",
  api_code_1200328: "Action failed. Post not found.",
  api_code_1200329: "Action failed, comment not found",
  operation_succeeds_and_switch_to_hot_view:
    "Pinned! This will now be visible when sorted by popularity.",
  myunion: "My Union",
  square_union_list: "Union List",
  share_my_union_and_recruit_members: "Share My Union",
  union_search_placeholder: "Enter Union name or ID",
  success_redeemed_gift_card_tips: "Go to the Transactions page to view the transaction status.",
  card_no: "Card Number",
  card_code: "Redemption Code",
  copied: "Copied.",
  union_post_title: "Union Recruitment Post",
  share_to_post: "Attach to Post",
  certificate_type: "Join Criteria",
  language: "Language",
  entry_level: "Entry Level",
  union_name: "Union Name",
  union_activity: "Union Activity",
  union_rank: "Union Rank",
  union_square: "Union Recruitment",
  not_in_union_empty_tips: "Not in a Union yet. Please use the {0} below to join one.",
  union_post_success: "Posted.",
  share_to_post_detail: "Share the Union card to recruit more Commanders!",
  sku_1: "US$10 Amazon gift card(US)",
  sku_2: "US$10 Google Play gift card(US)",
  sku_3: "JPY ¥1,500 Amazon gift card(JP) ",
  sku_4: "₩15,000 Google Play gift card(KR)",
  please_select_sku_type: "Select country or region",
  sku: "Country/Region",
  hashtag_manage: "Hashtag Management",
  no_hashtag_for_the_post: "No hashtags.",
  union_join_type_0: "Anyone can join",
  union_join_type_1: "Permission needed",
  union_join_type_2: "Closed",
  join_union: "Join Now",
  share_to_square: "Share to Union Square",
  save_the_hashtags: "Save Hashtag?",
  confirm_to_edit_hashtag: "Save",
  union_join_success: "Request sent.",
  join_union_detail: "Joined {0}.",
  union_rank_0: "Challenger",
  union_rank_1: "Diamond",
  union_rank_2: "Platinum",
  union_rank_3: "Gold",
  union_rank_4: "Sliver",
  union_rank_5: "Bronze",
  union_rank_6: "Beginner",
  had_add_union_card: "Union card added.",
  area_id_81: "Japan",
  area_id_82: "North America",
  area_id_83: "Korea",
  area_id_84: "Global",
  area_id_85: "Southeast Asia",
  area_id_91: "HK/MC/TW",
  join: "Join",
  authoring_statement: "Creation Statement",
  authoring_statement_category1: "This is an original.",
  authoring_statement_category1_1: "Insert source link.",
  authoring_statement_category1_2: "Do not repost.",
  authoring_statement_category1_3: "Reposting allowed.",
  authoring_statement_category2: "Contains sensitive content.",
  authoring_statement_category2_1: "spoilers",
  authoring_statement_category2_2: "NSFW/gore/violent",
  authoring_statement_category3: "contents generated by AI",
  authoring_statement_tips1: "May contain {0}. Please proceed with caution.",
  api_code_1303001: "Parameter error.",
  api_code_1303002: "System error.",
  api_code_1303003: "Union not found.",
  api_code_1303004: "Character not found on this server.",
  api_code_1303005: "No characters linked.",
  api_code_1303006: "Union card published.",
  api_code_1303007: "Union card not yet published.",
  api_code_1303008: "Does not match the linked character.",
  api_code_1303009: "You are not in a Union.",
  api_code_1303010: "You are not in this Union.",
  api_code_1303011: "Your in-game character is not on the same server as the Union.",
  api_code_1303012: "This feature is currently under maintenance.",
  api_code_1303013: "Commander Level does not meet the Union requirement.",
  api_code_1303014: "Too many join requests. Please try again later.",
  api_code_1303015: "Commander blocked.",
  api_code_1303016: "Union full.",
  api_code_1303017: "You have already requested to join this Union.",
  api_code_1303018: "Union not accepting applications currently.",
  api_code_1303019: "Requested to join {0}.",
  api_code_1303020: "You are already in a Union.",
  api_code_1303021: "Union card not found.",
  api_code_1303022: "Your current Union is not the same as the Union when this was published.",
  api_code_1303023: "Maintenance in progress.",
  api_code_1303024: "Union not on your server.",
  api_code_1100039: "Verification code incorrect.",
  api_code_503001: "Not enough Coins.",
  api_code_503002: "Reward claim limit reached.",
  api_code_400013: "No more rewards left.",
  api_code_400016: "No more rewards left.",
  api_code_200006: "The event has not started.",
  api_code_200007: "The event has ended.",
  gift_card_commodity_name: "[2.5-year anniversary] Gift Card",
  gift_card_commodity_desc:
    "1.Redemption Period: 04/25–04/30, refreshes daily at 11:00 (UTC+9)\n2.Please note that the gift cards are available in 4 types: US$10 Amazon gift card(US), US$10 Google Play gift card(US), JPY ¥1,500 Amazon gift card(JP) and ₩15,000 Google Play gift card(KR). Please select your preferred type when redeeming.\n3.Redemption URL：Amazon gift card:https://www.amazon.com/gc/redeem ; Google Play gift card:https://play.google.com/redeem \n4.Gift cards must be redeemed and used by the cardholder within the validity period. They cannot be returned, exchanged, or resold. Cardholders must comply with Amazon's or Google Play's platform rules. All issues related to taxes, accounts, etc., shall be borne by the cardholder. The event organizer is not involved in gift card activation, usage, or dispute resolution. \n",
  redeem_open_time: "Redemption starts at {0}.",
  next_redeem_open_time: "Next Redemption Opening: {0}",
  open_for_redemption_soon: "Redemption starts soon.",
  to_apple_official_website_tips: "Please redeem the gift card through the official Apple website.",
  to_google_official_website_tips:
    "Please redeem the gift card through the official Google website.",
  to_amazon_official_website_tips:
    "Please redeem the gift card through the official Amazon website.",
  to_stream_official_website_tips:
    "Please redeem the gift card through the official Steam website.",
  shiftyspad_user_set_module_private: "User has set {0} to private.",
  shiftyspad_private_setting_all: "Visible to all",
  shiftyspad_private_setting_friends: "Visible to friends",
  shiftyspad_private_setting_deny_all: "Hide from all",
  shiftyspad_private_setting_allies: "Visible to Union members",
  already_in_a_union: "Already in a Union.",
  daily_task_refresh_at: "Quests refresh at {0} daily.",
  blocking_setting_desc:
    "If you block this user, you will no longer see this user's content, and the user will be unable to interact with you.",
  blocking_setting_title: "Blocklist",
  blocking_setting: "Block Settings",
  blocked_at: "Blocked on {0}",
  unblock: "Unblock",
  unblock_this_user: "Unblock this user",
  block_this_user: "Block this user",
  unblock_this_user_desc: "Are you sure you want to unblock this user?",
  block_this_user_desc:
    "If you block this user, you will no longer see this user's content, and the user will be unable to interact with you.",
  blocking: "Blocked",
  cannot_block_official: "Block this user (unable to block official accounts)",
  no_blocked_user: "No blocked users.",
  allow_friend_request_via_game_card: "Allow others to send me friend requests through game card",
  video_loading_failed: "Failed to load video.",
  video_not_supported_on_this_network:
    "YouTube/TikTok videos cannot be played on the current network.",
  lang_region_en: "English",
  lang_region_ja: "Japanese",
  lang_region_ko: "Korean",
  lang_region_zh: "Simplified Chinese",
  "lang_region_zh-TW": "Traditional Chinese",
  select_language: "Select Language",
  select_region: "Select Preferences",
  select_region_tips: "Contents in the following language will be recommended",
  region_and_language: "Language and Content Preferences",
  union: "Union",
  cube_areana: "Arena",
  cube_combat: "Battle",
  api_code_1302122: "User does not allow others to add them as friends.",
  api_code_1302123: "User is not on this server.",
  api_code_1303025: "User has set their Union information to private.",
  api_code_1200354: "User has blocked you.",
  unlock_condition: "How to Unlock",
  cube: "Cube",
  item_level: "Phase {0}",
  favorite_item: "Collection",
  api_code_1200256: "User not found.",
  api_code_1200349: "Failed to block user.",
  api_code_1200348: "User has been blocked.",
  api_code_1200352: "Cannot unblock a user that has not been blocked.",
  api_code_1200353: "Failed to unblock.",
  please_bind_role_first: "Please bind your game character first",
  api_code_1302124: "The other party is already your friend.",
  comment_collapse: "Collapse",
  comment_expand: "Expand",
};
