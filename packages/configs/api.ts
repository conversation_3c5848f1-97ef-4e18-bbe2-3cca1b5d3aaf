/**
 * 后端接口相关配置
 */
import { ENV_PRE, ENV_TEST, ENV_PROD } from "./env";
export const API_TEST_AREA_ID = "asia";
export const API_PROD_AREA_ID = "global";

// export const API_TEST_REQUEST_URL = "https://api.blablalink.com";
export const API_TEST_REQUEST_URL = "https://test-api.blablalink.com";
export const API_PRE_REQUEST_URL = "https://pre-api.blablalink.com";
export const API_PROD_REQUEST_URL = "https://api.blablalink.com";

export const VITE_APP_TOOLS_CDN = {
  [ENV_TEST]: "https://test-tools-cdn.blablalink.com",
  [ENV_PRE]: "https://sg-tools-cdn.blablalink.com/test",
  [ENV_PROD]: "https://sg-tools-cdn.blablalink.com",
};

/**
 * @description 存放所有接口的请求地址
 */
const curryGenerateApi =
  <Pre extends string>(prefix: Pre) =>
  <Api extends `/${string}`>(api: Api) =>
    `${prefix}${api}` as const;

const generateUserApi = curryGenerateApi("/user");
const generateSystemApi = curryGenerateApi("/system");
const generateUGCApi = curryGenerateApi("/ugc");
const generateSSUGCApi = curryGenerateApi("/ugc/proxy/standalonesite");
const generateSSUGCDirectApi = curryGenerateApi("/ugc/direct/standalonesite");
const generateRewardsApi = curryGenerateApi("/lip/proxy/lipass/Points");
const generateRewardsDirect = curryGenerateApi("/lip/direct/lipass/Points");
const generateCommodityApi = curryGenerateApi("/lip/proxy/commodity/Commodity");
const generateCommodityDirectApi = curryGenerateApi("/lip/direct/commodity/Commodity");
const generateGameApi = curryGenerateApi("/lip/proxy/commodity/Game");
const generateProxyGameApi = curryGenerateApi("/game/proxy/Game");
const generateDirectGameApi = curryGenerateApi("/game/direct/Game");
const generateDirectCommodityGameApi = curryGenerateApi("/lip/direct/commodity/Game");
const generateIntlApi = curryGenerateApi("/lip/act/direct/account/Intlgame");
// const generateLipassApi = curryGenerateApi("/lip/act/proxy/present/Lipass");
const generateLipassActApi = curryGenerateApi("/act/proxy/present/Lipass");
const generateCommunityApi = curryGenerateApi("/community");
const generateToolsApi = curryGenerateApi("/game/proxy/Tools");

////////////////////////// User API //////////////////////////
export const API_USER_LOGIN = generateUserApi("/Login");
export const API_USER_LOGINOUT = generateUserApi("/Logout");
export const API_USER_CHECK_LOGIN = generateUserApi("/CheckLogin");
export const API_USER_CHECK_HAS_LIP_ACCOUNT = generateUserApi("/CheckHasLipAccount");
export const API_USER_GET_INFO = generateUserApi("/get_info");
export const API_USER_GET_LOGIN_INFO = generateUserApi("/GetGameLoginInfo");

////////////////////////// SS UGC API //////////////////////////
// Dynamic
export const API_SS_UGC_GET_PLATE_LIST = generateSSUGCDirectApi("/Dynamics/GetPlateList");
export const API_SS_UGC_GET_TAG_LIST = generateSSUGCDirectApi("/Dynamics/GetTagList");
export const API_SS_UGC_GET_DISTRICT_LIST = generateSSUGCDirectApi("/Dynamics/GetDistrictList");
export const API_SS_UGC_CREATE_POST = generateSSUGCApi("/Dynamics/CreatePost");
export const API_SS_UGC_CREATE_POST_NEW = generateSSUGCApi("/Dynamics/CreatePostNew");
export const API_SS_UGC_CREATE_UPDATE_POST = generateSSUGCApi("/Dynamics/UpdatePost");
export const API_SS_UGC_GET_POST_LIST = generateSSUGCDirectApi("/Dynamics/GetPostList");
/**
 * @description 获取单个动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112346
 */
export const API_SS_UGC_GET_POST = generateSSUGCDirectApi("/Dynamics/GetPost");
export const API_SS_UGC_DELETE_POST = generateSSUGCApi("/Dynamics/DeletePost");
export const API_SS_UGC_GET_POST_STAR = generateSSUGCApi("/Dynamics/GetPostStar");
export const API_SS_UGC_POST_STAR = generateSSUGCApi("/Dynamics/PostStar");
export const API_SS_UGC_GET_POST_COLLECTION = generateSSUGCApi("/Dynamics/GetPostCollection");
export const API_SS_UGC_POST_COLLECTION = generateSSUGCApi("/Dynamics/PostCollection");
export const API_SS_UGC_COS_STS = generateSSUGCApi("/Dynamics/GetCosSts");
export const API_SS_UGC_GET_VIDEO_INFO_BY_URL = generateSSUGCApi("/Dynamics/GetVideoInfoByURL");
export const API_SS_UGC_GET_POST_COMMENTS = generateSSUGCDirectApi("/Dynamics/GetPostComments");
export const API_SS_UGC_GET_POST_COMMENTS_V2 = generateSSUGCDirectApi(
  "/Dynamics/GetPostCommentsV2"
);
export const API_SS_UGC_BATCH_GET_POST_COMMENT_REPLIES = generateSSUGCDirectApi(
  "/Dynamics/BatchGetPostCommentReplies"
);
export const API_SS_UGC_GET_POST_COMMENT_REPLIES = generateSSUGCDirectApi(
  "/Dynamics/GetPostCommentReplies"
);
export const API_SS_UGC_GET_POST_COMMENT = generateSSUGCDirectApi("/Dynamics/GetPostComment");
export const API_SS_UGC_DELETE_POST_COMMENT = generateSSUGCApi("/Dynamics/DeletePostComment");
export const API_SS_UGC_SET_COMMENT_TOP_OR_BOTTOM = generateSSUGCApi(
  "/Dynamics/SetCommentTopOrBottom"
);

/** 移动帖子话题 */
export const API_SS_UGC_DYNAMICS_POST_CHANGE_TAG_BIND = generateSSUGCApi(
  "/Dynamics/PostChangeTagBind"
);
/** 更新创作声明 */
export const API_SS_UGC_DYNAMICS_UPDATE_STATEMENT = generateSSUGCApi("/Dynamics/UpdateStatement");
export const API_SS_UGC_POST_COMMENT = generateSSUGCApi("/Dynamics/PostComment");
export const API_SS_UGC_GET_ALL_EMOTICONS = generateSSUGCDirectApi("/Dynamics/GetAllEmoticons");
export const API_SS_UGC_COMMENT_STAR = generateSSUGCApi("/Dynamics/CommentStar");
export const API_SS_UGC_GET_TAG = generateSSUGCDirectApi("/Dynamics/GetTag");
export const API_SS_UGC_POST_FORWARD = generateSSUGCApi("/Dynamics/PostForward");
export const API_SS_UGC_GET_USER_COMMENT_LIST = generateSSUGCDirectApi(
  "/Dynamics/GetUserCommentList"
);
export const API_SS_UGC_GET_USER_POST_LIST = generateSSUGCDirectApi("/Dynamics/GetUserPostList");
export const API_SS_UGC_GET_USER_POST_COLLECTION_LIST = generateSSUGCDirectApi(
  "/Dynamics/GetUserPostCollectionList"
);
export const API_SS_UGC_CONTENT_REPORT = generateSSUGCApi("/Dynamics/ContentReport");
export const API_SS_UGC_MOVE_POST = generateSSUGCApi("/Dynamics/MovePost");
export const API_SS_UGC_SEARCH_TAG = generateSSUGCDirectApi("/Dynamics/SearchTag");
/** 获取活动话题列表 */
export const API_SS_UGC_DYNAMICS_GET_ACTIVITY_POST_TAGS = generateSSUGCApi(
  "/Dynamics/GetActivityPostTags"
);

// user
export const API_SS_UGC_USER_FOLLOW = generateSSUGCApi("/User/UserCollection");
export const API_SS_UGC_USER_GET_USER_LINKS = generateSSUGCDirectApi("/User/GetUserLinks");
export const API_SS_UGC_USER_SET_USER_LINKS = generateSSUGCApi("/User/SetUserLinks");
export const API_SS_UGC_USER_GET_USER_PRIVACY_SETTING = generateSSUGCDirectApi(
  "/User/GetUserPrivacySetting"
);
export const API_USER_SET_USER_SHIFTYSPAD_PRIVACY_SETTINGS = generateSSUGCApi(
  "/User/SetUserShiftyspadPrivacy"
);
export const API_SS_UGC_USER_USER_PRIVACY_SET = generateSSUGCApi("/User/UserPrivacySet");
export const API_SS_UGC_USER_SET_USER_MOOD = generateSSUGCApi("/User/SetUserMood");
export const API_SS_UGC_USER_GET_USER_FOLLOW = generateSSUGCDirectApi("/User/GetUserFollow");
export const API_SS_UGC_USER_GET_USER_FANS = generateSSUGCDirectApi("/User/GetUserFans");
export const API_SS_UGC_USER_GET_USER_PROFILE = generateSSUGCDirectApi("/User/GetUserProfile");
export const API_SS_UGC_USER_GET_USER_INFO_NEW = generateSSUGCApi("/User/GetUserInfoNew");
export const API_SS_UGC_USER_GET_INTL_GAME_USER_STATUS = generateSSUGCApi(
  "/User/GetIntlGameUserStatus"
);
export const API_SS_UGC_USER_MODIFY_INFO = generateSSUGCApi("/User/ModifyInfo");
export const API_SS_UGC_USER_GET_MESSAGE = generateSSUGCApi("/User/GetMessage");
export const API_SS_UGC_USER_READ_MESSAGE_ALL = generateSSUGCApi("/User/ReadMessageAll");
export const API_SS_UGC_USER_UNREAD_MESSAGE = generateSSUGCApi("/User/UnReadMessage");
export const API_SS_UGC_USER_READ_MESSAGE = generateSSUGCApi("/User/ReadMessage");
export const API_SS_UGC_USER_AVATARS = generateSSUGCApi("/User/GetUserAvatars");
export const API_SS_UGC_USER_SIGN_PRIVACY_PROTOCOL = generateSSUGCApi("/User/SignPrivacyProtocol");
export const API_USER_GET_AVATAR_PENDANT_LIST = generateSSUGCApi("/User/GetUserAvatarPendantList");
export const API_USER_SET_USER_AVATAR_PENDANT = generateSSUGCApi("/User/SetUserAvatarPendant");
export const API_SS_UGC_USER_PLAYER_INFO = generateSSUGCDirectApi("/User/GetUserGamePlayerInfo");
export const API_SS_UGC_USER_SET_GAME_TAG = generateSSUGCApi("/User/SetUserGameTag");
export const API_SS_UGC_USER_SEARCH_USER = generateSSUGCDirectApi("/User/SearchUser");
export const API_USER_GET_COMMENT_BUBBLE_LIST = generateSSUGCApi("/User/GetUserCommentBubbleList");
export const API_USER_SET_COMMENT_BUBBLE = generateSSUGCApi("/User/SetUserCommentBubble");
export const API_USER_GET_ALL_COMMENT_BUBBLE_LIST = generateSSUGCDirectApi(
  "/Dynamics/GetAllCommentBubbleList"
);
/** 设置拉黑/取消拉黑用户 */
export const API_SS_UGC_USER_SET_BLACK_USER = generateSSUGCApi("/User/SetBlackUser");
/** 获取拉黑用户列表 */
export const API_SS_UGC_USER_GET_BLACK_USER_LIST = generateSSUGCApi("/User/GetBlackUserList");
/** 设置用户选择的区域 */
export const API_SS_UGC_USER_SET_USER_REGIONS = generateSSUGCApi("/User/SetUserRegions");
// 分享链接
export const API_SS_UGC_POST_SHARE_URL = generateSSUGCDirectApi("/Dynamics/GetPostShareHtml");
export const API_SS_UGC_TOPIC_SHARE_URL = generateSSUGCDirectApi("/Dynamics/GetTagShareHtml");
// 翻译
export const API_SS_UGC_TRANSLATE_CONTENT = generateSSUGCDirectApi("/Dynamics/TranslateContent");
// CreatorHub 顶部筛选条件
export const API_SS_UGC_GET_CREATOR_HUB_LIST = generateSSUGCDirectApi(
  "/Dynamics/GetCreatorHubTaskList"
);
/** 拉取Creatorhub绑定账号信息 */
export const API_SS_UGC_USER_GET_CREATOR_HUB_USER_INFO = generateSSUGCApi(
  "/User/GetCreatorHubUserInfo"
);
/** 拉取RecentEvent */
export const API_SS_UGC_DIRECT_DYNAMICS_GET_RECENT_TASKS = generateSSUGCDirectApi(
  "/Dynamics/GetRecentTasks"
);

/** 设置自动同步 */
export const API_SS_UGC_USER_CHANGE_SYNC_STATUS = generateSSUGCApi("/User/ChangeSyncStatus");

/** 拉取我的提交作品列表 */
export const API_SS_UGC_DYNAMICS_GET_MY_SUBMISSION = generateSSUGCApi("/Dynamics/GetMySubmission");
/** 绑定Creatorhub账号 */
export const API_SS_UGC_USER_BIND_CREATOR_HUB_ACCOUNT = generateSSUGCApi(
  "/User/BindCreatorHubAccount"
);

/**
 * @description 发起NIKKE添加好友请求
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/122240
 */
export const API_SS_UGC_SEND_ADD_FRIEND_REQUEST = generateSSUGCApi("/Dynamics/SendFriendRequest");

export const API_SS_UGC_SEND_ADD_FRIEND_REQUEST_WITH_PRIVACY_PERMISSION = generateProxyGameApi(
  "/SendFriendRequestWithPrivacyPermission"
);
////////////////////////// UGC API //////////////////////////
/**
 * @description 内容相关
 */
/**
 * @description 在动态列表页面上报浏览量的接口，主要是gameid=8黎明觉醒才要上报，其他游戏都不上报
 * @link https://yapi.intlgame.com/project/1388/interface/api/73846
 */
export const API_UGC_POST_BROWSE = generateUGCApi("/post/browse");
/** 视频链接获取视频详情信息 */
export const API_UGC_GET_VIDEO_INFO = generateUGCApi("/video/get_info");
/** 发布动态 */
export const API_UGC_POST = generateUGCApi("/post");
/** 动态列表|搜索 */
export const API_UGC_GET_POSTS = generateUGCApi("/posts");
/** 点赞 */
export const API_UGC_POST_STAR = generateUGCApi("/post/star");
/** 评论点赞接口 */
export const API_UGC_POST_COMMENT_STAR = generateUGCApi("/post/comment/star");
/** 评论点赞接口 V2 */
export const API_UGC_POST_COMMENT_STAR_V2 = generateUGCApi("/post/comment/star/v2");
/** 回复点赞接口 */
export const API_UGC_POST_REPLY_STAR = generateUGCApi("/post/comment/reply/star");
/** 收藏接口 */
export const API_UGC_POST_COLLECTION = generateUGCApi("/post/collection");
/** 获取动态内容类型|详情 */
export const API_UGC_GET_POST_DETAIL = generateUGCApi("/post");
/** 评论接口 */
export const API_UGC_POST_COMMENT = generateUGCApi("/post/comment");
/** 评论接口 V2 */
export const API_UGC_POST_COMMENT_V2 = generateUGCApi("/post/comment/v2");
/** 动态评论列表 */
export const API_UGC_GET_COMMENTS = generateUGCApi("/post/comments");
/** 动态评论列表 V2 */
export const API_UGC_GET_COMMENTS_V2 = generateUGCApi("/post/comments/v2");
/** 动态评论回复列表查询 */
export const API_UGC_GET_REPLIES = generateUGCApi("/post/comment/replys");
/** 回复评论接口 */
export const API_UGC_POST_REPLY = generateUGCApi("/post/comment/reply");
/** 话题列表&话题排行 */
export const API_UGC_GET_TOPIC = generateUGCApi("/tags");
/** 话题详情 */
export const API_UGC_GET_TOPIC_DETAIL = generateUGCApi("/tag");
/** 检索话题 */
export const API_UGC_SEARCH_TOPIC = generateUGCApi("/suggest/tags");
/** 创建话题 */
export const API_UGC_CREATE_TOPIC = generateUGCApi("/tag");
/** 删除评论 */
export const API_UGC_DELETE_COMMENT = generateUGCApi("/post/comment");
/** 删除评论 V2 */
export const API_UGC_DELETE_COMMENT_V2 = generateUGCApi("/post/comment/v2");
/** 删除回复评论 */
export const API_UGC_DELETE_REPLY = generateUGCApi("/post/comment/reply");
/** 删除动态 */
export const API_UGC_DELETE_POST = generateUGCApi("/post");
/** 举报内容 */
export const API_UGC_POST_REPORT = generateUGCApi("/post/report");
/** 举报内容 V2 */
export const API_UGC_POST_REPORT_V2 = generateUGCApi("/post/report/v2");
/** 拍脸宣发列表 */
export const API_UGC_FACE_SLAPPING = generateUGCApi("/face/slapping");

// ////////////////////////////////////////////////////////////////
/**
 * @description 游戏相关
 */
// 游戏详情
export const API_UGC_GET_GAME_DETAIL = generateUGCApi("/game");
/** 游戏列表 */
export const API_UGC_GET_GAMES = generateUGCApi("/games");

// ////////////////////////////////////////////////////////////////
/**
 * @description 用户相关
 */
// 注册
export const API_UGC_REGISTER_INTL = generateUGCApi("/auth/register_intl");
/** 发送绑定验证码 */
export const API_UGC_SEND_BIND_EMAIL = generateUGCApi("/user/send_bind_email");
/** 通过验证码绑定邮箱 */
export const API_UGC_SAVE_BIND_EMAIL = generateUGCApi("/user/save_bind_email");
/**
 * @deprecated 获取当前登录用户信息，统一使用 get_info
 */
export const API_UGC_GET_USER_INFO = generateUGCApi("/user/info");
/**
 * @deprecated 根据 openid 获取用户基础信息，统一使用 get_info
 */
export const API_UGC_GET_USER_PROFILE = generateUGCApi("/user/profile");
/**
 * 用户所有信息(主态/客态)
 * 涉及到用户相关信息的接口统一使用这个api
 */
export const API_UGC_GET_USER_INFO_NEWS = generateUGCApi("/user/get_info");
/** 修改用户基础信息 */
export const API_UGC_MODIFY_USER_INFO = generateUGCApi("/user/modify_info");
/** 检查用户名是否存在 */
export const API_UGC_CHECK_USER_NAME_EXIST = generateUGCApi("/user/check_name");
/** 我的动态列表 */
export const API_UGC_GET_USER_POSTS = generateUGCApi("/user/posts");
/** 我的评论列表 */
export const API_UGC_GET_USER_COMMENTS = generateUGCApi("/user/comments");
/** 我的评论列表 V2 */
export const API_UGC_GET_USER_COMMENTS_V2 = generateUGCApi("/user/comments/v2");
/** 我的收藏列表 */
export const API_UGC_GET_USER_COLLOECTIONS = generateUGCApi("/user/collections");
/** 我的点赞列表 */
export const API_UGC_GET_USER_STARS = generateUGCApi("/user/stars");
/** 关注|取消关注个人用户 */
export const API_UGC_ON_FOLLOW = generateUGCApi("/user/user_collection");
/** 关注列表 */
export const API_UGC_GET_USER_FOLLOWS = generateUGCApi("/user/follow");
/** 粉丝列表 */
export const API_UGC_GET_USER_FANS = generateUGCApi("/user/fans");
/** 查看是否有消息 */
export const API_UGC_GET_USER_UNREAD_MESSAGE = generateUGCApi("/user/msgcount/unread");
/** 消息内容 */
export const API_UGC_GET_USER_MESSAGES = generateUGCApi("/user/messages");
/** 消息内容 V2 */
export const API_UGC_GET_USER_MESSAGES_V2 = generateUGCApi("/user/messages/v2");
/** 标记用户所有消息已读 */
export const API_UGC_MARK_USER_ALL_MESSAGE_READ = generateUGCApi("/user/message/read_all");
/** 检查用户是否登录 */
export const API_UGC_CHECK_LOGIN = generateUGCApi("/user/check_login");
/** 进行登录设置cookie */
export const API_UGC_LOGIN = generateUGCApi("/login");
export const API_UGC_GAME_LOGIN = generateUGCApi("/in_game_login");
export const API_UGC_LOGOUT = generateUGCApi("/logout");
/** 用户签署隐私协议 */
export const API_UGC_SIGN_PRIVACY = generateUGCApi("/user/sign_privacy");
/** 设置用户语言 */
export const API_UGC_SET_USER_LANGUAGE = generateUGCApi("/user/language");
/** 用户头像列表 */
export const API_UGC_GET_AVATARS = generateUGCApi("/avatars");
/** 获取社媒配置入口 */
export const API_UGC_GET_SOCIAL_MEDIA_ENTRANCE = generateUGCApi("/social/media/entrance");
/** floating配置列表 */
export const API_UGC_GET_FLOATING = generateUGCApi("/floating");
/** //////////////////////////////////////////////////////////////// */
/**
 * @description 其他
 */
// 文件上传
export const API_UGC_UPLOAD_FILE = generateUGCApi("/attachment");
/** 获取 cos 信息 */
export const API_UGC_COS_INFO = generateUGCApi("/cos/get_cos_info");
/** 获取 cos 临时密钥 */
export const API_UGC_COS_STS = generateUGCApi("/cos/get_cos_sts");
/** 获取 aws cos 临时密钥 */
export const API_UGC_AWS_STS = generateUGCApi("/cos/get_aws_sts");

////////////////////////// UGC API //////////////////////////

////////////////////////// REWARDS API //////////////////////////
/**
 * @description 积分相关
 */
export const API_REWARDS_RECORD_LIST = generateRewardsApi("/GetUserPointsList");
export const API_REWARDS_GET_POINTS = generateRewardsApi("/GetUserTotalPoints");
export const API_REWARDS_GET_TASKS = generateRewardsDirect("/GetTaskList");
export const API_REWARDS_GET_TASKS_LOGIN = generateRewardsApi("/GetTaskListWithStatus");
export const API_REWARDS_GET_TASK_DETAIL = generateRewardsDirect("/GetTaskDetail");
export const API_REWARDS_GET_TASK_DETAIL_LOGIN = generateRewardsApi("/GetTaskDetailWithStatus");
export const API_REWARDS_DAILY_CHECK_IN = generateRewardsApi("/DailyCheckIn");
export const API_REWARDS_MIDAS_TASK = generateRewardsApi("/GetMidasTaskStatus");
/** 按类型查询任务 */
export const API_REWARDS_GET_DISPOSABLE_TASK = generateRewardsApi("/GetDisposableTask");
/** 完成任务加积分 */
export const API_REWARDS_COMPLETE_TASK_ADD_POINT = generateRewardsApi("/CompleteTaskAddPoint");
/** 获取用户是否完成该任务 */
export const API_REWARDS_GET_USER_COLLECTION = generateRewardsApi("/GetUserCollection");
/** 用户完成收藏 */
export const API_REWARDS_USER_COMPLETE_COLLECTION = generateRewardsApi("/UserCompleteCollection");
////////////////////////// REWARDS API //////////////////////////

////////////////////////// COMMODITY API //////////////////////////
/**
 * @description 积分商品
 */
export const API_COMMODITY_LIST = generateCommodityDirectApi("/GetCommodityList");
export const API_USER_COMMODITY_LIST = generateCommodityApi("/GetUserCommodityList");
/** 商品收藏列表 */
export const API_USER_COLLECTION_COMMODITY_LIST = generateCommodityApi("/CommodityCollectionList");
/** 添加商品收藏 */
export const API_ADD_COMMODITY_COLLECTION = generateCommodityApi("/CommodityCollection");
/** 取消商品收藏 */
export const API_CANCEL_COMMODITY_COLLECTION = generateCommodityApi("/CommodityUnCollect");

export const API_COMMODITY_DETAIL = generateCommodityDirectApi("/GetCommodityData");
export const API_COMMODITY_EXCHANGE = generateCommodityApi("/ExchangeCommodity");
export const API_COMMODITY_CAN_EXCHANGE = generateCommodityApi("/CheckUserCanExchange");
export const API_COMMODITY_ORDER_DETAIL = generateCommodityApi("/GetOrderDetail");
/** export const */
////////////////////////// COMMODITY API //////////////////////////

////////////////////////// GAME API //////////////////////////
/**
 * @description 游戏内
 */
export const API_QUERY_BONUS_STATUS = generateProxyGameApi("/ShiftypadBindGameMissionStatus");
export const API_GET_BONUS = generateProxyGameApi("/OnboardingMissionGiftCollection");
export const API_GAME_ROLE = generateProxyGameApi("/GetRoleList");
export const API_GET_SAVED_ROLE = generateProxyGameApi("/GetSavedRoleInfo");
export const API_GAME_GET_CDK_REDEMPTION_HISTORY = generateProxyGameApi("/GetCdkRedemptionHistory");
export const API_GAME_RECORD_CDK_REDEMPTION = generateProxyGameApi("/RecordCdkRedemption");
export const API_SAVE_GAME_ROLE = generateGameApi("/SaveRoleInfo");
export const API_GAME_REGION = generateDirectCommodityGameApi("/GetRegionList");
/** lip添加游戏绑定角色 */
export const API_GAME_ADD_BINDING_GAME_ROLE = generateGameApi("/AddBindingGameRoleInfo");
/** 获取已绑定游戏角色列表 */
export const API_GAME_GET_BIND_GAME_ROLE_LIST = generateGameApi("/GetHasBindGameRoleList");
/** 设置默认游戏角色 */
export const API_GAME_SET_DEFAULT_GAME_ROLE = generateGameApi("/SetDefaultGameRoleInfo");
/** lip移除游戏绑定角色 */
export const API_GAME_DEL_BINDING_GAME_ROLE = generateGameApi("/DelBindingGameRoleInfo");
export const API_GET_SERVER_TS = generateSystemApi("/server_timestamp");
////////////////////////// GAME API //////////////////////////

/**
 * @description intl 相关接口
 */
export const API_INTL_DECRYPT_AES = generateIntlApi("/DecryptAes");

/**
 * @description Lipass 相关接口
 * @see {link https://yapi.gpts.woa.com/project/1344/interface/api/cat_14729}
 */
export const API_LIPASS_BIND_PRESENT_INFO = generateLipassActApi("/GetLipBindPresentInfo");
export const API_LIPASS_BIND_PRESENT = generateLipassActApi("/GetLipBindPresent");

//////////////////////// Community API //////////////////////////
/** 绑定第三方游戏账号 */
export const API_COMMUNITY_BINDING_ACCOUNT = generateCommunityApi("/Account/BindGameChannel");

////////////////////////// Tools API //////////////////////////
/**
 * @description nikke 工具站 API
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/cat_14205
 */
export const API_TOOLS_GET_USER_ROLE = generateToolsApi("/GetUserPlayerBasicInfo"); // 游戏内角色相关信息
export const API_TOOLS_GET_PLAYER_GAME_INFO = generateToolsApi("/GetPlayerBasicInfo"); // 游戏内信息, exp/进度等
export const API_TOOLS_GET_CLIENT_PLAYER_GAME_INFO = generateToolsApi("/GetUserSavedRoleInfo"); // 客态获取
export const API_TOOLS_GET_BATTLE_ROLE = generateToolsApi("/GetPlayerBattleInfo");
export const API_TOOLS_GET_EQUIPS = generateToolsApi("/GetPlayerEquipContents");
export const API_TOOLS_GET_PLAYER_NIKKES = generateToolsApi("/GetPlayerNikkes");
export const API_TOOL_GET_PLAYER_SORT_LIST = generateToolsApi("/GetUserNikkesOrder");
export const API_TOOL_SET_SELF_SORT_LIST = generateToolsApi("/SaveNikkesOrder");
export const API_TOOL_GET_SELF_SORT_LIST = generateToolsApi("/GetNikkesOrder");

////////////////////////// Tools API //////////////////////////

////////////////////////// Mission API //////////////////////////
export const API_MISSION_SHIFTYPAD_BIND_GAME_MISSION_STATUS = generateProxyGameApi(
  "/ShiftypadBindGameMissionStatus"
);
export const API_MISSION_HAS_FINISH_ONBOARDING_MISSION_LIST = generateProxyGameApi(
  "/HasFinishOnboardingMissionList"
);
export const API_MISSION_ADD_FINISH_ONBOARDING_MISSION = generateProxyGameApi(
  "/AddFinishOnboardingMission"
);
export const API_MISSION_ONBOARDING_MISSION_GIFT_COLLECTION = generateProxyGameApi(
  "/OnboardingMissionGiftCollection"
);
/** 获取任务列表 V2 */
export const API_REWARDS_GET_TASK_LIST_V2 = generateRewardsDirect("/GetTaskListV2");
/** 获取任务列表 V2 - 登录态 */
export const API_REWARDS_GET_TASK_LIST_WITH_STATUS_V2 = generateRewardsApi(
  "/GetTaskListWithStatusV2"
);

export const API_MISSION_GET_FOLLOW_TASK_OFFICIAL_ACCOUNTS = generateSSUGCDirectApi(
  "/Dynamics/GetFollowTaskOfficialAccounts"
);
export const API_MISSION_QUICKLY_FOLLOW_ALL_OFFICIAL_ACCOUNTS = generateSSUGCApi(
  "/Dynamics/QuicklyFollowAllOfficialAccounts"
);
////////////////////////// UNION API //////////////////////////
/** 查询公会卡片列表（游客态） */
export const API_GAME_QUERY_GUILD_CARDS_BY_TOURIST = generateDirectGameApi(
  "/QueryGuildCardsByTourist"
);
/** 查询公会卡片列表（登录态） */
export const API_GAME_QUERY_GUILD_CARDS = generateProxyGameApi("/QueryGuildCards");
/** 查询我的公会详情（登录态） */
export const API_GAME_GET_MY_GUILD_INFO = generateProxyGameApi("/GetMyGuildInfo");
/** 根据公会id查询公会详情信息（登录态） */
export const API_GAME_GET_GUILD_DETAIL = generateProxyGameApi("/GetGuildDetail");
/** 加入公会（登录态） */
export const API_GAME_JOIN_GUILD = generateProxyGameApi("/JoinGuild");
/** 发布公会卡片到公会广场（登录态） */
export const API_GAME_PUBLISH_GUILD_CARD = generateProxyGameApi("/PublishGuildCard");
/** 应援/取消应援 公会（登录态） */
export const API_GAME_SUPPORT_GUILD = generateProxyGameApi("/SupportGuild");
/** 查询公会应援用户信息列表（游客态） */
export const API_GAME_QUERY_GUILD_CARD_SUPPORTERS_BY_TOURIST = generateDirectGameApi(
  "/QueryGuildCardSupportersByTourist"
);
/** 查询公会广场热门帖子 */
export const API_GAME_QUERY_GUILD_HOT_POST = generateSSUGCDirectApi("/Dynamics/GetGuildHotPost");
