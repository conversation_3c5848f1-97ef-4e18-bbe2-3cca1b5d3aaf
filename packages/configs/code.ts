/**
 * @link https://git.woa.com/iegg_distribution/Standalone-Site/standalone-site-backend/blob/demo/app/code/1200000%20-%20129999_information_tweet.go
 */

export const CODE_ALL_CONFIGS = {
  // 活动未开始
  EVENT_NOT_STARTED: 200006,
  // 活动已结束
  EVENT_ENDED: 200007,

  NO_PERMISSION: 210002,
  GAME_NOT_LOGIN: 300001,
  // not bound lip
  NOT_BOUND_LIP: 303013,
  // 历史脏数据
  HISTORICAL_DIRTY_DATA: 400018,

  // 积分不足
  POINTS_NOT_ENOUGH: 503001,
  // 礼包领取已达个人限量
  GIFT_CARD_RECEIVE_LIMIT: 503002,
  // 礼包已被兑完
  GIFT_CARD_EXHAUSTED: 400013,
  // 礼包已被兑完
  GIFT_CARD_EXHAUSTED_2: 400016,

  // 区服不正确
  ServerNotCorrect: 1100036,

  // 验证码校验未通过
  CaptchaCheckFailed: 1100039,

  DefaultErr: 1200001,
  // TweetContentCheckErr 资讯内容校验失败
  TweetContentCheckErr: 1200002,
  // CreatePostAuditErr 创建审核流失败
  CreatePostAuditErr: 1200003,
  // UpdatePostAuditErr 更新审批流失败
  UpdatePostAuditErr: 1200004,
  // HTTPResponseEmptyError HTTP请求返回空对象
  HTTPResponseEmptyError: 1200005,
  // HTTPResponseError HTTP请求返回错误
  HTTPResponseError: 1200006,
  // HTTPResponseCodeError HTTP请求返回错误状态码
  HTTPResponseCodeError: 1200007,
  // GetXCommonParamsIsEmptyErr x-common-params为空
  GetXCommonParamsIsEmptyErr: 1200008,
  // GetXCommonParamsDeCodeErr 反序列化x-common-params失败
  GetXCommonParamsDeCodeErr: 1200009,
  // RichTextParseError 富文本解析失败
  RichTextParseError: 1200010,
  // CreateFailedTitleOverstepLimit 标题 是一起限制50
  // 发布失败,标题大于50字符
  CreateFailedTitleOverstepLimit: 1200011,
  // 图文的，文字限制1000,视频也是一起限制1000
  // 发布失败,内容大于10000字符
  CreateFailedContextOverstepLimit: 1200012,
  // 富文本的，限制11000，图片限制100张
  CreateFailedContextOverstepLimit1000: 1200013,
  // 图片超出限制
  CreateFailedIMGOverstepLimit: 1200014,
  // RichTextContainIllegalImg   富文本内容包含非法图像
  RichTextContainIllegalImg: 1200015,
  // CheckAuditPermissionErr 获取黑白名单失败
  CheckAuditPermissionErr: 1200016,
  // CreateFailedAuditPass 发布失败，被拉黑了
  CreateFailedAuditPass: 1200017,
  // UserCreatePostLimitMinuteError 发布限频，每分钟发动态过于频繁
  // 发送动态过于频繁，请稍后再试
  UserCreatePostLimitMinuteError: 1200018,
  // 发布限频，每小时发布动态超出限制
  // 发送动态过于频繁，请稍后再试
  UserCreatePostLimitHourError: 1200019,
  // 发布限频，超出每天发布动态上限
  UserCreatePostLimitDayError: 1200020,
  // 获取富文本主要内容失败
  NewDocumentFromReaderErr: 1200021,
  // 获取文章内容失败
  // 获取动态列表失败
  GetPostsFailed: 1200022,
  // 创建文章失败
  // 动态发布失败
  CreatePostFailed: 1200023,
  // 获取文章详情失败
  // 获取动态详情失败
  GetPostDetailFailed: 1200024,
  // 删除文章失败
  DeletePostFailed: 1200025,
  // 获取用户数据失败
  GetUserInfoFailed: 1200026,
  // 删除文章失败，没有权限
  DeleteFailedNotPermission: 1200027,
  GetCommunityDataHttpError: 1200028,
  GetCommunityDataInvalidParams: 1200029,
  ReviewCommentInvalidResp: 1200030,
  BatchReviewCommentInvalidResp: 1200031,
  DeleteCommunityCommentInvalidResp: 1200032,
  UpdateCommunityCommentInvalidResp: 1200033,
  AddCommunityCommentInvalidResp: 1200034,
  GetCommunityCommentsInvalidResp: 1200035,
  GetCommunityCommentInvalidResp: 1200036,
  // cms请求之后更新es中的post失败
  CMSUpdatePostError: 1200037,
  // 官方账户缺失
  GetOfficialGameUserError: 1200038,
  // cms通知删除资讯下的消息失败
  DeleteMessageByPostIDFailed: 1200039,
  // cms通知删除消息消息
  DeleteMessageByFeedIDsFailed: 1200040,
  UpdateCommunityParseExtInfoError: 1200041,
  GetCreatorHubDataInvalidParams: 1200042,
  GetCreatorHubDataHttpError: 1200043,
  GetCreatorHubDataInvalidResp: 1200044,

  GetUserTotalPointsInvalidResp: 1200045,
  GetUserTotalPointsReturnError: 1200046,
  GetLipPointsInvalidParams: 1200047,
  GetLipPointsDataHttpError: 1200048,
  COSConfigIsEmptyError: 1200049,
  IllegalCloudObjectType: 1200050,
  // InvalidParams参数错误
  InvalidParamsErr: 1200051,
  // No permission to view Shiftyspad
  NoPermissionVisitShiftyspad: 1301002,
  // 没有权限
  NoPermission: 1200052,
  // 更新文章可见性报错
  VisblePostFailed: 1200053,
  // 获取动态审批错误
  GetPostAuditFailed: 1200054,
  // 获取动态内容错误
  GetPostContentsFailed: 1200055,
  // 获取首页的动态列表错误
  GetIndexPostFailed: 1200056,
  // 获取话题失败
  GetTagFailed: 1200057,
  // 创建话题收藏失败
  CreateTagCollectionFailed: 1200058,
  // 删除话题收藏失败
  DeleteTagCollectionFailed: 1200059,
  // 获取用户文章收藏状态失败
  GetUserPosCollectionFailed: 1200060,
  // 资讯未审核不想允许收藏
  PostNotReviewStarFailed: 1200061,
  // 创建咨询收藏失败
  CreatePostCollectionFailed: 1200062,
  // 取消资讯收藏失败
  DeletePostCollectionFailed: 1200063,
  // 用户收藏、取消收藏操作太过于频繁
  UserCollectPostFrequentLimit: 1200064,
  // 获取自己收藏的文章列表失败
  GetPostListMyIsCollectionFailed: 1200065,
  // 获取自己是否评论失败
  GetPostListMyIsCommentFailed: 1200066,
  // 用户资讯点赞状态失败
  GetPostStarFailed: 1200067,
  // 创建用户点赞失败
  CreatePostStarFailed: 1200068,
  // 取消用户点赞失败
  DeletePostStarFailed: 1200069,
  // 用户点赞、取消点赞异常频繁
  UserStarPostFrequentLimit: 1200070,
  // 创建主题失败
  CreateTagError: 1200071,
  // 创建主题审核流失败
  CreateTagAuditError: 1200072,
  // 获取用户关注失败
  GetUserCollectionListError: 1200073,
  // 没有找到用户
  NoExistUsername: 1200074,
  // 获取用户关注失败
  GetUserFollowFailed: 1200075,
  // 创建用户关注失败
  CreateUserCollectionFailed: 1200076,
  // 用户取消关注失败
  DeleteUserCollectionFailed: 1200077,
  // 用户关注或者取消关注太频繁
  UserFollowOtherFrequentLimit: 1200078,
  // 获取用户绑定称号失败
  GetUserBindTitleFailed: 1200079,
  // 今日私信次数已达上限
  TooManyWhisperNum: 1200080,
  // 获取用户首次登录积分消息失败
  GetUserFirstAddPointsMsgErr: 1200081,
  // 添加消息失败
  AddMsgInvalidExtInfo: 1200082,
  // 获取文章列表失败
  GetPostFailed: 1200083,
  // 获取评论列表失败
  GetCommentFailed: 1200084,
  // 错误的的举报类型
  InvalidPostReportContentType: 1200085,
  // 自己不能举报自己
  NoNeedToReportSelfPost: 1200086,
  // 创建举报数据失败
  CreatePostReportError: 1200087,
  // 创建评论每分钟限频
  UserCreateCommentLimitMinuteError: 1200088,
  // 创建评论每小时限频
  UserCreateCommentLimitHourError: 1200089,
  // 创建评论每天限频
  UserCreateCommentLimitDayError: 1200090,
  // 创建评论失败
  CreateCommentFailed: 1200091,
  // 创建评论失败，资讯正在审核中
  CreatePostCommentFailedAuditIng: 1200092,
  // 超出最大评论数
  MaxCommentCount: 1200093,
  // 创建回复失败
  CreateReplyFailed: 1200094,
  // 创建回复时未资讯审核
  CreatePostCommentReplyFailedAuditIng: 1200095,
  // 创建回复的回复时资讯未审核
  CreatePostCommentReplyReplyFailedAuditIng: 1200096,
  // 评论未审核不允许点赞
  CommentNotReviewStarFailed: 1200097,
  // 获取评论点赞失败
  GetCommentStarFailed: 1200098,
  // 创建评论点赞失败
  CreateCommentStarFailed: 1200099,
  // 取消评论点赞失败
  DeleteCommentStarFailed: 1200100,
  // 用户评论点赞限频
  UserStarCommentFrequentLimit: 1200101,
  // 资讯评论为空
  GetCommunityCommentEmpty: 1200102,
  // 参数错误
  InvalidParams: 1200103,
  // 获取白名单失败
  GetUserWhiteListInfoError: 1200104,
  // 获取黑白审核列表失败
  GetUserPermissionAuditInfoError: 1200105,
  // 获取用户信息失败
  GetUserInfoError: 1200106,
  // 获取用户是否首次注册失败
  GetUserIsFirstRegisterError: 1200107,
  // 更新用户隐私协议失败
  UpdateUserSignProtocolError: 1200108,
  // 获取用户称号失败
  GetUserTitleError: 1200109,
  // 随机昵称达到最大限制
  MaxCreateUsernameError: 1200110,
  // 创建用户昵称失败
  CreateUsernameError: 1200111,
  // 获取头像列表失败
  AvatarGetListFailed: 1200112,
  //	用户注册失败
  UserRegisterFailed: 1200113,
  // 更新用户信息失败
  UpdateUserInfoErr: 1200114,
  // 更新用户昵称失败
  UpdateUsernameError: 1200115,
  // 创建用户昵称审核没有openid
  CreateUserNameAuditNotOpenidFailed: 1200116,
  // 更新用户昵称审核已经存在了
  UpdateUsernameAuditExistsError: 1200117,
  // 创建用户签名没有openid
  CreateUserRemarkAuditNotOpenidFailed: 1200118,
  // 更新用户签名审核已经存在数据
  UpdateUserRemarkAuditExistsError: 1200119,
  // 用户名已存在
  UsernameHasExisted: 1200120,
  // 用户名长度限制
  NicknameLengthLimit: 1200121,
  // 用户名不符合规范
  MathchUserNameFailed: 1200122,
  // 用户未成年
  UserIsNotAdultFailed: 1200123,
  // 获取审核数据列表失败
  GetUserAuditInfoListFailed: 1200124,
  // 更新用户信息失败
  UpdateUserInfoError: 1200125,
  // 更新用户签名失败
  UpdateUserRemarkFailed: 1200126,
  // 链接错误
  InvalidAvatarUrlError: 1200127,
  // 获取头像链接解析错误
  AvatarGetListFailedToJsonDecode: 1200128,
  // 获取头像绑定游戏的列表错误
  AvatarGetBindingGameListFailed: 1200129,
  // 获取评论内容失败
  GetCommentContentFailed: 1200130,
  // 获取回复列表失败
  GetCommentReplyListFailed: 1200131,

  //站内信反序列化json失败
  SiteMsgJsonDecodeFailed: 1200132,
  //站内信序列化json失败
  SiteMsgJsonCodeFailed: 1200133,
  //站内信写入队列中失败
  SiteMsgWriteToQueueFailed: 1200134,
  //站内信获取缓存失败
  SiteMsgGetCacheFailed: 1200135,
  //站内信获取信息失败
  GetSiteMessageInfoFailed: 1200136,
  //创建用户站内信失败
  CreateSiteMessageFailed: 1200137,

  //站内信的状态更改失败
  SiteMessageStatusFailed: 1200138,
  //推送类型不存在
  PushTypeNotExists: 1200139,
  //更改站内信用户号码包是否导入状态失败
  SiteMsgCreatedDataStatusFailed: 1200140,

  // 表名不存在
  NoTableName: 1200141,
  //获取全量用户不存在
  GetAllUserListFailed: 1200142,

  // 读取站内信号码包失败
  ReadSiteMessageNumberPackageFailed: 1200143,

  // 下载文件失败
  FileDownloadFailed: 1200144,

  // 站内信kafka写入失败
  SiteMessageProducerErr: 1200145,

  // 更新动态点赞数失败
  UpdateDynamicUpvoteCountFailed: 1200146,

  // 获取COS配置信息异常
  GetCOSConfigError: 1200147,
  // 获取消息列表失败
  GetMessageFailed: 1200148,
  //用户隐私开关反序列化开关失败
  UserPrivacySwitchJsonDecodeFailed: 1200149,
  //获取用户隐私协议开关失败
  GetUserPrivacySwitchFailed: 1200150,
  // 用户隐私协议开关类型不合法
  UserPrivacySwitchTypeFailed: 1200151,

  // 删除Redis缓存失败
  DelCacheByKeyErr: 1200152,
  // 获取锁失败
  RedisLockErr: 1200153,
  // 删除Redis锁失败
  RedisUnlockErr: 1200154,

  // 视频链接解析异常
  ParseVideoUrlError: 1200155,
  GetVideoUrlError: 1200156,
  ParseVideoUrlEmpty: 1200157,

  // 获取金刚区配置异常
  GetDistrictListError: 1200158,
  // 获取表情包列表异常
  GetEmoticonListError: 1200159,
  // 获取表情包关联关系列表失败
  GetEmoticonIconGroupRelationError: 1200160,

  // 设置用户心情失败
  SetUserMoodError: 1200161,
  // 用户重新关注失败
  UserReCollectionFailed: 1200162,
  // 重新资讯收藏失败
  PostReCollectionFailed: 1200163,
  PostUpvoteMapParseError: 1200164,
  GetPostUpvoteMapError: 1200165,

  CMSSetMuteUserInfoError: 1200166,
  CMSDelMuteUserInfoError: 1200167,
  CMSSetAdminUserInfoError: 1200168,
  CMSDelAdminUserInfoError: 1200169,
  CMSSetAuthUserInfoError: 1200170,
  CMSDelAuthUserInfoError: 1200171,

  // 重复举报
  RepeatedReportError: 1200172,
  UpdatePostContentError: 1200173,
  UpdateContentReportError: 1200174,

  // 更新用户审核数据记录失败
  UpdateUserAuditInfoError: 1200175,

  // 迁移帖子失败
  MovePostErr: 1200176,

  // 不支持该语言
  InvalidLanguageParams: 1200177,

  // 查询用户NIKKE游戏信息失败
  GetUserPlayerBasicInfoError: 1200178,

  // 用户设置了隐私，不可对外展示游戏卡片
  GetUserDisableGameCardError: 1200179,

  // 设置用户Nikke游戏tag失败
  SetUserGameTagError: 1200180,

  // 图文类型动态，一定要上传图片
  CreateFailedNeedPicError: 1200181,

  // 更新动态点赞数失败
  UpdateDynamicUpvoteCountJsonFailed: 1200182,

  // 评论已审核
  CommentAlreadyReviewed: 1200183,
  // 动态已审核
  PostAlreadyReviewed: 1200184,
  // 获取板块失败
  GetPlateFailed: 1200185,
  // event转换json失败
  EventJsonCodeFailed: 1200186,

  // 分页游标不合法
  PagingCursorIsInvalidS: 1200187,
  // 分页游标不合法
  PagingCursorIsInvalidI: 1200188,
  // 获取站内信所有信息映射关系异常
  GetAllSiteMessageMapFailed: 1200189,
  // 获取活动列表错误
  GetCreatorHubListFailed: 1200190,
  // 获取活动多语言列表错误
  GetCreatorHubLanguageListFailed: 1200191,
  // 获取活动塞到列表错误
  GetCreatorHubRankListFailed: 1200192,
  // 调用AIGC的翻译功能服务异常
  CallAIGCTranslateApiFailed: 1200193,
  // 解析AIGC的翻译功能服务返回体异常
  ParseAIGCTranslateApiResultFailed: 1200194,
  // 获取翻译缓存异常
  CacheGeTranslateDataErr: 1200195,
  // 原文条数和翻译文本条数对不上异常
  TranslateTextCountNotMatchErr: 1200196,
  // 获取板块多语言映射失败
  GetAllPlateLanguageMapFailed: 1200197,
  // 不能转发别人未审批的动态
  CanNotForwardUnapprovedPost: 1200198,
  // 为用户设置头像挂件失败
  AddAvatarPendantToUserFailed: 1200199,
  // 解析用户头像挂件列表失败
  AvatarPendansGetListFailedToJsonDecode: 1200200,
  // 获取用户头像挂件列表失败
  AvatarPendantGetListFailed: 1200201,
  // 为用户设置挂件失败
  SetAvatarPendantToUserFailed: 1200202,
  // 挂件未获得
  AvatarPendantNotAvalible: 1200203,
  // 获取穿戴挂件失败
  GetAvatarPendantFailed: 1200204,

  // 未绑定 CreatorHub
  NotBindCreatorHub: 1200303,
  // CH 接口异常
  CreatorHubApiError: 1200304,
  // 修改网红同步接口异常
  ChangeCreatorHubSubmissionError: 1200305,
  // CreatorHub 绑定失败
  CreatorHubBindError: 1200306,
  // CreatorHub 账号已经绑定
  CreatorHubAccountHasBound: 1200307,
  // CreatorHub 账号未注册
  CreatorHubAccountNotRegistered: 1200308,
  // CreatorHub 账号审核中
  CreatorHubAccountAuditing: 1200309,
  // CreatorHub 账号异常
  CreatorHubAccountAbnormal: 1200310,
  // 渠道不支持
  ChannelNotSupport: 1200311,
  // 独立站账户已绑定
  StandloneSiteHasBound: 1200312,
  // 无效的token
  InvalidToken: 1200313,
  // 该账号认证被驳回
  CreatorHubAccountRejected: 1200314,
  // 该账号已被冻结
  CreatorHubAccountFrozen: 1200315,
  // 请重新登录，完成家长认证后再进行操作
  CreatorHubAccountParentAuth: 1200316,

  // 该用户已被禁言
  UserMuted: 1200319,

  // 该账号已被冻结(绑定时)
  CreatorHubAccountFrozenBind: 1200322,

  // 对方已讲你拉黑
  UserHasBlockedYou: 1200354,

  // 此礼包已被领取
  GiftAlreadyReceived: 1300013,
  // cdkey参数错误
  CdkeyParamsWrong: 1302002,
  // 兑换码不合法
  CdkeyCodeInvalid: 1302003,
  // gameID 参数获取失败
  CdkeyGameidWrong: 1302004,
  // OpenID 参数获取失败
  CdkeyOpenidWrong: 1302005,
  // RoleID 参数获取失败
  CdkeyRoleIdWrong: 1302006,
  // 系统请求失败，请重试
  CdkeySystemWrong: 1302007,
  // 没有权限兑换，可能是活动未创建，或者活动下线，或者这个码不是这个业务申请的，业务A的用户不能兑换业务B的兑换码，或者用户不在白名单内
  CdkeyNoAuth: 1302008,
  // 不在活动时间范围内
  CdkeyNotInTime: 1302009,
  // 活动限制了固定区服的用户才能兑换，此区服的用户不能兑换
  CdkeyActivityNotAvailableInThisArea: 1302010,
  // 活动限制了固定国家的用户才能兑换，此国家的用户不能兑换
  CdkeyActivityNotAvailableInThisCountry: 1302011,
  // 活动限制了固定平台（IOS，安卓等）的用户才能兑换，此平台的用户不能兑换
  CdkeyActivityNotAvailableInThisPlatform: 1302012,
  // MRMS 礼包发放失败
  CdkeyMRMSFailed: 1302013,
  // CDK 码被别的用户使用过了
  CdkeyCodeIsUsed: 1302014,
  // CDK 码不存在
  CdkeyCodeDoesNotExist: 1302015,
  // 用户已经使用过 CDK 码了
  CdkeyUserAlreadyUsed: 1302016,
  // CDK 码/用户的兑换次数超过上限
  CdkeyUserExceedsMaxLimit: 1302017,
  // 访问太频繁，你正在使用该 CDK 码，请稍后尝试
  CdkeyUserTooFrequent: 1302018,
  // 网络错误，请重试
  CdkeyNetworkError: 1302019,
  // 输入错误的 CD Key 多次
  CdkeyIncorrectMultipleTimes: 1302020,

  // 不能添加自己好友卡
  CannotAddMyselfFriendCard: 1200235,

  // 用户不存在
  UserNotExist2: 1200256,
  // 拉黑失败
  BlockUserFailed: 1200349,
  // 用户已经被拉黑
  UserAlreadyBlocked: 1200348,
  // 用户没有被拉黑，禁止取消拉黑
  UserNotBlocked: 1200352,
  // 取消拉黑失败
  UnblockUserFailed: 1200353,

  // 添加失败
  AddFriendCardFailed: 1302102,
  // 调用接口返回异常
  ApiCallReturnException: 1302103,
  // 您的好友申请已达30个上限，无法发送更多申请
  RequestOverflow: 1302104,
  // 对方的好友申请已达30个上限，无法接受更多申请
  ReceiveOverflow: 1302105,
  // 接收方已经有30个好友，所以不能发送好友请求
  FriendOverflow: 1302106,
  // 已发送过好友申请（或对方已向你发送好友申请）
  FriendReceived: 1302107,
  // 目标用户不存在（例如，账户已删除）
  FriendNotExist: 1302108,
  // 后台异常，请稍后再试
  FriendException: 1302109,

  // 服务维护中
  ServiceInMaintenanceError: 1302120,

  // 翻译失败
  TranslateError: 1200262,
  // 没绑定角色
  NotBindGameRole: 1302101,
  // 重复添加好友异常
  DuplicateAddFriendException: 1302116,

  // 当前用户未启用添加好友权限
  AddFriendNotEnabled: 1302122,
  // 被加好友不在当前大区
  AddFriendNotInCurrentArea: 1302123,

  // 对方已将是你的好友了
  FriendAlreadyAdded: 1302124,

  /**
   * 公会相关错误码
   * @see: https://yapi.gpts.woa.com/project/1728/interface/api/126148
   */
  // 参数错误
  InvalidParam: 1303001,
  // 系统错误
  SystemError: 1303002,
  // 公会不存在
  GuildIdNotExist: 1303003,
  // 用户所在区服无此游戏角色
  RoleIdNotExist: 1303004,
  // 用户未绑定游戏角色
  UserNotYetBindRoleId: 1303005,
  // 公会卡片已发布
  GuildCardPublished: 1303006,
  // 公会卡片未发布
  GuildCardNotPublished: 1303007,
  // 用户绑定游戏角色不匹配
  UserBindRoleIdNotMatch: 1303008,
  // 用户未加入公会
  UserHasNoJoinGuild: 1303009,
  // 用户未加入此公会
  UserHasNoJoinThisGuild: 1303010,
  // 不属于同一区服
  JoinGuildNotTheSameArea: 1303011,
  // 维护中
  JoinGuildUnderMaintenance: 1303012,
  // 不满足公会等级要求
  JoinGuildInvalidLevel: 1303013,
  // RequestFull
  JoinGuildRequestFull: 1303014,
  // Reject
  JoinGuildReject: 1303015,
  // 公会已满
  JoinGuildFull: 1303016,
  // 已经存在申请待通过
  JoinGuildAlreadyRequested: 1303017,
  // UnableJoin
  JoinGuildUnableJoin: 1303018,
  // NeedApproval
  JoinGuildNeedApproval: 1303019,
  // 已经在此公会
  JoinGuildAlreadyJoined: 1303020,
  // 公会卡片不存在
  GuildCardUUIDNotExist: 1303021,
  // 用户当前所在公会与发布的公会不一致
  UserGuildNotMatchRequestGuildId: 1303022,
  // 游戏维护中
  GuildUnderMaintenance: 1303023,
  // 用户所在区服无此公会
  UserGuildNotExist: 1303024,
  // 用户不允许在shiftyspad展示公会
  UserNotAllowShowGuildInShiftypad: 1303025,

  // 接口超时
  ApiTimeoute: 101,
  // 接口超时
  ApiTimeout: 102,
  // 话题不存在（不需要进入 404 页面，可能是发布的时候带上一个不存在的话题，那么接口会报这个错）
  TopicNotExist: 1200280,
  // 用户不存在
  UserNotExist: 1200261,
  // 操作太快了，请稍后再试
  OperationTooFast: 212000,
  // 操作太快了，请稍后再试
  OperationTooFast2: 212001,
  // 用户未成年
  ErrCodeUserNotAdult: 1200316,
  // 设置置顶置底失败
  ErrSetCommentTopButtom: 1200325,
  // 设置评论置顶置底无权限
  ErrSetCommentTopButtomNoPermissoin: 1200326,
  // 已置顶/置底
  ErrCommentAlreadyTopButtom: 1200327,
  // 帖子不存在
  ErrSetCommentTopButtomNoPost: 1200328,
  // 评论不存在
  ErrSetCommentTopButtomNoComment: 1200329,
} satisfies Record<string, number>;

/**
 * 默认会导致跳转 404 的错误码
 * - 跳转 404 时，不会弹出错误提示
 * - 可以配置 fe_ignore_not_found_jump 来忽略跳转，并弹出错误提示
 */
export const CODES_NOT_FOUND = [
  CODE_ALL_CONFIGS.GetTagFailed,
  CODE_ALL_CONFIGS.GetPostsFailed,
  CODE_ALL_CONFIGS.GetPostFailed,
  CODE_ALL_CONFIGS.NoExistUsername,
  CODE_ALL_CONFIGS.GetUserPrivacySwitchFailed,
  CODE_ALL_CONFIGS.GetCommentFailed,
];

/**
 * 忽略错误 toast 的错误码
 */
export const CODES_IGNORE_TOAST = [
  // ...CODES_NOT_FOUND,
  CODE_ALL_CONFIGS.NOT_BOUND_LIP,
  CODE_ALL_CONFIGS.HISTORICAL_DIRTY_DATA,
  CODE_ALL_CONFIGS.GAME_NOT_LOGIN,
  CODE_ALL_CONFIGS.NotBindGameRole,
  CODE_ALL_CONFIGS.NoPermissionVisitShiftyspad,
];

const generateI18nKey = (code: string | number) => `api_code_${code}`;

export const CODE_MESSAGE_MAP: Record<number, string> = Object.values(CODE_ALL_CONFIGS).reduce(
  (acc: Record<number, string>, cur: number) => {
    return {
      ...acc,
      [cur]: generateI18nKey(cur),
    };
  },
  {}
);
