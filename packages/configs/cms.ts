// import { ENV_PRE, ENV_PROD } from "./env";
import { INTL_HMT_GAME_ID, INTL_GLOBAL_GAME_ID } from "./intl";

export const CMS_TEST_HOST = "t-sg-community.playerinfinite.com";
export const CMS_PRE_SG_HOST = "pre-sg-community.playerinfinite.com";
export const CMS_PROD_HOST = "sg-community.playerinfinite.com";

export const CMS_COLUMN_NAME_REWARDS = "Rewards";
export const CMS_COLUMN_NAME_FAQ = "FAQ";
export const CMS_COLUMN_NAME_REWARDS_BANNER = "RewardsBanner";
export const CMS_COLUMN_NAME_CHECKIN_BANNER = "CheckInBanner";
export const CMS_GAME_ID = "30054";
export const CMS_AREA_ID = "global";

export const CMS_SOURCE_TYPE = "nikke_stand_alone_site";
export const CMS_COLUMN_NAME_HOME = "Home";
export const CMS_COLUMN_NAME_HOME_BANNER = "Head";

export const CMS_COLUMN_NAME_MISSION = "Mission";
export const CMS_COLUMN_NAME_MISSION_BANNER = "Banner";

export const CMS_NIKKE_GAME_ID = "16";

export const CMS_INTL_GAME_ID_MAP_CMS_NIKKE_GAME_CONFIG = {
  [INTL_HMT_GAME_ID]: {
    game_id: CMS_NIKKE_GAME_ID,
    area_id: "tw",
  },
  [INTL_GLOBAL_GAME_ID]: {
    game_id: CMS_NIKKE_GAME_ID,
    area_id: "global",
  },
};
