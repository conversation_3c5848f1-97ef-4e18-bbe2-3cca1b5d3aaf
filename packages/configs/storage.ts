export const STORAGE_LS_PREFIX = `__ss_storage_ls_cache`;
export const STORAGE_SS_PREFIX = `__ss_storage_ss_cache`;
export const STORAGE_WINDOW_PREFIX = `__ss_storage_window_cache`;
export const STORAGE_INDEXDB_PREFIX = `__ss_storage_indexdb_cache`;
export const STORAGE_COOKIE_PREFIX = `__ss_storage_cookie_cache`;
export const STORAGE_CS_PREFIX = `__ss_storage_cs_cache`;

export const STORAGE_SS_INDEX_DB_NAME = "__ss_index_db_name";
export const STORAGE_SS_OBJECT_STORE_NAME = "__ss_object_store_name";

// ls
export const STORAGE_LS_LOGIN_META = `${STORAGE_LS_PREFIX}_login_meta__`;
export const STORAGE_LS_GAME_CHECK_IN = `${STORAGE_LS_PREFIX}_game_check_in__`;
export const STORAGE_LS_VERSION = `${STORAGE_LS_PREFIX}_version__`;
/** @deprecated 历史遗留字段，请勿使用，改成从cookie获取 —— STORAGE_COOKIE_GAME_ID */
export const STORAGE_LS_GAME_ID = `${STORAGE_LS_PREFIX}_game_id__`;
export const STORAGE_LS_LANG = `${STORAGE_LS_PREFIX}_lang__`;
export const STORAGE_LS_SHIFTYS_HINT = `${STORAGE_LS_PREFIX}_shiftyhint_v2__`;
export const STORAGE_LS_POST_DETAIL_COMMENT_SORT = `${STORAGE_LS_PREFIX}_post_detail_comment_sort__`;
export const STORAGE_LS_POST_COMOPOSE_UNION_CARD_NEW = `${STORAGE_LS_PREFIX}_post_compose_union_card_new__`;

export const STORAGE_LS_SHOWED_ADD_TO_SCREEN = `${STORAGE_LS_PREFIX}_lip_showed_add_to_screen__`;

export const STORAGE_LS_LOCAL_SAVED_REGIONS = `${STORAGE_LS_PREFIX}_local_saved_regions__`;

export const STORAGE_LS_TLOG_EVENTS_QUEUE = `${STORAGE_LS_PREFIX}_tlog_events_queue__`;

// ss

// indexdb
export const STORAGE_INDEXDB_CDN_CONFIGS = `${STORAGE_INDEXDB_PREFIX}_cdn_configs__`;
export const STORAGE_INDEXDB_I18N_CONFIGS = `${STORAGE_INDEXDB_PREFIX}_i18n_configs__`;
export const STORAGE_INDEXDB_VERSION_CONFIGS = `${STORAGE_INDEXDB_PREFIX}_version_configs__`;
export const STORAGE_INDEXDB_HISTORY_EMOJIS = `${STORAGE_INDEXDB_PREFIX}_history_emojis__`;
export const STORAGE_INDEXDB_HISTORY_GAMEDATA = `${STORAGE_INDEXDB_PREFIX}_history_gamedata__`;
export const STORAGE_INDEXDB_IMAGE_INFO = `${STORAGE_INDEXDB_PREFIX}_history_image_info__`;

// windows
export const STORAGE_WINDOW_CDN_CONFIGS = `${STORAGE_WINDOW_PREFIX}_cdn_configs__`;
export const STORAGE_WINDOW_I18N_CONFIGS = `${STORAGE_WINDOW_PREFIX}_i18n_configs__`;
export const STORAGE_WINDOW_VERSION_CONFIGS = `${STORAGE_WINDOW_PREFIX}_version_configs__`;

// cookies
export const STORAGE_COOKIE_GAME_ID = `${STORAGE_COOKIE_PREFIX}_game_id__`;
export const STORAGE_COOKIE_LANG = `${STORAGE_COOKIE_PREFIX}_lang__`;

// cache storage
export const STORAGE_CS_IMAGE = `${STORAGE_CS_PREFIX}_image__`;
export const STORAGE_CS_JS = `${STORAGE_CS_PREFIX}_js__`;
export const STORAGE_CS_CSS = `${STORAGE_CS_PREFIX}_css__`;
export const STORAGE_CS_FONT = `${STORAGE_CS_PREFIX}_font__`;

// external: 其他组件引用到的storage key, 如 pa-account-utils
export const STORAGE_PA_ACCOUNT_INFO = "lip-user-info";
