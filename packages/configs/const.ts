export const CONST_NIKKE_DEV_PORT = 9173;

export const CONST_NIKKE_APP_NAME = "nikke";

export const CONST_PATH_PREFIX = {
  [CONST_NIKKE_APP_NAME]: CONST_NIKKE_APP_NAME,
};

// 20MB
export const CONST_MAX_IMAGE_LIMIT_SIZE_UNITE = 20;
export const CONST_MAX_IMAGE_LIMIT_SIZE = CONST_MAX_IMAGE_LIMIT_SIZE_UNITE * 1024 * 1024;

export const CONST_SUPPORTED_IMAGE_TYPES = ["image/png", "image/jpeg", "image/gif", "image/webp"];

export const CONST_SUPPORTED_IMAGE_POSTFIXS = CONST_SUPPORTED_IMAGE_TYPES.map(
  (type) => `.${type.split("/")[1]}`
);

export const CONST_URL_WHITE_LIST_KEY = [
  "role_id",
  "role_name",
  "area_id",
  "zone_id",
  "channelid",
  "user_name",
  "os",
  "ts",
  "sdk_version",
  "seq",
  "encodeparam",
  "theme",
  "lang",
  "gameid",
  "is_log",
  "had_reloaded_page",
  "webid",
  "version",
];
