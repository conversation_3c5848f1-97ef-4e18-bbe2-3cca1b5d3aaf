export const createProxyConfig = (proxy: string, extra_proxy?: Record<string, string>) => {
  const default_proxy_settings = {
    "/api/system": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/user": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/game": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/lip": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/ugc": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/act": {
      target: proxy,
      changeOrigin: true,
    },
    "/api/flow": {
      target: proxy,
      changeOrigin: true,
    },
    "/standalone_site/ugc": {
      target: proxy,
      changeOrigin: true,
    },
    "/api": {
      target: "https://t-sg-community.playerinfinite.com",
      changeOrigin: true,
    },
  };

  return Object.assign({}, default_proxy_settings, extra_proxy ?? {});
};
