const BLABLA_HOST = () => {
  if (typeof location === "object") return location.hostname;
  return "www.blablalink.com";
};

// ReturnType<typeof getStandardizedLang>
const valid_complaince_lang = ["en", "ja", "zh", "ko", "zh-TW"];
const get_valid_lang = (lang: string) => (valid_complaince_lang.includes(lang) ? lang : "en");

// NIKKE
export const GET_NIKKE_PRIVACY_POLICY = () => "https://nikke-en.com/privacypolicy/";
export const GET_NIKKE_EULA = () => "https://nikke-en.com/termsofservice/";

// blabla
export const GET_BLABLA_EULA = (lang: string) =>
  `https://${BLABLA_HOST()}/compliance/termsofservice/${get_valid_lang(lang)}.html`;
export const GET_BLABLA_NDA = (lang: string) =>
  `https://${BLABLA_HOST()}/compliance/nda/${get_valid_lang(lang)}.html`;
export const GET_BLABLA_COMMUNITY_GUIDELINES = (lang: string) =>
  `https://${BLABLA_HOST()}/compliance/communityguidelines/${get_valid_lang(lang)}.html`;

// LIP
export const GET_LIP_EULA = () => "https://pass.levelinfinite.com/app/terms-of-service.html";
export const GET_LIP_PRIVACY_POLICY = () => "https://account.levelinfinite.com/privacypolicy.html";
