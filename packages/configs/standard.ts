export const STANDARD_LANG_KEY = "lang";
export const STANDARD_GAME_ID_KEY = "gameid";
// ingame
export const STANDARD_URL_ROLE_ID = "role_id";
export const STANDARD_URL_ZONE_ID_KEY = "zone_id";
export const STANDARD_URL_AREA_ID_KEY = "area_id";
export const STANDARD_URL_INGAME_ENCODE_TICKET = "encodeparam";
export const STANDARD_URL_INGAME_CHANNEL_ID = "channelid";

export const STANDARD_URL_KEY = {
  // lang
  lang: STANDARD_LANG_KEY,
  language: STANDARD_LANG_KEY,
  lang_type: STANDARD_LANG_KEY,
  sLanguage: STANDARD_LANG_KEY,
  s_language: STANDARD_LANG_KEY,

  // gameid
  gameid: STANDARD_GAME_ID_KEY,
  gameId: STANDARD_GAME_ID_KEY,
  game_id: STANDARD_GAME_ID_KEY,
};

// 这里是对齐了 cms 的语言文件
export const STANDARD_CMS_LANG_MAP = {
  en: "en",
  "2": "en",

  vi: "vi",
  "11": "vi",

  th: "th",
  "10": "th",

  ko: "ko",
  "4": "ko",

  fr: "fr",
  "6": "fr",

  de: "de",
  "7": "de",

  ru: "ru",
  "12": "ru",

  tr: "tr",
  "13": "tr",

  es: "es-US",
  esUS: "es-US",
  es_us: "es-US",
  "es-US": "es-US",
  "8": "es-US",
  "16": "es-US",
  eslt: "es-US",
  es_lt: "es-US",

  it: "it",
  "9": "it",

  nl: "nl",
  "17": "nl",

  ms: "ms",
  my: "ms",
  "18": "ms",

  id: "id",
  "19": "id",

  ar: "ar",
  "20": "ar",

  ja: "ja",
  jp: "ja",
  "3": "ja",

  ur: "ur",
  bn: "bn",

  hi: "hi",
  "21": "hi",

  zh: "zh",
  zh_cn: "zh",
  zh_hans: "zh",
  "zh-CN": "zh",
  "zh-Hans": "zh",
  CHS: "zh",
  "1": "zh",
  "zh-Hans-SG": "zh",

  "zh-HK": "zh-HK",
  zhHK: "zh-HK",
  zh_hk: "zh-HK",
  hk: "zh-HK",
  "zh-hk": "zh-HK",

  "zh-TW": "zh-TW",
  zhTW: "zh-TW",
  zh_tw: "zh-TW",
  tw: "zh-TW",
  CHT: "zh-TW",
  "zh-tw": "zh-TW",
  "5": "zh-TW",
  "zh-Hant": "zh-TW",

  // 葡萄牙语拉美
  pt: "pt-BR",
  "pt-br": "pt-BR",
  pt_br: "pt-BR",
  ptbr: "pt-BR",
  "14": "pt-BR",
  "15": "pt-BR",

  ptAll: "pt-all",

  uz: "uz",
} as const;

// type A = typeof STANDARD_CMS_LANG_MAP;

export type StandardCmsLangMapType =
  (typeof STANDARD_CMS_LANG_MAP)[keyof typeof STANDARD_CMS_LANG_MAP];

// 支持的语言
export const STANDARD_SS_SUPPORT_LANG: StandardCmsLangMapType[] = [
  // STANDARD_CMS_LANG_MAP.zh,
  STANDARD_CMS_LANG_MAP.tw,
  STANDARD_CMS_LANG_MAP.en,
  STANDARD_CMS_LANG_MAP.ja,
  STANDARD_CMS_LANG_MAP.ko,
  // STANDARD_CMS_LANG_MAP.th,
  // STANDARD_CMS_LANG_MAP.es,
  // STANDARD_CMS_LANG_MAP.pt,
  // STANDARD_CMS_LANG_MAP.tr,
  // STANDARD_CMS_LANG_MAP.ru,
  // STANDARD_CMS_LANG_MAP.ar,
  // STANDARD_CMS_LANG_MAP.id,
  // STANDARD_CMS_LANG_MAP.ms,
];

/**
 * @description 对齐 intl sdk 登录框的语言
 * @link https://docs.playernetwork.intlgame.com/docs/zh/Level-Infinite-Pass/Integration/Get-started/#changeLanguage
 * @var {[type]}
 */
export const STANDARD_INTL_LOGIN_SDK_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.zh]: "zh-Hans",
  [STANDARD_CMS_LANG_MAP.tw]: "zh-TW",
  [STANDARD_CMS_LANG_MAP.en]: "en",
  [STANDARD_CMS_LANG_MAP.ja]: "ja",
  [STANDARD_CMS_LANG_MAP.ko]: "ko",
  [STANDARD_CMS_LANG_MAP.th]: "th",
  [STANDARD_CMS_LANG_MAP.es]: "es",
  [STANDARD_CMS_LANG_MAP.pt]: "pt",
  [STANDARD_CMS_LANG_MAP.tr]: "tr",
  [STANDARD_CMS_LANG_MAP.ru]: "ru",
  [STANDARD_CMS_LANG_MAP.ar]: "ar",
  [STANDARD_CMS_LANG_MAP.id]: "id",
  [STANDARD_CMS_LANG_MAP.ms]: "ms",
};

/**
 * @description 对齐 account 中台账号中心的语言
 * @link https://docs.playernetwork.intlgame.com/docs/zh/Support/FAQ/SDKRelated/LangType/
 */
export const STANDARD_ACCOUNT_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.zh]: "zh-Hans",
  [STANDARD_CMS_LANG_MAP.tw]: "zh-Hant",
  [STANDARD_CMS_LANG_MAP.en]: "en",
  [STANDARD_CMS_LANG_MAP.ja]: "ja",
  [STANDARD_CMS_LANG_MAP.ko]: "ko",
  [STANDARD_CMS_LANG_MAP.th]: "th",
  [STANDARD_CMS_LANG_MAP.es]: "es",
  [STANDARD_CMS_LANG_MAP.pt]: "pt",
  [STANDARD_CMS_LANG_MAP.tr]: "tr",
  [STANDARD_CMS_LANG_MAP.ru]: "ru",
  [STANDARD_CMS_LANG_MAP.ar]: "ar",
  [STANDARD_CMS_LANG_MAP.id]: "id",
  [STANDARD_CMS_LANG_MAP.ms]: "ms",
};

/**
 * @description 对齐 midas 多语言，ar,hk,tr,de,id,pu,tw,en,ja,ru,vn,es,ko,fr,my,th
 * @link https://iwiki.woa.com/p/**********
 */
export const STANDARD_MIDAS_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.tw]: "hk",
  [STANDARD_CMS_LANG_MAP.en]: "en",
  [STANDARD_CMS_LANG_MAP.ja]: "ja",
  [STANDARD_CMS_LANG_MAP.ko]: "ko",
  [STANDARD_CMS_LANG_MAP.th]: "th",
  [STANDARD_CMS_LANG_MAP.es]: "es",
  [STANDARD_CMS_LANG_MAP.tr]: "tr",
  [STANDARD_CMS_LANG_MAP.ru]: "ru",
  [STANDARD_CMS_LANG_MAP.ar]: "ar",
  [STANDARD_CMS_LANG_MAP.id]: "id",
  [STANDARD_CMS_LANG_MAP.pt]: "pu",
  // midas 不支持的语种，统一映射成中文
  [STANDARD_CMS_LANG_MAP.ms]: "my",
  [STANDARD_CMS_LANG_MAP.zh]: "en",
};

/**
 * @description 对齐 ai help 语言
 * @link https://docs.aihelp.net/zh/web/config/language.html#%E8%AF%AD%E8%A8%80%E7%A0%81%E5%AF%B9%E7%85%A7%E8%A1%A8
 */
export const STANDARD_AI_HELP_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.zh]: "zh-CN",
  [STANDARD_CMS_LANG_MAP.tw]: "zh-TW",
  [STANDARD_CMS_LANG_MAP.en]: "en",
  [STANDARD_CMS_LANG_MAP.ja]: "ja",
  [STANDARD_CMS_LANG_MAP.ko]: "ko",
  [STANDARD_CMS_LANG_MAP.th]: "th",
  [STANDARD_CMS_LANG_MAP.es]: "es",
  [STANDARD_CMS_LANG_MAP.pt]: "pt",
  [STANDARD_CMS_LANG_MAP.tr]: "tr",
  [STANDARD_CMS_LANG_MAP.ru]: "ru",
  [STANDARD_CMS_LANG_MAP.ar]: "ar",
  [STANDARD_CMS_LANG_MAP.id]: "id",
  [STANDARD_CMS_LANG_MAP.ms]: "ms",
};

/**
 * @description 对齐 wand sig 魔法棒请求签名语言
 */
export const STANDARD_WAND_SIG_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.ja]: "jp",
  [STANDARD_CMS_LANG_MAP.ko]: "kr",
};

/**
 * @description 对齐腾讯云验证码语言
 * @link https://www.tencentcloud.com/zh/document/product/1159/49680#ecf01363-2b81-4fab-b0f5-0e90e8be7dfb
 */
export const STANDARD_CAPTCH_LANG_MAP = {
  [STANDARD_CMS_LANG_MAP.zh]: "zh-cn",
  [STANDARD_CMS_LANG_MAP.tw]: "zh-tw",
  [STANDARD_CMS_LANG_MAP.en]: "en",
  [STANDARD_CMS_LANG_MAP.ja]: "ja",
  [STANDARD_CMS_LANG_MAP.ko]: "ko",
  [STANDARD_CMS_LANG_MAP.th]: "th",
  [STANDARD_CMS_LANG_MAP.es]: "es",
  [STANDARD_CMS_LANG_MAP.pt]: "pt",
  [STANDARD_CMS_LANG_MAP.tr]: "tr",
  [STANDARD_CMS_LANG_MAP.ru]: "ru",
  [STANDARD_CMS_LANG_MAP.ar]: "ar",
  [STANDARD_CMS_LANG_MAP.id]: "id",
  [STANDARD_CMS_LANG_MAP.ms]: "ms",
};
