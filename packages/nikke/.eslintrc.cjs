module.exports = {
  root: true,
  plugins: ["vue", "@typescript-eslint"],
  env: {
    browser: true,
    node: true,
    "vue/setup-compiler-macros": true,
  },
  extends: ["eslint:recommended", "plugin:vue/vue3-recommended", "prettier"],
  // https://eslint.vuejs.org/user-guide/#how-to-use-a-custom-parser
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@typescript-eslint/parser",
    sourceType: "module",
    extraFileExtensions: [".vue"],
  },
  rules: {
    "no-undef": "off",
    "no-unused-vars": "off",
    "vue/multi-word-component-names": "off",
    "vue/no-v-html": "off",
    "vue/no-mutating-props": "off",
    "no-debugger": "off",
    "vue/prop-name-casing": "off",
  },
  globals: {},
  settings: {
    "eslint.packageManager": "pnpm",
  },
};
