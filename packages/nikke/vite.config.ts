// configs
import { CONST_NIKKE_APP_NAME, CONST_NIKKE_DEV_PORT } from "../configs/const";
import { ENV_DEVELOPMENT, ENV_TEST, ENV_PROD, ENV_PRE } from "../configs/env";
import { VITE_APP_TOOLS_CDN, API_TEST_REQUEST_URL } from "../configs/api";
import { createProxyConfig } from "../configs/proxy";

// plugins
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import legacy from "@vitejs/plugin-legacy";
import {
  VitePluginReplaceIndexHtmlPlaceholder,
  injectCookieBanner,
  injectAegis,
} from "../utils/plugins/vite-plugin-replace-index-html-placeholder";
import { visualizer } from "rollup-plugin-visualizer";

// import VitePluginInlineCritical from "../utils/plugins/vite-plugin-inline-critical.ts";
// import VitePluginCritical from "rollup-plugin-critical";
// import { chunkSplitPlugin } from "vite-plugin-chunk-split";

// utils
import dotenv from "dotenv";
import { resolve } from "node:path";
import { VitePWA } from "vite-plugin-pwa";
import { defineConfig, PluginOption, splitVendorChunkPlugin } from "vite";

const ensureUrlTrailingSlash = (url: string) => {
  if (!url) return url;
  if (url.endsWith("/")) return url;
  return `${url}/`;
};

// const hash_map = new Map();

// https://vitejs.dev/config/
// @ts-ignore
export default defineConfig(({ mode }) => {
  const APP_NAME = process.env.APP_NAME || CONST_NIKKE_APP_NAME;

  // 环境变量: .env.development
  const config: {
    VITE_PROXY_URL?: string;
    VITE_APP_TOOLS_CDN?: string;
    VITE_APP_DISABLE_OBSPATH?: "0" | "1";
  } = dotenv.config({ path: resolve(__dirname, `.env.${mode}`) }).parsed as any;

  // process.env.CDN_BASE_URL 蓝盾流水线注入的变量
  const CDN_BASE_URL = mode === ENV_DEVELOPMENT ? "" : process.env.CDN_BASE_URL || "";

  // shiftyspad cdn url
  const TOOLS_URL = (() => {
    if (mode === ENV_DEVELOPMENT) return config?.VITE_APP_TOOLS_CDN ?? VITE_APP_TOOLS_CDN["test"];
    if (mode === ENV_TEST) return VITE_APP_TOOLS_CDN[ENV_TEST];
    if (mode === ENV_PRE) return VITE_APP_TOOLS_CDN[ENV_PRE];
    return VITE_APP_TOOLS_CDN["prod"];
  })();
  const base = mode === ENV_DEVELOPMENT ? "/" : CDN_BASE_URL;

  /**
   * @link https://github.com/vitejs/vite/pull/12202
   * @link https://github.com/vitejs/vite/issues/3105
   * @description 4.2 开始需要使用 `VITE_` 开头
   */
  Object.assign(process.env, {
    VITE_VERSIOIN: process.env.unite_version || process.env.npm_package_version,
    VITE_MODE: mode === ENV_DEVELOPMENT ? ENV_TEST : mode,
    VITE_APP_TOOLS_CDN: TOOLS_URL,
    VITE_APP_CDN_BASE_URL: base,
    VITE_APP_BUILD_TIME: Date.now(),
    VITE_APP_DISABLE_OBSPATH: Number(config?.VITE_APP_DISABLE_OBSPATH) === 1 ? "1" : "0",
  });

  const IS_LANDUN_BUILD = Boolean(process.env.BK_CI_GIT_REPO_HEAD_COMMIT_AUTHOR);

  console.log(`[${APP_NAME}] mode => `, mode);
  console.log(`[${APP_NAME}] base => `, base);
  console.log(`[${APP_NAME}] VITE_MODE => `, process.env.VITE_MODE);
  console.log(`[${APP_NAME}] VITE_VERSIOIN => `, process.env.VITE_VERSIOIN);
  console.log(`[${APP_NAME}] SHIFTYSPAD_CDN => `, TOOLS_URL);
  console.log(`[${APP_NAME}] SHIFTYSPAD_ENV_CONFIG => `, config);
  console.log(`[${APP_NAME}] IS_LANDUN_BUILD => `, IS_LANDUN_BUILD);

  const preconnetlinks = {
    prod: [
      '<link rel="dns-prefetch" href="//cdn-apac.onetrust.com">',
      '<link rel="dns-prefetch" href="//sg-cdn.blablalink.com">',
      '<link rel="dns-prefetch" href="//sg-lipcommunity.playerinfinite.com">',
      '<link rel="dns-prefetch" href="//api.blablalink.com">',
      '<link rel="dns-prefetch" href="//sg-tools-cdn.blablalink.com">',
      '<link rel="preconnect" href="//cdn-apac.onetrust.com">',
      '<link rel="preconnect" href="//sg-cdn.blablalink.com">',
      '<link rel="preconnect" href="//sg-lipcommunity.playerinfinite.com">',
      '<link rel="preconnect" href="//api.blablalink.com">',
    ],
    pre: [
      '<link rel="dns-prefetch" href="//cdn-apac.onetrust.com">',
      '<link rel="dns-prefetch" href="//sg-cdn.blablalink.com">',
      '<link rel="dns-prefetch" href="//pre-api.blablalink.com">',
      '<link rel="dns-prefetch" href="//pre-sg-community.playerinfinite.com">',
      '<link rel="dns-prefetch" href="//sg-tools-cdn.blablalink.com">',
      '<link rel="preconnect" href="//cdn-apac.onetrust.com">',
      '<link rel="preconnect" href="//sg-cdn.blablalink.com">',
      '<link rel="preconnect" href="//pre-api.blablalink.com">',
      '<link rel="preconnect" href="//pre-sg-community.playerinfinite.com">',
    ],
    test: [
      '<link rel="dns-prefetch" href="//t-sg-community.playerinfinite.com">',
      '<link rel="dns-prefetch" href="//test-api.blablalink.com">',
      '<link rel="dns-prefetch" href="//webadmin-dev-1312254802.cos.ap-singapore.myqcloud.com">',
      '<link rel="dns-prefetch" href="//sg-tools-cdn.blablalink.com">',
      '<link rel="preconnect" href="//t-sg-community.playerinfinite.com">',
      '<link rel="preconnect" href="//test-api.blablalink.com">',
      '<link rel="preconnect" href="//webadmin-dev-1312254802.cos.ap-singapore.myqcloud.com">',
    ],
  }[mode]?.join("");

  return {
    base,
    server: {
      host: "0.0.0.0",
      port: CONST_NIKKE_DEV_PORT,
      proxy: createProxyConfig(config?.VITE_PROXY_URL ?? API_TEST_REQUEST_URL),
    },
    esbuild: {
      // NOTE: checklist
      drop: [
        ENV_PROD,
        // ENV_PRE,
        // ENV_TEST
      ].includes(process.env.VITE_MODE as string)
        ? ["console", "debugger"]
        : [],
    },
    plugins: [
      vue(),
      vueJsx(),
      legacy({
        targets: ["> 5%, last 5 version, ie >= 10"],
        additionalLegacyPolyfills: ["core-js/stable", "regenerator-runtime/runtime"],
      }),
      VitePWA({
        registerType: "prompt",
        /**
         * `false` - do nothing, you will need to register the sw you self, or imports from `virtual:pwa-register`
         * 这里设置为 false，只是避免插件本身不自动注册 sw 而已，但是 packages/nikke/src/components/common/service-workder-refresh-prompt.vue 组件的引入，会导致 sw 依然被注册
         */
        injectRegister: false,
        srcDir: "src",
        filename: "sw.ts",
        strategies: "injectManifest",
        base: "/",
        scope: "/",
        injectManifest: {
          minify: false,
          enableWorkboxModulesLogs: true,
          /**
           * FIXME: Vite Build Error: Unable to find a place to inject the manifest. This is likely because swSrc and swDest are configured to the same file. Please ensure that your swSrc file contains the following: self.__WB_MANIFEST
           * @link https://vite-pwa-org-zh.netlify.app/guide/inject-manifest.html#service-worker-%E4%BB%A3%E7%A0%81
           */
          injectionPoint: undefined,
        },
        devOptions: {
          enabled: true,
          /* when using generateSW the PWA plugin will switch to classic */
          type: "module",
          // navigateFallback: "index.html",
          // suppressWarnings: true,
        },
        manifest: {
          name: "Blablalink",
          scope: "/",
          /**
           * 如果变更，需同步修改 `pwa-install.ts` 相关逻辑
           */
          display: "standalone",
          start_url: "/?utm_medium=PWA",
          short_name: "Blablalink",
          theme_color: "#1d1d28",
          description: "Nikke blabla link",
          background_color: "#1d1d28",
          categories: ["games", "photo & video"],
          related_applications: [
            {
              url: "./manifest.json",
              platform: "webapp",
            },
          ],
          icons: [
            {
              src: `${ensureUrlTrailingSlash(base)}apple-touch-icon.png`,
              sizes: "180x180",
              type: "image/png",
            },
            {
              src: `${ensureUrlTrailingSlash(base)}mask-icon.png`,
              sizes: "512x512",
              type: "image/png",
              purpose: "maskable",
            },
          ],
        },
      }),
      VitePluginReplaceIndexHtmlPlaceholder([
        {
          mode,
          placeholder: "<!--cookiebanner-->",
          callback: injectCookieBanner,
          inline: true,
        },
        {
          mode,
          placeholder: "<!--aegis-->",
          callback: injectAegis,
          inline: true,
        },
      ]),
      // VitePluginInlineCritical({
      //   critical_type: "link",
      // }),
      /**
       * createHtmlPlugin 有 bug，会导致开发环境下 public 中的资源不可访问, 此处使用 transformIndexHtml 替代
       * @see: https://github.com/vbenjs/vite-plugin-html/issues/102
       */
      // createHtmlPlugin({
      //   inject: {
      //     data: {
      //       preconnetlinks,
      //     },
      //   },
      // }),
      {
        name: "html-transform",
        enforce: "pre",
        transformIndexHtml(html) {
          return html.replace("<!--preconnetlinks-->", preconnetlinks || "");
        },
      },

      // https://github.com/nystudio107/rollup-plugin-critical
      // VitePluginCritical({
      //   criticalBase: resolve(__dirname, "dist"),
      //   criticalUrl: resolve(__dirname, "dist/index.html"),
      //   criticalPages: [{ uri: "", template: "index" }],
      //   criticalConfig: {
      //     inline: true,
      //   },
      // }),

      /**
       * @link https://github.com/sanyuan0704/vite-plugin-chunk-split/blob/master/README-CN.md
       */
      // chunkSplitPlugin(),
      splitVendorChunkPlugin(),

      /**
       * @link https://github.com/btd/rollup-plugin-visualizer
       */
      !IS_LANDUN_BUILD &&
        false &&
        (visualizer({
          template: "treemap", // or sunburst
          open: true,
          gzipSize: true,
          brotliSize: true,
          filename: "visualizer-report.html", // will be saved in project's root
        }) as PluginOption),
    ],
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
        "packages/types": resolve(__dirname, "../types"),
        "packages/configs": resolve(__dirname, "../configs"),
        "packages/utils": resolve(__dirname, "../utils"),
      },
    },
    build: {
      sourcemap: "hidden",
      rollupOptions: {
        // output: {
        //   manualChunks(id: string) {
        //     if (id.includes("node_modules")) {
        //       if (id.includes("vue")) {
        //         return "vue-vendor";
        //       }
        //       return "vendor";
        //     }
        //     return "index";
        //   },
        // },
      },
      // https://main.vitejs.dev/config/build-options#build-assetsinlinelimit
      assetsInlineLimit: 2 * 1024, // 默认 4 * 1024  (4 KiB)
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData:
            '@use "sass:math";@use "sass:string";@use "sass:list"; @import "@/shiftyspad/assets/scss/_function.scss";', // 添加公共样式
        },
      },
    },
    envDir: resolve(__dirname, "./env"),
  };
});
