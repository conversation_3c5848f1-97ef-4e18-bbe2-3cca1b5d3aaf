<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Twitter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">
  <style>
    a.twitter-timeline>.loading {
      border: 3px solid #aaa;
      width: 26px;
      border-top-color: transparent;
      border-radius: 100%;
      animation: circle-loading infinite 0.8s linear;
      height: 26px;
      margin: 130px auto;
    }


    @keyframes circle-loading {
      0% {
        transform: rotate(0);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</head>

<body>
  <a class="twitter-timeline" href="">
    <div class="loading"></div>
  </a>
  <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

  <script>
    function getQueryParams() {
      const urlObj = new URL(location.href);
      const params = new URLSearchParams(urlObj.search);
      const queryParams = {};
      params.forEach((value, key) => {
        queryParams[key] = value;
      });
      return queryParams;
    }
    const langugeMap = {
      ko: 'NIKKE_kr',
      ja: 'NIKKE_japan',
      en: 'NIKKE_en',
    };
    const query = getQueryParams();
    const target = langugeMap[query.language || 'ja'] || langugeMap.en;
    const alink = document.querySelector('.twitter-timeline');
    alink.href = `https://twitter.com/${target}?ref_src=twsrc%5Etfw`
  </script>
</body>

</html>