{"name": "nikke", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "cross-env pnpm build:prod", "build:test": "vite build --mode test", "build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "format": "cross-env prettier ./src/* --write", "preview": "vite preview", "update:env": "tsx ./script/load-nikkes.ts"}, "dependencies": {"@esotericsoftware/spine-player": "^4.2.59", "@rollup/plugin-babel": "^6.0.4", "@tanstack/vue-query": "^5.53.3", "@tencent/pa-ingame-utils": "^0.1.2", "@tencent/pa-share-utils": "^0.0.6", "@tencent/webibi-embed": "^1.6.1", "@unocss/reset": "^0.62.3", "@vueuse/core": "^11.0.3", "@vueuse/motion": "^2.2.3", "axios": "^1.6.5", "charenc": "^0.0.2", "clsx": "^2.1.1", "crypt": "^0.0.2", "element-plus": "^2.4.4", "howler": "^2.2.4", "html2canvas": "1.0.0", "is-buffer": "^2.0.5", "js-cookie": "^3.0.5", "m-web-logger": "^1.0.0", "md5": "^2.3.0", "pinia": "^2.1.7", "primevue": "^3.46.0", "quill": "^2.0.2", "radix-vue": "^1.9.5", "spine-player401": "npm:@esotericsoftware/spine-player@4.0.1", "swiper": "^11.0.5", "tailwind-merge": "^2.5.2", "three": "^0.160.0", "viewerjs": "^1.11.6", "vue": "^3.5.1", "vue-i18n": "^9.14.0", "vue-lazyload": "^3.0.0", "vue-router": "^4.2.5", "vue-router-better-scroller": "^0.0.0"}, "devDependencies": {"@intlsdk/account-api": "^1.20.2", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/howler": "^2.2.11", "@types/js-cookie": "^3.0.6", "@types/jsdom": "^21.1.6", "@types/node": "^18.19.3", "@types/swiper": "^6.0.0", "@types/three": "^0.160.0", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "aegis-web-sdk": "^1.39.1", "dotenv": "^16.4.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^23.0.1", "npm-run-all2": "^6.1.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "sass": "^1.77.6", "terser": "^5.29.2", "tsx": "^4.7.0", "typescript": "^5.5.3", "vite": "5.0.0", "vue-tsc": "^2.0.29"}, "version": "1.6.0-v14"}