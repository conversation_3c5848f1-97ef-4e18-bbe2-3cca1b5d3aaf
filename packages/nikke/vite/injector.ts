import { <PERSON><PERSON><PERSON> } from 'jsdom'
import fs from 'fs'
import path from 'path'

function getContent(p: string): string {
  try {
    return fs.readFileSync(p, {
      encoding: 'utf-8'
    })
  } catch (err) {
    return ''
  }
}

export function viteInjectSnippets(
  snippet_list: {
    snippet: string
    query: 'head' | 'body' | string
    is_after?: boolean
  }[]
) {
  return {
    name: 'vite-plugin-snippets',
    transformIndexHtml(html: string) {
      const dom = new JSDOM(html)
      const { document } = dom.window
      snippet_list.forEach((snippet_info) => {
        const { snippet, query, is_after } = snippet_info
        const snippet_html_path = path.resolve(process.cwd(), 'snippets', snippet)
        const snippet_html = getContent(snippet_html_path)
        const el = document.querySelector(query)
        if (el) {
          if (is_after) {
            el.innerHTML = el.innerHTML + snippet_html
          } else {
            el.innerHTML = snippet_html + el.innerHTML
          }
        }
      })
      return dom.serialize()
    }
  }
}

export function viteInjectCdn(
  params: {
    script: string
    pos?: 'pre' | 'post'
  }[]
) {
  return {
    name: 'vite-plugin-cdn',
    transformIndexHtml(html: string) {
      const dom = new JSDOM(html)
      const { document } = dom.window
      const head = document.querySelector('head')

      params.forEach(({ script, pos = 'post' }) => {
        const script_el = document.createElement('script')
        script_el.src = script
        if (pos === 'post') {
          head?.appendChild(script_el)
        } else {
          head?.insertBefore(script_el, head.firstChild)
        }
      })
      return dom.serialize()
    }
  }
}
