/**
 * PWA install trigger
 *
 * @see {@link https://web.dev/articles/customize-install}
 * @see {@link https://developer.mozilla.org/docs/Web/Progressive_web_apps/Guides/Making_PWAs_installable}
 *
 * <AUTHOR>
 * @create 2024-09-25
 */
import { reactive } from "vue";

export type DisplayMode =
  | "browser"
  | "standalone"
  | "minimal-ui"
  | "fullscreen"
  | "window-controls-overlay"
  | "twa";

/**
 * The BeforeInstallPromptEvent is fired at the Window.onbeforeinstallprompt handler
 * before a user is prompted to "install" a web site to a home screen on mobile.
 *
 * @see {@link https://developer.mozilla.org/docs/Web/API/BeforeInstallPromptEvent}
 */
export interface BeforeInstallPromptEvent extends Event {
  /**
   * Returns an array of DOMString items containing the platforms on which the event was dispatched.
   * This is provided for user agents that want to present a choice of versions to the user such as,
   * for example, "web" or "play" which would allow the user to chose between a web version or
   * an Android version.
   */
  readonly platforms: Array<string>;

  /**
   * Returns a Promise that resolves to a DOMString containing either "accepted" or "dismissed".
   */
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: "web" | string;
  }>;

  /**
   * Allows a developer to show the install prompt at a time of their own choosing.
   * This method returns a Promise.
   */
  prompt(): Promise<void>;
}

/**
 * @notice Only Android support !!!
 * @since Android Chrome 84+
 * @see {@link https://issues.chromium.org/issues/40144143}
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Navigator/getInstalledRelatedApps}
 */
export interface AppInfo {
  readonly id?: string;
  readonly url?: string;
  readonly version?: string;
  readonly platform:
    | "chrome_web_store"
    | "play"
    | "chromeos_play"
    | "webapp"
    | "windows"
    | "f-droid"
    | "amazon";
}

/**
 * PWA install object
 */
const install = reactive<{
  /**
   * @notice Depend on `display` in `manifest.json` !!!
   * @see {vite.config.ts} - plugins[VitePWA(manifest.display)]
   * @see {@link https://developer.mozilla.org/en-US/docs/Web/Manifest/Reference/display}
   */
  mode?: DisplayMode;
  /**
   * @see {@link https://developer.mozilla.org/docs/Web/API/BeforeInstallPromptEvent}
   */
  event?: BeforeInstallPromptEvent;
  state?: "ready" | "pending" | "accepted" | "dismissed" | "installed";
  prompt: typeof prompt;
  /**
   * @see {@link https://web.dev/articles/install-criteria}
   */
  invokable: boolean;
  /**
   * @notice Depend on `related_applications` in `manifest.json` !!!
   * @see {vite.config.ts} - plugins[VitePWA(manifest.related_applications)]
   * @see {@link https://web.dev/articles/get-installed-related-apps}
   */
  detectable: boolean;
  promptable: boolean;
}>({
  mode: getDisplayMode(),
  event: undefined,
  state: undefined,
  prompt,
  invokable: "onbeforeinstallprompt" in window,
  detectable:
    "getInstalledRelatedApps" in navigator &&
    navigator.userAgent.toLowerCase().indexOf("android") > -1,
  promptable: false,
});

/**
 * Check installed (manual simulation)
 * The `beforeinstallprompt` event takes about 300ms, it will not be triggered if installed
 */
const timer =
  (install.invokable &&
    !install.detectable &&
    window.setTimeout(() => {
      onAppInstalled();
    }, 600)) ||
  -1;

/**
 * PWA install prompt
 */
async function prompt() {
  if (!install.invokable || !install.event || install.state !== "ready") {
    return false;
  }
  try {
    install.state = "pending";
    await install.event.prompt();
  } catch (error) {
    install.state = "ready";
    throw error;
  }

  const { outcome } = await install.event.userChoice;

  install.state = outcome;
  /**
   * We've used the prompt and can't use it again
   * But if dismissed, `beforeinstallprompt` event will be triggered again
   */
  install.promptable = false;

  return outcome === "accepted";
}

function onBeforeInstallPrompt(event: Event) {
  window.clearTimeout(timer);
  event.preventDefault();
  install.event = event as any as BeforeInstallPromptEvent;
  install.state = "ready";
  install.promptable = !!install.event.prompt;
}

function onAppInstalled() {
  install.state = "installed";
  install.promptable = false;
}

function getDisplayMode() {
  if (document.referrer.startsWith("android-app://")) return "twa";
  if ((navigator as any).standalone) return "standalone";
  const modes: DisplayMode[] = [
    "browser",
    "standalone",
    "minimal-ui",
    "fullscreen",
    "window-controls-overlay",
  ];
  for (const mode of modes) {
    if (window.matchMedia(`(display-mode: ${mode})`).matches) return mode;
  }
}

/**
 * Listen prompt immediately
 */
if (install.invokable) {
  window.addEventListener("beforeinstallprompt", onBeforeInstallPrompt);
  window.addEventListener("appinstalled", onAppInstalled);
}

/**
 * Check installed (only Android)
 */
if (install.detectable) {
  (navigator as any).getInstalledRelatedApps?.().then((apps?: AppInfo[]) => {
    apps?.length && onAppInstalled();
  });
}

/**
 * Listen display mode
 */
window
  .matchMedia("(display-mode: standalone)")
  .addEventListener("change", (event: MediaQueryListEvent) => {
    install.mode = getDisplayMode();
    install.mode === "standalone" && onAppInstalled();
  });

export default install;
