import { COMMON_QUERY_KEYS } from "@/configs/const";
import { RouteRecordRaw, RouteRecordSingleViewWithChildren } from "vue-router";

export enum Routes {
  HOME = "/",
  // 个人中心
  USER = "/user",
  USER_FOLLOWER = "/user/follower",
  USER_FOLLOWING = "/user/following",
  USER_LINK_MANAGE = "/user/links-manage",
  USER_EDIT_LINK = "/user/edit-link",
  USER_EDIT_ACCOUNT = "/user/edit-account",
  USER_EDIT_AVATAR = "/user/edit-avatar",
  USER_EDIT_AVATARFRAME = "/user/edit-avatarframe",
  USER_EDIT_NICKNAME = "/user/edit-nickname",
  USER_EDIT_SIGNATURE = "/user/edit-signature",
  USER_PERSONALIZED = "/user/personalized",
  USER_EDIT_COMMENT_BUBBLE = "/user/edit-comment-bubble",
  // 帖子
  POST = "/post",
  POST_DETAIL = "/post/detail",
  POST_COMMENTS = "/post/comments",
  POST_COMPOSE = "/post/compose",
  // 积分
  POINTS = "/points",
  POINTS_FAQ = "/points/faq",
  POINTS_HOME = "/points/home",
  POINTS_RECORD = "/points/record",
  POINTS_ORDER = "/points/order",
  // 搜索
  SEARCH = "/search",
  SEARCH_RESULT = "/search/result",
  // 设置
  SETTING = "/setting",
  SETTING_ACCOUNT = "/setting/account",
  SETTING_ABOUT = "/setting/about",
  SETTING_LANGUAGES = "/setting/languages",
  SETTING_BLOCKING = "/setting/blocking",
  SETTING_PRIVATE = "/setting/private",
  SETTING_SHIFTYSPAD_PRIVATE = "/setting/shiftyspad/private",
  SETTING_NOTIFICATIONS = "/setting/notifications",

  // 通知
  NOTIFICATION = "/notification",

  // 话题详情页面
  TOPIC = "/topic",
  TOPIC_MANAGE = "/topic/manage",

  // 登录
  LOGIN = "/login",
  LOGOUT = "/logout",

  // cdk
  CDK = "/cdk",

  // mission
  MISSION = "/mission",

  // 公告广场
  SQUARE = "/unionrecruitment",
  // creator hub
  CREATOR_HUB = "/creatorhub",
  CREATOR_HUB_BIND = "/creatorhub/bind",

  // laboratory
  LABORATORY = "/laboratory",

  // error
  ERROR = "/:pathMatch(.*)*",
  ERROR_NOT_FOUND = "/error/404",
  ERROR_LOGIN_TIPS = "/login/tips",

  // UI
  UI = "/ui",

  // SHIFTYSPAD
  SHIFTYSPAD = "/shiftyspad",
  SHIFTYSPAD_ROOT = "/shiftyspad/home",
  SHIFTYSPAD_NIKKE_DETAIL = "/shiftyspad/nikke",
  DEPRECATED_SHIFTYSPAD_NIKKE_DETAIL = "/shiftyspad/nikke/:id",
  SHIFTYSPAD_EDIT_NIKKE_LIST = "/shiftyspad/edit-nikke-list",
  SHIFTYSPAD_NIKKE_LIST = "/shiftyspad/nikke-list",
  SHIFTYSPAD_NIKKE_LIST_ALL = "/shiftyspad/nikke-list/all",
  SHIFTYSPAD_NIKKE_LIST_PLAYER = "/shiftyspad/nikke-list/player",
  SHIFTYSPAD_SCENE = "/shiftyspad/scene-list",
  SHIFTYSPAD_SCENE_MAIN = "/shiftyspad/scene-list/main",
  SHIFTYSPAD_SCENE_SUDDEN = "/shiftyspad/scene-list/sudden",
  SHIFTYSPAD_SCENE_ARCHIVE = "/shiftyspad/scene-list/archive",
  SHIFTYSPAD_SCENE_MAIN_DETAIL = "/shiftyspad/scene-list/main/:id",
  SHIFTYSPAD_SCENE_SUDDEN_DETAIL = "/shiftyspad/scene-list/sudden/:id",
  SHIFTYSPAD_SCENE_ARCHIVE_DETAIL = "/shiftyspad/scene-list/archive/:id",
  SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL = "/shiftyspad/scene-list/archive/:id/:index",
  SHIFTYSPAD_SCENE_ATTRACTIVE = "/shiftyspad/scene-list/attractive/:id",
  SHIFTYSPAD_SECTION = "/shiftyspad/scene-list/section/:id",
  UNAUTHORIZED = "/shiftyspad/unauthorized",
  NOTFOUND = "/notfound",

  INFO_DETAIL = "/info/detail",
  INFO_GROUPS = "/info/groups",
}

export enum RoutesName {
  HOME = "HOME",

  // 个人中心
  USER = "USER",
  USER_FOLLOWER = "USER_FOLLOWER",
  USER_FOLLOWING = "USER_FOLLOWING",
  USER_LINK_MANAGE = "USER_LINK_MANAGE",
  USER_EDIT_LINK = "USER_EDIT_LINK",
  USER_EDIT_ACCOUNT = "USER_EDIT_ACCOUNT",
  USER_EDIT_AVATAR = "USER_EDIT_AVATAR",
  USER_EDIT_AVATARFRAME = "USER_EDIT_AVATARFRAME",
  USER_EDIT_NICKNAME = "USER_EDIT_NICKNAME",
  USER_EDIT_SIGNATURE = "USER_EDIT_SIGNATURE",
  USER_PERSONALIZED = "USER_PERSONALIZED",
  USER_EDIT_COMMENT_BUBBLE = "USER_EDIT_COMMENT_BUBBLE",
  // 帖子
  POST = "POST",
  POST_DETAIL = "POST_DETAIL",
  POST_COMMENTS = "POST_COMMENTS",
  POST_COMPOSE = "POST_COMPOSE",
  // 积分
  POINTS = "POINTS",
  POINTS_FAQ = "POINTS_FAQ",
  POINTS_HOME = "POINTS_HOME",
  POINTS_RECORD = "POINTS_RECORD",
  POINTS_ORDER = "POINTS_ORDER",
  // 搜索
  SEARCH = "SEARCH",
  SEARCH_RESULT = "SEARCH_RESULT",

  // 设置
  SETTING = "SETTING",
  SETTING_ACCOUNT = "SETTING_ACCOUNT",
  SETTING_ABOUT = "SETTING_ABOUT",
  SETTING_LANGUAGES = "SETTING_LANGUAGES",
  SETTING_SHIFTYSPAD_PRIVATE = "SETTING_SHIFTYSPAD_PRIVATE",
  SETTING_PRIVATE = "SETTING_PRIVATE",
  SETTING_NOTIFICATIONS = "SETTING_NOTIFICATIONS",
  SETTING_BLOCKING = "SETTING_BLOCKING",
  // 通知
  NOTIFICATION = "NOTIFICATION",

  // 话题详情页面
  TOPIC = "TOPIC",
  TOPIC_MANAGE = "TOPIC_MANAGE",

  // 登录
  LOGIN = "LOGIN",
  LOGOUT = "LOGOUT",

  // cdk
  CDK = "CDK",

  // mission
  MISSION = "MISSION",

  // 公告广场
  SQUARE = "SQUARE",
  // creator hub
  CREATOR_HUB = "CREATOR_HUB",
  CREATOR_HUB_BIND = "CREATOR_HUB_BIND",

  // error
  ERROR = "ERROR",
  ERROR_NOT_FOUND = "ERROR_NOT_FOUND",
  ERROR_LOGIN_TIPS = "ERROR_LOGIN_TIPS",

  // laboratory
  LABORATORY = "LABORATORY",

  // UI
  UI = "UI",

  // swiftpad
  SHIFTYSPAD = "SHIFTYSPAD",
  SHIFTYSPAD_ROOT = "SHIFTYSPAD_ROOT",
  SHIFTYSPAD_NIKKE_DETAIL = "SHIFTYSPAD_NIKKE_DETAIL",
  DEPRECATED_SHIFTYSPAD_NIKKE_DETAIL = "DEPRECATED_SHIFTYSPAD_NIKKE_DETAIL",
  SHIFTYSPAD_EDIT_NIKKE_LIST = "SHIFTYSPAD_EDIT_NIKKE_LIST",
  SHIFTYSPAD_NIKKE_LIST = "SHIFTYSPAD_NIKKE_LIST",
  SHIFTYSPAD_NIKKE_LIST_ALL = "SHIFTYSPAD_NIKKE_LIST_ALL",
  SHIFTYSPAD_NIKKE_LIST_PLAYER = "SHIFTYSPAD_NIKKE_LIST_PLAYER",
  SHIFTYSPAD_SCENE = "SHIFTYSPAD_SCENE",
  SHIFTYSPAD_SCENE_MAIN = "SHIFTYSPAD_SCENE_MAIN",
  SHIFTYSPAD_SCENE_SUDDEN = "SHIFTYSPAD_SCENE_SUDDEN",
  SHIFTYSPAD_SCENE_ARCHIVE = "SHIFTYSPAD_SCENE_ARCHIVE",
  SHIFTYSPAD_SCENE_MAIN_DETAIL = "SHIFTYSPAD_SCENE_MAIN_DETAIL",
  SHIFTYSPAD_SCENE_SUDDEN_DETAIL = "SHIFTYSPAD_SCENE_SUDDEN_DETAIL",
  SHIFTYSPAD_SCENE_ARCHIVE_DETAIL = "SHIFTYSPAD_SCENE_ARCHIVE_DETAIL",
  SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL = "SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL",
  SHIFTYSPAD_SCENE_ATTRACTIVE = "SHIFTYSPAD_SCENE_ATTRACTIVE",
  SHIFTYSPAD_SECTION = "SHIFTYSPAD_SECTION",
  UNAUTHORIZED = "UNAUTHORIZED",
  NOTFOUND = "NOTFOUND",

  INFO_DETAIL = "INFO_DETAIL",
  INFO_GROUPS = "INFO_GROUPS",
}

export const ROUTES: Record<
  any,
  | (RouteRecordRaw & { name?: RoutesName })
  | (RouteRecordSingleViewWithChildren & { name?: RoutesName })
> = {
  [Routes.HOME]: {
    name: RoutesName.HOME,
    path: Routes.HOME,
    component: () => import("@/views/home/<USER>"),
  },

  [Routes.POINTS]: {
    path: Routes.POINTS,
    children: [
      {
        name: RoutesName.POINTS,
        path: "",
        component: () => import("@/views/points/home/<USER>"),
      },
      {
        name: RoutesName.POINTS_HOME,
        path: Routes.POINTS_HOME,
        component: () => import("@/views/points/home/<USER>"),
      },
      {
        name: RoutesName.POINTS_FAQ,
        path: Routes.POINTS_FAQ,
        component: () => import("@/views/points/faq/index.vue"),
      },
      {
        name: RoutesName.POINTS_RECORD,
        path: Routes.POINTS_RECORD,
        component: () => import("@/views/points/record/index.vue"),
      },
      {
        name: RoutesName.POINTS_ORDER,
        path: Routes.POINTS_ORDER,
        component: () => import("@/views/points/order/index.vue"),
      },
    ],
  },

  [Routes.POST]: {
    path: Routes.POST,
    children: [
      {
        name: RoutesName.POST_DETAIL,
        path: Routes.POST_DETAIL,
        component: () => import("@/views/post/detail/index.vue"),
      },
      {
        name: RoutesName.POST_COMPOSE,
        path: Routes.POST_COMPOSE,
        component: () => import("@/views/post/compose/index.vue"),
      },
      {
        name: RoutesName.POST_COMMENTS,
        path: Routes.POST_COMMENTS,
        component: () => import("@/views/post/comments/index.vue"),
      },
    ],
  },

  [Routes.USER]: {
    path: Routes.USER,
    children: [
      {
        name: RoutesName.USER,
        path: "",
        component: () => import("@/views/user/index/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_ACCOUNT,
        path: Routes.USER_EDIT_ACCOUNT,
        component: () => import("@/views/user/edit-account/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_AVATAR,
        path: Routes.USER_EDIT_AVATAR,
        component: () => import("@/views/user/edit-avatar/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_AVATARFRAME,
        path: Routes.USER_EDIT_AVATARFRAME,
        component: () => import("@/views/user/edit-avatarframe/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_NICKNAME,
        path: Routes.USER_EDIT_NICKNAME,
        component: () => import("@/views/user/edit-nickname/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_SIGNATURE,
        path: Routes.USER_EDIT_SIGNATURE,
        component: () => import("@/views/user/edit-signature/index.vue"),
      },
      {
        name: RoutesName.USER_FOLLOWING,
        path: Routes.USER_FOLLOWING,
        component: () => import("@/views/user/following/index.vue"),
      },
      {
        name: RoutesName.USER_FOLLOWER,
        path: Routes.USER_FOLLOWER,
        component: () => import("@/views/user/follower/index.vue"),
      },
      {
        name: RoutesName.USER_LINK_MANAGE,
        path: Routes.USER_LINK_MANAGE,
        component: () => import("@/views/user/links-manage/index.vue"),
      },
      {
        name: RoutesName.USER_EDIT_LINK,
        path: Routes.USER_EDIT_LINK,
        component: () => import("@/views/user/edit-link/index.vue"),
      },
    ],
  },
  [Routes.SEARCH]: {
    path: Routes.SEARCH,
    children: [
      {
        path: "",
        name: RoutesName.SEARCH,
        component: () => import("@/views/search/home/<USER>"),
      },
      {
        name: RoutesName.SEARCH_RESULT,
        path: Routes.SEARCH_RESULT,
        component: () => import("@/views/search/result/index.vue"),
      },
    ],
  },
  [Routes.NOTIFICATION]: {
    name: RoutesName.NOTIFICATION,
    path: Routes.NOTIFICATION,
    component: () => import("@/views/notifications/index.vue"),
    beforeEnter: (to, from, next) => {
      if (from.name === RoutesName.HOME) {
        to.meta.shouldRefresh = true;
      }
      next();
    },
  },
  [Routes.SETTING]: {
    path: Routes.SETTING,
    children: [
      {
        path: "",
        name: RoutesName.SETTING,
        component: () => import("@/views/setting/home/<USER>"),
      },
      {
        name: RoutesName.SETTING_ABOUT,
        path: Routes.SETTING_ABOUT,
        component: () => import("@/views/setting/about/index.vue"),
      },
      {
        name: RoutesName.SETTING_BLOCKING,
        path: Routes.SETTING_BLOCKING,
        component: () => import("@/views/setting/blocking/index.vue"),
      },
      {
        name: RoutesName.SETTING_ACCOUNT,
        path: Routes.SETTING_ACCOUNT,
        component: () => import("@/views/setting/account/index.vue"),
      },
      {
        name: RoutesName.SETTING_PRIVATE,
        path: Routes.SETTING_PRIVATE,
        component: () => import("@/views/setting/private/index.vue"),
      },
      {
        path: Routes.SETTING_SHIFTYSPAD_PRIVATE,
        name: RoutesName.SETTING_SHIFTYSPAD_PRIVATE,
        component: () => import("@/views/setting/shiftyspad/index.vue"),
      },
      {
        name: RoutesName.SETTING_NOTIFICATIONS,
        path: Routes.SETTING_NOTIFICATIONS,
        component: () => import("@/views/setting/notifications/index.vue"),
      },
      {
        name: RoutesName.SETTING_LANGUAGES,
        path: Routes.SETTING_LANGUAGES,
        component: () => import("@/views/setting/languages/index.vue"),
      },
    ],
  },
  // TOPIC
  [Routes.TOPIC]: {
    name: RoutesName.TOPIC,
    path: Routes.TOPIC,
    component: () => import("@/views/topic/index.vue"),
  },
  [Routes.TOPIC_MANAGE]: {
    name: RoutesName.TOPIC_MANAGE,
    path: Routes.TOPIC_MANAGE,
    component: () => import("@/views/topic-manage/index.vue"),
  },
  [Routes.UI]: {
    name: RoutesName.UI,
    path: Routes.UI,
    component: () => import("@/views/ui/index.vue"),
  },

  [Routes.SHIFTYSPAD]: {
    // name: RoutesName.SHIFTYSPAD,
    path: Routes.SHIFTYSPAD,
    component: () => import("@/shiftyspad/App.vue"),
    children: [
      {
        path: "",
        name: RoutesName.SHIFTYSPAD,
        component: () => import("@/shiftyspad/page/main/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_ROOT,
        name: RoutesName.SHIFTYSPAD_ROOT,
        component: () => import("@/shiftyspad/page/main/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_NIKKE_DETAIL,
        name: RoutesName.SHIFTYSPAD_NIKKE_DETAIL,
        meta: { title: "Nikke detail" },
        component: () => import("@/shiftyspad/page/character/index.vue"),
      },
      {
        path: Routes.DEPRECATED_SHIFTYSPAD_NIKKE_DETAIL,
        name: RoutesName.DEPRECATED_SHIFTYSPAD_NIKKE_DETAIL,
        redirect: (to) => {
          return {
            query: {
              [COMMON_QUERY_KEYS.NikkeId]: to.params.id,
            },
            name: RoutesName.SHIFTYSPAD_NIKKE_DETAIL, // 目标路由名称
          };
        },
      },
      {
        path: Routes.SHIFTYSPAD_EDIT_NIKKE_LIST,
        name: RoutesName.SHIFTYSPAD_EDIT_NIKKE_LIST,
        component: () => import("@/shiftyspad/page/edit-list/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_EDIT_NIKKE_LIST,
        name: RoutesName.SHIFTYSPAD_EDIT_NIKKE_LIST,
        component: () => import("@/shiftyspad/page/edit-list/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_NIKKE_LIST_ALL,
        name: RoutesName.SHIFTYSPAD_NIKKE_LIST_ALL,
        component: () => import("@/shiftyspad/page/nikke-list/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_NIKKE_LIST,
        name: RoutesName.SHIFTYSPAD_NIKKE_LIST,
        component: () => import("@/shiftyspad/page/nikke-list/index.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_NIKKE_LIST_PLAYER,
        name: RoutesName.SHIFTYSPAD_NIKKE_LIST_PLAYER,
        component: () => import("@/shiftyspad/page/nikke-list/index.vue"),
      },

      // Scene list

      {
        path: Routes.SHIFTYSPAD_SCENE,
        name: RoutesName.SHIFTYSPAD_SCENE,
        component: () => import("@/shiftyspad/page/scene-list/index.vue"),
        children: [
          {
            path: Routes.SHIFTYSPAD_SCENE_MAIN,
            name: RoutesName.SHIFTYSPAD_SCENE_MAIN,
            component: () => import("@/shiftyspad/page/scene-list/index.vue"),
          },
          {
            path: Routes.SHIFTYSPAD_SCENE_SUDDEN,
            name: RoutesName.SHIFTYSPAD_SCENE_SUDDEN,
            component: () => import("@/shiftyspad/page/scene-list/index.vue"),
          },
          {
            path: Routes.SHIFTYSPAD_SCENE_ARCHIVE,
            name: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE,
            component: () => import("@/shiftyspad/page/scene-list/index.vue"),
          },
        ],
      },

      // Scene detail
      {
        path: Routes.SHIFTYSPAD_SCENE_MAIN_DETAIL,
        name: RoutesName.SHIFTYSPAD_SCENE_MAIN_DETAIL,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id }),
        component: () => import("@/shiftyspad/page/scene-list/main-detail.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_SCENE_SUDDEN_DETAIL,
        name: RoutesName.SHIFTYSPAD_SCENE_SUDDEN_DETAIL,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id }),
        component: () => import("@/shiftyspad/page/scene-list/sudden-detail.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL,
        name: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id }),
        component: () => import("@/shiftyspad/page/scene-list/archive-detail.vue"),
      },

      {
        path: Routes.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL,
        name: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id, index: route.params.index }),
        component: () => import("@/shiftyspad/page/scene-list/archive-detail-detail.vue"),
      },
      // section
      {
        path: Routes.SHIFTYSPAD_SCENE_ATTRACTIVE,
        name: RoutesName.SHIFTYSPAD_SCENE_ATTRACTIVE,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id }),
        component: () => import("@/shiftyspad/page/scene-list/section-copy.vue"),
      },
      {
        path: Routes.SHIFTYSPAD_SECTION,
        name: RoutesName.SHIFTYSPAD_SECTION,
        meta: { title: "todo" },
        props: (route) => ({ id: route.params.id }),
        component: () => import("@/shiftyspad/page/scene-list/section.vue"),
      },
      {
        path: Routes.UNAUTHORIZED,
        meta: { title: "route.Unauthorized" },
        component: () => import("@/shiftyspad/page/error/401.vue"),
      },
    ],
  },

  // login
  [Routes.LOGIN]: {
    name: RoutesName.LOGIN,
    path: Routes.LOGIN,
    component: () => import("@/views/login/index.vue"),
  },
  [Routes.LOGOUT]: {
    name: RoutesName.LOGOUT,
    path: Routes.LOGOUT,
    component: () => import("@/views/logout/index.vue"),
  },

  // cdk
  [Routes.CDK]: {
    name: RoutesName.CDK,
    path: Routes.CDK,
    component: () => import("@/views/cdk/index.vue"),
  },

  // Personalized
  [Routes.USER_PERSONALIZED]: {
    name: RoutesName.USER_PERSONALIZED,
    path: Routes.USER_PERSONALIZED,
    component: () => import("@/views/user/personalized/index.vue"),
  },

  // decorations
  [Routes.USER_EDIT_COMMENT_BUBBLE]: {
    name: RoutesName.USER_EDIT_COMMENT_BUBBLE,
    path: Routes.USER_EDIT_COMMENT_BUBBLE,
    component: () => import("@/views/user/edit-comment-bubble/index.vue"),
  },

  // mission
  [Routes.MISSION]: {
    name: RoutesName.MISSION,
    path: Routes.MISSION,
    component: () => import("@/views/mission/index.vue"),
  },

  // 公告广场
  [Routes.SQUARE]: {
    name: RoutesName.SQUARE,
    path: Routes.SQUARE,
    component: () => import("@/views/announcement-square/index.vue"),
  },
  // creator hub
  [Routes.CREATOR_HUB]: {
    name: RoutesName.CREATOR_HUB,
    path: Routes.CREATOR_HUB,
    component: () => import("@/views/creatorhub/index.vue"),
  },
  [Routes.CREATOR_HUB_BIND]: {
    name: RoutesName.CREATOR_HUB_BIND,
    path: Routes.CREATOR_HUB_BIND,
    component: () => import("@/views/creatorhub/bind.vue"),
  },

  // laboratory
  [Routes.LABORATORY]: {
    name: RoutesName.LABORATORY,
    path: Routes.LABORATORY,
    component: () => import("@/views/laboratory/index.vue"),
  },

  // info
  [Routes.INFO_DETAIL]: {
    name: RoutesName.INFO_DETAIL,
    path: Routes.INFO_DETAIL,
    component: () => import("@/views/info/detail.vue"),
  },
  [Routes.INFO_GROUPS]: {
    name: RoutesName.INFO_GROUPS,
    path: Routes.INFO_GROUPS,
    component: () => import("@/views/info/groups.vue"),
  },

  // error relative
  [Routes.ERROR_NOT_FOUND]: {
    name: RoutesName.ERROR_NOT_FOUND,
    path: Routes.ERROR_NOT_FOUND,
    component: () => import("@/views/error/not-found.vue"),
  },
  [Routes.ERROR_LOGIN_TIPS]: {
    name: RoutesName.ERROR_LOGIN_TIPS,
    path: Routes.ERROR_LOGIN_TIPS,
    component: () => import("@/views/error/login-tips.vue"),
  },
  [Routes.ERROR]: {
    name: RoutesName.ERROR,
    path: Routes.ERROR,
    redirect: Routes.ERROR_NOT_FOUND,
  },
};

export const routes: RouteRecordRaw[] = Object.keys(ROUTES).map(function cb<T extends string>(
  key: T,
) {
  const route: RouteRecordRaw = ROUTES[key];
  return route;
});

// 需要在组件定义defineOptions({ name: RoutesName.XXX })
export const KEEP_ALIVE_NAMES = [
  RoutesName.HOME,
  RoutesName.TOPIC,
  RoutesName.NOTIFICATION,
  RoutesName.SEARCH_RESULT,
  RoutesName.USER,
  RoutesName.TOPIC,
  RoutesName.POST_DETAIL,
] satisfies RoutesName[];
