<template>
  <div class="pt-[44px] pb-[30px]">
    <Head
      bg="bg-[var(--color-white)]"
      :title="t('blabla_task_center')"
      color="var(--text-1)"
      @goback="router.back()"
    >
      <!-- <template #icon>
        <SvgIcon
          name="icon-share"
          color="var(--text-1)"
          class="w-[24px] h-[24px] cursor-pointer"
        ></SvgIcon>
      </template> -->
    </Head>
    <Banner :list="mission_store.bannerList"></Banner>

    <div
      class="flex flex-col gap-[16px] mt-[16px] px-[15px] pb-[22px]"
      :class="{
        'flex-col-reverse': all_mission_collected,
      }"
    >
      <Onboarding></Onboarding>
      <Reward></Reward>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Head from "@/components/common/head/index.vue";
// import SvgIcon from "@/components/common/svg-icon.vue";
import Banner from "@/components/home/<USER>/index.vue";
import Onboarding from "@/components/mission/onboarding/index.vue";
import Reward from "@/components/mission/reward/index.vue";
import { t } from "@/locales";
import { computed, onMounted } from "vue";

// import { useRoute } from "vue-router";
// import { Routes, RoutesName } from "@/router/routes";
import router from "@/router";
import { useMissionStore } from "@/store/mission";
import { MissionStatus } from "@/api/mission";
import { report } from "packages/utils/tlog";
// const route = useRoute();

// const bannerList = [
//   {
//     pic_urls: [
//       "https://webadmin-dev-1312254802.cos.ap-singapore.myqcloud.com/cms/nrft/feeds/pic/_fc5a082dc3db2fe8b2ea42e9cbb3e85d29d698ec-2667x1358-ori_s_80_50_ori_q_80.webp",
//     ],
//   },
// ];

const mission_store = useMissionStore();

onMounted(() => {
  mission_store.getBanners();
  report.standalonesite_mission_page.cm_vshow();
});

// 所有任务都领取了
const all_mission_collected = computed(() => {
  return (
    mission_store.mission_list.length > 0 &&
    mission_store.mission_list.every((item) => item.mission_status === MissionStatus.COLLECTED)
  );
});
</script>
