<script lang="ts" setup>
// cpnts
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Button from "@/components/ui/button/index.vue";
import Nodata from "@/components/common/nodata.vue";
import InfiniteScroll from "@/components/common/scroll/infinite-scroll.vue";
import MultiLineInput from "@/components/common/multi-line-input/index.vue";
import CommUserInfo from "@/components/common/comm-userinfo/index.vue";

// configs
import { CODE_MESSAGE_MAP } from "packages/configs/code";

// assets
import default_img from "@/assets/imgs/common/topic-default.png";

// utils
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { t } from "@/locales";
import { storeToRefs } from "pinia";
import { useUserStore as useShiftyspadUserStore } from "@/shiftyspad/stores/user";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { useApiGetCdkRedemptionHistory, useApiRecordCdkRedemption } from "@/api/cdk.ts";
import { useCommonShare } from "@/components/common/share-pop";
import { CdkRedemptionListItem } from "packages/types/cdk";
import { extractTextFromHtml } from "packages/utils/dom";
import dayjs from "dayjs";
import { resovledShareUrl } from "packages/utils/tools";
import { useScroll } from "@vueuse/core";
import { report } from "packages/utils/tlog";

const { y } = useScroll(window);

const shiftyspad_user_store = useShiftyspadUserStore();
const { user_role_info } = storeToRefs(shiftyspad_user_store);
const router = useRouter();
const { show: showDialog } = useDialog();

const get_cdk_redemption_history = ref({
  page_num: 1,
  page_size: 20,
});
const loading = ref(false);
const finished = ref(false);
const cdkey = ref("");
const list = ref([] as Array<CdkRedemptionListItem>);

const is_disabled_redeem = computed(() => !cdkey.value || !user_role_info.value?.role_name);

const onRedeem = async () => {
  if (is_disabled_redeem.value) {
    return;
  }

  report.standalonesite_cdkey_btn.cm_click();

  const cd_key = extractTextFromHtml(cdkey.value);

  try {
    await useApiRecordCdkRedemption.run({ cdkey: cd_key });

    showDialog({
      title: t("redeem_successful"),
      content: t("redeem_successful_pop_content"),
      confirm_text: t("close"),
      callback(options: { value: any; close: () => void }) {
        options.close();
      },
    });
  } catch (error: any) {
    console.error(error, error.code);
    const { code, message } = error;
    code &&
      showDialog({
        title: t("failed_redeemed"),
        content: t(CODE_MESSAGE_MAP[code] || message),
        confirm_text: t("close"),
        callback(options: { value: any; close: () => void }) {
          options.close();
        },
      });
  }
  // refresh list
  onRefreshList();
};

const onShare = () => {
  useCommonShare({}).share({ text: t("share_cdk_text"), url: resovledShareUrl({ from: "share" }) });
};

const onLoadMore = async () => {
  get_cdk_redemption_history.value.page_num += 1;
  const cdk_redemption_list = await onLoad({
    page_num: get_cdk_redemption_history.value.page_num,
    page_size: get_cdk_redemption_history.value.page_size,
  });

  list.value.push(...cdk_redemption_list);
};

const onLoad = async (params: { page_num: number; page_size: number }) => {
  loading.value = true;
  const { cdk_redemption_list, is_last_page } = await useApiGetCdkRedemptionHistory.run(params);
  loading.value = false;
  finished.value = is_last_page;
  return cdk_redemption_list;
};

const reset = () => {
  list.value = [];
  get_cdk_redemption_history.value.page_num = 1;
};

const onRefreshList = async () => {
  reset();
  const cdk_redemption_list = await onLoad(get_cdk_redemption_history.value);
  list.value.push(...cdk_redemption_list);
};

onRefreshList();

report.standalonesite_cdkey_page.cm_vshow();
</script>

<template>
  <div class="min-h-screen bg-[var(--fill-0)]">
    <Head v-if="y < 10" bg="bg-[transparent]" title="CDKey" @goback="router.back()">
      <template #icon>
        <span class="w-[24px] h-[24px] cursor-pointer" @click="onShare">
          <SvgIcon name="icon-share" class="w-full h-full"></SvgIcon>
        </span>
      </template>
    </Head>
    <div
      :class="[`w-full h-[211px] relative bg-[length:100%_100%] pt-[44px] overflow-hidden`]"
      :style="{ backgroundImage: `url(${default_img})` }"
    >
      <i
        class="absolute-center !w-full !h-full z-[1] !bg-[var(--color-black-70)] pointer-events-none"
      ></i>
      <!-- 组件抽离  -->
      <CommUserInfo></CommUserInfo>
    </div>

    <div
      class="relative z-[5] -mt-[94px] pb-[20px] min-h-[188px] flex-1 bg-[url('@/assets/imgs/common/cdk-bg.png')] bg-no-repeat bg-[length:100%_auto] bg-[center_top] pt-[25px]"
    >
      <i
        class="absolute top-[8px] left-0 w-full h-full bg-[var(--op-fill-white)] -z-[1] mask-bg"
      ></i>
      <i
        class="absolute top-[8px] left-0 w-full h-[116px] bg-[url('@/assets/imgs/common/cdk-bg-mask.png')] bg-[length:100%_100%] -z-[1] dark:opacity-15"
      ></i>
      <div
        class="flex items-center justify-center max-w-[max-content] px-[14px] min-w-[194px] h-[29px] bg-[url('@/assets/imgs/common/cdk-title-bg.png')] bg-[length:100%_100%] bg-no-repeat font-bold text-[18px] leading-[22px] text-[color:var(--brand-3)] mx-auto"
      >
        {{ t("cdk_redemption") }}
      </div>
      <div
        class="mt-[17px] mx-[12px] bg-[var(--fill-3)] border-[1px] border-[var(--line-1)] rounded-[2px]"
      >
        <MultiLineInput
          v-model="cdkey"
          :placeholder="t('please_enter_cdk')"
          :style="'height: 50px'"
          :max-length="32"
          class="ovderflow-y-auto"
        ></MultiLineInput>
      </div>
      <Button
        type="primary"
        :disabled="is_disabled_redeem"
        class="mx-[12px] mt-[15px]"
        @click="onRedeem"
      >
        {{ t("confirm") }}
      </Button>
    </div>
    <div class="bg-[var(--fill-3)] py-[16px]">
      <div
        class="flex items-center justify-center max-w-[max-content] px-[14px] min-w-[194px] h-[29px] bg-[url('@/assets/imgs/common/cdk-title-bg2.png')] bg-[length:100%_100%] bg-no-repeat font-bold text-[18px] leading-[22px] text-[color:var(--brand-3)] mx-auto"
      >
        {{ t("redemption_record") }}
      </div>
    </div>
    <template v-if="list.length">
      <div class="mt-[2px] mx-[16px]">
        <InfiniteScroll
          :back_to_top_visible="false"
          :loading="loading"
          :finished="finished"
          :empty="list.length === 0"
          @load-more="onLoadMore"
        >
          <div
            v-for="(item, index) in list"
            :key="index"
            class="py-[10px] flex items-center border-b-[1px] border-b-[var(--line-1)]"
          >
            <div class="flex-1">
              <div
                class="text-[color:var(--text-1)] text-[length:13px] leading-[16px] line-clamp-1 font-medium"
              >
                {{ item.cdk }}
              </div>
              <div class="mt-[8px] text-[color:var(--text-3)] text-[length:9px] leading-[11px]">
                {{ dayjs(+item.timestamp * 1000).format("DD/MM/YYYY HH:mm:ss") }}
              </div>
            </div>
            <div
              :class="[
                `flex flex-col items-center justify-center w-[40px] h-[36px] rounded-[4px]`,
                item.status
                  ? 'text-[color:var(--brand-1)] bg-[var(--brand-1-12)]'
                  : 'text-[color:var(--error)] bg-[var(--error-10)]',
              ]"
            >
              <SvgIcon
                v-if="item.status"
                name="icon-success-large"
                class="w-[12px] h-[12px]"
                color="var(--brand-1)"
              ></SvgIcon>
              <SvgIcon
                v-else
                name="icon-error"
                class="w-[12px] h-[12px]"
                color="var(--error-80)"
              ></SvgIcon>
              <div class="text-[length:8px] leading-[11px] mt-[2px]">
                {{ item.status ? t("success") : t("failed") }}
              </div>
            </div>
          </div>
        </InfiniteScroll>
      </div>
    </template>
    <Nodata v-else class="mt-[76px]"></Nodata>
  </div>
</template>

<style lang="scss" scoped>
.mask-bg {
  clip-path: polygon(20px 0%, 100% 0, 100% 100%, 0 100%, 0% 20px);
}
</style>
