<template>
  <div class="w-full min-h-screen relative pt-[84px] pb-[30px]">
    <Head :title="t('records')" @goback="router.back()"> </Head>

    <div
      class="fixed z-[20] top-[44px] left-1/2 w-full h-[40px] -translate-x-1/2 max-w-[var(--max-pc-w)]"
    >
      <i
        class="absolute left-0 top-0 z-[1] w-full h-[40px] bg-[url('@/assets/imgs/points/list-head-bg-3.png')] bg-[length:100%_100%] pointer-events-none"
      ></i>
      <TabScroll
        class="relative z-[2]"
        :tabs="tabs"
        :active_id="tab_index"
        @change="onChangeTab"
      ></TabScroll>
    </div>

    <InfiniteScroll
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="true"
      :empty_visible="true"
      :empty="empty"
      :loading="loading"
      :finished="finished"
      @load-more="loadMore"
    >
      <div class="px-[12px] pt-[12px]">
        <ListItem
          v-for="(item, index) in list"
          :key="index"
          :item="item"
          class="mb-[8px]"
          @click="toOrderDetail(item)"
        ></ListItem>
      </div>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import ListItem from "@/components/points/record/list-item/index.vue";
import TabScroll from "@/components/common/tabs/tab-scroll.vue";
import { InfiniteScroll } from "@/components/common/scroll/index";

// types
import { RecordListFilter, RecordListFilterType, RecordListItem } from "packages/types/record";

// utils
import { computed, onMounted, Ref, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { t } from "@/locales";
import { getUniqueArr } from "packages/utils/tools";
import { useRecordList } from "@/api/record";

const router = useRouter();
const route = useRoute();
const tabs = computed(() => [
  {
    label: t("all"),
    value: RecordListFilterType.all,
  },
  {
    label: t("gain"),
    value: RecordListFilterType.unused,
  },
  {
    label: t("redeemed"),
    value: RecordListFilterType.redeemed,
  },
  {
    label: t("overdue"),
    value: RecordListFilterType.expired,
  },
]);
const list: Ref<RecordListItem[]> = ref([]);
const getDefautlQuery = () => ({
  // 依赖于米大师那边的接口，但是目前那边只支持拉回 20 条数据
  limit: 20,
  page: 1,
  filter: +(route.query.tab || tabs.value[0].value) as RecordListFilterType,
});
const query = ref<RecordListFilter>(getDefautlQuery());
const tab_index = ref(query.value.filter);
const loading = ref(false);
const finished = ref(false);
const empty = computed(() => finished.value && !loading.value && list.value.length === 0);
const reset = () => {
  query.value.page = 1;
  tab_index.value = 0;
  list.value = [];
  query.value = Object.assign({}, getDefautlQuery());
};

const onChangeTab = async (id: number) => {
  if (loading.value) {
    return;
  }
  reset();
  query.value.filter = id as RecordListFilterType;
  tab_index.value = id;
  await router.replace({
    query: {
      tab: id,
    },
  });
  load();
};

const load = async () => {
  try {
    loading.value = true;
    const { points, total_count } = await useRecordList.run(query.value);

    list.value =
      query.value.page === 1
        ? points
        : [...list.value, ...getUniqueArr(list.value, points, "order_no")];
    finished.value = +total_count < query.value.limit * query.value.page;
    loading.value = false;
  } catch (error) {
    console.error(`[load] error`, error);
    loading.value = false;
    finished.value = true;
  }
};

const toOrderDetail = (item: RecordListItem) => {
  if (item.is_details) {
    router.push(`/points/order?id=${item.order_no}`);
  }
};

const loadMore = () => {
  query.value.page += 1;
  load();
};

onMounted(load);
</script>

<style lang="scss" scoped></style>
