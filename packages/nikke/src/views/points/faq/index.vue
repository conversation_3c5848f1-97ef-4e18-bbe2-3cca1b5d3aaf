<template>
  <div class="w-full min-h-screen relative pt-[74px] pb-[30px] bg-[var(--op-fill-white)]">
    <Head :title="t('faq')" @goback="handleRouterBack">
      <!--  <template #icon>
        <SvgIcon name="icon-setting" class="w-[24px] h-[24px] cursor-pointer"></SvgIcon>
      </template> -->
    </Head>

    <div
      class="fixed max-w-[var(--max-pc-w)] z-[20] top-[44px] left-1/2 w-full h-[30px] -translate-x-1/2"
    >
      <i
        class="absolute left-0 top-0 -z-[1] w-full h-[30px] bg-[url('@/assets/imgs/points/list-head-bg-3.png')] bg-[length:cover]"
      ></i>
    </div>
    <div ref="content_ref" class="relative px-[20px]"></div>
  </div>
</template>

<script setup lang="ts">
import Head from "@/components/common/head/index.vue";
import { useCms } from "@/composables/use-cms";
import { CMS_COLUMN_NAME_REWARDS, CMS_COLUMN_NAME_FAQ } from "packages/configs/cms";
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useNoCookie } from "packages/utils/cms";
import { t } from "@/locales";

const router = useRouter();
const content_ref = ref<HTMLElement>();
const handleRouterBack = () => {
  router.back();
};
const content = ref<{
  content_id: string;
  title: string;
  content: string;
}>({
  content_id: "",
  title: "",
  content: ``,
});
const { getSecondColumnConfig, getFeedDetail, cms_helper } = useCms({ cms_config: {} });
const load = async () => {
  const { label_id, primary_label_id } = await getSecondColumnConfig(
    CMS_COLUMN_NAME_REWARDS,
    CMS_COLUMN_NAME_FAQ,
  );
  const { data } = await cms_helper.getFeedsByColumns({
    primary_label_id,
    secondary_label_id: label_id,
    ext_info_type_list: [0, 1, 2],
    content_class: 0,
  });

  const target_content = data?.info_content?.[0] || {};
  content.value = await getFeedDetail(target_content.content_id);
  // 移到接口后面
  cms_helper.initDetailPage({
    el: content_ref.value,
    nocookie: useNoCookie(),
    cms_info: content.value,
    style_isolation: true,
    isolation_type: "shadowdom",
    isolation_extra_style: `
      img {
        max-width: 100%;
        max-height: 100%;
      }
      tr,
      td {
        border-color: var(--op-line-white) !important;
        color: var(--color-white) !important;
      }
      td span,
      td p,
      td div,
      td a {
        color: var(--color-white) !important;
      }
      p {
        margin: 0;
        padding: 0;
      }
      p > strong {
        display: block;
        padding-bottom: 8px;
        font-weight: 700;
        font-size: 14px;
        line-height: 16px;
        color: var(--text-1);
      }
      p > strong > span {
        display: block;
        padding-top: 16px;
      }
      p, p span {
        font-weight: 400;
        font-size: 13px;
        line-height: 16px;
        color: var(--text-1) !important;
      }
    `,
    video_style: "aspect-ratio: 16/9; width: 100%;",
  });
};

onMounted(() => {
  load();
});
onUnmounted(() => {});
</script>

<style lang="scss" scoped></style>
