<template>
  <div
    class="w-full min-h-screen relative z-[1] pt-[44px] pb-[30px] mx-auto bg-[var(--color-white)]"
  >
    <!-- 背景图 -->
    <i class="absolute -z-[1] top-0 left-0 w-full h-[480px] bg-[var(--other-4)]"></i>
    <i
      class="absolute -z-[1] top-0 left-0 w-full h-[415px] bg-[url('@/assets/imgs/points/points-bg.png')] bg-[length:100%_100%]"
    ></i>

    <Head :title="t('reward_center')" @goback="onBack">
      <template #icon>
        <SvgIcon
          name="icon-record"
          class="w-[24px] h-[24px] mr-[12px] cursor-pointer"
          color="var(--color-white)"
          @click="onDetail"
        ></SvgIcon>
        <SvgIcon
          name="icon-share"
          class="w-[24px] h-[24px] cursor-pointer"
          color="var(--color-white)"
          @click="onShare"
        ></SvgIcon>
      </template>
    </Head>

    <div class="flex items-center w-full justify-between mt-[8px] px-[12px]">
      <div class="flex items-center">
        <i
          class="w-[33px] h-[33px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%] mr-[8px]"
        ></i>
        <div
          v-if="is_login"
          class="font-[DINNextLTProBold] text-[color:var(--other-6)] mt-[4px] text-[length:20px] leading-[1]"
        >
          {{ user_store.total_point }}
        </div>
      </div>
      <!-- <div class="font-bold text-[color:var(--text-1)] text-[length:18px] leading-[22px]">FAQ</div> -->
      <div class="w-[24px] h-[24px] cursor-pointer relative" @click="toFaq">
        <i class="absolute-center"></i>
        <SvgIcon name="icon-question" color="var(--other-6)"></SvgIcon>
      </div>
    </div>

    <!-- banner -->
    <div class="w-[calc(100%_-_22px)] mt-[16px] mx-auto">
      <Banner></Banner>
    </div>

    <div class="px-[12px] mt-[16px] bg-[var(--other-4)] pb-[12px] pt-[10px]">
      <div class="flex items-center justify-between mb-[10px]">
        <div class="font-bold text-[color:var(--color-1)] text-[length:18px] leading-[22px]">
          {{ t("daily_quests") }}
        </div>
        <RefreshTag class="text-[color:var(--text-3)] text-[length:10px] font-[400]"></RefreshTag>
      </div>
      <DailyQuestion v-if="isCheckLipAccount" page="points"></DailyQuestion>
    </div>

    <div class="relative z-[20]">
      <Commodity v-if="isCheckLipAccount" @share="onShare"></Commodity>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, provide } from "vue";
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Banner from "@/components/points/home/<USER>/index.vue";
import DailyQuestion from "@/components/points/home/<USER>/index.vue";
import Commodity from "@/components/points/home/<USER>/index.vue";
import { useUser } from "@/store/user";
import { useCommonShare } from "@/components/common/share-pop";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { RoutesName } from "@/router/routes";
import { report } from "packages/utils/tlog";
import RefreshTag from "@/components/points/home/<USER>/index.vue";

defineEmits(["confirm", "cancel"]);

const { t } = useI18n();
const user_store = useUser();
const router = useRouter();
const isCheckLipAccount = ref(false);
const is_login = computed(() => user_store.is_login);

const onShare = () => {
  report.standalonesite_usercenter_reward_share_btn.cm_click();
  useCommonShare().share({
    text: t("share_points_text"),
    url: location.href,
  });
};
provide("share", onShare);
const onDetail = () => {
  report.standalonesite_usercenter_reward_history_btn.cm_click();
  router.push({ name: is_login.value ? RoutesName.POINTS_RECORD : RoutesName.LOGIN });
};
const onBack = () => {
  router.back();
};
onMounted(async () => {
  try {
    await user_store.checkHasLipAccount();
  } catch {
    console.warn("list onMounted checkHasLipAccount failed");
  } finally {
    isCheckLipAccount.value = true;
  }
  user_store.refreshPoints();
});

const toFaq = () => {
  router.push({ name: RoutesName.POINTS_FAQ });
  report.standalonesite_usercenter_reward_rule_btn.cm_click();
};

report.standalonesite_usercenter_reward_page.cm_vshow();
</script>

<style lang="scss" scoped></style>
