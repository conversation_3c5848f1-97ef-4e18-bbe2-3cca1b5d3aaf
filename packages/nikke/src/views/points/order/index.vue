<template>
  <div class="w-full min-h-screen relative pt-[74px] pb-[30px]">
    <Head :title="t('redeem_deatil')" @goback="router.back()"> </Head>

    <div
      class="fixed z-[20] max-w-[var(--max-pc-w)] top-[44px] left-1/2 w-full h-[30px] -translate-x-1/2"
    >
      <i
        class="absolute left-0 top-0 -z-[1] w-full h-[30px] bg-[var(--op-fill-white)] bg-[url('@/assets/imgs/points/list-head-bg-3.png')] bg-[length:cover]"
      ></i>
    </div>

    <div class="bg-[var(--op-fill-white)] relative px-[20px] flex flex-col">
      <div
        v-for="(item, index) in list.filter((item) => item.visible)"
        :key="index"
        :class="[
          item?.line
            ? `pb-[12px] border-b-[1px] border-[color:var(--line-1)] mb-[32px]`
            : `mb-[20px]`,
        ]"
      >
        <div
          :class="[
            `font-bold text-[color:var(--text-1)] flex items-center justify-between`,
            item?.line ? `text-[length:16px] leading-[19px]` : `text-[length:14px] leading-[16px]`,
          ]"
        >
          {{ item.title }}
        </div>

        <div
          :class="[
            `text-[color:var(--text-1)] leading-[16px] flex items-center gap-[8px]`,
            item?.line ? `text-[length:13px] mt-[8px]` : `text-[length:12px] mt-[6px]`,
          ]"
        >
          <span v-html="item.text"></span>
          <SvgIcon
            v-if="item.copy"
            name="icon-copy"
            class="w-[12px] h-[12px] mr-[4px] flex-shrink-0 cursor-pointer block"
            color="var(--text-3) "
            @click="onCopy(item.text)"
          ></SvgIcon>
        </div>

        <div v-if="item?.desc" class="flex items-center mt-[6px] h-[13px]">
          <SvgIcon
            name="icon-question"
            class="w-[12px] h-[12px] mr-[4px] flex-shrink-0 cursor-pointer"
            color="var(--text-3) "
          ></SvgIcon>
          <span
            v-safe-html="item.desc"
            class="text-[color:var(--text-3)] text-[length:11px] leading-[13px]"
          ></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import {
  OrderDetail,
  OrderDetailCommoditySubType,
  OrderDetailCommodityType,
  OrderDetailSendType,
  OrderStatus,
} from "packages/types/order";

// uitls
import { useRoute, useRouter } from "vue-router";
import { t } from "@/locales";
import { computed, ref } from "vue";
import dayjs from "dayjs";
import { useOrderDetail } from "@/api/order";
import { useToast } from "@/components/ui/toast";

const route = useRoute();
const router = useRouter();
const order_detail = ref<OrderDetail>({
  // order_id: "MJ21390384913298812387",
  // point_expired: true,
  // commodity_name: "PUBGM UC 30",
  // commodity_price: 100,
  // order_time: 1710231443843,
  // status: OrderStatus.send_commodity_failed_has_rollback,
});
const loading = ref(false);
const toast = useToast();
const list = computed(() => [
  {
    title: `${t("order_no")}.`,
    text: order_detail.value.order_id,
    desc: "",
    line: true,
    visible: true,
  },
  {
    title: t("trade_name"),
    text: order_detail.value.commodity_name,
    desc: "",
    visible: true,
  },
  {
    title: t("card_no"),
    text: order_detail.value.send_detail?.Card,
    visible: [OrderDetailSendType.CardPassword].includes(order_detail.value.send_type!),
    copy: true,
  },
  {
    title: t("card_code"),
    text:
      order_detail.value.send_type === OrderDetailSendType.Cdkey
        ? order_detail.value.send_detail?.cdkey
        : order_detail.value.send_detail?.Password,
    visible: [OrderDetailSendType.Cdkey, OrderDetailSendType.CardPassword].includes(
      order_detail.value.send_type!,
    ),
    copy: true,
  },
  {
    title: t("consumption_points"),
    text:
      (order_detail.value.commodity_is_discount
        ? `<span class="mr-[4px]">${order_detail.value.commodity_discount_price}</span> <span class="line-through">${order_detail.value.commodity_price}</span>`
        : `<span>${order_detail.value.commodity_price}</span>`) + ` ${t("coin")}`,
    desc: "",
    visible: true,
  },
  {
    title: t("trading_hours"),
    text: dayjs(order_detail.value.order_time! * 1000).format("MM/DD/YYYY HH:mm:ss"),
    desc: "",
    visible: true,
  },
  {
    title: t("order_status"),
    text: getOrderStatusText(order_detail.value.status!),
    desc: getOrderStatusTips(order_detail.value.status!),
    visible: true,
  },
]);

const getOrderStatusText = (status: OrderStatus) => {
  return (
    {
      [OrderStatus.order_not_begin]: t("order_pending"),
      [OrderStatus.deduct_points_err]: t("order_error"),
      [OrderStatus.send_commodity_err]: t("order_error"),
      [OrderStatus.send_commodity_ing]: t("order_pending"),
      [OrderStatus.gift_package_distribution_completed]: t("order_complete"),
      [OrderStatus.deduct_points_failed_has_rollback]: t("order_closed"),
      [OrderStatus.send_commodity_failed_has_rollback]: t("order_closed"),
      [OrderStatus.rollback_points_error]: t("order_closed"),
      [OrderStatus.send_zero_price_commodity_err]: t("order_closed"),
    }[status] || "--"
  );
};

const RedeemConfigs: Partial<Record<OrderDetailCommoditySubType, { url: string; tips: string }>> = {
  [OrderDetailCommoditySubType.Steam]: {
    url: "https://store.steampowered.com/account/redeemwalletcode",
    tips: t("to_stream_official_website_tips"),
  },
  [OrderDetailCommoditySubType.Amazon]: {
    url: "https://www.amazon.com/gc/redeem",
    tips: t("to_amazon_official_website_tips"),
  },
  [OrderDetailCommoditySubType.Apple]: {
    url: "https://apps.apple.com/redeem/",
    tips: t("to_apple_official_website_tips"),
  },
  [OrderDetailCommoditySubType.Google]: {
    url: "https://play.google.com/redeem",
    tips: t("to_google_official_website_tips"),
  },
};

const getOrderStatusTips = (status: OrderStatus) => {
  if ([OrderDetailCommodityType.card].includes(order_detail.value.commodity_type!)) {
    const config = RedeemConfigs[order_detail.value.commodity_sub_type!];
    if (!config) {
      return "";
    }
    return `<span>
          <span>${config.tips}</span>
          （<a href="${config.url}" target="_blank" class="normal-case underline text-[#7064ff]">${config.url}</a>）
        </span>`;
  }

  return (
    {
      [OrderStatus.order_not_begin]: t("order_not_begin"),
      [OrderStatus.deduct_points_err]: t("deduct_points_err"),
      [OrderStatus.send_commodity_err]: t("send_commodity_err"),
      [OrderStatus.send_commodity_ing]: "",
      [OrderStatus.gift_package_distribution_completed]: t("gift_package_distribution_completed"),
      [OrderStatus.deduct_points_failed_has_rollback]: t("deduct_points_failed_has_rollback"),
      [OrderStatus.send_commodity_failed_has_rollback]: t("send_commodity_failed_has_rollback"),
      [OrderStatus.rollback_points_error]: t("rollback_points_error"),
      [OrderStatus.send_zero_price_commodity_err]: t("send_zero_price_commodity_err"),
    }[status] || ""
  );
};

const onCopy = async (value: any) => {
  const win = window.parent;
  await win.navigator.clipboard.writeText(value);
  toast.show({
    text: `${t("copied")}: ${value}`,
    type: "success",
  });
};

const load = async () => {
  try {
    if (loading.value) {
      return;
    }
    loading.value = true;
    order_detail.value.order_id = route.query.id as string;
    Object.assign(
      order_detail.value,
      await useOrderDetail.run({ order_id: order_detail.value.order_id }),
    );
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error("[load] error: ", error);
  }
};
load();
</script>

<style lang="scss" scoped></style>
