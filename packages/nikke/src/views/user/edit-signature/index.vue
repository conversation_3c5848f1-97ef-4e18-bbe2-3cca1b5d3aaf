<template>
  <div class="w-full mx-auto pt-[44px] pb-[30px] box-border min-h-screen bg-[var(--op-fill-white)]">
    <Head
      class="border-b-[1px] border-solid border-[color:var(--line-1)]"
      :title="t('edit_signature')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      :nogo-back="true"
      @close="handleRouterBack"
    >
      <template #icon>
        <Btns
          :text="t('save')"
          :type="!is_disabled ? 'primary' : 'disabled'"
          size="m"
          @click="onSave"
        ></Btns>
      </template>
    </Head>

    <MultiLineInput
      v-model="remark"
      class="mt-[20px] mx-[15px]"
      :placeholder="t('share_my_opinion')"
      :max-length="100"
    ></MultiLineInput>
    <p
      v-if="user_info?.audit_remark"
      class="font-normal text-[length:10px] leading-[12px] text-[color:var(--text-3)] mt-[4px] px-[12px] break-words hyphens-auto"
    >
      {{ t("audit_remark") }}: {{ user_info?.audit_remark }}
    </p>
  </div>
</template>

<script setup lang="ts">
import Head from "@/components/common/head/index.vue";
import MultiLineInput from "@/components/common/multi-line-input/index.vue";
import Btns from "@/components/common/btns/index.vue";
import { useRouter } from "vue-router";
import { useUser } from "@/store/user";
import { computed, ref, watch } from "vue";
import { useModifyInfo } from "@/api/user";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { report } from "packages/utils/tlog";

const { t } = useI18n();

const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { user_info } = storeToRefs(useUser());
const remark = ref<string>(user_info.value?.remark || "");

watch(
  () => user_info.value?.remark,
  (val) => {
    val && (remark.value = val);
  },
);

const is_disabled = computed(() => remark.value === user_info.value?.remark);

const { mutateAsync: modify } = useModifyInfo();
const onSave = async () => {
  report.standalonesite_signaturesettingpage_submit_btn.cm_click({ sig: remark.value });
  if (!user_info.value) return console.error("user info is empty");
  await modify({
    remark: remark.value,
    username: user_info.value?.username,
    avatar: user_info.value?.avatar,
  });
  handleRouterBack();
};

report.standalonesite_signaturesettingpage.cm_vshow();
</script>

<style lang="scss" scoped></style>
