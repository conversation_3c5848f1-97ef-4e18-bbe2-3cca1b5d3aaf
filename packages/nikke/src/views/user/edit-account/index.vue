<template>
  <div
    class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border relative z-[0]"
  >
    <div class="absolute w-full h-[235px] top-[51px] left-0 -z-[1] pointer-events-none">
      <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
    </div>
    <i
      class="absolute top-[51px] left-0 w-full h-[235px] z-[-1] bg-[image:var(--linear-gradient-2)] pointer-events-none"
    ></i>

    <div class="absolute bottom-[7px] left-0 w-full h-[235px] rotate-180">
      <div class="absolute w-full top-0 h-[235px] left-0 -z-[1] pointer-events-none">
        <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
      </div>
      <i
        class="absolute bottom-0 left-0 w-full h-[235px] z-[-1] bg-[image:var(--linear-gradient-2)] pointer-events-none"
      ></i>
    </div>

    <Head
      :title="t('edit_my_account')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div
      class="w-[80px] h-[80px] mt-[28px] mb-[20px] mx-auto"
      @click="router.push(Routes.USER_EDIT_AVATAR)"
    >
      <Avatar
        v-if="user_info && user_info"
        :src="user_info.avatar"
        :frame="user_info.avatar_pendant"
      />
    </div>

    <div class="px-[12px]">
      <div class="flex items-center justify-center">
        <div
          class="cursor-pointer flex-1 bg-[var(--op-fill-white)] h-[42px] flex items-center justify-center mr-[5px]"
          @click="router.push(Routes.USER_EDIT_AVATAR)"
        >
          <div
            class="w-[16px] h-[16px] mr-[6px] bg-[url('@/assets/imgs/common/icon-edit1.png')] bg-[length:100%_100%]"
          ></div>
          <div class="text-[color:var(--text-1)] text-[12px] font-medium mt-[2px]">
            {{ t("edit_avatar") }}
          </div>
        </div>
        <div
          class="cursor-pointer flex-1 bg-[var(--op-fill-white)] h-[42px] flex items-center justify-center"
          @click="router.push(Routes.USER_EDIT_AVATARFRAME)"
        >
          <div
            class="w-[16px] h-[16px] mr-[6px] bg-[url('@/assets/imgs/common/icon-edit2.png')] bg-[length:100%_100%]"
          ></div>
          <div class="text-[color:var(--text-1)] text-[12px] font-medium mt-[2px]">
            {{ t("to_edit_avatar_frame") }}
          </div>
        </div>
      </div>

      <SettingItem
        v-for="item in list"
        :key="item.title"
        class="mt-[12px] first:mt-0"
        :title="item.title"
        :info-text="item.infoText"
        @user-click="item.event"
      ></SettingItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from "vue";
import Head from "@/components/common/head/index.vue";
import SettingItem from "@/components/common/setting-item/index.vue";
import { useRouter } from "vue-router";
import { useUser } from "@/store/user";
import { Routes } from "@/router/routes";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { report } from "packages/utils/tlog";
import Avatar from "@/components/common/avatar/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

const { t } = useI18n();

const router = useRouter();
const { refetchUserInfo } = useUser();
const { user_info } = storeToRefs(useUser());

const handleRouterBack = () => {
  router.back();
};

const list = computed(() => [
  {
    title: t("nickname"),
    infoText: user_info.value?.audit_username || user_info.value?.username,
    event: () => {
      router.push(Routes.USER_EDIT_NICKNAME);
    },
  },
  {
    title: t("signature"),
    infoText: user_info.value?.audit_remark || user_info.value?.remark,
    event: () => {
      router.push(Routes.USER_EDIT_SIGNATURE);
    },
  },
]);

onMounted(async () => {
  await refetchUserInfo();
  // console.log("user_info", user_info.value);
});

report.standalonesite_profilepage.cm_vshow();
</script>

<style lang="scss" scoped></style>
