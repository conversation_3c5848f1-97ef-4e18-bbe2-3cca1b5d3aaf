<template>
  <div class="w-full mx-auto min-h-screen pt-[44px] box-border flex flex-col">
    <Head
      :title="t('edit_avatar')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      :nogo-back="true"
      @close="handleRouterBack"
    >
      <template #icon>
        <Btns
          :text="t('save')"
          :type="!is_disabled ? 'primary' : 'disabled'"
          size="m"
          @click="onSave"
        ></Btns>
      </template>
    </Head>

    <div class="flex h-0 grow flex-col overflow-y-auto">
      <div
        class="w-full pt-[28px] overflow-hidden z-0 relative pb-[20px] bg-[var(--fill-3)] dark:bg-[var(--op-fill-white)] dark:border-none border-b-[1px] border-solid border-[color:var(--line-1)] flex-shrink-0"
      >
        <div class="absolute w-full h-[235px] top-[7px] left-0 -z-[1] pointer-events-none">
          <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
        </div>
        <i
          class="absolute top-[7px] left-0 w-full h-[235px] z-[-1] bg-[image:var(--linear-gradient-1)] pointer-events-none"
        ></i>

        <div class="w-[80px] h-[80px] mx-auto">
          <Avatar :src="current_avatar" :frame="user.user_info?.avatar_pendant" />
        </div>
      </div>

      <div
        class="w-full h-0 grow overflow-y-auto bg-[var(--fill-0)] pt-[20px] pb-[30px] box-border"
      >
        <ul
          class="grid grid-cols-[repeat(3,90px)] gap-x-[25px] gap-y-[20px] justify-items-center justify-center"
        >
          <li
            v-for="item in avatars"
            :key="item"
            :class="{ active: current_avatar === item }"
            class="w-[89px] h-[89px] rounded-[50%] overflow-hidden outline-[length:1px] outline-[color:var(--op-line-white)] outline cursor-pointer [&.active]:outline-[color:var(--brand-1)] [&.active]:outline-[length:2px] box-border"
            @click="current_avatar = item"
          >
            <CommonImage :src="item" class="inline-block w-full object-cover" />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import { useRouter } from "vue-router";
import { useUser } from "@/store/user";
import { useModifyInfo, useUserAvatars } from "@/api/user";
import { useI18n } from "vue-i18n";
import { CommonImage } from "@/components/common/image";
import { report } from "packages/utils/tlog";
import Avatar from "@/components/common/avatar/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

const router = useRouter();
const { t } = useI18n();
const handleRouterBack = () => {
  router.back();
};

const user = useUser();
const current_avatar = ref(user.user_info?.avatar);

const { data } = useUserAvatars({});

const avatars = computed(() =>
  data.value ? [...data.value.currency, ...data.value.game_avatar] : [],
);

watch(
  () => user.user_info?.avatar,
  (val) => {
    val && (current_avatar.value = val);
  },
);

const is_disabled = computed(
  () => !current_avatar.value || current_avatar.value === user.user_info?.avatar,
);

const { mutateAsync: modify } = useModifyInfo();
const onSave = async () => {
  if (!current_avatar.value) return;
  if (!user.user_info) return console.error("user info is empty");
  await modify({
    avatar: current_avatar.value,
    username: user.user_info?.username,
    remark: user.user_info?.remark,
  });
  handleRouterBack();
};

report.standalonesite_avatarsettingpage.cm_vshow();
</script>

<style lang="scss" scoped></style>
