<template>
  <div class="w-full mx-auto pt-[44px] pb-[30px] box-border relative z-[1] min-h-screen">
    <Head
      class="border-b-[1px] border-solid border-[color:var(--line-1)]"
      :title="t('links_manage')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div class="mt-[20px] px-[15px]">
      <LinkItem
        v-for="(item, index) in channel_links"
        :key="'link-m' + index"
        :item="item"
        :disabled="false"
        class="mt-[16px] first:mt-0"
      ></LinkItem>
    </div>

    <div class="absolute w-full h-[235px] bottom-[7px] left-0 -z-[1] pointer-events-none">
      <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import LinkItem from "@/components/common/link-item/index.vue";
import { useRouter } from "vue-router";
import { useUserCenter } from "@/composables/use-user-center";

import SvgIcon from "@/components/common/svg-icon.vue";

const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { channel_links } = useUserCenter();

const { t } = useI18n();
</script>

<style lang="scss" scoped></style>
