<template>
  <div class="w-full mx-auto min-h-screen box-border flex flex-col relative">
    <div class="sticky top-0 z-[1] pt-[44px] flex-none">
      <Head
        :title="t('edit_avatarframe')"
        bg="bg-[var(--fill-0)]"
        color="var(--text-1)"
        @goback="router.back()"
      >
      </Head>

      <div
        class="w-full pt-[28px] h-[205px] overflow-hidden z-0 relative pb-[20px] px-[20px] bg-[url('@/assets/imgs/common/edit-avatarframe-bg.png')] bg-[length:100%_auto] dark:border-none flex-shrink-0"
      >
        <div class="w-[80px] h-[80px] mx-auto">
          <Avatar :src="user.user_info?.avatar" :frame="selected_frame_info?.avatar_pendant" />
        </div>

        <div
          class="text-[color:var(--color-1)] text-[16px] font-bold mt-[14px] leading-[19px] text-center line-clamp-1"
        >
          {{ selected_frame_info?.title }}
        </div>
        <div
          v-safe-html="selected_frame_info?.condition"
          class="mt-[4px] text-[color:var(--color-7)] text-[12px] leading-[18px] mx-auto w-auto max-w-[max-content] line-clamp-3"
        ></div>
      </div>
    </div>

    <InfiniteScroll
      :distance="200"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      class="py-[30px] z-0 px-[26px] gap-[18px] grid grid-cols-[repeat(3,minmax(95px,1fr))] content-start !pb-[140px] flex-1"
      @load-more="load"
    >
      <div
        v-for="item in list"
        :key="item.id"
        :class="[
          `box-border w-full aspect-square relative flex items-center justify-center rounded-[2px] cursor-pointer`,
          selected_frame_id === item.id
            ? 'border-[var(--brand-1)] border-[1.5px] bg-[color:var(--brand-2)] dark:bg-[color:var(--brand-1-20)]'
            : 'border-[1px] border-[var(--line-1)]',
        ]"
        @click.stop="onClickItem(item.id)"
      >
        <div
          v-show="item.is_weared"
          class="absolute w-[20px] h-[20px] -top-[6px] -right-[6px] rounded-full bg-[color:var(--brand-1)] z-[2] flex items-center justify-center"
        >
          <SvgIcon name="icon-true2" class="w-[12px] h-[12px]" color="var(--color-white)"></SvgIcon>
        </div>
        <div class="w-[calc(100%_-_12px)] aspect-square">
          <CommonImage :src="item.avatar_pendant" class="w-full h-full"></CommonImage>
        </div>
        <div
          v-if="!item.is_owned"
          class="absolute w-[20px] h-[20px] bg-[var(--fill-1-80)] bottom-[4px] right-[4px] rounded-[2px] z-[10]"
        >
          <SvgIcon name="icon-lock" color="var(--color-white)"></SvgIcon>
        </div>
      </div>
    </InfiniteScroll>
  </div>

  <CommonBottomPopup :show="show" nobg @close="show = false">
    <div
      v-if="selected_frame_info"
      class="flex items-center justify-between px-[20px] pt-[35px] pb-[50px]"
    >
      <!-- 未锁住 -->
      <div v-if="selected_frame_info.is_owned" class="flex-1">
        <div class="flex items-center">
          <SvgIcon
            name="icon-history"
            class="w-[16px] h-[16px] mr-[4px] -mt-[2px]"
            color="var(--brand-1)"
          ></SvgIcon>
          <div class="text-[color:var(--text-1)] text-[12px] font-medium">
            {{ t("period_of_validity") }}
          </div>
        </div>
        <div
          v-if="!selected_frame_info.is_permanent"
          class="text-[color:var(--text-2)] text-[12px] font-medium mt-[4px]"
        >
          {{ dayjs.unix(selected_frame_info.valid_begin_at).format("YYYY/MM/DD") }}-{{
            dayjs.unix(selected_frame_info.valid_end_at).format("YYYY/MM/DD")
          }}
        </div>
        <div v-else class="text-[color:var(--text-2)] text-[12px] font-medium mt-[4px]">
          {{ t("permanent_valid") }}
        </div>
      </div>
      <!-- 锁住 -->
      <div v-else class="flex-1 flex items-center">
        <div class="w-[16px] h-[16px] mr-[4px] -mt-[2px]">
          <SvgIcon name="icon-lock" color="var(--text-1)"></SvgIcon>
        </div>
        <div class="text-[color:var(--text-1)] text-[12px]">{{ t("avatar_frame_locked") }}</div>
      </div>

      <Btns
        v-if="action === 'save'"
        :text="t('wear')"
        type="primary"
        size="m"
        @click="onSave"
      ></Btns>
      <Btns
        v-else-if="action === 'demount'"
        :text="t('demount')"
        type="primary"
        size="m"
        @click="onDemount"
      ></Btns>
      <Btns
        v-else-if="action === 'to-get'"
        :text="t('to_get')"
        type="primary"
        size="m"
        @click="jump(selected_frame_info?.jump_url || '')"
      ></Btns>
      <Btns v-else-if="action === 'expired'" :text="t('expired')" type="disabled" size="m"></Btns>
      <Btns
        v-else-if="action === 'locked'"
        :text="t('avatar_frame_locked')"
        type="disabled"
        size="m"
      ></Btns>
    </div>
  </CommonBottomPopup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import { useUser } from "@/store/user";
import { useGetUserAvatarPendantList, useSetUserAvatarPendant } from "@/api/user";
import { useI18n } from "vue-i18n";
import { CommonImage } from "@/components/common/image";
// import { report } from "packages/utils/tlog";
import CommonBottomPopup from "@/components/common/comm-bottom-popup.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Avatar from "@/components/common/avatar/index.vue";

import dayjs from "dayjs";
import { getStandardizedLang } from "packages/utils/standard";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { InfiniteScroll } from "@/components/common/scroll/index";
import { useRouter } from "vue-router";
import { useWebCredential } from "@/composables/use-webcredential";

const router = useRouter();

const { t } = useI18n();

const { openUrlWithAuth } = useWebCredential();

const show = ref(false);

const user = useUser();

const selected_frame_id = ref<string>();
const selected_frame_info = computed(() => {
  return list.value.find((item) => item.id == selected_frame_id.value);
});

// 头像挂件列表
const { list, loading, load, reset, finished, empty } = useInfiniteList({
  queryFn: async (ctx) => {
    const data = await useGetUserAvatarPendantList.run({
      limit: 24,
      next_page_cursor: ctx.next_page_cursor,
    });
    return { page_info: data.page_info, list: data.userAvatarPendants };
  },
  item_key: "id",
});

watch(
  () => [user.user_info?.avatar_pendant, list.value],
  ([val]) => {
    if (!val) return;
    const match = list.value.find((item) => item.avatar_pendant === val);
    if (!match) return;
    selected_frame_id.value = match.id;
    show.value = true;
  },
  { immediate: true },
);

const onClickItem = (id: string) => {
  const current = list.value.find((item) => item.id === id)!;
  if (selected_frame_id.value === id) {
    if (current.is_weared) {
      selected_frame_id.value = undefined;
      show.value = false;
    } else {
      const weared = list.value.find((item) => item.is_weared);
      selected_frame_id.value = weared?.id;
      show.value = !!weared;
    }
    show.value = false;
  } else {
    selected_frame_id.value = id;
    show.value = true;
  }
};

const is_valid = (id: string) => {
  const match = list.value.find((item) => item.id === id);
  if (!match) return false;
  if (match.is_permanent) return true;
  if (match.valid_begin_at && dayjs().isBefore(dayjs.unix(match.valid_begin_at))) return false;
  if (match.valid_end_at && dayjs().isAfter(dayjs.unix(match.valid_end_at))) return false;
  return true;
};

const { mutateAsync: modify } = useSetUserAvatarPendant();
const onSave = async () => {
  if (!selected_frame_info.value) return;
  if (!selected_frame_info.value.is_owned) return;
  if (selected_frame_info.value.is_weared) return;
  await modify({ avatar_pendant_id: +selected_frame_info.value.id, set_wear_status: 1 });
  reset();
};

const onDemount = async () => {
  const current = list.value.find((item) => item.is_weared);
  if (!current) return;
  await modify({ avatar_pendant_id: +current.id, set_wear_status: 0 });
  selected_frame_id.value = undefined;
  show.value = false;
  reset();
};

const action = computed(() => {
  if (!selected_frame_info.value) return null;
  if (selected_frame_info.value.is_weared) return "demount";
  if (!selected_frame_info.value.is_owned) {
    if (selected_frame_info.value.jump_url) return "to-get";
    return "locked";
  }
  if (is_valid(selected_frame_info.value.id)) return "save";
  return "expired";
});

const jump = (url: string) => {
  try {
    const parse = new URL(url);
    parse.searchParams.set("lang", getStandardizedLang());
    openUrlWithAuth(parse.toString(), "_blank");
  } catch (e) {
    url && openUrlWithAuth(url, "_blank");
  }
};

// report.standalonesite_avatarsettingpage.cm_vshow();
</script>

<style lang="scss" scoped></style>
