<template>
  <div class="bg-[var(--fill-3)] min-h-screen">
    <Head
      bg="bg-[var(--fill-0)]"
      :z-index="21"
      :title="t('comment_decorations')"
      :color="'var(--text-1)'"
      @goback="router.back()"
    >
    </Head>
    <div class="pt-[44px]">
      <div class="pt-[12px] px-[12px] bg-[var(--fill-0)] pb-[20px] flex sticky top-[44px] z-[10]">
        <div class="w-[32px] h-[32px] flex-shrink-0 mr-[8px]">
          <Avatar
            :src="user_store.user_info?.avatar || ``"
            :auth_type="user_store.user_info?.auth_type"
            :frame="user_store.user_info?.avatar_pendant"
          ></Avatar>
        </div>
        <div class="flex-1">
          <div class="text-[color:var(--text-3)] text-[length:13px] flex items-center h-[16px]">
            <span class="mr-[8px]">{{ user_store.user_info?.username }}</span>
            <!-- <span class="text-[length:11px]">#1</span> -->
          </div>
          <div class="mt-[12px] text-[var(--text-1)]">
            <Bubble
              :bg="selected_frame_info?.bg_color"
              :img="selected_frame_info?.comment_bubble"
              class="mt-[32px]"
            >
              <div class="text-[length:12px] leading-[14px] flex items-center h-[16px]">
                {{ t("hi_commander") }}
              </div>
            </Bubble>
          </div>
        </div>
      </div>

      <InfiniteScroll
        :distance="200"
        :back_to_top_visible="false"
        :loading_visible="false"
        :finished_visible="false"
        :loading="loading"
        :empty="empty"
        :finished="finished"
        :debounce_interval="10"
        class="py-[30px] z-0 px-[26px] gap-[18px] grid grid-cols-[repeat(2,minmax(95px,1fr))] !pb-[160px] flex-1"
        @load-more="load"
      >
        <div
          v-for="item in list"
          :key="item.id"
          :class="[
            `relative cursor-pointer __w-[calc((100%_-_16px)_/_2)] mb-[12px] bg-[var(--fill-0)] px-[12px] pb-[8px] pt-[28px] rounded-[10px] even:mr-0`,
            selected_frame_id === item.id
              ? 'border-[1.5px] border-[var(--brand-1)]'
              : 'border-[1px] border-[var(--line-1)]',
          ]"
          @click.stop="onClickItem(item.id)"
        >
          <Bubble :bg="item.bg_color" :img="item.comment_bubble" class="w-full !min-h-[57px]">
            <div class="text-[length:12px] leading-[14px] flex items-center h-[16px]">
              {{ t("hi_commander") }}
            </div>
          </Bubble>
          <div
            v-show="item.is_weared"
            class="absolute w-[20px] h-[20px] -top-[6px] -right-[6px] rounded-full bg-[color:var(--brand-1)] z-[2] flex items-center justify-center"
          >
            <SvgIcon
              name="icon-true2"
              class="w-[12px] h-[12px]"
              color="var(--color-white)"
            ></SvgIcon>
          </div>
          <div
            v-if="!item?.is_owned"
            class="absolute w-[20px] h-[20px] bg-[var(--fill-1-80)] bottom-[4px] right-[4px] rounded-[2px] z-[10]"
          >
            <SvgIcon name="icon-lock" color="var(--color-white)"></SvgIcon>
          </div>
        </div>
        <!-- empty -->
        <NoData v-if="empty" :text="t('no_data')"></NoData>
      </InfiniteScroll>
    </div>
  </div>

  <CommentBubbleAction
    :show="show"
    :selected_frame_info="selected_frame_info"
    :action="action"
    @close="show = false"
    @save="onSave"
    @demount="onDemount"
  ></CommentBubbleAction>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import Head from "@/components/common/head/index.vue";
import Avatar from "@/components/common/avatar/index.vue";
import Bubble from "@/components/common/bubble/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import CommentBubbleAction from "@/components/user-center/comment-bubble-action/index.vue";
import NoData from "@/components/common/nodata.vue";
import { useRouter } from "vue-router";
import { computed, ref, watch } from "vue";
// import avatar from "@/assets/imgs/test/avatar-02.png";

import { useI18n } from "vue-i18n";
// import { useUser } from "@/store/user";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { useGetUserCommentBubbleList, useSetUserCommentBubble } from "@/api/user-comment-bubble";
import dayjs from "dayjs";
// import { getStandardizedLang } from "packages/utils/standard";
import { useUser } from "@/store/user";
// import { mock_list as list } from "@/store/post/comment-bubble";

// const route = useRoute();
const router = useRouter();

// const active = ref(0);

const { t } = useI18n();

const show = ref(false);

const user_store = useUser();

// const user = useUser();

const selected_frame_id = ref<string | number>();
const selected_frame_info = computed(() => {
  return list.value.find((item) => item.id == selected_frame_id.value);
});

// 头像挂件列表
const {
  list: list,
  loading,
  load,
  reset,
  finished,
  empty,
} = useInfiniteList({
  queryFn: async (ctx) => {
    const data = await useGetUserCommentBubbleList.run({
      limit: 24,
      next_page_cursor: ctx.next_page_cursor,
    });
    return { page_info: data.page_info, list: data.user_comment_bubbles };
  },
  item_key: "id",
});

watch(
  () => [list.value],
  () => {
    const match = list.value.find((item) => item.is_weared);
    if (!match) return;
    selected_frame_id.value = match.id;
    show.value = true;
  },
  { immediate: true },
);

const onClickItem = (id: string) => {
  const current = list.value.find((item) => item.id == id)!;
  if (selected_frame_id.value === id) {
    if (current.is_weared) {
      selected_frame_id.value = undefined;
      show.value = false;
    } else {
      const weared = list.value.find((item) => item.is_weared);
      selected_frame_id.value = weared?.id;
      show.value = !!weared;
    }
    show.value = false;
  } else {
    selected_frame_id.value = id;
    show.value = true;
  }
};

const is_valid = (id: string) => {
  const match = list.value.find((item) => item.id == id);
  if (!match) return false;
  if (match.is_permanent) return true;
  if (match.valid_begin_at && dayjs().isBefore(dayjs.unix(match.valid_begin_at))) return false;
  if (match.valid_end_at && dayjs().isAfter(dayjs.unix(match.valid_end_at))) return false;
  return true;
};

const { mutateAsync: modify } = useSetUserCommentBubble();
const onSave = async () => {
  if (!selected_frame_info.value) return;
  if (!selected_frame_info.value.is_owned) return;
  if (selected_frame_info.value.is_weared) return;
  await modify({ comment_bubble_id: selected_frame_info.value.id, set_wear_status: 1 });
  reset();
};

const onDemount = async () => {
  const current = list.value.find((item) => item.is_weared);
  if (!current) return;
  await modify({ comment_bubble_id: current.id, set_wear_status: 0 });
  selected_frame_id.value = undefined;
  show.value = false;
  reset();
};

const action = computed(() => {
  if (!selected_frame_info.value) return null;
  if (selected_frame_info.value.is_weared) return "demount";
  if (!selected_frame_info.value.is_owned) {
    if (selected_frame_info.value.jump_url) return "to-get";
    return "locked";
  }
  if (is_valid(selected_frame_info.value.id)) return "save";
  return "expired";
});
</script>
