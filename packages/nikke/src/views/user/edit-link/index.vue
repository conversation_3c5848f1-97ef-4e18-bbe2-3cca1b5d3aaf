<template>
  <div class="w-full mx-auto pt-[44px] pb-[30px] box-border min-h-screen bg-[var(--op-fill-white)]">
    <Head
      class="border-b-[1px] border-solid border-[color:var(--line-1)]"
      :title="t('edit_link')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      :nogo-back="true"
      @close="handleRouterBack"
    >
      <template #icon>
        <Btns :text="t('save')" type="primary" size="m" @click="onSubmit"></Btns>
      </template>
    </Head>

    <MultiLineInput v-model="link" class="mt-[20px] mx-[15px]" :max-length="100"></MultiLineInput>

    <p
      class="font-normal text-[length:10px] leading-[12px] text-[--text-3] mt-[4px] px-[12px] break-words hyphens-auto"
    >
      {{ t("edit_user_link_tips") }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import MultiLineInput from "@/components/common/multi-line-input/index.vue";
import { useRouter, useRoute } from "vue-router";
import { useUserCenter } from "@/composables/use-user-center";
import { useModifyLink } from "@/api/user-links";
import { useToast } from "@/components/ui/toast";
import { t } from "@/locales";

const router = useRouter();
const handleRouterBack = () => {
  router.back();
};
const route = useRoute();
const { link_name = "" } = route.query;
if (!link_name) {
  router.push("/");
}
const link = ref(decodeURIComponent((route.query.link_url as string) ?? ""));

const { show } = useToast();

const { channel_links, refetchUserInfo } = useUserCenter();
const { mutateAsync: modify } = useModifyLink();
const onSubmit = async () => {
  if (link.value) {
    const url = parseUrl(link.value);
    if (!url) {
      return show({ text: t("enter_valid_url_tips"), type: "error" });
    }
    const { hostname } = url;
    const channel_hosts = getChannelHost(link_name as string);
    if (channel_hosts && channel_hosts.every((item) => !hostname.includes(item))) {
      return show({ text: t("enter_channel_url_tips"), type: "error" });
    }
  }
  const links = channel_links.value.map((item) => {
    const { channel_name } = item;
    if (link_name === channel_name) {
      item.url = link.value;
    }
    return { url: item.url, channel_name };
  });
  await modify({ links });
  refetchUserInfo();
  router.back();
};

const parseUrl = (url: string) => {
  try {
    return new URL(url);
  } catch (error) {
    return null;
  }
};
const getChannelHost = (channel: string) => {
  if (channel === "x") return ["x.com", "twitter.com"];
  if (channel === "youtube") return ["youtube.com", "youtu.be"];
  if (channel === "lt") return ["twitch.tv"];
  if (channel === "p") return ["pixiv.net"];
  if (channel === "dy") return ["tiktok.com"];
  return null;
};
</script>

<style lang="scss" scoped></style>
