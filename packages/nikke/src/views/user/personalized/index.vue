<template>
  <div class="bg-[var(--fill-3)] min-h-screen">
    <Head
      bg="bg-[var(--fill-0)]"
      :z-index="2"
      :title="t('personalized')"
      :color="'var(--text-1)'"
      @goback="router.back()"
    >
    </Head>
    <div class="pt-[64px] mx-[24px] flex flex-wrap">
      <div
        class="w-[calc((100%_-_12px)_/_2)] cursor-pointer pt-[16px] px-[16px] h-[161px] bg-[var(--fill-0)] shadow-[0px_1px_5px_var(--op-shadow-black-5)] mr-[12px]"
        @click="router.push(Routes.USER_EDIT_AVATARFRAME)"
      >
        <div class="w-[120px] h-[100px] mx-auto">
          <CommonImage :src="img1"></CommonImage>
        </div>
        <div
          class="mt-[5px] fotn-medium text-[length:11px] text-[color:var(--text-1)] leading-[14px] text-center"
        >
          {{ t("edit_avatarframe") }}
        </div>
      </div>
      <div
        class="w-[calc((100%_-_12px)_/_2)] cursor-pointer pt-[16px] px-[16px] h-[161px] bg-[var(--fill-0)] shadow-[0px_1px_5px_var(--op-shadow-black-5)]"
        @click="router.push(Routes.USER_EDIT_COMMENT_BUBBLE)"
      >
        <div class="w-[120px] h-[100px] mx-auto">
          <CommonImage :src="img2"></CommonImage>
        </div>
        <div
          class="mt-[5px] fotn-medium text-[length:11px] text-[color:var(--text-1)] leading-[14px] text-center"
        >
          {{ t("comment_decorations") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Head from "@/components/common/head/index.vue";
import { CommonImage } from "@/components/common/image";
import { Routes } from "@/router/routes";
import { useRouter } from "vue-router";

import img1 from "@/assets/imgs/common/icon-personalized-1.png";
import img2 from "@/assets/imgs/common/icon-personalized-2.png";
import { t } from "@/locales";

const router = useRouter();
</script>
