<template>
  <div class="w-full mx-auto pt-[44px] pb-[30px] box-border min-h-screen bg-[var(--op-fill-white)]">
    <Head
      class="border-b-[1px] border-solid border-[color:var(--line-1)]"
      :title="t('edit_nickname')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      :nogo-back="true"
      @close="handleRouterBack"
    >
      <template #icon>
        <Btns
          :text="t('save')"
          :type="!is_disabled ? 'primary' : 'disabled'"
          size="m"
          class="!border-0"
          @click="onSave"
        ></Btns>
      </template>
    </Head>

    <div class="mt-[20px] mx-[15px]">
      <SingleLineInput v-model="username" :max-length="MAX_USER_NAME_LEN"></SingleLineInput>
      <p
        class="font-normal text-[length:10px] leading-[12px] text-[color:var(--text-3)] mt-[4px] px-[12px] break-words hyphens-auto"
      >
        {{ t("edit_nickname_tips") }}
      </p>
      <p
        v-if="user_info?.audit_username"
        class="font-normal text-[length:10px] leading-[12px] text-[color:var(--text-3)] mt-[4px] px-[12px] break-words hyphens-auto"
      >
        {{ t("audit_username") }}: {{ user_info?.audit_username }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import SingleLineInput from "@/components/common/single-line-input/index.vue";
import { useRouter } from "vue-router";
import { useModifyInfo } from "@/api/user";
import { computed, ref, watch } from "vue";
import { useUser } from "@/store/user";
import { storeToRefs } from "pinia";

// configs
import { MAX_USER_NAME_LEN } from "@/configs/const";
import { report } from "packages/utils/tlog";

const { t } = useI18n();
const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { user_info } = storeToRefs(useUser());
const username = ref(user_info.value?.username || "");

watch(
  () => user_info.value?.username,
  (val) => {
    val && (username.value = val);
  },
);

// const checkUpdateTime = () => {
//   return user_info.info?.username_on * 1000 + (30 * 24 * 60 * 60 * 1000) < Date.now();
// };

const is_disabled = computed(() => !username.value || username.value === user_info.value?.username);

const { mutateAsync: modify } = useModifyInfo();
const onSave = async () => {
  if (!username.value) return;
  report.standalonesite_nickname_change_finish_btn.cm_click({ sig: username.value });
  if (!user_info.value) return console.error("user info is empty");
  await modify({
    username: username.value,
    remark: user_info.value?.remark,
    avatar: user_info.value?.avatar,
  });
  handleRouterBack();
};

report.standalonesite_nicknamesettingpage.cm_vshow();
</script>

<style lang="scss" scoped></style>
