<template>
  <div
    class="w-full min-h-screen bg-[color:var(--fill-3)] mx-auto pt-[44px] pb-[30px] box-border relative z-[1]"
  >
    <Head
      :title="t('creator_hub')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>
    <div
      :style="{ backgroundImage: `url(${banner})` }"
      class="relative h-[89px] bg-cover bg-center grid grid-cols-1 px-[16px]"
    >
      <div v-if="creatorhub_user" class="flex items-center justify-between gap-[30px]">
        <div class="flex flex-col overflow-hidden">
          <div class="text-[16px] font-[Inter] font-[700] leading-[19px] text-white truncate">
            {{ creatorhub_user.user_name }}
          </div>
          <div class="text-[11px] font-[Inter] font-[400] leading-[13px] text-[--color-3] mt-[2px]">
            {{ creatorhub_user.email }}
          </div>
          <div class="mt-[5px] flex">
            <template v-for="item in channel_list" :key="item">
              <div
                class="flex items-center justify-center w-[24px] h-[24px] rounded-full bg-[var(--color-white-20)] mr-[12px] last-of-type:mr-0"
              >
                <SvgIcon
                  :name="item"
                  color="var(--color-white)"
                  class="h-[15px] w-[15px]"
                ></SvgIcon>
              </div>
            </template>
          </div>
        </div>
        <div
          class="flex items-center justify-center relative w-[20px] h-[20px] rounded-full bg-[var(--color-white-10)] cursor-pointer ml-[12px] flex-none"
          @click="handleRouterToCreatorHubBind"
        >
          <i class="absolute-center"></i>
          <SvgIcon name="icon-switch2" class="inline-block w-[12px] h-[10px]"></SvgIcon>
        </div>
      </div>
      <div v-else-if="show_bind" class="flex items-center justify-between">
        <div class="text-white text-[12px] leading-[14px] font-[400]">
          {{ t("link_creatorhub_tips") }}
        </div>
        <Btns :text="t('bind')" type="primary" @click="handleRouterToCreatorHubBind"></Btns>
      </div>
    </div>
    <div class="px-[12px]">
      <CreatorHubRecentEvents />
      <div class="mt-[20px]">
        <div class="flex items-center justify-between mb-[10px]">
          <div class="text-[18px] leading-[22px] font-[700] text-[color:var(--text-1)]">
            {{ t("my_submissions") }}
          </div>
          <div v-if="is_account_normal" class="flex items-center gap-[6px]">
            <div class="text-[11px] font-[400] font-[Inter] text-[color:var(--color-7)]">
              {{ t("auto_sync") }}
            </div>
            <Switch
              :checked="sync_status"
              :disabled="async_status_loading"
              @update:checked="onChangeAutoSync"
            />
          </div>
        </div>
      </div>
    </div>
    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="true"
      :finished_visible="true"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <ul class="px-[12px]">
        <li v-for="item in list" :key="item.work_content.work_id" class="mt-[12px]">
          <div
            class="p-[16px] relative z-10 overflow-hidden bg-[var(--fill-0)] flex flex-col gap-[8px] pb-[12px]"
          >
            <div class="flex items-center justify-between">
              <span class="text-[12px] font-[400] text-[color:var(--color-7)]">
                {{ formatUnRealseTime(item.work_content.work_publish_time) }} ·
                {{ t("published_on", [renderPlatform(item.post_content.platform)]) }}
              </span>
              <Btns
                v-if="!item.is_published_post"
                v-click-interceptor.need_login.mute.sign_privacy.check_user_adult="
                  () => handlePost(item)
                "
                :text="t('one_click_post')"
                type="primary"
              ></Btns>
              <Btns
                v-else
                v-click-interceptor.need_login.mute.sign_privacy.check_user_adult="
                  () => handlePost(item)
                "
                :text="t('repost')"
                type="default"
                class="border-[var(--line-1)] border-[1px]"
              ></Btns>
            </div>
            <div
              v-if="item.post_content.title"
              class="font-[700] text-[color:var(--text-1)] text-[length:16px] leading-[20px] mb-[4px] line-clamp-2"
            >
              {{ item.post_content.title }}
            </div>

            <div
              v-if="item.post_content.content_summary"
              class="text-[color:var(--text-3)] text-[length:13px] leading-[16px] line-clamp-3 whitespace-normal break-words"
            >
              <div v-safe-html="item.post_content.content_summary" class="inline mr-[8px]"></div>
            </div>

            <Medias
              :img-list="item.post_content.pic_urls"
              :type="item.post_content.pic_urls.length ? 'image' : 'video'"
              :ext_info="item.post_content.ext_info"
            />

            <img :src="item_bottom_line" class="w-full" />
          </div>
        </li>
      </ul>
      <template #empty>
        <Empty class="mt-[50px] !pl-[20px] !pr-[20px]" :text="empty_text"></Empty>
      </template>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useRouter } from "vue-router";

import { useInfiniteList } from "@/composables/use-infinite-list";
import { computed, watch } from "vue";
import { t } from "@/locales";
import banner from "@/assets/imgs/creatorhub/banner.png";
import { useCreatorHubStore } from "@/store/creatorhub";
import Btns from "@/components/common/btns/index.vue";
import { Routes, RoutesName } from "@/router/routes";
import { storeToRefs } from "pinia";
import {
  ChannelType,
  CreatorHubAccountStatus,
  SubmissionItem,
  useGetMySubmission,
} from "@/api/creatorhub";
import { Switch } from "@/components/ui/switch";
import item_bottom_line from "@/assets/imgs/creatorhub/item-bottom-line.png";
import Empty from "@/components/common/nodata.vue";
import Medias from "@/components/common/medias/index.vue";
import { useDialog } from "@/components/ui/dialog";
import { PlatId, PopCallbackValue, Status } from "packages/types/common";
import { getTikTokVideoData, getTikTokVideoId, safeParse, sleep } from "packages/utils/tools";
import { Platform } from "packages/types/common";
import { useComposePostStore } from "@/store/post/compose";
import { useToast } from "@/components/ui/toast";
import { useGetVideoInfoByURL } from "@/api/post";
import { ComposeContentType } from "packages/types/content";
import { formatUnRealseTime } from "@/utils/str";
import CreatorHubRecentEvents from "@/components/creatorhub/recent-events.vue";
import { useUser } from "@/store/user";

const { show: showDialog } = useDialog();
const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const user_store = useUser();
const compose_store = useComposePostStore();

const { creatorhub_user, sync_status, async_status_loading, creatorhub_user_loading } =
  storeToRefs(useCreatorHubStore());
const { handleChangeSyncStatus } = useCreatorHubStore();

const handleRouterToCreatorHubBind = () => {
  router.push(Routes.CREATOR_HUB_BIND);
};

const show_bind = computed(() => {
  return (
    !creatorhub_user.value &&
    !creatorhub_user_loading.value &&
    !user_store.loading &&
    !user_store.is_checking
  );
});

const { empty, finished, list, load, loading, reset } = useInfiniteList({
  queryFn: async ({ limit, next_page_cursor }) => {
    const res = await useGetMySubmission.run({ limit, next_idx: +(next_page_cursor || 0) });
    return {
      list: res.list,
      page_info: { is_finish: res.next_idx == 0, next_page_cursor: res.next_idx + "" },
    };
  },
  item_key: (i) => i.work_content.work_id,
  immediate: false,
});

const empty_text = computed(() => {
  if (!creatorhub_user.value) return t("creatorhub_not_bind_list_tips");
  if (creatorhub_user.value.status === CreatorHubAccountStatus.Normal)
    return t("there_is_not_anything");
  const error = t(`creatorhub_account_status_${creatorhub_user.value.status}`);
  if (error.startsWith("creatorhub_account_status_")) return t("api_code_1200310");
  return error;
});

const onChangeAutoSync = (v: boolean) => {
  if (v === false) return handleChangeSyncStatus(v);
  showDialog({
    title: t("turn_on_auto_sync_title"),
    content: t("turn_on_auto_sync_content"),
    confirm_text: t("turn_on"),
    cancel_text: t("next_time"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        handleChangeSyncStatus(true);
      }
      close();
    },
  });
};

/** 账号是否正常 */
const is_account_normal = computed(
  () =>
    !!creatorhub_user.value &&
    creatorhub_user.value.status === CreatorHubAccountStatus.Normal &&
    !creatorhub_user.value.is_freezed,
);

watch(
  () => is_account_normal.value,
  () => {
    if (is_account_normal.value) reset();
  },
  { immediate: true },
);

const renderPlatform = (platform: string) => {
  return (
    {
      tiktok: "Tiktok",
      youtube: "Youtube",
      youtubeshort: "Youtube",
      twitter: "X",
    }[platform] || platform
  );
};

const getChannelIcon = (channel: ChannelType) => {
  const config: Record<ChannelType, string> = {
    [ChannelType.Facebook]: "icon-facebook",
    [ChannelType.FacebookLive]: "icon-facebook",
    [ChannelType.TikTok]: "icon-dy",
    [ChannelType.Twitch]: "icon-lt",
    [ChannelType.Twitter]: "icon-x",
    [ChannelType.YouTube]: "icon-youtube",
    [ChannelType.YouTubeShorts]: "icon-youtube",
    [ChannelType.Unknown]: "",
    [ChannelType.Instagram]: "icon-instagram",
    [ChannelType.Chzzk]: "icon-chzzk",
    [ChannelType.Soop]: "icon-soop",
    [ChannelType.YouTubeLive]: "icon-youtube",
    [ChannelType.TikTokLive]: "icon-dy",
    [ChannelType.Trovo]: "icon-trovo",
    [ChannelType.Pixiv]: "icon-p",
  };
  return config[channel] || "";
};

const channel_list = computed(() => {
  return [
    ...new Set(
      [...(creatorhub_user.value?.third_channels ?? [])]
        .map((i) => getChannelIcon(i.channel_type))
        .filter((i) => i),
    ),
  ]
    .map((i, j, all) => {
      return j === 4 && all.length > 5 ? "icon-more" : i;
    })
    .slice(0, 5);
});

const { show: toast } = useToast();

/** 等待帖子发布页 Plat 初始化 */
const waitPlatInit = async () => {
  await waitCondition({
    condition: () => compose_store.state.compose_params.plate_id !== 0,
    interval: 50,
    timeout: 10000,
  });
};

/** 发布帖子 */
const handlePost = async (item: SubmissionItem) => {
  const ext_info = safeParse<{ platform: Platform; video_id: string; video_url: string }>(
    item.post_content.ext_info,
  );
  const is_video_post = !!(ext_info?.platform && ext_info.video_id && ext_info.video_url);
  const is_images_post = !is_video_post && item.post_content.pic_urls.length > 0;

  // 视频贴
  if (is_video_post) {
    const { platform, video_url } = ext_info;
    if (![Platform.tiktok, Platform.youtube, Platform.youtubeshort].includes(platform)) {
      toast({ text: t("error_video_platform_parse_tips"), type: "error" });
      return;
    }
    try {
      const fe_video_parse_info = await (platform === Platform.tiktok
        ? getTikTokVideoData(video_url).then((json) => ({
            video_id: getTikTokVideoId(video_url) || "",
            video_title: json.title,
            video_desc: "",
            video_cover: json.thumbnail_url,
            platform: Platform.tiktok,
          }))
        : useGetVideoInfoByURL.run({ video_url, platform: Platform.youtube }));

      // 忽略视频描述
      fe_video_parse_info.video_desc = "";
      console.log("[fe_video_parse_info]", fe_video_parse_info);

      await router.push({ name: RoutesName.POST_COMPOSE, query: { plate_type: PlatId.outpost } });
      await waitPlatInit();

      compose_store.onChangeType(ComposeContentType.video, {
        silent: true,
        hide_emoji_panel: false,
      });
      const active_content = compose_store.onGetActiveContent();
      active_content.fe_video_parse_info = fe_video_parse_info;
      active_content.fe_video_parse_status = Status.success;

      active_content.title = fe_video_parse_info.video_title;
      active_content.ext_info = JSON.stringify([fe_video_parse_info]);
      active_content.content = fe_video_parse_info.video_desc;

      compose_store.state.compose_params.tags = [Number(item.tag_id)];
      compose_store.state.compose_params.ch_work_id = Number(item.work_content.work_id);

      compose_store.onSetActiveContentItem(active_content);
      compose_store.onEidtorInsertHtml(active_content.content);
    } catch (error) {
      toast({ text: t("error_video_platform_parse_tips"), type: "error" });
    }
    return;
  }

  // 图片贴
  if (is_images_post) {
    await router.push({ name: RoutesName.POST_COMPOSE, query: { plate_type: PlatId.nikkeart } });
    await waitPlatInit();

    compose_store.onChangeType(ComposeContentType.image, { silent: true, hide_emoji_panel: false });
    const active_content = compose_store.onGetActiveContent();

    active_content.title = item.post_content.title;
    active_content.content = item.post_content.content;
    active_content.pic_urls = item.post_content.pic_urls;

    compose_store.state.compose_params.tags = [Number(item.tag_id)];
    compose_store.state.compose_params.ch_work_id = Number(item.work_content.work_id);

    compose_store.onSetActiveContentItem(active_content);
    compose_store.onEidtorInsertHtml(active_content.content);

    return;
  }

  // 文字贴

  await router.push({ name: RoutesName.POST_COMPOSE, query: { plate_type: PlatId.outpost } });
  await waitPlatInit();

  compose_store.onChangeType(ComposeContentType.richtext, {
    silent: true,
    hide_emoji_panel: false,
  });
  const active_content = compose_store.onGetActiveContent();

  active_content.title = item.post_content.title;
  active_content.content = item.post_content.content;
  active_content.pic_urls = item.post_content.pic_urls;

  compose_store.state.compose_params.tags = [Number(item.tag_id)];
  compose_store.state.compose_params.ch_work_id = Number(item.work_content.work_id);

  compose_store.onSetActiveContentItem(active_content);
  compose_store.onEidtorInsertHtml(active_content.content);
};

const waitCondition = async (config: {
  condition: () => boolean;
  interval: number;
  timeout: number;
}) => {
  const start_time = Date.now();
  while (Date.now() - start_time < config.timeout) {
    if (config.condition()) {
      console.log(`wait condition success in ${Date.now() - start_time}ms`);
      return true;
    }
    await sleep(config.interval);
  }
  return false;
};
</script>
