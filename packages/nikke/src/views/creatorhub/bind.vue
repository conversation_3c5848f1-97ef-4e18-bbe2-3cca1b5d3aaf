<template>
  <div
    class="w-full h-screen bg-[color:var(--fill-3)] mx-auto pt-[44px] box-border relative z-[1] flex flex-col"
  >
    <Head
      :title="t('creator_hub')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>
    <iframe
      ref="iframe"
      class="flex-1 w-full"
      :class="{
        'pointer-events-none': isPending,
      }"
      :src="url_login"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { useRouter } from "vue-router";
import Head from "@/components/common/head/index.vue";
import { computed, ref } from "vue";
import { ENV_PROD } from "packages/configs/env";
import { getEnv } from "packages/utils/tools";
import { getStandardizedLang } from "packages/utils/standard";
import { Routes } from "@/router/routes";
import { useBindCreatorHubAccount } from "@/api/creatorhub";
import { useCreatorHubStore } from "@/store/creatorhub";
import { useDialog } from "@/components/ui/dialog";
import { PopCallbackValue } from "packages/types/common";
import { useEventListener } from "@vueuse/core";
import { useWebCredential } from "@/composables/use-webcredential";
import { CREATORHUB_LANG_MAP, CREATORHUB_ORIGIN_MAP } from "packages/configs/creatorhub";
import { useToast } from "@/components/ui/toast";
import { CODE_ALL_CONFIGS, CODE_MESSAGE_MAP } from "packages/configs/code";

const router = useRouter();
const { show: toast } = useToast();

const handleRouterBack = () => {
  router.back();
};

const iframe = ref<HTMLIFrameElement>();

const origin =
  CREATORHUB_ORIGIN_MAP[getEnv() as keyof typeof CREATORHUB_ORIGIN_MAP] ||
  CREATORHUB_ORIGIN_MAP[ENV_PROD];

const creatorhub_lang = computed(() => {
  return (
    CREATORHUB_LANG_MAP[getStandardizedLang() as keyof typeof CREATORHUB_LANG_MAP] ||
    CREATORHUB_LANG_MAP["en"]
  );
});

const { refetchCreatorHubUser } = useCreatorHubStore();
const { isPending, mutateAsync: bind } = useBindCreatorHubAccount();

const { show: showDialog } = useDialog();
const { openUrlWithAuth } = useWebCredential();

const url_login = computed(
  () => `${origin}/h5/signin?game_id=16&lang=${creatorhub_lang.value}&embed=blablalink`,
);
const url_register = computed(() => `${origin}/h5/signup?game_id=16&lang=${creatorhub_lang.value}`);
const url_forgot = computed(() => `${origin}/h5/forgot?game_id=16&lang=${creatorhub_lang.value}`);

useEventListener(window, "message", async (e) => {
  if (e.origin !== origin) return;
  const data = e.data as CreatorHubIframeEvent;

  if (data.type === "login") {
    const info = data.data;
    // 调用后端的登录接口
    try {
      await bind({ uid: info.uid, token: info.token });
    } catch (error) {
      const err = error as Error | { code: number };
      if ("code" in err) {
        // 这几个错误代表：账号未完成认证，需要引导用户去完成认证
        const codes_need_check = [
          CODE_ALL_CONFIGS.CreatorHubAccountRejected,
          CODE_ALL_CONFIGS.CreatorHubAccountAuditing,
          CODE_ALL_CONFIGS.CreatorHubAccountNotRegistered,
        ];
        if (codes_need_check.includes(err.code)) {
          showDialog({
            title: t("notice"),
            content: t("creatorhub_account_bind_not_verify_tips"),
            confirm_text: t("confirm"),
            cancel_text: t("cancel"),
            async callback(options: { value: PopCallbackValue; close: () => void }) {
              const { value, close } = options;
              if (value === PopCallbackValue.confirm) {
                window.open(`${origin}/account/verify?game_id=16&lang=${creatorhub_lang.value}`);
              }
              close();
            },
          });
        } else {
          // 其他错误
          const message = CODE_MESSAGE_MAP[err.code];
          toast({
            text: !message || message === t(message) ? t("default_error_tips") : t(message),
            type: "error",
          });
        }
      }
      return;
    }
    await refetchCreatorHubUser();
    router.replace(Routes.CREATOR_HUB);
    return;
  }

  if (data.type === "register") {
    showDialog({
      title: t("notice"),
      content: t("goto_creatorhub_registre_page_tips", [t("creator_hub")]),
      confirm_text: t("confirm"),
      cancel_text: t("cancel"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.confirm) {
          openUrlWithAuth(url_register.value, "_blank");
        }
        close();
      },
    });
    return;
  }

  if (data.type === "forgot") {
    showDialog({
      title: t("notice"),
      content: t("goto_creatorhub_forgot_page_tips", [t("creator_hub")]),
      confirm_text: t("confirm"),
      cancel_text: t("cancel"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.confirm) {
          openUrlWithAuth(url_forgot.value, "_blank");
        }
        close();
      },
    });
    return;
  }
});

type CreatorHubIframeEvent =
  | {
      type: "login";
      data: { uid: string; token: string; channelid: string };
    }
  | { type: "register" }
  | { type: "forgot" };
</script>

<style scoped lang="scss"></style>
