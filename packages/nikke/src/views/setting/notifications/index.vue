<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('notification_setting')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div class="mt-[20px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("related_to_me") }}
      </h2>
      <SwitchItem
        v-for="(item, index) in relative_mes"
        :key="'post_' + index"
        :title="item.desc"
        :item="item"
        :default-switch="!!item.on"
        @change="onSwitch"
      ></SwitchItem>
    </div>

    <div class="mt-[30px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("notifications") }}
      </h2>
      <SwitchItem
        v-for="(item, index) in notifications"
        :key="'notifications_' + index"
        :title="item.desc"
        :item="item"
        :default-switch="!!item.on"
        @change="onSwitch"
      ></SwitchItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import SwitchItem from "@/components/common/switch-item/index.vue";
import { SettingItem, NotificationsType } from "packages/types/setting";
import { computed } from "vue";
import { useSetting } from "@/store/setting";
const store = useSetting();
const router = useRouter();
const { t } = useI18n();
const handleRouterBack = () => {
  router.back();
};
const relative_me_keys = ["msg_comment_notify", "msg_like_notify", "msg_follow_notify"];
const notification_keys = ["msg_system_notify", "msg_activity_notify"];
const list = computed(() => {
  return Object.keys(store.setting.notifications).map((key) => {
    const name = key as keyof NotificationsType;
    return { name, on: store.setting.notifications[name] || 0, desc: t(`${name}_desc`) };
  });
});

const relative_mes = computed(() =>
  list.value.filter((item) => relative_me_keys.includes(item.name)),
);
const notifications = computed(() =>
  list.value.filter((item) => notification_keys.includes(item.name)),
);

const onSwitch = async (data: SettingItem) => {
  const idx = list.value.findIndex((item) => item.name === data.name);
  if (idx === -1) {
    return;
  }
  const val = data.on ? 0 : 1;
  list.value[idx].on = val;
  store.updateSetting("notifications", data.name, val);
};
</script>

<style lang="scss" scoped></style>
