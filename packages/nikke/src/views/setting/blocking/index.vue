<template>
  <div class="w-full min-h-screen bg-[color:var(--fill-3)] mx-auto pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('blocking_setting_title')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>
    <div
      class="flex items-center gap-[8px] text-[color:var(--text-2)] text-[11px] px-[13px] py-[10px] bg-white"
    >
      <SvgIcon name="icon-info" class="w-[13px] h-[13px] !text-[color:var(--text-2)] shrink-0" />
      <div v-fontfix class="leading-[13px]">{{ t("blocking_setting_desc") }}</div>
    </div>

    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <ul>
        <li v-for="(item, index) in list" :key="`follower_` + index" class="mt-[12px]">
          <BlockingUserItem
            :data="item.user_info"
            :blacking_on="item.blacking_on"
            @unblock="reset"
          ></BlockingUserItem>
        </li>
      </ul>
      <template #empty>
        <Empty class="mt-[150px]" :text="t('no_blocked_user')"></Empty>
      </template>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import Head from "@/components/common/head/index.vue";
import BlockingUserItem from "@/components/common/user-block/user-item.vue";
import { useRouter } from "vue-router";
import { useGetBlackUserList } from "@/api/user";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { watch } from "vue";
import { useUserCenter } from "@/composables/use-user-center";
import { t } from "@/locales";
import SvgIcon from "@/components/common/svg-icon.vue";
import Empty from "@/components/common/nodata.vue";
const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { user_info, is_self } = useUserCenter();

const { empty, finished, list, load, loading, reset } = useInfiniteList({
  queryFn: async ({ limit, next_page_cursor }) => {
    return useGetBlackUserList.run({
      limit,
      next_page_cursor,
    });
  },
  item_key: "id",
  immediate: false,
  page_size: window.innerHeight < 800 ? 10 : 20,
});

watch(
  () => user_info.value?.intl_openid,
  () => {
    if (user_info.value?.intl_openid) load();
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped></style>
