<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('private_setting')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div class="mt-[20px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("personal_setting") }}
      </h2>
      <SwitchItem
        v-for="(item, index) in shows"
        :key="'private_' + index"
        :title="item.desc"
        :item="item"
        :default-switch="!!item.on"
        @change="onSwitch"
      ></SwitchItem>
    </div>

    <div v-if="ads.length > 0" class="mt-[30px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("ads_setting") }}
      </h2>
      <SwitchItem
        v-for="(item, index) in ads"
        :key="'ads_' + index"
        :title="item.desc"
        :item="item"
        :default-switch="!!item.on"
        @change="onSwitch"
      ></SwitchItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import SwitchItem from "@/components/common/switch-item/index.vue";
import { ShowType, SettingItem } from "packages/types/setting";
import { useSetting } from "@/store/setting";
const store = useSetting();
const router = useRouter();
const { t } = useI18n();
const handleRouterBack = () => {
  router.back();
};
const show_keys = [
  "show_my_posts",
  "show_my_comment",
  "show_my_collection",
  "show_my_follow",
  "show_my_fans",
  "show_my_game_card",
];
const ads_keys = ["receive_tweet_email", "receive_tweet_emial"];
const list = computed(() => {
  return Object.keys(store.setting.privacy_shows).map((key) => {
    const name = key as keyof ShowType;
    return { name, on: store.setting.privacy_shows[name] || 0, desc: t(name) };
  });
});
console.log("private", list, store.setting);
const shows = computed(() => list.value.filter((item) => show_keys.includes(item.name)));
const ads = computed(() => list.value.filter((item) => ads_keys.includes(item.name)));
const onSwitch = async (data: SettingItem) => {
  const idx = list.value.findIndex((item) => item.name === data.name);
  if (idx === -1) {
    return;
  }
  const val = data.on ? 0 : 1;
  list.value[idx].on = val;
  store.updateSetting("privacy_shows", data.name, val);
};
</script>

<style lang="scss" scoped></style>
