<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('account_setting')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div class="mt-[12px] px-[12px]">
      <SettingItem
        v-for="item in list"
        :key="item.title"
        :title="item.title"
        @user-click="item.event"
      ></SettingItem>

      <Button v-if="!isGameLogin()" class="mt-[20px]" type="primary" @click="onLogout">
        {{ t("logout") }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import SettingItem from "@/components/common/setting-item/index.vue";
import Button from "@/components/ui/button/index.vue";

// utils
import { useRouter } from "vue-router";
import { Routes, RoutesName } from "@/router/routes";
import { t } from "@/locales";
import { isGameLogin } from "packages/utils/tools";
import { getToLoginQuery } from "packages/utils/login";
import { computed } from "vue";
import { useLaboratory } from "@/store/laboratory";

const laboratory_store = useLaboratory();
const router = useRouter();

laboratory_store.checkLaboratoryEntranceVisible();

const handleRouterBack = () => {
  router.back();
};

const list = computed(() =>
  [
    {
      title: t("account_management"),
      event: () => {
        router.push({
          name: RoutesName.SETTING_ACCOUNT,
        });
      },
      visible: true,
    },
    {
      title: t("private_setting"),
      event: () => {
        router.push({
          name: RoutesName.SETTING_PRIVATE,
        });
      },
      visible: true,
    },
    {
      title: t("blocking_setting"),
      event: () => {
        router.push({
          name: RoutesName.SETTING_BLOCKING,
        });
      },
      visible: true,
    },
    {
      title: t("notification_setting"),
      event: () => {
        router.push({
          name: RoutesName.SETTING_NOTIFICATIONS,
        });
      },
      visible: true,
    },
    {
      title: t("language_setting"),
      event: () => {
        router.push({
          name: RoutesName.SETTING_LANGUAGES,
        });
      },
      visible: true,
    },
    {
      title: t("about", [t("app_name")]),
      event: () => {
        router.push({
          name: RoutesName.SETTING_ABOUT,
        });
      },
      visible: true,
    },
    {
      title: "Laboratory",
      event: () => {
        router.push({
          name: RoutesName.LABORATORY,
        });
      },
      visible: laboratory_store.laboratory_entrance_visible,
    },
  ].filter((item) => item.visible),
);

const onLogout = () => {
  router.push({
    name: RoutesName.LOGOUT,
    query: getToLoginQuery({
      to: Routes.HOME,
      back_to: Routes.HOME,
    }),
  });
};
</script>

<style lang="scss" scoped></style>
