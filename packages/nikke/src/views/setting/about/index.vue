<template>
  <div class="w-full mx-auto pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('about', [t('app_name')])"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div
      class="flex flex-col relative z-0 overflow-hidden bg-[var(--fill-3)] items-center h-[175px] pt-[32px] box-border"
    >
      <div class="absolute w-full h-[235px] top-[7px] left-0 -z-[1] pointer-events-none">
        <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
      </div>
      <i
        class="absolute top-[7px] left-0 w-full h-[235px] z-[-1] bg-[image:var(--linear-gradient-1)] pointer-events-none"
      ></i>
      <img src="/apple-touch-icon.png" class="w-[66px] h-[66px] object-cover rounded-[16px]" />
      <!-- <span class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)] mt-[8px]"
        >NIKKE</span
      > -->
      <span
        class="font-normal text-[length:10px] leading-[12px] text-[color:var(--text-1)] mt-[6px]"
      >
        <EMAIL>
      </span>
    </div>

    <div>
      <SettingItem
        v-for="item in list"
        :key="item.title"
        :title="item.title"
        @user-click="item.event"
      ></SettingItem>
    </div>

    <div
      class="mt-[12px] pt-[16px] px-[12px] border-t-[0.5px] border-solid border-[color:var(--line-1)] w-full overflow-x-auto"
    >
      <div v-for="(item, index) in officialList" :key="index" class="flex items-start p-[10px]">
        <img :src="item.img" class="w-[20px] object-contain" />
        <div class="flex flex-col ml-[20px] gap-[12px] items-stretch flex-1 overflow-hidden">
          <span
            v-for="i of [item.channel1, item.channel2]"
            :key="i.text"
            class="whitespace-nowrap text-center cursor-pointer font-medium text-[length:11px] leading-[14px] text-[var(--brand-1)] rounded-[30px] border-[length:0.5px] border-solid border-[color:var(--brand-1)] py-[3px] px-[14px] truncate"
            @click="onClickChannel(i)"
          >
            {{ i.text }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import Head from "@/components/common/head/index.vue";
import SettingItem from "@/components/common/setting-item/index.vue";
import iconDiscord from "@/assets/imgs/logo/icon-discord.png";
import iconX from "@/assets/imgs/logo/icon-x.png";
import iconFacebook from "@/assets/imgs/logo/icon-facebook.png";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { computed } from "vue";
import { useLang } from "@/composables/use-lang";
import {
  GET_LIP_EULA,
  GET_BLABLA_EULA,
  GET_LIP_PRIVACY_POLICY,
  GET_NIKKE_PRIVACY_POLICY,
  GET_BLABLA_COMMUNITY_GUIDELINES,
} from "packages/configs/compliance";
import { getStandardizedLang } from "packages/utils/standard";
import { useWebCredential } from "@/composables/use-webcredential";
const router = useRouter();
const { t } = useI18n();
const handleRouterBack = () => {
  router.back();
};

const { openUrlWithAuth } = useWebCredential();

const list = [
  {
    title: t("terms_of_service", [t("app_name")]),
    event: () => {
      openUrlWithAuth(GET_BLABLA_EULA(getStandardizedLang()), "_blank");
    },
  },
  {
    title: t("privacy_policy", [t("app_name")]),
    event: () => {
      openUrlWithAuth(GET_NIKKE_PRIVACY_POLICY(), "_blank");
    },
  },
  {
    title: t("li_pass_terms_of_service"),
    event: () => {
      openUrlWithAuth(GET_LIP_EULA(), "_blank");
    },
  },
  {
    title: t("li_pass_privacy_policy"),
    event: () => {
      openUrlWithAuth(GET_LIP_PRIVACY_POLICY(), "_blank");
    },
  },
  {
    title: t("communityguidelines"),
    event: () => {
      openUrlWithAuth(GET_BLABLA_COMMUNITY_GUIDELINES(getStandardizedLang()), "_blank");
    },
  },
];

const { current_lang } = useLang();

const officialList = computed(() => [
  {
    img: iconDiscord,
    channel1: {
      text: t("nikke_channel"),
      href: byLang({
        en: "https://discord.gg/nikke-en",
        "zh-TW": "https://discord.gg/nikke-en",
        zh: "https://discord.gg/nikke-en",
        ja: "http://discord.gg/nikke-jp",
        ko: "https://discord.gg/nikke-en",
      }),
    },
    channel2: {
      text: t("li_pass_channel"),
      href: "https://discord.gg/jPsa5yeuhE",
    },
  },
  {
    img: iconX,
    channel1: {
      text: t("nikke_official"),
      href: byLang({
        en: "https://twitter.com/NIKKE_en",
        "zh-TW": "https://twitter.com/NIKKE_en",
        zh: "https://twitter.com/NIKKE_en",
        ja: "https://twitter.com/NIKKE_japan",
        ko: "https://twitter.com/NIKKE_kr",
      }),
    },
    channel2: { text: t("li_pass_official"), href: "https://twitter.com/LIPASS_en" },
  },
  {
    img: iconFacebook,
    channel1: {
      text: t("nikke_official_group"),
      href: byLang({
        en: "https://www.facebook.com/NIKKE.Global",
        "zh-TW": "https://www.facebook.com/TW.NIKKE/",
        zh: "https://www.facebook.com/TW.NIKKE/",
        ja: "https://www.facebook.com/NIKKE.Global",
        ko: "https://www.facebook.com/nikke.kr.official",
      }),
    },
    channel2: {
      text: t("li_pass_official_group"),
      href: "https://www.facebook.com/LevelInfinitePass/",
    },
  },
]);

const byLang = (data: Record<any, string>): string => {
  return data[current_lang.value?.value];
};

const onClickChannel = (item: { text: string; href: string }) => {
  if (item.href) openUrlWithAuth(item.href, "_blank");
};
</script>

<style lang="scss" scoped></style>
