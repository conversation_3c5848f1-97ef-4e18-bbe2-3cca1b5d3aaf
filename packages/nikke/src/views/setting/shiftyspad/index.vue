<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('shiftyspad_private_setting')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>
    <Spinner v-if="is_fetching"></Spinner>
    <div class="px-[12px] mt-[12px]">
      <SwitchItem
        :title="t('show_my_game_card')"
        :disable="is_fetching"
        :item="{
          name: 'show_my_game_card',
          on: setting.privacy_shows.show_my_game_card,
        }"
        :default-switch="Boolean(setting.privacy_shows.show_my_game_card)"
        @change="changeSwtichSetting"
      ></SwitchItem>
    </div>
    <div class="mt-[12px] px-[12px]">
      <div
        v-for="item in sub_settings_list"
        :key="item.name"
        class="flex items-center justify-between py-[14px] pl-[20px] pr-[12px] bg-[var(--op-fill-white)]"
      >
        <span class="font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)]">
          {{ item.desc }}
        </span>
        <DropdownNormal
          :list="dropdown_options"
          :active="setting.shiftyspad_settings[item.name]"
          @change="(v: any) => changeSetting(item, v)"
        >
          <template #trigger>
            <!-- @vue-ignore -->
            <SelectHead
              class="h-[28px] mr-[5px] max-w-[180px]"
              :type="1"
              :border="false"
              :text="option_keys[setting.shiftyspad_settings[item.name]]"
            />
          </template>
        </DropdownNormal>
      </div>
    </div>
    <div class="px-[12px] mt-[12px]">
      <SwitchItem
        :title="t('allow_friend_request_via_game_card')"
        :disable="is_fetching"
        :item="{
          name: 'allow_friend_request_via_game_card',
          on: setting.privacy_shows.allow_friend_request_via_game_card,
        }"
        :default-switch="Boolean(setting.privacy_shows.allow_friend_request_via_game_card)"
        @change="changeSwtichSetting"
      ></SwitchItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ShiftyspadSetting, SettingItem } from "packages/types/setting";
import { ShiftyspadSettingValue } from "packages/types/setting";

import { useSetting } from "@/store/setting";

import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";

// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import SwitchItem from "@/components/common/switch-item/index.vue";
import Head from "@/components/common/head/index.vue";

const settingStore = useSetting();
const router = useRouter();
const { t } = useI18n();
const { setting } = storeToRefs(settingStore);

type MultiOptionFilter = Exclude<keyof ShiftyspadSetting, "allow_friend_request_via_game_card">;

const handleRouterBack = () => {
  router.back();
};
const show_keys: Record<MultiOptionFilter, string> = {
  show_daily_info: t("daily_mission"),
  show_outpost_info: t("main_outpost"),
  show_resource_info: t("main_material"),
  show_union_info: t("myunion"),
  show_nikke_info: t("nikke_list_tab_player"),
};
const option_keys = {
  [ShiftyspadSettingValue.EveryoneVisible]: t("shiftyspad_private_setting_all"),
  [ShiftyspadSettingValue.AlliesVisible]: t("shiftyspad_private_setting_allies"),
  [ShiftyspadSettingValue.NoneVisible]: t("shiftyspad_private_setting_deny_all"),
  // 1.6
  // [ShiftyspadSettingValue.FriendsVisible]: t('shiftyspad_private_setting_friends'),
} as const;
const dropdown_options = Object.entries(option_keys).map((val) => ({
  name: val[1],
  value: Number(val[0]),
}));

const list = computed<SettingItem<ShiftyspadSetting>[]>(() => {
  return Object.keys(setting.value.shiftyspad_settings).map((key) => {
    const name = key as MultiOptionFilter;
    return {
      name,
      on: setting.value.shiftyspad_settings[name] as ShiftyspadSettingValue,
      desc: t("shiftyspad_settings_tips", [t(show_keys[name])]),
    };
  });
});
const sub_settings_list = computed(() =>
  list.value.filter((item) => Object.keys(show_keys).includes(item.name)),
);

const is_fetching = ref<boolean>(false);
const wrap = async (func: () => Promise<any>) => {
  if (is_fetching.value) return;
  try {
    is_fetching.value = true;
    await func();
  } finally {
    is_fetching.value = false;
  }
};

const changeSwtichSetting = (v: {
  name: "show_my_game_card" | "allow_friend_request_via_game_card";
  on: 0 | 1;
}) => {
  const { name, on } = v;
  wrap(async () => {
    await settingStore.updateSetting("privacy_shows", name, on === 0 ? 1 : 0);
  });
};

const changeSetting = async (data: SettingItem<ShiftyspadSetting>, val: { value: any }) => {
  const value = Number(val.value) as ShiftyspadSettingValue;
  const { on, name } = data;

  if (Number(on) === value) return;

  try {
    is_fetching.value = true;
    const origin_value = Object.assign({}, setting.value.shiftyspad_settings);
    const is_closed_before = Object.values(origin_value).every((val) => val === 0);
    const new_value = Object.assign(origin_value, {
      [name]: value,
    });
    const is_closed_after = Object.values(new_value).every((val) => val === 0);
    await settingStore.updateShiftyspadSetting(new_value);

    if (is_closed_after || is_closed_before) {
      await settingStore.refresh();
    }
  } finally {
    is_fetching.value = false;
  }
};
</script>

<style lang="scss" scoped></style>
