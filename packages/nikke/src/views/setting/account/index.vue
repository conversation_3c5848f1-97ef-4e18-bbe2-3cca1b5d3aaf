<template>
  <Loading v-if="is_loading" />
  <div
    v-else
    class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border"
  >
    <Head
      :title="t('account_management')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <div class="mt-[20px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("basic_info") }}
      </h2>

      <template v-if="is_lip_logined">
        <InfoInput
          v-if="!isGameLogin()"
          :title="t('my_account')"
          :placeholder="lip_ls_info?.channel_info?.account ?? '-'"
        ></InfoInput>
        <InfoInput :title="t('nickname')" :placeholder="user_name"></InfoInput>
        <InfoInput v-if="!isGameLogin()" :title="t('country')" :placeholder="country"></InfoInput>
      </template>

      <div
        v-else
        class="bg-[var(--op-fill-white)] flex items-center justify-between pl-[20px] pr-[12px] py-[12px] box-border cursor-pointer"
      >
        <div class="font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)]">
          {{ t("nickname") }}
        </div>
        <div class="flex items-center">
          <span
            class="font-normal text-[length:13px] leading-[16px] text-[color:var(--text-2)] mr-[6px]"
          >
            {{ getCurrentChannelUserName() ?? "" }}
          </span>
          <img
            :src="current_channel?.icon"
            class="w-[20px] h-[20px] object-contain flex-shrink-0"
          />
        </div>
      </div>
    </div>

    <div class="mt-[30px] px-[12px]">
      <h2
        class="font-bold text-[length:14px] leading-[16px] text-[color:var(--text-1)] px-[8px] mb-[12px]"
      >
        {{ t("account_links") }}
      </h2>
      <BindLinkItem
        v-for="item in bind_channel_list"
        :key="item.title"
        v-bind="item"
        @click="
          () => {
            clickBindAccount(item);
          }
        "
      ></BindLinkItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import Loading from "@/components/common/loading.vue";
import Head from "@/components/common/head/index.vue";
import InfoInput from "@/components/common/info-input/index.vue";
import BindLinkItem from "@/components/common/bind-link-item/index.vue";
import iconFacebook from "@/assets/imgs/logo/icon-facebook.png";
import iconApple from "@/assets/imgs/logo/icon-apple.png";
import iconGoogle from "@/assets/imgs/logo/icon-google.png";
import iconX from "@/assets/imgs/logo/icon-x.png";
import iconLine from "@/assets/imgs/logo/icon-line.png";
import iconLip from "@/assets/imgs/logo/icon-lip.png";
import gift from "@/assets/imgs/setting/gift.png";
import { useUser } from "@/store/user";
import { RoutesName } from "@/router/routes";
import { useGetBindPresent, useQueryUserAward } from "@/api/rewards";
import { useAccountSwitch } from "@/composables/use-bind-lip";
import { IntlChannel, INTL_RET_CODE } from "packages/types/intl";
import { useToast } from "@/components/ui/toast";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { isGameLogin, isSafari } from "packages/utils/tools";
import { getSpecialBrowserType } from "@tencent/pa-cms-utils";

const { t } = useI18n();
const { show } = useToast();
const { selectGameRole } = useBindRole({
  select_hint: t("select_role_to_get_reward"),
});
const router = useRouter();
const userStore = useUser();
const { user_info } = storeToRefs(userStore);

const {
  is_loading,
  pending_state,
  is_lip_binded,
  binded_account_list,
  user_login_data,
  lipLogin,
  lipBindThird,
  thirdLinkThird,
  thirdChannelLogin,
  thirdBindLip,
  getLipLSInfo,
  updateBindedListInfo,
  getCurrentChannel,
  getCurrentChannelUserName,
} = useAccountSwitch();
const handleRouterBack = () => {
  router.back();
};

userStore
  .checkLogin()
  .then(() => {
    if (!userStore.is_login) {
      router.push({
        name: RoutesName.LOGIN,
      });
    }
  })
  .catch(() => {
    router.push({
      name: RoutesName.LOGIN,
    });
  });

const lip_ls_info = getLipLSInfo();
const country = ref("-");
const user_name = computed(() => user_info?.value.username ?? "-");
import("@/static/region")
  .then((res) => {
    const region_map_data = res.default;
    const region = (lip_ls_info?.extra_json as any)?.get_status_rsp
      ?.region as keyof typeof region_map_data;
    const region_data = region_map_data[region];
    country.value = region_data?.en ?? "-";
  })
  .catch((err) => {
    console.error(`[load-country-data-failed]: ${err}`);
  });

type BindStatus = "linked" | "unlinked" | "bound";
type ChannelItem = {
  icon?: string;
  title?: string;
  type?: BindStatus;
  showGift?: boolean;
  channel: IntlChannel;
};
const linksList: ChannelItem[] = [
  {
    icon: iconLip,
    title: "LEVEL INFINITE PASS",
    channel: IntlChannel.Lip,
  },
  {
    icon: iconFacebook,
    title: "Facebook",
    channel: IntlChannel.Facebook,
  },
  {
    icon: iconApple,
    title: "Apple",
    channel: IntlChannel.Apple,
  },
  {
    icon: iconGoogle,
    title: "Google",
    channel: IntlChannel.Google,
  },
  {
    icon: iconX,
    title: "Twitter",
    channel: IntlChannel.Twitter,
  },
  {
    icon: iconLine,
    title: "Line",
    channel: IntlChannel.Line,
  },
];

const user_award_info = ref<null | {
  can_sent: boolean;
  is_sent: boolean;
}>(null);
const is_lip_logined = computed(() => current_channel.value?.channel === IntlChannel.Lip);
const current_channel = computed(() =>
  linksList.find((item) => item.channel === user_login_data.value?.channel_id),
);

const updateAwardInfo = () => {
  return useQueryUserAward.run({}).then((data) => {
    user_award_info.value = data;
    return data;
  });
};

Promise.all([updateAwardInfo(), pending_state]).then(([data]) => {
  const { can_sent = false, is_sent = false } = data;
  if (is_lip_binded.value && can_sent && !is_sent) {
    getPresent();
  }
});

const bind_channel_list = computed(() => {
  const get_type = (item: ChannelItem): BindStatus => {
    const binded_account = binded_account_list.value.find((val) => val.channelid === item.channel);
    const is_binded = Boolean(binded_account);
    const is_linked = !binded_account?.is_primary && is_binded;
    if (is_linked || is_binded) return "bound";
    return "unlinked";
  };
  return linksList
    .filter((item) => item.channel !== getCurrentChannel())
    .map((item) => {
      const is_received =
        get_type(item) === "bound" &&
        user_award_info.value?.is_sent &&
        item.channel === IntlChannel.Lip;
      return Object.assign({}, item, {
        type: get_type(item),
        showGift: item.channel === IntlChannel.Lip,
        giftImg: item.channel === IntlChannel.Lip ? gift : null,
        received: is_received,
      });
    });
});

const invokeThirdBind = async (channel: Exclude<IntlChannel, IntlChannel.Lip>) => {
  if (getSpecialBrowserType() || isGameLogin()) {
    return show({ text: t("browser_not_support"), type: "info" });
  }
  if (isSafari() && channel === IntlChannel.Twitter) {
    return show({ text: t("browser_not_support"), type: "info" });
  }
  const third_res = await thirdChannelLogin(channel);
  // 主动取消;
  if (third_res.ret === INTL_RET_CODE.USER_CANCEL_POPUP) {
    return;
  }
  if (current_channel.value?.channel === IntlChannel.Lip) {
    const res = await lipBindThird(channel, third_res);
    show({ text: res.msg, type: "success" });
  }
  if (current_channel.value?.channel !== IntlChannel.Lip) {
    const res = await thirdLinkThird(channel, third_res);
    show({ text: res.msg, type: "success" });
  }
  await updateBindedListInfo();
};
const clickBindAccount = (item: ChannelItem) => {
  if (item.type !== "unlinked") return;
  if (!is_lip_binded.value && item.channel !== IntlChannel.Lip) {
    return show({ text: t("bind_lip_first"), type: "info" });
  }
  switch (item.channel) {
    case IntlChannel.Lip: {
      bindLipAccount();
      break;
    }
    default: {
      invokeThirdBind(item.channel);
      break;
    }
  }
};

const getPresent = async () => {
  const role_info = await selectGameRole();
  try {
    await useGetBindPresent.run({ role_info });
    show({
      type: "success",
      text: t("receive_gitf_success"),
    });
    updateAwardInfo();
  } catch (err: any) {
    show({
      text: err?.message ?? err?.msg ?? "Error",
      type: "error",
    });
  }
};

const bindLipAccount = async () => {
  const lip_res = await lipLogin();
  const { channel_info } = lip_res;
  const res = await thirdBindLip({
    ...channel_info,
    uid: (channel_info as any).openid,
  });
  show({ text: res.msg, type: "success" });
  if (res.ret === 0) {
    getPresent();
  }
  await updateBindedListInfo();
  return show({ text: res.msg, type: "success" });
};
</script>

<style lang="scss" scoped></style>
