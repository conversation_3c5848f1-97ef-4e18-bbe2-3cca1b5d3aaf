<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('language_setting')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="router.back"
    >
      <template #icon>
        <Btns
          v-if="language_setting?.region_selection_valid"
          type="primary"
          :text="t('save')"
          @click="save"
        ></Btns>
        <Btns v-else type="disabled" :text="t('save')"></Btns>
      </template>
    </Head>

    <div class="m-[12px] mb-0">
      <LanguageSetting ref="language_setting" popup_position="bottom" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import Head from "@/components/common/head/index.vue";
import { useI18n } from "vue-i18n";
import LanguageSetting from "@/components/setting/language/LanguageSetting.vue";
import Btns from "@/components/common/btns/index.vue";

const router = useRouter();

const { t } = useI18n();

const language_setting = ref<InstanceType<typeof LanguageSetting>>();

const save = () => {
  language_setting.value?.save();
};
</script>

<style lang="scss" scoped></style>
