<template>
  <div class="min-h-screen bg-[var(--fill-0)]">
    <Head
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      title="Topic Management "
      @goback="router.back()"
    >
      <template #icon>
        <Btns type="primary" :text="t('Edit')"></Btns>
      </template>
    </Head>

    <div class="pt-[44px] m-[20px]">
      <div class="flex items-center text-[color:var(--text-1)] text-[length:14px] leading-[16px]">
        <span class="whitespace-nowrap font-bold">My Topics</span>
        <span class="ml-[9px] text-[color:var(--text-3)] text-[length:11px] line-clamp-1">
          Long press drag sequence
        </span>
      </div>

      <div class="mt-[12px]">
        <Cont :data="list"></Cont>
      </div>

      <div
        class="mt-[30px] flex items-center text-[color:var(--text-1)] text-[length:14px] leading-[16px]"
      >
        <span class="whitespace-nowrap font-bold">Followed Topics</span>
        <!-- <span class="ml-[9px] text-[color:var(--text-3)] text-[length:11px] line-clamp-1">
          Long press drag sequence
        </span> -->
      </div>

      <div class="mt-[12px] flex flex-wrap">
        <Card
          v-for="(item, index) in list"
          :key="index"
          :item="item"
          class="mr-[10px] mb-[10px] w-[calc((100%_-_10px)_/_2)] even:mr-0"
        ></Card>
      </div>

      <nodata></nodata>
    </div>
  </div>
</template>

<script setup lang="ts">
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import Card from "@/components/topic-manage/card/index.vue";
import Cont from "@/components/topic-manage/cont/index.vue";
import nodata from "@/components/common/nodata.vue";

import { useRouter } from "vue-router";

import { t } from "@/locales";

const router = useRouter();

// type 0-正常-默认 1-系统 2-编辑态 3-添加话题
const list = [
  {
    text: "#event guides guides",
  },
  {
    text: "#mutual help",
    type: 1,
  },
  {
    text: "#mutual help",
    type: 2,
  },
  {
    text: "#mutual help #event guides guides",
    type: 3,
  },
  {
    text: "#event guides guides",
  },
  {
    text: "#mutual help",
    type: 1,
  },
  {
    text: "#mutual help",
    type: 2,
  },
  {
    text: "#mutual help #event guides guides",
    type: 3,
  },
];
</script>
