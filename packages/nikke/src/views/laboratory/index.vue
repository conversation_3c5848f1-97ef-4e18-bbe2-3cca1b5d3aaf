<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import <PERSON>ton from "@/components/ui/button/index.vue";
import NoData from "@/components/common/nodata.vue";
// utils
import { useRouter } from "vue-router";
import { t } from "@/locales";
import { openInBrowser } from "@tencent/pa-ingame-utils";
import { computed } from "vue";
import { useLaboratory } from "@/store/laboratory";
import { useUserStore as useShiftysUser } from "@/shiftyspad/stores/user";
import { urlSearchObjectify } from "packages/utils/qs";

const { initSelfRole } = useShiftysUser();
const laboratory_store = useLaboratory();
const router = useRouter();

laboratory_store.checkLaboratoryMidas();
const url_search_object = urlSearchObjectify();

const list = computed(() =>
  [
    {
      text: "Midas Nikke",
      async handler() {
        let search = location.search;

        if (!+url_search_object.role_id) {
          try {
            const user_role_info = await initSelfRole();
            console.log(`user_role_info`, user_role_info);

            if (user_role_info.role_info) {
              let params = new URLSearchParams(search);
              params.set("role_id", String(user_role_info.role_info.role_id));
              params.set("area_id", String(user_role_info.role_info.area_id));
              params.set("zone_id", String(user_role_info.role_info.zone_id));
              search = `?${params.toString()}`;
            }
          } catch (error) {
            console.error(`[initSelfRole] error`, error);
          }
        }

        console.log("search", search);

        const url = `${laboratory_store.laboratory_midas_url}${search}`;
        console.log(`url`, url);
        openInBrowser(url);
      },
      visible: laboratory_store.laboratory_midas_visible,
    },
  ].filter((item) => item.visible),
);
</script>

<template>
  <div class="w-full mx-auto min-h-screen bg-[color:var(--fill-3)] pt-[44px] pb-[30px] box-border">
    <Head
      :title="t('laboratory')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="router.back"
    ></Head>

    <div class="px-[20px]">
      <template v-if="list.length">
        <Button
          v-for="(item, index) in list"
          :key="index"
          class="mt-[20px]"
          type="primary"
          @click="item.handler"
        >
          {{ item.text }}
        </Button>
      </template>

      <NoData v-else></NoData>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
