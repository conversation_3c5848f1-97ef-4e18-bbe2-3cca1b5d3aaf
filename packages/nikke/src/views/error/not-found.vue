<script setup lang="ts">
// cpnts
import CommonHead from "@/components/common/head/index.vue";
import { CommonImage } from "@/components/common/image/index";

// assets
import PageConentNotFound from "@/assets/imgs/common/page-content-not-found.png";

// utils
import router from "@/router/index";
import { t } from "@/locales/index";

import { Routes } from "@/router/routes";
</script>

<template>
  <div class="capitalize h-screen flex justify-center items-center text-[20px] flex-col">
    <CommonHead
      :bg="'bg-[var(--fill-0)]'"
      :color="'var(--text-1)'"
      :title="''"
      @goback="() => router.push({ path: Routes.HOME })"
    >
    </CommonHead>

    <CommonImage class="w-[238px]" :src="PageConentNotFound"></CommonImage>
    <span class="text-[color:var(--text-3)]">{{ t("page_content_not_found") }}</span>
  </div>
</template>

<style lang="scss" scoped></style>
