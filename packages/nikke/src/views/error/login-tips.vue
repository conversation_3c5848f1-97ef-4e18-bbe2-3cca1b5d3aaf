<script setup lang="ts">
// cpnts
import CommonHead from "@/components/common/head/index.vue";
import { CommonImage } from "@/components/common/image/index";
import Button from "@/components/ui/button/index.vue";

// assets
import NoContent from "@/assets/imgs/common/no-content.png";

// utils
import router from "@/router/index";
import { t } from "@/locales/index";
import { Routes } from "@/router/routes";
import { useUser } from "@/store/user";

const user_store = useUser();
</script>

<template>
  <div class="capitalize h-screen flex justify-center items-center text-[20px] flex-col">
    <template v-if="!user_store.user_info?.fe_user_no_permission">
      <CommonHead
        :bg="'bg-[var(--fill-0)]'"
        :color="'var(--text-1)'"
        :title="''"
        @goback="() => router.replace({ path: Routes.HOME })"
      >
      </CommonHead>
    </template>

    <CommonImage class="w-[238px]" :src="NoContent"></CommonImage>
    <Button
      type="primary"
      :class="[`w-[218px] mx-auto mt-[35px]`]"
      @click="() => router.push({ path: Routes.LOGIN })"
    >
      {{ !user_store.user_info?.fe_user_no_permission ? t("login_to_see_more") : t("login") }}
    </Button>
  </div>
</template>

<style lang="scss" scoped></style>
