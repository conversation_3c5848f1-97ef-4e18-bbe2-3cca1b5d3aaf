<template>
  <div class="min-h-screen pb-[80px]">
    <div
      :class="[`w-full h-[211px] relative pt-[44px] overflow-hidden bg-cover bg-center`]"
      :style="{ backgroundImage: `url(${DefaultImg})` }"
    >
      <i
        class="absolute top-0 left-0 w-full h-[211px] bg-[var(--color-black-70)] pointer-events-none"
      ></i>
      <!-- @todo:上滑之后此处的是固定在顶部的，此时的head需要和个人中心那边一样进行处理，此处的逻辑开发处理一下 -->
      <Head
        class="transition-all duration-300 !border-none"
        :bg="!show_float_header ? 'bg-[transparent]' : 'bg-[var(--color-black-70)]'"
        :title="t('union_square')"
        @goback="router.back()"
      >
        <template #icon>
          <!-- <SvgIcon
            name="icon-language"
            class="w-[24px] h-[24px] cursor-pointer mr-[12px]"
          ></SvgIcon> -->
          <SvgIcon
            name="icon-share"
            class="w-[24px] h-[24px] cursor-pointer"
            @click="shareClick"
          ></SvgIcon>
        </template>
      </Head>
      <CommUserInfo class="relative pt-[10px]"></CommUserInfo>
    </div>

    <div class="relative z-[2] -mt-[83px]">
      <CommonImage
        :class="[`w-full absolute top-0 left-0 -z-[1]`, logined ? `min-h-[175px]` : `h-[243px]`]"
        :src="logined ? BgCont : BgCont2"
      ></CommonImage>
      <div v-if="!logined" class="absolute top-[244px] left-0 w-full h-[235px] -z-[1]">
        <div class="absolute w-full top-0 h-[235px] left-0 -z-[1] pointer-events-none">
          <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
        </div>
        <i
          class="absolute bottom-0 left-0 w-full h-[232px] z-[-1] bg-[image:var(--linear-gradient-2)] pointer-events-none"
        ></i>
      </div>

      <template v-if="logined && user_role_info?.role_id">
        <div class="px-[12px] pt-[24px]">
          <div
            class="flex items-center !font-[Inter] justify-center max-w-[max-content] px-[14px] min-w-[194px] h-[29px] bg-[url('@/assets/imgs/common/cdk-title-bg.png')] bg-[length:100%_100%] bg-no-repeat font-bold text-[18px] leading-[22px] text-[color:var(--brand-3)] mx-auto"
          >
            {{ t("myunion") }}
          </div>
          <!-- 此处如果有数据就显示数据，没有就是无数据状态，重构逻辑占位处理，后续开发根据实际需求处理 -->
          <TeamInfo
            v-if="union_store.my_union"
            class="mt-[15px] mb-[12px]"
            :item="union_store.my_union"
          >
            <div
              class="w-[16px] h-[16px] relative rounded-full bg-[var(--op-fill-white-20)] flex items-center justify-center cursor-pointer"
              @click="union_store.shareToPost()"
            >
              <i class="absolute-center"></i>
              <SvgIcon
                name="icon-post2"
                color="var(--color-white)"
                class="w-[10px] h-[10px]"
              ></SvgIcon>
            </div>
          </TeamInfo>
          <Nodata
            v-else
            class="mt-[18px] mb-0 pb-[12px] bg-[var(--op-fill-white)]"
            :text="no_union_message"
          ></Nodata>
        </div>
      </template>
      <div
        class="relative z-[2] mx-[12px] pt-[12px]"
        :class="logined && user_role_info?.role_id ? '!pt-[12px]' : '!pt-[20px]'"
      >
        <div
          class="flex items-center !font-[Inter] justify-center max-w-[max-content] px-[14px] min-w-[194px] h-[29px] bg-[url('@/assets/imgs/common/cdk-title-bg2.png')] bg-[length:100%_100%] bg-no-repeat font-bold text-[18px] leading-[22px] text-[color:var(--brand-3)] mx-auto"
        >
          {{ t("square_union_list") }}
        </div>
        <SearchBox
          ref="search_box"
          class="mt-[12px] relative z-[1]"
          :search="search"
          :ranks="[]"
          :rank="rank"
          :area_id="area_id"
          :loading="loading && false"
          @refresh="onRefreshClick"
          @update:search="search = $event"
          @update:rank="onRankChange($event)"
          @update:area_id="onAreaChange($event)"
          @click.capture="onClickSearchBox"
        ></SearchBox>
        <div class="mt-[12px]">
          <InfiniteScroll
            :distance="100"
            :back_to_top_visible="false"
            :loading_visible="true"
            :finished_visible="true"
            :loading="loading"
            :empty="empty"
            :finished="finished"
            :debounce_interval="10"
            @load-more="load"
          >
            <TeamCard
              v-for="item in list"
              :key="item.guild_id"
              :ref="(el: InstanceType<typeof TeamCard> | null) => setChildRef(el, item.guild_id)"
              v-click-interceptor.need_login.mute.sign_privacy.stop="() => cardClick(item)"
              :item="item"
              class="mb-[12px]"
            ></TeamCard>
            <template #empty>
              <Empty class="mt-[50px]"></Empty>
            </template>
          </InfiniteScroll>
        </div>
      </div>
    </div>

    <div
      v-if="union_store.my_union"
      class="fixed bottom-[24px] comm-clip cursor-pointer flex items-center justify-center px-[10px] right-[24px] left-[24px] h-[40px] z-[20] bg-[var(--brand-1)] max-w-[calc(var(--max-pc-w)_-_48px)] mx-auto shadow-[0_4px_8px_0_var(--op-shadow-black-20)]"
      @click="cardClick(union_store.my_union)"
    >
      <i
        class="absolute top-0 -z-[1] left-0 w-full h-full bg-[url('@/assets/imgs/announcement-square/icon-arrow.png')] bg-repeat bg-[length:auto_100%]"
      ></i>
      <SvgIcon
        name="icon-post2"
        class="w-[16px] h-[16px] mr-[6px]"
        color="var(--color-white)"
      ></SvgIcon>
      <div class="font-[Inter] text-[color:var(--color-white)] text-[length:13px] font-medium">
        {{ t("share_my_union_and_recruit_members") }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onUnmounted, onMounted, nextTick } from "vue";
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import CommUserInfo from "@/components/common/comm-userinfo/index.vue";
import { CommonImage } from "@/components/common/image";
import TeamInfo from "@/components/announcement-square/team-info/index.vue";
import SearchBox from "@/components/announcement-square/searchbox/index.vue";
import TeamCard from "@/components/announcement-square/team-card/index.vue";
import Nodata from "@/components/common/nodata.vue";
import { InfiniteScroll } from "@/components/common/scroll/index";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { useUserStore as useShiftyspadUserStore } from "@/shiftyspad/stores/user";

import DefaultImg from "@/assets/imgs/common/topic-default.png";
import BgCont from "@/assets/imgs/common/bg-cont.png";
import BgCont2 from "@/assets/imgs/common/bg-cont2.png";
import { t } from "@/locales";
import { UnionCard } from "@/api/union";
import { showUnionDetail } from "@/components/announcement-square/team-pop";
import { useCommonShare } from "@/components/common/share-pop";
import { useUnionStore } from "@/store/union";
import { useQueryGuildCards, useQueryGuildCardsByTourist } from "@/api/union";
import { useUser } from "@/store/user";
import { useInfiniteList } from "@/composables/use-infinite-list";
import Empty from "@/components/common/nodata.vue";
import { Routes, RoutesName } from "@/router/routes";
import { report } from "packages/utils/tlog";
import { useScrollAction } from "@/composables/use-scroll";
import { useBindRole } from "@/shiftyspad/composable/game-role";

const router = useRouter();
const user_store = useUser();

const search = ref("");
const rank = ref<string>("");
const area_id = ref("");

const shiftyspad_user_store = useShiftyspadUserStore();
const { logined, user_role_info } = storeToRefs(shiftyspad_user_store);

const union_store = useUnionStore();
const card_refs = ref<Record<string, InstanceType<typeof TeamCard>>>({});
const setChildRef = (el: InstanceType<typeof TeamCard> | null, key: string) => {
  if (el) {
    card_refs.value[key] = el;
  } else {
    delete card_refs.value[key];
  }
};

const search_box = ref<InstanceType<typeof SearchBox>>();

const union_set = new Set<string>();
const {
  list,
  empty,
  finished,
  load,
  reset: reset_list,
  loading,
} = useInfiniteList({
  item_key: "guild_id",
  queryFn: async () => {
    if (!user_store.is_login && union_set.size) {
      // to login
      router.push({ name: RoutesName.LOGIN, query: { to: Routes.SQUARE } });
    }
    const { cards } = user_store.is_login
      ? await useQueryGuildCards.run({
          guild_rank: rank.value ? Number(rank.value) : -1,
          nikke_area_id: area_id.value ? Number(area_id.value) : undefined,
          search_keyword: search.value || undefined,
        })
      : await useQueryGuildCardsByTourist.run({});
    const new_items = cards.filter((item) => !union_set.has(item.guild_id));
    new_items.forEach((item) => union_set.add(item.guild_id));
    return { list: new_items, is_finish: new_items.length < 2 };
  },
  immediate: false,
});
const reset = () => {
  union_set.clear();
  reset_list();
};

onMounted(async () => {
  registerScroll();
  await user_store.waitLoginCheckFinish();
  if (user_store.is_login) {
    union_store.getMyGuildInfo({ latest: true });
    await waitForUserRoleInfo();
    if (user_role_info.value?.role_id) {
      area_id.value = user_role_info.value?.area_id.toString();
      search_box.value?.reset();
    }
  }
  reset();
});

let timer: number | null = null;
watch(search, () => {
  timer !== null && window.clearTimeout(timer);
  timer = window.setTimeout(() => reset(), 500);
});

const { bindRole } = useBindRole();

const cardClick = (item: UnionCard) => {
  report.standalonesite_unionsquare_item.cm_click({ unionsquare_id: item.guild_id });
  showUnionDetail({
    item,
    from: "square",
    from_post_item: undefined,
    onBindRole: bindRole,
    onSupportToggle: () => {
      card_refs.value[item.guild_id]?.refetchSupporters();
    },
  });
};

const onRankChange = (v: string) => {
  rank.value = v;
  reset();
};

const onAreaChange = (v: string) => {
  area_id.value = v;
  reset();
};

const onRefreshClick = async () => {
  search.value = "";
  await nextTick();
  timer !== null && window.clearTimeout(timer);
  if (loading.value) {
    return;
  }
  reset();
};

const onClickSearchBox = (e: MouseEvent) => {
  if (!user_store.is_login) {
    e.stopPropagation();
    e.preventDefault();
    router.push({ name: RoutesName.LOGIN, query: { to: Routes.SQUARE } });
  }
};

const no_union_message = computed(() => {
  if (union_store.my_guild_error && [1303012, 1303023].includes(union_store.my_guild_error?.code)) {
    return t("api_code_1303012");
  }
  return `<span class='text-[color:var(--text-3-60)] text-[10px]'>${t("not_in_union_empty_tips", [
    `<span class='!text-[color:var(--text-1)]'>${t("square_union_list")}</span>`,
  ])}</span>`;
});

const { scroll_top, registerScroll, unRegisterScroll } = useScrollAction();

const show_float_header = computed(() => {
  return scroll_top.value > 40;
});

const shareClick = () => {
  const { share } = useCommonShare();
  share({ text: t("share_others_text"), url: window.location.href });
};

onUnmounted(() => {
  unRegisterScroll();
  union_set.clear();
  card_refs.value = {};
});

function waitForUserRoleInfo() {
  return new Promise((resolve) => {
    const a = watch(
      () => user_role_info.value?.role_id,
      (v) => {
        if (v != null) {
          resolve(true);
          try {
            a.stop();
          } catch (e) {
            return;
          }
        }
      },
      { immediate: true },
    );
  });
}

report.standalonesite_unionsquare_page.cm_vshow();
</script>

<style lang="scss" scoped></style>
