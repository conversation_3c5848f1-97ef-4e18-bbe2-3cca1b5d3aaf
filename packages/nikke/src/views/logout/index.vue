<script setup lang="ts">
// cpnts
import Loading from "@/components/common/loading.vue";
import { useLogout } from "@/composables/use-logout.ts";

// utils
import { onBeforeMount } from "vue";

const { logout } = useLogout();

onBeforeMount(logout);
</script>

<template>
  <div class="flex justify-center w-full h-screen items-center text-[20px]">
    <Loading></Loading>
  </div>
</template>

<style lang="scss" scoped></style>
