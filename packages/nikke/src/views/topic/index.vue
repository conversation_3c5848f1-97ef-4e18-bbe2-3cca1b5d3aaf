<template>
  <div class="min-h-screen bg-[var(--fill-3)]">
    <Head bg="bg-[transparent]" :z-index="2" :go-home="true" @goback="router.back()">
      <template #icon>
        <SvgIcon
          name="icon-share"
          class="w-[24px] h-[24px] cursor-pointer"
          @click="topShare"
        ></SvgIcon>
      </template>
    </Head>
    <div
      :class="[`w-full aspect-[750/268] relative pt-[56px] overflow-hidden bg-cover bg-center`]"
      :style="{ backgroundImage: `url(${tagInfo?.pic_url || DefaultImg})` }"
    >
      <i
        class="absolute-center !w-full !h-full z-[1] !bg-[var(--color-black-70)] pointer-events-none"
      ></i>
      <div class="flex items-center justify-between mx-[18px]">
        <div
          v-safe-html="`#${tag_name}`"
          class="text-[color:var(--color-white)] font-bold text-[length:18px] leading-[22px] line-clamp-2 relative z-[2]"
        ></div>
        <!-- 下个版本再支持此功能 -->
        <!-- <Btns type="primary" :text="t('follow')">
          <template #icon>
            <span class="text-[18px] mr-[2px]">+ </span>
          </template>
        </Btns> -->
      </div>
    </div>

    <div class="relative z-[2] -mt-[20px]">
      <div
        class="flex sticky z-40 top-0 left-0 items-center h-[42px] border-b-[1px] border-b-[var(--line-1)] px-[12px]"
      >
        <i
          class="absolute -z-[1] top-0 right-0 w-[calc(100%_-_20px)] h-full bg-[var(--fill-3)]"
        ></i>

        <SvgIcon
          name="icon-line-left"
          color="var(--brand-type-1)"
          class="absolute -z-[1] top-[4px] left-0 w-[15px] h-[16px]"
        ></SvgIcon>
        <div class="absolute -z-[1] -top-[3px] right-[0] w-[174px] h-[3px] flex overflow-hidden">
          <i
            class="absolute top-0 left-[6px] bg-[var(--brand-type-1)] -skew-x-[35deg] w-full h-full"
          ></i>
        </div>
        <SvgIcon
          name="icon-line-head"
          color="var(--fill-3)"
          :class="[`absolute -z-[1] top-0 -left-[8px] w-[35px] h-full`]"
        ></SvgIcon>
        <div class="flex items-center justify-between w-full">
          <div
            class="text-[color:var(--text-1)] text-[length:14px] leading-[16px] font-bold flex-1"
          >
            {{ t("posts") }}
          </div>
          <DropdownNormal
            :list="orderList"
            :active="orderBy"
            side="bottom"
            align="end"
            @change="onOrderChange"
          >
            <template #trigger="{ item }">
              <SelectHead :text="item.name" />
            </template>
          </DropdownNormal>
        </div>
      </div>

      <InfiniteScroll
        :back_to_top_visible="false"
        :finished_visible="false"
        :loading="postLoading"
        :finished="false"
        class="w-full bg-[var(--fill-3)] relative -mt-[1px]"
        @load-more="getMore"
      >
        <CardItem
          v-for="(item, index) in showList"
          :key="item.post_uuid"
          :item="item"
          :index="index"
          :plate_id="0"
          plate_name="topic"
          @manage="onManage"
          @share="onShare"
          @star="onStar"
          @follow="onFollow"
          @detail="onDetail"
        />
        <no-data v-if="showList.length === 0 && !postLoading" class="top-no-data"></no-data>
      </InfiniteScroll>
    </div>
  </div>
  <ManageDialog
    :visible="manage_visible"
    :can_edit="cur_item?.can_edit"
    @close="manage_visible = false"
    @delete="onDelete"
    @edit="onEditPost"
  />
</template>

<script setup lang="ts">
import { computed, onActivated, onDeactivated, onMounted, onUnmounted, watch } from "vue";
import { storeToRefs } from "pinia";
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import CardItem from "@/components/common/card-item/index.vue";
import NoData from "@/components/common/nodata.vue";
import { useTagStore } from "@/store/tag.store";
import { useRouter, useRoute } from "vue-router";
import { ref } from "vue";
import { InfiniteScroll } from "@/components/common/scroll";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import { PostItem } from "packages/types/post.ts";
import { postStar, useDeletePost, usePostForward } from "@/api/post.ts";
import { useI18n } from "vue-i18n";
import { useFollowUser } from "@/api/user";
import { LikeType, StanceType } from "packages/types/stance";
import { itemStatusAndCountHandler, replaceNewLineWithBr } from "packages/utils/tools";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { PopCallbackValue } from "packages/types/common";
import { useDialog } from "@/components/ui/dialog/index";
import { useToast } from "@/components/ui/toast/index";
import { updatePostsData, updateUserStatusInPostList } from "@/utils/home";
import DefaultImg from "@/assets/imgs/home/<USER>";
import { usePostsStatus } from "@/composables/use-posts-status";
import { useCommonShare } from "@/components/common/share-pop";
import { report } from "packages/utils/tlog.ts";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { Routes, RoutesName } from "@/router/routes";
import { getStandardizedLang } from "packages/utils/standard";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const { show: showDialog } = useDialog();
const { show: toast } = useToast();
const { filterIsDeleted, setIsDeletedValue } = useIsDeleted();
const { getPostList, init, resetData } = useTagStore();
const { tagId, tagInfo, orderBy, postList, postLoading, pageInfo } = storeToRefs(useTagStore());
const { setPostData } = usePostsStatus();
const showList = computed(() => filterIsDeleted(postList.value));

const orderList = ref([
  { name: t("hot"), value: 2 },
  { name: t("latest"), value: 1 },
]);

const tag_name = computed(() => {
  // cms 转义了 \n => \\n
  const name = replaceNewLineWithBr(tagInfo.value?.tag_name || "", /\\n/);
  return name;
});

const onOrderChange = (data: { name: string; value: number | string }) => {
  orderBy.value = data.value as number;
  getPostList();

  report.standalonesite_topic_filter_btn.cm_click({ btn_name: data.name });
};

const getMore = () => {
  if (pageInfo.value.is_finish) return;
  getPostList(true);
};

const topShare = () => {
  const { share } = useCommonShare();
  share({
    text: t("share_topic_text", [tagInfo.value?.tag_name]),
    url: location.href,
  });
};

const cur_item = ref<PostItem>();

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const manage_visible = ref(false);
const onManage = (item: PostItem) => {
  cur_item.value = item;
  manage_visible.value = true;
};

const onShare = async (item: PostItem) => {
  const { forward_count } = await usePostForward.run({
    post_uuid: item.post_uuid,
  });
  item.forward_count += forward_count;
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onFollow = async (item: PostItem) => {
  const newStatus = await useFollowUser.run({ intl_openid: item.user.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.user.intl_openid,
    is_followed: newStatus.is_follow ? 1 : 0,
    is_mutual_follow: newStatus.is_mutual_follow ? 1 : 0,
  });
  updatePostsData(item, postList.value, newStatus);
};

const onDelete = async () => {
  showDialog({
    title: t("delete"),
    content: t("are_you_sure_to_delete"),
    confirm_text: t("confirm"),
    cancel_text: t("close"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        await useDeletePost.run({
          post_uuid: cur_item.value?.post_uuid || "",
        });
        manage_visible.value = false;
        toast({
          text: t("delete_successfully"),
          type: "success",
        });
        setIsDeletedValue(cur_item.value, true);
      }
      close();
    },
  });
};

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: cur_item.value?.post_uuid || ""!,
      edit_lang: getStandardizedLang(),
    },
  });
};

const startTime = ref(0); // 当前时间戳

const reset = () => {
  const { query } = route;
  resetData((query.tag_id as string) || (query.id as string));
};

onActivated(() => {
  startTime.value = Date.now();

  setTimeout(() => {
    if (!tagInfo.value) return;
    report.standalonesite_topic_page.cm_vshow({
      topic_name: tagInfo.value.tag_name,
      topic_id: +tagInfo.value.id,
    });
  }, 2000);

  event_emitter.on(EVENT_NAMES.topic_detail_reset_list, reset);

  if (route.meta.needRefresh) {
    postList.value = [];
    reset();
  }
});

onDeactivated(() => {
  const stayTime = Date.now() - startTime.value;
  report.standalonesite_topic_page_stay.cm_lvtm({ du: stayTime });
});

onMounted(() => {
  const { query } = route;
  init((query.tag_id as string) || (query.id as string));
});

onUnmounted(() => {
  event_emitter.off(EVENT_NAMES.topic_detail_reset_list, reset);
});

watch(
  () => route.query.tag_id,
  (val) => {
    const { name } = route;
    if (name !== "TOPIC" || val === undefined) return;

    if (tagId.value !== val) {
      resetData(val as string);
    }
  },
);

defineOptions({
  name: RoutesName.TOPIC, // 定义组件名称
});

onEventEmitter(EVENT_NAMES.refresh_post_list_item_info, (v) => {
  if (!v?.post_uuid) return;
  updatePostsData(v, postList.value, v);
});

onEventEmitter(EVENT_NAMES.user_status_change, (v) =>
  updateUserStatusInPostList(postList.value, v),
);
</script>
<style lang="scss" scoped>
.top-no-data {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding: 24px 0;
}
</style>
