<template>
  <Swiper :loop="false" :index="0" :autoplay="false" :duration="3000">
    <div
      v-for="(tab, index) in tabList"
      :key="index"
      class="flex items-center flex-nowrap h-[40px] px-[10px]"
    >
      {{ tab.label }}
    </div>
  </Swiper>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { SwiperDirection } from "@/types/swiper";
import { Swiper } from "@/components/common/swiper/index.ts";

const isSwitch = ref(false);

const tabList = ref([
  { label: "tab1", value: 1 },
  { label: "tab2", value: 2 },
  { label: "tab3", value: 3 },
  { label: "tab4", value: 4 },
  { label: "tab5", value: 5 },
  { label: "tab6", value: 6 },
  { label: "tab7", value: 7 },
  { label: "tab8", value: 8 },
  { label: "tab9", value: 9 },
  { label: "tab10", value: 10 },
  { label: "tab11", value: 11 },
  { label: "tab12", value: 12 },
  { label: "tab13", value: 13 },
  { label: "tab14", value: 14 },
  { label: "tab15", value: 15 },
]);
</script>
