<template>
  <Button @click="handle_open(true)">open toast</Button>
</template>
<script lang="ts" setup>
import { useToast } from "@/components/ui/toast";
import Button from "@/components/ui/button/index.vue";

const { show } = useToast();

const handle_open = () => {
  show({
    text: "测试一下测试一下",
    type: "warning",
  });

  // show({
  //   text: "测试一下测试一下",
  //   type: "loading",
  // });

  // show({
  //   text: "测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下",
  //   type: "success",
  //   auto_close: true,
  // });

  //   show({
  //     text: "测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下测试一下",
  //     type: "warning",
  //     auto_close: true,
  //   });
};
</script>
