<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-6">UI Components & Route Transition Demo</h1>

    <!-- Route Transition Test -->
    <div class="mb-8 p-4 border rounded-lg">
      <h2 class="text-lg font-semibold mb-4">Route Transition Test</h2>
      <div class="flex gap-4 flex-wrap">
        <button
          @click="$router.push('/')"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Go to Home (Back Animation)
        </button>
        <button
          @click="$router.push('/user')"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Go to User (Forward Animation)
        </button>
        <button
          @click="$router.push('/setting')"
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          Go to Settings (Forward Animation)
        </button>
        <button
          @click="$router.back()"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Go Back (Back Animation)
        </button>
      </div>
    </div>

    <!-- Original UI Components -->
    <div class="space-y-8">
      <div class="flex items-center justify-center w-full h-[120px]">
        <DemoSwitch />
      </div>
      <div class="flex items-center justify-center w-full h-[120px]">
        <DemoDropdown />
      </div>

      <div class="flex flex-col items-center justify-center w-full h-[240px] overflow-hidden">
        <DemoTabs />
      </div>
      <div class="flex flex-col items-center justify-center w-full h-[120px] overflow-hidden">
        <DemoActionSheet />
      </div>
      <div class="flex flex-col items-center justify-center w-full h-[120px] overflow-hidden">
        <DemoToast />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import { ref } from "vue";
import DemoSwitch from "./switch.vue";
import DemoDropdown from "./dropdown.vue";
import DemoTabs from "./tabs.vue";
import DemoActionSheet from "./actionsheet.vue";
import DemoToast from "./toast.vue";
</script>
