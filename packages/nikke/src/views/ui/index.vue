<template>
  <div class="flex items-center justify-center w-full h-[120px]">
    <DemoSwitch />
  </div>
  <div class="flex items-center justify-center w-full h-[120px]">
    <DemoDropdown />
  </div>

  <div class="flex flex-col items-center justify-center w-full h-[240px] overflow-hidden">
    <DemoTabs />
  </div>
  <div class="flex flex-col items-center justify-center w-full h-[120px] overflow-hidden">
    <DemoActionSheet />
  </div>
  <div class="flex flex-col items-center justify-center w-full h-[120px] overflow-hidden">
    <DemoToast />
  </div>
</template>

<script lang="ts" setup>
// import { ref } from "vue";
import DemoSwitch from "./switch.vue";
import DemoDropdown from "./dropdown.vue";
import DemoTabs from "./tabs.vue";
import DemoActionSheet from "./actionsheet.vue";
import DemoToast from "./toast.vue";
</script>
