<template>
  <Button @click="handleOpen(true)">open actionsheet</Button>
  <ActionSheet
    title="Action Sheet"
    :visible="is_open"
    :actions="langs"
    :active_id="active_id"
    :click_mask_close="true"
    @change="handleChange"
    @close="handleClose"
  >
    <template #item="{ item, active }">
      <div class="text-[color:var(--text-1)] text-[length:12px] leading-[14px]">
        {{ item?.label }}
      </div>
      <SvgIcon
        v-show="active"
        name="icon-true"
        color="var(--brand-1)"
        class="w-[12px] h-[12px]"
      ></SvgIcon>
    </template>
  </ActionSheet>
</template>
<script lang="ts" setup>
import { ref, onUnmounted } from "vue";
import ActionSheet from "@/components/ui/actionsheet/index.vue";
import Button from "@/components/ui/button/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

import { ActionItem } from "packages/types/actionsheet";

const is_open = ref(false);
const active_id = ref("en");

const langs = ref([
  { value: "en", label: "English" },
  { value: "jp ", label: "日本語" },
  { value: "kr", label: "한국어" },
  { value: "zh-cn", label: "简体中文" },
  { value: "zh-TW", label: "繁體中文" },
  { value: "ru", label: "한국어 RU" },
  { value: "tl", label: "简体中文 TL" },
  { value: "zh", label: "繁體中文 ZH" },
]);

let hideTimer: ReturnType<typeof setTimeout>;

const handleChange = (item: ActionItem) => {
  console.log(item);
  const find_index = langs.value.findIndex((v) => v.value === item.value);
  if (find_index !== -1) {
    active_id.value = langs.value[find_index].value;
  }
  hideTimer = setTimeout(() => {
    handleClose();
  }, 300);
};

const handleOpen = (v: boolean) => {
  console.log("handleOpen", v);
  is_open.value = v;
};

const handleClose = () => {
  is_open.value = false;
};

onUnmounted(() => {
  hideTimer && clearTimeout(hideTimer);
});
</script>
