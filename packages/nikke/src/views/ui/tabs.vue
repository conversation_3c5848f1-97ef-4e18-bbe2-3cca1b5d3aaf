<template>
  <Tabs v-model="activeTab" default-value="notifications" class="w-full">
    <TabsList class="grid w-full grid-cols-4 h-[82px] border-b-[1px] border-[var(--line-1)]">
      <TabsTrigger value="comments" class="flex flex-col items-center justify-between">
        <div class="relative w-[50px] h-[50px] flex items-center justify-center">
          <div
            class="absolute w-[50px] h-[50px] rounded-full"
            :class="activeTab === 'comments' ? 'bg-[var(--brand-2)]' : 'bg-transparent'"
          ></div>
          <Badge :value="99">
            <svgIcon
              name="icon-line-comments"
              class="w-[30px] h-[30px] mb-[1px]"
              :class="
                activeTab === 'comments' ? '!text-[var(--brand-1)]' : '!text-[color:var(--text-1)]'
              "
            ></svgIcon>
          </Badge>
        </div>

        <p
          class="text-[length:11px] w-full truncate"
          :class="activeTab === 'comments' ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]'"
        >
          CommentsCommentsCommentsComments
        </p>
      </TabsTrigger>
      <TabsTrigger value="follower" class="flex flex-col items-center justify-between">
        <div class="relative w-[50px] h-[50px] flex items-center justify-center">
          <div
            class="absolute w-[50px] h-[50px] rounded-full"
            :class="activeTab === 'follower' ? 'bg-[var(--brand-2)]' : 'bg-transparent'"
          ></div>
          <Badge :value="100"
            ><svgIcon
              name="icon-line-follower"
              class="w-[30px] h-[30px] mb-[1px]"
              :class="
                activeTab === 'follower' ? '!text-[var(--brand-1)]' : '!text-[color:var(--text-1)]'
              "
            ></svgIcon
          ></Badge>
        </div>
        <p
          class="text-[length:11px] w-full truncate"
          :class="activeTab === 'follower' ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]'"
        >
          New Follower
        </p>
      </TabsTrigger>
      <TabsTrigger value="like" class="flex flex-col items-center justify-between">
        <div class="relative w-[50px] h-[50px] flex items-center justify-center">
          <div
            class="absolute w-[50px] h-[50px] rounded-full"
            :class="activeTab === 'like' ? 'bg-[var(--brand-2)]' : 'bg-transparent'"
          ></div>
          <Badge :value="0">
            <svgIcon
              name="icon-line-like"
              class="w-[30px] h-[30px] mb-[1px]"
              :class="
                activeTab === 'like' ? '!text-[var(--brand-1)]' : '!text-[color:var(--text-1)]'
              "
            ></svgIcon>
          </Badge>
        </div>
        <p
          class="text-[length:11px] w-full truncate"
          :class="activeTab === 'like' ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]'"
        >
          Received Like
        </p></TabsTrigger
      >
      <TabsTrigger value="notifications" class="flex flex-col items-center justify-between">
        <div class="relative w-[50px] h-[50px] flex items-center justify-center">
          <div
            class="absolute w-[50px] h-[50px] rounded-full"
            :class="activeTab === 'notifications' ? 'bg-[var(--brand-2)]' : 'bg-transparent'"
          ></div>
          <Badge :value="9">
            <svgIcon
              name="icon-line-notification"
              class="w-[30px] h-[30px] mb-[1px]"
              :class="
                activeTab === 'notifications'
                  ? '!text-[var(--brand-1)]'
                  : '!text-[color:var(--text-1)]'
              "
            ></svgIcon>
          </Badge>
        </div>
        <p
          class="text-[length:11px] w-full truncate"
          :class="
            activeTab === 'notifications' ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]'
          "
        >
          Notifications
        </p>
      </TabsTrigger>
    </TabsList>
    <TabsContent value="comments"> Comments. </TabsContent>
    <TabsContent value="follower"> New Follower. </TabsContent>
    <TabsContent value="like"> Received Like. </TabsContent>
    <TabsContent value="notifications"> Notifications. </TabsContent>
  </Tabs>

  <TabScroll :tabs="tabs" :loading="loading" :active_id="activeTab02"></TabScroll>
</template>
<script lang="ts" setup>
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import svgIcon from "@/components/common/svg-icon.vue";
import { ref, onMounted, computed, onBeforeUnmount } from "vue";
import Badge from "@/components/ui/badge/index.vue";
import TabScroll from "@/components/common/tabs/tab-scroll.vue";
import { useScroll } from "@vueuse/core";
const activeTab = ref("comments");

const tabsList = ref([
  { name: "all", label: "All" },
  { name: "gain", label: "Gain" },
  { name: "redeem", label: "Redeem" },
  { name: "overdue", label: "Overdue" },
  { name: "outpost", label: "OutPost" },
  { name: "nikkeart", label: "NikkeArt" },
]);

const loading = ref(false);
const tabs = ref([
  { value: "all", label: "All" },
  { value: "gain", label: "Gain" },
  { value: "redeem", label: "Redeem" },
  { value: "overdue", label: "Overdue" },
  { value: "outpost", label: "OutPost" },
  { value: "nikkeart", label: "NikkeArt" },
]);

const activeTab02 = ref("all");

const slideTabInnerRef = ref<HTMLElement>();
const { x } = useScroll(slideTabInnerRef, { behavior: "smooth" });
const slideTabs = ref<HTMLElement[]>([]);
const getTabsWidth = (arr: HTMLElement[]) => {
  return arr.reduce((acc, currentValue) => {
    return acc + currentValue.getBoundingClientRect().width;
  }, 0);
};
const curTabIndex = computed(() => {
  const isFind = tabsList.value.findIndex((item) => item.name === activeTab02.value);
  if (isFind !== -1) return isFind;
  return 0;
});
const tabContainerWidth = computed(() => {
  return slideTabInnerRef.value?.offsetWidth || 0;
});
const isShowPlaceholder = computed(() => {
  console.log("isShowPlaceholder", getTabsWidth(slideTabs.value), tabContainerWidth.value);
  return tabContainerWidth.value >= getTabsWidth(slideTabs.value);
});
const currentTransform = () => {
  const totalMove = getTabsWidth(slideTabs.value);

  const halfViewPortWidth = tabContainerWidth.value / 2;
  if (totalMove < tabContainerWidth.value) return 0;

  const currentMove = getTabsWidth(slideTabs.value.slice(0, curTabIndex.value));

  // console.log(
  //   "currentTransform",
  //   totalMove,
  //   tabContainerWidth.value,
  //   curTabIndex.value,
  //   "currentMove",
  //   currentMove,
  // );

  // 判断最小滑动区域
  if (currentMove < halfViewPortWidth) {
    x.value = 0;
    return 0;
  }

  // 判断最大滑动区域
  const moveMax = totalMove - halfViewPortWidth;
  if (currentMove > moveMax) {
    x.value = totalMove - tabContainerWidth.value;
    return (totalMove - tabContainerWidth.value) * -1;
  }
  x.value = currentMove - halfViewPortWidth;
  return (currentMove - halfViewPortWidth) * -1;
};

const handleChangeTab = (e: string) => {
  console.log("handleChangeTab", e, currentTransform());
};

onBeforeUnmount(() => {});
onMounted(() => {
  if (slideTabInnerRef.value?.children) {
    slideTabs.value = Array.from(slideTabInnerRef.value?.children) as HTMLElement[];
  }
});
</script>

<style lang="scss">
.tab-scroll-container {
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: transparent;
  }
}
</style>
