<template>
  <!-- <Dropdown
    :open="isOpen"
    :list="langs"
    side="bottom"
    align="end"
    :active="activeId"
    @change="handleChange"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #trigger="{ item }">
      <div class="flex items-center justify-center">
        <span>{{ item?.name }}</span>
        <span class="g-icon is-language"></span>
      </div>
    </template>
    <template #default="{ item, index }">
      <div class="text-[color:var(--text-1)] text-[length:12px] leading-[14px]">
        {{ item?.name }}
      </div>
      <svgIcon
        v-show="activeId === index"
        name="icon-true"
        color="var(--brand-1)"
        class="w-[12px] h-[12px]"
      ></svgIcon>
    </template>
  </Dropdown> -->

  <!-- <Dropdown
    :open="isOpen"
    :list="langs"
    side="bottom"
    align="end"
    :side-offset="7"
    :active="activeId"
    type="customize"
    @change="handleChange"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #trigger="{ item }">
      <div class="flex items-center justify-center">
        <span>{{ item?.name }}</span>
        <span class="g-icon is-language"></span>
        <span>首页的下拉</span>
      </div>
    </template>
    <template #default="{ item, active, first, last }">
      <div
        class="text-[length:12px] leading-[14px] px-[8px]"
        :class="[
          active ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]',
          first ? 'pt-[4px]' : '',
          last ? 'pb-[8px]' : '',
        ]"
      >
        {{ item?.name }}
      </div>
    </template>
  </Dropdown> -->
  <DropdownLang :list="langs">
    <template #trigger="{ item }">
      <div class="flex items-center justify-center">
        <span class="g-icon is-language"></span>
        <span>当前：</span>
        <span>{{ item?.name }}</span>
      </div>
    </template>
  </DropdownLang>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import DropdownLang from "@/components/common/dropdown/dropdown-lang.vue";
// const isOpen = ref(false);
// const activeId = ref(0);
const langs = ref([
  { name: "English", value: "en" },
  { name: "日本語", value: "jp" },
  { name: "한국어", value: "kr" },
  { name: "简体中文", value: "zh-cn" },
  { name: "繁體中文", value: "zh-tw" },
]);
// const handleChange = (item: { name: string; value: string }) => {
//   console.log(item);
//   const findIndex = langs.value.findIndex((v) => v.name === item.name);
//   if (findIndex !== -1) {
//     activeId.value = findIndex;
//   }
// };
// const handleOpen = (v: boolean) => {
//   console.log("handleOpen", v);
//   isOpen.value = v;
// };
// const handleClose = () => {
//   console.log("handleClose");
//   isOpen.value = false;
// };
</script>
