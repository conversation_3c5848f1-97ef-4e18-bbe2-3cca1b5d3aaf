<script setup lang="ts">
// cpnts
import ActionSheet from "@/components/ui/actionsheet/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Loading from "@/components/common/loading.vue";
import Head from "@/components/common/head/index.vue";

import { CommonImage } from "@/components/common/image";
import bg from "@/assets/imgs/login/head-bg.png";
import logo from "@/assets/imgs/login/logo.png";

// configs
import { STORAGE_COOKIE_GAME_ID } from "packages/configs/storage";
import { STANDARD_GAME_ID_KEY, STANDARD_INTL_LOGIN_SDK_LANG_MAP } from "packages/configs/standard";

// types
import { IntlRenderMode } from "packages/types/intl";
import { ActionItem } from "packages/types/actionsheet";
import { LoginRes } from "packages/types/login";
import { CODE_ALL_CONFIGS, CODE_MESSAGE_MAP } from "packages/configs/code";

// utils
import { Step, useLogin, getAccountConfig } from "packages/utils/login";
import { getIntlSocailList, injectCss } from "packages/utils/tools";
import { Ref, ref } from "vue";
import { t } from "@/locales/index";
import { useApiLogin } from "@/api/login";
import { useLSLoginMetaStorage } from "packages/utils/storage";
import {
  getStandardizedGameId,
  getStandardizedLang,
  setStandardizedCookie,
} from "packages/utils/standard";
import { Routes, RoutesName } from "@/router/routes";
import { delay } from "lodash-es";
import { useInterceptor } from "@/composables/use-interceptor";
import { useUser } from "@/store/user";
import router from "@/router";
import { report } from "packages/utils/tlog";
import { useToast } from "@/components/ui/toast";
import { useConfigs } from "@/composables/use-configs.ts";
import { urlSearchObjectify } from "packages/utils/qs";

const { login_areas, fingLoginAreaByGameId } = useConfigs();
const { setLoginMeta } = useLSLoginMetaStorage();
const { show: toast } = useToast();

const qs_url_search = urlSearchObjectify();
const game_id = getStandardizedGameId();
const active_area: Ref<ActionItem> = ref(fingLoginAreaByGameId(game_id) || login_areas.value[1]);
const loading_visible = ref(true);
const area_action_sheet_visible = ref(!game_id);
const login_pop_config = ref(getAccountConfig(active_area.value.value));
const current_url_lang = getStandardizedLang() as keyof typeof STANDARD_INTL_LOGIN_SDK_LANG_MAP;
const lang_type = STANDARD_INTL_LOGIN_SDK_LANG_MAP[current_url_lang];

const use_login_configs = {
  env: login_pop_config.value.env as string,
  gameID: login_pop_config.value.gameID,
  appID: login_pop_config.value.appID,
  webID: login_pop_config.value.webID,
  common_config: {
    use_debugger: false,
  },
  config: {
    isMobile: true,
    langType: lang_type,
    renderMode: IntlRenderMode.inline,
    loginWithCode: {
      enable: true,
      registerType: "auto", //"auto",
    },
    loginWithPwd: {
      enable: true,
      registerType: "manual", //"disabled",
    },
    procedureSwitch: {
      region: true,
      adultStatus: true,
      agreement: true,
      registerPassword: "required",
    },
    /**
     * @link https://docs.playernetwork.intlgame.com/docs/zh/Level-Infinite-Pass/Integration/Get-started/#%E6%AD%A5%E9%AA%A44%E4%BD%BF%E7%94%A8%E7%BD%91%E9%A1%B5%E7%99%BB%E5%BD%95%E7%BB%84%E4%BB%B6%E5%AE%9E%E7%8E%B0%E7%AC%AC%E4%B8%89%E6%96%B9%E6%B8%A0%E9%81%93%E7%99%BB%E5%BD%95
     */
    socialList: getIntlSocailList(),
    theme: window.IS_DARK_MODE
      ? {
          token: {} as any,
          algorithm: "dark",
        }
      : {
          token: {
            colorPrimary: "#3EAFFF",
            colorSecondary: "#B3AEC1",
            colorPrimaryBg: "#FFFFFF",
            colorSecondaryBg: "#E6E4EB",
            colorIcon: "#0A001A",
            colorInput: "#E6E4EB",
            colorHeaderBg: "#F5F7FF",
            borderRadiusContainer: "0px",
            borderRadiusButton: "0px",
            fontSize: "MD",
            colorPrimaryText: "#3EAFFF",
            colorTextBase: "#0A001A",
            colorTextButton: "#FFFFFF",
            colorError: "#FC725A",
          },
          algorithm: "light",
        },
  },
};

// if (qs_url_search.webid !== "1") {
//   delete use_login_configs.webID;
// }

// NOTE: mock hok config
// Object.assign(use_login_configs, {
//   env: "test",
//   gameID: "29134",
//   appID: "304953b95531249e24a5dcb5104e5ed7",
//   // webID: "e30f5225-4ed6-4caf-bcd1-9f5c7178758c",
// });

console.log(`[login] use_login_configs`, use_login_configs);

const { login, setLoginPopConfig } = useLogin(use_login_configs);

const onBack = () => {
  if (qs_url_search.back_to) {
    router.push({
      path: qs_url_search.back_to,
    });
    return;
  }
  router.back();
};

const handleChange = async (item: ActionItem) => {
  active_area.value = item;
  login_pop_config.value = getAccountConfig(item.value);

  // if (qs_url_search.webid !== "1") {
  //   delete login_pop_config.value.webID;
  // }

  setLoginPopConfig(login_pop_config.value);
  toggleAreaActionSheetVisible();
  render();
};

const handleBodyPointerEvents = () => {
  /**
   * actionsheet 时 body 会被禁止点击，所以这里需要手动开启
   */
  area_action_sheet_visible.value
    ? document.body.classList.add("!pointer-events-auto")
    : document.body.classList.remove("!pointer-events-auto");
};

const toggleAreaActionSheetVisible = () => {
  area_action_sheet_visible.value = !area_action_sheet_visible.value;
  handleBodyPointerEvents();
};

const toggleLoadingVisible = () => {
  loading_visible.value = !loading_visible.value;
};

const handleClose = () => {
  area_action_sheet_visible.value = false;
};

const afterLogin = async (log_res: LoginRes) => {
  toggleLoadingVisible();
  setLoginMeta(log_res);
  const { openid, channel_info, token, token_expire_time, user_name, extra_json } = log_res;

  try {
    report.standalonesite_login_btn.cm_click({ channel_name: channel_info.channelId });

    await useApiLogin.run({
      game_openid: openid,
      game_channelid: channel_info.channelId,
      game_token: token,
      game_id: login_pop_config.value?.gameID || active_area.value.value,
      game_expire_time: token_expire_time,

      game_uid: channel_info.openid,
      game_user_name: user_name,
      game_user_region: extra_json?.get_status_rsp?.region,
      game_adult_status: extra_json?.get_status_rsp?.adult_check_status,
      game_email: channel_info.account,
    });

    report.standalonesite_login_ret.cm_click({ channel_name: channel_info.channelId, ret: 0 });

    // 登录成功之后设置 game_id 缓存
    setStandardizedCookie(STORAGE_COOKIE_GAME_ID, login_pop_config.value.gameID);

    const jump = () => {
      const search_params = new URLSearchParams(window.location.search);
      const to_path = (search_params.get("to") || "/") as Routes;
      const redirect_path = Object.values(Routes).includes(to_path) ? to_path : "/";

      search_params.delete("to");
      search_params.delete("back_to");
      search_params.delete(STANDARD_GAME_ID_KEY);

      const remaining_params = search_params.toString();
      let redirect_url = redirect_path;
      if (remaining_params) {
        const separator = redirect_url.includes("?") ? "&" : "?";
        redirect_url += `${separator}${remaining_params}`;
      }
      window.location.replace(redirect_url || "/");
    };

    try {
      const user_store = useUser();
      await user_store.checkLogin();
      const res = await user_store.refetchUserInfo();
      const code = res?.error?.code;

      // 无权限
      if (CODE_ALL_CONFIGS.NO_PERMISSION === code) {
        user_store.user_info.fe_user_no_permission = true;
        toast({
          text: t(CODE_MESSAGE_MAP[code]),
          type: "error",
        });

        router.replace({
          name: RoutesName.ERROR_LOGIN_TIPS,
        });
        return false;
      }

      const { afterLoginInterceptor } = useInterceptor();
      await afterLoginInterceptor();
    } catch (error) {
      console.error(`[afterLoginInterceptor] error: `, error);
    }

    jump();
  } catch (error: any) {
    console.error("error: ", error);

    report.standalonesite_login_ret.cm_click({
      channel_name: channel_info.channelId,
      ret: error.code!,
    });
  } finally {
    toggleLoadingVisible();
  }
};

const afterRendered = async () => {
  delay(() => {
    loading_visible.value = false;
  }, 500);
};

const render = async () => {
  const timer = setTimeout(() => {
    toast({
      text: t("intl_sdk_fail"),
      type: "error",
      // auto_close: false,
    });
    loading_visible.value = false;
    throw new Error("load intl game sdk time out");
  }, 8000);

  injectCss(`
    .pass__wrapper {
      width: 100% !important;
    }
    .pass-content__wrapper {
      width: 100% !important;
      box-shadow: none !important;
      border: none !important;
    }
    .infinite-confirm-content p {
      color: #0A001A !important;
    }
  `);
  login("#infinite-pass-component", async (step: Step, value: null | LoginRes) => {
    clearTimeout(timer);

    switch (step) {
      case Step.rendered:
        report.standalonesite_login_window.cm_vshow();
        afterRendered();
        break;

      case Step.logined:
        afterLogin(value as LoginRes);
        break;

      case Step.registered:
        console.log("[login] registered value", value);
        afterLogin(value as LoginRes);
        break;
      default:
    }
  });
};

// FIXME: 如果没有 game_id, 会弹出登录弹窗，这个时候需要处理 body pointer-events-none 的情况
if (area_action_sheet_visible.value) {
  handleBodyPointerEvents();
}

render();
</script>

<template>
  <div class="flex flex-col h-screen bg-[var(--op-fill-white)]">
    <!-- :title="t('login')" -->
    <Head bg="bg-[transparent]" color="var(--color-white)" @goback="onBack"> </Head>

    <ActionSheet
      :title="t('please_choose_region')"
      :visible="area_action_sheet_visible"
      :actions="login_areas"
      :active_id="active_area.value"
      :click_mask_close="false"
      @change="handleChange"
      @close="handleClose"
    >
      <template #item="{ item, active }">
        <div class="text-[color:var(--text-1)] text-[length:12px] leading-[14px]">
          {{ item?.label }}
        </div>
        <svgIcon
          v-show="active"
          name="icon-true"
          color="var(--brand-1)"
          class="w-[12px] h-[12px]"
        ></svgIcon>
      </template>
    </ActionSheet>

    <template v-if="active_area.value">
      <div class="h-[190px] relative z-[1] pt-[34px] flex flex-col">
        <CommonImage
          :src="bg"
          class="w-full h-full object-cover -z-1 absolute top-0 left-0"
          image_class="w-full h-full object-cover"
        ></CommonImage>

        <CommonImage
          :src="logo"
          class="mx-auto w-[101px] h-[81px] object-cover mb-[26px] relative"
        ></CommonImage>

        <div class="px-[19px] w-full box-border mb-[20px] h-[30px] relative">
          <div
            class="common-btns border-[1px] border-[var(--brand-2-20)] bg-[var(--brand-2-20)] relative text-center transition-all duration-200 h-full flex items-center pl-[10px] pr-[13px] justify-between cursor-pointer"
            @click="toggleAreaActionSheetVisible"
          >
            <div class="flex items-center text-[length:11px]">
              <span class="text-[color:var(--color-white)] font-[Inter]">
                {{ t("choose_region") }}
              </span>
              <span class="mx-[10px] h-[12px] w-[0.5px] bg-[var(--color-white)] inline-flex"></span>
              <span class="text-[color:var(--color-white)] font-[Inter] font-semibold">
                {{ active_area.label }}
              </span>
            </div>

            <span
              class="rotate-180 border-l-[4px] border-r-[4px] border-b-[4px] rounded-[2px] border-b-[color:var(--op-line-white)] border-l-transparent border-r-transparent"
            ></span>

            <span
              class="absolute -bottom-[5px] -right-[6px] w-[10px] h-[10px] bg-[color:var(--brand-2-20)] rotate-45"
            ></span>
          </div>
        </div>
      </div>

      <div class="relative flex-1">
        <div
          v-show="loading_visible"
          class="w-full flex items-center justify-center absolute inset-0 z-50"
        >
          <Loading></Loading>
        </div>

        <!-- lip 登陆器挂在元素 -->
        <div id="infinite-pass-component"></div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
