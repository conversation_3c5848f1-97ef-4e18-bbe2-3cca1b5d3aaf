<template>
  <InfiniteScroll
    :finished="is_finish"
    :loading="loading"
    class="relative h-full w-full pb-2"
    @load-more="loadMore"
  >
    <div v-for="(item, index) in list" :key="index" class="mb-2 md:!mb-4">
      <LeftMediaRightTextCard
        :title="item.title"
        :time="item.time"
        :desc="item.desc"
        :src="item.src"
        :is_video="item.is_video"
        @click="onItemClick(item)"
      ></LeftMediaRightTextCard>

      <div
        v-if="isCmsListSplitLineVisible(list, index)"
        class="mt-2 h-[1px] w-full bg-[var(--color-border-16)] md:!mt-4"
      ></div>
    </div>
  </InfiniteScroll>
</template>

<script setup lang="ts">
// cpnts
import { LeftMediaRightTextCard } from "@/components/info/cards/index";
import { InfiniteScroll } from "@/components/common/scroll";

// types
import { BsNewsGroupsProps } from "packages/types/groups";

// utils
import { useBsCmsListCpnt } from "@/composables/use-cms";
import { isCmsListSplitLineVisible } from "packages/utils/tools";
import { watch } from "vue";
import { useCms } from "@/composables/use-cms";

const props = withDefaults(defineProps<BsNewsGroupsProps>(), {});
const emits = defineEmits(["more-click", "item-click"]);

const { useCmsJump } = useCms();

const { list, is_finish, loading, loadMore, reset, getLoadParams } = useBsCmsListCpnt(
  Object.assign({}, props, { list: props.list }),
);

const onItemClick = (item: any) => {
  useCmsJump(item);
};

watch(
  () => props.group_id,
  () => {
    reset();
    loadMore(getLoadParams({ content_id: props.group_id }));
  },
);
</script>

<style lang="scss" scoped></style>
