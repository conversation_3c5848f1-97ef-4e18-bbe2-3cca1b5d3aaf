<template>
  <div v-show="!isEmpty(detail)" class="w-full px-[16px] pt-[10px]">
    <div v-if="header_visible" class="flex flex-col">
      <div class="flex items-center">
        <SvgIcon
          class="w-[24px] min-w-[24px] min-h-[24px] h-[24px] mr-[10px]"
          name="icon-goback"
          color="var(--text-1)"
          @click="onBack"
        ></SvgIcon>

        <div
          v-safe-html="detail.title"
          class="line-clamp-none break-words font-bold uppercase text-[color:var(--text-1)] text-[length:22px] mt-[4px]"
        ></div>
      </div>
      <div
        class="text flex justify-between border-b-[1px] border-solid border-[var(--op-line-black-10)] text-[color:var(--text-2)] mb-5 pb-2 pt-6"
      >
        <div class="flex items-center">
          <div
            v-if="detail.avatar"
            class="mr-2 h-9 w-9 flex-shrink-0 overflow-hidden rounded-full md:h-14 md:w-14"
          >
            <img class="h-full w-full object-cover" :src="detail.avatar" />
          </div>
          <div class="font-thin">
            <p class="line-clamp-1 text-[color:var(--text-2)] text-[12px]">
              {{ detail.author }}
            </p>
            <p class="text-[7a7a7a] text-[12px]">
              {{ ymd(+detail.pub_timestamp) }}
            </p>
          </div>
        </div>

        <div class="flex items-center">
          <!-- <div class="mr-7 flex cursor-pointer items-center" @click="onLike">
            <SvgIcon
              class="h-[12px] w-[12px] mr-3"
              name="icon-like"
              :color="content_like_info.like_status ? `var(--brand-1)` : `var(--text-3)`"
            />

            <span
              :class="[
                `whitespace-nowrap text-[12px]`,
                content_like_info.like_status
                  ? 'text-[color:var(--color-brand1)]'
                  : 'text-[color:var(--color-text-white-40)]',
              ]"
            >
              {{ abbrNumn(detail?.like_num || content_like_info.like_num) }}
            </span>
          </div> -->

          <!-- <div class="flex items-center">
            <SvgIcon
              class="h-[18px] w-[18px] mr-3"
              name="icon-view"
              color="var(--color-text-white-40)"
            />
            <span class="whitespace-nowrap text-[7a7a7a] text-[12px]">
              {{ abbrNumn(detail.view_num) }}
            </span>
          </div> -->
        </div>
      </div>
    </div>

    <div ref="content_ref" class="text-[14px] text-[color:var(--text-1)]"></div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { useToast } from "@/components/ui/toast";

// types
import { CmsDetailProps, Detail } from "packages/types/cms";
import { LikeOp } from "@tencent/pa-cms-utils";

// configs

// utils
import { ref, watch } from "vue";
import { ymd } from "packages/utils/tools";
import { useCms } from "@/composables/use-cms";
import { isEmpty } from "lodash-es";
import { t } from "@/locales/index";
// import { useUser } from "@/store/user";
// import { useUserSignPolicyPop } from "@/components/common/pop/user-sign-policy";
import router from "@/router";
import { useNoCookie } from "packages/utils/cms";

const { show: toast } = useToast();
// const user_store = useUser();
// const { show: showUserSignPolicyPop } = useUserSignPolicyPop();

const getInitContentLikeInfo = () => ({
  like_status: LikeOp.Cancel,
  like_num: 0,
});

const props = withDefaults(defineProps<CmsDetailProps>(), {
  header_visible: true,
});

const {
  // likeOperation,
  getFeedLikeInfo,
  getFeedDetail,
  cms_helper,
} = useCms({
  cms_config: props.cms_config,
});

const content_ref = ref();
const detail = ref<Detail>({} as any);
const content_like_info = ref(getInitContentLikeInfo());
const emits = defineEmits(["detail-loading", "detail-loaded", "pop-login", "pop-user-setting"]);

const onBack = () => router.back();

// const onLike = async () => {
//   // 未登录
//   if (!user_store.is_login) {
//     console.error("user info is undefined");
//     return;
//   }

//   //
//   if (!user_store.user_info.has_sign_privacy) {
//     await showUserSignPolicyPop({});
//     return false;
//   }

//   const status = content_like_info.value.like_status === LikeOp.Like ? LikeOp.Cancel : LikeOp.Like;
//   if (detail.value.like_num) {
//     detail.value.like_num = status ? detail.value.like_num + 1 : detail.value.like_num - 1;
//     content_like_info.value.like_status = status;
//   }
//   await likeOperation({
//     content_id: props.content_id,
//     op_type: status,
//   });
//   // 重新获取点赞状态
//   loadLikeInfo();
// };

const loadLikeInfo = async () => {
  // 未登录的情况下，like_info 是 undefined
  const like_info = await getFeedLikeInfo(props.content_id);
  content_like_info.value = like_info || getInitContentLikeInfo();
};

const resetDetail = () => {
  detail.value = {} as any;
  if (content_ref.value?.innerHTML) {
    content_ref.value.innerHTML = "";
  }
};

const replaceNBSP = (str: string) => {
  return str.replace(/(&nbsp;)+/g, function (match) {
    return match.length > 6 ? match.slice(0, -6) + " " : " ";
  });
};

const patchDetailField = (detail: Detail): Detail => {
  if (!detail.content) {
    return Object.assign(detail, { content: detail.content_part });
  }

  detail.content = replaceNBSP(detail.content);

  return detail;
};

const loadDetail = async () => {
  const cms_detail = await getFeedDetail(props.content_id);
  if (cms_detail.result !== 0) {
    console.error("[loadDetail] load detail failed", cms_detail);
    toast({
      text: t("load_content_error"),
      type: "error",
    });
    return;
  }
  //  某些情况下需要做补齐字段，比如 banner 类型的详情页，content 缺失，得 content_part 补齐
  detail.value = patchDetailField(cms_detail);

  cms_helper.initDetailPage({
    el: content_ref.value,
    nocookie: useNoCookie(),
    cms_info: detail.value,
    style_isolation: true,
    isolation_type: "shadowdom",
    isolation_extra_style: `
      img {
        max-width: 100%;
        max-height: 100%;
      }
      tr,
      td {
        border-color: var(--op-line-white) !important;
        color: var(--color-white) !important;
      }
      td span,
      td p,
      td div,
      td a {
        color: var(--color-white) !important;
      }
      p {
        margin: 0;
        padding: 1em 0;
        line-height: 1.5;
      }
    `,
    video_style: "aspect-ratio: 16/9; width: 100%;",
  });
};

const load = async () => {
  loadLikeInfo();
  resetDetail();
  loadDetail();
};

watch(
  () => props.content_id,
  () => {
    // console.log(`[watch] props.content_id`, props.content_id);
    //  NOTE: cms sdk 如果判断 content_id 为空了，那么会从 url 那 father_content_id 来请求，所以这里不能用非空处理
    load();
  },
  {
    immediate: true,
  },
);
</script>

<style lang="scss" scoped></style>
