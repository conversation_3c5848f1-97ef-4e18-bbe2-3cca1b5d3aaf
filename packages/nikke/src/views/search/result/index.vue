<template>
  <div class="relative bg-[var(--fill-3)] w-full mx-auto min-h-screen pb-[30px] box-border">
    <div
      class="flex w-full z-[9] items-center h-[44px] pl-[12px] pr-[25px] bg-[var(--fill-0)] border-b-[var(--line-1)] border-b-[length:0.5px] box-border sticky top-0"
    >
      <div class="w-[24px] h-[24px] cursor-pointer box-content" @click="router.back()">
        <SvgIcon name="icon-goback" color="var(--text-1)"></SvgIcon>
      </div>
      <SearchBox
        ref="searchBoxRef"
        class="ml-[12px] flex-1"
        :keyword="keyword"
        :is-focus="true"
        @click-search="clickSearchHandle"
      ></SearchBox>
    </div>

    <div
      class="flex z-[9] justify-between items-center h-[40px] w-full pt-[10px] box-border bg-[var(--fill-0)] border-b-[var(--line-1)] border-b-[length:0.5px] px-[12px] sticky top-[44px]"
    >
      <Tabs
        v-model="activePlate"
        :default-value="activePlate"
        class="overflow-x-auto h-full flex items-center mr-[8px] flex-1"
      >
        <TabsList>
          <TabsTrigger
            v-for="item in allPlates"
            :key="item.label"
            :value="item.value"
            class="relative mr-[8px] ml-[9px] first:ml-0 last:mr-0 first:before:hidden before:absolute before:left-[-9px] before:top-1/2 before:-translate-y-1/2 before:w-[0.5px] before:h-[14px] before:bg-[var(--fill-3)]"
          >
            <div
              class="relative text-[12px] font-medium leading-[15px] text-[var(--text-1-50)] group"
              :class="[
                activePlate == item.value
                  ? 'active italic [&.active]:font-bold [&.active]:text-[var(--text-1)] after:w-full after:h-[5px] after:absolute after:bg-[var(--brand-1)] after:skew-x-[-12deg] after:bottom-0 after:left-1/2 after:-translate-x-1/2'
                  : '',
              ]"
            >
              <span class="relative group-[&.active]:px-[2px] z-[1] cursor-pointer">
                {{ item.label }}
              </span>
            </div>
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <!-- 搜索会按相关性排序，不再主动设置 -->
      <!-- <template
        v-if="
          ([PlatId.outpost, PlatId.nikkeart, PlatId.guides] as string[]).includes(activePlateKey)
        "
      >
        <DropdownNormal
          :active="activeOrderby"
          :list="orderByList"
          side="bottom"
          align="end"
          @change="changeOrderByHandle"
        >
          <template #trigger="{ item }">
            <SelectHead :text="item.name" />
          </template>
        </DropdownNormal>
      </template> -->
    </div>

    <Tabs v-model="activePlate" :default-value="activePlate" class="px-[12px]">
      <TabsContent v-for="item in allPlates" :key="item.label" :value="item.value" tabindex="">
        <InfiniteScroll
          :back_to_top_visible="false"
          :finished_visible="false"
          :loading="loading"
          :finished="pageInfo.is_finish"
          class="w-full relative mt-[12px]"
          @load-more="loadMore"
        >
          <template v-if="!['Topic', 'User'].includes(item.key)">
            <Card
              v-for="dataItem in postList"
              :key="dataItem.post_uuid"
              :item="dataItem"
              class="mt-[12px]"
              @star="onStar"
              @detail="onDetail"
            ></Card>
            <template v-if="postList.length === 0 && !loading && pageInfo.is_finish">
              <Nodata class="mt-[150px]"></Nodata>
            </template>
          </template>

          <template v-if="item.key === 'Topic'">
            <Topic v-for="data in topicList" :key="data.id" class="mt-[12px]" :data="data"></Topic>
            <template v-if="topicList.length === 0 && !loading && pageInfo.is_finish">
              <Nodata class="mt-[150px]"></Nodata>
            </template>
          </template>

          <template v-if="item.key === 'User'">
            <User
              v-for="data in userList"
              :key="data.id"
              class="mt-[12px]"
              :data="data"
              @follow="onFollow"
            >
            </User>
            <template v-if="userList.length === 0 && !loading && pageInfo.is_finish">
              <Nodata class="mt-[150px]"></Nodata>
            </template>
          </template>
        </InfiniteScroll>
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted, onActivated, ref } from "vue";
import { storeToRefs } from "pinia";
import { useRoute, useRouter } from "vue-router";
import SvgIcon from "@/components/common/svg-icon.vue";
import SearchBox from "@/components/common/search-box/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import Card from "@/components/search/card/index.vue";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Nodata from "@/components/common/nodata.vue";
import { InfiniteScroll } from "@/components/common/scroll";
import { postStar } from "@/api/post";
import { LikeType, StanceType } from "packages/types/stance";
import { useHomeStore } from "@/store/home/<USER>";
import { PlatId } from "packages/types/common";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { PostItem } from "packages/types/post";
import User from "@/components/search/list/user.vue";
import Topic from "@/components/search/list/topic.vue";
import { useSearchStore } from "@/store/search/search.store.ts";
import { UserInfo } from "packages/types/user";
import { useFollowUser } from "@/api/user.ts";
import { RoutesName } from "@/router/routes";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { usePostsStatus } from "@/composables/use-posts-status";

const { getConfig } = useHomeStore();

defineOptions({
  name: RoutesName.SEARCH_RESULT,
});

const router = useRouter();
const searchBoxRef = ref<InstanceType<typeof SearchBox>>();

const store = useSearchStore();
const { searchHandle, clickSearchHandle, loadMore, changeOrderByHandle } = store;
const {
  postList,
  keyword,
  queryParams,
  activePlateKey,
  allPlates,
  pageInfo,
  loading,
  orderByList,
  activePlate,
  topicList,
  userList,
  activeOrderby,
} = storeToRefs(store);

const route = useRoute();
keyword.value = (route.query.keyword as string) || "";

watch(
  () => queryParams.value,
  () => {
    searchHandle();
  },
  {
    deep: true,
    immediate: true,
  },
);

const { setPostData } = usePostsStatus();

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });

  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onFollow = async (item: UserInfo) => {
  const newStatus = await useFollowUser.run({ intl_openid: item.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.intl_openid,
    is_followed: newStatus.is_follow ? 1 : 0,
    is_mutual_follow: newStatus.is_mutual_follow ? 1 : 0,
  });
  searchHandle(); // 重新请求数据
};

onMounted(() => {
  getConfig();
});

onActivated(() => {
  if (route.meta.needRefresh) {
    keyword.value = (route.query.keyword as string) || "";
    searchBoxRef.value?.setSearchValue(keyword.value);
    searchHandle();
  }
});

/** 事件通知: 帖子详情页数据变动 */
onEventEmitter(EVENT_NAMES.refresh_post_list_item_info, async (v) => {
  if (!v?.post_uuid) return;
  postList.value = postList.value.map((item) => {
    if (item.post_uuid === v.post_uuid) {
      item.my_upvote = v.my_upvote;
      item.is_collection = v.is_collection;
      item.is_comment = v.is_comment;
      item.upvote_count = v.upvote_count;
      item.comment_count = v.comment_count;
      item.forward_count = v.forward_count;
    }
    return item;
  });
});

onEventEmitter(EVENT_NAMES.user_status_change, (v) => {
  if (!v?.intl_openid) return;
  userList.value.forEach((item) => {
    if (item.intl_openid === v.intl_openid) Object.assign(item, v);
  });
});
</script>

<style lang="scss" scoped></style>
