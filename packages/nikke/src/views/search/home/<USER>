<template>
  <div class="relative bg-[var(--fill-3)] w-full mx-auto min-h-screen pb-[30px] box-border">
    <div
      class="flex w-full z-[9] items-center h-[44px] pl-[12px] pr-[25px] bg-[var(--fill-0)] border-b-[var(--line-1)] border-b-[length:0.5px] box-border sticky top-0"
    >
      <div class="w-[24px] h-[24px] cursor-pointer box-content" @click="router.back()">
        <SvgIcon name="icon-goback" color="var(--text-1)"></SvgIcon>
      </div>
      <SearchBox
        class="ml-[12px] flex-1"
        :is-focus="true"
        @click-search="clickSearchHandle"
      ></SearchBox>
    </div>

    <div class="pt-[22px]">
      <Panel v-if="historyKeyword.length > 0" :title="t('history')">
        <template #icon>
          <SvgIcon name="icon-delete" color="var(--text-3)" @click="clearHistoryHandle"></SvgIcon>
        </template>
        <Words :list="historyKeyword" class="mt-[8px]" @item-click="clickSearchHandle"> </Words>
      </Panel>
      <!-- 本期不做 -->
      <!-- <Panel :title="'Hot Searches'" class="mt-[20px]">
        <Words
          :list="hotList"
          class="mt-[8px]"
          @item-click="(item) => clickSearchHandle(item)"
        ></Words>
      </Panel> -->
      <Panel
        v-if="recommends.length > 0"
        :title="t('recommend')"
        :class="{ 'mt-[20px]': historyKeyword.length }"
      >
        <ul class="mt-[8px]">
          <li
            v-for="(item, index) in recommends"
            :key="index"
            class="rounded-[4px] mt-[12px] first-of-type:mt-0"
            :class="{
              'cursor-pointer': !item?.ext_info?.default, // 兜底图不显示跳转鼠标
            }"
            @click="onClick(item)"
          >
            <CommonImage
              :src="item.pic_urls[0]"
              class="rounded-[4px] w-full object-contain"
            ></CommonImage>
          </li>
        </ul>
      </Panel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ref, onMounted } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import SearchBox from "@/components/common/search-box/index.vue";
import Words from "@/components/search/words/index.vue";
import Panel from "@/components/search/panel/index.vue";
import { useLSStorage } from "packages/utils/storage";
import { useHomeStore } from "@/store/home/<USER>";
import { useCms } from "@/composables/use-cms";
import type { IDetail } from "@tencent/pa-cms-utils";
import { CommonImage } from "@/components/common/image/index";

const router = useRouter();
const storage = useLSStorage();
const { t } = useI18n();
const { getBanners } = useHomeStore();
const { bannerList: recommends } = storeToRefs(useHomeStore());
const historyKeyword = ref(JSON.parse(storage.getStorage("searchHistoryKeywordList") || "[]"));
const { useCmsJump } = useCms();
// const hotList = ref<string[]>([]);
onMounted(() => {
  getBanners();
});

const clickSearchHandle = (keyword: string) => {
  console.log("[clickSearchHandle] keyword", keyword);
  keyword &&
    router.push({
      path: "/search/result",
      query: {
        keyword: keyword,
      },
    });
};

const clearHistoryHandle = () => {
  historyKeyword.value = [];
  storage.setStorage("searchHistoryKeywordList", JSON.stringify([]));
};

// const getHotList = async () => {
//   try {
//     const data = await Promise.resolve({
//       ret: 0,
//       list: ["1.5th anniversary event", "mutual help", "crown A", "crown", "mutual help"],
//     });

//     if (data.ret === 0) {
//       hotList.value = data.list;
//     }
//   } catch (error) {
//     console.log(error);
//   }
// };

const onClick = (item: IDetail) => {
  if (!item.jump_link_info && !item.jump_scheme) return;
  useCmsJump(item);
};
</script>

<style lang="scss" scoped></style>
