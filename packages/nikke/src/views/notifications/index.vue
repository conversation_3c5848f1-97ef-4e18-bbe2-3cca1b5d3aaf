<template>
  <div class="w-full relative z-[1] bg-[color:var(--fill-3)] pt-[44px] pb-[30px]">
    <Head
      :title="t('notifications')"
      :color="'var(--text-1)'"
      bg="bg-[var(--fill-0)]"
      @goback="router.back()"
    >
      <template #icon>
        <svgIcon
          name="icon-config"
          class="w-[24px] h-[24px] cursor-pointer"
          color="var(--text-1)"
          @click="toSetting"
        ></svgIcon>
      </template>
    </Head>
    <Tabs v-if="activeTab" v-model="activeTab" :default-value="activeTab" class="w-full">
      <TabsList
        class="grid w-full grid-cols-4 h-[82px] border-b-[1px] border-[var(--line-1)] dark:border-[var(--line-1)] bg-[color:var(--fill-0)]"
      >
        <TabsTrigger
          v-for="(item, index) in data"
          :key="index"
          :value="item?.key"
          class="flex flex-col items-center justify-between"
        >
          <div class="relative w-[50px] h-[50px] flex items-center justify-center">
            <div
              class="absolute w-[50px] h-[50px] rounded-full"
              :class="
                activeTab === item.key
                  ? 'bg-[var(--brand-2)] dark:bg-[var(--brand-1-20)]'
                  : 'bg-transparent'
              "
            ></div>
            <Badge :value="item.badge">
              <svgIcon
                :name="item.svg"
                class="w-[30px] h-[30px] mb-[1px]"
                :class="
                  activeTab === item.key
                    ? '!text-[color:var(--brand-1)]'
                    : '!text-[color:var(--text-1)]'
                "
              ></svgIcon>
            </Badge>
          </div>
          <p
            class="text-[length:12px] w-full truncate"
            :class="activeTab === item.key ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-3)]'"
          >
            {{ item?.title }}
          </p>
        </TabsTrigger>
      </TabsList>
      <TabsContent
        v-for="(item, index) in data"
        :key="index"
        :value="item?.key"
        class="w-full"
        tabindex=""
      >
        <component
          :is="item.component"
          :data="item?.data"
          @show-reply="showReply = true"
          @manage="onManage"
        ></component>
      </TabsContent>
    </Tabs>
  </div>
  <!-- <CommentPop :show="showReply" @close="showReply = !showReply"></CommentPop> -->
  <Dialog
    :show="popshow"
    :title="t('alert')"
    :content="t('are_you_sure_to_unfollow')"
    :confirm_text="t('confirm')"
    :cancel_text="t('cancel')"
    @confirm="onFollow"
    @cancel="popshow = !popshow"
  />
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import svgIcon from "@/components/common/svg-icon.vue";
import { useFollowUser } from "@/api/user";
import Dialog from "@/components/ui/dialog/index.vue";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Badge from "@/components/ui/badge/index.vue";
import Comments from "@/components/notifications/comments/index.vue";
import Follower from "@/components/notifications/follower/index.vue";
import ReceivedLike from "@/components/notifications/received-like/index.vue";
import Notifications from "@/components/notifications/notifications/index.vue";
import { Routes } from "@/router/routes";
import { useRouter, useRoute } from "vue-router";
import { ref, onActivated, computed, watch } from "vue";
import { useUnReadMessage, useReadMessageAll } from "@/api/user";
import { UnReadMessageCounts } from "@/types/user";
import { report } from "packages/utils/tlog";
import { useCommentStar } from "@/api/comments";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";

defineOptions({
  name: "NOTIFICATION", // 定义组件名称
});

const enum NotificationTab {
  COMMENTS = "comments",
  FOLLOWER = "follower",
  RECEIVEDLIKE = "like",
  NOTIFICATIONS = "notification",
}

const readMessageAllMap = {
  comments: 1,
  follower: 2,
  like: 3,
  notification: 4,
};

const { t } = useI18n();

const router = useRouter();
const route = useRoute();
const activeTab = ref("notification" as NotificationTab);

const unReadMessageCounts = ref<UnReadMessageCounts>({
  comment_count: 0,
  follow_count: 0,
  like_count: 0,
  site_msg_count: 0,
});

const tabMap = {
  [NotificationTab.NOTIFICATIONS]: "site_msg_count",
  [NotificationTab.FOLLOWER]: "follow_count",
  [NotificationTab.RECEIVEDLIKE]: "like_count",
  [NotificationTab.COMMENTS]: "comment_count",
} as const;

onActivated(async () => {
  // 1. 获得未读数
  await getUnReadMessage();
  if (route.meta?.shouldRefresh) {
    // 2. 计算默认tab
    activeTab.value = getdefaultActiveTab();
    // 3. 上报该栏目全部已读
    if (unReadMessageCounts.value[tabMap[activeTab.value]] > 0) {
      useReadMessageAll.run({ type: readMessageAllMap[activeTab.value] });
      unReadMessageCounts.value[tabMap[activeTab.value]] = 0;
    }
  }

  report.standalonesite_message_page.cm_vshow({
    tab_name: activeTab.value,
  });
});

const getUnReadMessage = async () => {
  unReadMessageCounts.value = (await useUnReadMessage.run({})) || {};
};

const getdefaultActiveTab: () => NotificationTab = () => {
  if (unReadMessageCounts.value.site_msg_count > 0) return NotificationTab.NOTIFICATIONS;
  if (unReadMessageCounts.value.comment_count > 0) return NotificationTab.COMMENTS;
  if (unReadMessageCounts.value.follow_count > 0) return NotificationTab.FOLLOWER;
  if (unReadMessageCounts.value.like_count > 0) return NotificationTab.RECEIVEDLIKE;
  // 都为0 选择通知
  return NotificationTab.NOTIFICATIONS;
};

const data = computed(() => [
  {
    title: t("comments"),
    key: "comments",
    svg: "icon-line-comments",
    badge: unReadMessageCounts.value.comment_count,
    component: Comments,
    data: [],
  },
  {
    title: t("new_follower"),
    svg: "icon-line-follower",
    key: "follower",
    badge: unReadMessageCounts.value.follow_count,
    component: Follower,
    data: [],
  },
  {
    title: t("received_like"),
    svg: "icon-line-like",
    key: "like",
    badge: unReadMessageCounts.value.like_count,
    component: ReceivedLike,
    data: [],
  },
  {
    title: t("notifications"),
    svg: "icon-line-notification2",
    key: "notification",
    badge: unReadMessageCounts.value.site_msg_count,
    component: Notifications,
    data: [],
  },
]);

const showReply = ref(false);

const popshow = ref(false);
const pop_data = ref<any>(null);
const onManage = async (action: string, item: any) => {
  if (action === "reply") {
    // showReply.value = true;
    router.push({
      path: Routes.POST_DETAIL,
      query: {
        post_uuid: item.post_uuid,
      },
    });
  }

  if (action === "like") {
    const res = await useCommentStar.run({ comment_uuid: item.comment_uuid, type: item.type });
    console.log("onManage", action, res);
    item.is_star = res.status === 1;
  }

  if (action === "follow") {
    const has_follow = false;
    pop_data.value = item;
    if (has_follow) {
      popshow.value = true;
    } else {
      await onFollow();
    }
  }
};
const onFollow = async () => {
  if (!pop_data.value) return;
  const res = await useFollowUser.run({ intl_openid: pop_data.value.send_user_info.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: pop_data.value.send_user_info.intl_openid,
    is_followed: res.is_follow ? 1 : 0,
    is_mutual_follow: res.is_mutual_follow ? 1 : 0,
  });
  popshow.value = false;
  pop_data.value = null;
  console.log("onFollow", res);
};

const toSetting = () => {
  report.standalonesite_message_set_btn.cm_click();
  router.push("/setting/notifications");
};

watch(activeTab, async (val) => {
  if (unReadMessageCounts.value[tabMap[val]] > 0) {
    await useReadMessageAll.run({ type: readMessageAllMap[val] });
    await getUnReadMessage();
  }
  report.standalonesite_message_sub_btn.cm_click({
    tab_name: val,
  });
});
</script>

<style lang="scss" scoped></style>
