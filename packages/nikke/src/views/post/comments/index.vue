<template>
  <div class="w-full relative z-[1] pt-[44px] pb-[100px]">
    <Head
      :title="t('comment')"
      :color="'var(--text-1)'"
      bg="bg-[var(--fill-0)]"
      class="border-b-[1px] border-b-[color:var(--line-1)]"
      @goback="router.back()"
    >
      <template #icon>
        <div
          v-if="route.query.jump_from === RoutesName.NOTIFICATION"
          class="text-[color:var(--brand-1)] text-[16px]"
          @click="toDetail"
        >
          {{ t("original_text") }}
        </div>
      </template>
    </Head>

    <div class="px-[15px]">
      <div v-if="comment" class="border-b-[1px] border-b-[var(--line-1)] pt-[20px] pb-[8px]">
        <CommentItem
          :item="comment"
          @translate="comments_store.onTranslateComment(comment)"
          @upvote="comments_store.onCommentStar(comment, CommentType.comment)"
          @more="
            showPop({
              type: 'more',
              ignores: comments_store
                .onGetMoreIgnoreItemList(comment)
                .concat([ActionType.copy_comment_id]),
              onClick: (type: ActionType) => comments_store.onMoreItemClick(comment, type, {}),
            })
          "
          @reply="
            showCommentsPop({
              post_uuid,
              comment_uuid,
              type: CommentType.reply,
            })
          "
        ></CommentItem>
      </div>

      <div
        v-if="comments_store.state.reply_list[0]"
        class="text-[14px] leading-[16px] font-bold mb-[12px] mt-[20px] text-[color:var(--text-1)]"
      >
        {{ t("all_comments") }}
      </div>

      <InfiniteScroll
        :back_to_top_visible="false"
        :loading="loading"
        :finished="finished"
        :empty="filterIsDeleted(comments_store.state.reply_list).length === 0"
        @load-more="load"
      >
        <ReplyItem
          v-for="item in filterIsDeleted(comments_store.state.reply_list)"
          :key="item.comment_uuid"
          class="mb-[20px]"
          :item="item"
          :line="true"
          @more="
            showPop({
              type: 'more',
              ignores: comments_store
                .onGetMoreIgnoreItemList(item)
                .concat([ActionType.copy_comment_id]),
              onClick: (type: ActionType) =>
                comments_store.onMoreItemClick(item, type, {
                  report_content_type: ReportContentType.comment,
                }),
            })
          "
          @reply="
            showCommentsPop({
              post_uuid: item.post_uuid,
              comment_uuid: item.comment_uuid,
              type: CommentType.reply,
            })
          "
          @translate="comments_store.onTranslateComment(item)"
          @upvote="comments_store.onCommentStar(item, CommentType.reply)"
        ></ReplyItem>

        <template #empty>
          <Nodata
            class="mt-[70px]"
            first
            :text="
              t('come_to_grab', [
                `<span class='text-[var(--brand-1)]'>${t('first_comment')}</span>`,
              ])
            "
          ></Nodata>
        </template>

        <template #finished>
          <div class="flex justify-center w-full items-center text-[var(--text-4)] text-[13px]">
            {{ t("loaded_replies") }}
          </div>
        </template>
      </InfiniteScroll>

      <div
        v-click-interceptor.need_login.mute.sign_privacy="
          () =>
            showCommentsPop({
              post_uuid,
              comment_uuid,
              type: CommentType.reply,
            })
        "
        class="flex-1 fixed max-w-[var(--max-pc-w)] mx-auto bottom-0 left-0 right-0 py-[10px] inline-flex items-center rounded-[1px] border border-solid border-[color:var(--fill-3)] font-normal text-[13px] px-[12px] box-border justify-center text-[color:var(--text-3)] bg-[var(--op-fill-white)] z-[6]"
      >
        <div
          class="h-[40px] px-[20px] bg-[color:var(--fill-3)] w-full flex items-center justify-start"
        >
          {{ t("reply") }} {{ comment?.user?.username }}:
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import CommentItem from "@/components/post/comments/item/comment.vue";
import ReplyItem from "@/components/post/comments/item/reply.vue";
import Nodata from "@/components/common/nodata.vue";
import InfiniteScroll from "@/components/common/scroll/infinite-scroll.vue";
import { usePop } from "@/components/post/detail/pop/index.ts";

// types
import { CommentType, GetPostCommentsResponseItem } from "packages/types/comments";
import { ReportContentType } from "packages/types/content";

// utils
import { useRouter, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { useComments } from "@/store/post/comments";
import { Routes, RoutesName } from "@/router/routes";
import { onBeforeMount, ref } from "vue";
import { useIsDeleted } from "@/composables/use-is-deleted.ts";
import { useCommentsPop } from "@/components/post/comments/pop/index.ts";
import { ActionType } from "packages/types/post";
import { usePostDetailStore } from "@/store/post/detail";

const { t } = useI18n();

const router = useRouter();
const route = useRoute();
const comments_store = useComments();
const { filterIsDeleted } = useIsDeleted();
const { show: showCommentsPop } = useCommentsPop();
const { show: showPop } = usePop();
const post_detail_store = usePostDetailStore();

const post_uuid = route.query.post_uuid as string;
const comment_uuid = route.query.comment_uuid as string;
const comment = ref({} as GetPostCommentsResponseItem);

const { load, loading, finished } = comments_store.onGetPostCommentReplies();

const toDetail = () => {
  router.push({ path: Routes.POST_DETAIL, query: route.query });
};

comments_store.onGetPostCommment(comment_uuid).then((res) => {
  comment.value = res.comment;
});

onBeforeMount(async () => {
  await post_detail_store.onLoadDetail(route.query.post_uuid as string, true);
});

// const agree = ref<boolean>(false);
</script>

<style lang="scss" scoped></style>
