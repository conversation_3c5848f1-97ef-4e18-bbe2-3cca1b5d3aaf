<script setup lang="ts">
// cpnts
import CommonHead from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import PostDetailHeader from "@/components/post/detail/header/index.vue";
import PostDetailComments from "@/components/post/detail/comments/index.vue";
import PostDetailFooter from "@/components/post/detail/footer/index.vue";
import PostDetailContainerBody from "@/components/post/detail/body/index.vue";
import { Tag } from "@/components/common/tag";
import { usePop } from "@/components/post/detail/pop/index.ts";
import Stance from "@/components/common/stance/index.vue";
import FriendCard from "@/components/common/friend-card/index.vue";
import { showRoleDialog } from "@/components/common/player-pop/index.ts";
import { useToast } from "@/components/ui/toast";
import {
  AuthoingStatementCreatorStatementTypeCpnt,
  AuthoingStatementTipsCpnt,
} from "./authoring-statement-atomic";
// types
import { ActionType, PostDetail } from "packages/types/post";
import { LikeType, StanceItem, StanceType } from "packages/types/stance";

// utils
import { computed, onActivated, onBeforeMount, onBeforeUnmount, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { usePostDetailStore } from "@/store/post/detail.ts";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { Routes, RoutesName } from "@/router/routes";
import { formatTime } from "@/utils/str.ts";
import { report } from "packages/utils/tlog";
import { useMore } from "@/components/post/detail/pop/more/composition.ts";
import { isEmpty } from "lodash-es";
import { useRouterBack } from "@/composables/use-router-back.ts";
import { FriendCardStatus, UnionCardStatus } from "packages/types/user";
import UnionCard from "@/components/common/union-card/index.vue";
import { useUnionStore } from "@/store/union";
import { showUnionDetail } from "@/components/announcement-square/team-pop";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";
import { formatNumber } from "packages/utils/tools";
import { useBindRole } from "@/shiftyspad/composable/game-role";

const { show: toast } = useToast();
const { onRouterBack } = useRouterBack();
const { getMoreActions } = useMore();
const { t } = useI18n();
const post_detail_store = usePostDetailStore();
const router = useRouter();
const route = useRoute();
const { show: showPop } = usePop();
const union_store = useUnionStore();

defineOptions({
  name: RoutesName.POST_DETAIL,
});

const { detail, post_detail_title, loading } = storeToRefs(post_detail_store);
const du = ref(0);

const onBack = () => {
  onRouterBack();
};

const onMoreItemClick = (type: ActionType) => {
  post_detail_store.onMoreItemClick(detail.value, type);
};

const onStance = async (item: StanceItem) => {
  await post_detail_store.onLike(
    route.query.post_uuid as string,
    item.type,
    item.type === StanceType.like ? LikeType.stance : null,
  );

  onNotifyPostChange(detail.value);
};

/** 事件通知: 帖子详情页数据变动 */
const onNotifyPostChange = async (v: PostDetail) => {
  event_emitter.emit(EVENT_NAMES.refresh_post_list_item_info, v);
};

const user_game_player_info = computed(
  () => (detail.value.friend_card || {}) as PostDetail["friend_card"],
);

const friend_card_status = computed(() => {
  if (user_game_player_info.value.is_send_friend_request) {
    return FriendCardStatus.requested;
  }
  return FriendCardStatus.canrequest;
});

const union_card = computed(() => {
  return detail.value.guild_card?.guild_id ? detail.value.guild_card : undefined;
});

const union_card_status = computed(() => {
  if (
    union_card.value?.guild_id &&
    union_store.canJoinUnion(union_card.value.guild_id) &&
    !union_store.my_union &&
    !union_store.isJoinUnionPending(union_card.value.guild_id)
  ) {
    return UnionCardStatus.canrequest;
  }
  return UnionCardStatus.disabled;
});

const post_uuid = computed(() => route.query.post_uuid as string);

onBeforeMount(async () => {
  du.value = performance.now();

  post_detail_store.onLoadPlats();
  post_detail_store.onRestDetail();

  await post_detail_store.onLoadDetail(post_uuid.value, true);

  report.standalonesite_news_detail_page.cm_vshow({
    content_id: detail.value.post_uuid,
    content_type: detail.value.type,
    label_id: detail.value.plate_id,
    label_name: detail.value.plate_name || "",
  });
});

onBeforeUnmount(() => {
  du.value = performance.now() - du.value;

  report.standalonesite_news_detail_page_stay.cm_lvtm({
    du: du.value,
  });
});

const onClickFriendCard = () => {
  if (!detail.value.friend_card.show_friend_card_detail) {
    toast({
      text: t("player_set_game_card_to_be_private"),
      type: "warning",
    });
    return;
  }

  showRoleDialog({
    is_client: true,
    uid: detail.value.user.intl_openid,
    area_id: detail.value.friend_card.area_id,
    friend_card_status: friend_card_status.value,
    onFriendCardRequest() {
      post_detail_store.onFriendCardRequest();
    },
  });
};

const { bindRole } = useBindRole();

const onClickUnionCard = () => {
  if (union_card.value) {
    showUnionDetail({
      item: union_card.value,
      from: "post",
      from_post_item: detail.value,
      onBindRole: bindRole,
    });
  }
};

watch(
  () => loading.value.detail,
  () => {
    if (route.query.scroll_target === "comment" && !loading.value.detail) {
      setTimeout(() => {
        event_emitter.emit(EVENT_NAMES.post_detail_comment_list_scroll_into_view);
      }, 1000);
    }
  },
  {
    flush: "post",
  },
);

onActivated(async () => {
  if (route.meta.needRefresh || post_uuid.value !== detail.value?.post_uuid) {
    post_detail_store.onRestDetail();
    await post_detail_store.onLoadDetail(post_uuid.value, true);
    report.standalonesite_news_detail_page.cm_vshow({
      content_id: detail.value.post_uuid,
      content_type: detail.value.type,
      label_id: detail.value.plate_id,
      label_name: detail.value.plate_name || "",
    });
  }
});
</script>

<template>
  <div class="w-full min-h-screen pt-[44px] pb-[100px]">
    <CommonHead
      :bg="'bg-[var(--fill-0)]'"
      :color="'var(--text-1)'"
      :go-home="true"
      @goback="onBack"
    >
      <template #title="{ title_class }">
        <div
          :class="title_class + ' !cursor-pointer'"
          @click="
            () => {
              event_emitter.emit(EVENT_NAMES.page_scroll_to_top);
            }
          "
        >
          {{ post_detail_title }}
        </div>
      </template>
      <template #icon>
        <div
          v-if="!isEmpty(getMoreActions(post_detail_store.onGetMoreIgnoreItemList(detail)))"
          v-click-interceptor.need_login.mute.sign_privacy="
            () =>
              showPop({
                type: 'more',
                ignores: post_detail_store.onGetMoreIgnoreItemList(detail),
                onClick: onMoreItemClick,
              })
          "
          class="w-[24px] h-[24px] relative cursor-pointer"
        >
          <i class="absolute-center"></i>
          <SvgIcon name="icon-ellipsis" color="var(--text-1)"></SvgIcon>
        </div>
      </template>
    </CommonHead>

    <div class="px-[15px]">
      <PostDetailHeader class="mt-[8px]"></PostDetailHeader>

      <AuthoingStatementTipsCpnt
        class="mt-[8px]"
        :ai_content_type="detail.ai_content_type"
        :risk_remind_type="detail.risk_remind_type"
      ></AuthoingStatementTipsCpnt>

      <div
        class="mt-[10px] font-bold text-[length:16px] text-[color:var(--text-1)] leading-[20px] break-words"
      >
        {{ detail?.title }}
      </div>

      <div class="mt-[12px]">
        <PostDetailContainerBody></PostDetailContainerBody>
      </div>

      <FriendCard
        v-if="detail.friend_card?.show_friend_card"
        :status="friend_card_status"
        :user_game_player_info="user_game_player_info"
        :class="[`my-[12px]`]"
        @request="post_detail_store.onFriendCardRequest"
        @click="onClickFriendCard"
      ></FriendCard>

      <UnionCard
        v-if="union_card"
        :class="[`my-[12px]`]"
        :status="union_card_status"
        :union_info="union_card"
        @click="onClickUnionCard"
        @request="union_store.joinUnion(union_card)"
      ></UnionCard>

      <!-- 话题 -->
      <div v-if="detail.tags.length" class="mt-[8px]">
        <Tag
          v-for="(tag, index) in detail.tags"
          :key="index"
          :closable="false"
          class="!text-[--brand-1] !bg-[color:unset] !p-0 !border-none mr-[8px] last-of-type:mr-0"
          @click="
            router.push({
              path: Routes.TOPIC,
              query: {
                tag_name: tag.name,
                tag_id: tag.id,
              },
            })
          "
        >
          #{{ tag.name }}
        </Tag>
      </div>

      <div class="w-full my-[7px]">
        <div
          v-if="detail.created_on"
          class="text-[--text-3] text-[length:11px] leading-[13px] capitalize flex flex-col gap-[6px]"
        >
          <div class="flex items-center">
            <div class="flex items-center gap-[16px]">
              <span>{{ t("posted") }} {{ formatTime(+detail.created_on * 1000, t) }}</span>
              <div v-if="detail.browse_count" class="flex items-center gap-[4px]">
                <SvgIcon
                  name="icon-eye-open"
                  color="var(--text-3)"
                  class="w-[13px] h-[13px] translate-y-[-1px]"
                ></SvgIcon>
                <span>{{ formatNumber(detail.browse_count) }}</span>
              </div>
            </div>
          </div>
          <AuthoingStatementCreatorStatementTypeCpnt
            :creator_statement_type="detail.creator_statement_type"
          ></AuthoingStatementCreatorStatementTypeCpnt>
        </div>

        <Stance
          :type="detail?.my_upvote?.upvote_type"
          :nums="post_detail_store.stance_nums"
          @stance="onStance"
        ></Stance>
      </div>
    </div>

    <PostDetailComments></PostDetailComments>

    <PostDetailFooter></PostDetailFooter>
  </div>
</template>

<style scoped lang="scss"></style>
