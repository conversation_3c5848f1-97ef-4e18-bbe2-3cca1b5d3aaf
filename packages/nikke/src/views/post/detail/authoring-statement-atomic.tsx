// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
// types
import {
  ComposeNewAuthoringStatementAIContentType,
  ComposeNewAuthoringStatementRiskRemindType,
  ComposeNewAuthoringStatementType,
} from "packages/types/post";
// utils
import { t } from "@/locales";
import { computed, defineComponent } from "vue";

export const AuthoingStatementTipsCpnt = defineComponent({
  name: "AuthoingStatementTipsCpnt",
  props: {
    risk_remind_type: {
      type: Number as () => ComposeNewAuthoringStatementRiskRemindType,
      required: true,
    },
    ai_content_type: {
      type: Number as () => ComposeNewAuthoringStatementAIContentType,
      required: true,
    },
  },
  setup(props) {
    const is_none = computed(() => {
      return (
        props.ai_content_type === undefined ||
        props.risk_remind_type === undefined ||
        (props.ai_content_type === ComposeNewAuthoringStatementAIContentType.no &&
          props.risk_remind_type === ComposeNewAuthoringStatementRiskRemindType.no)
      );
    });

    const icon_name = computed(() => {
      if (props.ai_content_type === ComposeNewAuthoringStatementAIContentType.yes) {
        return "icon-info";
      }
      if (props.risk_remind_type !== ComposeNewAuthoringStatementRiskRemindType.no) {
        return "icon-info";
      }
      return "";
    });

    const tips = computed(() => {
      const arr: string[] = [];
      switch (props.risk_remind_type) {
        case ComposeNewAuthoringStatementRiskRemindType.reveal_the_plot_risk:
          arr.push(t("authoring_statement_category2_1"));
          break;
        case ComposeNewAuthoringStatementRiskRemindType.risk_content:
          arr.push(t("authoring_statement_category2_2"));
          break;
        case ComposeNewAuthoringStatementRiskRemindType.all:
          arr.push(t("authoring_statement_category2_1"), t("authoring_statement_category2_2"));
          break;
      }
      if (props.ai_content_type === ComposeNewAuthoringStatementAIContentType.yes) {
        arr.push(t("authoring_statement_category3"));
      }
      return t("authoring_statement_tips1", [arr.join(", ")]);
    });

    return () =>
      is_none.value ? null : (
        <div class="bg-[color:var(--other-11)] flex flex-col w-full py-[5px] px-[6px]">
          <div class="flex">
            {icon_name.value && (
              <SvgIcon
                name={icon_name.value}
                color="var(--text-3)"
                class="w-[14px] h-[14px] min-w-[14px] min-h-[14px] inline-block mr-[4px]"
              />
            )}
            <span
              class="text-zinc-600 text-[11px] font-normal font-[DINNextLTPro] leading-snug line-clamp-2"
              v-fontfix={1}
            >
              {tips.value}
            </span>
          </div>
        </div>
      );
  },
});

export const AuthoingStatementCreatorStatementTypeCpnt = (
  props: { creator_statement_type: ComposeNewAuthoringStatementType },
  options: { slots: any; expose: any; emit: any },
) => {
  const { emit } = options;
  const onClick = () => {
    emit("click");
  };

  const renderText = () => {
    if (props.creator_statement_type === ComposeNewAuthoringStatementType.no) {
      return "";
    }
    const text = (
      {
        [ComposeNewAuthoringStatementType.transport_content]: t("authoring_statement_category1_1"),
        [ComposeNewAuthoringStatementType.repost_prohibited]: t("authoring_statement_category1_2"),
        [ComposeNewAuthoringStatementType.repost_allowed]: t("authoring_statement_category1_3"),
      } as any
    )[props.creator_statement_type];

    return (
      <span class="text-[var(--text-3)] leading-[1]" v-fontfix>
        {text}
      </span>
    );
  };

  const renderIcon = () => {
    if (props.creator_statement_type === ComposeNewAuthoringStatementType.no) {
      return "";
    }

    const icon_name = (
      {
        [ComposeNewAuthoringStatementType.transport_content]: "icon-transport",
        [ComposeNewAuthoringStatementType.repost_prohibited]: "icon-prohibit",
        [ComposeNewAuthoringStatementType.repost_allowed]: "icon-repost",
      } as any
    )[props.creator_statement_type];

    return (
      <SvgIcon
        name={icon_name}
        color="var(--text-3)"
        class="w-[12px] h-[12px] inline-block mr-[4px]"
      ></SvgIcon>
    );
  };

  return (
    <div class="flex items-center" onClick={onClick}>
      {renderIcon()}
      {renderText()}
    </div>
  );
};
