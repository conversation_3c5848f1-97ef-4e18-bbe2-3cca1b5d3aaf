<script setup lang="ts">
// cpnts
import Head from "@/components/common/head/index.vue";
import Btns from "@/components/common/btns/index.vue";
import ContainerBody from "@/components/post/compose/container/body/index.vue";
import ContainerFooter from "@/components/post/compose/container/footer/index.vue";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { useKeyboardPopupCompatible } from "@/composables/use-compatible";

// types
import { PlatId, PopCallbackValue } from "packages/types/common";

// utils
import { useComposePostStore } from "@/store/post/compose";
import { useI18n } from "vue-i18n";
import { onBeforeMount, onBeforeUnmount } from "vue";
import router from "@/router";
import { urlSearchObjectify } from "packages/utils/qs";
import { get } from "lodash-es";
import { useUser } from "@/store/user";
import { useTagStore } from "@/store/tag.store";
import { usePlatPop } from "@/components/post/compose/pop/plat";
import { useRoute } from "vue-router";

const { removeTouchMoveEventListener } = useKeyboardPopupCompatible();
const user_store = useUser();
const { show: showDialog } = useDialog();
const tag_store = useTagStore();
const { t } = useI18n();
const route = useRoute();

const compose_store = useComposePostStore();
const { show: showPlatPop } = usePlatPop();

const onClose = () => {
  if (compose_store.is_straightly_close) {
    router.back();
    return;
  }
  showDialog({
    title: t("abandon_the_post_title"),
    content: t("abandon_the_post_content"),
    confirm_text: t("abandon_the_post_confirm_text"),
    cancel_text: t("abandon"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.cancel) {
        compose_store.onResetState();
        close();
        router.back();
        return;
      }
      close();
    },
  });
};

onBeforeMount(async () => {
  user_store.onGetUserGamePlayerInfo();
  tag_store.onLoadAllTagList();

  const post_uuid = get(urlSearchObjectify(), "post_uuid");
  compose_store.onResetState();

  // edit
  if (post_uuid) {
    await compose_store.onLoadPlats();
    await compose_store.onGetPostDetail(post_uuid);
  } else {
    // create
    await compose_store.onLoadPlats();

    const is_recommend_plat = route.query.plate_type === PlatId.recommend;
    if (is_recommend_plat) {
      showPlatPop({
        plat_list: compose_store.plat_list,
        plat_id: compose_store.state.compose_params.plate_id,
        onChange(id: number) {
          compose_store.onChangePlat(id);
        },
      });
    }
  }

  tag_store.onLoadRecommendedTagList({
    limit: 10,
    plate_id: compose_store.state.compose_params.plate_id,
  });
});

onBeforeUnmount(() => {
  removeTouchMoveEventListener();
});
</script>

<template>
  <div
    class="w-full mx-auto box-border flex flex-col overflow-y-auto"
    :style="{
      height: `calc(var(--vh) * 100)`,
    }"
  >
    <Head
      :title="t('create_post')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      class="border-b-[length:0.5px] border-solid border-[color:var(--line-1)] !relative"
      :nogo-back="true"
      @close="onClose"
    >
      <template #icon>
        <span v-click-interceptor.need_login.mute.sign_privacy.stop="compose_store.onCompose">
          <Btns
            :text="t('publish')"
            :type="compose_store.can_publish ? 'primary' : 'disabled'"
            size="m"
          ></Btns>
        </span>
      </template>
    </Head>
    <ContainerBody></ContainerBody>
    <ContainerFooter></ContainerFooter>
  </div>
</template>
