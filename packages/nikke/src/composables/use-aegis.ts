interface UseAegis {
  aegis: any;
  sendPv: () => void;
  logAndReportError: (msg: string, error: Error | any) => void;
}

export const useAegis = (aegis?: any): UseAegis => {
  const win = window.parent || window;
  // @ts-ignore
  aegis = aegis || win?.aegis;

  /**
   * { level: 1, name: '白名单日志' },
   * { level: 2, name: '一般日志' },
   * { level: 4, name: '错误日志' },
   * { level: 8, name: 'Promise 错误' },
   * { level: 16, name: 'Ajax 请求异常' },
   * { level: 32, name: 'JS 加载异常' },
   * { level: 64, name: '图片加载异常' },
   * { level: 128, name: 'css 加载异常' },
   * { level: 256, name: 'console.error' },
   * { level: 512, name: '音视频资源异常' },
   * { level: 1024, name: 'retcode 异常' },
   * { level: 2048, name: 'aegis report' },
   * { level: 4096, name: 'PV' },
   * { level: 8192, name: '自定义事件' },
   * { level: 16384, name: '小程序 页面不存在' },
   * { level: 32768, name: 'websocket错误' },
   * { level: 65536, name: 'js bridge错误' },
   */

  /**
   * @description 统一格式化错误输出
   */
  const getFormatError = (error: Error) =>
    error ? `Error: ${JSON.stringify(error)}\nStack: ${error.stack}\n` : "";

  /**
   * @description 打印和上报错误
   */
  const logAndReportError = (msg: string, error: Error) => {
    if (!aegis) {
      console.warn("[logAndReportError] aegis is not initialized");
      return;
    }

    console.error(msg, error);
    aegis.report({
      msg: msg,
      level: aegis.LogType.AJAX_ERROR,
      ext1: getFormatError(error),
    });
  };

  let origin_fire_url: string;

  /**
   * @description 手动上报 pv
   * @reference aegis sdk 源码：src/plugins/spa.ts
   */
  const sendPv = () => {
    if (!aegis) {
      console.warn("[sendPv] aegis is not initialized");
      return;
    }
    const { pvUrl: pv_url, id } = aegis.config;
    const fired_url = win.location.href + id;

    // 如果 url 并没有发生变化时，没有必要上报
    if (!pv_url || !fired_url || fired_url === origin_fire_url) {
      return;
    }
    aegis.sendPipeline(
      [
        (_log: any, resolve: any) => {
          resolve({
            url: `${pv_url}`,
            type: "pv",
          });
        },
      ],
      "pv",
    )(null);

    // 记录最新 url
    origin_fire_url = fired_url;
  };

  return {
    aegis: aegis || {},
    sendPv,
    logAndReportError,
  };
};
