// cpnts
import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog/index.ts";

// types
import router from "@/router";
import {
  Routes,
  //
  RoutesName,
} from "@/router/routes";
import { CODE_ALL_CONFIGS, CODE_MESSAGE_MAP, CODES_NOT_FOUND } from "packages/configs/code";

// utils
import { useUser } from "@/store/user";
import { t } from "@/locales";
import {
  API_SS_UGC_POST_STAR,
  API_SS_UGC_USER_FOLLOW,
  API_SS_UGC_POST_COLLECTION,
  API_SS_UGC_POST_FORWARD,
  API_SS_UGC_POST_COMMENT,
  API_SS_UGC_CREATE_POST,
  API_SS_UGC_CREATE_POST_NEW,
  API_SS_UGC_COMMENT_STAR,
  API_SS_UGC_CONTENT_REPORT,
  API_USER_CHECK_HAS_LIP_ACCOUNT,
  API_SS_UGC_COS_STS,
  API_SS_UGC_USER_GET_USER_INFO_NEW,
  API_COMMODITY_EXCHANGE,
} from "packages/configs/api";
import { PopCallbackValue } from "packages/types/common";
import { UserLoginStatus } from "packages/types/user";
import { get } from "lodash-es";
import { isInGame, safeJSONParse } from "packages/utils/tools";
import { addParamToCurrentUrl, urlSearchObjectify } from "packages/utils/qs";
import { getToLoginQuery } from "packages/utils/login";

// 需要正确的 token 的 api 列表
const NEED_CORRECT_TOKEN_INTERCEPTOR_APIS = [
  API_SS_UGC_POST_STAR,
  API_SS_UGC_USER_FOLLOW,
  API_SS_UGC_POST_COLLECTION,
  API_SS_UGC_POST_FORWARD,
  API_SS_UGC_POST_COMMENT,
  API_SS_UGC_CREATE_POST,
  API_SS_UGC_CREATE_POST_NEW,
  API_SS_UGC_COMMENT_STAR,
  API_SS_UGC_CONTENT_REPORT,
  API_USER_CHECK_HAS_LIP_ACCOUNT,
  API_SS_UGC_COS_STS,
  API_COMMODITY_EXCHANGE,
];

export const useAuth = () => {
  const checkPermission = async (options: {
    intl_openid?: string;
    code: number;
    api: string;
    config: any;
  }): Promise<boolean> => {
    const user_store = useUser();
    const { show: toast } = useToast();
    const { show: showDialog } = useDialog();

    const { code, api, config } = options;
    const url_search_object = urlSearchObjectify();

    if (
      code === CODE_ALL_CONFIGS.UserNotExist &&
      api === API_SS_UGC_USER_GET_USER_INFO_NEW &&
      !url_search_object.had_reloaded_page
    ) {
      addParamToCurrentUrl({ had_reloaded_page: "1" });
      window.location.reload();
      return false;
    }

    if (
      code === CODE_ALL_CONFIGS.GAME_NOT_LOGIN &&
      // user_store.login_status === UserLoginStatus.logined &&
      ![Routes.LOGOUT, Routes.LOGIN].includes(location.pathname as Routes) &&
      NEED_CORRECT_TOKEN_INTERCEPTOR_APIS.includes(api as any)
    ) {
      if (isInGame()) {
        toast({
          text: t(CODE_MESSAGE_MAP[CODE_ALL_CONFIGS.UserNotExist]),
          type: "error",
          interval: 5000,
        });
        return false;
      }

      user_store.setLoginStatus(UserLoginStatus.unlogin);
      showDialog({
        title: t("warning"),
        content: t("invalid_login_token_tips"),
        confirm_text: t("confirm"),
        cancel_text: t("cancel"),
        // NOTE: 避免被其他弹窗覆盖
        z_index: 100,
        async callback(options: { value: PopCallbackValue; close: () => void }) {
          const { value, close } = options;
          if (value === PopCallbackValue.confirm) {
            router.push({
              name: RoutesName.LOGIN,
              query: getToLoginQuery(),
            });
          }
          close();
        },
      });
      return false;
    }

    if (CODE_ALL_CONFIGS.NO_PERMISSION === code) {
      user_store.user_info.fe_user_no_permission = true;

      toast({
        text: t(CODE_MESSAGE_MAP[code]),
        type: "error",
      });

      router.replace({
        name: RoutesName.ERROR_LOGIN_TIPS,
      });
      return false;
    }

    /**
     * 忽略404跳转
     * 同一个接口在不同的地方调用，有些需要 404，有些不需要 404，比如从首页进入详情页面，返回首页会重新调用详情接口，这个时候就不能 404
     */
    const fe_ignore_not_found_jump =
      safeJSONParse(get(config, "data"))?.fe_ignore_not_found_jump ||
      config?.["fe_ignore_not_found_jump"];

    if (
      //
      CODES_NOT_FOUND.includes(code) &&
      ![Routes.LOGIN].includes(location.pathname as Routes) &&
      !fe_ignore_not_found_jump
    ) {
      router.push({
        name: RoutesName.ERROR_NOT_FOUND,
      });
      return false;
    }

    return true;
  };

  return {
    checkPermission,
  };
};
