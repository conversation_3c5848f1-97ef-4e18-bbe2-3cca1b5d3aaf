import { GameRegion, Role } from "packages/types/games";
import { useGameRegion } from "@/composables/role/use-server-info";
import { getStandardizedGameId } from "packages/utils/standard";
import { getRoleInfo } from "@/api/games";
import { ref } from "vue";

export function useGamesPlus() {
  const selected_server = ref<GameRegion>();
  const saved_role_info = ref<Role>();
  const selected_role_info = ref<Role | null>(null);
  const role_list = ref<Role[]>([]);

  const pending = ref<boolean>();

  const setCurrentRegion = (region: GameRegion) => (selected_server.value = region);
  const setCurrentRole = (role: Role | null) => (selected_role_info.value = role);
  const { server_list } = useGameRegion({ all: false });

  const getRoles = async () => {
    if (!selected_server.value) {
      setCurrentRole(null);
      return [];
    }
    try {
      pending.value = true;
      const { role_list: remote_list } = await getRoleInfo({
        zone_id: 0,
        game_id: getStandardizedGameId(),
        area_id: Number(selected_server.value.area_id),
      });
      role_list.value = [];
      role_list.value = remote_list;
      if (remote_list.length) {
        setCurrentRole(remote_list[0]!);
      }
    } finally {
      pending.value = false;
    }
    return role_list.value;
  };

  return {
    pending,
    selected_server,
    server_list,
    selected_role_info,
    saved_role_info,
    role_list,
    getRoles,
    setCurrentRole,
    setCurrentRegion,
  };
}
