import router from "@/router";
import logger from "packages/utils/logger";

const debug = logger("[router:back:logger]");

export const useRouterBack = () => {
  let timer: any;

  const onCheckSame = () => {
    const { back, current } = router.options.history.state;
    return back === current;
  };

  /**
   * @description 路由返回
   * router action:         replace         push
   * page:           detail   ->    compose  ->   detail
   * router stack:   detail   ->    detail   ->   detail|detail
   */
  const onRouterBack = async () => {
    const is_same_router = onCheckSame();
    debug.log(`[onRouterBack] is_same_router: `, is_same_router);

    if (is_same_router) {
      if (!timer) {
        timer = setInterval(onRouterBack, 20);
      }
      router.back();
      return;
    }

    timer && clearInterval(timer);
    router.back();
  };

  return { onRouterBack };
};
