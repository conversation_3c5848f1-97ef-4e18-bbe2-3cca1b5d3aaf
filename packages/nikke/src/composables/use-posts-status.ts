import { ref, watch } from "vue";
import { PostItem } from "packages/types/post";
import { useRoute } from "vue-router";
import { useGetPost } from "@/api/post.ts";
import { updatePostsData } from "@/utils/home";
import { useLSStorage } from "packages/utils/storage.ts";
import logger from "packages/utils/logger";
import { RoutesName } from "@/router/routes";

const debug = logger("[use-post-status:logger]");

/**
 * 处理动态列表，跳转其他页面，返回时状态的更新
 */
export const usePostsStatus = () => {
  const route = useRoute();
  const clickItem = ref<PostItem>();
  const postList = ref<PostItem[]>([]);
  const { getStorage, removeStorage } = useLSStorage();

  watch(
    () => route.query.post_uuid,
    () => {
      const { name } = route;

      // 回到详情页
      if (
        (name === RoutesName.HOME ||
          name === RoutesName.TOPIC ||
          name === RoutesName.USER ||
          name === RoutesName.SEARCH_RESULT) &&
        clickItem.value
      ) {
        // console.log("back");
        updatePosts();
      }
    },
  );

  const setPostData = (target: PostItem, list: PostItem[]) => {
    clickItem.value = target;
    postList.value = list;
  };

  const updatePosts = async () => {
    const deleteId = getStorage("delete_post_uuid");
    const bool = deleteId === clickItem.value!.post_uuid;

    debug.log(`[updatePosts] deleteId`, deleteId);
    debug.log(`[updatePosts] clickItem`, clickItem.value);
    debug.log(`[updatePosts] bool`, bool);

    // 判断是否被删除了
    if (bool) {
      clickItem.value!.is_deleted = true;
    } else {
      const newPost = await useGetPost.run({
        post_uuid: clickItem.value!.post_uuid,
        // 如果帖子不存在，也不要跳到 404
        fe_ignore_not_found_jump: true,
        browse_post: 2,
        original_content: 0,
      });
      updatePostsData(clickItem.value!, postList.value, newPost);
    }
    removeStorage("delete_post_uuid");
  };

  return {
    setPostData,
  };
};
