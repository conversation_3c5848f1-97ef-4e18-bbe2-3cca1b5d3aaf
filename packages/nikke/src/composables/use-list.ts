import { getUniqueArr } from "packages/utils/tools";
import { reactive } from "vue";

interface Options {
  query?: any;
  list_key?: string;
  total_key?: string;
  list_item_unique_key?: string;
  resolvedList?: (list: any[]) => any[];
  callback?: (data: any) => void;
}

interface State {
  list: any[];
  total: number;
  loading: boolean;
  finished: boolean;
  empty: boolean;
  query: any;
  load_more_interval?: number;
}

export const useList = (api: any, options?: Options) => {
  const getDefaultState = (): State => ({
    list: [],
    total: 0,
    loading: false,
    finished: false,
    empty: false,
    load_more_interval: 500,
    query: Object.assign(
      {},
      {
        page_num: 1,
        page_size: 10,
      },
      options?.query,
    ),
  });

  const { list_key, total_key, list_item_unique_key, resolvedList, callback } = Object.assign({
    list_key: options?.list_key || "list",
    total_key: options?.total_key || "total",
    list_item_unique_key: options?.list_item_unique_key,
    query: getDefaultState().query,
    resolvedList: options?.resolvedList,
    callback: options?.callback,
  });

  const state = reactive(getDefaultState());

  const resetState = () => {
    Object.assign(state, getDefaultState());
  };

  const load = async () => {
    try {
      if (state.finished || state.loading) {
        return;
      }
      state.loading = true;
      const data = await api(state.query);
      const list = resolvedList ? resolvedList(data[list_key]) : data[list_key] || [];
      const total = data[total_key] || 0;

      state.list.push(
        ...(list_item_unique_key ? getUniqueArr(state.list, list, list_item_unique_key) : list),
      );
      state.total = total;
      state.finished = state.list.length >= state.total;
      state.loading = false;
      state.empty = state.total <= 0;

      callback && callback(data);
    } catch (error) {
      console.error("[load] catch error: ", error);

      state.loading = false;
      state.finished = true;
    }
  };

  let last_load_more_time = Date.now();
  const loadMore = async () => {
    const diff = Date.now() - last_load_more_time;
    if (diff < (state.load_more_interval || 500)) {
      return;
    }
    last_load_more_time = Date.now();
    state.query.page_num += 1;
    await load();
  };

  return {
    state,
    load,
    loadMore,
    resetState,
  };
};
