import { ref, watchEffect, type Ref, type ComputedRef } from "vue";
import { useResizeObserver } from "@vueuse/core";

/**
 * 检测元素内容是否被 line-clamp 截断
 * @param element_ref 要检测的元素引用
 * @param is_collapsed 当前是否处于折叠状态
 * @returns 截断状态和手动检测方法
 */
export function useTruncationDetection(
  element_ref: Ref<HTMLElement | undefined> | ComputedRef<HTMLElement | undefined>,
  is_collapsed: Ref<boolean>,
) {
  const can_truncate = ref(false);

  const is_truncated = ref(false);

  // 检测截断
  const checkTruncation = () => {
    const element = element_ref.value;
    if (!element) {
      is_truncated.value = false;
      can_truncate.value = false;
      return;
    }

    const current_truncation = element.scrollHeight > element.clientHeight;
    is_truncated.value = current_truncation;

    // 只有在折叠状态下检测到截断时，才更新 can_truncate
    // 这样展开后我们仍然知道内容原本是否会被截断
    if (is_collapsed.value && current_truncation) {
      can_truncate.value = true;
    }
    // 如果折叠状态下没有截断，说明内容不会被截断
    else if (is_collapsed.value && !current_truncation) {
      can_truncate.value = false;
    }
  };

  useResizeObserver(element_ref, checkTruncation);

  watchEffect(() => {
    checkTruncation();
  });

  return {
    /** 当前是否被截断 */
    is_truncated,
    /** 内容是否会被截断（用于决定是否显示展开/收起按钮） */
    can_truncate,
    /** 手动检测方法 */
    checkTruncation,
  };
}
