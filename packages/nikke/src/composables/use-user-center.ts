import { computed, onActivated, ref } from "vue";
import { useUser } from "@/store/user";
import { ShowType } from "packages/types/setting";
import { useGetUserProfile } from "@/api/user";
import { useGetPrivacySetting } from "@/api/user-privacy";
import { useRoute } from "vue-router";
import { useJsonStore } from "@/shiftyspad/stores/json";

import { storeToRefs } from "pinia";
import {
  // useGetPlayerBasicInfoNoAuth,
  // useGetPlayerBattle,
  useGetUserGamePlayerInfo,
  useSetUserGameTag,
  // useModifyPlayerTag,
} from "@/api/user-games";
import { tags_config } from "@/utils/player-tags";
import { t } from "@/locales";
import { base64Decode, isOriginalOpenId } from "packages/utils/encrypt";
import { NO_VALUE_OR_ZERO, VALUE_IS_HIDDEN } from "@/shiftyspad/const/setting";

const parseJson = <T = unknown>(str: string | undefined) => {
  try {
    return JSON.parse(str ?? "") as T;
  } catch (error) {
    return null;
  }
};

export const useUserCenter = () => {
  const route = useRoute();
  const intl_openid_encrypted = computed(() => route.query.openid as string | undefined);

  const getIntlOpenid = () => {
    return intl_openid_encrypted.value && isOriginalOpenId(intl_openid_encrypted.value)
      ? intl_openid_encrypted.value
      : base64Decode(intl_openid_encrypted.value);
  };

  // 使用 ref 记录，避免 deactivate 的时候，路径变化导致的变动
  const intl_openid = ref<string | undefined>(getIntlOpenid());

  onActivated(() => {
    intl_openid.value = getIntlOpenid();
  });

  const user = useUser();

  // 目标用户信息
  const {
    data: user_info_by_openid,
    isLoading: loading,
    refetch: refetchUserInfoByOpenid,
  } = useGetUserProfile(
    computed(() => ({ intl_openid: intl_openid.value })),
    { enabled: computed(() => !!intl_openid.value) },
  );

  // 隐私设置
  const {
    data: privacy_settings,
    refetch: refetchPrivacySettings,
    isLoading: privacy_loading,
  } = useGetPrivacySetting(
    computed(() => ({ intl_openid: intl_openid.value! })),
    { enabled: computed(() => !!intl_openid.value) },
  );

  // // 玩家战绩
  // const { data: player_battle } = useGetPlayerBattle(
  //   computed(() => ({ intl_openid: intl_openid.value! })),
  //   { enabled: computed(() => !!intl_openid.value) },
  // );

  // 玩家基本信息
  const {
    data: player_info,
    refetch: refetchPlayerInfo,
    isLoading: player_loading,
  } = useGetUserGamePlayerInfo(
    computed(() => ({ intl_openid: intl_openid.value! })),
    { enabled: computed(() => !!intl_openid.value) },
  );

  const { getHardProgress, getNormalProgress } = useGameProgressData();

  const normalizeTagValue = (game_tag: number, game_tag_num: number | undefined) => {
    if (game_tag === 2) return getNormalProgress(game_tag_num || 0);
    if (game_tag === 3) return getHardProgress(game_tag_num || 0);
    if (game_tag_num === NO_VALUE_OR_ZERO) return 0;
    return game_tag_num ?? 0;
  };

  const user_tags = computed(() => {
    return tags_config.map((item) => {
      const { i18n } = item;
      const value =
        {
          tower: normalizeTagValue(1, player_info.value?.tower_floor),
          battle_normal: normalizeTagValue(2, player_info.value?.normal_progress),
          battle_hard: normalizeTagValue(3, player_info.value?.hard_progress),
          nikkes: normalizeTagValue(4, player_info.value?.own_nikke_cnt),
          frames: normalizeTagValue(5, player_info.value?.avatar_frame),
          costumes: normalizeTagValue(6, player_info.value?.costume),
        }[item.key] ?? 0;
      return {
        ...item,
        value,
        title: item.key.startsWith("battle_")
          ? upperCaseFirst(t("battle"))
          : upperCaseFirst(t(i18n)),
        label: item.key.startsWith("battle_") ? (item.key.split("_")[1]?.toUpperCase() ?? "") : "",
        // checked: player_info.value?.tag_id === item.id,
      };
    });
  });

  // 修改玩家标签
  const { mutateAsync: modifyPlayerTag } = useSetUserGameTag({
    onSuccess: () => refetchUserInfoByOpenid(),
  });

  /** 目标用户信息（如果地址栏中没有openid，则返回登陆用户信息） */
  const user_info = computed(() => {
    return intl_openid.value ? user_info_by_openid.value?.info : user.user_info;
  });

  /** 目标用户第三方链接汇总 */
  const channel_links = computed(() => {
    const links = parseJson<{ channel_name: string; url: string }[]>(
      user_info.value?.home_page_links,
    );
    return [
      { channel_name: "x", icon: "icon-x", url: "" },
      { channel_name: "youtube", icon: "icon-youtube", url: "" },
      { channel_name: "p", icon: "icon-p", url: "" },
      { channel_name: "lt", icon: "icon-lt", url: "" },
      { channel_name: "dy", icon: "icon-dy", url: "" },
    ].map((channel) => {
      const target = links?.find((item) => item.channel_name === channel.channel_name);
      if (target?.url) {
        return { ...channel, url: target.url };
      }
      return channel;
    });
  });

  /** 当前用户中心页面，是否是自己（如果地址中没有openid，则默认是自己的页面） */
  const is_self = computed(() => {
    return !intl_openid.value || user.user_info?.intl_openid === intl_openid.value;
  });

  return {
    user_info,
    loading: intl_openid.value ? loading : computed(() => user.loading),
    privacy_loading,
    privacy_settings: privacy_settings.value,
    refetchPrivacySettings,
    refetchUserInfo: () => {
      if (intl_openid.value) return refetchUserInfoByOpenid();
      user.refetchUserInfo();
    },
    channel_links,
    intl_openid: computed(() => intl_openid.value || user_info.value?.intl_openid),
    is_show: (key: keyof ShowType) => {
      if (is_self.value) return true;
      return !!privacy_settings.value?.[key];
    },
    is_self,
    player_info,
    refetchPlayerInfo,
    player_loading,
    user_tags,
    modifyPlayerTag,
    normalizeTagValue,
  };
};

const upperCaseFirst = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const useGameProgressData = () => {
  const {
    is_stage_loading,
    stage_list_hard: hard,
    stage_list: normal,
  } = storeToRefs(useJsonStore());
  return {
    loading: is_stage_loading,
    getHardProgress: (hard_progress: number) => {
      const stage_list_hard = hard.value;
      const remain = hard_progress || 0;
      if (remain === NO_VALUE_OR_ZERO || remain === 0 || !stage_list_hard) {
        return "0-0";
      } else if (remain === VALUE_IS_HIDDEN) {
        return "-";
      } else {
        const stage = stage_list_hard?.filter((s) => s.id === remain)[0];
        if (stage) {
          const name = stage.name_localkey.name;
          return name.replace("STAGE", "").replace("BOSS", "").replace("HARD", "").trim();
        } else {
          // 没有的展示当前json数据最后一关
          const lastStage = stage_list_hard[stage_list_hard.length - 1];
          const name = lastStage?.name_localkey.name;
          return name?.replace("STAGE", "").replace("BOSS", "").trim() ?? "-";
        }
      }
    },
    getNormalProgress: (normal_progress: number) => {
      const stage_list = normal.value;
      const remain = normal_progress || 0;
      if (remain === NO_VALUE_OR_ZERO || remain === 0 || !stage_list) {
        return "0-0";
      } else if (remain === VALUE_IS_HIDDEN) {
        return "-";
      } else {
        const stage = stage_list?.filter((s) => s.id === remain)[0];
        if (stage) {
          const name = stage.name_localkey.name;
          return name.replace("STAGE", "").replace("BOSS", "").trim();
        } else {
          // 没有的展示当前json数据最后一关
          const lastStage = stage_list[stage_list.length - 1];
          const name = lastStage?.name_localkey.name;
          return name?.replace("STAGE", "").replace("BOSS", "").trim() ?? "-";
        }
      }
    },
  };
};
