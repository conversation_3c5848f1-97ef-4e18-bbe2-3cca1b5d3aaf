import { computed, Ref, ref } from "vue";

export const useInfiniteList = <T extends Record<string, any>>(config: {
  queryFn: (ctx: {
    /** 从 1 开始 */
    page_num: number;
    page_size: number;
    total: number;
    offset: number;
    limit: number;
    next_page_cursor: string | undefined;
    previous_page_cursor: string | undefined;
    /** 是否清空列表, 默认 false */
    clear?: boolean;
  }) => Promise<SupportedPageResult<T>>;
  item_key: ItemKey<T> | ((item: T) => string | number);
  /** 默认 10 */
  page_size?: number;
  /** 是否去重, 默认 true */
  remove_duplicate?: boolean;
  /** 是否立即加载, 默认 true */
  immediate?: boolean;
  /** 加载更多间隔时间, 默认 500ms */
  interval?: number;
}) => {
  const list: Ref<T[]> = ref([]);
  const total = ref<number>(0);
  const loading = ref<boolean>(false);
  const finished = ref<boolean>(false);
  const next_page_cursor = ref(undefined as string | undefined);
  const previous_page_cursor = ref(undefined as string | undefined);
  /** 从 1 开始 */
  const page_num = ref<number>(1);
  const page_size = ref<number>(config.page_size || 10);

  const last_loaded_time = ref<number>(0);

  const item_key_set = new Set<string | number>();

  const empty = computed(() => list.value.length === 0 && !loading.value);

  const getItemKey = (item: T) => {
    if (typeof config.item_key === "function") {
      return config.item_key(item);
    } else {
      return item[config.item_key] as string | number;
    }
  };

  const getList = async (params?: { clear?: boolean }) => {
    if (loading.value || finished.value) return;
    if (Date.now() - last_loaded_time.value < (config.interval ?? 500)) return;

    loading.value = true;
    try {
      const {
        list: res_list,
        total: res_total,
        is_finish,
        next_page_cursor: next_cursor,
        previous_page_cursor: previous_cursor,
      } = resolvePageResult(
        await config.queryFn({
          page_num: page_num.value,
          page_size: page_size.value,
          total: total.value,
          offset: (page_num.value - 1) * page_size.value,
          limit: page_size.value,
          next_page_cursor: next_page_cursor.value,
          previous_page_cursor: previous_page_cursor.value,
          clear: params?.clear,
        }),
      );
      if (params?.clear) {
        list.value = [];
        item_key_set.clear();
      }
      if (config.remove_duplicate ?? true) {
        const new_list = [...list.value];
        res_list.forEach((item) => {
          const key = getItemKey(item);
          if (!item_key_set.has(key)) {
            new_list.push(item);
            item_key_set.add(key);
          }
        });
        list.value = new_list;
      } else {
        list.value = [...list.value, ...res_list];
      }
      last_loaded_time.value = Date.now();
      if (res_total !== undefined) total.value = res_total;
      next_page_cursor.value = next_cursor || undefined;
      previous_page_cursor.value = previous_cursor || undefined;
      page_num.value += 1;
      finished.value =
        is_finish ??
        // TODO: 这个判断有问题，如果最后一页 res_list.length === page_size.value, 也会继续加载
        res_list.length < page_size.value;
    } catch (error) {
      last_loaded_time.value = Date.now();
    }
    loading.value = false;
  };

  const load = () => getList();

  const reset = () => {
    total.value = 0;
    page_num.value = 1;
    page_size.value = config.page_size || 10;
    loading.value = false;
    finished.value = false;
    next_page_cursor.value = undefined;
    previous_page_cursor.value = undefined;
    getList({ clear: true });
  };

  if (config.immediate ?? true) reset();

  return { list, total, empty, loading, finished, page_num, page_size, load, reset };
};

export type ItemKey<T extends Record<string, any>> = {
  [key in keyof T]: T[key] extends string | number ? key : never;
}[keyof T];

/** 所有内置支持的分页格式 */
export type SupportedPageResult<T extends Record<string, any>> =
  | PageResultWithPageInfo<T>
  | PageResultWithTotal<T>;

export type PageResultWithTotal<T extends Record<string, any>> = {
  list: T[];
  total?: number;
  is_finish: boolean;
};

export type PageResultWithPageInfo<T extends Record<string, any>> = {
  list: T[];
  page_info: {
    previous_page_cursor?: string;
    next_page_cursor?: string;
    is_finish: boolean;
  };
};

/** 解析分页结果 */
export const resolvePageResult = <T extends Record<string, any>>(
  res: SupportedPageResult<T>,
): {
  list: T[];
  total?: number;
  is_finish?: boolean;
  next_page_cursor?: string;
  previous_page_cursor?: string;
} => {
  if ("page_info" in res) {
    let is_finish = res.page_info.is_finish;
    // 处理部分完全没有数据的情况下，is_finish 返回仍然为 false 的情况
    if (
      res.list.length === 0 &&
      !res.page_info.next_page_cursor &&
      !res.page_info.previous_page_cursor
    ) {
      is_finish = true;
    }
    return {
      list: res.list,
      is_finish: is_finish,
      next_page_cursor: res.page_info.next_page_cursor,
      previous_page_cursor: res.page_info.previous_page_cursor,
    };
  }

  if ("total" in res || "is_finish" in res) {
    return {
      list: res.list,
      total: res.total,
      is_finish: res.is_finish,
    };
  }
  throw new Error("unsupported page format");
};
