import { useApiLogout } from "@/api/login";
import router from "@/router";
import { Routes } from "@/router/routes";
import { useUser } from "@/store/user";
import { STORAGE_LS_LOGIN_META } from "packages/configs/storage";
import { UserLoginStatus } from "packages/types/user";
import { getToLoginQuery } from "packages/utils/login";
import { useLSStorage } from "packages/utils/storage";

export const useLogout = () => {
  const user_store = useUser();
  const { removeStorage } = useLSStorage();

  const logout = async () => {
    await useApiLogout.run({});
    user_store.setIsLogin(false);
    user_store.setLoginStatus(UserLoginStatus.unlogin);
    removeStorage(STORAGE_LS_LOGIN_META);
    // setStandardizedCookie(STORAGE_COOKIE_GAME_ID, "");

    const to_login_query = getToLoginQuery();
    // 避免重复横跳
    if (to_login_query.to === Routes.LOGOUT) {
      to_login_query.to = Routes.HOME;
    }

    await router.replace({
      path: Routes.LOGIN,
      query: to_login_query,
    });
    window.location.reload();
  };
  return {
    logout,
  };
};
