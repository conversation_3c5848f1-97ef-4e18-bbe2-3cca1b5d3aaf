import { slideLeft, slideRight, slideBottom, slideTop, useMotion } from "@vueuse/motion";
import { Position } from "packages/types/common";
import { ComputedRef, Ref, computed, onBeforeUnmount, onMounted, ref } from "vue";

const SLIDE_DISTANCE = 30;

interface Options {
  slide_position?: Position;
  touch_config?: TouchPosition;
}

type OptionsCallback = (options?: Options) => void;
interface TouchPosition {
  x_start: number;
  x_end: number;
  y_start: number;
  y_end: number;
}

export const useSlide = (
  el: Ref<HTMLElement>,
  options: {
    distance?: number;
    position?: Position;
    callback?: OptionsCallback;
    touchstart?: (e: TouchEvent, options?: Options) => void;
    touchmove?: (e: TouchEvent, options?: Options) => void;
    touchend?: (e: TouchEvent, options?: Options) => void;
  },
) => {
  const proxy_position = computed(() => options.position || Position.left);
  const distance = options.distance || SLIDE_DISTANCE;

  let touch_config: Ref<TouchPosition> = ref({
    x_start: 0,
    x_end: 0,
    y_start: 0,
    y_end: 0,
  });

  const is_left_slide = computed(
    () => touch_config.value.x_start - touch_config.value.x_end > distance,
  );

  const is_right_slide = computed(
    () => touch_config.value.x_end - touch_config.value.x_start > distance,
  );

  const is_top_slide = computed(
    () => touch_config.value.y_start - touch_config.value.y_end > distance,
  );

  const is_buttom_slide = computed(
    () => touch_config.value.y_end - touch_config.value.y_start > distance,
  );

  const is_horizontal_slide = computed(() => is_left_slide.value || is_right_slide.value);
  const is_vertical_slide = computed(() => is_top_slide.value || is_buttom_slide.value);

  const slide_position: ComputedRef<Position> = computed(() => {
    if (is_left_slide.value) return Position.left;
    if (is_right_slide.value) return Position.right;
    if (is_top_slide.value) return Position.top;
    if (is_buttom_slide.value) return Position.bottom;
    return Position.left;
  });

  const isDistanceFulfilled = () => {
    const handler = {
      // slide left
      [Position.left]: () => (is_vertical_slide.value ? false : is_left_slide.value),
      // slide right
      [Position.right]: () => (is_vertical_slide.value ? false : is_right_slide.value),
      // slide top
      [Position.top]: () => (is_horizontal_slide.value ? false : is_top_slide.value),
      // slide down
      [Position.bottom]: () => (is_horizontal_slide.value ? false : is_buttom_slide.value),
    }[proxy_position.value];

    return handler?.();
  };

  const onTouchStart = (e: TouchEvent) => {
    touch_config.value.x_start = e.touches[0].clientX;
    touch_config.value.y_start = e.touches[0].clientY;

    options.touchstart?.(e);
  };

  const onTouchMove = (e: TouchEvent) => {
    touch_config.value.x_end = e.touches[0].clientX;
    touch_config.value.y_end = e.touches[0].clientY;

    options.touchmove?.(e, { touch_config: touch_config.value });
  };

  const onTouchEnd = (e: TouchEvent) => {
    touch_config.value.x_end = e.changedTouches[0].clientX;
    touch_config.value.y_end = e.changedTouches[0].clientY;

    options.touchend?.(e);

    if (options.position) {
      isDistanceFulfilled() && options.callback?.();
      return;
    }

    options.callback?.({
      slide_position: slide_position.value,
      touch_config: touch_config.value,
    });
  };

  onMounted(() => {
    el.value.addEventListener("touchstart", onTouchStart);
    el.value.addEventListener("touchmove", onTouchMove);
    el.value.addEventListener("touchend", onTouchEnd);
  });

  onBeforeUnmount(() => {
    el.value.removeEventListener("touchstart", onTouchStart);
    el.value.removeEventListener("touchmove", onTouchMove);
    el.value.removeEventListener("touchend", onTouchEnd);
  });
};

export const useSlideMotion = (el: Ref<HTMLElement>, options: { position: Position }) => {
  const proxy_position = computed(() => options.position || Position.left);

  const getSlidePosition = () => {
    return (
      {
        [Position.left]: slideLeft,
        [Position.right]: slideRight,
        [Position.bottom]: slideBottom,
        [Position.top]: slideTop,
      }[proxy_position.value] || slideLeft
    );
  };

  useMotion(el, getSlidePosition());
};
