// configs
import { STANDARD_CMS_LANG_MAP } from "packages/configs/standard";

// types
import { DropdownItem } from "packages/types/dropdown";

// utils
import { computed, ref } from "vue";
import { STORAGE_COOKIE_LANG } from "packages/configs/storage";
import { report } from "packages/utils/tlog.ts";
import router from "@/router";
import {
  getStandardizedLang,
  getStandardizedUrlLang,
  setStandardizedCookie,
} from "packages/utils/standard";

// const storage = useLSStorage();
export const useLang = () => {
  const lang_list = ref([
    { name: "English", value: STANDARD_CMS_LANG_MAP.en },
    { name: "日本語", value: STANDARD_CMS_LANG_MAP.ja },
    { name: "한국어", value: STANDARD_CMS_LANG_MAP.ko },
    // { name: "简体中文", value: STANDARD_CMS_LANG_MAP.zh },
    { name: "繁體中文", value: STANDARD_CMS_LANG_MAP.tw },
  ]);

  const lang = ref(getStandardizedLang());

  const current_lang = computed(() => lang_list.value.find((item) => item.value === lang.value)!);

  const onLangChange = async (lang_item: DropdownItem) => {
    await report.standalonesite_lang_btn
      .cm_click({
        cur_lang: lang.value,
        dst_name: lang_item.value as string,
      })
      .catch((err) => {
        console.log("onLangChange err", err);
      });

    setAppLang(lang_item.value as string);
    // 如果地址中存在 lang, 替换这个参数
    if (getStandardizedUrlLang()) {
      await router.replace({
        query: {
          ...router.currentRoute.value.query,
          lang: lang_item.value,
        },
      });
    }

    window.location.reload();
  };

  const setAppLang = (new_lang: string) => {
    lang.value = new_lang;
    setStandardizedCookie(STORAGE_COOKIE_LANG, new_lang);
  };

  return {
    lang_list,
    current_lang,
    onLangChange,
    setAppLang,
  };
};
