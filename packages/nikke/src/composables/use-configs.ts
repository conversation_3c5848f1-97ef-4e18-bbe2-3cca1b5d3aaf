// congis
import {
  INTL_GLOBAL_ENV,
  INTL_GLOBAL_GAME_ID,
  INTL_HMT_ENV,
  INTL_HMT_GAME_ID,
} from "packages/configs/intl";
// utils
import { t } from "@/locales";
import { computed, ComputedRef } from "vue";
import { getStandardizedGameId } from "packages/utils/standard";

interface AreaItem {
  value: string;
  game_id: string;
  label: string;
}

export const useConfigs = () => {
  const login_areas: ComputedRef<AreaItem[]> = computed(() => [
    { value: INTL_HMT_ENV, label: t("nikke_area_29157"), game_id: INTL_HMT_GAME_ID },
    { value: INTL_GLOBAL_ENV, label: t("nikke_area_29080"), game_id: INTL_GLOBAL_GAME_ID },
  ]);

  const fingLoginAreaByGameId = (game_id: string) => {
    return login_areas.value.find((area: AreaItem) => `${area.game_id}` === game_id);
  };

  const getCurrentLoginAreaConfig = (game_id: undefined | string = getStandardizedGameId()) => {
    return login_areas.value.find((area: AreaItem) => `${area.game_id}` === game_id);
  };

  const getAnotherLoginAreaConfig = (game_id: undefined | string = getStandardizedGameId()) => {
    return login_areas.value.find((area: AreaItem) => `${area.game_id}` !== game_id);
  };

  return {
    login_areas,
    fingLoginAreaByGameId,
    getCurrentLoginAreaConfig,
    getAnotherLoginAreaConfig,
  };
};
