/**
 * 适用于弹窗时，禁止底部容器的滚动
 * @param element 禁用的容器，默认body
 * @param options 禁用方向，支持横竖向(x,y)，默认竖向(y)
 * @returns
 */
export const useLockScroll = (
  element: HTMLElement = document.body,
  options = { x: false, y: true },
) => {
  const { x, y } = options;
  // 记录初始值
  let stateX = element?.style?.overflowX;
  let stateY = element?.style?.overflowY;
  // 如果容器元素晚于调用useLockScroll()，可用init重新赋值
  const init = () => {
    stateX = element.style.overflowX;
    stateY = element.style.overflowY;
  };

  const lock = () => {
    if (x) {
      element.style.overflowX = "hidden";
    }
    if (y) {
      element.style.overflowY = "hidden";
    }
  };

  const unlock = () => {
    if (x) {
      element.style.overflowX = stateX;
    }
    if (y) {
      element.style.overflowY = stateY;
    }
  };
  return {
    init,
    lock,
    unlock,
  };
};
