import { getUserCollection, setUserCompleteCollection } from "@/api/rewards";
import { CollectionStatus, Task } from "packages/types/rewards";

import { t } from "@/locales";
import { useToast } from "@/components/ui/toast";
const { show } = useToast();

export const useCollection = () => {
  const checkCollection = async (task: Task) => {
    // TODO: frontend cache
    const { status } = await getUserCollection({ task_id: task.task_id });
    if (status === CollectionStatus.complete) {
      setUserCompleteCollection({ task_id: task.task_id });
      show(t("daily_collection_tips"));
    }
  };

  return {
    checkCollection,
  };
};
