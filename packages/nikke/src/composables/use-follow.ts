import { useFollowUser } from "@/api/user";
import { useDialog } from "@/components/ui/dialog";
import { useUser } from "@/store/user";
import { Ref } from "vue";
import { useI18n } from "vue-i18n";
import { PopCallbackValue } from "packages/types/common";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";

/**
 * 处理主客态的关注列表、被关注列表的交互
 */
export const useFollow = (
  info: Ref<{
    openid: string;
    is_follow: boolean;
    is_mutual_follow: boolean;
    onSuccess?: () => void;
  }>,
) => {
  const { t } = useI18n();
  const user = useUser();
  const { show } = useDialog();
  const { mutateAsync: follow } = useFollowUser({
    onSuccess: (data) => {
      event_emitter.emit(EVENT_NAMES.user_status_change, {
        intl_openid: info.value.openid,
        is_followed: data.is_follow ? 1 : 0,
        is_mutual_follow: data.is_mutual_follow ? 1 : 0,
      });
      info.value.onSuccess?.();
    },
  });

  const handleFollow = async () => {
    if (info.value.openid === user.user_info?.intl_openid) return;

    if (!info.value.is_follow) return follow({ intl_openid: info.value.openid });

    return new Promise((resolve) => {
      show({
        title: t("unfollowed"),
        content: t("are_you_sure_to_unfollow"),
        confirm_text: t("keep_follow"),
        cancel_text: t("unfollow"),
        async callback(options: { value: PopCallbackValue; close: Function }) {
          const { value, close } = options;
          // 确认取消关注
          if (value === PopCallbackValue.cancel) {
            await follow({ intl_openid: info.value.openid });
          }
          close?.();
          resolve(null);
        },
      });
    });
  };

  return { handleFollow };
};
