import { useSetBlackUser } from "@/api/user";
import { useDialog } from "@/components/ui/dialog";
import { t } from "@/locales";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";

export const useUserBlock = () => {
  const { show: showDialog } = useDialog();

  const { mutateAsync: setBlackUser } = useSetBlackUser();

  const handleUnblock = (intl_openid: string) => {
    return new Promise<boolean>((resolve) => {
      showDialog({
        title: t("unblock_this_user"),
        content: t("unblock_this_user_desc"),
        confirm_text: t("confirm"),
        cancel_text: t("cancel"),
        callback: async (options) => {
          if (options.value === "confirm") {
            await setBlackUser({
              to_intl_openid: intl_openid,
              operate_type: 1,
            });
            event_emitter.emit(EVENT_NAMES.user_status_change, {
              intl_openid,
              is_black: 0,
              is_followed: 0,
              is_mutual_follow: 0,
            });
            options.close();
            resolve(true);
          } else {
            options.close();
            resolve(false);
          }
        },
      });
    });
  };

  const handleBlock = (intl_openid: string) => {
    return new Promise<boolean>((resolve) => {
      showDialog({
        title: t("block_this_user"),
        content: t("block_this_user_desc"),
        confirm_text: t("confirm"),
        cancel_text: t("cancel"),
        callback: async (options) => {
          if (options.value === "confirm") {
            await setBlackUser({
              to_intl_openid: intl_openid,
              operate_type: 0,
            });
            event_emitter.emit(EVENT_NAMES.user_status_change, {
              intl_openid,
              is_black: 1,
              is_followed: 0,
              is_mutual_follow: 0,
            });
            options.close();
            resolve(true);
          } else {
            options.close();
            resolve(false);
          }
        },
      });
    });
  };

  return {
    handleUnblock,
    handleBlock,
  };
};
