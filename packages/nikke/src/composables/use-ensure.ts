import { urlSearchObjectify } from "packages/utils/qs";
import qs from "query-string";
import { isSameOrigin } from "packages/utils/tools";
import { CONST_URL_WHITE_LIST_KEY } from "packages/configs/const";

export interface To {
  query: Record<string, Object>;
}

export enum EnsureType {
  router = "router",
  location = "location",
}

export const useEnsureUrl = () => {
  const ensureUrlKeepWhiteList = (to: To) => {
    const qs_url_search = urlSearchObjectify();
    const white_list = CONST_URL_WHITE_LIST_KEY;

    white_list.forEach((key: string) => {
      if (qs_url_search[key] && !to.query[key]) {
        Object.assign(to.query, {
          [key]: qs_url_search[key],
        });
      }
    });
  };

  type EnsureArgs<T extends EnsureType> = T extends EnsureType.location
    ? [type: T, value: string]
    : [type: T, value: To];

  const ensure = <T extends EnsureType>(...args: EnsureArgs<T>) => {
    const [type, value] = args;

    if (type === EnsureType.location && value) {
      if (!isSameOrigin(value)) {
        return value;
      }

      const parse_url = qs.parseUrl(value);
      const to = Object.assign({ query: {} });

      Object.assign(to.query, parse_url.query);

      ensureUrlKeepWhiteList(to);

      Object.assign(parse_url.query, to.query);
      const ret = qs.stringifyUrl(parse_url);
      return ret;
    }

    if (type === EnsureType.router) {
      ensureUrlKeepWhiteList(value);
      return value;
    }

    throw new Error("ensure type error");
  };

  return {
    ensure,
    ensureUrlKeepWhiteList,
  };
};
