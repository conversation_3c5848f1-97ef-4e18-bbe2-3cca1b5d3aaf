import { isMobileDevice, isLandscape } from "packages/utils/tools";
import { useScreenOrientation } from "@vueuse/core";
import { Ref, onBeforeUnmount, onMounted, ref, computed, ComputedRef } from "vue";

export interface UseResponsiveParams {
  screenChangeCallback?: () => void;
}

export const useResponsive = (
  params?: UseResponsiveParams,
): {
  is_mobile: Ref<boolean>;
  is_landscape: Ref<boolean>;
  new_is_landscape: ComputedRef<boolean>;
} => {
  let timer: ReturnType<typeof setTimeout>;

  const { screenChangeCallback } = params || {};
  const { isSupported, orientation } = useScreenOrientation();
  const win = window.parent || window;
  const is_mobile = ref(isMobileDevice());
  const last_width = ref<number>(win.innerWidth);
  const is_landscape = ref(isLandscape());

  const isSupportScreenOrientation = () => typeof win.screen.orientation !== "undefined";

  /**
   * 判断设备是否横竖屏幕
   */
  const checkScreenOrientation = () => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      if (last_width.value === win.innerWidth) {
        // 宽度没变，可能是唤起键盘导致的事件触发
        // 不是切换横竖屏事件
        return;
      }
      last_width.value = win.innerWidth;
      is_landscape.value = isLandscape();
      is_mobile.value = isMobileDevice();

      screenChangeCallback?.();

      // console.log(
      //   `[checkScreenOrientation] is_landscape: ${is_landscape.value}, is_mobile: ${is_mobile.value}`
      // );
    }, 500);
  };

  const onVisibleChange = () => {
    last_width.value = win.innerWidth;
    is_landscape.value = isLandscape();
    is_mobile.value = isMobileDevice();

    screenChangeCallback?.();

    // console.log(
    //   `[onVisibleChange] is_landscape: ${is_landscape.value}, is_mobile: ${is_mobile.value}`
    // );
  };

  const register = () => {
    document.addEventListener("visibilitychange", onVisibleChange);
    //
    isSupportScreenOrientation()
      ? win.screen.orientation.addEventListener("change", checkScreenOrientation)
      : /**
         * @deprecated 非标准做法，未来可能会废弃
         * @link https://developer.mozilla.org/en-US/docs/Web/API/Window/orientationchange_event
         */
        win.addEventListener("orientationchange", checkScreenOrientation);
  };

  const new_is_landscape = computed(() => {
    if (!isSupported.value || !isMobileDevice()) return is_landscape.value;
    return orientation.value?.includes("landscape") ?? is_landscape.value;
  });

  const unRegister = () => {
    document.removeEventListener("visibilitychange", onVisibleChange);
    //
    isSupportScreenOrientation()
      ? win.screen.orientation.removeEventListener("change", checkScreenOrientation)
      : win.removeEventListener("orientationchange", checkScreenOrientation);
  };

  onMounted(register);
  onBeforeUnmount(unRegister);

  return {
    is_mobile,
    is_landscape,
    new_is_landscape,
  };
};
