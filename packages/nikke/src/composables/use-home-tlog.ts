import { useHomeStore } from "@/store/home/<USER>";
import { report } from "packages/utils/tlog";
import { onBeforeMount, ref } from "vue";

/**
 * 首页几个子页面的加载时长上报
 */
export const useHomeSubPageLoadTlog = () => {
  const on_before_mount_time = ref<number | null>(null);

  const home_store = useHomeStore();

  onBeforeMount(() => {
    on_before_mount_time.value = Math.floor(performance.now());
  });

  const reportSubPageLoadTime = () => {
    if (on_before_mount_time.value) {
      const time_cost = Math.floor(performance.now()) - on_before_mount_time.value;
      console.log(`Home Sub Page (${home_store.activeKey}) Load Time: ${time_cost}ms`);
      report.standalonesite_sub_page_loading.cm_vshow({
        du: time_cost,
        plate: home_store.activeKey,
      });
      on_before_mount_time.value = null;
    }
  };

  return {
    reportSubPageLoadTime,
  };
};
