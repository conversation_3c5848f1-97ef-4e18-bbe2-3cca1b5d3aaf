import { ref, computed, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import { route_transition_direction } from "@/router";
import type {
  TransitionDirection,
  TransitionState,
} from "@/components/common/route-transition/types";

export function useRouteTransition() {
  const route = useRoute();

  // 过渡状态
  const transitionState = ref<TransitionState>({
    direction: "none",
    isTransitioning: false,
    prevComponent: null,
    currentComponent: null,
  });

  // 过渡方向
  const direction = computed(() => route_transition_direction.value);

  // 重置过渡方向
  const resetDirection = () => {
    route_transition_direction.value = "none";
  };

  // 设置过渡方向
  const setDirection = (dir: TransitionDirection) => {
    route_transition_direction.value = dir;
  };

  // 开始过渡
  const startTransition = (prevComponent: any, currentComponent: any) => {
    transitionState.value = {
      direction: direction.value,
      isTransitioning: true,
      prevComponent,
      currentComponent,
    };
  };

  // 结束过渡
  const endTransition = () => {
    transitionState.value.isTransitioning = false;
    transitionState.value.prevComponent = null;
    resetDirection();
  };

  // 获取过渡类名
  const getTransitionClasses = (isEntering: boolean) => {
    const dir = transitionState.value.direction;

    if (dir === "none") return "";

    if (dir === "forward") {
      // 前进：新页面从右侧滑入，旧页面向左滑出
      return isEntering
        ? "slide-enter-from-right slide-enter-to-center"
        : "slide-leave-from-center slide-leave-to-left";
    } else {
      // 后退：新页面从左侧滑入（实际是露出），旧页面向右滑出
      return isEntering
        ? "slide-enter-from-left slide-enter-to-center"
        : "slide-leave-from-center slide-leave-to-right";
    }
  };

  // 监听路由变化
  watch(
    () => route.path,
    () => {
      nextTick(() => {
        resetDirection();
      });
    },
  );

  return {
    transitionState,
    direction,
    startTransition,
    endTransition,
    getTransitionClasses,
    resetDirection,
    setDirection,
  };
}
