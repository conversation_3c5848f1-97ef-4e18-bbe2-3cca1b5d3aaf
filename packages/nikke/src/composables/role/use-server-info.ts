import { useRegionList } from "@/api/games";
import { promiseWrap } from "@/shiftyspad/utils/comm";

import { GameRegion } from "packages/types/games";
import { getStandardizedGameId } from "packages/utils/standard";
import { INTL_GLOBAL_GAME_ID, INTL_HMT_GAME_ID } from "packages/configs/intl";

import { computed, ref } from "vue";

const regions_hmt = ref<{ area_list: GameRegion[] } | null>(null);
const regions_global = ref<{ area_list: GameRegion[] } | null>(null);
const init_data = promiseWrap(async () => {
  if (regions_hmt.value?.area_list.length || regions_global.value?.area_list.length) {
    return Promise.resolve();
  }
  const [hmt_list, global_list] = await Promise.all([
    useRegionList.run({ game_id: INTL_HMT_GAME_ID }),
    useRegionList.run({ game_id: INTL_GLOBAL_GAME_ID }),
  ]);
  regions_hmt.value = hmt_list;
  regions_global.value = global_list;
});

export const useGameRegion = (params?: { all?: boolean }) => {
  const current_game_id = getStandardizedGameId();
  const { all = true } = params ?? {};
  const server_list = (() => {
    if (all) {
      return computed(() => [
        ...(regions_hmt.value?.area_list ?? []),
        ...(regions_global.value?.area_list ?? []),
      ]);
    }
    if (current_game_id === String(INTL_HMT_GAME_ID)) {
      return computed(() => regions_hmt.value?.area_list ?? []);
    }
    // 默认返回国际服区服列表
    return computed(() => regions_global.value?.area_list ?? []);
  })();

  const getServerName = (player_area_id?: string) => {
    return (
      server_list.value?.find((item: GameRegion) => item.area_id + "" == player_area_id + "")
        ?.area_name ?? "-"
    );
  };

  init_data();

  return {
    server_list,
    getServerName,
  };
};
