import { createTimer } from "@/utils/timer";
import { computed, ref } from "vue";

export const useStanceGift = (options?: {
  reaching_gift_progress?: number;
  gift_duration?: number;
  reaching_gift_step?: number;
}) => {
  const REACHING_GIFT_PROGRESS = 100;
  const REACHING_GIFT_STEP = 2;
  const GIFT_DURATION = 1500;

  const { reaching_gift_progress, gift_duration, reaching_gift_step } = Object.assign(
    {
      reaching_gift_progress: REACHING_GIFT_PROGRESS,
      gift_duration: GIFT_DURATION,
      reaching_gift_step: REACHING_GIFT_STEP,
    },
    options,
  );

  const step_increase_gift_progress = reaching_gift_progress / reaching_gift_step;

  const current_gift_progress = ref(0);
  const is_reaching_gift_progress = computed(
    () => current_gift_progress.value >= reaching_gift_progress,
  );
  const gift_timer = ref();

  const increaseGiftProgress = (value: number | undefined = step_increase_gift_progress) => {
    if (current_gift_progress.value >= reaching_gift_progress && value > 0) {
      return;
    }
    current_gift_progress.value += value;
  };

  const resetGiftProgress = () => {
    current_gift_progress.value = 0;
  };

  const startGiftTimer = () => {
    if (gift_timer.value) {
      gift_timer.value.cancel();
    }
    gift_timer.value = createTimer(
      () => {
        gift_timer.value = null;
        resetGiftProgress();
      },
      {
        interval: gift_duration,
        immediate: false,
      },
    );
  };

  return {
    increaseGiftProgress,
    resetGiftProgress,
    startGiftTimer,
    is_reaching_gift_progress,
    gift_timer,
  };
};
