import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "packages/types/grayscale";
import { useGrayscale } from "packages/utils/grayscale";
import { useUser } from "@/store/user";
import { ENV_DEVELOPMENT, ENV_TEST } from "packages/configs/env";
import { isMobileDevice } from "packages/utils/tools";

export const useVconsole = () => {
  const { getGrayscale } = useGrayscale();
  const is_mobile = isMobileDevice();

  const loadVConsole = async () => {
    const newInstance = async () => {
      const res = await import("vconsole");
      new res.default();
    };

    if ([ENV_TEST, is_mobile && ENV_DEVELOPMENT].filter(Boolean).includes(import.meta.env.MODE)) {
      newInstance();
      return;
    }

    const user_store = useUser();
    await user_store.waitingGetUserInfoFinish();
    const openid = user_store.user_info.intl_openid;
    const vconsole_visible = await getGrayscale(GrayscaleKey.vconsole, {
      openid,
    });

    console.log(`[loadVConsole] vconsole visible status: ${vconsole_visible}, openid: ${openid}`);

    if (!vconsole_visible) {
      return;
    }
    newInstance();
  };

  return {
    loadVConsole,
  };
};
