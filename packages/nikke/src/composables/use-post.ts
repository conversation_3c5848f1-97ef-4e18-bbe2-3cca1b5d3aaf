import { get } from "lodash-es";
import { Platform } from "packages/types/common";
import { PostAuditStatus, PostItem } from "packages/types/post";
import { limitImageWidth, useImageSize } from "./use-image";

export const usePostItem = () => {
  const isVideoType = (type: number) => [3].includes(type);
  const { getImageOriginalSize } = useImageSize();

  const isTiktokVideoType = (platform: Platform) =>
    [Platform.tiktok].includes(platform as Platform);

  const getExtInfo = (ext_info: string) => {
    const info = JSON.parse(ext_info || "[]");
    const target = info instanceof Array ? info[0] : info;
    return target as { video_id: string; platform: string; video_cover: string } | undefined;
  };

  const getPostItemImageDisplaySize = (post_item: PostItem) => {
    const image_height = post_item.fe_image_info.height;
    const image_width = post_item.fe_image_info.width;

    const { width, height } = limitImageWidth({ width: image_width, height: image_height });

    return { width, height };
  };

  const resolvePostItemImageInfo = async (post_item: PostItem) => {
    let image_url = isVideoType(post_item.type)
      ? get(getExtInfo(post_item.ext_info), "video_cover")
      : post_item.pic_urls[0];

    let image_info = {};
    if (image_url) {
      const size = await getImageOriginalSize(image_url);
      image_info = { height: size.height, width: size.width };
    }

    Object.assign(post_item, {
      fe_image_info: { ...image_info, url: image_url },
    });

    return post_item;
  };

  const useUsePostItem = (post_item: PostItem) => {
    const is_video = isVideoType(post_item.type);
    const ext_info = getExtInfo(post_item.ext_info);
    const is_tiktok_video = is_video && isTiktokVideoType(ext_info?.platform as Platform);

    return {
      is_tiktok_video,
      is_video,
      ext_info,
    };
  };

  const getPostIsRelease = (post_item: PostItem) => {
    return (
      post_item.publish_on * 1000 < Date.now() && post_item.is_audit === PostAuditStatus.success
    );
  };

  return {
    isVideoType,
    isTiktokVideoType,
    getExtInfo,
    useUsePostItem,
    getPostItemImageDisplaySize,
    resolvePostItemImageInfo,
    getPostIsRelease,
  };
};
