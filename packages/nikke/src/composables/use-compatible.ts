import { ref } from "vue";

enum Status {
  focus = "focus",
  blur = "blur",
}

// 采用全局变量，用于在浏览器自身返回的时候，可以清除 touchmove 事件，不然在会导致返回首页的时候页面滚动不了
let touchMoveHandler: any;

export const useKeyboardPopupCompatible = () => {
  const el = ref<HTMLElement>();
  const status = ref<Status>(Status.blur);

  const setElement = (element: HTMLElement) => {
    el.value = element;
  };

  /**
   * @description 检查是否默认事件
   *
   * @param   {TouchEvent}  e  [e description]
   *
   * @return  {boolean}        [return description]
   */
  const checkStopPrevent = (e: TouchEvent): boolean => {
    const target = e.target as HTMLElement;
    return status.value === Status.blur || Boolean(el.value?.contains(target));
  };

  touchMoveHandler = (e: TouchEvent) => {
    if (checkStopPrevent(e)) {
      return;
    }
    e.preventDefault();
  };

  const onElementFocus = () => {
    status.value = Status.focus;
    addTouchMoveEventListener();
  };

  const onElementBlur = () => {
    status.value = Status.blur;
    window.scrollTo({
      top: -999,
      left: 0,
      behavior: "smooth",
    });

    removeTouchMoveEventListener();
  };

  const addTouchMoveEventListener = () => {
    document.body.addEventListener("touchmove", touchMoveHandler, { passive: false });
  };

  const removeTouchMoveEventListener = () => {
    document.body.removeEventListener("touchmove", touchMoveHandler);
  };

  return {
    setElement,
    checkStopPrevent,
    onElementFocus,
    onElementBlur,
    addTouchMoveEventListener,
    removeTouchMoveEventListener,
  };
};
