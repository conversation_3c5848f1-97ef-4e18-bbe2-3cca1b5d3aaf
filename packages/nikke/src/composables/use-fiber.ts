export const useFiber = () => {
  /**
   * @description 调度器
   *
   * @param   {(idle_time: number) => void} judge  [调度回调函数]
   *
   * @return  {void}                        [无返回值]
   */
  const scheduler = (judge: (idle_time: number) => void): void => {
    setTimeout(() => {
      const start_time = performance.now();
      // current frame has remaining
      judge(16 - (performance.now() - start_time));
    }, 0);
  };

  /**
   * @description 分块
   *
   * @param   {any[]}  list  [需要处理的列表]
   * @param   {(item: any, index: number) => void} cb    [处理函数]
   *
   * @return  {void}        [无返回值]
   */
  const chunk = <T>(list: T[], cb: (item: T, index: number) => void): void => {
    let i = 0;
    const len = list.length;

    const run = () => {
      if (i >= len) {
        return;
      }
      scheduler((time_remaining) => {
        const bool =
          i < len &&
          // current frame has remaining
          time_remaining > 0;

        if (bool) {
          cb(list[i], i);
          i++;
        }
        run();
      });
    };

    run();
  };

  return {
    chunk,
  };
};
