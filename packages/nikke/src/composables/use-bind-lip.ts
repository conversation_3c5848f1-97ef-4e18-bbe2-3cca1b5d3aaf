/**
 * @description 绑定 LIP 的逻辑
 *
 * 注意: 此处的LIP是 - LIP社区的 LIP 登录渠道
 */

import { useAccount, getAccountConfigByGameId, useLogin, Step } from "packages/utils/login";
import { urlSearchObjectify } from "packages/utils/qs";
import { BasicUserAuthInfo, IntlChannel, INTL_RET_CODE } from "packages/types/intl";
import { LoginRes, IntlRes } from "packages/types/login";
import { useLSStorage } from "packages/utils/storage";
import { useToast } from "@/components/ui/toast";
import { BindedAccountInfo } from "packages/types/login";
import { useQueryUserLoginInfo } from "@/api/user";
import { getStandardizedLang } from "packages/utils/standard";
import { INTL_LIP_COMMUNITY_GAME_ID } from "packages/configs/intl";
import {
  STANDARD_ACCOUNT_LANG_MAP,
  STANDARD_URL_INGAME_CHANNEL_ID,
} from "packages/configs/standard";
import { isGameLogin } from "packages/utils/tools";
import { getStandardizedGameId } from "packages/utils/standard";
import { IntlRenderMode } from "packages/types/intl";
import { injectCss } from "packages/utils/tools";
import { t } from "@/locales";
import { STORAGE_PA_ACCOUNT_INFO } from "packages/configs/storage";

import { get } from "lodash-es";
import { computed, ref } from "vue";

function useLSAccountInfo() {
  const { getStorage, setStorage } = useLSStorage();
  const query = urlSearchObjectify();

  // TODO: replace with pa-account-utils
  const setLipLSInfo = (info: any) => {
    setStorage(STORAGE_PA_ACCOUNT_INFO, JSON.stringify(info));
  };

  const getLipLSInfo = () => {
    const ls_info = getStorage(STORAGE_PA_ACCOUNT_INFO);
    return ls_info ? (JSON.parse(ls_info) as LoginRes) : null;
  };

  const getCurrentChannel = () => {
    if (isGameLogin()) return Number(get(query, STANDARD_URL_INGAME_CHANNEL_ID));
    const cached_login_info = getLipLSInfo();
    if (cached_login_info) return cached_login_info.channel_info?.channelId;
    // throw new Error("Supposed to call this method with login status");
    return undefined;
  };

  const getCurrentChannelUserName = () => {
    if (isGameLogin()) return Number(get(query, "role_name"));
    const cached_login_info = getLipLSInfo();
    if (cached_login_info) return cached_login_info.user_name;
    // throw new Error("Supposed to call this method with login status");
    return undefined;
  };

  return {
    setLipLSInfo,
    getLipLSInfo,
    getCurrentChannel,
    getCurrentChannelUserName,
  };
}

export function useAccountSwitch(game_id?: string) {
  // config
  const config = getAccountConfigByGameId(game_id || String(getStandardizedGameId()));
  const { getCurrentChannel, getCurrentChannelUserName, getLipLSInfo, setLipLSInfo } =
    useLSAccountInfo();
  const { getAccountSdk } = useAccount(config);

  // current status
  const is_loading = ref(true);
  const binded_account_list = ref<BindedAccountInfo[]>([]);

  const is_lip_binded = computed(() => {
    return binded_account_list.value.find((item) => item.channelid === IntlChannel.Lip);
  });
  const updateBindedListInfo = async () => {
    binded_account_list.value = [];
    const binded_list = await getBindedAccountList();
    (binded_list?.bind_list ?? []).forEach((val) => binded_account_list.value.push(val));
  };

  const user_login_data = ref<BasicUserAuthInfo>();
  const pending_state = useQueryUserLoginInfo
    .run({})
    .then(async (data) => {
      user_login_data.value = data;
      setAccountInfo(user_login_data.value!);
      await updateBindedListInfo();
    })
    .finally(() => (is_loading.value = false));

  // utils
  const { show } = useToast();
  const getApi = () => getAccountSdk({ accountPlatType: getCurrentChannel() });
  const warpIntlRes = <T>(req: IntlRes<T>): IntlRes<T> => {
    return req.then((res) => {
      if (res.ret === INTL_RET_CODE.SUCCESS) {
        return res;
      }
      if (res.ret === INTL_RET_CODE.USER_CANCEL_POPUP) {
        return res;
      }
      if (res.ret === INTL_RET_CODE.OPEN_ID_IS_BOUND) {
        show({ text: t("openid_is_bound"), type: "error" });
      } else {
        show({ text: res.msg, type: "error" });
      }
      throw new Error(res.msg);
    });
  };

  let account_info: BasicUserAuthInfo;
  const setAccountInfo = (info: BasicUserAuthInfo) => {
    account_info = info;
  };

  const lipLogin = async (): Promise<LoginRes> => {
    const current_url_lang = getStandardizedLang() as keyof typeof STANDARD_ACCOUNT_LANG_MAP;
    const lang_type = STANDARD_ACCOUNT_LANG_MAP[current_url_lang];
    const login_conf = getAccountConfigByGameId(INTL_LIP_COMMUNITY_GAME_ID);
    const prev_info = getLipLSInfo();
    // 使用此方法必定登录过，不用担心影响到登录页面
    injectCss(`
      .pass-content__wrapper {
        overflow: hidden;
      }
      .pass-login__wapper {
        background: #231C30;
      }
      .pass-resetPwd__wrapper {
        background: #231C30;
      }
      .pass-register__content {
        background: #231C30;
      }
      .infinite-confirm-content p {
        color: rgba(255, 255, 255, 0.7) !important;
      }
      .infinite-modal {
        max-width: 480px !important;
      }
    `);
    const { login } = await useLogin({
      env: login_conf.env,
      gameID: login_conf.gameID,
      appID: login_conf.appID,
      config: {
        isMobile: true,
        socialList: [],
        langType: lang_type,
        renderMode: IntlRenderMode.modal,
        loginWithCode: {
          enable: true,
          registerType: "auto",
        },
        // @ts-ignore
        loginWithPwd: {
          enable: true,
          registerType: "manual",
        },
        procedureSwitch: {
          region: true,
          adultStatus: true,
          agreement: true,
          registerPassword: "onDemand",
        },
      },
    });
    return new Promise((resolve) => {
      login("", (step: Step, value) => {
        switch (step) {
          case Step.logined:
            resolve(value as LoginRes);
            // FIXME: 绑定账号的覆盖了...
            setTimeout(() => {
              setLipLSInfo(prev_info);
            });
            break;
          default:
            break;
        }
      });
    });
  };

  const thirdChannelLogin = async (third_channel: Exclude<IntlChannel, IntlChannel.Lip>) => {
    const api = await getApi();
    const third_type_map = {
      [IntlChannel.Apple]: "apple",
      [IntlChannel.Facebook]: "facebook",
      [IntlChannel.Google]: "google",
      [IntlChannel.Line]: "line",
      [IntlChannel.Twitter]: "twitter",
    } as const;
    return warpIntlRes(
      api.thirdAuthorize({
        third_type: third_type_map[third_channel],
      }) as any,
    );
  };

  const thirdLinkThird = async (target_channel: IntlChannel, third_auth: any) => {
    const api = await getApi();
    const bind_req_params = {
      // 当前的
      token: account_info.token,
      openid: account_info.open_id,
      oauth_channelid: account_info.channel_id,
      // 被绑的
      bind_channelid: target_channel,
      bind_channel_info: third_auth,
    };
    return warpIntlRes(api.bind(bind_req_params));
  };

  const lipBindThird = async (target_channel: IntlChannel, third_auth: any) => {
    const api = await getApi();
    const bind_req_params = {
      // 当前的
      token: account_info.token,
      openid: account_info.open_id,
      oauth_channelid: account_info.channel_id,
      // 被绑的
      bind_channelid: target_channel,
      bind_channel_info: third_auth,
    };
    return warpIntlRes(api.bind(bind_req_params));
  };

  const thirdBindLip = async (lip_res: { uid: string; token: string; account: string }) => {
    const api = await getApi();
    const bind_req_params = {
      token: account_info.token,
      openid: account_info.open_id,
      oauth_channelid: account_info.channel_id,
      // lip
      bind_channelid: IntlChannel.Lip,
      bind_channel_info: {
        openid: lip_res.uid,
        token: lip_res.token,
        account: lip_res.account,
        account_plat_type: IntlChannel.Lip,
      },
    };
    return warpIntlRes(api.bind(bind_req_params));
  };

  const getBindedAccountList = async () => {
    const api = await getApi();
    return warpIntlRes(
      api!.getBindChannelsByOpenID({
        openid: account_info.open_id,
        token: account_info.token,
        channel_id: account_info.channel_id,
      }),
    );
  };

  return {
    is_loading,
    is_lip_binded,
    binded_account_list,
    user_login_data,
    pending_state,
    lipLogin,
    lipBindThird,
    thirdLinkThird,
    thirdChannelLogin,
    thirdBindLip,
    getLipLSInfo,
    updateBindedListInfo,
    getCurrentChannel,
    getCurrentChannelUserName,
  };
}
