import { ref, Ref, watch } from "vue";
import { onClickOutside } from "@vueuse/core";
export type Options<T = {}> = {
  name: string;
  value: string | number;
} & T;

interface State<T = {}> {
  /**
   * 下拉选项列表
   */
  list: Ref<Options<T>[]>;
  /**
   * 默认值
   */
  value?: string;
  /**
   * 默认文案
   */
  placeholder?: string;
  /**
   * 容器元素引用（主要用来解决容器外点击收起列表）
   */
  content_ref: Ref<HTMLElement | undefined>;
  /**
   * 自定义回调
   * @param option
   */
  handle?(option: Options<T>): void;
}

export const useSelect = <T = {}>(state: State<T>) => {
  const { value, content_ref, list, placeholder, handle } = state;
  const label = ref(placeholder || "");
  const is_show = ref(false);
  const checked = ref<Options<T> & { index: number }>();

  const setDefault = () => {
    const idx = list.value.findIndex((item) => `${item.value}` === value);
    const target = list.value[idx];
    if (target) {
      checked.value = {
        ...target,
        index: idx,
      };
      label.value = target.name;
      handle?.(target);
    }
  };
  const open = () => {
    if (list.value.length === 0) {
      return;
    }
    is_show.value = !is_show.value;
  };
  /**
   * 选中回调
   * @param option option选项 或者 option索引
   * @returns
   */
  const change = async (option: Options<T> | number) => {
    const target = typeof option === "number" ? list.value[option] : option;
    if (checked.value?.value === target.value) {
      return;
    }

    label.value = target.name;
    const index = list.value.findIndex((item) => item.value === target.value);
    checked.value = {
      ...target,
      index,
    };
    open();
    handle?.(target);
  };

  if (value) {
    // watch监听 下拉列表异步获取的场景
    if (list.value.length === 0) {
      watch(
        list,
        () => {
          setDefault();
        },
        { deep: true },
      );
    } else {
      setDefault();
    }
  }

  if (content_ref) {
    onClickOutside(content_ref, () => {
      is_show.value = false;
    });
  }

  const reset = (value?: string | number) => {
    if (value) {
      const index = list.value.findIndex((item) => item.value === value);
      const target = list.value[index];
      if (target) {
        checked.value = {
          ...target,
          index,
        };
        label.value = checked.value.name;
      }
    } else {
      checked.value = undefined;
      label.value = placeholder || "";
    }
  };

  return {
    reset,
    open,
    change,
    checked,
    label,
    is_show,
  };
};
