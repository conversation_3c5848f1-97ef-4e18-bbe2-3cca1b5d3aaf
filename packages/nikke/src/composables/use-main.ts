import { App as AppType } from "vue";
import { createPinia } from "pinia";
import { MotionPlugin } from "@vueuse/motion";
import { VueQueryPlugin, VueQueryPluginOptions } from "@tanstack/vue-query";
import { i18n } from "@/locales/index";
import router from "@/router/index";
import VueLazyload from "vue-lazyload";
import CustomDirective from "@/shiftyspad/directives";
import PrimeVue from "primevue/config";
import { useDirectives } from "@/directives";
import { createRouterScroller } from "vue-router-better-scroller";

const vueQueryPluginOptions: VueQueryPluginOptions = {
  queryClientConfig: {
    defaultOptions: { queries: { staleTime: 3000, retry: false, refetchOnWindowFocus: false } },
  },
};

const pinia = createPinia();

export const useMain = () => {
  /** 安装插件, 导出并支持为独立弹框安装 */
  const installAppPlugins = (app: AppType, options: { is_main_app?: boolean } = {}) => {
    app
      .use(MotionPlugin)
      .use(VueQueryPlugin, vueQueryPluginOptions)
      .use(i18n)
      .use(router)
      .use(pinia)
      .use(VueLazyload, {
        dispatchEvent: true,
      })
      .use(PrimeVue)
      .use(CustomDirective)
      .use(useDirectives)
      .use({
        install(app) {
          app.mixin({
            mounted() {
              // 将组件名称追加到元素上，临时缓解 【样式原子化导致的：界面-追溯->源码】不便的问题
              const { name, __name } = this.$options;
              const componentName = name || __name;
              if (componentName) {
                this.$el?.setAttribute?.("data-cname", componentName);
              }
            },
          });
        },
      });
    if (options.is_main_app) {
      return app.use(
        // 修复由于调整滚动元素引起的路由滚动恢复失效问题
        createRouterScroller({
          selectors: {
            // window: true,
            // body: true,
            "#layout-content": true,
          },
        }),
      );
    }
    return app;
  };

  return {
    installAppPlugins,
  };
};
