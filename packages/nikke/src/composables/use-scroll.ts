import { useEventListener } from "@vueuse/core";
import { debounce } from "lodash-es";
import { isMobileDevice } from "packages/utils/tools";
import { computed, onBeforeMount, onMounted, onUnmounted, ref } from "vue";

export function useScrollAction(options?: { getScrollEl: () => HTMLElement | null }) {
  const scroll_top = ref(0);
  const direction = ref<"down" | "up" | undefined>(undefined);

  const { getScrollEl } = Object.assign(
    {
      getScrollEl: () => document.getElementById("layout-content"),
    },
    options,
  );

  let cleanup: () => void;

  const registerScroll = () => {
    const scoll_el = getScrollEl();

    if (!scoll_el) {
      console.warn("scroll element not found");
      return;
    }

    cleanup = useEventListener(
      scoll_el,
      "scroll",
      debounce(() => {
        console.log("scroll el scrolling");

        const v = scoll_el.scrollTop;
        if (v > scroll_top.value) {
          direction.value = "down";
        } else if (v < scroll_top.value) {
          direction.value = "up";
        }
        scroll_top.value = v;
      }, 100),
    );
  };

  const unRegisterScroll = () => {
    cleanup?.();
  };

  return { direction, scroll_top, registerScroll, unRegisterScroll };
}

export const useAppWheel = () => {
  const outer_container = computed(() => document.getElementById("app"));
  const inner_container = computed(() => document.getElementById("layout-content"));
  const is_mobile = isMobileDevice();

  const onAppWheelAction = () => {
    // 移动端不处理
    if (is_mobile) {
      return;
    }

    const onInnerContainerWheel = (event: WheelEvent) => {
      event.stopPropagation();
    };

    const onOuterContainerWheel = (event: WheelEvent) => {
      event.preventDefault();
      const deltaY = event.deltaY;
      inner_container.value &&
        inner_container.value.scrollBy({
          top: deltaY,
          // behavior: "smooth",
        });
    };

    onMounted(() => {
      inner_container.value &&
        inner_container.value.addEventListener("wheel", onInnerContainerWheel);
      outer_container.value &&
        outer_container.value.addEventListener("wheel", onOuterContainerWheel);
    });

    onUnmounted(() => {
      inner_container.value &&
        inner_container.value.removeEventListener("wheel", onInnerContainerWheel);
      outer_container.value &&
        outer_container.value.removeEventListener("wheel", onOuterContainerWheel);
    });
  };

  return { onAppWheelAction };
};
