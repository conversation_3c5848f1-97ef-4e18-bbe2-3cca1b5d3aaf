/**
 * 商品相关的功能
 */
import { computed, ComputedRef, ref, Ref, watch } from "vue";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { Commodity } from "packages/types/commodity";

dayjs.extend(duration);

/**
 * 商品倒计时
 */
export const useDiscount = (
  data: Ref<Commodity | undefined> | ComputedRef<Commodity | undefined>,
) => {
  const count_down = ref("");
  let timer: ReturnType<typeof setTimeout>;

  // 商品是否告罄
  const is_sold_out = computed(() => {
    return data.value && !data.value.commodity_has_left;
  });

  const is_discount_show = computed(() => {
    return Boolean(count_down.value && is_discount.value);
    // return Boolean(count_down.value && is_discount.value && !is_sold_out.value);
  });

  const is_discount = computed(() => {
    return Boolean(data.value?.commodity_is_discount);
  });

  const end_time = computed(() => {
    return Number(data.value?.commodity_discount_end_time || 0) * 1000;
  });

  const start = () => {
    stop();
    const result = getCountDown(end_time.value);
    count_down.value = result;
    if (result) {
      timer = setTimeout(() => {
        start();
      }, 1000);
    }
  };

  const stop = () => {
    clearTimeout(timer);
  };

  watch(
    () => is_discount.value,
    (v: boolean) => {
      if (v) {
        start();
      } else {
        stop();
      }
    },
    { immediate: true },
  );

  return {
    is_discount_show,
    is_sold_out,
    count_down,
  };
};

const getCountDown = (end_time: number) => {
  const now = Date.now();
  const space = end_time - now;
  if (space < 0) {
    // 倒计时结束
    return "";
  }
  const diff = dayjs.duration(space);
  const days = diff.days();
  const hours = diff.hours();
  const minutes = diff.minutes();
  const seconds = diff.seconds();
  let result = "";

  if (days > 0) {
    result += formatUnit(days) + " : ";
  }

  result += formatUnit(hours) + " : ";
  result += formatUnit(minutes) + " : ";
  result += formatUnit(seconds);
  return result;
};

const formatUnit = (unit: number) => {
  return unit < 10 ? "0" + unit : unit;
};
