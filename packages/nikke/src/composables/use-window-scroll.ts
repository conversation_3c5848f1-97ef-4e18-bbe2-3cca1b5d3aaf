import { ref } from "vue";

export const useWindowScroll = () => {
  const curScrollTop = ref(0); // 记录页面滚动高度

  // 重新激活页面，滚动到之前的位置
  const start = () => {
    setTimeout(() => {
      document.documentElement.scrollTop = curScrollTop.value;

      window.onscroll = () => {
        curScrollTop.value = document.documentElement.scrollTop;
      };
    });
  };

  // 页面隐藏
  const end = () => {
    window.onscroll = null;
  };

  return {
    start,
    end,
    curScrollTop,
  };
};
