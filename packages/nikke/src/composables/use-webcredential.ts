import { useUser } from "@/store/user";
import { openUrlWithWhiteListQuery } from "@/utils/tools";
import { getAccountConfigByGameId, useAccount } from "packages/utils/login";
import { getStandardizedGameId } from "packages/utils/standard";
import { useLSLoginMetaStorage } from "packages/utils/storage";
import { getDefaultOpenUrlTarget } from "packages/utils/tools";

export const useWebCredential = () => {
  const user_store = useUser();

  const { getLoginMeta } = useLSLoginMetaStorage();

  const openUrlWithAuth = async (
    url: string,
    open_type: undefined | string = getDefaultOpenUrlTarget(url),
    window_features: undefined | string = "noopener,noreferrer",
  ) => {
    const target_game_id = getTargetGameId(url);

    if (!target_game_id || !user_store.is_login) {
      // 没有登录时，直接跳转，不携带 web_credential
      openUrlWithWhiteListQuery(url, open_type, window_features);
      return;
    }
    // config
    const config = getAccountConfigByGameId(String(getStandardizedGameId()));

    const { getAccountSdk } = useAccount(config);

    const account_api = await getAccountSdk();

    const meta = getLoginMeta();
    // 游戏内 meta 是: { open_id: string; token: string; channel_id: number }
    // 海外 meta 是: { openid: string; token: string; channel_info: { channelId: number } }
    const res = await account_api.exchangeWebCredential({
      openid: meta.openid || meta.open_id,
      token: meta.token,
      target_gameid: target_game_id,
      channel_id: meta.channel_info?.channelId || meta.channel_id,
    });
    console.log("[exchangeWebCredential res]", res);

    if (res.ret === WebCredentialResultRet.Success) {
      const { web_credential } = res;

      if (!isNeedRedirectByLip(url)) {
        const parsed = new URL(url);
        parsed.searchParams.set("web_credential", web_credential);
        fixLipTestUrl(parsed);
        openUrlWithWhiteListQuery(parsed.toString(), open_type, window_features);
      } else {
        // 需要 LIP 官网中转解析，再跳转到目标活动页面
        const parsed = new URL(url);
        const loading_url = new URL(`${parsed.origin}/external/nikke`);
        loading_url.searchParams.set("web_credential", web_credential);
        loading_url.searchParams.set("redirect", encodeURIComponent(url));
        fixLipTestUrl(loading_url);
        openUrlWithWhiteListQuery(loading_url.toString(), open_type, window_features);
      }
    } else if (res.ret === WebCredentialResultRet.NotBindLip) {
      // 没有绑定 LIP 账号时，直接跳转，不携带 web_credential
      openUrlWithWhiteListQuery(url, open_type, window_features);
    } else {
      console.warn("[exchangeWebCredential] error", res);
      // 报错时，直接跳转，不携带 web_credential
      openUrlWithWhiteListQuery(url, open_type, window_features);
    }
  };

  const isSupportWebCredential = (url: string) => {
    return !!getTargetGameId(url);
  };

  const isPassLevelInfiniteUrl = (url: string) => {
    try {
      const url_object = new URL(url);
      return url_object.host.includes("pass.levelinfinite.com");
    } catch (error) {
      return false;
    }
  };

  /**
   * 是否需要 LIP 官网重定向
   * - 部分活动页面，挂载在 pass.levelinfinite.com 下，但是本身没有实现 web_credential 的逻辑，可以先跳转到 LIP 官网，解析 web_credential，再跳转回原页面
   */
  const isNeedRedirectByLip = (url: string) => {
    if (!isPassLevelInfiniteUrl(url)) return false;
    try {
      const url_object = new URL(url);
      return url_object.pathname.includes("/lottery/wand/");
    } catch (error) {
      return false;
    }
  };

  /**
   * 修复 LIP 测试环境，需要设置 env 参数，以使用和独立站一样的现网登录态
   */
  const fixLipTestUrl = (url: URL) => {
    if (url.origin.includes("test-pass.levelinfinite.com")) {
      url.searchParams.set("env", "sg");
    }
  };

  const getTargetGameId = (url: string) => {
    if (isPassLevelInfiniteUrl(url)) return 30004;
    return null;
  };

  return {
    isSupportWebCredential,
    openUrlWithAuth,
  };
};

enum WebCredentialResultRet {
  /** 成功 */
  Success = 0,
  /** 没有绑定Lip */
  NotBindLip = 11312,
}
