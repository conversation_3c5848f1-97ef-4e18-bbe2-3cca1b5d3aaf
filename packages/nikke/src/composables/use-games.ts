// types
import { CDNGameItem } from "packages/types/cdn";
// configs
import { useCDNConfigs } from "packages/utils/cdn";
// utils
import { computed, ref } from "vue";
import { Role } from "packages/types/games";
import {
  useRegionList,
  getRoleInfo,
  getSavedRoleInfo,
  saveRoleInfo,
  saveRoleParams,
} from "@/api/games";
import { t } from "@/locales/index";
const default_role = ref<Role>(); // 默认角色
export const useGames = () => {
  const { getCDNConfigs } = useCDNConfigs();
  const { games: cdn_configs_games } = getCDNConfigs();
  console.log("cdn_configs_games", cdn_configs_games);
  // nikke intl_gameid 直接写死
  const REWARD_GAMES = [29080, 29157];

  const game_meta = computed(() => {
    return cdn_configs_games.filter((item) => REWARD_GAMES.includes(Number(item.value)));
  });

  const resolvedI18nKey = (key: string, default_value: string): string => {
    return !key || t(key) === key ? default_value : t(key);
  };

  const resolveGamesI18n = (games: Array<CDNGameItem>) => {
    const res = games.map((item) => {
      return {
        ...item,
        game: resolvedI18nKey(item?.game_i18n_key, item.game),
        label: resolvedI18nKey(item?.label_i18n_key, item.label),
      };
    });

    return res;
  };

  const games = computed(() => {
    return resolveGamesI18n(game_meta.value);
  });

  const getServerByGame = async (game_id: string) => {
    const target = game_meta.value.find((item) => item.value === game_id);
    if (!target) {
      return [];
    }
    const { area_list = [] } = await useRegionList.run({ game_id: target.value });
    return area_list.map((item) => {
      const { area_id, area_name } = item;
      return { value: area_id, name: area_name, ext: item };
    });
  };
  const getRoles = async (server: string, game_id: string, zone: string | number) => {
    const { role_list } = await getRoleInfo({
      area_id: Number(server),
      game_id,
      zone_id: zone || undefined,
    });
    return role_list.map((item) => ({
      name: item.role_name,
      value: item.role_id,
      ext: item,
    }));
  };
  const getSavedRole = async () => {
    if (typeof default_role.value === "undefined") {
      const { has_saved_role_info, role_info } = await getSavedRoleInfo();
      if (!has_saved_role_info) {
        return;
      }
      default_role.value = role_info;
    }

    if (!default_role.value) {
      return;
    }
    const info = default_role.value;
    return {
      name: info.role_name,
      value: info.role_id,
      ext: info,
    };
  };

  const saveRole = async (role: saveRoleParams) => {
    await saveRoleInfo(role);
  };
  return {
    games,
    default_role,
    getServerByGame,
    getRoles,
    saveRole,
    getSavedRole,
  };
};
