// cpnts
import { useToast } from "@/components/ui/toast";
// configs
import {
  CONST_MAX_IMAGE_LIMIT_SIZE,
  CONST_MAX_IMAGE_LIMIT_SIZE_UNITE,
  CONST_SUPPORTED_IMAGE_POSTFIXS,
  CONST_SUPPORTED_IMAGE_TYPES,
} from "packages/configs/const";
// types
import { UploadFileType } from "packages/types/common";
// utils
import { getUploadClientInstance } from "@/api/axios";
import { t } from "@/locales";
import { ref } from "vue";
import { decodeHTMLEntities, loadImageSize } from "packages/utils/tools";
import { useIndexDB } from "packages/utils/storage";
import { urlSearchObjectify } from "packages/utils/qs";
import { STORAGE_INDEXDB_IMAGE_INFO } from "packages/configs/storage";
import { getDocument } from "packages/utils/dom";

export enum ImageUploadErrorToastType {
  exceed_size_limit = "exceed_size_limit",
  file_upload_failed = "file_upload_failed",
  file_type_not_support = "file_type_not_support",
}

export const useImageUpload = () => {
  const { show: toast } = useToast();
  const uploading = ref(false);

  const ERROR_MESSAGE_MAP = {
    [ImageUploadErrorToastType.exceed_size_limit]: t("max_image_limit_size_tips", [
      `${CONST_MAX_IMAGE_LIMIT_SIZE_UNITE}MB`,
    ]),
    [ImageUploadErrorToastType.file_upload_failed]: t("file_upload_failed"),
    [ImageUploadErrorToastType.file_type_not_support]: t("file_type_not_support", [
      `(${CONST_SUPPORTED_IMAGE_POSTFIXS})`,
    ]),
  } as {
    [key in ImageUploadErrorToastType]: string;
  };

  const onUpload = async (file: File) => {
    if (!file) {
      return;
    }

    const upload_client = await getUploadClientInstance();
    await upload_client.getToken();

    let url = await upload_client.uploadFile({
      file,
      type: UploadFileType.image,
    });

    if (url) {
      const image_info = await loadImageSize(url);
      url += `?height=${image_info.height}&width=${image_info.width}`;
    }

    return url;
  };

  const onCheckSizeLimit = (file_size: number) => {
    return file_size <= CONST_MAX_IMAGE_LIMIT_SIZE;
  };

  const onCheckFileType = (file_type: string) => {
    return CONST_SUPPORTED_IMAGE_TYPES.includes(file_type);
  };

  const onErrorToast = (toast_type: ImageUploadErrorToastType) => {
    const handler = {
      [ImageUploadErrorToastType.exceed_size_limit]: () =>
        toast({
          text: ERROR_MESSAGE_MAP.exceed_size_limit,
          type: "error",
        }),
      [ImageUploadErrorToastType.file_upload_failed]: () =>
        toast({
          text: ERROR_MESSAGE_MAP.file_upload_failed,
          type: "error",
        }),
      [ImageUploadErrorToastType.file_type_not_support]: () =>
        toast({
          text: ERROR_MESSAGE_MAP.file_type_not_support,
          type: "error",
        }),
    }[toast_type];

    return handler?.();
  };

  const toggleUploading = () => {
    uploading.value = !uploading.value;
  };

  const getUploading = () => uploading.value;

  return {
    uploading,
    ERROR_MESSAGE_MAP,
    getUploading,
    toggleUploading,
    onCheckSizeLimit,
    onCheckFileType,
    onUpload,
    onErrorToast,
  };
};

/**
 * @description 处理数据万象
 * @link https://www.tencentcloud.com/zh/document/product/1045/45582?has_map=1
 */
export const useImageCloudInfinite = () => {
  const isUrlSearch = (url: string) => {
    try {
      const url_obj = new URL(url);
      return Boolean(url_obj.search);
    } catch (error) {
      return false;
    }
  };

  const handleQuality = (url: string, quality: undefined | number | string = 100) => {
    const sign = isUrlSearch(url) ? "&" : "?";
    // 先转成 webp 的格式，然后再压缩
    return `${url}${sign}imageMogr2/quality/${quality}/format/webp/interlace/0`;
  };

  const removeQuality = (url: string) => {
    return url.replace(/(imageMogr2\/quality[^&]*)/g, "");
  };

  return {
    handleQuality,
    removeQuality,
  };
};

export const useImageSize = () => {
  const { updateCache, getCache } = useIndexDB();

  const getImageOriginalSize = async (image_url: string) => {
    const decode_image_url = decodeHTMLEntities(image_url);
    const url_image_info = urlSearchObjectify(decode_image_url);
    if (url_image_info.height && url_image_info.width) {
      return {
        height: +url_image_info.height,
        width: +url_image_info.width,
      };
    } else {
      let cache = await getCache(STORAGE_INDEXDB_IMAGE_INFO);
      if (!cache) {
        updateCache({
          key: STORAGE_INDEXDB_IMAGE_INFO,
          value: [],
        });

        cache = {
          value: [],
        } as any;
      }
      const target = cache.value.find((item: { key: string }) => item.key === image_url) as {
        key: string;
        value: { width: number; height: number };
      };
      const image_info = target?.value || (await loadImageSize(image_url));
      if (!target?.value) {
        // cache
        if (cache.value.length >= 100) {
          cache.value.shift();
        }
        updateCache({
          key: STORAGE_INDEXDB_IMAGE_INFO,
          value: cache.value.concat({
            key: image_url,
            value: image_info,
          }),
        });
      }

      return {
        width: +image_info.width,
        height: +image_info.height,
      };
    }
  };

  /**
   * 限制图片宽高比例，输入图片地址，输出限制后的宽高
   * - 如果图片宽高比例大于16/9，则限制宽度为16/9，高度不变
   * - 如果图片宽高比例小于9/16，则限制高度为9/16，宽度不变
   */
  const getImageDisplaySizeByLimit = async (image_url: string) => {
    const original_size = await getImageOriginalSize(image_url);
    const display_size = limitImageWidth(original_size);
    return display_size;
  };

  return {
    getImageOriginalSize,
    getImageDisplaySizeByLimit,
  };
};

/**
 * 限制图片宽高比例，输入原始宽高，输出限制后的宽高
 * - 如果图片宽高比例大于16/9，则限制宽度为16/9，高度不变
 * - 如果图片宽高比例小于9/16，则限制高度为9/16，宽度不变
 */
export const limitImageWidth = (size: { width: number; height: number }) => {
  const { width, height } = size;

  if (width / height > 16 / 9) {
    return {
      width: (height * 16) / 9,
      height,
    };
  }

  if (width / height < 9 / 16) {
    return {
      width,
      height: (width * 16) / 9,
    };
  }

  return { width, height };
};

/**
 * @description 处理 html 图片的展示
 * @param html
 * @param quality
 * @returns
 */
export const resovledHtmlImgQuality = (html: string, quality: number): string => {
  const doc = getDocument(html);
  const images = doc.querySelectorAll("img");
  const { handleQuality } = useImageCloudInfinite();

  images.forEach((img) => {
    img.src = handleQuality(img.src, quality);
  });

  return doc.body.innerHTML;
};
