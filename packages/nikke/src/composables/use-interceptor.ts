// cpnts
import { useUserSignPolicyPop } from "@/components/common/pop/user-sign-policy/index";
import { useModifyUsernamePop } from "@/components/common/pop/modify-username/index.ts";
import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog/index.ts";

// configs
import {
  API_SS_UGC_CREATE_POST,
  API_SS_UGC_CREATE_POST_NEW,
  API_SS_UGC_POST_COLLECTION,
  API_SS_UGC_POST_COMMENT,
  API_SS_UGC_POST_FORWARD,
  API_SS_UGC_POST_STAR,
  API_SS_UGC_USER_FOLLOW,
  API_SS_UGC_COMMENT_STAR,
  API_SS_UGC_CONTENT_REPORT,
  // read game info
  API_TOOLS_GET_PLAYER_GAME_INFO,
  API_TOOLS_GET_BATTLE_ROLE,
  API_TOOLS_GET_EQUIPS,
  API_TOOLS_GET_PLAYER_NIKKES,
} from "packages/configs/api";

// utils
import router from "@/router";
import { Routes, RoutesName } from "@/router/routes";
import { useUser } from "@/store/user";
import { computed } from "vue";
import { AxiosRequestConfig } from "axios";
import { t } from "@/locales";
import { safeExecutePromisesSequentially } from "packages/utils/tools";
import { getToLoginQuery } from "packages/utils/login";
import { PopCallbackValue } from "packages/types/common";
import { UserGameAdultStatus, UserParentCertificateStatus } from "packages/types/user";
// import { urlSearchObjectify } from "packages/utils/qs";

// atomic
enum InterceptorTypes {
  login = "login",
  muted = "muted",
  sign_privacy = "sign_privacy",
  modify_username = "modify_username",
  check_user_adult = "check_user_adult",
}

// 需要同意隐私 & 登录 但是不需要 mute 的 api 列表
const NEED_PRIVACY_AND_LOGIN_APIS = [
  API_TOOLS_GET_PLAYER_GAME_INFO,
  API_TOOLS_GET_BATTLE_ROLE,
  API_TOOLS_GET_EQUIPS,
  API_TOOLS_GET_PLAYER_NIKKES,
];

// 需要拦截的 api 列表
const INTERCEPTOR_APIS = [
  API_SS_UGC_POST_STAR,
  API_SS_UGC_USER_FOLLOW,
  API_SS_UGC_POST_COLLECTION,
  API_SS_UGC_POST_FORWARD,
  API_SS_UGC_POST_COMMENT,
  API_SS_UGC_CREATE_POST,
  API_SS_UGC_CREATE_POST_NEW,
  API_SS_UGC_COMMENT_STAR,
  API_SS_UGC_CONTENT_REPORT,
];

const getErrorMsg = (type: InterceptorTypes) =>
  `${window.ERROR_OPERATION_INTERCEPTE_MESSAGE}: ${type}`;

export const useInterceptor = () => {
  const { show: showUserSignPolicyPop } = useUserSignPolicyPop();
  const { show: showModifyUsernamePop } = useModifyUsernamePop();
  const { show: toast } = useToast();
  const { show: showDialog } = useDialog();
  // const url_search_object = urlSearchObjectify();
  const user_store = useUser();

  const user = computed(() => {
    const user_store = useUser();

    return {
      is_login: user_store.is_login,
      user_info: user_store.user_info,
    };
  });

  ////////////////////////////////////////////////////////////////////////////////////////////
  // atomic

  const needLoginInterceptor = async () => {
    if (!user.value.is_login) {
      router.push({
        path: Routes.LOGIN,
        query: getToLoginQuery(),
      });
      return Promise.reject(getErrorMsg(InterceptorTypes.login));
    }
    return Promise.resolve();
  };

  const muteInterceptor = async () => {
    if (user.value.is_login && user.value.user_info.is_mute) {
      toast({ text: t("violation_tips"), type: "error" });
      return Promise.reject(getErrorMsg(InterceptorTypes.muted));
    }
    return Promise.resolve();
  };

  const signPrivacyInterceptor = async () => {
    if (user.value.is_login && !user.value.user_info.has_sign_privacy) {
      await showUserSignPolicyPop({});
      return Promise.reject(getErrorMsg(InterceptorTypes.sign_privacy));
    }
    return Promise.resolve();
  };

  const modifyUsernameInterceptor = async () => {
    if (user.value.is_login && !user.value.user_info.had_modified_username) {
      await showModifyUsernamePop({});
      return Promise.reject(getErrorMsg(InterceptorTypes.modify_username));
    }
    return Promise.resolve();
  };

  const checkUserAdultInterceptor = async () => {
    if (user.value.is_login) {
      const {
        // 未成年人状态
        adult_check_status,
        // 家长认证状态
        parent_certificate_status,
      } = user_store.intl_user_status;
      // update intl user status
      // user_store.onUpdateIntlUserStatus();

      if (
        adult_check_status !== UserGameAdultStatus.adult &&
        parent_certificate_status !== UserParentCertificateStatus.parent_passed &&
        // TODO: 临时关闭
        !1
      ) {
        showDialog({
          title: t("warning"),
          content: t("api_code_1200316"),
          confirm_text: t("confirm"),
          cancel_text: t("cancel"),
          z_index: 100,
          async callback(options: { value: PopCallbackValue; close: () => void }) {
            const { value, close } = options;
            if (value === PopCallbackValue.confirm) {
              router.push({
                name: RoutesName.LOGIN,
                query: getToLoginQuery(),
              });
            }
            close();
          },
        });
        return Promise.reject(getErrorMsg(InterceptorTypes.check_user_adult));
      }
    }

    return Promise.resolve();
  };

  ////////////////////////////////////////////////////////////////////////////////////////////
  // compose

  const axiosRequestInterceptor = async (config: AxiosRequestConfig) => {
    if (INTERCEPTOR_APIS.includes(config.url as any)) {
      return Promise.all([
        //
        needLoginInterceptor,
        muteInterceptor,
        signPrivacyInterceptor,
      ]);
    }

    if (NEED_PRIVACY_AND_LOGIN_APIS.includes(config.url as any)) {
      return Promise.all([
        //
        needLoginInterceptor,
        signPrivacyInterceptor,
      ]);
    }

    return Promise.resolve();
  };

  const afterLoginInterceptor = async () => {
    return safeExecutePromisesSequentially([
      //
      signPrivacyInterceptor,
      modifyUsernameInterceptor,
    ]);
  };

  return {
    needLoginInterceptor,
    muteInterceptor,
    signPrivacyInterceptor,
    axiosRequestInterceptor,
    afterLoginInterceptor,
    checkUserAdultInterceptor,
  };
};
