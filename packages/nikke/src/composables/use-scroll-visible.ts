import { ref, onMounted, onUnmounted, Ref } from "vue";

export function useScrollVisible(dom: Ref<HTMLElement | null>) {
  const observer = ref();
  const visible = ref(false);

  const handleIntersect = (entries: IntersectionObserverEntry[]) => {
    entries.forEach((entry) => {
      visible.value = entry.isIntersecting;
    });
  };

  onMounted(() => {
    const observer = new IntersectionObserver(handleIntersect, {
      threshold: 0.3, // 30%可见触发
    });

    if (dom.value) {
      observer.observe(dom.value);
    }
  });

  onUnmounted(() => {
    if (dom.value) {
      observer.value.unobserve(dom.value);
    }
  });

  return {
    visible,
  };
}
