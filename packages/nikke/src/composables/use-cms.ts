import { Ref, ref } from "vue";
import { initSdks, HostSwitcher, IReqDetailGroup, IDetail, LikeOp } from "@tencent/pa-cms-utils";
import {
  CmsListCpntCommonProps,
  CmsListCpntLoadParams,
  CmsListCpntLoadOptions,
  CmsContentClass,
} from "packages/types/cms";
import {} from "@/store/system";
import { ymd, getUniqueArr, removeObjectEmptyKey } from "packages/utils/tools";
import { filterCmsItem, parseCMSJumpDetail, resovledCmsConfig } from "packages/utils/cms";
import { useAegis } from "@/composables/use-aegis";
import { useLSLoginMetaStorage } from "packages/utils/storage.ts";

import router from "@/router";
import { Routes } from "@/router/routes";
import { isEmpty } from "lodash-es";
import { CMS_GAME_ID } from "packages/configs/cms";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { useWebCredential } from "./use-webcredential";

const { getLoginMeta } = useLSLoginMetaStorage();
const { logAndReportError } = useAegis();

// 缓存
let default_cms_helper: any = null;

HostSwitcher.getInstance().createSwitch({
  "sg-lipcommunity.playerinfinite.com": "sg-lipcommunity-cdn.levelinfinite.com",
});

export const useCms = (params?: { cms_config: any }) => {
  let cms_helper: any = null;
  const cms_config = resovledCmsConfig(params?.cms_config);
  // 预发布临时切换CMS 渠道
  /*   if (window.location.host.includes("pre.")) {
    cms_config.source_type = "inner_game";
  } */
  if (params?.cms_config) {
    // 有特殊的配置， 将创建新的实例
    const { cms_helper: ch } = initSdks({ cms_config });
    cms_helper = ch;
  } else if (!default_cms_helper) {
    const { cms_helper: ch } = initSdks({ cms_config });
    default_cms_helper = ch;
    cms_helper = ch;
  } else {
    cms_helper = default_cms_helper;
  }
  /**
   * @description 获取资讯详情
   */
  const getFeedDetail = async (content_id: string) => {
    const { data } = await cms_helper.getFeedDetailV2({ content_id });
    return data;
  };
  /**
   * @description 获取一级栏目的配置信息
   */
  const getColumnByName = async (name: string) => {
    return await cms_helper.getColumnByName(name);
  };

  /**
   * @description 获取二级栏目的配置信息
   */
  const getSecondColumnConfig = async (primary: string, second: string) => {
    const primary_column = second ? await getColumnByName(primary) : { secondary_label_list: [] };
    const second_column = primary_column.secondary_label_list?.find(
      (item: any) => item.raw_label_name === second,
    ) || { label_id: 0 };

    // 这里不能报错，因为有些 banner column 可能没有二级栏目
    // if (!second_column) {
    //   const msg: string = `[getSecondColumnConfig] second column ${second} not found, primary: ${primary}`;
    //   logAndReportError(msg);
    //   throw new Error(msg);
    // }

    return Object.assign(second_column, { primary_label_id: primary_column.label_id });
  };
  /**
   * @description 获取二级栏目的数据
   */
  const getSecondColumnData = async (
    params: {
      offset?: number;
      content_class?: number;
      get_num?: number;
      ext_info_type_list?: number[];
      with_off?: boolean;
    },
    options: { primary: string; second: string },
  ) => {
    const { primary, second } = options;
    const { label_id: secondary_label_id, primary_label_id } = await getSecondColumnConfig(
      primary,
      second,
    );
    const { offset, content_class, get_num, ext_info_type_list, with_off } = Object.assign(
      { offset: 0, content_class: 0, get_num: 20, ext_info_type_list: [0, 1, 2], with_off: false },
      params,
    );

    const feed = await cms_helper.getFeedsByColumns({
      primary_label_id,
      secondary_label_id,
      offset,
      get_num,
      content_class,
      ext_info_type_list,
      with_off,
    });

    return feed;
  };
  /**
   * @description 获取资讯组
   * @link https://sg-assets.levelinfiniteapps.com/common/cmsutils/guide/cms/full-cms-api.html#%E8%8E%B7%E5%8F%96%E8%B5%84%E8%AE%AF%E7%BB%84
   * @param   {IReqDetailGroup}  params  [params description]
   * @return  {[type]}                   [return description]
   */
  const getGroupFeedDetail = async (params: IReqDetailGroup) => {
    return await cms_helper.getGroupFeedDetail(params);
  };

  const useCmsJump = (item: IDetail): void => {
    const { openUrlWithAuth } = useWebCredential();

    const { external_link, is_group_content, content_id, father_content_id, not_jump } =
      parseCMSJumpDetail(item, openUrlWithAuth);

    if (external_link || not_jump) {
      return;
    }

    // query 中出现空值，会导致返回时需要多返回一次，提前移除空值
    is_group_content
      ? router.push({
          path: Routes.INFO_GROUPS,
          query: removeObjectEmptyKey({ group_id: content_id }),
        })
      : router.push({
          path: Routes.INFO_DETAIL,
          query: removeObjectEmptyKey({ content_id, father_content_id }),
        });
    return;
  };

  const getImage = (item: IDetail & { mobile_pic_urls: Array<string> }): string => {
    const pc_img = item.pic_urls?.[0] || "";
    const mobile_img = item.mobile_pic_urls?.[0] || "";
    return mobile_img || pc_img;
  };

  /**
   * @description 获取intl票据
   * @link https://sg-assets.levelinfiniteapps.com/common/cmsutils/guide/cms/full-cms-api.html#%E6%8D%A2%E5%8F%96-pa-%E7%A5%A8%E6%8D%AE
   */
  const loginByIntlToken = async () => {
    const already_login = cms_helper.is_login && cms_helper.uid && cms_helper.ticket;
    if (!already_login) {
      const meta = getLoginMeta();

      if (isEmpty(meta)) return;

      // log("[loginByIntlToken] meta", meta);

      const params = {
        channel_id: meta?.channel_info?.channelId,
        login_res: meta,
        intl_game_id: CMS_GAME_ID,
      };

      // 调用后，cms_helper 会自动把用户的 uid 或者  ticket 设置在 cms_helper 上
      await cms_helper.loginByIntlToken(params);
    }

    // 实测 cms_helper 会自动添加 header， 手动添加反倒会出现 X-Uid 重复的错误
    // if (!cms_helper.headers["X-Uid"]) cms_helper.setHeaders({ "X-Uid": cms_helper.uid });
    // if (!cms_helper.headers["X-Ticket"]) cms_helper.setHeaders({ "X-Ticket": cms_helper.ticket });

    if (!cms_helper.headers["X-uid"]) {
      cms_helper.setHeaders({
        "X-uid": cms_helper.uid, // 部分接口不会自动添加header，但是获取banner的v2接口，会自动判断添加 X-uid(不是X-Uid)、X-Ticket，如果大小写不一致，会认为不存在，再次添加会导致逗号分隔的重复uid，为了兼容sdk的写法，保持拼写一致
        "X-Ticket": cms_helper.ticket,
      });
    }
  };

  /**
   * @description 点赞、取消点赞
   */
  const likeOperation = async (params: { content_id: string; op_type: LikeOp }) => {
    await loginByIntlToken();
    await cms_helper.likeOperation({
      ...params,
      biz_id: 1,
      uid: cms_helper.uid,
    });
  };

  /**
   * @description 批量获取点赞状态
   * @link https://sg-assets.levelinfiniteapps.com/common/cmsutils/guide/cms/full-cms-api.html#%E7%82%B9%E8%B5%9E%E7%9B%B8%E5%85%B3
   */
  const batchGetLikeInfo = async (content_ids: string[]) => {
    const { data } = await cms_helper.batchGetLikeInfo({
      content_ids,
      uid: cms_helper.uid,
      need_like_status: true,
      biz_id: 1,
    });

    return data?.like_info_items;
  };

  /**
   * @description 请求点赞信息
   */
  const getFeedLikeInfo = async (content_id: string) => {
    await loginByIntlToken();
    const info: any = await batchGetLikeInfo([content_id]);
    return info?.[0];
  };

  return {
    cms_helper,
    likeOperation,
    getFeedLikeInfo,
    getSecondColumnConfig,
    getFeedDetail,
    getSecondColumnData,
    getGroupFeedDetail,
    useCmsJump,
    getImage,
  };
};

export const resolveLoadInfoContentForList = (info_content: any) => {
  const lang = getStandardizedLang();
  const game_id = getStandardizedGameId();
  return (
    info_content
      ?.map((item: any) => {
        return {
          ...item,
          src: item.pic_urls || [],
          desc: item.content_part, // || item.content_desc,
          is_video: Boolean(item.video_info),
          time: ymd(item.pub_timestamp),
          source: item?.content_source?.content_source,
        };
      })
      .filter((i: any) => filterCmsItem({ ext_info: i.ext_info, lang, game_id })) || []
  );
};

export const useBsCmsListCpnt = (
  props: CmsListCpntCommonProps,
): {
  list: any;
  is_finish: Ref<boolean>;
  next_offset: Ref<number>;
  loading: Ref<boolean>;
  load: (load_params: CmsListCpntLoadParams, load_options?: CmsListCpntLoadOptions) => any;
  loadMore: (
    load_more_params?: CmsListCpntLoadParams,
    load_more_options?: CmsListCpntLoadOptions,
  ) => void;
  getLoadParams: (params?: CmsListCpntLoadParams) => CmsListCpntLoadParams;
  getLoadOptions: (options?: CmsListCpntLoadOptions) => CmsListCpntLoadOptions;
  reset: () => void;
} => {
  const cms_config = props.cms_config;
  //
  const primary_column_name = props.primary_column_name;
  const second_column_name = props.second_column_name;
  const game_code = props.game_code;
  const content_class = props.content_class || CmsContentClass.info;
  const with_off = props.with_off || false;
  const get_num = props.get_num || 10;
  const offset = props.offset || 0;
  const group_id = props.group_id;
  //
  // const env = props.env || getEnv();
  const list = ref(props.list || []);
  const is_finish = ref(false);
  const next_offset = ref(0);
  const loading = ref(false);
  //
  let load: any = null;

  const getLoadParams = (params?: CmsListCpntLoadParams): CmsListCpntLoadParams => {
    return Object.assign(
      group_id
        ? {
            content_id: group_id,
          }
        : {
            content_class,
          },
      {
        with_off,
        get_num,
        offset,
      },
      params || {},
    );
  };

  const getLoadOptions = (options?: CmsListCpntLoadOptions) => {
    return Object.assign(
      {
        primary: primary_column_name,
        second: second_column_name,
      },
      options || {},
    ) as CmsListCpntLoadOptions;
  };

  const reset = () => {
    list.value = [];
    is_finish.value = false;
    next_offset.value = 0;
    loading.value = false;
  };

  if (
    cms_config &&
    // 合集可以直接加载，不需要一级栏目和二级栏目
    (primary_column_name || group_id) &&
    // 这里 cms_areaid 或者 game_code 必须要有才能确保 cms_areaid 字段的正确性
    (cms_config?.cms_areaid || game_code)
  ) {
    //
    const { getSecondColumnData, getGroupFeedDetail } = useCms({
      cms_config,
    });
    //
    const params = getLoadParams();
    const options = getLoadOptions();
    //
    if (!load) {
      load = async (load_params: CmsListCpntLoadParams, load_options?: CmsListCpntLoadOptions) => {
        const api = group_id ? getGroupFeedDetail : getSecondColumnData;
        // @ts-ignore
        const res = await api(load_params || params, load_options || options);
        if (res.code !== 0) {
          const error = { msg: res.msg, request_id: res.request_id };
          logAndReportError(error.msg, error);
          throw new Error(JSON.stringify(error));
        }
        return res.data;
      };
    }
    // 合集可以直接加载，不需要二级栏目
    if (second_column_name || group_id) {
      loading.value = true;
      load(params, options)
        .then((data: any) => {
          // console.log("[useBsCmsListCpnt][load] data", data);
          const { is_finish: finished, next_offset: offset, info_content: content } = data;
          is_finish.value = Boolean(finished);
          next_offset.value = offset;
          // const res = content.concat(content);
          list.value = resolveLoadInfoContentForList(content);
        })
        .finally(() => {
          loading.value = false;
          props.callback?.();
        });
    }
  }

  const loadMore = async (
    load_more_params?: CmsListCpntLoadParams,
    load_more_options?: CmsListCpntLoadOptions,
  ) => {
    try {
      if (loading.value) {
        return;
      }
      loading.value = true;
      const params = load_more_params || getLoadParams({ offset: next_offset.value });
      const options = load_more_options || getLoadOptions();
      const {
        is_finish: finished,
        next_offset: offset,
        info_content: content,
      } = await load(params, options);

      is_finish.value = Boolean(finished);
      next_offset.value = offset;

      list.value.push(
        ...getUniqueArr(list.value, resolveLoadInfoContentForList(content), "content_id"),
      );
      loading.value = false;
    } catch (error) {
      logAndReportError("", error);
      loading.value = false;
    } finally {
      props.callback?.();
    }
  };

  return {
    list,
    is_finish,
    next_offset,
    loading,
    load,
    loadMore,
    getLoadParams,
    getLoadOptions,
    reset,
  };
};
