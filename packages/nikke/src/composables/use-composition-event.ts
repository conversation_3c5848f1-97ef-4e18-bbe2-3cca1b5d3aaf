import { Ref, ref } from "vue";

export const useCompositionEvent = (): {
  composition: Ref<boolean>;
  onAddCompositionsEvent: (el: HTMLElement) => void;
  onRemoveCompositionsEvent: (el: HTMLElement) => void;
} => {
  const composition = ref(false);

  const onCompositionstart = () => {
    composition.value = true;
  };

  const onCompositionend = () => {
    composition.value = false;
  };

  const onAddCompositionsEvent = (el: HTMLElement) => {
    if (!el) return;
    el.addEventListener("compositionstart", onCompositionstart);
    el.addEventListener("compositionend", onCompositionend);
  };

  const onRemoveCompositionsEvent = (el: HTMLElement) => {
    if (!el) return;
    el.removeEventListener("compositionstart", onCompositionstart);
    el.removeEventListener("compositionend", onCompositionend);
  };

  return {
    composition,
    onAddCompositionsEvent,
    onRemoveCompositionsEvent,
  };
};
