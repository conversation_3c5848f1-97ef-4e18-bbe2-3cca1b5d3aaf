import { Component, createApp, reactive } from "vue";
import { useExpose } from "@/composables/use-expose";
import { useMain } from "@/composables/use-main";

const { installAppPlugins } = useMain();

let z_index = 10000;

export type MountOptions = string | any;

export type MergeOptions = {
  type?: string;
  auto_close?: boolean;
  resolve?: (value: number | PromiseLike<number>) => void;
};

export type MountReturn = {
  instance?: any;
  unmount?: () => void;
};

export const resolvedOptions = (options: string | Object | any, merge?: any) => {
  let opts = null;
  if (!options) {
    opts = {};
  } else if (typeof options === "string") {
    opts = {
      content: options,
    };
  } else {
    opts = {
      ...options,
    };
  }
  if (merge) {
    Object.assign(opts, merge);
  }
  return opts;
};

export function usePopupState(options: any) {
  z_index++;
  const state = reactive({
    show: false,
    z_index: z_index,
    ...options,
  });

  // console.log("[usePopupState] state", state);

  const toggle = (show: boolean) => {
    state.show = show;
  };

  const open = (props: any) => {
    Object.assign(state, props);
    toggle(true);
  };

  const close = () => toggle(false);

  useExpose({ open, close, toggle });

  return {
    open,
    close,
    state,
    toggle,
  };
}

export function mountComponent(cpnt: Component) {
  const app = createApp(cpnt);
  const root = document.createElement("div");
  document.body.appendChild(root);
  const instance = app.mount(root);
  // console.log("[mountComponent] app", app);
  // console.log("[mountComponent] instance", instance);
  return {
    unmount() {
      app.unmount();
      document.body.removeChild(root);
    },
    instance,
    app,
  };
}

export function mountDialogComponent(component: Component, props?: any) {
  const app = createApp(component, props ?? null);
  installAppPlugins(app);
  const root = document.createElement("div");
  document.body.appendChild(root);
  const instance = app.mount(root);
  return {
    unmount() {
      app.unmount();
      document.body.removeChild(root);
    },
    instance,
    app,
  };
}

/**
 * @description 示例化一个游离态组件
 * @note toast 是需要定时清除的
 * @param component
 */
export const mountToastComponent = <T>(component: new () => T, props?: any) => {
  const temp_dom = document.createElement("div");
  document.body.appendChild(temp_dom);
  const temp_app: any = createApp(component, props ?? null);
  const instance = temp_app.mount(temp_dom);
  return {
    instance,
    app: temp_app,
    unmount() {
      temp_app.unmount();
      document.body.removeChild(temp_dom);
    },
  };
};
