export const isFunction = (val: unknown): val is Function => typeof val === "function";
export const isDef = <T>(val: T): val is NonNullable<T> => val !== undefined && val !== null;
export const isObject = (val: unknown): val is Record<any, any> =>
  val !== null && typeof val === "object";

export const isSupportIntersectionObserver = () => "IntersectionObserver" in window;
export const isSupportOnBeforeInstallPrompt = () => "onbeforeinstallprompt" in window;
