/**
 * 启动 Nikke 游戏
 *
 * <AUTHOR>
 * @create 2024-10-12
 */
import { isMobileDevice } from "packages/utils/tools";
import { getStandardizedGameId } from "packages/utils/standard";

/**
 * 获取启动地址
 */
export function getScheme() {
  const is_mobile = isMobileDevice();
  const game_id = getStandardizedGameId();
  if (is_mobile) {
    return (
      {
        "29157": "pbpsdknhmt://", // 港澳台
      }[game_id] || "pbpsdknikke://" // 国际版
    );
  }
  return (
    {
      "29157": "nikkelauncherhmt://", // PC 港澳台
    }[game_id] || "nikkelauncher://" // PC 国际版
  );
}

/**
 * 获取通用链
 */
export function getAppLink(): string {
  const game_id = getStandardizedGameId();
  return (
    {
      "29157": "https://nikke-hmt.onelink.me/6VPY/ynaa93mr", // 港澳台
    }[game_id] || "https://nikke.onelink.me/cCNP/cj5clvuc" // 国际版
  );
}

/**
 * 获取商店地址
 */
export function getDownload(): string {
  const is_mobile = isMobileDevice();
  const game_id = getStandardizedGameId();
  if (is_mobile) {
    return getAppLink();
  }
  return (
    {
      "29157": "https://nikke.hotcool.tw/download/", // PC 港澳台
    }[game_id] || "https://nikke-en.com/download.html" // PC 国际版
  );
}

/**
 * 新开网址
 */
export function open(url: string, force = false) {
  const win = window.open(url, "_blank");
  // 窗口被阻止
  if (force && (!win || !("closed" in win) || win.closed)) {
    window.location.assign(url);
  }
  return win;
}

/**
 * 检查启动
 */
async function check(timeout = 3000): Promise<boolean> {
  const vendor =
    ["ms", "moz", "webkit"].find((name) => (document as any)[`${name}Hidden`] !== undefined) || "";
  const hidden = vendor ? `${vendor}Hidden` : "hidden";
  const visibilityState = vendor ? `${vendor}VisibilityState` : "visibilityState";
  const visibilitychange = `${vendor}visibilitychange`;

  return new Promise((resolve) => {
    const handler = () => {
      clearTimeout(timer);
      document.removeEventListener(visibilitychange, handler);
      document.removeEventListener("baiduboxappvisibilitychange", handler);
      (window as any).mqq?.removeEventListener("qbrowserVisibilityChange", handler);
      resolve((document as any)[hidden] || (document as any)[visibilityState] === "hidden");
    };

    const timer = setTimeout(handler, timeout);
    document.addEventListener(visibilitychange, handler);
    document.addEventListener("baiduboxappvisibilitychange", handler);
    (window as any).mqq?.addEventListener("qbrowserVisibilityChange", handler);
  });
}

/**
 * 启动 APP
 *
 * @notice 必须由用户触发 (window.open), 效果:
 * - applink (默认)
 *   - 已安装: 直接打开 APP (如果 Safari 不是默认浏览器, 可能打不开)
 *   - 未安装: iOS 打开商店, Android 提示下载
 * - scheme
 *   - 已安装: 弹框询问是否打开 APP
 *   - 未安装: iOS 弹框报错, Android 无反应
 */
export async function launch(type: "scheme" | "applink" = isMobileDevice() ? "applink" : "scheme") {
  // Scheme 方式
  if (type === "scheme") {
    window.location.assign(getScheme());
    if (await check()) return;
    // 启动超时
    return Promise.reject({
      name: "TimeoutError",
      message: "Launch Game Timeout",
    });
  }

  // Applink 方式
  try {
    const win = open(getAppLink(), true);
    // 启动遗留
    await new Promise((resolve) => setTimeout(resolve, 5000));
    win?.location.href === "about:blank" && win.close();
  } catch {
    // 已开商店
  }
}

/**
 * 下载 APP
 *
 * @notice 如果 iOS 报错: `Safari cannot open the page because the address is invalid.`
 * - 原因: 上一次 scheme 被手动取消后, 再次用 JS 跳转的 scheme 无法执行
 * - 补充 1: APP Store Link 在 Safari 会重定向到 `itms-appss://`
 * - 补充 2: 点击事件 1 秒内可无视上一次选择
 */
export function download(force = false) {
  open(getDownload(), force);
}
