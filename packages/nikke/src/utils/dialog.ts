import { Component, DefineComponent, defineComponent, h, onMounted, PropType, ref } from "vue";
import { mountDialogComponent } from "./mount";

/**
 * 弹框参数 基础类型
 * - 所有弹框的 props 都应该继承于该类型
 * - FinishData 泛型代表弹框返回的数据，可选
 */
export interface BaseDialog<FinishData extends any = void> {
  /** 取消并关闭弹框 */
  cancel: () => void;
  /** 完成并关闭弹框，参数为弹框返回的数据 */
  finish: (data: FinishData) => void;
  /** 弹框动画状态（预留字段，后续基于此字段可实现关闭动画） */
  visible: boolean;
  /** 是否正在完成中（传入异步 onFinish 事件时） */
  is_finishing: boolean;
  /** 是否正在取消中（传入异步 onCancel 事件时） */
  is_cancelling: boolean;
}

export type DialogConfig<T extends Component> = Omit<
  GetDialogProps<DialogComponentProps<T>>,
  "onFinish" | "onCancel"
> & {
  onFinish?: (data: GetDialogFinishData<DialogComponentProps<T>>) => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
};

/**
 * 打开弹框
 * @param com 弹框组件，props 需要继承 BaseDialog
 * @param options 弹框打开参数, 需要满足弹框的 props 约束
 * @returns 返回主动关闭的方法
 */
export function showDialog<
  DialogComponent extends Component,
  FinishData extends GetDialogFinishData<
    DialogComponentProps<DialogComponent>
  > = GetDialogFinishData<DialogComponentProps<DialogComponent>>,
>(com: DialogComponent, options: DialogConfig<DialogComponent>): { close: () => Promise<void> } {
  let forceClose = async () => {};

  const { unmount } = mountDialogComponent(
    defineComponent({
      setup() {
        const visible = ref(false);
        const is_finishing = ref(false);
        const is_cancelling = ref(false);
        const data = ref(options);
        onMounted(() => {
          visible.value = true;
        });
        const finish = async (data: FinishData) => {
          is_finishing.value = true;
          await options?.onFinish?.(data);
          is_finishing.value = false;
          unmount();
        };
        const cancel = async () => {
          is_cancelling.value = true;
          await options?.onCancel?.();
          is_cancelling.value = false;
          unmount();
        };
        forceClose = async () => {
          await cancel();
        };
        return { visible, data, is_finishing, is_cancelling, finish, cancel };
      },
      render: function () {
        return h(com as any, {
          ...this.data,
          visible: this.visible,
          is_finishing: this.is_finishing,
          is_cancelling: this.is_cancelling,
          finish: this.finish,
          cancel: this.cancel,
        });
      },
    }),
  );

  const result = { close: forceClose, unmount };
  return result as any;
}

/** 弹框内置参数，自定义参数不得使用 */
export type DialogInsetPropsKeys =
  | "cancel"
  | "finish"
  | "visible"
  | "is_finishing"
  | "is_cancelling";

type DialogComponentProps<T> =
  T extends DefineComponent<infer R, any, any, any, any>
    ? R
    : T extends () => Promise<{ default: DefineComponent<infer R, any, any, any, any> }>
      ? R
      : never;

type GetDialogRequiredProps<
  ComProps extends Record<string, { required?: boolean; type: PropType<any> }>,
> = {
  [K in keyof ComProps as ComProps[K] extends { required: true }
    ? K
    : never]: ComProps[K]["type"] extends PropType<infer T> ? T : never;
};
type GetDialogPartialProps<
  ComProps extends Record<string, { required?: boolean; type: PropType<any> }>,
> = Partial<{
  [K in keyof ComProps as ComProps[K] extends { required: true }
    ? never
    : K]: ComProps[K]["type"] extends PropType<infer T> ? T : never;
}>;

/**
 * 兼容多个vue版本的类型
 * - vue 升级到 3.5 后，props 类型获取不再需要从 Options 格式中进行复杂转换，此处保留兼容处理
 */
export type GetDialogProps<ComProps> =
  ComProps extends Record<string, { required?: boolean; type: PropType<any> }>
    ? Omit<GetDialogRequiredProps<ComProps> & GetDialogPartialProps<ComProps>, DialogInsetPropsKeys>
    : ComProps extends Record<string, any>
      ? Omit<ComProps, DialogInsetPropsKeys>
      : never;

/**
 * 兼容多个vue版本的类型
 * - vue 升级到 3.5 后，props 类型获取不再需要从 Options 格式中进行复杂转换，此处保留兼容处理
 */
export type GetDialogFinishData<ComProps> =
  ComProps extends Record<string, any>
    ? ComProps["finish"]["type"] extends PropType<infer K extends (...args: any) => any>
      ? Parameters<K>[0]
      : ComProps["finish"] extends infer K extends (...args: any) => any
        ? Parameters<K>[0]
        : never
    : never;
