import { PostDetail, PostItem } from "packages/types/post";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { EMOJI_IMAGE_SYMBOL } from "@/configs/const";
import { EventPayload } from "packages/utils/event-emitter";
import { aegis } from "@/shiftyspad/service/rum";

const { setIsDeletedValue } = useIsDeleted();

// 获取当前帖子的操作权限
export const getPostItemOperationAuth = (
  item: PostItem,
): {
  status: number; // 1-自己的帖子，2-未关注对方 3-关注对方，对方未关注自己 4-互关
  icon: string;
  iconcolor?: string;
} => {
  if (item.is_mine) return { status: 1, icon: "icon-ellipsis", iconcolor: "#959596" };
  if (item.is_mutual_follow) return { status: 4, icon: "iocn-follower-cur", iconcolor: "#3EAFFF" };
  if (item.is_follow) return { status: 3, icon: "icon-followed", iconcolor: "var(--brand-1)" };
  return { status: 2, icon: "", iconcolor: "" };
};

export const resovledPostListCard = (card: PostItem): PostItem => {
  card.pic_urls = card.pic_urls.filter((url: string) => !url.includes(EMOJI_IMAGE_SYMBOL));
  return card;
};

// 将字段带count的值转为int类型
export const dealPostData = (arr: PostItem[]) => {
  const first = arr[0];
  if (!first) return arr;

  const countKeys = Object.keys(first).filter((k) => k.includes("_count"));

  arr.forEach((item) => {
    setIsDeletedValue(item, false);
    item.pic_urls = item.pic_urls.filter((i) => !!i);
    countKeys.forEach((k) => {
      const key = k as keyof typeof item;
      (item[key] as unknown) = Number(item[key]);
    });

    resovledPostListCard(item);
  });

  return arr;
};

// 批量更新帖子状态
export const updatePostsData = (
  target: PostItem | PostDetail, // 操作的post
  list: PostItem[], // post列表
  newData: any, // 新的post数据（部分字段）
) => {
  if (newData.pic_urls) {
    newData.pic_urls = newData.pic_urls.filter((url: string) => !!url);
  }
  [target, ...list].forEach((item) => {
    if (item.post_uuid === target.post_uuid) {
      if (item === newData) return;
      for (const key in newData) {
        // 避免列表帖子内容
        if (["language", "title", "content", "content_summary"].includes(key)) continue;
        (item as any)[key] = newData[key];
      }
    }
    // 更新关注状态
    if (item.user.intl_openid === target.user.intl_openid) {
      item.is_follow = newData.is_follow;
      item.is_mutual_follow = newData.is_mutual_follow;
    }
  });
};

// 批量更新帖子列表中的用户状态
export const updateUserStatusInPostList = (
  postList: PostItem[],
  event_payload: EventPayload["user_status_change"],
) => {
  postList.forEach((item) => {
    if (item.user.intl_openid === event_payload.intl_openid) {
      if (event_payload.is_black !== undefined) item.user.is_black = event_payload.is_black;
      if (event_payload.is_followed !== undefined)
        item.user.is_followed = event_payload.is_followed;
      if (event_payload.is_mutual_follow !== undefined)
        item.user.is_mutual_follow = event_payload.is_mutual_follow;
    }
    if (item.intl_openid === event_payload.intl_openid) {
      if (event_payload.is_followed !== undefined) item.is_follow = event_payload.is_followed === 1;
      if (event_payload.is_mutual_follow !== undefined)
        item.is_mutual_follow = event_payload.is_mutual_follow === 1;
    }
  });
};

// 移除重复帖子
export const removeDuplicatePost = (postList: PostItem[]) => {
  const postMap = new Map<string, PostItem>();
  postList.forEach((post) => {
    if (postMap.has(post.post_uuid)) {
      aegis.report({
        msg: `duplicate-post: ${post.post_uuid} ${post.title}`,
        level: aegis.LogType.REPORT,
      });
      return;
    }
    postMap.set(post.post_uuid, post);
  });
  return Array.from(postMap.values());
};
