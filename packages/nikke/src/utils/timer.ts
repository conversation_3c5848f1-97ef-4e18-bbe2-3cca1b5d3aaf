import { CreateTimerReturn } from "@/types/timer";
/**
 * @description 创建定时器
 * @param callback 定时器回调函数
 * @param interval 定时器间隔时间
 * @param loop 是否循环执行定时器，默认为 true
 * @return 返回一个包含控制定时器的方法的对象
 */
export const createTimer = (
  callback: () => void,
  options: {
    interval: number;
    loop?: boolean;
    immediate?: boolean;
  },
): CreateTimerReturn => {
  let paused = false;
  let last_time: number = Date.now();
  let timer_id: ReturnType<typeof setTimeout> | null;

  const { interval, loop, immediate } = Object.assign({ loop: false, immediate: false }, options);

  const tick = () => {
    if (paused) return;

    const now = Date.now();
    if (!last_time || now - last_time >= interval) {
      callback();
      last_time = now;

      if (!loop) {
        cancel();
        return;
      }
    }

    timer_id = setTimeout(tick, Math.max(0, interval - (now - last_time)));
  };

  if (immediate) {
    callback();
    last_time = Date.now();
  }

  timer_id = setTimeout(tick, interval);

  const pause = () => {
    paused = true;
    if (timer_id !== null) {
      clearTimeout(timer_id);
      timer_id = null;
    }
  };

  const resume = () => {
    if (paused) {
      paused = false;
      last_time = Date.now();
      timer_id = setTimeout(tick, interval);
    }
  };

  const cancel = () => {
    if (timer_id !== null) {
      clearTimeout(timer_id);
      timer_id = null;
    }
    last_time = Date.now();
    paused = false;
  };

  const isPaused = () => {
    return paused === true;
  };

  return {
    pause,
    resume,
    cancel,
    isPaused,
  };
};
