import dayjs from "dayjs";

export function f(str: any, temp = "-"): string {
  if (!str) {
    return temp;
  }
  return typeof str === "string" ? str : f(str.toString(), temp);
}

export function formatNum(num: number | string) {
  const thousand = 1000;
  const million = 1000 * thousand;
  const sign = Number(num) < 0 ? "-" : "";
  const map = {
    M: million,
    K: thousand,
  } as const;
  const keys = Object.keys(map) as (keyof typeof map)[];
  const res = Math.abs(Number(num));
  for (let i = 0; i < keys.length; i++) {
    if (res > 10 * map[keys[i]]) {
      return `${sign}${Math.round(res / map[keys[i]])}${keys[i]}`;
    }
  }
  return `${sign}${num}`;
}

export const patch = (val: number | string, count: number) => {
  let result = String(val);
  while (result.length < count) {
    result = `0${result}`;
  }
  return result;
};

const convertToHTMLSpan = (text: string) => {
  const regex = /<color=(#[0-9a-fA-F]{6})>|<\/color>/g;

  // @ts-ignore
  const result = text.replace(regex, (_match: string, color1: string, color2: string) => {
    if (color1) {
      return `<span style="color: ${color1};">`;
    }
    if (color2) {
      return `</span>`;
    }
  });

  return result;
};

export const parseNewLine = (str: string) => {
  str = str.replace(new RegExp(`\\n`, "g"), "<br />");
  str = str.replace(new RegExp(`\\\\n`, "g"), "<br />");
  return str;
};

// 格式化时间
export function formatTime(timestamp: number, t: Function): string {
  const now = dayjs();
  const inputTime = timestamp === 0 ? now : dayjs(timestamp);
  let diffMinutes = now.diff(inputTime, "minute");
  const diffHours = now.diff(inputTime, "hour");
  // const diffDays = now.diff(inputTime, "day");
  const isSameYear = now.year() === inputTime.year();

  if (diffMinutes < 60) {
    if (diffMinutes < 1) diffMinutes = 1;
    return `${diffMinutes} ${t("min_before")}`;
  } else if (diffHours < 24) {
    return `${diffHours} ${t("hour_before")}`;
  } else if (isSameYear) {
    return inputTime.format("MM/DD");
  } else {
    return inputTime.format("YYYY/MM/DD");
  }
}

// 格式化数量
export function formatInteractNum(num: number) {
  if (num <= 0) return "";
  if (num > 0 && num <= 999) return String(num);
  if (num > 999 && num <= 9999) return "999+";
  return "10K+";
}

export const formatUnRealseTime = (time: number) => {
  const current_year = dayjs().year();
  const mm = time * 1000;
  const is_current_year = current_year === dayjs(mm).get("year");

  const formatter = is_current_year ? "MM-DD HH:mm" : "YYYY-MM-DD";
  return dayjs(mm).format(formatter);
};
