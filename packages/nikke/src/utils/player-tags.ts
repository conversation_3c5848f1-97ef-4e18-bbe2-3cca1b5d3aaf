import tower from "@/assets/svg/icon-tower.svg";
import battle_normal from "@/assets/svg/icon-battle-normal.svg";
import battle_hard from "@/assets/svg/icon-battle-hard.svg";
import nikkes from "@/assets/svg/icon-nikkes.svg";
import frames from "@/assets/svg/icon-avatar-frames.svg";
import costumes from "@/assets/svg/icon-costumes.svg";
import { PlayerTagId } from "packages/types/tag";
export type PlayerTag = {
  icon: string;
  id: number;
  key: PlayerTagId;
  i18n: string;
  // value?: string;
  // checked?: boolean;
};

export const tags_config: PlayerTag[] = [
  {
    icon: tower,
    key: PlayerTagId.tower,
    id: 1,
    i18n: "main_tower",
  },
  {
    icon: battle_normal,
    key: PlayerTagId.battle_normal,
    id: 2,
    i18n: "battle_normal",
  },
  {
    icon: battle_hard,
    key: PlayerTagId.battle_hard,
    id: 3,
    i18n: "battle_hard",
  },
  {
    icon: nikkes,
    key: PlayerTagId.nikkes,
    id: 4,
    i18n: "main_nikke_num",
  },
  {
    icon: frames,
    key: PlayerTagId.frames,
    id: 5,
    i18n: "main_avatar_frame",
  },
  {
    icon: costumes,
    key: PlayerTagId.costumes,
    id: 6,
    i18n: "main_costume",
  },
];
