import { EMOJI_IMAGE_SYMBOL } from "@/configs/const";
import { getDefaultOpenUrlTarget, openUrl } from "packages/utils/tools";
import { useEnsureUrl, EnsureType } from "@/composables/use-ensure.ts";

/**
 * [export 格式化日期]
 *
 * @param   {number}  timestamp  [timestamp description]
 * @param   {[type]}  Date       [Date description]
 * @param   {string}  format     [format description]
 *
 * @return  {[type]}             [return description]
 */
export function formatDate(timestamp: number | Date, format: string) {
  if (!timestamp) {
    return "";
  }

  let date;
  if (typeof timestamp === "number") {
    if (timestamp.toString().length === 10) {
      // 10 位时间戳
      date = new Date(timestamp * 1000);
    } else if (timestamp.toString().length === 13) {
      // 13 位时间戳
      date = new Date(timestamp);
    } else {
      return "";
    }
  } else if (timestamp instanceof Date) {
    date = timestamp;
  } else {
    return "";
  }

  const options: Intl.DateTimeFormatOptions = {};

  switch (format) {
    case "MM-DD":
      options.month = "2-digit";
      options.day = "2-digit";
      break;
    case "YYYY-MM-DD":
      options.year = "numeric";
      options.month = "2-digit";
      options.day = "2-digit";
      break;
    case "DD/MM/YYYY":
      options.day = "2-digit";
      options.month = "2-digit";
      options.year = "numeric";
      break;
    case "MM/DD/YYYY":
      options.month = "2-digit";
      options.day = "2-digit";
      options.year = "numeric";
      break;
    case "YYYY/MM/DD":
      options.year = "numeric";
      options.month = "2-digit";
      options.day = "2-digit";
      break;
    default:
      options.month = "2-digit";
      options.day = "2-digit";
      break;
  }

  return new Intl.DateTimeFormat("zh-CN", options).format(date);
}

export const isSiteEmojiImage = (url: string): boolean => {
  return url.indexOf(EMOJI_IMAGE_SYMBOL) > -1;
};

// 高亮显示搜索关键字
export const highlight = (target: string, keyword: string, classname: string): string => {
  // 使用正则表达式匹配关键字
  const regex = new RegExp(keyword, "gi");
  return target.replace(regex, (match) => `<span class="${classname}">${match}</span>`);
};

/**
 * @description
 *
 * @return  {[type]}  [return description]
 */
export const openUrlWithWhiteListQuery = (
  url: string,
  target: undefined | string = getDefaultOpenUrlTarget(url),
  window_features: undefined | string = "noopener,noreferrer",
) => {
  const { ensure } = useEnsureUrl();
  const new_url = ensure(EnsureType.location, url) as string;
  openUrl(new_url, target, window_features);
};
