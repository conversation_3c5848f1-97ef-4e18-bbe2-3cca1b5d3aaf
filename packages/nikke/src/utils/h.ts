/**
 * @link https://github.com/dnldsht/vue-insta-stories/blob/main/packages/lib/src/utils/h-demi.ts
 */

import { h as hDemi } from "vue";
import { isFunction } from "./is";

interface Options {
  props?: Object;
  domProps?: Object;
  on?: Object;
}

const adaptOnsV3 = (ons: Object) => {
  if (!ons) return null;
  return Object.entries(ons).reduce((ret, [key, handler]) => {
    key = key.charAt(0).toUpperCase() + key.slice(1);
    key = `on${key}`;
    return { ...ret, [key]: handler };
  }, {});
};

const h = (type: String | Object, options: Options & any = {}, chidren?: any) => {
  const { props, domProps, on, ...extraOptions } = options;

  let ons = adaptOnsV3(on);
  const params = { ...extraOptions, ...props, ...domProps, ...ons };
  return hDemi(type, params, chidren);
};

// @ts-ignore
const slot = (s, attrs?) => {
  if (isFunction(s)) return s(attrs);
  return s;
};
export { slot };

export default h;
