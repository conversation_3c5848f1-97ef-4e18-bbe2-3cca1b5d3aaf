/**
 * @description 本文件内均是补丁方法
 *
 * 非规范实现（有坑）；需要后端配合或后期排期修复
 */

/**
 * @description OneTrust 禁止了创建iframe, 这是一个补丁方法
 */
export function getIframeEl(src: string) {
  const iframe_el = document.createElement("iframe");
  iframe_el.src = src;
  // it was hijacked by ot
  if (src && !iframe_el.getAttribute("src") && iframe_el.getAttribute("data-src")) {
    const container = document.createElement("div");
    container.innerHTML = iframe_el.outerHTML.replace("data-src=", "src=");
    const el = container.querySelector("iframe");
    if (el) {
      return el;
    }
  }
  return iframe_el;
}

/**
 * @description 有些场景仅有 [gameid]-[openid]格式的标识, 然后某些接口内需要使用 openid
 */
export function getOpenIdFromUid(uid: string = "") {
  return uid?.split("-")?.[1] ?? "";
}
