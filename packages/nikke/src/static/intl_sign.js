/* eslint-disable */
(function (_0x25fa2b, _0x528401) {
  const _0x464629 = _0x25fa2b();
  function _0x85f417(_0x24aba3, _0x318fee, _0x318b67, _0x27e404) {
    return _0x3980(_0x318b67 - 0x287, _0x27e404);
  }
  function _0x4a3b14(_0x2900d3, _0x12e595, _0x304034, _0x5e6201) {
    return _0x3980(_0x2900d3 - 0x3e4, _0x12e595);
  }
  while (!![]) {
    try {
      const _0x17f1c0 =
        -parseInt(_0x85f417(0x4b9, 0x4b3, 0x4be, 0x4a1)) / (-0x541 + -0x8 * -0x342 + -0x14ce) +
        -parseInt(_0x4a3b14(0x5f9, 0x5e1, 0x612, 0x621)) / (0x13b0 + 0x1611 * 0x1 + -0x29bf) +
        (parseInt(_0x85f417(0x49b, 0x463, 0x487, 0x46c)) / (0xa * -0x2d + 0x1 * 0x2b7 + -0xf2)) *
          (parseInt(_0x4a3b14(0x5c8, 0x5b6, 0x5cb, 0x5ba)) / (-0x1b15 + 0x3 * 0xb65 + -0x716)) +
        parseInt(_0x85f417(0x4bb, 0x4b4, 0x48f, 0x46f)) / (0x144c + 0x12ed + 0xd * -0x304) +
        (parseInt(_0x85f417(0x44b, 0x450, 0x476, 0x44e)) / (-0x71e + -0x1 * 0x26f5 + 0x2e19)) *
          (-parseInt(_0x85f417(0x468, 0x47d, 0x48a, 0x4a4)) / (0x752 + 0x167e + -0x1dc9)) +
        (-parseInt(_0x4a3b14(0x5e8, 0x5c7, 0x609, 0x5d8)) / (0x13c2 + 0x1d77 + -0x3131 * 0x1)) *
          (parseInt(_0x85f417(0x4ae, 0x4d1, 0x4b0, 0x49e)) /
            (-0x1d95 * 0x1 + 0x2 * -0xa + 0x1db2)) +
        (-parseInt(_0x4a3b14(0x5ea, 0x5e2, 0x5c3, 0x612)) / (0x93b + -0x1e3b + 0x150a)) *
          (-parseInt(_0x85f417(0x4a6, 0x4c7, 0x4ad, 0x4d0)) / (0x25f7 + 0x16a1 + -0x3c8d));
      if (_0x17f1c0 === _0x528401) break;
      else _0x464629["push"](_0x464629["shift"]());
    } catch (_0x5ac48c) {
      _0x464629["push"](_0x464629["shift"]());
    }
  }
})(_0x187b, 0xe2f6 + -0xb4854 + -0x40fc * -0x56);
import _0x43a7a9 from "md5";
function createUuid() {
  const _0x44c893 = {};
  function _0x16535e(_0x409ce1, _0x5baef7, _0x5bec3c, _0xf440a3) {
    return _0x3980(_0xf440a3 - -0x239, _0x5bec3c);
  }
  function _0x55e1e9(_0x36e6d0, _0xbaf110, _0x3da38b, _0x281437) {
    return _0x3980(_0x36e6d0 - 0x1b9, _0x3da38b);
  }
  (_0x44c893[_0x55e1e9(0x3c5, 0x39a, 0x3e0, 0x3af)] = function (_0x97f376, _0x489fe7) {
    return _0x97f376 + _0x489fe7;
  }),
    (_0x44c893[_0x16535e(-0x30, -0x22, -0x45, -0x41)] = function (_0x3c9eba, _0x4e4143) {
      return _0x3c9eba + _0x4e4143;
    });
  const _0x54faf6 = _0x44c893;
  return _0x54faf6[_0x55e1e9(0x3c5, 0x3e9, 0x3b1, 0x3a1)](
    _0x54faf6[_0x55e1e9(0x3c5, 0x3a2, 0x3d8, 0x39d)](
      _0x54faf6[_0x55e1e9(0x3b1, 0x3b6, 0x398, 0x3b2)](
        [0x7b5777 * 0x1 + 0x68a1a2 + -0x4b6299],
        -(-0x2451 + 0x83 * 0x1d + 0x1962),
      ),
      -(0xbf * -0x29 + -0x1 * -0x142f + 0x1a08),
    ) + -(-0x2ec5 * -0x1 + -0x26 * -0x103 + -0x2d * 0x133),
    -(-0x4d * -0x3faa0db2 + 0x2d21d72a59 + -0x28ff8660e3),
  )["replace"](/[018]/g, (_0x5b3814) =>
    (_0x5b3814 ^
      (crypto[_0x55e1e9(0x3c4, 0x3c2, 0x3b8, 0x3b8) + _0x16535e(0x7, -0x3, -0xb, -0xe)](
        new Uint8Array(0x469 * 0x1 + 0x9d4 + -0x38f * 0x4),
      )[0x1f * -0x56 + -0x12bd + 0x1d27 * 0x1] &
        ((-0x3d * -0x14 + 0x558 + -0x1 * 0xa0d) >>
          (_0x5b3814 / (0x1cf * 0xb + 0xcd * 0x2b + -0x3650)))))["toString"](
      0x1a06 * -0x1 + 0x1ca + 0x613 * 0x4,
    ),
  );
}
function _0x187b() {
  const _0x3664f5 = [
    "ect",
    "test.undaw",
    "www.undawn",
    "toLowerCas",
    "get",
    "need\x20url",
    "zfCkF",
    "te.com",
    "&ts=",
    "taKCf",
    "split",
    "-jp.com",
    "?u=",
    "intel#!202",
    "3OTUEUw",
    "FcwuR",
    "ffantasy-g",
    "7364jUqkAa",
    "103120jIDruD",
    "http://",
    "10DhLJmf",
    "&a=",
    "3606885WfrtsL",
    "www.moriko",
    "xFXUO",
    "getRandomV",
    "nVJsV",
    "sMbOy",
    "function",
    "XzNIx",
    "fkings.com",
    "2$act",
    ".hotcool.t",
    "test.morik",
    "invalid\x20us",
    "2389298OcDcch",
    "bile.com",
    "n.game",
    "&u=",
    "morilife.c",
    "nikke-sea.",
    "url",
    "nikke.hotc",
    "com",
    "ame.com",
    "sg-pubg.pl",
    "www.towero",
    "nikke-jp.c",
    "ytVHK",
    "error",
    "global.com",
    "omorilife.",
    "27942310ourcdZ",
    "lanet.com",
    "isPlainObj",
    "9APiBhX",
    "finite.com",
    "alues",
    "www.nikke-",
    "test.nikke",
    "nikke-kr.c",
    "includes",
    "OORWc",
    "startsWith",
    "some",
    "host",
    "https://",
    "base.ajax\x20",
    "length",
    "1296947xLbgzN",
    "10005",
    "lobal.com",
    "params",
    ".game",
    "2721172Axpiup",
    "join",
    "slice",
    "test.hok5v",
    "www.dragon",
    "nn2.com",
    "-sea.com",
    "5.com",
    "nikke-glob",
    "test-sg-ac",
    "isEmptyObj",
    "3924inRGRB",
    "EgRTB",
    "getTime",
  ];
  _0x187b = function () {
    return _0x3664f5;
  };
  return _0x187b();
}
function _0x3980(_0x16e014, _0x4df101) {
  const _0x4a10a9 = _0x187b();
  return (
    (_0x3980 = function (_0x5ae4e5, _0x2c2ea0) {
      _0x5ae4e5 = _0x5ae4e5 - (0x205b + 0x1 * 0x1894 + -0x370c);
      let _0x2dbad7 = _0x4a10a9[_0x5ae4e5];
      return _0x2dbad7;
    }),
    _0x3980(_0x16e014, _0x4df101)
  );
}
function objectToParam(_0x5812a9) {
  const _0x5b4c29 = {};
  (_0x5b4c29["xFXUO"] =
    _0x199b27(0x31c, 0x32f, 0x324, 0x325) + _0x199b27(0x2eb, 0x2f1, 0x309, 0x308)),
    (_0x5b4c29[_0x146a49(0x474, 0x479, 0x475, 0x452)] = function (_0x298f65, _0x4da82c) {
      return _0x298f65 === _0x4da82c;
    });
  function _0x146a49(_0x22a46e, _0x25a290, _0x29fadf, _0x3abc0a) {
    return _0x3980(_0x29fadf - 0x245, _0x22a46e);
  }
  (_0x5b4c29[_0x199b27(0x2d1, 0x2f5, 0x2e5, 0x31d)] = _0x199b27(0x347, 0x31c, 0x303, 0x341)),
    (_0x5b4c29[_0x146a49(0x441, 0x427, 0x446, 0x46b)] = function (_0x262a8d, _0x169137) {
      return _0x262a8d === _0x169137;
    }),
    (_0x5b4c29[_0x146a49(0x436, 0x463, 0x452, 0x46b)] = function (_0x442807, _0x2bb589) {
      return _0x442807 === _0x2bb589;
    });
  function _0x199b27(_0x29cd1f, _0x4a2708, _0x284f6e, _0x3d24b7) {
    return _0x3980(_0x4a2708 - 0xfa, _0x284f6e);
  }
  const _0x57f081 = _0x5b4c29;
  let _0x25c0d7 = "";
  for (let _0x5402b1 in _0x5812a9) {
    let _0x3f7ed = _0x5812a9[_0x5402b1];
    if ($[_0x146a49(0x494, 0x45c, 0x46d, 0x472) + _0x146a49(0x413, 0x43b, 0x437, 0x45d)](_0x3f7ed))
      for (let _0x148417 in _0x3f7ed) {
        if (
          _0x57f081["OORWc"](_0x57f081["taKCf"], _0x57f081[_0x146a49(0x451, 0x41f, 0x440, 0x42e)])
        ) {
          let _0xed347a = _0x5402b1 + "[" + _0x148417 + "]=" + _0x3f7ed[_0x148417];
          _0x25c0d7 += _0x57f081[_0x199b27(0x321, 0x2fb, 0x2f5, 0x2fb)](_0x25c0d7, "")
            ? _0xed347a
            : "&" + _0xed347a;
        } else {
          _0x5b4926[_0x146a49(0x474, 0x472, 0x468, 0x491)](
            _0x57f081[_0x199b27(0x32f, 0x304, 0x32a, 0x2db)],
          );
          return;
        }
      }
    else
      _0x25c0d7 += _0x57f081[_0x199b27(0x317, 0x307, 0x2f9, 0x2ea)](_0x25c0d7, "")
        ? _0x5402b1 + "=" + _0x3f7ed
        : "&" + _0x5402b1 + "=" + _0x3f7ed;
  }
  return _0x25c0d7;
}
export default function (param) {
  const eoRQbk = {
      oWmrq: function (callee, param1) {
        return callee(param1);
      },
      VXQZW: function (x, y) {
        return x === y;
      },
    },
    validDomain = [
      "intlgame.c" + "om",
      "syncedtheg" + _0x294a53(0x591, 0x599, 0x57e, 0x568),
      "syncedoffp" + _0x4d7bab(0x2d3, 0x2b5, 0x2e7, 0x2cf),
      _0x294a53(0x539, 0x565, 0x54d, 0x53e) + "t.playerin" + _0x294a53(0x589, 0x574, 0x58a, 0x57b),
      _0x4d7bab(0x2c8, 0x2b4, 0x2df, 0x2c7) + "ayerinfini" + _0x4d7bab(0x281, 0x2bc, 0x282, 0x2a1),
      "www.pubgmo" + _0x294a53(0x592, 0x565, 0x576, 0x56d),
      _0x294a53(0x525, 0x55b, 0x547, 0x55c) + _0x4d7bab(0x27c, 0x2b7, 0x297, 0x293),
      "pre.honoro" + _0x294a53(0x547, 0x545, 0x570, 0x550),
      "www.honoro" + _0x294a53(0x59a, 0x55a, 0x570, 0x55b),
      _0x4d7bab(0x2a9, 0x2b8, 0x2ad, 0x2bb) +
        _0x294a53(0x583, 0x55b, 0x585, 0x5ab) +
        _0x294a53(0x5a5, 0x5a0, 0x57d, 0x564),
      _0x294a53(0x554, 0x55b, 0x569, 0x558) + _0x294a53(0x581, 0x597, 0x579, 0x55f) + "om",
      _0x294a53(0x54f, 0x575, 0x553, 0x57e) + _0x4d7bab(0x2ad, 0x2c1, 0x2d3, 0x2bf),
      _0x294a53(0x569, 0x565, 0x554, 0x541) + _0x294a53(0x536, 0x518, 0x543, 0x53d),
      _0x294a53(0x597, 0x5b3, 0x58d, 0x59c) + "-en.com",
      _0x4d7bab(0x2d0, 0x2fd, 0x2ee, 0x2d5) + _0x4d7bab(0x2b5, 0x2bc, 0x282, 0x2a5),
      _0x4d7bab(0x2fe, 0x2d3, 0x2ab, 0x2d5) + "-kr.com",
      _0x294a53(0x5a9, 0x59f, 0x58d, 0x571) + _0x294a53(0x544, 0x546, 0x54a, 0x550),
      "nikke-en.c" + "om",
      _0x4d7bab(0x2f5, 0x2ae, 0x2da, 0x2c9) + "om",
      _0x294a53(0x598, 0x582, 0x58e, 0x59f) + "om",
      _0x4d7bab(0x2b7, 0x2b7, 0x2b4, 0x2c2) + _0x4d7bab(0x2ae, 0x2cf, 0x2dc, 0x2c5),
      _0x294a53(0x591, 0x564, 0x57c, 0x561) + "ool.tw",
      "nikke-test" + _0x4d7bab(0x2cd, 0x2ab, 0x2de, 0x2ba) + "w",
      "test.drago" + _0x4d7bab(0x29c, 0x27d, 0x285, 0x291),
      _0x294a53(0x533, 0x527, 0x548, 0x571) + "n2.com",
      "test.tower" + "offantasy-" + _0x4d7bab(0x2ab, 0x2cf, 0x2e3, 0x2cc),
      _0x294a53(0x59a, 0x581, 0x580, 0x56b) +
        _0x294a53(0x53f, 0x542, 0x562, 0x566) +
        _0x4d7bab(0x2d2, 0x2c4, 0x2e5, 0x2e1),
      _0x4d7bab(0x26c, 0x287, 0x2be, 0x294) + "al.com",
      _0x4d7bab(0x2ae, 0x2f7, 0x2fe, 0x2d4) + _0x4d7bab(0x2c9, 0x2ae, 0x2bd, 0x2cc),
    ],
    isValid = validDomain[_0x294a53(0x56d, 0x5a0, 0x592, 0x58c)]((domain) =>
      location[_0x294a53(0x570, 0x587, 0x593, 0x58d)][_0x4d7bab(0x2be, 0x2ee, 0x2b0, 0x2d7)](
        domain,
      ),
    );
  if (!isValid) throw new Error(_0x4d7bab(0x2bb, 0x2cc, 0x2a3, 0x2bc) + "er");
  let appid = _0x4d7bab(0x2fe, 0x309, 0x2da, 0x2e0);
  function _0x4d7bab(_0xe71134, _0x4521de, _0x453abf, _0x14a132) {
    return _0x3980(_0x14a132 - 0xa8, _0x4521de);
  }
  let appkey = _0x4d7bab(0x291, 0x29f, 0x2ad, 0x2a7) + _0x4d7bab(0x2d2, 0x2c5, 0x28f, 0x2b9);
  param = param || {};
  if (param["url"] === undefined) {
    console[_0x4d7bab(0x2e2, 0x2ae, 0x2d1, 0x2cb)](
      _0x294a53(0x56e, 0x5b3, 0x595, 0x56d) + _0x4d7bab(0x29f, 0x2c8, 0x2a6, 0x29f),
    );
    return;
  }
  let method = param["method"] || _0x294a53(0x55f, 0x55c, 0x556, 0x53c),
    url = param["url"],
    uuid = createUuid();
  if (method[_0x4d7bab(0x2c0, 0x2bb, 0x274, 0x29d) + "e"]() === "get") {
    if (_0x294a53(0x52a, 0x536, 0x550, 0x53e) === "YANoa")
      _0x20ad95 = eoRQbk["oWmrq"](_0x5519d2, _0xd1a2cd[_0x4d7bab(0x2d8, 0x2c1, 0x2c6, 0x2e2)]);
    else {
      let paramData = "";
      param[_0x294a53(0x59f, 0x587, 0x59a, 0x5c3)] != undefined &&
        !$[_0x294a53(0x532, 0x560, 0x54e, 0x559) + "ect"](
          param[_0x294a53(0x579, 0x586, 0x59a, 0x570)],
        ) &&
        (paramData = objectToParam(param[_0x294a53(0x5ac, 0x5a2, 0x59a, 0x5bb)])),
        (url =
          paramData != ""
            ? url[_0x294a53(0x581, 0x57d, 0x58f, 0x563)]("?")
              ? url + "&" + paramData
              : url + "?" + paramData
            : url),
        (param[_0x4d7bab(0x2fc, 0x2b7, 0x2dc, 0x2e2)] = {});
    }
  }
  let timeStamp =
    typeof window["getCurrent" + "Timestamp"] == _0x4d7bab(0x29c, 0x2c9, 0x2aa, 0x2b6)
      ? getCurrentTimestamp()
      : Math["round"](
          new Date()[_0x4d7bab(0x274, 0x2a6, 0x29c, 0x299)]() /
            (0x250b * -0x1 + 0x19 * -0xdb + 0x3e56),
        )["toString"]();
  url += url["includes"]("?")
    ? _0x4d7bab(0x2d7, 0x2ac, 0x2bf, 0x2c0) +
      uuid +
      _0x4d7bab(0x283, 0x2b9, 0x2c1, 0x2af) +
      appid +
      _0x294a53(0x544, 0x53d, 0x55a, 0x551) +
      timeStamp
    : _0x294a53(0x56a, 0x549, 0x55e, 0x544) +
      uuid +
      _0x294a53(0x54e, 0x55b, 0x567, 0x56b) +
      appid +
      _0x4d7bab(0x2b8, 0x284, 0x28d, 0x2a2) +
      timeStamp;
  let apiUrl = "";
  url[_0x294a53(0x581, 0x5b2, 0x591, 0x582)](_0x294a53(0x590, 0x56f, 0x565, 0x581)) ||
  url[_0x4d7bab(0x2ef, 0x2ea, 0x2bb, 0x2d9)](_0x294a53(0x5a2, 0x583, 0x594, 0x5b7)) ||
  url[_0x294a53(0x599, 0x570, 0x591, 0x598)]("//")
    ? (apiUrl = url[_0x294a53(0x57c, 0x586, 0x55c, 0x573)]("/")
        [_0x4d7bab(0x28d, 0x2a0, 0x2b5, 0x28e)](0xa * 0x397 + -0x10f * 0x1 + -0x8b5 * 0x4)
        [_0x4d7bab(0x2b0, 0x271, 0x270, 0x28d)]("/"))
    : (apiUrl = url[_0x294a53(0x57f, 0x53c, 0x55c, 0x555)]("/")
        ["slice"](0xa8 * 0xd + -0x182 * 0x7 + -0x3 * -0xad)
        [_0x4d7bab(0x296, 0x2b3, 0x2aa, 0x28d)]("/"));
  function _0x294a53(_0x4aa286, _0x40dd28, _0x427e70, _0x85b676) {
    return _0x3980(_0x427e70 - 0x360, _0x40dd28);
  }
  let _tmpUrl = apiUrl["split"]("?")[0x3 * 0x45b + 0x6bb * 0x5 + -0x2eb8];
  if (
    _tmpUrl[_tmpUrl[_0x4d7bab(0x2d3, 0x2d0, 0x2f9, 0x2de)] - (-0x153e + 0x4 * -0x4a9 + 0x27e3)] ===
    "/"
  ) {
    if ("XzNIx" !== _0x4d7bab(0x2aa, 0x2d8, 0x29a, 0x2b7)) {
      let _0x191391 = _0x1777fc + "[" + _0x3f9c09 + "]=" + _0x23475f[_0x2e96c2];
      _0x3fe44d += eoRQbk["VXQZW"](_0x536855, "") ? _0x191391 : "&" + _0x191391;
    } else
      (_tmpUrl = _tmpUrl[_0x4d7bab(0x279, 0x26d, 0x274, 0x28e)](
        0x793 + -0x1c39 + -0x6 * -0x371,
        _tmpUrl[_0x4d7bab(0x301, 0x2e0, 0x306, 0x2de)] -
          (0x7 * 0x2b3 + -0x7 * -0x55 + -0x1 * 0x1537),
      )),
        (apiUrl =
          _tmpUrl +
          "?" +
          apiUrl[_0x294a53(0x550, 0x575, 0x55c, 0x55c)]("?")[0x1faf * 0x1 + 0xe73 + 0xf1 * -0x31]);
  }
  let sign = _0x43a7a9("/" + decodeURI(apiUrl) + "&appkey=" + appkey);
  return (param[_0x4d7bab(0x2eb, 0x2bc, 0x2df, 0x2c3)] = url + "&s=" + sign), param;
}
