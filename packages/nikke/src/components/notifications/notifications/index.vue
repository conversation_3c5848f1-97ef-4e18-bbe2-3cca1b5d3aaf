<template>
  <InfiniteScroll
    :distance="100"
    :back_to_top_visible="false"
    :loading_visible="false"
    :finished_visible="false"
    :loading="loading"
    :empty="empty"
    :finished="finished"
    :debounce_interval="10"
    class="bg-[color:var(--fill-3)]"
    @load-more="load"
  >
    <NotificationsItem v-for="(item, index) in list" :key="index" :item="item"></NotificationsItem>
  </InfiniteScroll>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import { useGetMessages } from "@/api/notifications";
import { useInfiniteList } from "@/composables/use-infinite-list";
import NotificationsItem from "@/components/notifications/notifications-item/index.vue";
import { ReadMessageAllType } from "@/types/user";
import { onActivated } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

const { empty, finished, list, load, loading, reset } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    return useGetMessages.run({
      page_type: 0,
      limit,
      next_page_cursor,
      type: ReadMessageAllType.SITE_MSG,
    });
  },
  item_key: "id",
});

onActivated(() => {
  if (route.meta.needRefresh) {
    reset();
  }
});
</script>

<style lang="scss" scoped></style>
