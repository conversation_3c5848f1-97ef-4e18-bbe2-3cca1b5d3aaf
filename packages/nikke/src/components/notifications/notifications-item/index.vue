<template>
  <div class="w-full h-full bg-[color:var(--fill-3)] px-[15px] pt-[12px] last-of-type:pb-[12px]">
    <div
      class="w-full bg-[var(--op-fill-white)] px-[16px] py-[12px] rounded"
      :class="{
        'cursor-pointer': ext_info?.href || ext_info?.avatar_pendant || ext_info?.comment_bubble_id,
      }"
      @click="onItemClick"
    >
      <!-- <div v-if="item.vid" class="w-full h-[179px] mb-[8px] rounded-[4px] overflow-hidden">
        <YoutubePlayer :info="item.vid"></YoutubePlayer>
      </div> -->
      <CommonImage
        v-if="item.img_url"
        :src="item.img_url"
        class="w-full mb-[8px] rounded-[4px] overflow-hidden"
      ></CommonImage>
      <div class="flex gap-[8px]">
        <div class="flex-1 w-0 flex flex-col">
          <p
            v-safe-html="title"
            class="text-[length:16px] text-[color:var(--text-1)] leading-[21px] font-bold mb-[5px]"
          ></p>
          <span
            v-if="content"
            ref="content_dom"
            v-safe-html="content"
            class="text-[12px] leading-[15px] font-normal text-[color:var(--text-3)] mt-[5px] mb-[8px] html-content"
          ></span>
          <span class="text-[11px] leading-[13px] text-[color:var(--text-3)]">{{
            formatTime(+item.created_on * 1000, t)
          }}</span>
        </div>
        <div v-if="ext_info?.avatar_pendant" class="flex-none">
          <CommonImage
            :src="ext_info?.avatar_pendant"
            class="w-[80px] h-[80px] rounded-[4px] object-fill"
          />
        </div>
      </div>
      <CodeKdy v-if="ext_info?.cdkey" class="mt-[8px]" :cdkey="ext_info?.cdkey"></CodeKdy>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
// import { YoutubePlayer } from "@/components/common/video/player/index";
import { formatTime } from "@/utils/str";
import { computed, ref, watch } from "vue";
import { BaseNotification } from "@/api/notifications";
import { CommonImage } from "@/components/common/image";
import { safeParse } from "packages/utils/tools";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";

import CodeKdy from "@/components/notifications/code-kdy.vue";
import { useWebCredential } from "@/composables/use-webcredential";
import { getPostDeleteSelectList } from "@/components/post/detail/pop/utils";

const props = defineProps<{
  item: BaseNotification;
}>();
const { t } = useI18n();

const title = computed(() => {
  return props.item.brief;
  // 20 为站内信, 直接返回内容
  // if (props.item.type === 20) return props.item.brief || "";
  // return props.item.brief || "";
  // const message = t(`notification_message_type_${props.item.type}`, [
  //   getHtmlText(props.item.content),
  // ]);
  // if (message.includes("notification_message_type_")) return "";
  // return message.replace(/""/g, "").replace(/“”/g, "");
});

const ext_info = computed(() => {
  return safeParse<{
    href: string;
    avatar_pendant?: string;
    avatar_pendant_id?: string;
    days?: number;
    cdkey?: string;
    comment_bubble_id?: string;
    reason_type?: number;
    del_reason?: number;
  }>(props.item.ext_info);
});

const content = computed(() => {
  // 20 为站内信, 直接返回内容
  if (props.item.type === 20) return props.item.content || "";
  // 22 为禁言，包含天数、反馈邮箱
  if (props.item.type === 22) {
    const text = t("notification_message_type_22", [
      ext_info.value?.days ?? 0,
      t(
        `notification_message_type_22_reason_${`${ext_info.value?.reason_type ?? 0}`.padStart(2, "0")}`,
      ),
      "<EMAIL>",
    ]);
    // HACK: 简单通过 replace 处理国际化的复数问题
    if ((ext_info.value?.days ?? 0) <= 1) return text.replace(" days", " day");
    return text;
  }
  // 这几项需要额外插入删除/审核不通过原因
  if ([7, 8, 11, 12, 13, 18, 19].includes(props.item.type)) {
    const del_reason_options = getPostDeleteSelectList();
    const reason = del_reason_options.find((option) => option.type === ext_info.value?.del_reason);
    const message = t(`notification_message_type_${props.item.type}`, [
      getHtmlText(props.item.content),
      reason?.text || t("content_delete_reason_9"),
    ]);
    if (message.includes("notification_message_type_")) return "";
    return message.replace(/""/g, "").replace(/“”/g, "");
  }
  const message = t(`notification_message_type_${props.item.type}`, [
    getHtmlText(props.item.content),
  ]);
  if (message.includes("notification_message_type_")) return "";
  return message.replace(/""/g, "").replace(/“”/g, "");
});

const getHtmlText = (str: string) => {
  const div = document.createElement("div");
  div.innerHTML = str;
  const text = div.innerText.trim();
  if (text === "" && str.includes("<img")) return "【图片】";
  return text;
};

const router = useRouter();
const { openUrlWithAuth } = useWebCredential();

const onItemClick = (e: Event) => {
  if (isInAnchor(e.target as HTMLElement)) return;
  if (ext_info.value?.href) return openUrlWithAuth(ext_info.value?.href);
  if (ext_info.value?.avatar_pendant) return router.push(Routes.USER_EDIT_AVATARFRAME);
  if (ext_info.value?.comment_bubble_id) return router.push(Routes.USER_EDIT_COMMENT_BUBBLE);
};

// 确保正文的 a 链接跳转到新页面
const content_dom = ref(null as HTMLElement | null);
watch(
  () => [content.value, content_dom.value],
  () => {
    if (!content_dom.value) return;
    [...content_dom.value.querySelectorAll("a[href]")].forEach((a) => {
      a.setAttribute("target", "_blank");
    });
  },
  { immediate: true, flush: "post" },
);

const isInAnchor = (target: HTMLElement): boolean => {
  if (target.tagName === "A" && target.getAttribute("href")) return true;
  return target.parentElement ? isInAnchor(target.parentElement) : false;
};
</script>

<style lang="scss" scoped>
.html-content:deep(a[href]) {
  cursor: pointer;
  /* text-decoration: underline; */
  color: var(--brand-1);
}
</style>
