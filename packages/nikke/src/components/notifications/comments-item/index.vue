<template>
  <div class="w-full h-full bg-[color:var(--fill-3)] px-[15px] pt-[12px]">
    <div class="w-full bg-[var(--op-fill-white)] px-[16px] py-[12px]">
      <div class="flex justify-between items-center">
        <div class="flex items-center flex-1 w-0" @click.stop="onUserClick(item)">
          <!-- <img :src="avatar" class="w-[50px] mr-[5px] p-[2px] rounded-full" /> -->
          <div class="w-[48px] h-[48px] p-[2px] mr-[10px]">
            <Avatar
              :src="avatar"
              :auth_type="item.send_user_info?.auth_type"
              :frame="item.send_user_info?.avatar_pendant"
            />
          </div>

          <div class="flex flex-col overflow-hidden flex-1 w-0">
            <p
              class="text-[length:14px] text-[color:var(--text-1)] leading-[18px] font-bold truncate"
            >
              {{ username }}
            </p>
            <span class="text-[length:11px] leading-[13px] text-[color:var(--text-3)]">
              {{ formatTime(+item.created_on * 1000, t) }} {{ tips }}
            </span>
          </div>
        </div>
        <div class="flex items-center flex-none">
          <slot name="icons"></slot>
        </div>
      </div>
      <!-- <div
        v-if="showContent && item.content"
        class="comments_content_box mt-[8px] py-[8px] px-[10px] text-[13px] leading-[14px] text-[var(--text-1)] bg-[color:var(--fill-3)] border border-[corlor:var(--line-1)] text-ellipsis"
        @click.stop="onClickContent(item)"
      >
        <div v-safe-html="item?.content" class="comments_item_content line-clamp-2"></div>
      </div> -->

      <div v-if="type === 'comment' || type === 'like'" class="mt-[8px]">
        <div
          v-if="content"
          v-safe-html="content"
          :class="[item.content_del && '!text-[var(--text-4)]']"
          class="mb-[8px] text-[var(--text-1)] text-[length:12px] leading-[14px] line-clamp-2 break-all content-with-images"
        ></div>
        <div
          v-if="comment_content"
          class="mb-[8px] flex items-center text-[var(--text-3)] text-[length:12px] leading-[14px] overflow-hidden cursor-pointer"
          :class="[item.reply_del && '!text-[var(--text-4)]']"
          @click="toTargetComment"
        >
          <i class="self-stretch w-[3px] bg-[var(--fill-3)] mr-[7px] flex-none"></i>
          <div v-safe-html="comment_content" class="truncate text-nowrap content-with-images"></div>
        </div>

        <div
          v-if="original_data"
          v-safe-html="original_data"
          :class="[item.original_del && '!text-[var(--text-4)]']"
          class="px-[10px] py-[8px] bg-[var(--fill-3)] text-[var(--text-1)] text-[length:12px] leading-[14px] content-with-images cursor-pointer"
          @click="toPostDetail"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { formatTime } from "@/utils/str";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import Avatar from "@/components/common/avatar/index.vue";
import { BaseNotification } from "@/api/notifications";

defineEmits(["click-content"]);
const router = useRouter();
const props = defineProps<{
  item: BaseNotification;
  type: "comment" | "follower" | "like";
}>();
const { t } = useI18n();
const avatar = computed(() => props.item.send_user_info?.avatar || "");
const username = computed(() => props.item.send_user_info?.username || "");
const onUserClick = (item: BaseNotification) => {
  router.push({
    path: Routes.USER,
    query: {
      openid: item.send_user_info.intl_openid,
    },
  });
};

const tips = computed(() => {
  const message = t(`notification_message_type_${props.item.type}`, "");
  if (message.includes("notification_message_type_")) return "";
  return message.replace(/""/g, "").replace(/“”/g, "");
});

const original_data = computed(() => {
  return props.item.original_del ? t("content_deleted") : props.item?.original_data;
});

const comment_content = computed(() => {
  return (props.item.reply_to_reply_uuid ? props.item?.reply_del : props.item?.comment_del)
    ? t("content_deleted")
    : props.item?.comment_content;
});

const content = computed(() => {
  return props.item.content_del ? t("content_deleted") : props.item.content;
});

// const onDetail = (item: any) => {
//   if (!item.post_uuid) return;
//   router.push({
//     path: Routes.POST_DETAIL,
//     query: {
//       post_uuid: item.post_uuid,
//     },
//   });
// };

// const onClickContent = (item: any) => {
//   if (props.isComment) {
//     if (item.reply_uuid) {
//       router.push({ path: Routes.POST_COMMENTS, query: { comment_uuid: item.reply_uuid } });
//     } else if (item.post_uuid) {
//       router.push({ path: Routes.POST_DETAIL, query: { post_uuid: item.post_uuid } });
//     }
//     return;
//   }
//   onDetail(item);
// };

const toTargetComment = () => {
  if (props.item.comment_del || !props.item.reply_uuid || props.item.original_del) return;
  router.push({
    path: Routes.POST_COMMENTS,
    query: {
      comment_uuid: props.item.reply_uuid,
      post_uuid: props.item.post_uuid,
    },
  });
};

const toPostDetail = () => {
  if (props.item.original_del || !props.item.post_uuid) return;
  router.push({
    path: Routes.POST_DETAIL,
    query: {
      post_uuid: props.item.post_uuid,
    },
  });
};
</script>

<style>
.comments_item_content img {
  display: inline-block;
}

.comments_content_box {
  position: relative;
}

.comments_content_box::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px; /* 覆盖底部内边距的高度 */
  background: inherit; /* 继承父元素背景色 */
}
</style>
