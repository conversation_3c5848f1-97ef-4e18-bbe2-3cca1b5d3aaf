<template>
  <div class="w-full flex items-center h-[28px] bg-[var(--fill-3-80)] cursor-default" @click.stop>
    <div
      class="w-[28px] h-[28px] flex items-center justify-center bg-[var(--fill-3)] border-r-[1px] border-r-[var(--line-1)] flex-shrink-0"
    >
      <SvgIcon name="icon-link" color="var(--text-3-60)" class="w-[15px] h-[15px]"></SvgIcon>
    </div>
    <div class="flex-1 h-full mx-[6px] relative flex items-center z-[1] overflow-hidden">
      <div :class="[`font-sans text-[length:15px] truncate text-[color:var(--text-1)]`]">
        {{ renderSecret(props.cdkey) }}
      </div>
      <div
        class="w-[20px] h-[20px] flex items-center justify-center mx-[6px] cursor-pointer flex-shrink-0"
        @click="show = !show"
      >
        <SvgIcon :name="show ? `icon-eye` : `icon-eye-open`" color="var(--text-3-60)"></SvgIcon>
      </div>
    </div>
    <div class="w-[1px] h-[15px] bg-[var(--text-4)]"></div>
    <div
      class="w-[20px] h-[20px] flex items-center justify-center mx-[6px] flex-shrink-0"
      :class="{ 'cursor-pointer': show }"
      @click="show && onCopy()"
    >
      <SvgIcon
        name="icon-copy"
        :color="show ? `var(--text-3-60)` : `var(--text-3-20)`"
        class="w-[15px] h-[15px]"
      ></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { useToast } from "@/components/ui/toast";
import { t } from "@/locales";
import { ref } from "vue";

const show = ref(false);
const { show: toast } = useToast();

const props = withDefaults(
  defineProps<{
    cdkey: string;
  }>(),
  {},
);

const onCopy = async () => {
  await navigator.clipboard.writeText(props.cdkey);
  toast({ text: t("copy_link_success"), type: "success" });
};

const renderSecret = (text: string) => {
  return !show.value ? `${text[0]}**************${text[text.length - 1]}` : text;
};
</script>
