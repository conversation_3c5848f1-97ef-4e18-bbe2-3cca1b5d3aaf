<template>
  <InfiniteScroll
    :distance="100"
    :back_to_top_visible="false"
    :loading_visible="false"
    :finished_visible="false"
    :loading="loading"
    :empty="empty"
    :finished="finished"
    :debounce_interval="10"
    @load-more="load"
  >
    <CommentsItem v-for="(item, index) in list" :key="index" :item="item" type="comment">
      <template #icons>
        <!-- <ButtonIconText
          class="ml-[12px]"
          icon="icon-line-comments"
          @click.stop="$emit('manage', 'reply', item)"
        /> -->
        <ButtonIconText
          v-if="!item.reply_del"
          class="ml-[12px]"
          icon="icon-line-like"
          has-toggle
          active-icon="icon-line-like-cur"
          :is-active="item.is_star"
          @click.stop="$emit('manage', 'like', item)"
        />
        <!-- <ButtonIconText
          class="w-[16px] h-[16px] ml-[12px]"
          icon="icon-no-follower"
          has-toggle
          active-icon="icon-followed"
          :is-active="item.is_follow"
          @click.stop="$emit('manage', item)"
        /> -->
      </template>
    </CommentsItem>
  </InfiniteScroll>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import { BaseNotification, useGetMessages } from "@/api/notifications";
import { useInfiniteList } from "@/composables/use-infinite-list";
import CommentsItem from "@/components/notifications/comments-item/index.vue";
import ButtonIconText from "@/components/common/button-icon-text/index.vue";
import { ReadMessageAllType } from "@/types/user";
import { onActivated } from "vue";
import { useRoute } from "vue-router";
// import { useRouter } from "vue-router";
// import { Routes } from "@/router/routes";

// const router = useRouter();

defineProps<{
  data?: BaseNotification;
}>();

const route = useRoute();

defineEmits<{
  manage: ["like" | "reply" | "follow", BaseNotification];
  showReply: [BaseNotification];
  showDialog: [BaseNotification];
}>();
const { empty, finished, list, load, loading, reset } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    return useGetMessages.run({
      page_type: 0,
      limit,
      next_page_cursor,
      type: ReadMessageAllType.COMMENT,
    });
  },
  item_key: "id",
});

onActivated(() => {
  if (route.meta.needRefresh) {
    reset();
  }
});
</script>

<style lang="scss" scoped></style>
