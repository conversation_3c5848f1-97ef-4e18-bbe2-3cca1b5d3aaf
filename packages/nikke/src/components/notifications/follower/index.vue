<template>
  <InfiniteScroll
    :distance="100"
    :back_to_top_visible="false"
    :loading_visible="false"
    :finished_visible="false"
    :loading="loading"
    :empty="empty"
    :finished="finished"
    :debounce_interval="10"
    @load-more="load"
  >
    <CommentsItem v-for="(item, index) in list" :key="index" :item="item" type="follower">
      <template #icons>
        <!-- <ButtonIconText
          class="w-[16px] h-[16px]"
          icon="icon-no-follower"
          has-toggle
          active-icon="icon-followed"
          :is-active="item.isFollower"
          @click.stop="$emit('manage', 'follow', item)"
        /> -->
      </template>
    </CommentsItem>
  </InfiniteScroll>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import { useGetMessages } from "@/api/notifications";
import { useInfiniteList } from "@/composables/use-infinite-list";
import CommentsItem from "@/components/notifications/comments-item/index.vue";
// import ButtonIconText from "@/components/common/button-icon-text/index.vue";
// import { useRouter } from "vue-router";
import { ReadMessageAllType } from "@/types/user";
import { onActivated } from "vue";
import { useRoute } from "vue-router";
// const router = useRouter();
const route = useRoute();

defineEmits(["manage"]);
const { empty, finished, list, load, loading, reset } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    return useGetMessages.run({
      page_type: 0,
      limit,
      next_page_cursor,
      type: ReadMessageAllType.FOLLOW,
    });
  },
  item_key: "id",
});

onActivated(() => {
  if (route.meta.needRefresh) {
    reset();
  }
});
</script>

<style lang="scss" scoped></style>
