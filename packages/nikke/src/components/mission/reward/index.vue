<template>
  <div
    v-show="mission_store.reward_task_list.length > 0"
    class="pt-[8px] pb-[11px] px-[12px] bg-[var(--op-fill-white)] relative overflow-hidden"
  >
    <div class="flex items-center">
      <div
        class="text-[length:16px] leading-[19px] text-[color:var(--text-1)] font-[Inter] font-bold mr-[8px]"
      >
        {{ t("reward") }}
      </div>
      <div class="ml-[6px] mr-[4px] flex-shrink-0">
        <CommonImage :src="gold" class="w-[24px] h-[24px]"></CommonImage>
      </div>
      <div
        class="font-[DINNextLTProBold] mt-[2px] font-bold text-[color:var(--text-1)] text-[length:16px] leading-[1]"
      >
        {{ user_store.total_point }}
      </div>
    </div>
    <div
      class="absolute top-0 right-0 min-w-[120px] max-w-[160px] h-[26px] z-[2] flex items-center px-[8px] cursor-pointer pt-[2px]"
      @click="onRedeemRewards"
    >
      <i
        class="bg-gradient-to-r border-t-[3px] border-[color:var(--fill-3)] from-[#FFB621] to-[#FF5D1C] absolute top-0 left-0 w-[calc(100%_+_20px)] h-full -z-[1] skew-x-[26deg]"
      ></i>
      <SvgIcon
        name="icon-gift"
        color="var(--color-white)"
        class="w-[16px] g-[16px] mr-[7px]"
      ></SvgIcon>
      <div
        class="text-[length:11px] font-medium leading-[14px] text-[color:var(--color-white)] truncate font-[Inter]"
      >
        {{ t("redeem_rewards") }}
      </div>
      <div class="w-[12px] h-[12px] ml-[3px] flex-shrink-0">
        <SvgIcon name="icon-arrow-right" color="var(--color-white)"></SvgIcon>
      </div>
    </div>
    <div
      v-if="!user_store.user_had_bound_lip"
      class="bg-red-300/20 p-4 text-center flex items-center gap-[4px] mt-[4px] cursor-pointer"
      @click="toBindLipAccount"
    >
      <img src="@/assets/imgs/mission/info.png" class="w-[16px] h-[16px] inline-block flex-none" />
      <div class="font-[Inter] text-[11px] text-gray-600/90 text-left">
        {{ t("need_bind_lip_first") }}
      </div>
      <div class="w-[12px] h-[12px] flex-shrink-0 self-center">
        <img src="@/assets/imgs/mission/arrow-left.png" class="w-[12px] h-[12px]" />
      </div>
    </div>
    <div class="mt-[10px]">
      <TaskItem
        v-for="(item, index) in mission_store.reward_task_list"
        :key="index"
        :data="item"
      ></TaskItem>
    </div>
    <RefreshTag
      class="text-[color:var(--text-3)] text-[length:10px] font-[400] mt-[10px]"
    ></RefreshTag>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { CommonImage } from "@/components/common/image/index";
import TaskItem from "@/components/mission/task-item/index.vue";

import gold from "@/assets/imgs/common/icon-gold.png";
import { t } from "@/locales";
import { useUser } from "@/store/user";
import { computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { useMissionStore } from "@/store/mission";
import { useCheck } from "@/components/points/use-check-user";
import { sleep } from "packages/utils/tools";
import RefreshTag from "@/components/points/home/<USER>/index.vue";

const router = useRouter();

const user_store = useUser();

onMounted(() => {
  if (user_store.is_login) {
    user_store.refreshPoints();
  }
});

const onRedeemRewards = () => {
  router.push(Routes.POINTS);
};

const mission_store = useMissionStore();

const { bindLipAccount } = useCheck();
const toBindLipAccount = async () => {
  await bindLipAccount();
  await sleep(500);
  window.location.reload();
};

// 此时再发起请求：1. 确认未登录； 2. 确认是已登录而且查询到 LIP 否绑定状态（是否绑定都行）
const canFetchRewardTasks = computed(() => {
  if (user_store.loading) return false;
  if (user_store.user_had_bind_lip_loading) return false;
  if (user_store.is_login && user_store.user_had_bind_lip_loading) return false;
  return true;
});
watch(canFetchRewardTasks, (v) => v && mission_store.refetchRewardTask(), { immediate: true });
</script>
