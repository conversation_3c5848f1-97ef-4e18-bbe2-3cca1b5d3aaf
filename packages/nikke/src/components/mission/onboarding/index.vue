<template>
  <div
    class="relative shadow-[0_1px_5px_var(--op-shadow-black-5)] bg-[color:var(--op-fill-white)] z-[1] px-[12px] pt-[16px] pb-[12px] overflow-hidden"
  >
    <CommonImage
      :src="bg"
      class="w-full full object-cover absolute top-0 left-0 -z-[1]"
    ></CommonImage>

    <div
      class="text-[length:14px] leading-[17px] text-[color:var(--text-1)] font-bold px-[4px] font-[Inter]"
    >
      {{ t("onboarding_mission") }}
    </div>
    <div
      v-if="!all_collected"
      class="absolute top-0 right-0 min-w-[120px] max-w-[160px] h-[26px] z-[2] flex items-center px-[6px]"
      :class="[need_bind_role ? `cursor-pointer` : ``]"
      @click="!is_login ? toLogin() : need_bind_role ? showBindRole() : null"
    >
      <i
        class="bg-gradient-to-r from-[#FFB621] to-[#FF5D1C] absolute top-0 left-0 w-[calc(100%_+_20px)] h-full -z-[1] skew-x-[26deg]"
      ></i>
      <div class="w-[20px] h-[20px] mr-[7px] flex-shrink-0">
        <RoleAvatar
          :avatar_id="mission_store.player_info?.icon"
          :default_avatar="
            mission_store.player_info?.has_saved_role_info ? default_avatar : not_bind_avatar
          "
        ></RoleAvatar>
      </div>
      <div v-if="!is_login" class="flex items-center">
        <div
          class="text-[length:11px] font-medium leading-[14px] text-[color:var(--color-white)] truncate font-[Inter]"
        >
          {{ t("login") }}
        </div>
        <div class="w-[12px] h-[12px] ml-[3px] flex-shrink-0">
          <SvgIcon name="icon-arrow-right" color="var(--color-white)"></SvgIcon>
        </div>
      </div>
      <div v-else-if="need_bind_role" class="flex items-center">
        <div
          class="text-[length:11px] font-medium leading-[14px] text-[color:var(--color-white)] truncate font-[Inter]"
        >
          {{ t("link_channel") }}
        </div>
        <div class="w-[12px] h-[12px] ml-[3px] flex-shrink-0">
          <SvgIcon name="icon-arrow-right" color="var(--color-white)"></SvgIcon>
        </div>
      </div>
      <div
        v-else
        class="text-[length:11px] font-medium leading-[14px] text-[color:var(--color-white)] truncate font-[Inter]"
      >
        {{ mission_store.player_info?.role_name }}
      </div>
    </div>

    <div v-else class="flex items-center justify-end z-[5] absolute top-[14px] right-[16px]">
      <CommonImage :src="gift_img" class="w-[21px] h-[21px] flex-shrink-0 mr-[2px]"></CommonImage>
      <div class="text-[length:11px] leading-[13px] text-[color:var(--brand-1)] font-[Inter]">
        5/5
      </div>
    </div>

    <div v-show="expand" :class="`${expand ? `h-auto overflow-visible` : `h-0 overflow-hidden`}`">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="mt-[10px] flex items-center justify-between pl-[8px] pr-[6px] h-[36px] border-b-[1px] border-[var(--line-1)] last-of-type:border-none"
      >
        <CommonImage
          :src="item?.icon"
          class="w-[21px] h-[21px] flex-shrink-0 mr-[6px]"
        ></CommonImage>
        <div
          class="flex-1 gap-[5px] text-[length:11px] leading-[16px] text-[color:var(--text-1)] flex items-center"
        >
          <span>{{ item?.text }}</span>
        </div>
        <div class="flex items-center justify-end ml-[6px]">
          <CommonImage :src="gift_img" class="w-[23px] h-[23px] flex-shrink-0"></CommonImage>
          <div
            class="min-w-[10px] font-[Inter] text-[length:11px] leading-[13px] text-[color:var(--text-3)] px-[2px]"
          >
            x1
          </div>
          <div
            class="flex items-center justify-start ml-[8px] cursor-pointer min-w-[60px]"
            @click="itemClick(item)"
          >
            <div
              :class="[
                `min-w-[33px] text-left text-[length:11px] leading-[14px] font-[Inter]`,
                item?.mission_status == MissionStatus.COLLECTED
                  ? `text-[color:var(--text-3)] opacity-70`
                  : `text-[color:var(--brand-1)]`,
              ]"
            >
              {{
                item?.mission_status == MissionStatus.UNFINISHED
                  ? t("to_complete")
                  : item?.mission_status == MissionStatus.COLLECTED
                    ? t("already_received")
                    : t("receive")
              }}
            </div>
            <div
              v-show="item?.mission_status == MissionStatus.UNFINISHED"
              class="w-[12px] h-[12px] ml-[4px]"
            >
              <SvgIcon name="icon-arrow-right2" color="var(--brand-1)"></SvgIcon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-show="all_collected && !expand"
      class="mt-[10px] font-[Inter] text-center text-[length:13px] leading-[16px] text-[color:var(--text-2)]"
    >
      {{ t("all_mission_finished_tips") }}
    </div>

    <div
      v-show="all_collected"
      :class="[
        `w-[24px] h-[24px] flex items-center justify-center mx-auto -mb-[10px] cursor-pointer transition-all duration-200`,
        expand ? `-rotate-90` : `rotate-90`,
      ]"
      @click="expand = !expand"
    >
      <SvgIcon name="icon-arrow-right" color="var(--text-1)"></SvgIcon>
    </div>
  </div>
  <!--  cancel_text="Cancel"  @cancel="show = !show" -->
  <Dialog
    :show="show"
    :title="t('success')"
    :content="t('item_received_tips')"
    :confirm_text="t('confirm')"
    @confirm="show = !show"
  ></Dialog>
  <IsHomeScreen :show="show_add_to_home_screen" @close="onCloseAddToDesktop"></IsHomeScreen>
  <Follow :show="show_follow_panel" @close="show_follow_panel = !show_follow_panel"></Follow>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { CommonImage } from "@/components/common/image/index";
// import Avatar from "@/components/common/avatar/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Dialog from "@/components/ui/dialog/index.vue";
import IsHomeScreen from "@/components/home/<USER>/index.vue";
import Follow from "@/components/mission/follow/index.vue";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";

import bg from "@/assets/imgs/mission/bg-card.png";
import icon1 from "@/assets/imgs/mission/icon1.png";
import icon2 from "@/assets/imgs/mission/icon2.png";
import icon3 from "@/assets/imgs/mission/icon3.png";
import icon4 from "@/assets/imgs/mission/icon4.png";
import icon5 from "@/assets/imgs/mission/icon5.png";
import not_bind_avatar from "@/assets/imgs/common/avatar-default.png";
import default_avatar from "@/shiftyspad/assets/images/appicon.png";

import gift_img from "@/assets/imgs/test/gift.png";
import { useMissionStore } from "@/store/mission";
import { MissionTagId, MissionStatus } from "@/api/mission";
import { t } from "@/locales";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { useUser } from "@/store/user";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { report } from "packages/utils/tlog";
import install from "@/boot/pwa-install";

const router = useRouter();

const show = ref(false);
const show_add_to_home_screen = ref(false);

const show_follow_panel = ref(false);

const user_store = useUser();
const is_login = computed(() => user_store.is_login);

// 展开
const expand = ref(true);

const mission_store = useMissionStore();
const { makeSureBindRole } = useBindRole();

const mission_icon_map = {
  [MissionTagId.LOGIN]: icon1,
  [MissionTagId.BIND_LIPASS]: icon2,
  [MissionTagId.SHIFTYPAD_BIND_GAME]: icon3,
  [MissionTagId.ADD_TO_DESKTOP]: icon4,
  [MissionTagId.FOLLOW_NIKKE]: icon5,
};

const list = computed(() => {
  const mission_desc_map = {
    [MissionTagId.LOGIN]: t("task_signup_and_signin"),
    [MissionTagId.BIND_LIPASS]: t("task_bind_lip"),
    [MissionTagId.SHIFTYPAD_BIND_GAME]: t("task_visite_shiftypad"),
    [MissionTagId.ADD_TO_DESKTOP]: t("task_add_to_desktop"),
    [MissionTagId.FOLLOW_NIKKE]: t("task_follow_nikke"),
  };
  const items = ["1", "2", "3", "4", "5"] as MissionTagId[];
  return items.map((item) => {
    const status = mission_store.mission_list.find((i) => i.tag_id === item)?.mission_status;
    return {
      tag_id: item,
      mission_status: status ?? MissionStatus.UNFINISHED,
      icon: mission_icon_map[item],
      text: mission_desc_map[item],
    };
  });
});

// 所有任务都领取了
const all_collected = computed(() => {
  return (
    list.value.length > 0 &&
    list.value.every((item) => item.mission_status === MissionStatus.COLLECTED)
  );
});

watch(
  all_collected,
  (val) => {
    if (val) expand.value = false;
  },
  { immediate: true },
);

const showBindRole = async () => {
  await makeSureBindRole();
  mission_store.refetchPlayerInfo();
};

const itemClick = async (item: (typeof list.value)[number]) => {
  if (!is_login.value) {
    return toLogin();
  }

  report.standalonesite_onboarding_mission_btn.cm_click({
    tag_id: item.tag_id,
    tag_status: item.mission_status,
  });

  if (item.mission_status === MissionStatus.COLLECTED) return;

  if (item.mission_status === MissionStatus.FINISHED) {
    if (mission_store.player_loading) return;
    // 需要绑定角色
    if (need_bind_role.value) {
      return await showBindRole();
    }
    await mission_store.receiveMissionGift(item.tag_id);
    show.value = !show.value;
    return;
  }

  if (item.tag_id === MissionTagId.LOGIN) {
    return toLogin();
  }

  if (item.tag_id === MissionTagId.BIND_LIPASS) {
    return router.push(Routes.SETTING_ACCOUNT);
  }

  if (item.tag_id === MissionTagId.SHIFTYPAD_BIND_GAME) {
    return router.push(Routes.SHIFTYSPAD);
  }

  if (item.tag_id === MissionTagId.ADD_TO_DESKTOP) {
    if (install.promptable) {
      try {
        install.prompt();
      } catch (error) {
        console.error(error);
      }
    } else {
      show_add_to_home_screen.value = !show_add_to_home_screen.value;
    }
    if (item.mission_status === MissionStatus.UNFINISHED) {
      await mission_store.finishMission(item.tag_id);
    }
    return;
  }

  if (item.tag_id === MissionTagId.FOLLOW_NIKKE) {
    show_follow_panel.value = !show_follow_panel.value;
    return;
  }
};

const onCloseAddToDesktop = async () => {
  show_add_to_home_screen.value = !show_add_to_home_screen.value;
};

const need_bind_role = computed(() => {
  return !mission_store.player_loading && !mission_store.player_info?.has_saved_role_info;
});

onMounted(() => {
  if (is_login.value && !mission_store.mission_status_loading) {
    mission_store.refetchMissionStatus();
  }
  if (is_login.value && !mission_store.player_loading) {
    mission_store.refetchPlayerInfo();
  }
});

const toLogin = () => {
  router.push(Routes.LOGIN);
};
</script>
