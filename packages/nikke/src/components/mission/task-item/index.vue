<template>
  <div
    class="flex items-center justify-between mb-[8px] bg-[var(--op-fill-white)] overflow-hidden relative z-[1]"
  >
    <i
      class="absolute top-0 left-0 w-full h-full border-[1px] border-[color:var(--line-1)] -z-[1]"
    ></i>
    <div class="flex-1 mr-[10px] py-[9x] pl-[10px]">
      <div class="text-[length:13px] text-[color:var(--text-1)] font-[Inter] leading-[16px]">
        {{ info.task_name }}
      </div>
      <div class="flex items-center">
        <CommonImage :src="gold" class="w-[16px] h-[16px] flex-shrink-0 mr-[4px]"></CommonImage>
        <span class="text-[length:12px] mt-[4px] text-[color:var(--text-1)] leading-[1]">
          +{{ info.points }}
        </span>
      </div>
    </div>
    <div
      class="w-[87px] cursor-pointer text-center h-[50px] relative z-[2] flex items-center justify-end"
      @click="onClickItem"
    >
      <SvgIcon
        name="icon-gift-mask"
        color="var(--fill-1-20)"
        class="w-[61px] h-full absolute top-0 -left-[30px] -z-[1] icon-gift-mask"
      ></SvgIcon>
      <i
        v-if="!info.is_completed"
        class="absolute top-0 -right-[15px] w-full h-full -z-[1] -skew-x-[30deg] bg-[var(--brand-1)]"
      ></i>
      <i
        v-else
        class="absolute top-0 -right-[15px] w-full h-full -z-[1] -skew-x-[30deg] bg-[var(--fill-1-60)]"
        :class="is_extra_task ? `bg-gray-300` : ``"
      ></i>
      <div
        v-if="!show_progress"
        :class="[
          `w-[24px] h-[24px] bg-[length:100%_100%] mr-[19px]`,
          info.is_completed
            ? `bg-[url('@/assets/imgs/common/icon-gift-true.png')]`
            : `bg-[url('@/assets/imgs/common/icon-gift.png')]`,
        ]"
      >
        <!-- <SvgIcon
          v-show="!info.is_completed"
          name="icon-arrow-right3"
          color="var(--color-white)"
        ></SvgIcon> -->
      </div>
      <div v-else class="flex flex-col w-[43px] mr-[12px]">
        <div
          class="font-[Inter] text-[length:13px] text-[color:var(--color-white)] leading-[16px] font-medium"
          :class="info.is_completed ? `!text-[color:var(--text-3)] opacity-60` : ``"
        >
          {{ info.completed_times }} / {{ info.need_completed_times }}
        </div>
        <div class="w-full h-[4px] bg-[color:var(--op-fill-white-40)] mt-[5px] relative">
          <div
            class="absolute top-0 left-0 h-full bg-[color:var(--op-fill-white)]"
            :style="`width:${(info.completed_times / info.need_completed_times) * 100}%`"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { CommonImage } from "@/components/common/image/index";

import gold from "@/assets/imgs/common/icon-gold.png";
import { TaskV2 } from "@/api/mission";
import { computed } from "vue";
import { useTask } from "@/components/points/home/<USER>/composition";
import { useUser } from "@/store/user";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { useCheck } from "@/components/points/use-check-user";
import { report } from "packages/utils/tlog";

const user_store = useUser();

const props = defineProps<{
  data: TaskV2;
}>();

const router = useRouter();

const is_extra_task = computed(() => props.data.task_type > 12); // 前 12个是配置的固定任务，一般来说，新增的任务才需要展示进度

const show_progress = computed(() => {
  return info.value.need_completed_times > 1 || is_extra_task.value;
});

const info = computed(() => ({
  ...props.data,
  ...props.data.reward_infos?.[0],
}));

const { check, go } = useCheck();
const { checkTask } = useTask();
const onClickItem = async () => {
  if (!user_store.is_login) {
    return router.push(Routes.LOGIN);
  }
  if (is_extra_task.value) return;

  report.standalonesite_reward_task_btn.cm_click({ task_id: info.value.task_id });

  if (check()) {
    go();
    return;
  }
  checkTask(info.value);
};
</script>
