<template>
  <CommBottomPopup :show="show" @close="emits('close')">
    <div class="py-[20px]">
      <div class="max-h-[50vh] overflow-y-auto overscroll-x-hidden mx-[10px] px-[10px]">
        <div
          v-for="(item, index) in data?.users"
          :key="index"
          class="flex items-center py-[10px] border-b-[1px] border-[var(--line-1)] first-of-type:pt-0"
        >
          <div class="w-[40px] h-[40px] flex-shrink-0 mr-[10px]">
            <Avatar
              :src="item.user_info.avatar"
              :auth_type="item.user_info.auth_type"
              :frame="item.user_info.avatar_pendant"
            ></Avatar>
          </div>
          <div class="flex-1 overflow-hidden">
            <div class="text-[length:13px] leading-[24px] text-[color:var(--text-1)] truncate">
              {{ item.user_info.username }}
            </div>
            <div class="text-[length:10px] leading-[12px] text-[color:var(--text-3)]">
              {{ item.user_info.auth_desc }}
            </div>
          </div>
          <div class="w-[64px] h-[25px] flex items-center justify-center ml-[20px]">
            <Btns
              v-if="item.is_mutual_follow"
              icon="iocn-follower-cur"
              iconcolor="var(--text-1)"
            ></Btns>
            <Btns v-else-if="item.is_follow" icon="icon-followed" iconcolor="var(--text-1)"></Btns>
            <!-- <Btns v-else type="primary" text="Follow" @click="item.follow = !item.follow"> </Btns> -->
          </div>
        </div>
      </div>
      <div class="mt-[20px] flex flex-col justify-center items-center">
        <Button
          type="primary"
          :disabled="is_all_followed"
          class="w-[227px] mb-[6px]"
          @click="followClick"
        >
          {{ t("follow_all") }}
        </Button>
        <Button class="w-[227px]" @click="emits('close')">{{ t("cancel") }}</Button>
      </div>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Button from "@/components/ui/button/index.vue";
import Avatar from "@/components/common/avatar/index.vue";
import Btns from "@/components/common/btns/index.vue";
import { t } from "@/locales";
import { computed, watch } from "vue";
import {
  MissionStatus,
  MissionTagId,
  useGetFollowTaskOfficialAccounts,
  useQuicklyFollowAllOfficialAccounts,
} from "@/api/mission";
import { useMissionStore } from "@/store/mission";

const props = defineProps<{
  show: boolean;
}>();

const mission_store = useMissionStore();

const emits = defineEmits(["close"]);

const { data, refetch, isLoading } = useGetFollowTaskOfficialAccounts({});

watch(
  () => props.show,
  async () => {
    if (!props.show) return;
    await refetch();
    if (
      is_all_followed.value &&
      mission_store.mission_list.find((i) => i.tag_id === MissionTagId.FOLLOW_NIKKE)
        ?.mission_status === MissionStatus.UNFINISHED
    ) {
      await mission_store.finishMission(MissionTagId.FOLLOW_NIKKE);
    }
  },
);

const is_all_followed = computed(() => {
  return !!data.value?.users?.length && data.value?.users.every((item) => item.is_follow);
});

const follow = useQuicklyFollowAllOfficialAccounts();
const followClick = async () => {
  if (isLoading.value) return;
  if (is_all_followed.value) return;
  await follow.mutateAsync({});
  await refetch();
  await mission_store.refetchMissionStatus();
  emits("close");
};
</script>
