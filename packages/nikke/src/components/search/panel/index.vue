<template>
  <div class="px-[12px]">
    <div class="flex justify-between items-center h-[20px]">
      <span class="font-bold text-[14px] leading-[16px] text-[color:var(--text-1)]">{{
        title
      }}</span>
      <span v-if="$slots.icon" class="w-[20px] h-[20px] cursor-pointer">
        <slot name="icon"></slot>
      </span>
    </div>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string;
}>();
</script>

<style lang="scss" scoped></style>
