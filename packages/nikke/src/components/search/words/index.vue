<template>
  <ul class="flex flex-wrap gap-x-[8px] gap-y-[10px]">
    <li
      v-for="item in list"
      :key="item"
      class="font-normal text-[11px] leading-[12px] text-[color:var(--text-2)] p-[8px] box-border bg-[var(--op-fill-white)] rounded-[2px] cursor-pointer max-w-full whitespace-nowrap overflow-hidden text-ellipsis"
      @click="handleClick(item)"
    >
      {{ item }}
    </li>
  </ul>
</template>

<script setup lang="ts">
defineProps<{
  list?: string[];
}>();

const emit = defineEmits(["itemClick"]);

const handleClick = (item: string) => {
  emit("itemClick", item);
};
</script>

<style lang="scss" scoped></style>
