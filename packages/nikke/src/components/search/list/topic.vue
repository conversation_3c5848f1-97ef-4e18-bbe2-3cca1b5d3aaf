<template>
  <div
    class="flex items-center min-h-[60px] bg-[var(--op-fill-white)] py-[8px] pl-[14px] pr-[12px] box-border"
    @click="goToTopic"
  >
    <div class="flex flex-col items-start flex-1 cursor-pointer overflow-hidden">
      <span
        v-safe-html="data.tag_name"
        class="text-[length:14px] leading-[17px] font-bold text-[color:var(--text-1)] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
      </span>
      <span
        class="text-[length:12px] leading-[13px] font-normal text-[color:var(--text-2)] mt-[1px] max-w-full"
      >
        {{ formatNum(Number(data.post_num)) }} {{ t("posts") }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tag } from "packages/types/home";
import { formatNum } from "@/utils/str";
import { Routes } from "@/router/routes";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps<{
  data: Tag;
}>();

const router = useRouter();

const goToTopic = () => {
  router.push({
    path: Routes.TOPIC,
    query: { tag_id: props.data.id },
  });
};
</script>

<style lang="scss" scoped></style>
