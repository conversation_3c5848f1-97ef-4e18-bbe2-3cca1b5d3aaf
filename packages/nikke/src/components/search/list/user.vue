<template>
  <div
    class="flex items-center min-h-[60px] bg-[var(--op-fill-white)] py-[8px] pl-[14px] pr-[12px] box-border"
  >
    <div
      v-if="data.avatar"
      class="w-[44px] h-[44px] border-solid flex-shrink-0 mr-[12px]"
      @click="openUserCenter"
    >
      <Avatar
        :src="data.avatar"
        :auth_type="data?.auth_type"
        :frame="data?.avatar_pendant"
        class="object-cover w-full"
      />
    </div>
    <div
      class="flex flex-col items-start flex-1 cursor-pointer overflow-hidden"
      @click="openUserCenter"
    >
      <span
        class="text-[length:14px] leading-[17px] font-bold text-[color:var(--text-1)] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
        v-html="data.username"
      >
      </span>
      <span
        v-if="data?.auth_desc"
        class="text-[length:12px] font-normal text-[color:var(--brand-1)] mt-[1px] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ data?.auth_desc }}
      </span>
      <span
        v-if="data?.remark"
        class="text-[length:11px] leading-[13px] font-normal text-[color:var(--text-2)] mt-[1px] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ data?.remark }}
      </span>
      <span
        class="text-[length:11px] leading-[13px] font-normal text-[color:var(--text-3)] mt-[1px] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ formatNum(Number(data.fans_num)) }} {{ t("follower") }}
      </span>
    </div>
    <template v-if="!is_me">
      <div class="ml-[10px] flex-none">
        <Btns v-if="data.is_black" :text="t('blocking')" type="disabled"></Btns>
        <Btns
          v-else
          v-click-interceptor.need_login.mute.sign_privacy.stop="followClick"
          :icon="followStatus.icon"
          :text="followText"
          :iconcolor="followStatus?.iconcolor"
          :type="followText ? `primary` : 'default'"
        >
          <template #icon>
            <span class="text-[18px] mr-[2px]">+ </span>
          </template>
        </Btns>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Btns from "@/components/common/btns/index.vue";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { computed } from "vue";
import { useUser } from "@/store/user";
import Avatar from "@/components/common/avatar/index.vue";
import { UserInfo } from "packages/types/user";
import { formatNum } from "@/utils/str";
import { PopCallbackValue } from "packages/types/common";
import { useDialog } from "@/components/ui/dialog/index";

const { show: showDialog } = useDialog();
const { t } = useI18n();

const props = defineProps<{
  data: UserInfo;
}>();

const emit = defineEmits(["follow"]);

const router = useRouter();
const user = useUser();

const openUserCenter = () => {
  router.push({
    name: RoutesName.USER,
    query: { openid: props.data.intl_openid },
  });
};

const is_me = computed(() => user.user_info?.intl_openid === props.data.intl_openid);

const followStatus = computed(() => {
  if (is_me.value) return { status: 1, icon: "icon-ellipsis", iconcolor: "var(--text-3)" };
  if (props.data.is_mutual_follow)
    return { status: 4, icon: "iocn-follower-cur", iconcolor: "var(--brand-1)" };
  if (props.data.is_followed)
    return { status: 3, icon: "icon-followed", iconcolor: "var(--brand-1)" };
  return { status: 2, icon: "", iconcolor: "" };
});

const followText = computed(() => {
  return followStatus.value.status === 2 ? t("follow") : "";
});

// 统一处理关注人列表和粉丝列表的操作
const followClick = () => {
  if (props.data.is_followed || props.data.is_mutual_follow) {
    // 已经关注的，要弹框确认
    showDialog({
      title: t("unfollowed"),
      content: t("are_you_sure_to_unfollow"),
      confirm_text: t("keep_follow"),
      cancel_text: t("unfollow"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.cancel) {
          emit("follow", props.data);
        }
        close();
      },
    });
  } else {
    emit("follow", props.data);
  }
};
</script>

<style lang="scss" scoped></style>
