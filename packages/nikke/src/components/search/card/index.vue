<template>
  <div
    class="bg-[var(--op-fill-white)] p-[16px] pb-[12px] shadow-[0_1px_5px_0_var(--op-shadow-black-5)] cursor-pointer"
    @click="toPostDetail"
  >
    <div class="flex items-start">
      <div class="flex-1 w-full overflow-hidden">
        <div
          v-safe-html="item.title"
          class="text-[16px] font-bold leading-[19px] text-[color:var(--text-1)] text-ellipsis line-clamp-2 break-words hyphens-auto"
        ></div>
        <div
          v-if="item.content_summary || icon_list.length"
          class="text-[13px] font-normal text-[color:var(--text-1)] opacity-70 mt-[6px]"
        >
          <span class="inline-flex items-center float-left">
            <SvgIcon
              v-for="(item, index) in icon_list"
              :key="index"
              color="var(--text-3)"
              class="h-[16px] w-[16px] mr-[2px]"
              :name="item.name"
            ></SvgIcon>
          </span>
          <p
            v-safe-html="extractTextFromHtml(item.content_summary)"
            class="text-ellipsis line-clamp-3 break-words hyphens-auto"
          ></p>
        </div>
      </div>
      <div v-if="urls && urls.length" class="relative w-[90px] h-[90px] ml-[6px]">
        <CommonImage
          v-if="isVideo"
          key="playIcon"
          :src="playIcon"
          class="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[30%] z-10"
        ></CommonImage>
        <CommonImage
          key="pic"
          :src="urls[0]"
          class="w-full rounded-[4px] h-full"
          image_class="object-cover w-full h-full"
        ></CommonImage>
        <MorePicNum v-if="urls.length > 1" :num="urls.length"></MorePicNum>
      </div>
    </div>
    <!-- 底部 -->
    <div class="mt-[8px] flex justify-between">
      <div class="flex items-center">
        <div class="w-[20px] h-[20px] rounded-[50%] mr-[6px]">
          <Avatar
            :src="item.user?.avatar"
            :auth_type="item.user?.auth_type"
            :frame="item.user?.avatar_pendant"
            class="object-cover w-full"
          />
        </div>
        <div>
          <span
            class="block text-[10px] leading-[12px] font-normal text-[color:var(--text-2)] max-w-[220px] text-ellipsis overflow-hidden whitespace-nowrap"
          >
            {{ item.user?.username }}
          </span>
          <span
            class="block text-[10px] leading-[12px] font-normal text-[color:var(--text-3)] mt-[1px]"
          >
            {{ formatDate(+item.created_on, "MM-DD") }}
          </span>
        </div>
      </div>
      <div class="flex items-center">
        <ButtonIconText icon="icon-comment" :text="item.comment_count + ''" />
        <ButtonIconText
          v-click-interceptor.need_login.mute.sign_privacy.stop="() => likeHandle()"
          class="ml-[24px]"
          icon="icon-line-like"
          :text="formatNumber(item.upvote_count)"
          :is_like_icon="true"
          has-toggle
          active-icon="icon-line-like-cur"
          :is-active="item.my_upvote?.is_star"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import ButtonIconText from "@/components/common/button-icon-text/index.vue";
import { Routes } from "@/router/routes";
import { useRouter } from "vue-router";
import { formatDate } from "@/utils/tools";
import { PostItem } from "packages/types/post";
import Avatar from "@/components/common/avatar/index.vue";
import { CommonImage } from "@/components/common/image/index";
import { PlatId } from "packages/types/common";
import { storeToRefs } from "pinia";
import { useHomeStore } from "@/store/home/<USER>";
import { ComposeContentType } from "packages/types/content";
import MorePicNum from "@/components/common/more-pic-num/index.vue";
import { useWebCredential } from "@/composables/use-webcredential";
import playIcon from "@/assets/imgs/common/play_icon.png";
import { formatNumber } from "packages/utils/tools";
import { extractTextFromHtml } from "packages/utils/dom";

const props = defineProps<{
  item: PostItem;
}>();

const emit = defineEmits(["star", "detail"]);

const router = useRouter();
const { openUrlWithAuth } = useWebCredential();
const { tabList } = storeToRefs(useHomeStore());
const isVideo = computed(() => props.item.type === ComposeContentType.video);

const icon_list = computed(() => {
  return [
    {
      name: "icon-list-vote",
      visible: props.item.show_vote_icon,
    },
    {
      name: "icon-list-union",
      visible: props.item.show_guild_icon,
    },
    {
      name: "icon-list-friend",
      visible: props.item.show_friend_icon,
    },
  ].filter((item) => item.visible);
});

const urls = computed(() => {
  if (isVideo.value) {
    let ext_info = JSON.parse(props.item?.ext_info || "");
    ext_info = Array.isArray(ext_info) ? ext_info[0] : ext_info;
    const video_cover = ext_info.video_cover || ext_info.video_preview_url;
    return video_cover ? [video_cover] : [];
  }
  return props.item.pic_urls;
});

const toPostDetail = () => {
  if (is_event_plate.value) {
    props.item.original_url && openUrlWithAuth(props.item.original_url, "_blank");
    return;
  }
  emit("detail", props.item);
  router.push({
    path: Routes.POST_DETAIL,
    query: {
      post_uuid: props.item.post_uuid,
    },
  });
};

const likeHandle = async () => {
  emit("star", props.item);
};

const is_event_plate = computed(() => {
  return tabList.value.find((item) => item.value == props.item.plate_id)?.key === PlatId.event;
});
</script>

<style lang="scss" scoped></style>
