<template>
  <span
    class="fill-current"
    :style="{
      color,
    }"
    @click="emits('click')"
    v-html="icon"
  ></span>
</template>

<script setup lang="ts">
import { watch, ref } from "vue";

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: "var(--color-white)",
  },
});

const emits = defineEmits(["click"]);

const getSvgIcon = async (name: string) => {
  const module = await import(`../../assets/svg/${name}.svg?raw`);
  return module.default;
};

const icon = ref();

watch(
  () => props.name,
  async () => {
    icon.value = await getSvgIcon(props.name);
  },
  {
    immediate: true,
  },
);
</script>
<style lang="css">
.fill-current > svg {
  width: 100%;
  height: 100%;
}
</style>
