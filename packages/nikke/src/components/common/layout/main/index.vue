<script setup lang="ts">
// cpnts
import VerticalScreenPrompt from "@/components/vertical-screen-prompt/index.vue";
import { useAnniversary } from "@/store/home/<USER>";
import { computed } from "vue";
import { isMobileDevice } from "packages/utils/tools";

// utils

const anniversary_store = useAnniversary();

const style = computed(() => {
  if (anniversary_store.anniversary_bg_config.bg_image) {
    return {
      backgroundImage: `url(${anniversary_store.anniversary_bg_config.bg_image})`,
      backgroundColor: anniversary_store.anniversary_bg_config.bg_color,
    };
  }

  return {};
});

const is_mobile = isMobileDevice();
</script>

<template>
  <div
    :class="[
      `flex justify-center w-full h-full overflow-hidden bg-no-repeat bg-[position:top_center] bg-fixed bg-[100%_auto]`,
    ]"
    :style="style"
  >
    <div
      id="layout-content"
      :class="[
        `overflow-y-auto max-w-[var(--max-pc-w)] w-full relative bg-[var(--fill-3)] no-scrollbar`,
        is_mobile && `pb-[100px]`,
      ]"
    >
      <slot></slot>
    </div>
    <VerticalScreenPrompt></VerticalScreenPrompt>
  </div>
</template>

<style scoped lang="scss"></style>
