<template>
  <div class="relative z-[1]">
    <div
      v-if="item?.text"
      class="text-[color:var(--text-1)] opacity-[0.7] text-[length:13px] leading-[16px] line-clamp-5"
    >
      {{ item?.text }}
    </div>

    <Imgs v-if="item?.imgs" :imgs="item?.imgs" class="mt-[12px]"></Imgs>
  </div>
</template>

<script setup lang="ts">
import Imgs from "@/components/common/imgs/index.vue";

defineProps<{
  item?: any;
}>();
</script>
