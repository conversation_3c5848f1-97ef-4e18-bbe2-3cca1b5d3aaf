<template>
  <div
    class="absolute z-[5] bottom-[4px] right-[4px] h-[20px] px-[6px] rounded-[2px] overflow-hidden flex items-center before:absolute before:top-0 before:left-0 before:w-full before:h-full before:bg-[color:var(--color-black-70)] before:opacity-70"
  >
    <span class="relative h-[9px] w-[9px] mr-[2px]">
      <SvgIcon name="icon-pics" color="var(--color-white)"></SvgIcon>
    </span>
    <span
      class="relative text-[length:11px] text-[color:var(--color-white)] leading-[13px] mt-[3px]"
    >
      +{{ num }}
    </span>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

defineProps<{
  num: number;
}>();
</script>
