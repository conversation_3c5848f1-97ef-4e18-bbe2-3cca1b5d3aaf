<!--
/**
 * Image Viewer
 *
 * <AUTHOR>
 * @create 2024-09-27
 *
 * @example
 * ```vue
 * <template>
 *   <Viewer>
 *     <img src="..." />
 *     ...
 *     <Swiper class="ignore-preview">
 *       <img src="..." />
 *     </Swiper>
 *   </Viewer>
 * </template>
 * <script>
 * import Viewer from "@/components/common/img-viewer/index.vue";
 * </script>
 * ```
 */
-->
<template>
  <component :is="is" ref="dom">
    <slot />

    <teleport v-if="ready && viewer?.length" :to="viewer.toolbar">
      <section class="viewer-toolbox">
        <template v-if="!single">
          <div class="viewer-action" @click="viewer.prev()">
            <SvgIcon
              name="icon-goback"
              :color="index === 0 ? 'var(--color-3)' : 'var(--color-4)'"
            />
          </div>

          <div class="min-w-[50px] text-center text-[length:13px]">
            <span
              :style="{ color: index + 1 === viewer.length ? 'var(--color-3)' : 'var(--color-4)' }"
            >
              {{ index + 1 }}
            </span>
            <span class="text-[color:var(--color-3)]"> / {{ viewer.length }} </span>
          </div>

          <div class="viewer-action rotate-180" @click="viewer.next()">
            <SvgIcon
              name="icon-goback"
              :color="index + 1 === viewer.length ? 'var(--color-3)' : 'var(--color-4)'"
            />
          </div>

          <div class="w-[1px] h-[24px] mx-[6px] bg-[var--fill-1-80)]" />
        </template>

        <div v-if="removable" class="viewer-action" @click="remove">
          <SvgIcon name="icon-delete" color="var(--color-4)" />
        </div>

        <div v-else class="viewer-action" @click="download">
          <SvgIcon name="icon-download" color="var(--color-4)" />
        </div>

        <div class="viewer-action" @click="rotate">
          <SvgIcon name="icon-rotate2" color="var(--color-4)" />
        </div>

        <div class="viewer-action" @click="viewer.hide()">
          <SvgIcon name="icon-close2" color="var(--color-4)" />
        </div>
      </section>
    </teleport>
  </component>
</template>

<script setup lang="ts">
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.css";

import { ref, onMounted, onBeforeUnmount } from "vue";
import { useImageCloudInfinite } from "@/composables/use-image";

import SvgIcon from "@/components/common/svg-icon.vue";

interface Props {
  is?: keyof HTMLElementTagNameMap;
  ignore?: string;
  options?: Viewer.Options;
  removable?: boolean;
  immediate?: boolean;
  lazykey?: string;
}

type Emits = {
  (event: "remove", index: number): any;
};

const props = withDefaults(defineProps<Props>(), {
  is: "div",
  ignore: ".ignore-preview",
  immediate: true,
  lazykey: "data-src",
});

const emit = defineEmits<Emits>();

const { removeQuality } = useImageCloudInfinite();

const dom = ref<HTMLElement>();
const index = ref(0);
const ready = ref(false);
const single = ref(false);
const viewer = ref<
  Viewer & {
    index: number;
    length: number;
    image: HTMLImageElement;
    toolbar: HTMLDivElement;
    imageData: { rotate: number };
  }
>();

/**
 * @todo Rename & Move to Common
 */
function _download(url: string, title = "untitled") {
  const a = document.createElement("a");
  a.href = url;
  a.target = "_blank";
  a.download = title;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

function download() {
  if (!viewer.value) return;
  const { src, alt, title } = viewer.value.image;
  _download(src, alt || title);
}

function remove() {
  if (!viewer.value) return;
  emit("remove", viewer.value.index);
}

function rotate() {
  if (!viewer.value) return;
  viewer.value.rotate(-90);
}

/**
 * @see {@link https://github.com/fengyuanchen/viewerjs}
 */
function init() {
  if (!dom.value) return;
  viewer.value = new Viewer(dom.value as HTMLElement, {
    url: (image: HTMLImageElement) => {
      return removeQuality(image.getAttribute(props.lazykey) || image.src);
    },
    filter: (image: HTMLImageElement) => !image.closest(props.ignore),
    title: false,
    button: false,
    navbar: false,
    toolbar: false,
    movable: true,
    ...props.options,
    ready(e) {
      ready.value = true;
      props.options?.ready?.(e);
    },
    show(e) {
      single.value = viewer.value?.length! < 2;
      props.options?.show?.(e);
    },
    viewed(e) {
      index.value = viewer.value?.index || 0;
      props.options?.viewed?.(e);
    },
  }) as any;
}

const destroy = () => {
  viewer.value?.destroy();
};

const refresh = () => {
  destroy();
  init();
};

onBeforeUnmount(() => {
  if (props.immediate) {
    destroy();
  }
});

onMounted(() => {
  if (props.immediate) {
    init();
  }
});

defineExpose({
  init,
  viewer,
  destroy,
  refresh,
});
</script>

<style>
.viewer-toolbar {
  display: block;
}
.viewer-toolbox {
  display: inline-flex;
  align-items: center;
  color: var(--color-4);
  white-space: nowrap;
  border-radius: 4px;
  background-color: var(--color-2);
}
.viewer-action {
  width: 48px;
  height: 48px;
  padding: 14px;
  background: none !important;
  border-radius: 0;
  cursor: pointer;
}
.viewer-action svg {
  width: 20px;
  height: 20px;
}
</style>
