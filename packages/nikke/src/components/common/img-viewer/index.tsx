// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { CommonImage } from "@/components/common/image/index";

// utils
import { createApp, defineComponent, ref } from "vue";

import Viewer from "viewerjs";
import "viewerjs/dist/viewer.css";
import "./index.css";
import { useImageCloudInfinite } from "@/composables/use-image";
import { extractImageSrcV2 } from "packages/utils/dom";
import { EMOJI_IMAGE_SYMBOL } from "@/configs/const";
import logger from "packages/utils/logger";

const debug = logger("[img-viewer:logger]");
const { removeQuality } = useImageCloudInfinite();

type ViewerInterface = Viewer & {
  index: number;
  length: number;
  image: HTMLImageElement;
  toolbar: HTMLDivElement;
  imageData: { rotate: number };
};

export const ViewerToolbar = defineComponent({
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    removable: {
      type: Boolean,
      default: false,
    },
    viewer: {
      type: Object as () => ViewerInterface,
      default: undefined,
    },
  },
  emits: ["close", "remove", "download", "rotate", "prev", "next"],
  setup(props, { emit }) {
    const renderMultiple = () => {
      if (!props.viewer) {
        return;
      }
      if (!props.multiple) {
        return <></>;
      }
      return (
        <>
          <div class="viewer-action" onClick={() => emit("prev")}>
            <SvgIcon
              name="icon-goback"
              color={props.viewer.index === 0 ? "var(--color-3)" : "var(--color-4)"}
            />
          </div>
          <div class="min-w-[50px] text-center text-[length:13px]">
            <span
              style={{
                color:
                  props.viewer.index + 1 === props.viewer.length
                    ? "var(--color-3)"
                    : "var(--color-4)",
              }}
            >
              {props.viewer.index + 1}
            </span>
            <span class="text-[color:var(--color-3)]"> / {props.viewer.length}</span>
          </div>

          <div class="viewer-action rotate-180" onClick={() => emit("next")}>
            <SvgIcon
              name="icon-goback"
              color={
                props.viewer.index + 1 === props.viewer.length ? "var(--color-3)" : "var(--color-4)"
              }
            />
          </div>
          <div class="w-[1px] h-[24px] mx-[6px] bg-[var--fill-1-80)]" />
        </>
      );
    };

    const renderRemove = () => {
      return props.removable ? (
        <div class="viewer-action" onClick={() => emit("remove")}>
          <SvgIcon name="icon-delete" color="var(--color-4)" />
        </div>
      ) : (
        <div class="viewer-action" onClick={() => emit("download")}>
          <SvgIcon name="icon-download" color="var(--color-4)" />
        </div>
      );
    };

    return () => {
      return (
        <section class="viewer-toolbox">
          {renderMultiple()}
          {renderRemove()}
          <div class="viewer-action" onClick={() => emit("rotate")}>
            <SvgIcon name="icon-rotate2" color="var(--color-4)" />
          </div>
          <div class="viewer-action" onClick={() => emit("close")}>
            <SvgIcon name="icon-close2" color="var(--color-4)" />
          </div>
        </section>
      );
    };
  },
});

const createImageList = (image_list: string[]) => {
  if (!image_list?.length) {
    console.warn("[createImageList] image list is empty");
    return;
  }

  const list = document.createElement("ul");

  list.innerHTML = image_list
    .map((image) => {
      // const original = removeQuality(decodeHTMLEntities(image));
      const li = document.createElement("li");
      let img = createApp(CommonImage, {
        loading: true,
        src: image,
        thumbnail: true,
        auto_dethumbnail: true,
      });
      img.mount(li);
      setTimeout(() => {
        img.unmount();
        img = null as any;
      }, 0);
      return li.outerHTML;
    })
    .join("");
  return list;
};

const getImageList = (image_list?: string[], dom?: HTMLElement): string[] => {
  if (image_list) {
    debug.log("[getImageList] image_list", image_list);
    return image_list;
  }
  if (dom) {
    const list = extractImageSrcV2(dom.innerHTML).filter(
      (url: string) => !url.includes(EMOJI_IMAGE_SYMBOL),
    );
    debug.log("[getImageList] extractImageSrc list", list);
    return list;
  }
  return [];
};

export const createViewer = (options?: {
  image_list?: string[];
  index?: number;
  src?: string;
  target?: HTMLElement;
  ignore?: string;
  dom?: HTMLElement;
}) => {
  let { src, target, ignore, dom } = Object.assign(
    {
      src: "",
      ignore: ".ignore-preview",
      image_list: [
        // "https://sg-cdn.blablalink.com/standalonesite/ugc/public/image/4d784519-72a7-4b58-9153-26f803f9fa0f.png?height=1500&amp;width=1000&",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/58fef752-9d0a-4990-824a-ee16b3578d52.jpeg?height=2160&width=3840",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/03743723-3181-48cc-a402-23e9f1468e0e.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/bbec8a51-46a0-46b2-a154-e57b8adcbe4a.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/426de436-8029-45f7-a4b2-a1dd50309ef0.gif?height=126&width=120",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/bf2ebaa2-e255-4eab-923e-d22b972a8b26.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/a7198ceb-3962-46af-afa5-99676b8babc1.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/d4ef48c3-9dfe-47d8-ad3d-27a64850af7e.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/6c112d04-2cee-4711-9417-f8fe36e48574.gif?height=126&width=120",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/3d53a373-6d06-4e19-8ef7-64bde4cbd5d3.gif?height=378&width=360",
        // "https://lipcommunity-dev-1312254802.cos.ap-singapore.myqcloud.com/standalonesite/ugc/public/image/dc7ebfb5-864e-4566-b769-ef179f99e43c.gif?height=378&width=360",
      ],
    },
    options,
  );

  if (!src && target) {
    src = target.getAttribute("src") || "";
  }

  debug.log("[createViewer] src", src);

  const image_list = getImageList(options?.image_list, dom);
  const src_found_index = image_list?.findIndex((item) => src.includes(item));
  const index = options?.index ?? src_found_index;

  debug.log("[createViewer] image_list", image_list);
  debug.log("[createViewer] src_found_index", src_found_index);
  debug.log("[createViewer] index", index);

  // src not found in image list
  if (index < 0) {
    return;
  }

  const container = createImageList(image_list);

  if (!container) {
    throw new Error("[createViewer]: container is not found");
  }

  const viewer = ref<ViewerInterface>();

  const onDownload = () => {
    if (!viewer.value?.image) return;
    const { src, alt, title } = viewer.value.image;
    const a = document.createElement("a");
    a.href = src;
    a.target = "_blank";
    a.download = alt || title;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const onPrev = () => {
    if (!viewer.value?.image) return;
    viewer.value.prev();
  };

  const onNext = () => {
    if (!viewer.value?.image) return;
    viewer.value.next();
  };

  const onRotate = () => {
    if (!viewer.value?.image) return;
    viewer.value.rotate(-90);
  };

  const onClose = () => {
    if (!viewer.value) return;
    viewer.value.destroy();
    viewer.value = undefined;
  };

  const show = () => {
    if (viewer.value) {
      viewer.value.show();
      const toolbar = createApp(ViewerToolbar, {
        viewer: viewer.value,
        onDownload,
        onPrev,
        onNext,
        onRotate,
        onClose,
      });
      toolbar.mount(viewer.value.toolbar);
    }
  };

  if (viewer.value) {
    show();
    return;
  }

  viewer.value = new Viewer(container, {
    title: false,
    button: false,
    navbar: false,
    toolbar: false,
    movable: true,
    initialViewIndex: index,
    url: (image: HTMLImageElement) => image.src,
    filter: (image: HTMLImageElement) => !image.closest(ignore),
    viewed() {
      const img = viewer.value?.image as HTMLElement;
      if (img) {
        setTimeout(() => {
          const src = img.getAttribute("src") || "";
          img.setAttribute("src", removeQuality(src));
        }, 200);
      }
    },
    ready() {},
    /**
     * @description 立即触发
     */
    hide() {},
    /**
     * @description 隐藏后触发
     */
    hidden() {
      onClose();
    },
  }) as any;

  show();
};
