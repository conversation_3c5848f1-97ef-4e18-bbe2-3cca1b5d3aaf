<template>
  <div
    ref="postRef"
    class="p-[15px] relative z-10 overflow-hidden cursor-pointer border-b-[6px] border-[var(--line-1)] card-item"
    @click="goToPostDetail()"
  >
    <div class="flex h-[44px] mb-[12px]">
      <div
        class="w-[42px] h-[42px] m-[2px] rounded-full mr-[8px]"
        :class="[item.user?.avatar_pendant && '!mr-[12px]']"
      >
        <Avatar
          :src="item.user?.avatar"
          :auth_type="item.user?.auth_type"
          :frame="item.user?.avatar_pendant"
          alt=""
          @click.stop="goToUserCenter"
        />
      </div>
      <div class="flex-1 mt-[5px] w-[1px] mr-[4px]" @click.stop="goToUserCenter">
        <div class="flex items-center">
          <div
            class="truncate text-[color:var(--text-1)] font-bold text-[length:14px] leading-[17px] mr-[4px]"
          >
            {{ item.user?.username }}
          </div>
          <!-- <TypeLabel
            :game_tag="item.user?.game_tag"
            :game_tag_num="item.user?.game_tag_num"
            :mood="item.user?.mood"
            :is_self="false"
          ></TypeLabel> -->
        </div>
        <div class="text-[color:var(--text-3)] text-[length:12px] leading-[16px] mt-[2px]">
          <div class="text-[length:12px] flex items-center flex-wrap">
            <span v-if="item.user?.auth_desc" class="mr-[8px] text-[color:var(--brand-1)] truncate">
              {{ item.user?.auth_desc }}
            </span>
            <span v-if="!is_release && user_store.user_can_publish_multiple_language">
              <span class="mr-[4px]">
                {{ formatUnRealseTime(item.publish_on || +item.created_on) }}
              </span>
              <span>({{ t("unrelease") }})</span>
            </span>
            <span v-else>
              {{ formatTime((item.publish_on || +item.created_on) * 1000, t) }}
              {{ show_plate_name && item.plate_name ? ` · ${item.plate_name || ""}` : "" }}
            </span>
          </div>
        </div>
      </div>
      <div class="mt-[8px]">
        <Btns v-if="item.user.is_black" :text="t('blocking')" type="disabled"></Btns>
        <Btns
          v-else
          v-click-interceptor.need_login.mute.sign_privacy.stop="followClick"
          :icon="followStatus.icon"
          :text="followText"
          :iconcolor="followStatus?.iconcolor"
          :type="followText ? `primary` : 'default'"
        >
          <template #icon>
            <span class="text-[18px] mr-[2px]">+ </span>
          </template>
        </Btns>
      </div>
    </div>

    <AuthoingStatementTipsCpnt
      class="mb-[8px]"
      :ai_content_type="item.ai_content_type"
      :risk_remind_type="item.risk_remind_type"
    ></AuthoingStatementTipsCpnt>

    <div
      v-if="item.title"
      class="font-bold text-[color:var(--text-1)] text-[length:14px] leading-[18px] mb-[4px] line-clamp-2"
    >
      {{ item.title }}
    </div>

    <div
      v-if="item.content_summary || icon_list.length"
      class="text-[color:var(--text-3)] text-[length:12px] line-clamp-3 whitespace-normal break-words"
    >
      <span
        v-safe-html.merge="extractTextFromHtml(item.content_summary).slice(0, 300)"
        class="content-summary mr-[8px]"
      >
        <img
          v-for="(icon_item, index) in icon_list"
          :key="index"
          :src="icon_item.icon"
          class="h-[16px] w-[16px] mr-[2px] text-[var(--text-3)] last-of-type:mr-[6px]"
          align="left"
        />
      </span>
    </div>

    <Topics v-if="item.tags.length > 0" :data="item.tags.slice(0, 3)" class="mt-[2px]"></Topics>

    <template v-if="showMedia">
      <Medias
        :img-list="item.pic_urls"
        :type="[1, 2].includes(item.type) ? 'image' : 'video'"
        :ext_info="item.ext_info"
        class="mt-[12px]"
        @play="onPlay"
      />
    </template>

    <div class="mt-[12px]">
      <div class="flex items-center justify-between">
        <SvgIcon name="icon-card-line" color="var(--line-1)" class="h-[6px] w-[16px]"></SvgIcon>
        <i class="flex-1 h-[1px] bg-[var(--line-1)] mx-[2px]"></i>
        <SvgIcon
          name="icon-card-line"
          color="var(--line-1)"
          class="h-[6px] w-[16px] rotate-180"
        ></SvgIcon>
      </div>

      <div class="flex justify-around items-center mt-[12px] gap-[30px] pl-[8px]">
        <template v-for="(foot_item, index) in footer_list">
          <span v-if="foot_item.type === 'divider'" :key="index + 'divider'" class="mr-auto"></span>

          <span
            v-else-if="foot_item.type === 'like'"
            :key="index + 'like'"
            class="inline-flex items-center justify-center"
            @click.stop="foot_item.onClick"
          >
            <StanceLike
              icon_class="w-[20px] h-[20px]"
              :is_star="item.my_upvote?.is_star"
              :value="foot_item.value"
            ></StanceLike>
          </span>

          <span
            v-else
            :key="index"
            v-click-interceptor.need_login.mute.sign_privacy.stop="foot_item.onClick"
            class="inline-flex items-center justify-center"
          >
            <SvgIcon
              :name="foot_item.icon"
              class="w-[20px] h-[20px] shrink-0"
              :color="foot_item.active ? `var(--brand-1)` : `var(--text-3)`"
            ></SvgIcon>
            <span
              v-if="!foot_item.only_icon"
              class="text-[length:12px] font-normal leading-[14px] text-[color:var(--text-3)] ml-[4px] mt-[2px] min-w-[20px]"
            >
              {{ foot_item.value }}
            </span>
          </span>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AuthoingStatementTipsCpnt } from "@/views/post/detail/authoring-statement-atomic.tsx";
import SvgIcon from "@/components/common/svg-icon.vue";
import { ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import Avatar from "@/components/common/avatar/index.vue";
import StanceLike from "@/components/common/stance/like.vue";
import Medias from "@/components/common/medias/index.vue";
import Btns from "@/components/common/btns/index.vue";
import Topics from "@/components/common/topics/index.vue";
import { PostAuditStatus, PostItem } from "packages/types/post";
import { formatTime, formatUnRealseTime } from "@/utils/str";
import { getPostItemOperationAuth, resovledPostListCard } from "@/utils/home";
import { useI18n } from "vue-i18n";
import { Routes } from "@/router/routes";
import { postShareUrl } from "@/api/post";
import { useDialog } from "@/components/ui/dialog/index";
import { PopCallbackValue } from "packages/types/common";
import { getApiBaseUrl, formatNumber, itemStatusAndCountHandler } from "packages/utils/tools";
import { report } from "packages/utils/tlog";
import { useToast } from "@/components/ui/toast";
import { useScrollVisible } from "@/composables/use-scroll-visible";
import { useCommonShare } from "@/components/common/share-pop";
import { useUser } from "@/store/user.ts";
import { useInterceptor } from "@/composables/use-interceptor";
import { extractTextFromHtml } from "packages/utils/dom";
import { usePostDetailStore } from "@/store/post/detail";

// assets
import IconListVote from "@/assets/imgs/post/icon-list-vote.png";
import IconListUnion from "@/assets/imgs/post/icon-list-union.png";
import IconListFriend from "@/assets/imgs/post/icon-list-friend.png";

const user_store = useUser();
const { t } = useI18n();
const router = useRouter();
const { show: showDialog } = useDialog();
const detail_link = ref(""); // post 详情路径确定以后扔到这
const { checkUserAdultInterceptor } = useInterceptor();

const props = withDefaults(
  defineProps<{
    need_expose?: boolean;
    index?: number; // 索引
    plate_id?: number; // 板块id
    plate_name?: string; // 板块名称
    /** 是否在日期后面展示 (OutPost/NikkeArch), 仅个人中心的3个tab需要展示 */
    show_plate_name?: boolean;
    item: PostItem;
    /** 是否确保跳转的详情页仅返回原文，不要默认翻译 */
    original_content?: boolean;
  }>(),
  {
    need_expose: false, // 是否需要曝光上报，默认false
    index: 0, // 索引
    plate_id: 0, // 板块id
    plate_name: "", // 板块名称
  },
);

const post_detail_store = usePostDetailStore();

// 展示媒体数据
const showMedia = computed(
  () => resovledPostListCard(props.item).pic_urls.length > 0 || props.item.type === 3,
);

// 获取上报数据
const getReportData = () => {
  return {
    location: props.index,
    content_id: props.item.post_uuid,
    label_id: props.plate_id,
    label_name: props.plate_name,
  };
};

const icon_list = computed(() => {
  return [
    {
      name: "icon-list-vote",
      visible: props.item.show_vote_icon,
      icon: IconListVote,
    },
    {
      name: "icon-list-union",
      visible: props.item.show_guild_icon,
      icon: IconListUnion,
    },
    {
      name: "icon-list-friend",
      visible: props.item.show_friend_icon,
      icon: IconListFriend,
    },
  ].filter((item) => item.visible);
});

const footer_list = computed(() => {
  return [
    {
      icon: "icon-eye-open",
      type: "view",
      value: formatNumber(props.item.browse_count),
    },
    {
      icon: "icon-comment",
      type: "comment",
      value: formatNumber(props.item.comment_count),
      async onClick() {
        await checkUserAdultInterceptor();

        goToPostDetail("comment");
        emit("comment", props.item);

        report.standalonesite_news_comment_btn.cm_click(getReportData());
      },
    },
    {
      icon: props.item.my_upvote?.is_star ? "icon-line-like-cur" : "icon-line-like",
      type: "like",
      value: formatNumber(props.item.upvote_count),
      active: props.item.my_upvote?.is_star,
      onClick() {
        emit("star", props.item);

        report.standalonesite_news_praise_btn.cm_click({
          ...getReportData(),
          is_praise: props.item.my_upvote?.is_star ? 0 : 1,
        });
      },
    },
    { icon: "", type: "divider" },
    {
      icon: props.item.is_collection ? "icon-collection-cur" : "icon-collection",
      type: "collection",
      active: props.item.is_collection,
      only_icon: true,
      // value: props.item.collection_count,
      async onClick() {
        await post_detail_store.onCollection(props.item.post_uuid);
        itemStatusAndCountHandler(props.item, "is_collection", "collection_count");
      },
    },
    {
      icon: "icon-share-link",
      type: "share",
      only_icon: true,
      // value: formatNumber(props.item.forward_count),
      onClick() {
        if (props.item.is_audit !== PostAuditStatus.success)
          return useToast().show({ text: t("content_under_review"), type: "warning" });

        let apiUrl = getApiBaseUrl(import.meta.env.MODE, true) as string;
        apiUrl = apiUrl.slice(0, apiUrl.length - 1);
        detail_link.value = `${apiUrl}${postShareUrl}?post_uuid=${props.item.post_uuid}`;
        useCommonShare({
          onShare: (channel_name) => emit("share", props.item, channel_name, detail_link.value),
        }).share({
          text: t("share_others_text"),
          url: `${location.origin}/post/detail?post_uuid=${props.item.post_uuid}`,
        });

        report.standalonesite_news_share_btn.cm_click(getReportData());
      },
    },
  ];
});

// 获取关注状态
const followStatus = computed(() => getPostItemOperationAuth(props.item));
const emit = defineEmits(["manage", "share", "star", "follow", "detail", "comment"]);

const reportFollow = () => {
  const params = getReportData();
  return {
    ...params,
    dst_open_id: props.item.user.intl_openid,
    is_follow: props.item.is_follow ? 0 : 1,
  };
};

// 关注点击
const followClick = () => {
  if (followStatus.value.status === 1) {
    emit("manage", props.item);
    return;
  } else {
    if (props.item.is_follow || props.item.is_mutual_follow) {
      // 已经关注的，要弹框确认
      showDialog({
        title: t("unfollowed"),
        content: t("are_you_sure_to_unfollow"),
        confirm_text: t("keep_follow"),
        cancel_text: t("unfollow"),
        async callback(options: { value: PopCallbackValue; close: () => void }) {
          const { value, close } = options;
          if (value === PopCallbackValue.cancel) {
            emit("follow", props.item);
            report.standalonesite_news_follow_btn.cm_click(reportFollow());
          }
          close();
        },
      });
    } else {
      emit("follow", props.item);
      report.standalonesite_news_follow_btn.cm_click(reportFollow());
    }
  }
};

const followText = computed(() => {
  return followStatus.value.status === 2 ? t("follow") : "";
});
// 视频播放上报（titok TODO）
const onPlay = () => {
  report.standalonesite_news_video_btn.cm_click(getReportData());
};

const goToPostDetail = (target?: string) => {
  const query = Object.assign(
    {
      post_uuid: props.item.post_uuid,
      original_content: props.original_content ? 1 : undefined,
    },
    target ? { scroll_target: target } : {},
  );
  router.push({
    path: Routes.POST_DETAIL,
    query,
  });
  emit("detail", props.item);

  report.standalonesite_news_item_click.cm_click(getReportData());
};

const goToUserCenter = () => {
  router.push({
    path: Routes.USER,
    query: { openid: props.item.user.intl_openid },
  });
};

const postRef = ref<HTMLElement | null>(null);
const { visible } = useScrollVisible(postRef);

const is_release = computed(() => {
  return (
    props.item.publish_on * 1000 < Date.now() && props.item.is_audit === PostAuditStatus.success
  );
});

watch(
  () => visible.value,
  (val: boolean) => {
    if (!props.need_expose) return;
    // 已经曝光过的，忽略
    if (val && !props.item.is_exposed) {
      report.standalonesite_news_item_expose.cm_vshow(getReportData());
      props.item.is_exposed = true;
    }
  },
);
</script>

<style lang="scss" scoped>
:deep(.content-summary) {
  img {
    max-height: 250px;
  }
}
</style>
