import Share from "./share.vue";
import { onUnmounted } from "vue";
import { mountDialogComponent } from "@/utils/mount";
import { useCopy } from "@/shiftyspad/composable/interact/copy";
import { isMobileDevice, getSpecialBrowserType, isGameLogin } from "packages/utils/tools";

export function useCommonShare(params?: {
  component?: any;
  ignore_channels?: string[];
  onShow?: () => void;
  onShare?: (name: string) => void;
  onError?: () => void;
  onClose?: () => void;
}) {
  const voidcb = () => {};
  const { support_copy } = useCopy();

  const getHideChannels = () => {
    const hide_channel: Set<string> = new Set();
    if (!navigator.canShare?.()) {
      hide_channel.add("system");
    }
    if (isGameLogin()) {
      hide_channel.add("system");
    }
    if (!isMobileDevice()) {
      hide_channel.add("Line");
    }
    if (["facebook", "line"].includes(getSpecialBrowserType())) {
      hide_channel.add("system");
    }
    if (!support_copy.value) {
      hide_channel.add("copy");
    }
    hide_channel.add("Reddit");
    return Array.from(hide_channel);
  };

  const {
    component,
    onShow = voidcb,
    onClose = voidcb,
    onError = voidcb,
    onShare = voidcb,
    ignore_channels = [],
  } = params ?? {};
  const { unmount, instance } = mountDialogComponent(Share, {
    comp: component,
    onShow,
    onShare,
    onError,
    onClose: () => {
      unmount();
      onClose();
    },
    ignore_channels: [ignore_channels, getHideChannels()].flat(),
  });

  const share = (params: { text?: string; url?: string; image?: any }) =>
    (instance as any).invoke(params);

  onUnmounted(() => {
    unmount();
  });

  return { share, unmount };
}
