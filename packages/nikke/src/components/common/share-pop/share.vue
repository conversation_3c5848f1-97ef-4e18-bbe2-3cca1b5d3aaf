<template>
  <Teleport to="body">
    <template v-if="display">
      <div
        v-if="comp"
        id="test"
        class="bg-[var(--op-fill-white)] fixed z-[-10000] top-0 left-[-9999px]"
      >
        <component :is="comp" ref="comp_ref"></component>
      </div>
      <Spinner v-if="loading"></Spinner>
      <div
        v-if="image"
        class="fixed z-[10000] left-1/2 transform -translate-x-1/2"
        :class="`${isMobileDevice() ? 'top-[22px]' : 'top-[44px]'}`"
      >
        <img :class="`${isMobileDevice() ? 'max-h-[50vh]' : 'max-h-[65vh]'}`" :src="image" />
      </div>
    </template>
  </Teleport>

  <CommBottomPopup :visible="display" line @close="close">
    <div class="p-[24px] pt-[36px] pb-[38px]">
      <div
        class="font-bold text-[length:18px] text-[color:var(--text-1)] leading-[22px] text-center"
      >
        {{ t("share_to") }}
      </div>
      <div class="flex flex-wrap justify-center items-baseline mt-[24px]">
        <div
          v-for="(item, index) in share_channels"
          :key="index"
          class="flex flex-col cursor-pointer mb-[10px] flex-shrink-0 items-center justify-center w-[66px] mx-[6px]"
          @click="clickShare(item)"
        >
          <img :src="item?.logo" alt="" class="w-[44px] h-[44px] mx-auto" />
          <div
            class="mt-[6px] text-center text-[length:12px] leading-[13px] text-[color:var(--text-2)]"
          >
            {{ item?.name }}
          </div>
        </div>
      </div>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import html2canvas from "html2canvas";
import { share, WebShareChannel } from "@tencent/pa-share-utils";
import { computed, nextTick, ref, toRefs, onUnmounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useToast } from "@/components/ui/toast";
import { useCopy } from "@/shiftyspad/composable/interact/copy";
import { useResponsive } from "@/composables/use-responsive";

import { report } from "packages/utils/tlog";
import { isMobileDevice } from "packages/utils/tools";
import { CONST_URL_WHITE_LIST_KEY as INGAME_URL_QUERYS } from "packages/configs/const";
import { isInIntlBrowser, openInBrowser } from "@tencent/pa-ingame-utils";
import { useWebCredential } from "@/composables/use-webcredential";

const { new_is_landscape } = useResponsive();
const getAssetsImage = (url: string) => {
  if (!import.meta.env.DEV) {
    return new URL(`../../../assets/imgs/logo/icon-${url}.png`, import.meta.url).href;
  }
  return `/src/assets/imgs/logo/icon-${url}.png`;
};

const { show } = useToast();
const emits = defineEmits<{
  close: [];
  show: [];
  error: [];
  share: [value: (typeof social_list)[number]["name"]];
}>();
const props = withDefaults(
  defineProps<{
    comp: any;
    ignore_channels?: (typeof social_list)[number]["type"][];
  }>(),
  {},
);

const display = ref(false);
const text = ref("");
const origin_url = ref("");
const loading = ref(false);
const image = ref<any>(null);
const { ignore_channels, comp } = toRefs(props);
const { t } = useI18n();
const { openUrlWithAuth } = useWebCredential();

const url = computed(() => {
  // TODO: 这里的 origin_url.value 如果没有协议，那么会被处理成一个路径
  const url_obj = new URL(origin_url.value, location.origin);
  INGAME_URL_QUERYS.forEach((key) => {
    if (["gameid", "lang"].includes(key)) return;
    // fix
    url_obj.searchParams.delete(key);
  });
  return url_obj.href;
});

const { copy } = useCopy({
  onError: () => emits("error"),
});

const close = () => {
  blur(false);
  display.value = false;
  loading.value = false;
  image.value = null;
  emits("close");
};

watch(
  () => new_is_landscape.value,
  () => close(),
);
onUnmounted(() => {
  blur(false);
});

const shareAction = (link: string) => {
  if (isInIntlBrowser) {
    return openInBrowser(link);
  }
  return openUrlWithAuth(link, "_blank");
};

const comp_ref = ref();
const blur = (b: boolean) => {
  const app = document.querySelector("#app") as HTMLElement;
  if (app) {
    app.style.filter = b && display.value ? "blur(3px)" : "";
  }
};
const resetPage = () => {
  window.pageYOffset = 0;
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
const createImage = () => {
  if (!comp.value) {
    return;
  }
  resetPage();
  nextTick(async () => {
    loading.value = true;
    if (!comp_ref.value.isready?.()) {
      setTimeout(() => {
        createImage();
      }, 200);
      return;
    }
    await new Promise((resolve) => setTimeout(resolve, 500));
    try {
      const canvas = await domToCanvas(document.getElementById("test")!);
      image.value = canvasToDataUri(canvas);
      blur(true);
    } catch (err) {
      close();
      console.error(err);
      // show({ text: t("load_content_error"), type: "error" });
    } finally {
      loading.value = false;
    }
  });
};

const clickShare = (channel: (typeof social_list)[number]) => {
  emits("share", channel.name);
  if (channel.get_share_params) {
    shareAction(share(channel.get_share_params(), { only_get_url: true }));
  }
  if (channel.type === "copy") {
    copy(url.value || location.href);
  }
  if (channel.type === "system") {
    systemShare();
  }
  close();
};

function dataURLtoFile(
  dataurl: string,
  filename: string,
): {
  blob: Blob;
  file: File;
} {
  const arr = dataurl.split(",");
  // @ts-ignore
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return {
    file: new File([u8arr], filename, { type: mime }),
    blob: new Blob([u8arr], { type: mime }),
  };
}

function canvasToDataUri(
  canvas: HTMLCanvasElement,
  options?: { type: string; compress_rate: number },
) {
  const { type = "image/png", compress_rate = 1 } = options || {};
  return canvas.toDataURL(type, compress_rate);
}

function domToCanvas(el: HTMLElement) {
  return html2canvas(el, {
    useCORS: true,
    allowTaint: true,
    backgroundColor: null,
    scale: isMobileDevice() ? 2 : 1,
    ignoreElements: (node) => {
      const isHead = document.head.contains(node);
      const isParent = node.contains(el);
      const isChild = el.contains(node);
      return !(isHead || isParent || isChild);
    },
  });
}

const systemShare = async () => {
  if (comp.value && image.value) {
    const file = dataURLtoFile(image.value, "share.png").file;
    const can_share = navigator.canShare({
      files: [file],
      url: url.value,
      text: text.value,
    });
    can_share &&
      navigator.share({
        files: [file],
      });
    return;
  }
  const cantext_share = navigator.canShare({
    text: text.value,
    url: url.value,
  });
  if (!cantext_share) {
    return show({ text: t("share_failed"), type: "error" });
  }
  navigator.share({
    url: url.value,
    text: text.value,
  });
};

const social_list: {
  type?: "copy" | "system";
  logo: string;
  name: string;
  get_share_params?: () => Parameters<typeof share>[0];
}[] = [
  {
    logo: getAssetsImage("facebook"),
    name: "Facebook",
    get_share_params: () => ({
      third_type: WebShareChannel.Facebook,
      quote: text.value,
      href: url.value,
    }),
  },
  {
    logo: getAssetsImage("twitter"),
    name: "Twitter",
    get_share_params: () => ({
      third_type: WebShareChannel.Twitter,
      url: url.value,
      text: text.value,
    }),
  },
  {
    logo: getAssetsImage("reddit"),
    name: "Reddit",
    get_share_params: () => ({
      third_type: WebShareChannel.Reddit,
      url: url.value,
      text: text.value,
      title: text.value,
    }),
  },
  {
    logo: getAssetsImage("naver"),
    name: "Naver",
    get_share_params: () => ({
      third_type: WebShareChannel.Naver,
      title: text.value,
      url: url.value,
    }),
  },
  {
    logo: getAssetsImage("line"),
    name: "Line",
    get_share_params: () => ({
      third_type: WebShareChannel.Line,
      text: `${text.value || ""} ${url.value}`,
    }),
  },
  {
    logo: getAssetsImage("whats-app"),
    name: "whatsApp",
    get_share_params: () => ({
      third_type: WebShareChannel.Whatsapp,
      text: `${text.value || ""} ${url.value}`,
    }),
  },
  {
    logo: getAssetsImage("copy-link"),
    name: t("copy_link"),
    type: "copy",
  },
  {
    logo: getAssetsImage("system"),
    name: t("system_share"),
    type: "system",
  },
];
const share_channels = computed(() =>
  social_list.filter(
    (item) =>
      !ignore_channels.value?.includes(item.type) &&
      !ignore_channels.value?.includes(item.name as any),
  ),
);

defineExpose({
  invoke: (params: { text?: string; url?: string; image?: any }) => {
    display.value = true;
    const { text: t = "", url: u = location.href } = params;
    text.value = t;
    origin_url.value = u;
    createImage();
    emits("show");
    report.standalonesite_share_window.cm_vshow({ url: url.value, dst_url: url.value });
  },
});
</script>
