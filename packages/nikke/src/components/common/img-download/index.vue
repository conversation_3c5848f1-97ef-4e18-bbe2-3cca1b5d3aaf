<template>
  <div class="w-full overflow-x-auto overflow-y-hidden flex items-center py-[3px]">
    <div v-for="(item, index) in list" :key="index" class="relative mr-[6px]">
      <div class="w-[50px] h-[50px] rounded-[4px] overflow-hidden" @click="itemclick(index)">
        <img :src="item?.src" alt="" />
      </div>
      <div
        class="w-[17px] h-[17px] z-[2] flex items-center justify-center absolute right-[-3px] top-[-2px] bg-[var(--fill-1-80)] rounded-full cursor-pointer"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-delete" color="var(--color-white)" class="w-[10px] h-[10px]"></SvgIcon>
      </div>
    </div>
    <ImgLook :visible="visible" :src="list[active]?.src" @close="visible = !visible"></ImgLook>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import ImgLook from "@/components/common/img-look/index.vue";

import test from "@/assets/imgs/test/pic1.jpg";
import test1 from "@/assets/imgs/test/pic1.jpg";
import test2 from "@/assets/imgs/test/pic1.jpg";

import { ref } from "vue";

const visible = ref(false);
const active = ref(0);

const list = [
  {
    src: test,
  },
  {
    src: test1,
  },
  {
    src: test2,
  },
  {
    src: test,
  },
  {
    src: test1,
  },
  {
    src: test2,
  },
  {
    src: test,
  },
  {
    src: test1,
  },
  {
    src: test2,
  },
  {
    src: test,
  },
  {
    src: test1,
  },
  {
    src: test2,
  },
];

const itemclick = (index: number) => {
  visible.value = !visible.value;
  active.value = index;
};
</script>
