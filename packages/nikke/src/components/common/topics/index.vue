<template>
  <div
    v-if="!isInline"
    class="flex items-center"
    :class="[nowrap ? 'overflow-y-hidden overflow-x-auto' : 'flex-wrap']"
  >
    <div
      v-for="(item, index) in data"
      :key="index"
      :class="[
        `cursor-pointer text-[var(--brand-1)] mr-[8px] last-of-type:mr-0`,
        !nowrap ? `whitespace-nowrap overflow-hidden text-ellipsis` : ``,
        computed_font,
      ]"
      @click.stop="handleClick(item)"
    >
      #{{ item.name }}
    </div>
  </div>
  <template v-else>
    <div
      v-for="(item, index) in data"
      :key="index"
      class="inline cursor-pointer whitespace-nowrap text-[var(--brand-1)] mr-[8px] last-of-type:mr-0"
      :class="computed_font"
      @click.stop="handleClick(item)"
    >
      #{{ item.name }}
    </div>
  </template>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Tag } from "packages/types/post";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";

const props = withDefaults(
  defineProps<{
    data: Tag[];
    size?: "sm" | "md" | "lg";
    isInline?: boolean;
    nowrap?: boolean;
  }>(),
  {
    size: "md",
    isInline: false,
    nowrap: false,
  },
);

const router = useRouter();

const computed_font = computed(() => {
  if (props.size === "sm") {
    return "text-[length:11px] leading-[13px]";
  } else {
    return "text-[length:12px] leading-[16px]";
  }
});

const handleClick = (item: Tag) => {
  router.push({
    path: Routes.TOPIC,
    query: { tag_id: item.id },
  });
};
</script>
