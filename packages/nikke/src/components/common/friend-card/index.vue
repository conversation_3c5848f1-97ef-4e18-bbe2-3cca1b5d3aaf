<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { RequestBtnAtomicCpnt } from "./atomic";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
// types
import { FriendCardStatus, GetUserGamePlayerInfoResponse } from "packages/types/user";
// assets
// import default_avatar from "@/assets/imgs/test/team-combat-icon.png";
import default_avatar from "@/shiftyspad/assets/images/appicon.png";
import team_combat_icon from "@/assets/imgs/test/team-combat-icon.png";
// utils
import { useGameRegion } from "@/composables/role/use-server-info";
import { PostDetail } from "packages/types/post";

const { getServerName } = useGameRegion();

const props = defineProps<{
  user_game_player_info: GetUserGamePlayerInfoResponse | PostDetail["friend_card"];
  delete_visible?: boolean;
  disabled?: boolean;
  status: FriendCardStatus;
}>();

const emits = defineEmits(["delete", "request", "click"]);

const onDelete = () => emits("delete");
const onRequest = () => {
  if (props.status === FriendCardStatus.canrequest) {
    emits("request");
  }
};
</script>

<template>
  <div
    v-click-interceptor.need_login.mute.sign_privacy.stop="() => emits('click')"
    class="h-[68px] w-full px-[12px] flex items-center bg-[color:var(--op-fill-white)] border-[1px] border-[var(--line-1)] relative cursor-pointer"
  >
    <div class="mr-[12px] flex-shrink-0 w-[46px]">
      <RoleAvatar
        :avatar_id="user_game_player_info.icon"
        :default_avatar="default_avatar"
        class="!w-[46px] !h-[46px]"
      ></RoleAvatar>
      <div
        class="-mt-[4px] relative z-[2] mx-auto border-[1px] border-[color:var(--op-line-white)] px-[3px] h-[11px] flex items-center justify-center text-[length:9px] text-[color:var(--op-text-white)] font-[Inter] bg-[var(--brand-1)] max-w-max"
      >
        LV.{{ user_game_player_info.player_level }}
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <div class="mr-[12px] flex-1 mt-[6px] overflow-hidden">
        <div
          class="font-[Inter] text-[length:13px] leading-[16px] text-[color:var(--text-1)] truncate font-medium flex-1"
        >
          {{ user_game_player_info.role_name }}
        </div>
        <div
          class="font-[Inter] text-[length:9px] leading-[11px] text-[color:var(--text-1)] mt-[6px]"
        >
          {{ getServerName(user_game_player_info.area_id) }}
        </div>
      </div>
      <div class="min-w-[90px] max-w-max flex-shrink-0 mt-[2px]">
        <div class="flex items-center mb-[4px] last-of-type:mb-0">
          <img :src="team_combat_icon" alt="" class="w-[22px] object-contain mr-[4px]" />
          <div class="text-[length:12px] font-bold leading-[1] text-[color:var(--text-1)]">
            {{ user_game_player_info.team_combat }}
          </div>
        </div>
      </div>
    </div>

    <RequestBtnAtomicCpnt :status="status" @request="onRequest"></RequestBtnAtomicCpnt>

    <div
      v-if="delete_visible"
      class="absolute z-[2] -top-[10px] right-[1px] w-[20px] h-[20px] bg-[var(--fill-1-80)] rounded-full flex items-center justify-center cursor-pointer"
      @click="onDelete"
    >
      <SvgIcon name="icon-delete" color="var(--op-text-white)" class="w-[12px] h-[12px]"></SvgIcon>
    </div>
  </div>
</template>
