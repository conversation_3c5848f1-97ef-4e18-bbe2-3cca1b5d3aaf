// assets
import bg1 from "@/assets/imgs/common/join-friend-bg1.png";
import bg2 from "@/assets/imgs/common/join-friend-bg2.png";
import bg3 from "@/assets/imgs/common/join-friend-bg3.png";
// types
import { FriendCardStatus } from "packages/types/user";
// utils
import { t } from "@/locales";

export const useFriendCard = () => {
  const getIconConfig = (status: FriendCardStatus) => {
    console.log(`status`, status);
    switch (status) {
      case FriendCardStatus.disabled:
        return {
          bg: bg1,
          icon_name: "icon-no-follower",
          color: "var(--text-3)",
          text: t("request"),
        };
      case FriendCardStatus.canrequest:
        return {
          bg: bg2,
          icon_name: "icon-no-follower",
          color: "var(--op-text-white)",
          text: t("request"),
        };
      case FriendCardStatus.requested:
        return {
          bg: bg3,
          icon_name: "icon-followed",
          color: "var(--text-3)",
          text: "",
        };
      default:
    }

    return {
      bg: bg1,
      icon_name: "icon-no-follower",
      color: "var(--text-3)",
      text: t("request"),
    };
  };

  return {
    getIconConfig,
  };
};
