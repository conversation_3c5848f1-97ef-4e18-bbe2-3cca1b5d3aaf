// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
// types
import { FriendCardStatus } from "packages/types/user";
// utils
import { useFriendCard } from "./composition";
import { computed } from "vue";

export const RequestBtnAtomicCpnt = (
  props: { status: FriendCardStatus },
  options: { slots: any; expose: any; emit: any },
) => {
  const { emit } = options;
  const { getIconConfig } = useFriendCard();
  const icon_config = computed(() => getIconConfig(props.status));

  const onClick = () => {
    emit("request");
  };

  // custom directive link: https://github.com/vuejs/babel-plugin-jsx?tab=readme-ov-file#custom-directive
  return (
    <div
      v-click-interceptor={[
        // value
        onClick,
        // args
        "",
        // modifiers
        ["need_login", "mute", "sign_privacy", "stop"],
      ]}
      class={[
        `flex-shrink-0 min-w-[44px] min-h-[44px] items-center justify-center relative z-[1] ml-[12px] flex flex-col`,
        props.status === FriendCardStatus.canrequest && "cursor-pointer",
      ]}
    >
      <img
        alt=""
        src={icon_config.value.bg}
        class="w-full h-full object-cover absolute top-0 left-0 -z-[1]"
      />
      <SvgIcon
        name={icon_config.value.icon_name}
        color={icon_config.value.color}
        class="w-[16px] h-[16px]"
      ></SvgIcon>
      {icon_config.value.text && (
        <span style={{ color: icon_config.value.color }}>{icon_config.value.text}</span>
      )}
    </div>
  );
};
