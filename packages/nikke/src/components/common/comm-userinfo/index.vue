<template>
  <div v-if="logined" class="pt-[10px] pb-[8px] px-[16px] relative z-[2] flex items-center">
    <template v-if="user_role_info?.role_name">
      <div class="w-[56px] h-[56px] mr-[12px] flex-shrink-0">
        <RoleAvatar
          :default_avatar="avatar_pic"
          :avatar_id="user_role_info?.icon ?? 0"
          class="box-border"
        ></RoleAvatar>
      </div>

      <div class="flex-1 overflow-hidden">
        <div class="flex items-center">
          <div
            class="text-[color:var(--color-white)] font-bold text-[length:18px] leading-[22px] line-clamp-1 mr-[4px]"
          >
            {{ user_role_info?.role_name }}
          </div>
          <div
            class="px-[3px] h-[16px] font-[DINNextLTProBold] text-[length:12px] leading-[1.5] bg-[var(--brand-1)] flex items-center justify-center max-w-max text-[color:var(--color-white)]"
          >
            Lv.{{ user_basic_info?.player_level }}
          </div>
        </div>

        <div
          class="text-[color:var(--color-3)] text-[length:12px] leading-[16px] flex items-center mt-[2px]"
        >
          <span>{{ server_name }}</span>
          <i class="w-[8px]"></i>
          <span>UID:{{ shown_role_id }}</span>
          <i class="w-[8px]"></i>
          <SvgIcon
            name="icon-copy"
            class="w-[12px] h-[12px] cursor-pointer"
            @click="onCopy"
          ></SvgIcon>
        </div>
      </div>

      <div
        v-if="show_bind_role"
        class="flex items-center justify-center relative w-[20px] h-[20px] rounded-full bg-[var(--color-white-10)] cursor-pointer ml-[12px]"
        @click="() => bindRole()"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-switch2" class="inline-block w-[12px] h-[10px]"></SvgIcon>
      </div>
    </template>

    <template v-else>
      <div class="w-[56px] h-[56px] mr-[12px] flex-shrink-0">
        <Avatar :src="''"></Avatar>
      </div>
      <div class="flex-1 overflow-hidden">
        <div class="text-[length:13px] leading-[16px] text-[color:var(--color-white)]">
          <p>{{ t("please_link_character") }}</p>
          <p>{{ t("view_game_data") }}</p>
        </div>
      </div>
      <Btns
        :text="t('link_channel')"
        type="primary"
        class="ml-[12px]"
        @click="makeSureBindRole"
      ></Btns>
    </template>
  </div>

  <div v-else class="pt-[10px] pb-[8px] px-[16px] relative z-[2] flex items-center">
    <div class="w-[56px] h-[56px] mr-[12px] flex-shrink-0">
      <Avatar :src="''"></Avatar>
    </div>

    <div class="flex-1 overflow-hidden">
      <div class="text-[length:13px] leading-[16px] text-[color:var(--color-white)]">
        <p>{{ t("please_login") }}</p>
        <p>{{ t("view_game_data") }}</p>
      </div>
    </div>

    <Btns
      type="primary"
      :text="t('login')"
      class="ml-[12px]"
      @click="shiftyspad_user_store.liPassLogin"
    ></Btns>
  </div>
</template>

<script lang="ts" setup>
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import Btns from "@/components/common/btns/index.vue";
import { useToast } from "@/components/ui/toast";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
import Avatar from "@/components/common/avatar/index.vue";

// assets
import avatar_pic from "@/shiftyspad/assets/images/appicon.png";

// utils
import { computed } from "vue";
import { useClipboard } from "@vueuse/core";
import { t } from "@/locales";
import { storeToRefs } from "pinia";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useUserStore as useShiftyspadUserStore } from "@/shiftyspad/stores/user";
import { useGameRegion } from "@/composables/role/use-server-info";
import { report } from "packages/utils/tlog";
import { useUser } from "@/store/user";

const user_store = useUser();
const shiftyspad_user_store = useShiftyspadUserStore();
const { user_basic_info, logined, user_role_info, shown_role_id } =
  storeToRefs(shiftyspad_user_store);
const { show_bind_role, bindRole, makeSureBindRole } = useBindRole();
const { show: toast } = useToast();

const { getServerName } = useGameRegion();

const server_name = computed(() =>
  getServerName(`${shiftyspad_user_store.user_role_info?.area_id}`),
);

const onCopy = () => {
  const { copy } = useClipboard({ source: shown_role_id.value });
  copy();
  toast({
    text: t("copy_link_success"),
    type: "success",
  });
};

report.standalonesite_cdkey_page.cm_vshow();

const init = async () => {
  await user_store.waitingGetUserInfoFinish();
  shiftyspad_user_store.initRoleInfo();
  shiftyspad_user_store.initUserBasicInfo();
};

init();
</script>
