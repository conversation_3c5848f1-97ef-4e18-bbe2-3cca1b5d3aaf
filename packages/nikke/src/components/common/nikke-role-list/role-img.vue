<template>
  <img
    crossorigin="anonymous"
    :src="mi_image"
    class="absolute bottom-0 left-0 w-full h-[141px] object-cover object-[center_top]"
    alt=""
  />
</template>

<script lang="ts" setup>
import { useRoleImage } from "@/shiftyspad/composable/role/role-avatar";
import { toRaw } from "vue";

const props = defineProps<{
  resource_id: number;
}>();

const { resource_id } = toRaw(props);

const { mi_image } = useRoleImage(resource_id);
</script>
