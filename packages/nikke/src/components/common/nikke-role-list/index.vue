<template>
  <div>
    <ul class="flex gap-x-[4px]">
      <li
        v-for="item in nikke_list_data"
        :key="item.resource_id"
        class="w-1/5 flex justify-center cursor-pointer"
        @click="toNikkeDetail(item)"
      >
        <div class="relative w-[65px] h-[161px]">
          <div class="absolute z-[2] top-[4px] left-[3px] flex items-center">
            <div
              v-for="i in getStars(item).active_star"
              :key="i"
              class="w-[10px] h-[10px] mr-[1px] last-of-type:mr-0"
            >
              <img :src="starOnImg" alt="" />
            </div>
            <div
              v-for="i in getStars(item).non_active_star"
              :key="i"
              class="w-[10px] h-[10px] mr-[1px] last-of-type:mr-0"
            >
              <img :src="starImg" alt="" />
            </div>
            <p
              v-if="getStars(item).break_num"
              style="background-size: 100%"
              class="ml-[2px] flex justify-center items-center bg-[url('@/shiftyspad/assets/images/icon-evolve.png')] font-[DINNextLTProBold] bg-auto w-[13px] h-[13px] leading-[13px] text-center text-white box-border relative;"
              :class="`${String(getStars(item).break_num).length === 1 ? 'text-[length:7px]' : 'text-[length:4px]'} `"
            >
              <span v-automarquee>{{ getStars(item).break_num }}</span>
            </p>
          </div>
          <div
            class="absolute top-[16px] left-[4px] flex items-end z-[1] font-[DINNextLTProBold] text-[length:9px] font-bold text-white text-stroke"
          >
            Lv.{{ item.level }}
          </div>
          <i
            class="absolute top-[-7px] left-[-7px] w-[79px] h-[175px] bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-[length:100%_100%] bg-no-repeat pointer-events-none"
          ></i>
          <div class="role-clip relative w-full h-full">
            <RoleImg :resource_id="item.resource_id"></RoleImg>
          </div>
          <i
            class="absolute left-0 top-0 w-[65px] h-[161px] bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-[length:100%_100%] bg-no-repeat pointer-events-none"
          ></i>
          <div class="absolute left-[4px] bottom-[2px]">
            <div class="font-normal text-[9px] leading-[11px] text-[color:var(--color-white)]">
              {{ item?.name_localkey ?? "-" }}
            </div>
            <div
              class="!font-[DINNextLTProBold] text-[16px] leading-[1] text-[color:var(--color-white)] mt-[1px]"
            >
              {{ t("nikke_power") }}. {{ (item as any)?.real_character_fight_attribute }}
            </div>
          </div>
        </div>
      </li>
    </ul>
    <Loading v-if="is_loading"></Loading>
    <Nodata v-if="show_nodata && !is_hidden"></Nodata>
    <Nodata
      v-if="is_hidden"
      :text="t('shiftyspad_user_set_module_private', [t('nikke_list_tab_player')])"
    ></Nodata>
  </div>
</template>

<script setup lang="ts">
import Nodata from "@/components/common/nodata.vue";
import Loading from "@/components/common/loading.vue";
import RoleImg from "@/components/common/nikke-role-list/role-img.vue";

import starImg from "@/shiftyspad/assets/images/icon-nikke-star.png";
import starOnImg from "@/shiftyspad/assets/images/icon-nikke-star-gold.png";

import { watch, computed, ref, onUnmounted, toRefs } from "vue";
import { debounce } from "lodash-es";
import { useRouter } from "vue-router";

import { CharacterData, Nikke, NikkeListData, RarityType } from "packages/types/shiftyspad";
import { useCustomSortNikkeList } from "@/shiftyspad/composable/nikke-filter";
import { useUserRoleLevelData } from "@/shiftyspad/composable/character";
import { getCharacterData } from "@/shiftyspad/service/character";
import { ShiftyUserInstance } from "@/shiftyspad/composable/game-data";
import { useStar } from "@/shiftyspad/composable/role/star";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import { RoutesName } from "@/router/routes";
import { t } from "@/locales";

/**
 * NOTICE:
 * 注意, 调用此组件时要确认当前用户且主态信息已获取, 否则主客态判断会错乱;
 *
 * <NikkeRoleList
 *  v-if="self_user_id"
 *  ...
 * />
 *
 * 类似这种方式可以保证不错乱。
 */
const props = withDefaults(
  defineProps<{
    uid: string;
    is_client: boolean;
    shiftys_user: ShiftyUserInstance;

    jumpable?: boolean;
  }>(),
  { jumpable: true },
);

const { shiftys_user } = toRefs(props);
const router = useRouter();
const {
  main_page_sorted_nikke_list,
  filter_player_nikke_list,
  is_nikke_list_loading,
  is_loading_sort,
  is_loading_self_sort,
  is_hidden,
} = useCustomSortNikkeList({
  uid: props.uid,
  is_client: props.is_client,
  user_nikkelist_info: shiftys_user.value.user_nikkelist_info,
});

const is_loading_fight_attribute = ref(true);

const no_nikke = computed(
  () => !filter_player_nikke_list.value.length && !is_nikke_list_loading.value,
);
const is_loading = computed(
  () =>
    !no_nikke.value &&
    (is_loading_sort.value || is_loading_self_sort.value || is_loading_fight_attribute.value),
);
const show_nodata = computed(() => {
  return (!main_page_sorted_nikke_list.value?.length && !is_loading.value) || no_nikke.value;
});
const toNikkeDetail = (params: { resource_id: number }) => {
  if (props.jumpable) {
    router.push({
      name: RoutesName.SHIFTYSPAD_NIKKE_DETAIL,
      query: { [COMMON_QUERY_KEYS.NikkeId]: params.resource_id },
    });
  }
};
const getStars = (nikke: Nikke & CharacterData) => {
  const { active_star, max_stars, break_num } = useStar({
    rarity: nikke.original_rare as RarityType,
    limit_break: nikke.limit_break,
  });
  return {
    break_num,
    active_star,
    non_active_star: max_stars - active_star,
  };
};

const nikke_list_data = ref<(Nikke & NikkeListData & CharacterData)[]>([]);
const abortController = new AbortController();
const cancellable = (promise: Promise<any>) => {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      abortController.signal.addEventListener("abort", () => {
        reject(new Error("Canceled"));
      });
    }),
  ]);
};

onUnmounted(() => abortController.abort());

const sortAndGetPower = debounce(async () => {
  is_loading_fight_attribute.value = true;
  try {
    const nikke_resource_ids = main_page_sorted_nikke_list.value.map((nikke) => nikke.resource_id);
    await cancellable(shiftys_user.value.batchQueryEquipData(nikke_resource_ids));
    const multiple_req = main_page_sorted_nikke_list.value.reverse().map(async (nikke) => {
      if (abortController.signal.aborted) return console.error("aborted.");
      const { resource_id } = nikke;
      const character_data = await getCharacterData(String(resource_id));
      if (abortController.signal.aborted) return console.error("aborted.");

      const { real_character_fight_attribute, cost_pending_state, init_player_equip_data } =
        useUserRoleLevelData({
          shiftys_user,
          character_data,
          options: { use_mock: false },
        });
      await Promise.all([cost_pending_state, init_player_equip_data()]);
      await new Promise((resolve) => window.setTimeout(resolve, 20));
      return Object.assign({}, nikke, character_data, {
        real_character_fight_attribute,
      });
    });
    nikke_list_data.value = await cancellable(Promise.all(multiple_req));
  } finally {
    is_loading_fight_attribute.value = false;
  }
}, 500);

// TODO: fixme
watch(
  () => main_page_sorted_nikke_list.value,
  () => {
    if (main_page_sorted_nikke_list.value?.length && !is_loading_self_sort.value) sortAndGetPower();
  },
  { immediate: true },
);

defineExpose({
  isready: () => Boolean(nikke_list_data.value.length > 0 && !is_loading_fight_attribute.value),
});
</script>

<style lang="scss" scoped>
.role-clip {
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 10px) 100%, 0 100%);
}
</style>
