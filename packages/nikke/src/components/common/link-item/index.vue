<template>
  <div
    :class="[
      `flex pl-[20px]`,
      disabled ? `bg-[color:var(--fill-3)]` : `bg-[color:var(--op-fill-white)]`,
      item.url ? 'cursor-pointer' : 'cursor-default',
    ]"
    @click="openInNewTab(item.url)"
  >
    <div class="flex items-center flex-1 py-[11px]">
      <div
        class="flex items-center justify-center w-[24px] h-[24px] bg-[color:var(--color-1)] rounded-full"
      >
        <SvgIcon :name="item.icon" color="var(--color-6)" class="w-[15px] h-[15px]"></SvgIcon>
      </div>
      <p
        :class="[
          `flex-1 font-normal text-[length:13px] leading-[16px] break-words hyphens-auto break-all`,
          disabled
            ? `text-[color:var(--text-2)] ml-[12px] mr-[20px]`
            : `text-[color:var(--text-3)] mx-[12px]`,
        ]"
      >
        {{ item.url }}
      </p>
    </div>
    <div v-if="!disabled" class="w-[44px] flex-none" @click.stop>
      <div
        class="w-full h-full flex items-center justify-center border-l-[length:0.5px] border-solid border-[color:var(--line-1)] cursor-pointer"
      >
        <SvgIcon
          name="icon-edit"
          color="var(--text-3)"
          class="w-[18px] h-[18px]"
          @click="onEdit"
        ></SvgIcon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChannelLink } from "@/api/user-links";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useWebCredential } from "@/composables/use-webcredential";
import { useRouter } from "vue-router";

const router = useRouter();

const props = withDefaults(
  defineProps<{
    item: ChannelLink;
    disabled?: boolean;
  }>(),
  {},
);
const onEdit = () => {
  router.push({
    path: "/user/edit-link",
    query: {
      link_name: props.item.channel_name,
      link_url: encodeURIComponent(props.item.url),
    },
  });
};

const { openUrlWithAuth } = useWebCredential();

const openInNewTab = (url: string) => {
  openUrlWithAuth(url, "_blank");
};
</script>

<style lang="scss" scoped></style>
