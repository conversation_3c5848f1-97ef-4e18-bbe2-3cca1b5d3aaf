// cpnts
import { useDialog } from "@/components/ui/dialog/index.ts";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { checkUrlIsSameOrigin, ensureUrlKeepProtocol } from "packages/utils/tools";
import logger from "packages/utils/logger";
import { t } from "@/locales";
import { useJumpUrlWhiteListCheck } from "packages/utils/jump-url-white-list";
// import { openUrlWithWhiteListQuery } from "@/utils/tools";
import { useWebCredential } from "@/composables/use-webcredential";
import { report } from "packages/utils/tlog";

const debug = logger("[editor:utils]");
const { show: showDialog } = useDialog();
const { check: jumpUrlWhiteListCheck } = useJumpUrlWhiteListCheck();
const { openUrlWithAuth } = useWebCredential();

export const onClickHyperLinkHandler = async (e: Event) => {
  e.preventDefault();

  const target = e.target as HTMLElement;
  const href = ensureUrlKeepProtocol(target.getAttribute("href") || "");
  const bool = checkUrlIsSameOrigin(href);

  report.standalonesite_detail_content_link.cm_click({ dst_url: href });

  debug.log("[onClickHyperLinkHandler][checkUrlIsSameOrigin] bool", bool);

  if (bool) {
    openUrlWithAuth(href);
    return;
  }

  const is_jump_url_in_white_list = await jumpUrlWhiteListCheck(href);
  debug.log("[onClickHyperLinkHandler][jumpUrlWhiteListCheck] bool", is_jump_url_in_white_list);

  if (is_jump_url_in_white_list) {
    openUrlWithAuth(href);
    return;
  }

  showDialog({
    title: t("warning"),
    content: t("jump_outside_warning_tips"),
    confirm_text: t("confirm"),
    cancel_text: t("cancel"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        report.standalonesite_detail_content_link_confirm.cm_click({ dst_url: href });
        openUrlWithAuth(href);
        close();
        return;
      }
      close();
    },
  });
};
