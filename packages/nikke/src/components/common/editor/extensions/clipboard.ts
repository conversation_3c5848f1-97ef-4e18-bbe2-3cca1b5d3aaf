import pLimit from "p-limit";
import { dataURLToBlob } from "packages/utils/tools";
import Quill from "quill";
import { ImageUploadErrorToastType, useImageUpload } from "@/composables/use-image";
import {
  getDocument,
  isBase64Image,
  getImageExtensionByBase64,
  replaceAllImagesWithPlaceholder,
} from "packages/utils/dom";

const { onUpload, onCheckSizeLimit, onCheckFileType, onErrorToast } = useImageUpload();

const Clipboard: any = Quill.import("modules/clipboard");

/**
 * @description 替换 HTML 字符串中所有 src 为 Base64 编码的 <img> 标签的 src 为指定的 URL，并保留文件后缀。
 * @param html - 原始的 HTML 字符串。
 * @returns 修改后的 HTML 字符串。
 */
async function replaceBase64Images(html: string): Promise<string> {
  const doc = getDocument(html);
  const images = doc.querySelectorAll("img");
  const limit = pLimit(1);

  try {
    await Promise.all(
      Array.from(images).map((img) =>
        limit(async () => {
          const src = img.getAttribute("src");
          if (src && isBase64Image(src)) {
            const extension = getImageExtensionByBase64(src) || "";
            if (!onCheckFileType(`image/${extension}`)) {
              img.setAttribute("src", "");
              return onErrorToast(ImageUploadErrorToastType.file_type_not_support);
            }
            const blob = dataURLToBlob(src);
            const file = new File([blob], `${Math.random()}.${extension}`, {
              type: `image/${extension}`,
            });
            const { size } = file;
            if (!onCheckSizeLimit(size)) {
              img.setAttribute("src", "");
              return onErrorToast(ImageUploadErrorToastType.exceed_size_limit);
            }
            const url = (await onUpload(file)) || "";
            img.setAttribute("src", url);
          }
        }),
      ),
    );
  } catch (error) {
    console.error("[replaceBase64Images] catch an error: ", error);
  }

  // 删除所有 src 属性为空的 <img> 标签
  const empty_src_images = doc.querySelectorAll("img[src='']");
  empty_src_images.forEach((img) => img.remove());

  return doc.body.innerHTML;
}

/**
 * @link packages/quill/src/modules/clipboard.ts
 * @description [ExtendClipboard description]
 * @param quill
 * @param options
 */
export class ClipboardExtensioin extends Clipboard {
  constructor(quill: any, options: any) {
    super(quill, options);
  }

  getUploading() {
    throw new Error("[getUploading] Method not implemented.");
  }

  toggleUploading() {
    throw new Error("[toggleUploading] Method not implemented.");
  }

  async onCapturePaste(e: ClipboardEvent) {
    if (e.defaultPrevented || !this.quill.isEnabled() || this.getUploading()) return;
    e.preventDefault();

    const range = this.quill.getSelection(true);
    if (range == null) return;

    let html = e.clipboardData?.getData("text/html") || "";
    let text = e.clipboardData?.getData("text/plain");

    if (!html && !text) {
      const urlList = e.clipboardData?.getData("text/uri-list");
      if (urlList) {
        text = this.normalizeURIList(urlList);
      }
    }

    const files = Array.from(e.clipboardData?.files || []);
    if (!html && files.length > 0) {
      this.quill.uploader.upload(range, files);
      return;
    }

    if (html && files.length > 0) {
      const doc = new DOMParser().parseFromString(html, "text/html");
      if (doc.body.childElementCount === 1 && doc.body.firstElementChild?.tagName === "IMG") {
        this.quill.uploader.upload(range, files);
        return;
      }
    }

    this.toggleUploading();

    html = this.disabled_pasted_image
      ? replaceAllImagesWithPlaceholder(html)
      : await replaceBase64Images(html);

    this.toggleUploading();

    this.onPaste(range, { html, text });
  }
}

export const extendClipboardExtensionPrototype = (options: Record<string, any>) => {
  Object.assign(ClipboardExtensioin.prototype, options);
};
