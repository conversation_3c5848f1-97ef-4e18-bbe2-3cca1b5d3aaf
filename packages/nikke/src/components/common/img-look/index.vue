<template>
  <DialogRoot :open="visible">
    <DialogPortal>
      <DialogOverlay
        class="fixed inset-0 z-50 bg-[var(--color-black-55)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
        @click="$emit('maskClose')"
      />
      <DialogContent
        class="w-full cursor-pointer fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
      >
        <div
          ref="wrap_ref"
          :class="[
            ` bg-[var(--color-black)] overflow-hidden flex items-center justify-center`,
            direction === 'horizontal' ? 'w-full h-[206px]' : 'mx-auto w-[85vw] h-[72vh]',
          ]"
        >
          <Swiper
            :key="swiperKey"
            class="!w-full"
            :loop="false"
            :index="0"
            :autoplay="false"
            :duration="3000"
          >
            <div class="w-full h-full">
              <div
                class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                :class="[direction === 'horizontal' ? '' : 'rotate-[90deg]']"
                :style="imgSize"
              >
                <img :src="src" alt="" class="block w-full h-full object-contain" />
              </div>
            </div>
          </Swiper>
        </div>

        <div
          class="mt-[12px] mx-[27px] flex items-center justify-center h-[48px] bg-[var(--fill-1-80)] rounded-[4px] cursor-pointer"
        >
          <div class="w-[20px] h-[20px] relative">
            <i class="absolute-center"></i>
            <SvgIcon name="icon-goback" color="var(--text-4)"></SvgIcon>
          </div>
          <div
            class="w-[88px] text-center text-[color:var(--text-4)] text-[length:13px] leading-[16px]"
          >
            <!-- 如果当前的显示页数=总页数，颜色为 #D0D0D0，否则为 #959596 -->
            1<span class="text-[color:var(--text-3)]">/2</span>
          </div>
          <div class="w-[20px] h-[20px] relative rotate-180">
            <i class="absolute-center"></i>
            <SvgIcon name="icon-goback" color="var(--text-4)"></SvgIcon>
          </div>

          <div class="w-[1px] h-[24px] bg-[var(--fill-6)] mx-[20px]"></div>

          <div
            v-if="mainBtn === 'delete'"
            class="w-[20px] h-[20px] relative mx-[12px]"
            @click="handle(true), $emit('close')"
          >
            <i class="absolute-center"></i>
            <SvgIcon name="icon-delete" color="var(--text-4)"></SvgIcon>
          </div>

          <div
            v-else
            class="w-[20px] h-[20px] relative mx-[12px]"
            @click="handle(true), $emit('close')"
          >
            <i class="absolute-center"></i>
            <SvgIcon name="icon-download" color="var(--text-4)"></SvgIcon>
          </div>

          <div
            class="w-[20px] h-[20px] relative mx-[12px]"
            @click="direction = direction === 'horizontal' ? 'vertical' : 'horizontal'"
          >
            <i class="absolute-center"></i>
            <SvgIcon name="icon-rotate2" color="var(--text-4)"></SvgIcon>
          </div>

          <div class="w-[18px] h-[18px] relative mx-[12px]" @click="$emit('close')">
            <i class="absolute-center"></i>
            <SvgIcon name="icon-close2" color="var(--text-4)"></SvgIcon>
          </div>
        </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup lang="ts">
import { DialogRoot, DialogContent, DialogOverlay, DialogPortal } from "radix-vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { Swiper } from "@/components/common/swiper/index.ts";
import { useToast } from "@/components/ui/toast";

import { ref, watch, nextTick, computed } from "vue";

const props = withDefaults(
  defineProps<{
    visible?: boolean;
    src?: string;
    mainBtn?: "download" | "delete";
  }>(),
  {
    mainBtn: "download",
  },
);

defineEmits(["maskClose", "close"]);

const direction = ref<"vertical" | "horizontal">("horizontal");

// toast
const { show } = useToast();
const is_open = ref(false);
let hide_timer: ReturnType<typeof setTimeout>;

const handle = (v: boolean) => {
  is_open.value = v;
  show({
    text: "Delete successfully",
    type: "success",
  });
};

const wrap_ref = ref<HTMLElement>();
const imgSize = ref({ width: "100%", height: "100%" });
watch(
  direction,
  (newVal) => {
    nextTick(() => {
      if (newVal === "vertical") {
        const width = wrap_ref.value?.clientWidth;
        const height = wrap_ref.value?.clientHeight;
        if (width && height) {
          imgSize.value = { width: `${height}px`, height: `${width}px` };
        } else {
          imgSize.value = { width: "100%", height: "100%" };
        }
      } else {
        imgSize.value = { width: "100%", height: "100%" };
      }
    });
  },
  { immediate: true },
);
const swiperKey = computed(() => {
  return `${direction.value} ${JSON.stringify(imgSize.value)}`;
});
</script>
