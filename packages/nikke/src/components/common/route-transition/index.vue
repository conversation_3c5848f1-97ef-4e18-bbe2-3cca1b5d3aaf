<template>
  <div class="route-transition-container">
    <Transition
      :name="transitionName"
      mode="out-in"
      @before-enter="onBeforeEnter"
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @before-leave="onBeforeLeave"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <slot />
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouteTransition } from "@/composables/use-route-transition";
import type { RouteTransitionProps } from "./types";

const props = withDefaults(defineProps<RouteTransitionProps>(), {
  duration: 300,
  easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  enabled: true,
});

const { direction, setDirection } = useRouteTransition();

// 过渡名称
const transitionName = computed(() => {
  if (!props.enabled) return "";

  const dir = direction.value;
  if (dir === "none") return "";

  return dir === "forward" ? "slide-forward" : "slide-back";
});

// 过渡事件处理
const onBeforeEnter = (el: Element) => {
  const element = el as HTMLElement;
  element.style.setProperty("--transition-duration", `${props.duration}ms`);
  element.style.setProperty("--transition-easing", props.easing);
  console.log("Route transition: onBeforeEnter", direction.value);
};

const onEnter = (_el: Element, done: () => void) => {
  setTimeout(done, props.duration);
};

const onAfterEnter = () => {
  // 动画完成后重置方向
  setTimeout(() => {
    setDirection("none");
  }, 50);
};

const onBeforeLeave = (el: Element) => {
  const element = el as HTMLElement;
  element.style.setProperty("--transition-duration", `${props.duration}ms`);
  element.style.setProperty("--transition-easing", props.easing);
};

const onLeave = (_el: Element, done: () => void) => {
  setTimeout(done, props.duration);
};

const onAfterLeave = () => {
  // 清理完成
};
</script>

<style scoped>
.route-transition-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 前进动画 - 新页面从右侧滑入 */
.slide-forward-enter-active,
.slide-forward-leave-active {
  transition: transform var(--transition-duration, 300ms)
    var(--transition-easing, cubic-bezier(0.25, 0.46, 0.45, 0.94));
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: transform;
  backface-visibility: hidden;
}

.slide-forward-enter-from {
  transform: translateX(100%);
}

.slide-forward-enter-to {
  transform: translateX(0);
}

.slide-forward-leave-from {
  transform: translateX(0);
}

.slide-forward-leave-to {
  transform: translateX(-100%);
}

/* 后退动画 - 当前页面向右滑出 */
.slide-back-enter-active,
.slide-back-leave-active {
  transition: transform var(--transition-duration, 300ms)
    var(--transition-easing, cubic-bezier(0.25, 0.46, 0.45, 0.94));
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: transform;
  backface-visibility: hidden;
}

.slide-back-enter-from {
  transform: translateX(-100%);
}

.slide-back-enter-to {
  transform: translateX(0);
}

.slide-back-leave-from {
  transform: translateX(0);
}

.slide-back-leave-to {
  transform: translateX(100%);
}

/* 确保过渡期间容器有正确的层叠上下文 */
.route-transition-container > * {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}
</style>
