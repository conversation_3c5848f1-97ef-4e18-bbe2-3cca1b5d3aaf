<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import Loading from "@/components/common/loading.vue";

// utils
import { t } from "@/locales";
import { BaseDialog } from "@/utils/dialog";
import { ref } from "vue";
import { useVersionCDNConfigs } from "packages/utils/cdn";
import dayjs from "dayjs";

interface Props extends BaseDialog {
  version: string;
}

const props = defineProps<Props>();
const emits = defineEmits(["click"]);

const { loadCDNConfigs, getCDNConfigs } = useVersionCDNConfigs();

const local_version = ref(props.version || "default");
const versions = ref();
const loading = ref();

const onItemClick = (item: { text: string; value: string }) => {
  if (local_version.value === item.value) return;
  props.finish(item as any);
};

const onGetVersoins = async () => {
  loading.value = true;
  await loadCDNConfigs(import.meta.env.MODE, "version");
  const { values } = getCDNConfigs();
  versions.value = values.map((item) => {
    return {
      ...item,
      version: window.STANDALONE_SITE_VERSION,
      build_time: window.APP_BUILD_TIME,
    };
  });
  loading.value = false;
};

onGetVersoins();
</script>

<template>
  <Dialog :show="visible" :title="t('Version Selection')" @close="props.cancel">
    <div class="pb-[16px] pt-[5px] max-h-[40vh] min-h-[25vh] overflow-y-auto">
      <template v-if="loading">
        <Loading></Loading>
      </template>

      <template v-else>
        <div
          v-for="(item, index) in versions"
          :key="index"
          :class="[
            `flex items-center px-[10px] py-[8px] border-[1px] cursor-pointer mb-[6px] last-of-type:mb-0 rounded-md text-[length:12px] leading-[14px] capitalize justify-between`,
            local_version === item.value
              ? 'bg-[var(--brand-1)] border-[var(--brand-1)] text-white'
              : 'bg-[var(--fill-0)] border-[var(--line-1)] text-[color:var(--text-1)]',
          ]"
          @click="onItemClick(item)"
        >
          <div>{{ item.label }}</div>
          <div v-if="local_version === item.value">
            <span>(</span>
            <span>
              t{{ dayjs.unix(item.build_time / 1000).format("YYYY-MM-DD HH:mm") }} - v{{
                item.version
              }}
            </span>
            <span>)</span>
          </div>
        </div>
      </template>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped></style>
