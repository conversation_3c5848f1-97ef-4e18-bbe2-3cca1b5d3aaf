<script setup lang="ts">
// cpnt
import SvgIcon from "@/components/common/svg-icon.vue";
// utils
import { computed, ref, useTemplateRef } from "vue";
import { showMultipleVersion } from "./pop/index";
import { addParamToCurrentUrl, urlSearchObjectify } from "packages/utils/qs";
import { useDraggable } from "@vueuse/core";

const url_search_object = urlSearchObjectify();

const is_dragging = ref();
const version = ref(url_search_object.version || "");
const el = useTemplateRef<HTMLElement>("el");
const { style } = useDraggable(el, {
  preventDefault: true,
  initialValue: { x: 20 } as any,
  onMove() {
    is_dragging.value = true;
  },
  onEnd() {
    setTimeout(() => {
      is_dragging.value = false;
    }, 100);
  },
});

const display_version = computed(() =>
  version.value === "default" ? "" : version.value.slice(0, 4),
);

const onSelectVersion = () => {
  if (is_dragging.value) {
    return;
  }
  showMultipleVersion({
    version: version.value,
    onFinish(data: any) {
      version.value = data.value;

      addParamToCurrentUrl({
        version: version.value,
      });

      setTimeout(() => {
        window.location.reload();
      }, 1000);
    },
  });
};
</script>

<template>
  <div
    ref="el"
    class="fixed bg-[var(--brand-type-1)] text-white w-[46px] h-[46px] items-center flex justify-center rounded-full bottom-[100px] right-[10px] z-50 capitalize text-[14px] font-bold cursor-move"
    :style="style"
    @click="onSelectVersion"
  >
    <SvgIcon v-if="!display_version" name="icon-version-switch" class="w-3/5 h-3/5" color="white" />
    <span v-else>{{ display_version }}</span>
  </div>
</template>

<style lang="scss" scoped></style>
