<template>
  <div class="flex items-center mx-[12px] h-[72px] bg-[var(--op-fill-white)] px-[12px] box-border">
    <div class="w-[48px] h-[48px] p-[2px] flex-shrink-0" @click="openUserCenter">
      <!-- <img :src="data.user_info.avatar" class="object-cover w-full" /> -->
      <Avatar
        :src="data.avatar"
        :auth_type="data.auth_type"
        :frame="data.avatar_pendant"
        class="object-cover w-full"
      />
    </div>
    <div
      class="ml-[8px] flex flex-col items-start flex-1 cursor-pointer overflow-hidden"
      @click="openUserCenter"
    >
      <span
        class="text-[length:14px] leading-[18px] font-bold text-[color:var(--text-1)] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ data.username }}
      </span>
      <span
        v-if="black_time"
        class="text-[length:11px] leading-[13px] font-normal text-[color:var(--color-3)] mt-[4px] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ t("blocked_at", [black_time]) }}
      </span>
    </div>
    <div class="ml-[10px] flex-none">
      <Btns type="primary" :text="t('unblock')" class="uppercase" @click="onUnblock"> </Btns>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Btns from "@/components/common/btns/index.vue";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import Avatar from "@/components/common/avatar/index.vue";
import { UserInfo } from "packages/types/user";
import { useUserBlock } from "@/composables/use-user-block";
import dayjs from "dayjs";
import { computed } from "vue";

const { t } = useI18n();

const props = defineProps<{
  data: UserInfo;
  blacking_on: string;
}>();

const black_time = computed(() => {
  return props.blacking_on !== "0" ? dayjs.unix(+props.blacking_on).format("YYYY-MM-DD HH:mm") : "";
});

const emit = defineEmits(["unblock"]);

const router = useRouter();

const { handleUnblock } = useUserBlock();

const onUnblock = async () => {
  const changed = await handleUnblock(props.data.intl_openid);
  if (changed) {
    emit("unblock");
  }
};

const openUserCenter = () => {
  router.push({
    name: RoutesName.USER,
    query: { openid: props.data.intl_openid },
  });
};
</script>

<style lang="scss" scoped></style>
