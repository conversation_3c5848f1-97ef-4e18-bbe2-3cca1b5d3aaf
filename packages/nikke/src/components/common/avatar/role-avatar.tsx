import { computed, defineComponent, ref, toRefs } from "vue";
import { useQuery } from "@tanstack/vue-query";
import { CommonImage } from "@/components/common/image";
import { getAvatar } from "@/shiftyspad/service/character";
import Spinner from "@/shiftyspad/components/common/spinner.vue";

export const RoleAvatar = defineComponent({
  props: {
    avatar_id: Number,
    scene: {
      type: String,
      required: false,
    },
    default_avatar: {
      type: String,
      required: false,
    },
    loading_size: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    const { avatar_id, scene, default_avatar, loading_size } = toRefs(props);
    const { data: user_avatar, isLoading: is_avatar_loading } = useQuery({
      queryKey: [avatar_id.value],
      enabled: computed(() => Boolean(avatar_id.value)),
      queryFn: async (): Promise<string> => {
        return (await getAvatar(avatar_id.value ?? 0)) ?? "";
      },
    });
    const is_loaed = ref(false);
    const img_src = computed(() => user_avatar.value || default_avatar?.value);

    return () => {
      if (is_avatar_loading.value) {
        return (
          <div
            v-bind="$attrs"
            class={
              "relative w-full h-full rounded-full bg-[var(--fill-2)] border-[1px] border-[color:var(--op-line-white)]"
            }
          >
            <Spinner size={loading_size.value ?? "5px"} />
          </div>
        );
      }

      switch (scene?.value) {
        case "section": {
          return (
            <CommonImage
              v-show={is_loaed.value}
              src={img_src.value ?? ""}
              class="section-avatar"
              onLoad={() => (is_loaed.value = true)}
            />
          );
        }
        default:
          return (
            <>
              <div
                key={avatar_id.value}
                class={
                  "relative w-full h-full rounded-full bg-[var(--fill-2)] border-[1px] border-[color:var(--op-line-white)]"
                }
              >
                <CommonImage
                  src={img_src.value ?? ""}
                  onError={() => {
                    user_avatar.value = "";
                  }}
                  class="rounded-full w-full h-full overflow-hidden"
                  alt=""
                />
              </div>
            </>
          );
      }
    };
  },
});
