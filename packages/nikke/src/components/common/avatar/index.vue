<template>
  <div
    :class="[
      `relative w-full h-full rounded-full bg-[var(--fill-2)] border-[1px] border-[color:var(--op-line-white)]`,
    ]"
  >
    <CommonImage :src="avatar" class="rounded-full w-full h-full overflow-hidden" alt="" />
    <CommonImage
      v-if="auth_type_img"
      :src="auth_type_img"
      class="absolute bottom-0 -right-[6%] w-[32%] h-[32%] z-[5]"
    />
    <div
      v-if="frame"
      :class="[
        `absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(125%)] h-[calc(125%)] pointer-events-none z-[1]`,
        frameclass,
      ]"
    >
      <CommonImage :src="frame" class="w-full h-full"></CommonImage>
    </div>
  </div>
</template>

<script setup lang="ts">
import default_avatar from "@/assets/imgs/common/avatar-default.png";
import { computed } from "vue";
import { CommonImage } from "../image";
import auth_type_1 from "@/assets/imgs/user-center/auth-type-01.png";
import auth_type_2 from "@/assets/imgs/user-center/auth-type-02.png";
import auth_type_3 from "@/assets/imgs/user-center/auth-type-03.png";
import auth_type_4 from "@/assets/imgs/user-center/auth-type-04.png";

// import test_frame from "@/assets/imgs/test/test-frame1.png";

const props = withDefaults(
  defineProps<{
    src: string;
    /** 1 官方认证，2创作认证, 3 机构认证, 4 玩家管理员 */
    auth_type?: number | undefined;
    // 头像框
    frame?: string;
    // 头像框class
    frameclass?: string;
  }>(),
  {},
);

const avatar = computed(() => props.src || default_avatar);

const auth_type_img = computed(() => {
  if (!props.auth_type) return undefined;
  return (
    {
      1: auth_type_1,
      2: auth_type_2,
      3: auth_type_3,
      4: auth_type_4,
    }[props.auth_type] || undefined
  );
});
</script>
