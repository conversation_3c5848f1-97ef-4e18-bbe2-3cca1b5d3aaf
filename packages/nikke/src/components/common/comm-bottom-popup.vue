<template>
  <PopBox :visible="show" :nobg="nobg" @close="$emit('close')">
    <div
      v-motion
      class="absolute bottom-0 w-full shadow-[0px_1px_10px_var(--op-shadow-black-5)] max-w-[var(--max-pc-w)] max-h-[90vh] box-border bg-[color:var(--op-fill-white)] overflow-hidden rounded-t-[8px] overflow-y-auto"
      :initial="{ y: 100 }"
      :enter="{ y: 0 }"
      @click.stop
    >
      <div
        v-if="line"
        class="absolute top-[8px] left-1/2 z-[20] -translate-x-1/2 w-[60px] h-[4px] rounded-[2px] bg-[var(--fill-2)]"
      ></div>
      <slot name="header"></slot>
      <slot></slot>
    </div>
  </PopBox>
</template>

<script setup lang="ts">
import PopBox from "@/components/common/popbox.vue";
import { watch } from "vue";
import { useRoute } from "vue-router";

const props = defineProps<{
  show?: boolean;
  line?: boolean;
  nobg?: boolean;
}>();

const emits = defineEmits(["close"]);

const route = useRoute();

watch(
  () => route.fullPath,
  () => {
    if (props.show) {
      emits("close");
    }
  },
);
</script>
