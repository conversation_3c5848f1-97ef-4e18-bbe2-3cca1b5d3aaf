<script setup lang="ts">
// configs
import { ENV_DEVELOPMENT, ENV_TEST } from "packages/configs/env";
// utils
import { defineAsyncComponent, ref } from "vue";

const ServiceWorkerRefreshPrompt = defineAsyncComponent(
  () => import("@/components/common/service-workder-refresh-prompt.vue"),
);
const MultipleVersion = defineAsyncComponent(
  () => import("@/components/common/multiple-version/index.vue"),
);

const is_service_worker_enable = ref([ENV_DEVELOPMENT, ENV_TEST].includes(import.meta.env.MODE));

console.log(`Is Service Worker Enable: `, is_service_worker_enable.value);
</script>

<template>
  <template v-if="is_service_worker_enable">
    <component :is="ServiceWorkerRefreshPrompt" />
    <component :is="MultipleVersion" />
  </template>
</template>

<style lang="scss" scoped></style>
