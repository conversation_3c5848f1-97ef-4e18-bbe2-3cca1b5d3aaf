<template>
  <div class="relative z-[10]">
    <SelectHead :text="list[selectIndex]?.text" @click="show = !show"></SelectHead>

    <SelectOption
      v-show="show"
      :list="list"
      :active-id="selectIndex"
      class="top-[22px] right-0 w-[335px]"
      @change="change"
    ></SelectOption>
  </div>
</template>

<script setup lang="ts">
import SelectHead from "@/components/common/select-head/index.vue";
import SelectOption from "@/components/common/select-option/index.vue";

import { ref } from "vue";

const show = ref(false);

const selectIndex = ref(0);

const list = [
  {
    text: "Global",
  },
  {
    text: "HK/MC/TW",
  },
];

const change = (index: number) => {
  selectIndex.value = index;
};
</script>
