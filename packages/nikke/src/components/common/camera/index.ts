// cpnts
import Camera from "./index.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";

export const useCamera = () => {
  let dialog: any;
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return (dialog = showDialog(
      Camera,
      Object.assign(options, {
        onClose: () => {
          dialog.unmount();
        },
      }),
    ));
  };
  return {
    show,
  };
};
