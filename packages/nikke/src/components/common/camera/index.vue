<script setup lang="ts">
/**
 * reference:
 * @link https://github.com/yusuf<PERSON><PERSON><PERSON>/javascript-camera-capture/tree/master
 * @link https://developer.mozilla.org/zh-CN/docs/Web/API/MediaDevices/getUserMedia
 */

// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import Btns from "@/components/common/btns/index.vue";

// utils
import { getUploadClientInstance } from "@/api/axios";
import { UploadFileType } from "packages/types/common";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { dataURLToBlob } from "packages/utils/tools";
import { isMobileDevice } from "packages/utils/tools";

const emits = defineEmits<{
  (e: "confirm", url: string): void;
  (e: "close"): void;
}>();

enum Step {
  opening = "opening",
  record = "record",
  deny = "deny",
  capture = "capture",
  preview = "preview",
}

const { t } = useI18n();

const getId = () => Math.ceil(Math.random() * 1000000);
const getElementById = (id: string) => document.getElementById(id);

const video_id = `video-${getId()}`;
const canvas_id = `canvas-${getId()}`;
const img_id = `img-${getId()}`;
const step = ref(Step.opening);
const img_data = ref();
const uploading = ref(false);
const stream = ref();

const setStep = (new_step: Step) => (step.value = new_step);

const onLoadVideo = async function () {
  const video = getElementById(video_id) as HTMLVideoElement;
  const canvas = getElementById(canvas_id) as HTMLCanvasElement;

  video.setAttribute("width", `${video.videoWidth}`);
  video.setAttribute("height", `${video.videoHeight}`);
  canvas.setAttribute("width", `${video.videoWidth}`);
  canvas.setAttribute("height", `${video.videoHeight}`);

  setStep(Step.record);
  video.play();
};

const onOpenCamera = async () => {
  try {
    const is_mobile = isMobileDevice();
    const video = getElementById(video_id) as HTMLVideoElement;

    const constraints = {
      video: {
        facingMode: is_mobile ? { exact: "environment" } : "user",
      },
    };

    stream.value = await navigator.mediaDevices.getUserMedia(constraints);
    video.srcObject = stream.value;

    video.addEventListener("loadedmetadata", onLoadVideo);
  } catch (error) {
    setStep(Step.deny);
    console.error("Error accessing camera: ", error);
  }
};

const onCapture = async () => {
  setStep(Step.capture);

  const video = getElementById(video_id) as HTMLVideoElement;
  const img = getElementById(img_id) as HTMLVideoElement;

  // Ensure the video element is playing
  if (!video.srcObject) {
    throw new Error("Video stream is not available.");
  }

  const canvas = getElementById(canvas_id) as HTMLCanvasElement;
  const context = canvas.getContext("2d")!;
  context.drawImage(video, 0, 0, video.width, video.height);
  // Convert drawn image to data URL and set it as the src of img element
  img_data.value = canvas.toDataURL("image/png");

  img.src = img_data.value;
};

const onStopCamera = async () => {
  if (stream.value) {
    stream.value.getTracks().forEach((track: { stop: () => any }) => track.stop());
    stream.value = null;

    const video = getElementById(video_id) as HTMLVideoElement;
    video.srcObject = null;
  }
};

const onConfirm = async () => {
  if (uploading.value) {
    return;
  }

  try {
    uploading.value = true;
    const upload_client = await getUploadClientInstance();
    await upload_client.getToken();

    const img = getElementById(img_id) as HTMLVideoElement;
    const blob = dataURLToBlob(img.src);
    const file = new File([blob], `${img_id}.png`, { type: "image/png" });
    const url = await upload_client.uploadFile({
      file,
      type: UploadFileType.image,
    });

    emits("confirm", url || "");
  } catch (error) {
    console.error("upload file catch error", error);
  } finally {
    uploading.value = false;
    onStopCamera();
  }
};

const onPreview = () => {
  setStep(step.value === Step.preview ? Step.capture : Step.preview);
};

const onDelete = () => {
  setStep(Step.record);
  img_data.value = "";
};

const onClose = () => {
  onStopCamera();
  emits("close");
};

onMounted(onOpenCamera);
</script>

<template>
  <Teleport to="body">
    <div class="fixed inset-0 z-[10000]">
      <div class="fixed h-screen w-screen bg-[color:var(--color-black-80)] inset-0"></div>
      <SvgIcon
        name="icon-close2"
        class="fixed top-[20px] right-[20px] w-[20px] h-[20px] z-10"
        @click="onClose"
      ></SvgIcon>

      <div
        class="relative h-screen w-screen text-[color:var(--color-white)] flex items-center justify-center flex-col"
      >
        <div class="w-[320px] h-[320px] relative flex justify-center items-center">
          <div class="absolute inset-0">
            <div
              class="absolute top-0 left-0 w-10 h-10 border-l-4 border-t-4 border-[var(--op-line-white)] transform -translate-x-2.5 -translate-y-2.5"
            ></div>
            <div
              class="absolute top-0 right-0 w-10 h-10 border-r-4 border-t-4 border-[var(--op-line-white)] transform translate-x-2.5 -translate-y-2.5"
            ></div>
            <div
              class="absolute bottom-0 left-0 w-10 h-10 border-l-4 border-b-4 border-[var(--op-line-white)] transform -translate-x-2.5 translate-y-2.5"
            ></div>
            <div
              class="absolute bottom-0 right-0 w-10 h-10 border-r-4 border-b-4 border-[var(--op-line-white)] transform translate-x-2.5 translate-y-2.5"
            ></div>
          </div>

          <template v-if="[Step.deny].includes(step)">
            <SvgIcon class="w-[160px] h-[160px]" name="icon-reporting"></SvgIcon>
          </template>

          <template v-else>
            <video
              v-show="[Step.record, Step.capture].includes(step)"
              :id="video_id"
              :class="[`w-full`]"
            ></video>

            <canvas :id="canvas_id" class="w-full absolute hidden inset-0"></canvas>
            <img v-show="[Step.preview].includes(step)" :id="img_id" :class="[`w-full`]" />
          </template>
        </div>

        <div class="mt-[20px] flex items-center relative w-[320px] justify-center">
          <img
            v-show="[Step.record, Step.capture].includes(step) && img_data"
            :id="img_id"
            :src="img_data"
            class="absolute left-0 w-[80px] object-contain cursor-pointer"
            @click="onPreview"
          />

          <div class="w-[80px] h-[80px] inline-flex items-center justify-center">
            <SvgIcon
              v-if="[Step.preview].includes(step)"
              name="icon-delete"
              class="w-[40px] inline-flex cursor-pointer"
              @click="onDelete"
            ></SvgIcon>

            <button
              v-else-if="[Step.record, Step.capture].includes(step)"
              class="w-full h-full border-[4px] rounded-full shadow-lg flex items-center justify-center transition-all transform hover:scale-105 active:scale-95"
              @click="onCapture"
            >
              <div class="bg-[var(--op-fill-white)] rounded-full w-[60px] h-[60px] shadow-md"></div>
            </button>
          </div>
        </div>

        <div class="w-[320px] flex justify-center mt-[40px]">
          <Btns
            v-if="img_data"
            :text="t('confirm')"
            :type="uploading ? 'disabled' : 'primary'"
            size="m"
            @click="onConfirm"
          ></Btns>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style lang="scss" scoped></style>
