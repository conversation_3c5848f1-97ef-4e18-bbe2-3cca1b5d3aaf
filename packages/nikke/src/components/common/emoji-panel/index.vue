<script lang="ts" setup>
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { EmojisType } from "packages/types/emojis";

// utils
import { useEmojis } from "@/store/emojis.ts";
import { storeToRefs } from "pinia";
import { t } from "@/locales";
import { EMOJI_IMAGE_SYMBOL } from "@/configs/const";

defineProps<{
  panel_style?: any;
}>();

const emits = defineEmits(["change", "delete"]);

const emojis_store = useEmojis();
const {
  groups,
  group_id,
  current_group_list,
  // emojis_select_index,
  // history_emojis_select_index,
} = storeToRefs(emojis_store);

emojis_store.onLoadEmojis();

const onSelectEmoji = (item: { emoji: string; index: number }, type: EmojisType) => {
  emojis_store.onSelectEmoji(item, type);
  // 加上 imgtype=emoji 是为了区分普通的图片地址，这样在卡片展示或者其他地方可以做判断
  emits("change", `${item.emoji}?${EMOJI_IMAGE_SYMBOL}`);
};
</script>

<template>
  <div class="relative bg-[color:var(--op-fill-white)]">
    <div class="flex items-center gap-[12px] pt-[6px] pb-[8px] px-[12px] overflow-x-auto">
      <div
        v-for="(item, index) in groups"
        :key="index"
        class="relative flex items-center justify-center flex-shrink-0 w-[36px] h-[36px] first:bg-[color:var(--fill-3)] first:rounded-[50%] cursor-pointer"
        @click="emojis_store.onChangeGroupId(item.id)"
      >
        <template v-if="item.icon_name">
          <SvgIcon
            :name="item.icon_name"
            color="var(--text-1)"
            class="w-[20px] h-[20px] bg-[color:var(--fill-3)] rounded-[50%]"
          ></SvgIcon>
        </template>

        <template v-else-if="item.pic_url">
          <img class="w-full h-full object-cover rounded-[50%]" :src="item.pic_url" />
        </template>

        <i
          :class="[
            item.id === group_id
              ? 'absolute top-0 left-0 w-full h-full rounded-[50%] border-[length:2px] border-solid border-[color:var(--brand-1)] '
              : 'hidden',
          ]"
        ></i>
      </div>
    </div>
    <div
      :style="panel_style"
      class="h-[200px] overflow-y-auto flex flex-wrap content-start justify-start gap-x-[16px] gap-y-[8px] mx-[12px] py-[12px] px-[3px] border-t-[length:0.5px] border-solid border-[color:var(--line-1)]"
    >
      <template v-if="current_group_list.length">
        <div
          v-for="(emoji, index) in current_group_list"
          :key="index"
          :class="[
            `w-[56px] h-[56px] cursor-pointer box-border`,
            // `border-[length:1px] border-solid`,
            // index === emojis_select_index ? 'border-[color:var(--brand-1)]' : 'border-[color:var(--line-1)]',
          ]"
          @click="onSelectEmoji({ emoji, index }, EmojisType.list)"
        >
          <img :src="emoji" class="w-full h-full object-cover" />
        </div>
      </template>

      <template v-else>
        <div
          class="text-center w-full font-normal text-[length:13px] leading-[16px] text-[color:var(--text-3)]"
        >
          <span>{{ t("not_used_recently") }}</span>
        </div>
      </template>
    </div>

    <div
      class="absolute bottom-[68px] right-[8px] flex items-center justify-center w-[45px] h-[45px] bg-[var(--op-fill-white)] rounded-full border-[1px] border-[color:var(--line-1)] shadow-[2px_3px_2px_0_var(--op-shadow-black-5)] z-10 cursor-pointer"
      @click="emits('delete')"
    >
      <SvgIcon name="icon-delete" class="w-[18px] h-[18px]" color="var(--text-1)"></SvgIcon>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
