<template>
  <div class="flex justify-betwee items-center py-[14px] px-[20px] bg-[var(--op-fill-white)]">
    <label
      v-if="title"
      :for="title"
      class="w-[75px] flex-[2] font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)] mr-[15px]"
    >
      {{ title }}
    </label>
    <input
      :id="title"
      type="text"
      :placeholder="placeholder"
      readonly
      class="inline-block text-right flex-[5] bg-none bg-[color:transparent] font-normal text-[length:13px] leading-[16px] text-[color:var(--text-2)] placeholder:text-[color:var(--text-2)] appearance-none m-0 p-0"
    />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string;
  placeholder?: string;
}>();
</script>

<style lang="scss" scoped></style>
