<template>
  <div class="flex items-center flex-shrink-0">
    <div
      v-if="player_tag || is_self"
      class="flex items-center relative rounded-[20px] h-[16px] pl-[3px] pr-[6px] mr-[7px] cursor-pointer whitespace-nowrap"
      @click="$emit('click', 'gameTag')"
    >
      <i :class="[`absolute top-0 left-0 w-full h-full rounded-[20px] opacity-20`, cls.bg]"></i>
      <CommonImage
        v-if="player_tag"
        :src="player_tag?.icon"
        class="min-w-[12px] h-[12px] object-cover flex-shrink-0 mr-[1px]"
        alt=""
      />
      <template v-else-if="is_self">
        <SvgIcon
          name="icon-add"
          color="var(--color-white)"
          class="w-[12px] h-[12px] mr-[2px] ml-[3px]"
        ></SvgIcon>
        <span class="text-[length:10px] leading-[12px] text-[color:var(--color-white)] mt-[2px]">
          {{ t("game_tag") }}
        </span>
      </template>
      <span
        v-if="player_tag"
        :class="[`text-[length:10px] leading-[12px] font-bold mt-[2px]`, cls.text]"
      >
        {{ t(player_tag.i18n) }}: {{ normalizeTagValue(game_tag, game_tag_num) }}
      </span>
    </div>

    <div class="flex items-center" @click="$emit('click', 'status')">
      <div v-if="mood && mood_img" class="flex items-center">
        <CommonImage
          :src="`${mood_img}`"
          class="w-[16px] h-[16px] mr-[7px] flex-shrink-0 last-of-type:mr-0 cursor-pointer"
          alt=""
        />
      </div>
      <div
        v-else-if="is_self"
        class="cursor-pointer flex items-center rounded-[20px] h-[16px] px-[6px] bg-[var(--color-white-20)]"
      >
        <SvgIcon
          name="icon-add"
          color="var(--color-white)"
          class="w-[12px] h-[12px] mr-[2px]"
        ></SvgIcon>
        <span class="text-[length:10px] leading-[12px] text-[color:var(--color-white)] mt-[2px]">
          {{ t("user_status") }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// type 6 种状态 + 无状态
// 0、Set Gametag #fff， #fff 0.2
// 1、Tower #E8B442， #E9BE61 0.2
// 2、Battle(Normal) #FC7237， #FC6A37 0.2
// 3、Battle(Hard) #FF5D5A， #F95555 0.2
// 4、Nikkes #C57EEB， #C57EEB 0.2
// 5、Avatar Frames #37D1DF， #36DCEC 0.2
// 6、Costumes #3EAFFF，#3EAFFF 0.2

// types
import { PlayerTagId } from "packages/types/tag";

// utils
import { computed } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useI18n } from "vue-i18n";
import { get } from "lodash-es";
import { useUser } from "@/store/user";
import { useUserCenter } from "@/composables/use-user-center";
import { CommonImage } from "../image";

const props = withDefaults(
  defineProps<{
    game_tag: number | undefined;
    game_tag_num: number | undefined;
    mood?: string;
    is_self?: boolean;
  }>(),
  {},
);

defineEmits(["click"]);

const { t } = useI18n();

const user = useUser();
const { user_tags, normalizeTagValue } = useUserCenter();

const mood_img = computed(
  () => user.mood_list?.find((item) => item.library_data_id === props.mood)?.icon?.[0],
);

// 获取选择的TAG
const player_tag = computed(() => {
  return user_tags.value.find((item) => item.id === props.game_tag);
});

const cls = computed(() => {
  const ret = {
    [PlayerTagId.tower]: {
      text: `text-[color:#E8B442]`,
      bg: "bg-[color:#E9BE61]",
    },
    [PlayerTagId.battle_normal]: {
      text: `text-[color:#FC7237]`,
      bg: "bg-[color:#FC6A37]",
    },
    [PlayerTagId.battle_hard]: {
      text: `text-[color:#FF5D5A]`,
      bg: "bg-[color:#F95555]",
    },
    [PlayerTagId.nikkes]: {
      text: `text-[color:#C57EEB]`,
      bg: "bg-[color:#C57EEB]",
    },
    [PlayerTagId.frames]: {
      text: `text-[color:#37D1DF]`,
      bg: "bg-[color:#36DCEC]",
    },
    [PlayerTagId.costumes]: {
      text: `text-[var(--brand-1)]`,
      bg: "bg-[color:var(--brand-1)]",
    },
  };

  return get(ret, `${player_tag.value?.key}`, {
    text: `text-[color:var(--color-white)]`,
    bg: "bg-[color:var(--color-white)]",
  });
});
</script>
