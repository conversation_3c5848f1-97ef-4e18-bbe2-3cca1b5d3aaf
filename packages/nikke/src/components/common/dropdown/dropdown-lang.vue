<template>
  <Dropdown
    :open="is_open"
    :list="proxy_lang_list"
    :side="side"
    :align="align"
    :side-offset="sideOffset"
    :active="active_id"
    :type="type"
    :is-anniversary="isAnniversary"
    @change="handleChange"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #trigger="{ item }">
      <slot v-if="$slots.trigger" name="trigger" :item="item" />
      <div v-else class="w-[20px] h-[20px] cursor-pointer box-content">
        <div class="icon-language-origin">
          <SvgIcon name="icon-language" :color="icon_color"></SvgIcon>
        </div>
        <div class="icon-language-ann"></div>
      </div>
    </template>
    <template #default="{ item, active, first, last }">
      <div
        class="text-[length:12px] leading-[14px] px-[8px]"
        :class="[
          active ? 'text-[var(--brand-1)]' : 'text-[color:var(--text-1)]',
          first ? 'pt-[4px]' : '',
          last ? 'pb-[8px]' : '',
        ]"
      >
        {{ item?.name }}
      </div>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
// cpnts
import Dropdown from "@/components/ui/dropdown/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { DropdownItem, Dropdown as IDropdown, DropdownEmits } from "packages/types/dropdown";

// utils
import { computed, ref } from "vue";
import { getStandardizedLang } from "packages/utils/standard";
import { useLang } from "@/composables/use-lang";

const { lang_list } = useLang();

const props = withDefaults(defineProps<IDropdown>(), {
  icon_color: "var(--text-1)",
  side: "bottom",
  align: "start",
  open: false,
  sideOffset: 14,
  type: "customize",
  isAnniversary: true,
});

const proxy_lang_list = computed(() => {
  return props.list || lang_list.value;
});

const emits = defineEmits<DropdownEmits>();

const is_open = ref(false);
const active_id = ref<string | number>(getStandardizedLang());

const handleChange = <T = {},>(item: DropdownItem<T>) => {
  const target = proxy_lang_list.value.find((v) => v.name === item.name);
  if (target !== undefined) {
    active_id.value = target.value;
    emits("change", item);
  }
};

const handleOpen = (v: boolean) => {
  is_open.value = v;
  emits("open", v);
};
const handleClose = () => {
  is_open.value = false;
  emits("close");
};

defineExpose({
  handleOpen,
});
</script>
