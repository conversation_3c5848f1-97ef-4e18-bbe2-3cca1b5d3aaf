<template>
  <Dropdown
    :open="isOpen"
    :list="list"
    :side="side"
    :align="align"
    :side-offset="sideOffset"
    :active="activeId"
    :type="type"
    @change="handleChange"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #trigger="{ item }">
      <slot v-if="$slots.trigger" name="trigger" :item="item" />
      <div v-else class="flex items-center justify-center">
        <span>{{ item?.name }}</span>
      </div>
    </template>
    <template #default="{ item, active, index, first, last }">
      <slot
        v-if="$slots.default"
        :item="item"
        :index="index"
        :active="active"
        :first="first"
        :last="last"
      >
        {{ item }}
      </slot>

      <template v-else>
        <div class="text-[color:var(--text-2)] text-[length:12px] leading-[14px] dropdown-text">
          {{ item?.name }}
        </div>
        <SvgIcon
          name="icon-true"
          color="var(--brand-1)"
          class="w-[12px] h-[12px] ml-[4px]"
          :class="active ? 'opacity-100' : 'opacity-0'"
        ></SvgIcon>
      </template>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import Dropdown from "@/components/ui/dropdown/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { DropdownItem, Dropdown as IDropdown, DropdownEmits } from "packages/types/dropdown";

const props = withDefaults(defineProps<IDropdown>(), {
  side: "bottom",
  align: "start",
  open: false,
  sideOffset: 2,
});
const emit = defineEmits<DropdownEmits>();
const isOpen = ref(false);
const activeId = ref<string | number | undefined>(props.active ?? undefined);
const handleChange = <T = {},>(item: DropdownItem<T>) => {
  const findItem = props.list!.find((v) => v.name === item.name);
  if (findItem !== undefined) {
    activeId.value = findItem.value;
    emit("change", item);
  }
};

// note: 不用 torefs是害怕破坏以前的逻辑.
watch(
  () => props.active,
  () => {
    activeId.value = props.active;
  },
);

const handleOpen = (v: boolean) => {
  isOpen.value = v;
  emit("open", v);
};
const handleClose = () => {
  isOpen.value = false;
  emit("close");
};
</script>
