<template>
  <div
    class="bg-[var(--op-fill-white)] flex items-center justify-between pl-[20px] pr-[12px] py-[14px] box-border cursor-pointer"
    @click="emit('userClick')"
  >
    <div
      class="font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)] mr-[20px] flex-none"
    >
      {{ title }}
    </div>
    <div class="flex items-center overflow-hidden">
      <span
        v-if="infoText"
        class="font-normal text-[length:13px] leading-[16px] text-[color:var(--text-2)] mr-[12px] truncate"
      >
        {{ infoText }}
      </span>
      <SvgIcon
        v-if="type === 'right-arrow'"
        name="icon-arrow-right2"
        color="var(--text-1)"
        class="w-[12px] h-[10px] flex-shrink-0"
      ></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

withDefaults(
  defineProps<{
    title?: string;
    infoText?: string;
    type?: "right-arrow";
  }>(),
  {
    type: "right-arrow",
  },
);
const emit = defineEmits(["userClick"]);
</script>

<style lang="scss" scoped></style>
