// cpnts
import UserSignPolicyPop from "./index.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";
import { ref } from "vue";

export const useUserSignPolicyPop = () => {
  const dialog: any = ref();
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return new Promise(
      (resolve) =>
        (dialog.value = showDialog(
          UserSignPolicyPop,
          Object.assign(options, {
            show: true,
            onClose: async () => {
              resolve(PopCallbackValue.close);
              dialog.value.unmount();
            },
          }),
        )),
    );
  };
  return {
    show,
    dialog,
  };
};
