<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import Button from "@/components/ui/button/index.vue";
import { useToast } from "@/components/ui/toast";
import { Checkbox, CheckboxGroup } from "@/components/common/checkbox/index.ts";

// utils
import { computed, ref } from "vue";
import { t } from "@/locales";
import { useSignPrivacyProtocol } from "@/api/user.ts";

import { useUser } from "@/store/user";
// urls
import {
  GET_NIKKE_PRIVACY_POLICY,
  GET_BLABLA_EULA,
  GET_BLABLA_COMMUNITY_GUIDELINES,
} from "packages/configs/compliance";
import { getStandardizedLang } from "packages/utils/standard";

defineProps<{
  show: boolean;
}>();

const emits = defineEmits(["close"]);

const { show: toast } = useToast();
const is_indeterminate = ref(false);
const checked_policy = ref<string[]>([]);
const check_all = ref(false);

const polices = computed(() => [
  {
    name: t("nikke_end_user_license_agreement"), // "NIKKE End User License Agreement",
    value: "termsofservice",
    href: GET_BLABLA_EULA(getStandardizedLang()),
  },
  {
    name: t("nikke_privacy_policy"), //"NIKKE Privacy Policy",
    value: "privacypolicy",
    href: GET_NIKKE_PRIVACY_POLICY(),
  },
  {
    name: t("communityguidelines"), //"NIKKE Privacy Policy",
    value: "communityguidelines",
    href: GET_BLABLA_COMMUNITY_GUIDELINES(getStandardizedLang()),
  },
  // {
  //   name: t("none_disclosure_agreement", [t("app_name")]), //"NIKKE Privacy Policy",
  //   value: "none_disclosure_agreement",
  //   href: GET_BLABLA_NDA(getStandardizedLang()),
  // },
]);

const onConfirm = async () => {
  if (!check_all.value) {
    return;
  }
  await useSignPrivacyProtocol.run({});

  const user_store = useUser();
  user_store.user_info.has_sign_privacy = true;

  toast({
    text: t("sign_successfully"),
    type: "success",
  });
  emits("close");
};

const onChecklistChange = (v: string[]) => {
  checked_policy.value = v;
};
const onUpdateCheckAll = (v: boolean) => {
  check_all.value = v;
};

const onCheckAllChange = (val: boolean) => {
  checked_policy.value = val ? polices.value.map((item) => item.value) : [];
  is_indeterminate.value = false;
};

const onCheckedPolicyChange = (value: string[]) => {
  const checked_count = value.length;
  check_all.value = checked_count === polices.value.length;
  is_indeterminate.value = checked_count > 0 && checked_count < polices.value.length;
};
</script>

<template>
  <Dialog :show="show" :title="t('additional_information')">
    <div class="flex flex-col items-start">
      <Checkbox
        :label="t('have_agree_agreement')"
        :model-value="check_all"
        :indeterminate="is_indeterminate"
        class="text-[color:var(--text-1)] !items-start"
        @update:model-value="onUpdateCheckAll"
        @change="onCheckAllChange"
      />
      <CheckboxGroup
        class="ml-[12px] flex flex-col items-start"
        :model-value="checked_policy"
        @update:model-value="onChecklistChange"
        @change="onCheckedPolicyChange"
      >
        <Checkbox v-for="(item, index) in polices" :key="index" :value="item.value">
          <a class="text-[color:var(--text-1)] underline" :href="item.href" target="_blank">
            {{ item.name }}
          </a>
        </Checkbox>
      </CheckboxGroup>
    </div>

    <Button type="primary" :disabled="!check_all" class="w-full !mt-[12px]" @click="onConfirm">
      {{ t("confirm") }}
    </Button>

    <Button type="secondary" class="w-full !mt-[12px]" @click="emits('close')">
      <span class="text-[color:var(--text-1)]">{{ t("cancel") }}</span>
    </Button>
  </Dialog>
</template>

<style lang="scss" scoped></style>
