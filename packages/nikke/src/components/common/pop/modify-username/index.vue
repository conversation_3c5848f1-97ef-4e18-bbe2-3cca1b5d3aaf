<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import Button from "@/components/ui/button/index.vue";
import { useToast } from "@/components/ui/toast";
import SingleLineInput from "@/components/common/single-line-input/index.vue";

// configs
import { MAX_USER_NAME_LEN } from "@/configs/const";

// utils
import { ref } from "vue";
import { t } from "@/locales";
import { useModifyInfo } from "@/api/user.ts";

defineProps<{
  show: boolean;
}>();

const emits = defineEmits(["close"]);

const { show: toast } = useToast();

const username = ref("");

const onConfirm = async () => {
  if (!username.value) {
    return;
  }
  await useModifyInfo.run({ username: username.value });
  toast({
    text: t("modify_successfully"),
    type: "success",
  });
  emits("close");
};
</script>

<template>
  <Dialog :show="show" :title="t('modify_username')">
    <SingleLineInput
      v-model="username"
      :placeholder="t('please_enter')"
      :max-length="MAX_USER_NAME_LEN"
    ></SingleLineInput>

    <Button
      type="primary"
      :disabled="!Boolean(username)"
      class="w-full !mt-[12px]"
      @click="onConfirm"
    >
      {{ t("confirm") }}
    </Button>

    <Button type="secondary" class="w-full !mt-[12px]" @click="emits('close')">
      <span class="text-[color:var(--text-1)]">{{ t("cancel") }}</span>
    </Button>
  </Dialog>
</template>

<style lang="scss" scoped></style>
