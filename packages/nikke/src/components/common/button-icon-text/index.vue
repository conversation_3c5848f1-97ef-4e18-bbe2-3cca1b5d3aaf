<template>
  <StanceLike
    v-if="is_like_icon"
    :is_star="isActive"
    :value="text"
    :direction="type"
    @click="handleClick"
  ></StanceLike>

  <div
    v-else
    :class="[
      `cursor-pointer flex `,
      type == 'col' ? 'flex-col justify-center items-center' : 'items-center',
    ]"
    @click="handleClick"
  >
    <span class="w-[20px] h-[20px]">
      <SvgIcon
        :name="isActive ? activeIcon : icon"
        :color="isActive ? activeIconColor : iconColor"
      ></SvgIcon>
    </span>
    <span
      v-if="text"
      class="text-[length:12px] font-normal leading-[14px]"
      :class="[
        isActive ? `text-[color:${activeIconColor}]` : textColor && `text-[color:${textColor}]`,
        type == 'row' ? ' ml-[4px] mt-[3px]' : 'mt-[8px]',
      ]"
    >
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import Stance<PERSON>ike from "@/components/common/stance/like.vue";

withDefaults(
  defineProps<{
    type?: string;
    icon: string;
    isActive?: boolean;
    iconColor?: string;
    text?: string;
    textColor?: string;
    activeIcon?: string;
    activeIconColor?: string;
    is_like_icon?: boolean;
  }>(),
  {
    type: "row",
    isActive: false,
    iconColor: "var(--text-3)",
    text: "",
    textColor: "var(--text-2)",
    activeIcon: "",
    activeIconColor: "var(--brand-1)",
  },
);

const emits = defineEmits(["click"]);

const handleClick = (e: MouseEvent) => {
  emits("click", e);
};
</script>

<style scoped></style>
