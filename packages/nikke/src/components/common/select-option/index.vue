<template>
  <div
    class="absolute p-[12px] bg-[color:var(--op-fill-white)] shadow-[0_0_10px_0_var(--op-shadow-black-10)]"
  >
    <div
      v-for="(item, index) in list"
      :key="index"
      class="flex items-center justify-between mb-[12px] last-of-type:mb-0 cursor-pointer"
      @click.stop="$emit('change', index)"
    >
      <div
        :class="[
          `text-[length:12px] leading-[14px]`,
          activeId === index ? `text-[color:var(--brand-1)]` : `text-[color:var(--text-1)]`,
        ]"
      >
        {{ item?.label || item?.name }}
      </div>
      <SvgIcon
        v-show="activeId === index"
        name="icon-true"
        color="var(--brand-1)"
        class="w-[12px] h-[12px]"
      ></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

defineProps<{
  icon?: boolean;
  list?: any;
  activeId?: number;
}>();

defineEmits(["change"]);
</script>
