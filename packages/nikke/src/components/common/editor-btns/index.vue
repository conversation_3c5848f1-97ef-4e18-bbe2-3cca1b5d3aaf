<template>
  <div
    class="w-full h-[44px] flex items-center gap-[20px] bg-[var(--color-1)] px-[20px] py-[10px] box-border relative"
  >
    <SvgIcon
      v-if="emoji"
      class="w-[20px] h-[19px] cursor-pointer"
      name="icon-expression"
      :color="getColor(emojiStatus)"
      @click="emit('emojiClick')"
    ></SvgIcon>
    <SvgIcon
      v-if="picture"
      class="w-[20px] h-[17px] cursor-pointer"
      name="icon-picture"
      :color="getColor(pictureStatus)"
      @click="emit('pictureClick')"
    ></SvgIcon>
    <SvgIcon
      v-if="link"
      class="w-[20px] h-[21px] cursor-pointer"
      name="icon-link"
      :color="getColor(linkStatus)"
      @click="emit('linkClick')"
    ></SvgIcon>
    <SvgIcon
      v-if="label"
      class="w-[20px] h-[17px] cursor-pointer"
      name="icon-label"
      :color="getColor(labelStatus)"
      @click="emit('labelClick')"
    ></SvgIcon>

    <template v-if="friendCard">
      <i class="w-[1px] h-[24px] bg-[var(--fill-6)]"></i>

      <SvgIcon
        v-if="friendCard"
        class="w-[20px] h-[17px] cursor-pointer"
        name="icon-friend-card"
        :color="getColor(friendCardStatus)"
        @click="emit('friendCardClick')"
      ></SvgIcon>

      <span class="relative w-[20px] h-[17px] inline-flex">
        <SvgIcon
          class="cursor-pointer h-full w-full"
          name="icon-fact-check"
          :color="getColor(unionCardStatus)"
          @click="emit('unionCardClick')"
        ></SvgIcon>
        <span
          v-if="unionCardNew"
          class="absolute inline-flex justify-center text-[10px] h-[11px] pt-[1.5px] items-center top-[-6px] right-[-13px] px-[3px] font-bold rounded-2xl text-[color:var(--color-white)] bg-[var(--error)]"
        >
          NEW
        </span>
      </span>
    </template>

    <i class="w-[1px] h-[24px] bg-[var(--fill-6)]"></i>
    <SvgIcon
      v-if="authoringStatement"
      class="w-[22px] h-[20px] cursor-pointer"
      name="icon-authoring-statement"
      :color="getColor(authoringStatementStatus)"
      @click="emit('authoringStatementClick')"
    ></SvgIcon>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

type Status = "default" | "active" | "disabled";
withDefaults(
  defineProps<{
    emoji?: boolean;
    picture?: boolean;
    link?: boolean;
    label?: boolean;
    friendCard?: boolean;
    emojiStatus?: Status;
    pictureStatus?: Status;
    linkStatus?: Status;
    labelStatus?: Status;
    friendCardStatus?: Status;
    unionCardStatus?: Status;
    authoringStatementStatus?: Status;
    unionCard: boolean;
    authoringStatement?: boolean;
    unionCardNew?: boolean;
  }>(),
  {
    emoji: true,
    picture: true,
    link: false,
    label: false,
    friendCard: true,
    emojiStatus: "active",
    pictureStatus: "active",
    linkStatus: "active",
    labelStatus: "active",
    friendCardStatus: "active",
    unionCardStatus: "active",
    authoringStatementStatus: "active",
    unionCard: false,
    authoringStatement: false,
    unionCardNew: false,
  },
);
const emit = defineEmits([
  "emojiClick",
  "pictureClick",
  "linkClick",
  "labelClick",
  "friendCardClick",
  "unionCardClick",
  "authoringStatementClick",
]);

const getColor = (status: Status) => {
  if (status === "active") {
    return "var(--color-white)";
  } else if (status === "disabled") {
    return "var(--text-2)";
  } else {
    return "var(--text-3)";
  }
};
</script>

<style lang="scss" scoped></style>
