<script setup lang="ts">
// cpnts
import CommonImage from "./index.vue";
// utils
import { useCDNConfigs } from "packages/utils/cdn";

const { getCDNConfigs } = useCDNConfigs();
const { performance } = getCDNConfigs();

const images = performance?.lcp?.images || [];

console.log("images", images);
</script>

<template>
  <div class="h-0 overflow-hidden w-0">
    <CommonImage
      v-for="(src, index) in images"
      :key="index"
      :src="src"
      fetchpriority="high"
      class="h-0 w-0"
    ></CommonImage>
  </div>
</template>

<style lang="scss" scoped></style>
