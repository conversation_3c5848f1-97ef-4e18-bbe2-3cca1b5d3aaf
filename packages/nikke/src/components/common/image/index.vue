<template>
  <span :class="['inline-flex']">
    <img
      ref="root"
      :alt="alt"
      :src="is_lazy_load ? props.placeholder : display_src"
      :data-src="is_lazy_load ? display_src : ''"
      :class="image_class"
      :style="image_style || {}"
      @error="onError"
      @load="onLoad"
      @click="emits('click')"
    />
    <component :is="useImageStatusCpnt({ status: image_status })" v-if="loading"></component>
  </span>
</template>

<script setup lang="ts">
/**
 * @description 图片组件
 * @description 测试链接
 * https://test.blablalink.com/user?openid=MjkwODAtMTUzNjExODIyNzU2MTg1MTcyMzU=
 * https://test.blablalink.com/post/detail?post_uuid=7383021884649979590&original_content=1&scroll_target=comment
 * https://pre.blablalink.com/login
 * https://pre.blablalink.com/search/result?keyword=dd
 */

// types
import { ImageProps } from "packages/types/image";

// utils
import { cms_img_utils } from "@tencent/pa-cms-utils";
import { computed, ref } from "vue";
import { useImageCloudInfinite } from "@/composables/use-image";
import { useCDNConfigs } from "packages/utils/cdn";
import { useImageStatusCpnt, useImageStaus, ImageStatus } from "./image-status";
import { isMobileDevice } from "packages/utils/tools";

const is_mobile = isMobileDevice();
const win = window as { unavailable_webp_images_set?: Set<string> };
/**
 * 记录尝试加载 webp 但是报错的图片，避免下次继续尝试加载 webp
 */
const unavailable_webp_images_set =
  win.unavailable_webp_images_set || (win.unavailable_webp_images_set = new Set<string>());

const { toWebp, isCmsOptImage } = cms_img_utils;
const { handleQuality } = useImageCloudInfinite();
const { getCDNConfigs } = useCDNConfigs();
const { image_status, setImageStatus } = useImageStaus();
const { image } = getCDNConfigs();

const props = withDefaults(defineProps<ImageProps>(), {
  /**
   * Smallest data URI image possible for a transparent image
   * @link https://stackoverflow.com/questions/6018611/smallest-data-uri-image-possible-for-a-transparent-image
   */
  placeholder: "data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=", //"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
  src: "",
  auto_dethumbnail: undefined,
});
const emits = defineEmits(["click", "load", "error"]);

const root = ref();
const quality = ref(props.thumbnail_quality || image?.thumbnail_quality || 1);
const is_lazy_load = ref(props.lazy);

/**
 * @description 默认
 * @description PC 端：loading => 缩略 => 高清
 * @description 移动端：loading => 缩略
 */
const proxy_auto_dethumbnail = computed(() => {
  return props.auto_dethumbnail ?? !is_mobile;
});

const display_src = computed(() => {
  const src = handleSrc(props.src);
  /**
   * 展示原始图片
   */
  if (
    props.original ||
    !props.thumbnail ||
    (ImageStatus.loaded === image_status.value && proxy_auto_dethumbnail.value)
  ) {
    return src;
  }

  return handleQuality(src, quality.value);
});

const isMatchImageSrc = (src: string) => root.value?.getAttribute("src") === src;

const noneHandler = (src: string) => src;
const webpHandler = (src: string) => toWebp(src);

const handleSrc = (src: string = props.src) => {
  if (!props.original && !unavailable_webp_images_set.has(src)) {
    if (isCmsOptImage(props.src)) {
      return webpHandler(src);
    }
  }
  return noneHandler(src);
};

const onError = () => {
  if (isMatchImageSrc(props.src)) {
    return;
  }
  console.warn("onError call");

  setImageStatus(ImageStatus.error);
  unavailable_webp_images_set.add(props.src);
  root.value?.setAttribute("src", props.src);

  emits("error", root.value);
};

const onLoad = () => {
  setImageStatus(ImageStatus.loaded);
  emits("load", root.value);
};

const onChangeIsLazyLoad = (bool: boolean) => {
  is_lazy_load.value = bool;
};

defineExpose({
  onChangeIsLazyLoad,
});
</script>

<style lang="scss" scoped></style>
