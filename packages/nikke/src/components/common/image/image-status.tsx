// cpnts
// import Loading from "@/components/common/loading.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
// assets
import { ref } from "vue";

export enum ImageStatus {
  loading = "loading",
  error = "error",
  timeout = "timeout",
  loaded = "loaded",
  original = "original",
}

export const useImageStaus = () => {
  const image_status = ref(ImageStatus.loading);
  const timer = ref();

  const onTimeoutSniff = () => {
    timer.value = setTimeout(() => {
      setImageStatus(ImageStatus.timeout);
    }, 100 * 1000);
  };

  const setImageStatus = (new_status: ImageStatus) => {
    image_status.value = new_status;
    clearTimeout(timer.value);
  };

  onTimeoutSniff();

  return { image_status, setImageStatus };
};

export const useImageStatusCpnt = (props: { status: ImageStatus }) => {
  if ([ImageStatus.loaded].includes(props.status)) {
    return;
  }
  return (
    <div class="flex items-center justify-center bg-white flex-col absolute inset-0 z-[1]">
      {props.status === ImageStatus.loading ? (
        <SvgIcon
          name="icon-loading"
          class="common-rotate w-1/4 h-1/4 max-w-[32px] max-h-[32px]"
          color="var(--text-4)"
        ></SvgIcon>
      ) : (
        <></>
      )}
    </div>
  );
};
