<template>
  <!-- 触发元素 - 用于检测是否进入 sticky 状态 -->
  <div ref="triggerElement" class="h-0" :style="{ scrollMarginTop: `${props.top}px` }"></div>

  <div ref="stickyContainer" :class="classes" :style="styles" v-bind="$attrs">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, CSSProperties } from "vue";
import { useIntersectionObserver } from "@vueuse/core";

interface StickyLayoutProps {
  /** sticky 时距离顶部的距离（像素） */
  top?: number;
  class?: string;
  style?: CSSProperties;
  /** 自定义阴影类名，默认为 'shadow-[0_2px_4px_-1px_rgba(0,0,0,0.1),_0_1px_2px_-1px_rgba(0,0,0,0.08)]' */
  shadowClass?: string;
}

const props = withDefaults(defineProps<StickyLayoutProps>(), {
  top: 0,
  shadowClass: "shadow-[0_2px_4px_-1px_rgba(0,0,0,0.1),_0_1px_2px_-1px_rgba(0,0,0,0.08)]",
});

defineOptions({
  inheritAttrs: false,
});

/**
 * 触发元素 ref - 用于 IntersectionObserver 监听
 */
const triggerElement = ref<HTMLElement>();

/**
 * Sticky 容器 ref - 暴露给父组件使用
 */
const stickyContainer = ref<HTMLElement>();

/**
 * IntersectionObserver 监听触发元素是否可见
 * 当触发元素不可见时，表示应该进入 sticky 状态
 */
useIntersectionObserver(
  triggerElement,
  ([{ isIntersecting }]) => {
    is_sticky.value = !isIntersecting;
  },
  {
    threshold: 0,
    rootMargin: `-${props.top - 1}px 0px 0px 0px`,
  },
);

const is_sticky = ref(false);

const classes = computed(() => {
  const classes = ["sticky"];

  // 添加透传的 class
  if (props.class) {
    classes.push(...props.class.split(" "));
  }

  // sticky 状态时的样式
  if (is_sticky.value) {
    classes.push(props.shadowClass);
  }

  return classes.join(" ");
});

const styles = computed(() => {
  const styles: Record<string, any> = {};

  if (props.style) {
    Object.assign(styles, props.style);
  }

  styles.top = `${props.top}px`;

  return styles;
});

defineExpose({
  /** sticky 容器元素引用 */
  stickyContainer,
  /** trigger 元素引用 */
  triggerElement,
  /** 当前是否处于 sticky 状态 */
  is_sticky,
  /** 停止 IntersectionObserver 监听 */
  scrollToTop: () => {
    triggerElement.value?.scrollIntoView({
      block: "start",
      behavior: "smooth",
    });
  },
});
</script>
