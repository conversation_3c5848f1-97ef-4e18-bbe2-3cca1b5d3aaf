<template>
  <div class="flex items-center mx-[12px] h-[72px] bg-[var(--op-fill-white)] px-[12px] box-border">
    <div class="w-[48px] h-[48px] p-[2px] flex-shrink-0" @click="openUserCenter">
      <!-- <img :src="data.user_info.avatar" class="object-cover w-full" /> -->
      <Avatar
        :src="data.user_info.avatar"
        :auth_type="data.user_info?.auth_type"
        :frame="data.user_info?.avatar_pendant"
        class="object-cover w-full"
      />
    </div>
    <div
      class="ml-[8px] flex flex-col items-start flex-1 cursor-pointer overflow-hidden"
      @click="openUserCenter"
    >
      <span
        class="text-[length:14px] leading-[18px] font-bold text-[color:var(--text-1)] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ data.user_info.username }}
      </span>
      <span
        class="text-[length:11px] leading-[13px] font-normal text-[color:var(--text-3)] mt-[1px] max-w-full text-ellipsis overflow-hidden whitespace-nowrap"
      >
        {{ data.user_info.remark }}
      </span>
    </div>
    <template v-if="!is_me">
      <div class="ml-[10px] flex-none">
        <Btns v-if="data.user_info.is_black" :text="t('blocking')" type="disabled"></Btns>
        <Btns
          v-else-if="data.is_mutual_follow"
          icon="icon-both-side-followed"
          iconcolor="var(--brand-1)"
          @click="handleFollow"
        ></Btns>
        <Btns v-else-if="!data.is_follow" type="primary" :text="t('follow')" @click="handleFollow">
          <template #icon>
            <span class="text-[18px] mr-[2px]">+ </span>
          </template>
        </Btns>
        <Btns v-else icon="icon-followed" iconcolor="var(--text-1)" @click="handleFollow"></Btns>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import Btns from "@/components/common/btns/index.vue";
import { Follower } from "@/types/user";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { computed } from "vue";
import { useFollow } from "@/composables/use-follow";
import { useUser } from "@/store/user";
import Avatar from "@/components/common/avatar/index.vue";

const { t } = useI18n();

const props = defineProps<{
  data: Follower;
}>();

const emit = defineEmits(["follow"]);

const router = useRouter();
const user = useUser();

const openUserCenter = () => {
  router.push({
    name: RoutesName.USER,
    query: { openid: props.data.user_info.intl_openid },
  });
};

const is_me = computed(() => user.user_info?.intl_openid === props.data.user_info.intl_openid);

// 统一处理关注人列表和粉丝列表的操作
const { handleFollow } = useFollow(
  computed(() => ({
    openid: props.data.user_info.intl_openid,
    is_follow: props.data.is_follow,
    is_mutual_follow: props.data.is_mutual_follow,
    onSuccess: () => {
      emit("follow");
    },
  })),
);
</script>

<style lang="scss" scoped></style>
