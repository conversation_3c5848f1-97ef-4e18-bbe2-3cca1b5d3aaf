<template>
  <div
    :class="[
      `cursor-pointer max-w-max px-[8px] flex items-center justify-center relative z-[1]`,
      type_class,
      size_class,
      anniversary && 'anniversary-btn',
    ]"
    @click="handleClick"
  >
    <slot name="content"></slot>
    <!-- <i
      :class="[
        `absolute -bottom-[3px] right-[2px] w-[1.2px] h-[11px] -z-[1] rotate-[45deg]`,
        i_type_class,
      ]"
    ></i> -->
    <span v-if="text" class="flex items-center justify-center">
      <slot name="icon"></slot>
      <span class="font-Abolition">{{ text }}</span>
    </span>

    <SvgIcon
      v-if="icon && !anniversary"
      :name="icon"
      :color="iconcolor"
      class="w-[16px] h-[16px]"
    ></SvgIcon>
    <!-- <div v-if="icon && anniversary" class="aaa">1111</div> -->
    <SvgIcon
      v-if="icon && anniversary"
      :name="icon"
      :color="iconcolor"
      class="w-[16px] h-[16px]"
    ></SvgIcon>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useAnniversary } from "@/store/home/<USER>";

const { anniversary } = storeToRefs(useAnniversary());

const props = withDefaults(
  defineProps<{
    text?: string;
    icon?: string;
    iconcolor?: string;
    type?: "default" | "primary" | "disabled";
    size?: "s" | "m" | "l";
    mSmallFont?: boolean;
  }>(),
  {
    text: "",
    icon: "",
    iconcolor: "",
    type: "default", // primary | default ,默认 default,
    size: "s",
    mSmallFont: false,
  },
);

const emit = defineEmits(["click"]);

const type_class = computed(() => {
  if (props.type === "default") {
    return `btn-default bg-[var(--op-fill-white)] text-[color:var(--text-1)] `;
  } else if (props.type === "primary") {
    return `btn-primary bg-[color:var(--brand-1)] text-[color:var(--color-white)] bg-[url(@/assets/imgs/common/btns-bg.png)] bg-[length:100%_100%]`;
  } else if (props.type === "disabled") {
    return `btn-disabled bg-[color:var(--fill-2)] text-[color:var(--text-3-50)]`;
  } else {
    return "";
  }
});

// const type_class = computed(() => {
//   if (props.type === "default") {
//     return `bg-[transparent] border-[color:var(--line-1)] text-[color:var(--text-1)]`;
//   } else if (props.type === "primary") {
//     return `bg-[color:var(--brand-1)] border-[color:var(--brand-1)] text-[color:var(--color-white)]`;
//   } else if (props.type === "disabled") {
//     return `bg-[color:var(--fill-2)] border-[color:var(--fill-2)] text-[color:var(--text-4)] dark:text-[color:var(--text-3)]`;
//   } else if (props.type === "secondary") {
//     return `bg-[transparent] border-[color:var(--line-1)] text-[color:var(--text-1)]`;
//   } else {
//     return "";
//   }
// });

// const i_type_class = computed(() => {
//   if (props.type === "default") {
//     return `bg-[color:var(--line-1)]`;
//   } else if (props.type === "primary") {
//     return `bg-[color:var(--brand-1)]`;
//   } else if (props.type === "disabled") {
//     return `bg-[color:var(--fill-2)]`;
//   } else if (props.type === "secondary") {
//     return `bg-[color:var(--line-1)]`;
//   } else {
//     return "";
//   }
// });

const size_class = computed(() => {
  if (props.size === "s") {
    return `min-w-[64px] h-[25px] text-[length:var(--font-size-l)] leading-[14px] font-medium`;
  } else if (props.size === "m") {
    const font = props.mSmallFont
      ? "text-[length:var(--font-size-base)]"
      : "text-[length:var(--font-size-xl)]";
    return `min-w-[79px] h-[27px] font-normal ${font}`;
  } else {
    return "";
  }
});

const handleClick = (e: MouseEvent) => {
  if (props.type === "disabled") {
    e.stopPropagation();
    return;
  }

  emit("click", e);
};
</script>

<style lang="scss" scoped></style>
