<template>
  <div
    class="flex items-center justify-center rounded-[2px] font-normal text-[11px] leading-[13px] text-[color:var(--text-1)] px-[4px] cursor-pointer"
    :class="[active ? 'bg-[var(--brand-2)]' : 'bg-[var(--op-fill-white)]']"
  >
    <slot v-if="$slots.icon" name="icon"></slot>
    <template v-else>{{ text }}</template>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    text?: string;
    active?: boolean;
  }>(),
  {
    active: false,
  },
);
</script>
