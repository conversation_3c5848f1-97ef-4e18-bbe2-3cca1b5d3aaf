<template>
  <div
    class="flex items-center justify-between bg-[var(--op-fill-white)] py-[13px] pl-[20px] pr-[12px]"
  >
    <div class="flex items-center">
      <img :src="icon" class="w-[18px] h-[18px] object-contain flex-shrink-0 mr-[6px]" />
      <span
        class="font-normal text-[length:13px] leading-[16px] text-[color:var(--text-2)] mt-[2px]"
      >
        {{ title }}
      </span>
    </div>
    <div class="flex items-center">
      <div
        v-if="type === 'unlinked' && showGift"
        class="flex items-center border-[length:0.5px] border-solid border-[color:var(--other-2)] rounded-[36px] px-[7px] py-[2px] mr-[4px] cursor-pointer"
      >
        <img
          v-if="giftImg"
          :src="giftImg"
          class="w-[12px] h-[12px] object-contain flex-shrink-0 mr-[2px]"
        />
        <span class="font-normal text-[length:10px] leading-[12px] text-[color:var(--other-2)]">
          {{ giftTip || t("bound_get_gift") }}
        </span>
      </div>
      <div v-if="type === 'bound' && showGift && giftImg" class="relative mr-[13px]">
        <img :src="giftImg" class="w-[16px] h-[16px] object-contain" @click="emits('gift-click')" />
        <img
          v-if="received"
          src="@/assets/imgs/setting/icon-ok.png"
          class="absolute right-[-8px] bottom-[-2px] w-[12px] h-[12px]"
        />
      </div>
      <div class="flex items-center" :class="[type === 'bound' ? '' : 'cursor-pointer']">
        <span
          class="font-normal text-[length:11px] leading-[13px] mt-[2px]"
          :class="[`text-[color:${computedColor}]`]"
        >
          {{ computedText }}
        </span>
        <SvgIcon
          v-if="type !== 'bound'"
          name="icon-arrow-right2"
          :color="computedColor"
          class="w-[12px] h-[10px] flex-shrink-0 ml-[6px]"
        ></SvgIcon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { t } from "@/locales";

const emits = defineEmits(["gift-click"]);
const props = withDefaults(
  defineProps<{
    received?: boolean;
    title?: string;
    icon?: string;
    type?: "linked" | "unlinked" | "bound";
    showGift?: boolean;
    giftImg?: string | null;
    giftTip?: string;
  }>(),
  {
    received: false,
    title: "",
    icon: "",
    type: "unlinked",
    showGift: false,
    giftImg: "",
    giftTip: "",
  },
);

const computedColor = computed(() => {
  if (props.type === "unlinked") {
    return "var(--brand-1)";
  }
  return "var(--text-3)";
});
const computedText = computed(() => {
  if (props.type === "unlinked") {
    return t("link_channel");
  } else if (props.type === "linked") {
    return t("unlink_channel");
  } else if (props.type === "bound") {
    return t("channel_bounded");
  }
  return "";
});
</script>

<style lang="scss" scoped></style>
