<template>
  <Teleport to="body">
    <transition name="popbox-ani">
      <div
        v-if="visible"
        class="fixed bottom-0 z-50 h-full w-screen flex flex-col items-center justify-center"
        :class="[nobg && '!h-auto']"
      >
        <div
          v-if="!nobg"
          class="absolute top-0 h-full w-screen bg-[var(--color-black-55)]"
          @click="$emit('close')"
        ></div>

        <slot></slot>
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
defineProps<{
  visible?: boolean;
  nobg?: boolean;
}>();

defineEmits(["close"]);
</script>

<style scoped lang="scss"></style>
