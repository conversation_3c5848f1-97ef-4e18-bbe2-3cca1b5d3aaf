<template>
  <CommBottomPopup :show="visible" @close="onClose">
    <template #header>
      <div
        class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)] p-[20px] pb-[14px]"
      >
        {{ t("manage") }}
      </div>
    </template>
    <ul class="px-[20px] mb-[35px]">
      <li
        class="flex items-center px-[19px] py-[9px] bg-[color:var(--fill-3)] border-[length:0.5px] border-solid border-[color:var(--line-1)] box-border cursor-pointer"
        @click="onDelete"
      >
        <span class="flex items-center justify-center w-[40px] h-[40px]">
          <SvgIcon name="icon-delete" color="var(--text-1)" class="w-[33px] h-[33px]"></SvgIcon>
        </span>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] ml-[40px]"
        >
          {{ t("delete_post") }}
        </span>
      </li>
      <li
        v-if="can_edit"
        class="flex items-center px-[19px] py-[9px] bg-[color:var(--fill-3)] border-[length:0.5px] border-solid border-[color:var(--line-1)] box-border cursor-pointer mt-[14px]"
        @click="onEdit"
      >
        <span class="flex items-center justify-center w-[40px] h-[40px]">
          <SvgIcon name="icon-edit" color="var(--text-1)" class="w-[30px] h-[30px]"></SvgIcon>
        </span>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] ml-[40px]"
        >
          {{ t("edit_post") }}
        </span>
      </li>
    </ul>
  </CommBottomPopup>
</template>
<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";

// utils
import { useI18n } from "vue-i18n";
// import { Routes } from "@/router/routes";

const { t } = useI18n();

defineProps<{
  visible: boolean;
  can_edit?: boolean;
}>();

const emits = defineEmits(["delete", "close", "edit"]);

const onDelete = () => emits("delete");

const onEdit = () => emits("edit");

const onClose = () => emits("close");
</script>
