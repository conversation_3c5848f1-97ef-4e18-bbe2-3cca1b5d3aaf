<template>
  <div class="relative">
    <textarea
      v-model="text"
      :placeholder="placeholder"
      class="inline-block w-full h-[120px] rounded-[1px] font-normal text-[13px] leading-[16px] text-[color:var(--text-1)] placeholder:text-[color:var(--text-3-60)] appearance-none m-0 p-0 px-[12px] pt-[9px] pb-[18px] box-border resize-none"
      :class="[
        showBorder ? `border border-solid border-[color:var(--line-1)]` : 'border-0',
        bgColor ? `bg-[color:${bgColor}]` : 'bg-[color:var(--fill-3)]',
      ]"
      :maxlength="maxLength"
      :style="style"
    ></textarea>
    <span
      class="absolute right-[12px] bottom-[10px] font-normal text-[length:11px] leading-[13px] text-[color:var(--text-3)]"
    >
      <span
        :class="[text.length > 0 ? 'text-[color:var(--brand-1)]' : 'text-[color:var(--text-3)]']"
      >
        {{ text.length }} </span
      >/{{ maxLength }}
    </span>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    maxLength?: number;
    placeholder?: string;
    showBorder?: boolean;
    bgColor?: string;
    style?: string;
  }>(),
  {
    maxLength: 100,
  },
);
const text = defineModel<string>({ required: true });
</script>

<style lang="scss" scoped></style>
