<template>
  <template v-if="bg || img">
    <div
      :class="[
        `relative z-[1] min-h-[32px] pt-[12px] px-[8px] pb-[6px] rounded-[10px]`,
        isEdit && `!min-h-[110px] mb-[8px] !pb-[0] mt-[12px]`,
        bubble_class,
        props.class,
      ]"
      :style="{
        backgroundColor: bg || undefined,
        color: bg ? (getTextTheme(bg) === 'black' ? 'var(--text-1)' : 'var(--color-6)') : undefined,
      }"
    >
      <div class="absolute -top-[20px] right-0 h-[30px] bg-no-repeat overflow-hidden">
        <CommonImage
          v-if="img"
          :src="img"
          class="h-[30px] w-auto justify-end"
          image_class="object-contain"
        ></CommonImage>
      </div>
      <slot></slot>
    </div>
  </template>

  <template v-else>
    <slot></slot>
  </template>
</template>

<script setup lang="ts">
import { CommonImage } from "../image";
import { getTextColorByBackground } from "packages/utils/tools";
import { computed } from "vue";

const props = defineProps<{
  bg?: string;
  img?: string;
  isEdit?: boolean;
  class?: string;
}>();

const app_theme = computed(() => {
  // 示例：从 HTML 的 class 判断主题
  return document.documentElement.classList.contains("dark") ? "dark" : "light";
});

const getTextTheme = (bg: string) => {
  const v = getTextColorByBackground(bg, app_theme.value);
  console.log("[color] bg: ", props.bg, "theme: ", app_theme.value, "color: ", v);
  return v;
};

const bubble_class = computed(() => {
  return getTextTheme(props.bg || "#ffffff00") === "white" ? "bubble-dark" : "bubble-light";
});
</script>

<style lang="scss" scoped>
/** 亮色评论气泡内默认颜色 */
.bubble-light:deep(.ql-editor) {
  color: var(--text-1);
}

/** 暗色评论气泡内默认颜色 */
.bubble-dark:deep(.ql-editor) {
  color: var(--color-6);
}
.bubble-dark {
  --text-3: var(--text-4);
  --text-3-60: var(--text-4-60);
}
</style>
