<template>
  <div class="relative text-[length:0]">
    <div
      ref="contentRef"
      class="w-full leading-[16px] text-[color:var(--text-1)] invisible pointer-events-none whitespace-pre-wrap break-words"
      :class="computedFont"
      :style="{ minHeight: height }"
    >
      {{ textAreaValue + "\n" }}
    </div>
    <textarea
      ref="textarea"
      rows="1"
      class="absolute outline-none top-0 bottom-0 left-0 right-0 w-full leading-[16px] text-[color:var(--text-1)] whitespace-pre-wrap break-words resize-none appearance-none m-0 p-0 bg-none bg-[color:transparent]"
      :class="computedFont"
      :value="textAreaValue"
      :maxLength="maxLength"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
    ></textarea>
    <div
      v-show="placeholderVisible"
      class="absolute top-0 left-0 pointer-events-none leading-[16px] text-[color:var(--text-3)]"
      :class="computedFont"
    >
      <span>
        {{ placeholder }}
      </span>
      <span :class="[isFocus ? '' : 'text-[color:var(--error)]']">*</span>
    </div>
    <div
      v-show="isFocus"
      class="absolute -bottom-[8px] right-0 font-normal text-[length:9px] leading-[11px] pointer-events-none"
    >
      <span class="text-[color:var(--brand-1)]">{{ textAreaValue.length }}</span>
      /
      <span class="text-[color:var(--text-3)]">{{ maxLength }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = withDefaults(
  defineProps<{
    value?: string;
    placeholder?: string;
    defaultHeight?: number;
    type: "title" | "text";
    maxLength?: number;
  }>(),
  {
    value: "",
    placeholder: "",
    defaultHeight: 200,
    type: "text",
    maxLength: 200,
  },
);
const emit = defineEmits(["change", "update:value"]);
const computedFont = computed(() => {
  if (props.type === "title") {
    return `font-bold text-[length:14px]`;
  } else {
    return `font-normal text-[length:13px]`;
  }
});

const contentRef = ref();
const height = ref("auto");
const placeholderVisible = ref(props.placeholder ? (props.value ? false : true) : false);
const textAreaValue = ref(props.value);
const isFocus = ref(false);
const adjustHeight = () => {
  const contentHeight = contentRef.value.scrollHeight;
  if (contentHeight > props.defaultHeight) {
    height.value = "auto";
  } else {
    height.value = props.defaultHeight + "px";
  }
};
const handleFocus = () => {
  isFocus.value = true;
  adjustHeight();
};
const handleInput = (e: Event) => {
  const value = (e.target as HTMLTextAreaElement)?.value;
  textAreaValue.value = value ? value : "";
  adjustHeight();
  emit("update:value", textAreaValue.value);
  emit("change", textAreaValue.value);
};
const handleBlur = () => {
  isFocus.value = false;
  height.value = "auto";
};
watch(textAreaValue, (newVal) => {
  placeholderVisible.value = newVal.length === 0;
});
</script>

<style lang="scss" scoped></style>
