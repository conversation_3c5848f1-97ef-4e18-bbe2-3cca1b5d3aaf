<template>
  <label class="mb-[6px] inline-flex items-center">
    <span
      class="checkbox-input relative inline-flex h-[var(--height)] w-[var(--width)] cursor-pointer outline-none"
    >
      <input
        class="absolute bottom-0 left-0 right-0 top-0 z-[1] cursor-pointer opacity-0"
        type="checkbox"
        @click.stop="handleInput"
      />

      <span
        :class="{
          'checkbox-inner relative inline-block h-[var(--height)] w-[var(--width)] rounded-sm bg-[var(--bg-color)]': true,
          'is-checked': checked,
          'is-indeterminate': indeterminate,
        }"
      />
    </span>

    <span class="ml-2 cursor-pointer text-[12px] text-left">
      {{ label }}
      <slot />
    </span>
  </label>
</template>

<script lang="ts" setup>
/**
 * @description 复选框组件
 */
import { CheckboxProps } from "packages/types/checkbox";
import { computed, inject, Ref } from "vue";

const props = withDefaults(defineProps<CheckboxProps>(), {
  size: "default",
  modelValue: false,
});

const emits = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "change", value: boolean): void;
}>();

const checklist = inject("checklist", undefined) as Ref<Array<string | number>> | undefined;
const updateChecklist = inject("updateChecklist", undefined) as
  | ((value: string | number) => void)
  | undefined;

const isGroupMode = computed(() => {
  return !!checklist;
});

// 复选框是否被选中
const checked = computed(() => {
  if (!isGroupMode.value) {
    return props.modelValue;
  }
  if (!props.value) {
    return false;
  }
  return checklist?.value.includes(props.value);
});

// 处理点击事件
const handleInput = (e: MouseEvent) => {
  e.stopPropagation();
  if (isGroupMode.value) {
    return updateChecklist?.(props.value!);
  }
  emits("update:modelValue", !checked.value);
  emits("change", !checked.value);
};
</script>

<style lang="scss" scoped>
.checkbox-input {
  --width: 16px;
  --height: 16px;
}

.checkbox-inner {
  --bg-color: var(--fill-2);

  &.is-checked,
  &.is-indeterminate {
    --bg-color: var(--brand-1);
  }

  &::before {
    content: "";
    position: absolute;
    transform-origin: center;
  }

  &.is-indeterminate {
    &::before {
      transform: scale(0.5);
      height: 4px;
      left: 0;
      right: 0;
      top: 6px;
      background-color: var(--op-line-white);
    }
  }

  &.is-checked {
    &::before {
      transform: rotate(45deg) scaleY(1);
      border: 2px solid var(--op-line-white);
      border-left: 0;
      border-top: 0;
      height: 10px;
      width: 5px;
      left: 6px;
      top: 2px;
      transform: rotate(45deg) scaleY(1);
    }
  }
}
</style>
