<template>
  <div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
/**
 * @description 复选框组
 */
import { CheckboxGroupProps } from "packages/types/checkbox";
import { computed, provide } from "vue";

const props = defineProps<CheckboxGroupProps>();
const emits = defineEmits<{
  (e: "update:modelValue", value: string[]): void;
  (e: "change", value: string[]): void;
}>();

const checklist = computed({
  get: () => props.modelValue,
  set: (new_value: string[]) => {
    emits("update:modelValue", new_value);
    emits("change", new_value);
  },
});

// 将选中数组注入到上下文中
provide("checklist", checklist);

// 注入更新选中数组的方法
provide("updateChecklist", (value: string) => {
  const new_value = [...checklist.value];
  if (checklist.value.includes(value)) {
    const index = checklist.value.indexOf(value);
    new_value.splice(index, 1);
  } else {
    new_value.push(value);
  }
  checklist.value = new_value;
});
</script>
