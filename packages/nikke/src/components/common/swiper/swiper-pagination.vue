<template>
  <ul
    :class="[
      `flex cursor-pointer justify-center items-center`,
      is_horizontal ? `flex-row` : `flex-col`,
    ]"
  >
    <li v-for="(_, index) in slides_length" :key="index" @click.stop="onPaginationChange(index)">
      <div
        :class="[size_class, index === current_index ? theme.active_bg : theme.default_bg]"
      ></div>
    </li>

    <!-- 左侧箭头 -->
    <li
      v-if="prev_visible && !isMobile"
      class="fixed left-[10px] top-1/2 -translate-y-1/2 w-[24px] h-[32px] z-10 flex items-center justify-center"
      @click.stop="goToPrev"
    >
      <SvgIcon name="icon-arrow2-left" color="var(--text-1)"></SvgIcon>
    </li>

    <!-- 右侧箭头 -->
    <li
      v-if="next_visible && !isMobile"
      class="fixed right-[10px] top-1/2 -translate-y-1/2 w-[24px] h-[32px] z-10 flex items-center justify-center"
      @click.stop="goToNext"
    >
      <SvgIcon name="icon-arrow2-right" color="var(--text-1)"></SvgIcon>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { useSwiper } from "@/composables/use-swiper.ts";
import { Size } from "packages/types/common";
import {
  SwiperPaginationProps,
  SwiperPaginationTheme,
  SwiperPaginationThemeType,
  SwiperDirection,
} from "@/types/swiper";
import { computed } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { isMobileDevice } from "packages/utils/tools";

const isMobile = isMobileDevice();

const props = withDefaults(defineProps<SwiperPaginationProps>(), {
  theme: SwiperPaginationTheme.default,
  size: Size.sm,
  direction: SwiperDirection.horizontal,
});

const swiper = useSwiper();

const is_horizontal = computed(() => props.direction === SwiperDirection.horizontal);

const all_themes: Record<SwiperPaginationThemeType, { default_bg: string; active_bg: string }> = {
  [SwiperPaginationTheme.default]: {
    default_bg: "swiper-pagination-item bg-[var(--color-white-40)]",
    active_bg: "swiper-pagination-item swiper-pagination-item-active bg-[var(--color-white)]",
  },
  [SwiperPaginationTheme.primary]: {
    default_bg: "",
    active_bg: "",
  },
  [SwiperPaginationTheme.light]: {
    default_bg: "",
    active_bg: "",
  },
};

const size_class = computed(() => {
  const size_map = {
    sm: is_horizontal.value ? "mr-[5px] h-[6px] w-[6px] rounded-full" : "",
    md: is_horizontal.value ? "mr-1 h-1 w-8 rounded" : "h-[21px] w-[8px] mb-[8px] rounded",
  };
  // @ts-ignore
  return size_map[props.size] || size_map.sm;
});

const theme = computed(() => {
  const current =
    props.custom_theme || all_themes[props.theme] || all_themes[SwiperPaginationTheme.default];

  return current;
});

const { current_index, slides_length, onChange } = swiper;

const goToPrev = () => {
  const prevIndex = current_index.value > 0 ? current_index.value - 1 : slides_length.value - 1;
  onChange(prevIndex, { is_from_pagination_change: true });
};

const goToNext = () => {
  const nextIndex = current_index.value < slides_length.value - 1 ? current_index.value + 1 : 0;
  onChange(nextIndex, { is_from_pagination_change: true });
};

const onPaginationChange = (index: number) => {
  if (props.disable_click) {
    return;
  }
  onChange(index, { is_from_pagination_change: true });
};
</script>

<style lang="scss" scoped></style>
