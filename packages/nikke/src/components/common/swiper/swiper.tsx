import { computed, ref, defineComponent, watch, onMounted, onUnmounted, provide } from "vue";

import { createTimer } from "@/utils/timer";
import { isRTL } from "packages/utils/tools";

import { useSlide } from "@/composables/use-slide";
import { SWIPER_PROVIDE_KEY } from "@/composables/use-swiper";

// types
import { Position } from "packages/types/common";
import { SwiperDirection } from "@/types/swiper";

const DEFAULT_AUTOPLAY_DURATION = 5e3;

/**
 * NOTE: slide slot 不会随时外部变量的改变而充新刷新，这里可以给外部加上 key 让 slide slot 刷新
 */
export default defineComponent({
  props: {
    loop: {
      type: Boolean,
      default: true,
    },
    index: {
      type: Number,
      default: 0,
    },
    rotate: {
      type: Number,
      default: 0,
    },
    scale: {
      type: Number,
      default: 1,
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
    duration: {
      type: Number,
      default: DEFAULT_AUTOPLAY_DURATION,
    },
    width: {
      type: Number,
      default: 0,
    },
    /**
     * 轮播方向
     */
    direction: {
      type: String,
      default: SwiperDirection.horizontal,
    },
  },

  emits: ["change", "slide-transition-end"],

  setup(props, { slots, expose, emit }) {
    const swiper_ref: any = ref();
    const should_transition = ref(true);
    const slide_position = ref(Position.left);
    const autoplay_timer = ref();

    const rotate = ref(props.rotate);
    const scale = ref(props.scale);
    const current_index = ref(props.index);
    const transition_durtatoin = 1; // 1s;
    const is_on_changing = ref(false);
    // 幻灯片插槽
    const slides_slot = computed(() => slots.default?.());
    // 所有幻灯片的个数
    const slides_length: any = computed(() => slides_element.value?.length || 0);
    // 最后一个元素
    const last_index = computed(() => Math.max(slides_length.value - 1, 0));
    // 所有幻灯片的元素
    const slides_element: any = computed(() => slides_slot.value?.[0].children);
    // 是否是水平轮播
    const is_horizontal = computed(() => props.direction === SwiperDirection.horizontal);

    const expose_current_index = computed(() =>
      current_index.value < 0
        ? last_index.value
        : current_index.value > last_index.value
          ? 0
          : current_index.value,
    );
    const all_list = computed(() => {
      const ret = [
        // 头部加上
        slides_element.value[slides_length.value - 1],
        ...slides_element.value,
        slides_element.value[0],
      ];

      return ret;
    });

    const translate_position = computed(() => {
      const basic = is_horizontal.value && isRTL() ? +100 : -100;
      return (basic * (current_index.value + 1)) / all_list.value.length;
    });

    const next = (indicator: Position | number) => {
      if (typeof indicator === "number") {
        if (indicator < 0 || indicator >= slides_length.value) {
          console.warn("indicator out of range");
          return;
        }
        current_index.value = indicator;
        return;
      }

      const left = () => {
        if (current_index.value >= 0) {
          current_index.value--;
        } else {
          current_index.value = last_index.value;
        }
      };

      const right = () => {
        // 达到最后一个需要重置
        current_index.value >= slides_length.value
          ? (current_index.value = 0)
          : (current_index.value += 1);
      };

      const cb = {
        [Position.left]: left,
        [Position.right]: right,
        // fix ts lint error
        [Position.top]: () => {},
        [Position.bottom]: () => {},
      }[indicator];

      cb?.();
    };

    const onChange = (
      indicator: Position | number,
      options?: { is_from_pagination_change?: boolean; from?: string },
    ) => {
      if (is_on_changing.value) {
        return;
      }

      const old_index = current_index.value;

      is_on_changing.value = true;

      rotate.value = 0;
      scale.value = 1;

      // animationend 在某些条件下会设置 should_transition=false
      // 所以这里需要判断如果 should_transition=false 那么需要重新设置为 true
      // 使得动画可以一致触发
      !should_transition.value && (should_transition.value = true);

      next(indicator);

      // emit change event
      emit("change", {
        current_index: expose_current_index.value,
        old_index,
        options,
      });

      // 重置定时器
      if (props.autoplay && autoplay_timer.value && options?.is_from_pagination_change) {
        stopAutoplayTimer();
        startAutoplayTimer();
      }
    };

    const onTransitionstart = () => {};

    const onTransitionend = () => {
      // console.log("onTransitionend called");

      if (current_index.value === -1) {
        should_transition.value = false;
        current_index.value = last_index.value;
      } else if (current_index.value === slides_length.value) {
        should_transition.value = false;
        current_index.value = 0;
      }

      is_on_changing.value = false;
      emit("slide-transition-end");
    };
    //
    const startAutoplayTimer = () => {
      autoplay_timer.value = createTimer(
        () => {
          // 达到最后一个需要重置
          onChange(Position.right);
        },
        {
          interval: props.duration,
          loop: true,
        },
      );
    };

    const stopAutoplayTimer = () => {
      autoplay_timer.value?.cancel();
    };

    useSlide(swiper_ref, {
      callback(options) {
        // 如果只有一个slide，则不需要滑动
        if (slides_length.value <= 1) {
          return;
        }

        const { slide_position: sp, touch_config } = options || {};

        if (!touch_config || Math.abs(touch_config.x_end - touch_config.x_start) < 50) {
          return;
        }

        if (sp) {
          slide_position.value =
            // 滑动的左右需要调整
            sp === Position.left ? Position.right : sp === Position.right ? Position.left : sp;

          onChange(slide_position.value, {
            from: "touch",
          });
        }
      },
    });

    watch(
      () => [props.rotate, props.scale],
      ([new_rotate, new_scale]) => {
        rotate.value = new_rotate;
        scale.value = new_scale;
      },
    );

    onMounted(() => {
      if (props.autoplay) {
        stopAutoplayTimer();
        startAutoplayTimer();
      }
    });

    onUnmounted(() => {
      if (props.autoplay) {
        stopAutoplayTimer();
      }
    });

    //
    const expose_object = {
      onChange,
      slides_length,
      current_index: expose_current_index,
    };

    provide(SWIPER_PROVIDE_KEY, expose_object);
    expose(expose_object);

    return () => {
      return (
        <div
          class="relative h-full overflow-hidden"
          style={{ width: props.width ? props.width + "px" : "100%" }}
        >
          <div
            ref={swiper_ref}
            class={["flex will-change-transform"].concat(
              is_horizontal.value ? ["h-full flex-row"] : ["w-full flex-col"],
            )}
            onTransitionend={onTransitionend}
            onTransitionstart={onTransitionstart}
            style={Object.assign(
              {
                transform: is_horizontal.value
                  ? `translate3d(${translate_position.value}%, 0, 0)`
                  : `translate3d(0, ${translate_position.value}%, 0)`,
                transition: should_transition.value ? `transform ${transition_durtatoin}s` : "none",
              },
              is_horizontal.value
                ? { width: `${100 * all_list.value.length}%` }
                : { height: `${100 * all_list.value.length}%` },
            )}
          >
            {all_list.value.map((child: any, index: number) => {
              const isActiveIndex = (idx: number) => idx === current_index.value;

              const transform = [
                `rotate(${isActiveIndex(index - 1) ? rotate.value : 0}deg)`,
                `scale(${isActiveIndex(index - 1) ? scale.value : 1})`,
              ];
              // 追加一些【位置样式类】；方便调用方通过类名实现自定义的swiper效果
              let pos_cls = isActiveIndex(index - 1) ? "swiper-slide-active" : "";
              pos_cls = isActiveIndex(index - 2) ? "swiper-slide-next" : pos_cls;
              const cls = `flex h-full w-full items-center justify-center overflow-hidden will-change-transform swiper-slide-item ${pos_cls}`;
              return (
                <div
                  class={cls}
                  style={{
                    transition: `transform ${transition_durtatoin}s`,
                    transform: transform.join(" "),
                  }}
                >
                  {child}
                </div>
              );
            })}
          </div>

          {slots.pagination?.()}
        </div>
      );
    };
  },
});
