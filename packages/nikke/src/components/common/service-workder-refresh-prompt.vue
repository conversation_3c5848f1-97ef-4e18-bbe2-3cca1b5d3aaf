<script setup lang="ts">
/**
 * @link vite-plugin-pwa/examples/vue-router/src/ReloadPrompt.vue
 */
import { createTimer } from "@/utils/timer";
import { pwaInfo } from "virtual:pwa-info";
import { useRegisterSW } from "virtual:pwa-register/vue";
import { ref, Ref } from "vue";

console.log("PWA Info: ", pwaInfo);

const timer: Ref<ReturnType<typeof createTimer> | null> = ref(null);

const { needRefresh, updateServiceWorker } = useRegisterSW({
  immediate: false,
  onRegisteredSW(sw_url, r) {
    console.log(`Service Worker at: ${sw_url}`);
    console.log(`Service Worker Registered: `, r);

    // interval.value?.cancel();
    // interval.value = null;

    r &&
      (timer.value = createTimer(
        async () => {
          console.log("Checking for Service Worker update.");

          /**
           * @description ServiceWorkerRegistration 的 update 方法尝试更新 service worker。
           * @description 获得 worker 脚本的 URL，逐字节匹配新获取的 worker 和当前的 worker，存在差异的时候安装新的 worker。
           * @description 获取 worker 脚本的更新操作会忽略浏览器缓存的 24 小时前的内容。
           * [MDN Reference](https://developer.mozilla.org/docs/Web/API/ServiceWorkerRegistration/update)
           */
          await r.update();
        },
        {
          interval: 10 * 1000,
          loop: true,
        },
      ));

    console.log(`Service Worker timer: `, timer.value);
  },
});

function close() {
  needRefresh.value = false;
}
</script>

<template>
  <transition name="scale-fade">
    <div v-if="needRefresh" class="pwa-toast" role="alert">
      <div class="mb-2 text-gray-800;">
        <span>New content available, click on refresh button to update.</span>
      </div>
      <button v-if="needRefresh" class="btn btn-refresh" @click="updateServiceWorker()">
        Refresh
      </button>
      <button class="btn btn-close" @click="close">Close</button>
    </div>
  </transition>
</template>

<style>
.pwa-toast {
  @apply fixed right-0 bottom-0 m-4 p-4 border border-gray-300 rounded-lg shadow-lg bg-white z-50 text-[12px];
}
.pwa-toast .message {
  @apply mb-2 text-gray-800;
}
.pwa-toast .btn {
  @apply border rounded-md px-4 py-1 font-medium text-[12px];
}
.pwa-toast .btn-refresh {
  @apply border-[var(--brand-1)] bg-[var(--brand-1)] text-white mr-[4px];
}
.pwa-toast .btn-close {
  @apply border-gray-300 bg-white text-gray-800 hover:bg-gray-300;
}

.scale-fade-enter-active,
.scale-fade-leave-active {
  @apply transition-all duration-300;
}
.scale-fade-enter,
.scale-fade-leave-to {
  @apply opacity-0 scale-75;
}
</style>
