<template>
  <div class="flex items-center justify-center my-[20px]">
    <SvgIcon
      name="icon-loading"
      class="w-[32px] h-[32px] common-rotate"
      :color="computedColor"
    ></SvgIcon>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { computed } from "vue";

const props = defineProps<{
  color?: string; // 使 color 成为可选
}>();

const computedColor = computed(() => {
  return props.color || "var(--text-4)"; // 默认颜色
});
</script>

<style lang="scss" scoped></style>
