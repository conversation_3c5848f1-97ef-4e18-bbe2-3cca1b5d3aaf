<template>
  <!-- <CommBottomPopup class="overflow-scroll" :visible="display" line @close="close">
    <Spinner v-show="loading"></Spinner>
    <div class="max-h-[75vh] overflow-scroll mt-[16px]"> -->
  <PlayerPopWrapper
    :display="display"
    :loading="loading"
    :hide_wrapper="hide_wrapper"
    @close="close"
  >
    <User
      :user_basic_info="user_basic_info"
      :user_role_info="user_role_info"
      :friend_card_status="friend_card_status"
      class="!p-0"
    >
      <div v-if="show_add_friend" class="pr-[16px] flex items-center">
        <Btns type="primary" @click="onAddFriend">
          <template #content>
            <SvgIcon
              name="icon-follow"
              color="var(--color-white)"
              class="w-[12px] h-[12px]"
            ></SvgIcon>
          </template>
        </Btns>
      </div>
    </User>
    <div
      class="relative flex-1 bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-no-repeat bg-[length:100%_auto] bg-[center_top] pt-[25px] mt-[8px]"
    >
      <div class="relative pb-[24px]">
        <TypeData
          :type="1"
          :user_basic_info="user_basic_info"
          :user_battle_info="user_battle_info"
        ></TypeData>

        <DailyNews :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
        <!-- 基础信息 -->
        <template v-if="expanded">
          <div class="mt-[12px]">
            <Outpost :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
          </div>
          <div class="mt-[12px]">
            <Material :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
          </div>
        </template>
        <div
          ref="expandBtnRef"
          class="expand-btn absolute bottom-0 left-1/2 -translate-x-1/2 bg-[#141416] w-[43px] h-[16px] flex items-center justify-center cursor-pointer"
          @click="expanded = !expanded"
        >
          <SvgIcon
            name="icon-arrow-top"
            color="var(--color-white)"
            class="w-[13px] h-[8px]"
            :class="[expanded ? '' : 'rotate-[180deg]']"
          ></SvgIcon>
        </div>
      </div>
      <!-- 公会信息 -->
      <UserUnionInfo
        :target_nikke_area_id="user_role_info?.area_id"
        :target_intl_open_id="uid"
        :is_client="is_client"
      />
      <div class="mt-[12px] mb-[20px]">
        <div class="flex items-center justify-between mx-[12px]">
          <div class="flex items-center">
            <div class="font-bold text-[18px] text-[color:var(--text-1)] font-center-patch">
              {{ t("nikke_list_tab_player") }}
            </div>
          </div>
          <div class="flex items-center cursor-pointer mt-[5px]">
            <span
              class="text-[11px] text-[color:var(--text-1)] font-center-patch"
              @click="clientVisit"
            >
              {{ t("view_all") }}
            </span>
          </div>
        </div>
        <NikkeRoleList
          class="mt-[8px] mx-[12px]"
          :uid="uid"
          :is_client="true"
          :jumpable="false"
          :shiftys_user="shiftys_user"
        ></NikkeRoleList>
      </div>
    </div>
  </PlayerPopWrapper>
</template>

<script setup lang="ts">
import { createShiftysUser } from "@/shiftyspad/composable/game-data";
import { ref, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { base64Encode } from "packages/utils/encrypt";
// comps
// import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import TypeData from "@/components/common/type-data/index.vue";
// import Spinner from "@/shiftyspad/components/common/spinner.vue";
import NikkeRoleList from "@/components/common/nikke-role-list/index.vue";
// import SubHeading from "@/shiftyspad/components/main/sub/sub-heading.vue";
import Outpost from "@/shiftyspad/components/main/sub/outpost.vue";
import DailyNews from "@/shiftyspad/components/main/sub/daily-news.vue";
import Material from "@/shiftyspad/components/main/sub/material.vue";
import User from "@/components/common/player-pop/user.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { FriendCardStatus } from "packages/types/user";
// import { RequestBtnAtomicCpnt } from "@/components/common/friend-card/atomic.tsx";
import PlayerPopWrapper from "@/components/common/player-pop/wrapper.vue";
import UserUnionInfo from "@/shiftyspad/components/main/UserUnionInfo.vue";
import Btns from "@/components/common/btns/index.vue";
import { useAddFriendIngame } from "@/shiftyspad/composable/role/add-friend";

const { t } = useI18n();
const props = defineProps<{
  uid: string;
  area_id?: string;
  is_client?: boolean;
  friend_card_status?: FriendCardStatus;
  hide_wrapper?: boolean;
}>();
const emits = defineEmits<{
  close: [];
  show: [];
  error: [];
}>();

const display = ref(true);
const loading = ref(true);
const expanded = ref(false);

const router = useRouter();
const { is_client, uid, area_id } = toRaw(props);
const shiftys_user = createShiftysUser({
  uid,
  area_id,
  is_client,
});
const { show_add_friend, onAddFriend } = useAddFriendIngame(shiftys_user);
const { user_basic_info, user_battle_info, user_role_info, initAllData } = shiftys_user;

const close = () => {
  display.value = false;
  loading.value = false;
  emits("close");
};

initAllData().finally(() => {
  loading.value = false;
});

const clientVisit = () => {
  close();
  router.push({
    name: RoutesName.SHIFTYSPAD_NIKKE_LIST,
    query: {
      uid: base64Encode(uid),
    },
  });
};
</script>
