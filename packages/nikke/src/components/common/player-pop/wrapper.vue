<template>
  <CommBottomPopup
    v-if="!hide_wrapper"
    class="overflow-scroll"
    :visible="display"
    line
    @close="close"
  >
    <Spinner v-show="loading"></Spinner>
    <div class="max-h-[75vh] overflow-scroll mt-[16px]">
      <slot></slot>
    </div>
  </CommBottomPopup>
  <template v-else>
    <slot></slot>
  </template>
</template>

<script setup lang="ts">
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Spinner from "@/shiftyspad/components/common/spinner.vue";

defineProps<{
  display: boolean;
  loading: boolean;
  hide_wrapper?: boolean;
}>();

const emits = defineEmits<{
  close: [];
}>();

const close = () => {
  emits("close");
};
</script>
