<script setup lang="ts">
import { computed, toRefs } from "vue";
import { useGameRegion } from "@/composables/role/use-server-info";
import { UserBasicInfo } from "packages/types/shiftyspad";
import avatarPic from "@/shiftyspad/assets/images/appicon.png";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
import { Role } from "packages/types/games";

const props = withDefaults(
  defineProps<{
    user_role_info: Role | null;
    user_basic_info: UserBasicInfo | null;
  }>(),
  {
    user_battle_info: null,
    user_basic_info: null,
    type: 0,
  },
);

const { user_role_info, user_basic_info } = toRefs(props);
const { server_list } = useGameRegion({
  all: true,
});

const server_name = computed(() => {
  const player_area_id = user_role_info.value?.area_id ?? "";
  return (
    server_list.value.find((item) => item.area_id + "" == player_area_id + "")?.area_name ?? "-"
  );
});
</script>

<template>
  <div class="pt-[16px] flex justify-between">
    <div class="flex items-center px-[16px]">
      <!-- 角色信息 -->
      <div class="w-[56px] h-[56px] mr-[12px]">
        <RoleAvatar
          :scene="'shiftyspad'"
          :default_avatar="avatarPic"
          :avatar_id="user_role_info?.icon ?? 0"
          class="box-border"
        ></RoleAvatar>
      </div>
      <div>
        <div class="flex items-center gap-x-[4px]">
          <span class="font-medium text-[18px] leading-[22px] text-[color:var(--text-1)]">
            {{ user_role_info?.role_name ?? "-" }}
          </span>
          <span
            class="flex items-center justify-center !font-[DINNextLTProBold] text-[12px] leading-[12px] text-[color:var(--text-4)] border-[1px] border-[#fff] bg-[var(--fill-1)] px-[3px] pt-[3px] pb-[1px]"
          >
            Lv.{{ user_basic_info?.player_level ?? "-" }}
          </span>
        </div>
        <div
          class="flex items-center gap-x-[8px] font-normal text-[11px] leading-[13px] text-[#959596] mt-[2px]"
        >
          <span>{{ server_name }}</span>
        </div>
      </div>
    </div>
    <slot></slot>
  </div>
</template>
