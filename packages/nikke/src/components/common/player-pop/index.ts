import { onUnmounted } from "vue";
import { mountDialogComponent } from "@/utils/mount";
import RolePop from "./index.vue";
import { FriendCardStatus } from "packages/types/user";

/**
 * 按 uid (gameid-openid) 拉起玩家的弹窗。
 */
export function showRoleDialog(params?: {
  uid: string;
  is_client?: boolean;

  // 按角色查询
  area_id?: string;
  friend_card_status?: FriendCardStatus;

  onShow?: () => void;
  onShare?: (name: string) => void;
  onError?: () => void;
  onClose?: () => void;
  onFriendCardRequest?: () => void;
}) {
  const voidcb = () => {};

  const {
    uid,
    area_id,
    is_client = true,
    friend_card_status = FriendCardStatus.disabled,
    onShow = voidcb,
    onClose = voidcb,
    onError = voidcb,
    onShare = voidcb,
    onFriendCardRequest = voidcb,
  } = params ?? {};
  const { unmount, instance: _ } = mountDialogComponent(RolePop, {
    uid,
    area_id,
    is_client,
    friend_card_status,
    onShow,
    onShare,
    onError,
    onClose: () => {
      unmount();
      onClose();
    },
    onFriendCardRequest,
  });

  onUnmounted(() => {
    unmount();
  });

  return { unmount };
}
