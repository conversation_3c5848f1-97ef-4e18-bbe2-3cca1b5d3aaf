<template>
  <span
    :class="[
      'inline-flex cursor-pointer items-center justify-between relative',
      shape_class,
      theme_class,
      size_class,
    ]"
  >
    <span class="line-clamp-1" @click.stop="() => emits('click')">
      <slot>
        {{ content }}
      </slot>
    </span>

    <div
      v-if="closable"
      :class="[
        'absolute cursor-pointer rounded-full bg-[var(--fill-1-80)] dark:bg-[var(--fill-1)] flex justify-center items-center',
        close_icon_size_class,
      ]"
      @click="onClose"
    >
      <i class="absolute-center !w-[24px] !h-[24px]"></i>
      <SvgIcon name="icon-close3" :color="close_icon_color"></SvgIcon>
    </div>
  </span>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { TagProps } from "packages/types/tag";
import { Shape, Theme, Size } from "packages/types/common";

// utils
import { computed } from "vue";

const props = withDefaults(defineProps<TagProps>(), {
  shape: Shape.normal,
  theme: Theme.default,
  size: Size.sm,
  closable: false,
});
const emits = defineEmits(["close", "click"]);

const shape_class = computed(() => {
  const shape_config = {
    [Shape.square]: "",
    [Shape.normal]: "rounded-[4px]",
    [Shape.round]: "rounded-full",
    [Shape.mark]: "rounded-tr-full rounded-br-full",
  };
  return shape_config[props.shape];
});

const theme_class = computed(() => {
  const theme_config = {
    [Theme.default]:
      "bg-[color:var(--op-fill-white)] text-[color:var(--brand-1)] border-[1px] border-[color:var(--brand-1)]",
    [Theme.primary]: "",
    [Theme.success]: "",
    [Theme.warning]: "",
    [Theme.danger]: "",
  };
  return theme_config[props.theme];
});

const size_class = computed(() => {
  const size_config = {
    [Size.sm]: "py-[4px] px-[10px] text-[length:12px]",
    [Size.md]: "",
    [Size.lg]: "",
    [Size.xl]: "",
    [Size.xxl]: "",
  };
  return size_config[props.size];
});

const close_icon_size_class = computed(() => {
  const size_config = {
    [Size.sm]: "h-[14px] w-[14px] -right-[4px] -top-[4px]",
    [Size.md]: "",
    [Size.lg]: "",
    [Size.xl]: "",
    [Size.xxl]: "",
  };
  return size_config[props.size];
});

const close_icon_color = computed(() => {
  const config = {
    [Theme.default]: "var(--fill-2)",
    [Theme.primary]: "",
    [Theme.success]: "",
    [Theme.warning]: "",
    [Theme.danger]: "",
  };
  return config[props.theme];
});

const onClose = () => emits("close");
</script>

<style lang="scss" scoped></style>
