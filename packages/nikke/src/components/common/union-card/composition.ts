// assets
import bg1 from "@/assets/imgs/common/join-friend-bg1.png";
import bg2 from "@/assets/imgs/common/join-friend-bg2.png";
import bg3 from "@/assets/imgs/common/join-friend-bg3.png";
// types
import { UnionCardStatus } from "packages/types/user";
// utils
import { t } from "@/locales";

export const useUnionCard = () => {
  const getIconConfig = (status: UnionCardStatus) => {
    switch (status) {
      case UnionCardStatus.disabled:
        return {
          icon_name: "icon-join-union-disabled",
          color: "var(--text-3)",
          text: t("join"),
        };
      case UnionCardStatus.canrequest:
        return {
          icon_name: "icon-join-union",
          color: "var(--op-text-white)",
          text: t("join"),
        };
      // case UnionCardStatus.requested:
      //   return {
      //     bg: bg3,
      //     icon_name: "icon-followed",
      //     color: "var(--text-3)",
      //     text: "",
      //   };
      default:
    }

    return {
      icon_name: "icon-join-union",
      color: "var(--op-text-white)",
      text: t("join"),
    };
  };

  return {
    getIconConfig,
  };
};
