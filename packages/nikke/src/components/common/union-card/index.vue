<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { RequestBtnAtomicCpnt } from "./atomic";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
// types
import {
  FriendCardStatus,
  GetUserGamePlayerInfoResponse,
  UnionCardStatus,
} from "packages/types/user";
// assets
// import default_avatar from "@/assets/imgs/test/team-combat-icon.png";
import default_avatar from "@/shiftyspad/assets/images/appicon.png";
import team_combat_icon from "@/assets/imgs/test/team-combat-icon.png";
// utils
import { useGameRegion } from "@/composables/role/use-server-info";
import { PostDetail } from "packages/types/post";
import { UnionCard } from "@/api/union";
import test from "@/assets/imgs/test/team-combat-icon.png";
import { t } from "@/locales";
import UnionIcon from "@/components/announcement-square/union-icon/index.vue";
import UnionRankIcon from "@/components/announcement-square/union-rank-icon";
import { CommonImage } from "../image";
import { onMounted } from "vue";
import { useUnionStore } from "@/store/union";
const { getServerName } = useGameRegion();

const props = defineProps<{
  union_info: UnionCard;
  delete_visible?: boolean;
  disabled?: boolean;
  status: UnionCardStatus;
}>();

const union_store = useUnionStore();
const emits = defineEmits(["delete", "request", "click"]);

const onDelete = () => emits("delete");
// const onRequest = () => {
// if (props.status === UnionCardStatus.canrequest) {
//   emits("request");
// }
// };

onMounted(async () => {
  await union_store.getMyGuildInfo({ latest: false });
});
</script>

<template>
  <div
    v-click-interceptor.need_login.mute.sign_privacy.stop="() => emits('click')"
    class="h-[68px] w-full px-[12px] flex items-center bg-[color:var(--op-fill-white)] border-[1px] border-[var(--line-1)] relative cursor-pointer"
  >
    <div class="flex flex-1 w-0">
      <UnionIcon
        :guild_icon="union_info.guild_icon"
        class="w-[50px] h-[50px] flex-shrink-0 mr-[8px]"
      ></UnionIcon>
      <div class="flex-grow-[10] mt-[10px]">
        <div class="flex items-center">
          <div
            class="bg-[var(--brand-1)] font-[Inter] rounded-[1px] px-[5px] py-[2px] text-[length:10px] text-[color:var(--color-white)] leading-[1]"
          >
            Lv.{{ union_info.guild_level }}
          </div>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:13px] font-[DINNextLTProBold] ml-[5px] leading-[16px] truncate w-[100px] flex-1"
          >
            {{ union_info.guild_name }}
          </div>
        </div>
        <div class="mt-[5px] text-[color:var(--text-3)] text-[length:9px] leading-[11px] truncate">
          <!-- {{ union_info.guild_description }} -->
          {{ t(`area_id_${union_info.nikke_area_id}`) }}
        </div>
      </div>
      <div class="ml-[10px] mt-[3px] flex-grow-[1] flex-shrink-0">
        <div class="flex items-center">
          <CommonImage :src="test" class="w-[18px] h-[18px] flex-shrink-0 mr-[4px]"></CommonImage>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:12px] leading-[1] font-bold font-[DINNextLTProBold]"
          >
            {{ union_info.guild_activity }}
          </div>
        </div>
        <div class="flex items-center mt-[8px]">
          <UnionRankIcon
            :rank="union_info.guild_rank"
            class="w-[18px] h-[18px] flex-shrink-0 mr-[4px]"
          ></UnionRankIcon>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:11px] leading-[14px] font-bold font-[Inter]"
          >
            {{ t(`union_rank_${union_info.guild_rank}`) }}
          </div>
        </div>
      </div>
    </div>

    <RequestBtnAtomicCpnt :status="status"></RequestBtnAtomicCpnt>

    <div
      v-if="delete_visible"
      class="absolute z-[2] -top-[10px] right-[1px] w-[20px] h-[20px] bg-[var(--fill-1-80)] rounded-full flex items-center justify-center cursor-pointer"
      @click="onDelete"
    >
      <SvgIcon name="icon-delete" color="var(--op-text-white)" class="w-[12px] h-[12px]"></SvgIcon>
    </div>
  </div>
</template>
