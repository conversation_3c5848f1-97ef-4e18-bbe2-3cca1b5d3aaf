<template>
  <Popbox :visible="visible">
    <div class="w-[255px] h-auto relative z-[2] pt-[8px] pb-[92px] min-h-[200px]">
      <img
        src="@/assets/imgs/common/bg-dialog-01.png"
        class="absolute top-0 left-0 w-full h-[57px] -z-[1]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-star-left.png"
        class="absolute -top-[21px] -left-[30px] w-[86px] h-[76px] -z-[1]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-star-right.png"
        class="absolute -top-[16px] -right-[53px] w-[60px] h-[80px] -z-[1]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-02.png"
        class="absolute top-[56px] left-0 w-full h-[calc(100%_-_127px)] -z-[2]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-03.png"
        class="absolute bottom-0 left-0 w-full h-[72px] -z-[1]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-content-01.png"
        class="absolute top-[57px] left-[16px] w-[79px] h-[62px] -z-[1]"
      />
      <img
        src="@/assets/imgs/common/bg-dialog-content-02.png"
        class="absolute bottom-[72px] right-[16px] w-[96px] h-[110px] -z-[2]"
      />
      <i
        class="absolute top-[58px] right-[16px] w-[4px] h-[4px] -z-[1] bg-[color:var(--other-2)] opacity-20"
      ></i>
      <i
        class="absolute bottom-[74px] left-[16px] w-[4px] h-[4px] -z-[1] bg-[color:var(--other-2)] opacity-20"
      ></i>
      <div
        class="text-[length:18px] px-[24px] h-[50px] flex justify-center items-center font-bold text-center text-[color:var(--other-6)] leading-[22px]"
      >
        {{ title }}
      </div>
      <div
        v-if="is_suc && data?.points"
        class="flex justify-center items-center h-[39px] mt-[10px]"
      >
        <span
          class="text-[length:40px] font-[DINNextLTProBold] leading-[20px] mt-[10px] text-[color:var(--other-6)]"
        >
          +{{ data?.points }}
        </span>
        <i
          class="w-[39px] h-[39px] ml-[12px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%]"
        ></i>
      </div>
      <div
        v-if="is_suc"
        class="mt-[11px] h-[43px] px-[24px] w-full text-[color:var(--other-6)] text-center leading-[14px] text-[length:12px]"
      >
        {{ desc }}
      </div>

      <div
        v-else
        class="text-[color:var(--color-black-70)] px-[24px] text-[length:12px] text-center leading-[14px] mt-[20px]"
        @click="onDesc"
      >
        {{ desc }}
      </div>

      <slot></slot>
      <!--       <div v-if="list.length === 1" class="w-[200px] mx-auto mt-[10px] h-[94px]">
        <img :src="getImage(list[0])" alt="" />
      </div -->
      <Swiper
        v-if="is_suc && list.length > 0"
        class="mt-[10px] w-[200px] !h-[94px] mx-auto swiper"
        :class="{ more_swiper: list.length > 1 }"
        :loop="false"
        :index="0"
        :autoplay="list.length > 1"
        :duration="3000"
      >
        <div
          v-for="(item, index) in list"
          :key="'checkin_' + index"
          class="w-[200px] h-[94px]"
          @click="onImageClick(item)"
        >
          <img :src="getImage(item)" class="cursor-pointer" />
        </div>
      </Swiper>
      <div
        :class="[
          `absolute z-[10] bottom-[48px] left-1/2 -translate-x-1/2 w-[170px] -ml-[1px] text-center font-bold pt-[10px] text-[color:var(--color-white)] text-[length:16px] leading-[19px] h-[35px] bg-[length:100%_100%]`,
          !is_suc
            ? `bg-[url('@/assets/imgs/common/icon-btn-bg-fail.png')]`
            : `bg-[url('@/assets/imgs/common/icon-btn-bg.png')]`,
        ]"
        @click="onConfirm"
      >
        {{ confirmText }}
      </div>
    </div>

    <div class="w-[20px] h-[20px] mt-[14px] relative cursor-pointer" @click="close">
      <i class="absolute-center"></i>
      <SvgIcon name="icon-close" color="var(--color-white-50)"></SvgIcon>
    </div>
  </Popbox>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { Swiper } from "@/components/common/swiper/index.ts";
import { useBsCmsListCpnt, useCms } from "@/composables/use-cms";
import { CMS_COLUMN_NAME_REWARDS, CMS_COLUMN_NAME_CHECKIN_BANNER } from "packages/configs/cms";
import { CmsContentClass } from "packages/types/cms";
import { Task } from "packages/types/rewards";
import Popbox from "@/components/common/popbox.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { PopCallbackValue } from "packages/types/common";
import { resovledCmsConfig } from "packages/utils/cms";
import { IDetail } from "@tencent/pa-cms-utils";
import { useLockScroll } from "@/composables/use-lock-scroll";
import { report } from "packages/utils/tlog";
const { lock, unlock } = useLockScroll();
const { useCmsJump, getImage } = useCms();
const props = defineProps<{
  visible: boolean;
  data?: Task;
  title: string;
  desc: string;
  is_suc?: boolean;
  confirmText: string;
  callback?(type: PopCallbackValue): void;
  onDesc?(): void;
}>();

const emits = defineEmits(["close"]);

watch(
  () => props.visible,
  (cur) => {
    if (cur) {
      lock();
      if (props.data) {
        report.standalonesite_reward_window.cm_click({
          task_id: props.data.task_id,
          num: props.data.points,
        });
      }
    }
  },
  {
    immediate: true,
  },
);

const { list } = useBsCmsListCpnt({
  cms_config: resovledCmsConfig(),
  primary_column_name: CMS_COLUMN_NAME_REWARDS,
  second_column_name: CMS_COLUMN_NAME_CHECKIN_BANNER,
  content_class: CmsContentClass.banner,
});

const close = (use_callback = true) => {
  emits("close");
  use_callback && props.callback?.(PopCallbackValue.close);
  unlock();
};
const onImageClick = (item: IDetail) => {
  close(false);
  useCmsJump(item);
};
const onConfirm = () => {
  close(false);
  props.callback?.(PopCallbackValue.confirm);
};
const onDesc = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target) {
    return;
  }
  if (target.classList.contains("trigger")) {
    props.onDesc?.();
    close();
  }
};
</script>
<style lang="scss" scoped>
.swiper {
  width: 230px !important;
  margin: 0 auto;
}
:deep(.more_swiper .swiper-slide-next) {
  transform: translateX(-25px) !important;
}
</style>
