<template>
  <div ref="container_dom" class="relative flex items-center">
    <slot></slot>

    <div v-if="proxy_back_to_left_visible" @click="onBackToLeft">
      <slot name="back-to-left">
        <div
          :class="[
            'z-10 flex h-10 w-10 min-w-[auto] cursor-pointer items-center justify-center rounded-full bg-[var(--tag-bg)] md:!h-14 md:!w-14',
            scroll_container
              ? 'absolute left-2 top-1/2 -translate-y-1/2 md:!left-4'
              : 'fixed left-4 top-1/2 -translate-y-1/2 md:!left-8',
          ]"
        >
          <SvgIcon
            name="arrow-left"
            class="h-3 w-3 md:!h-6 md:!w-6"
            color="var(--bg-fill-white)"
          ></SvgIcon>
        </div>
      </slot>
    </div>

    <div v-if="loading_visible && loading" class="flex justify-center items-center h-full">
      <slot name="loading">
        <Loading></Loading>
      </slot>
    </div>

    <template v-else-if="empty_visible && empty">
      <slot name="empty">
        <!-- <Empty class="mx-auto"></Empty> -->
      </slot>
    </template>

    <template v-else-if="finished_visible && finished">
      <slot name="finished">
        <!--  <Finish class="px-4"></Finish> -->
      </slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import Loading from "@/components/common/loading.vue";
/* import { Finish } from "@/components/finish/index"; */
// import Empty from "@/components/common/nodata.vue";

import { debounce } from "lodash-es";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

interface HorizontalInfiniteScrollProps {
  loading?: boolean;
  finished?: boolean;
  failed?: boolean;
  empty?: boolean;
  distance?: number;
  loading_visible?: boolean;
  finished_visible?: boolean;
  empty_visible?: boolean;
  failed_visible?: boolean;
  back_to_left_visible?: boolean;
  scroll_container?: HTMLElement | "self";
  debounce_interval?: number;
}

const props = withDefaults(defineProps<HorizontalInfiniteScrollProps>(), {
  distance: 150,
  loading_visible: true,
  finished_visible: true,
  empty_visible: true,
  back_to_left_visible: false,
  debounce_interval: 500,
});

const emit = defineEmits(["load-more"]);

const scroll_x = ref(0);

const container_dom = ref<HTMLDivElement>();

const scroll_container = computed(() => {
  if (props.scroll_container === "self") return container_dom.value;
  return props.scroll_container;
});

const proxy_back_to_left_visible = computed(() => {
  if (!props.back_to_left_visible) {
    return false;
  }
  // 滚动超过100px或容器宽度的20%时显示返回按钮
  const threshold = scroll_container.value
    ? Math.min(100, scroll_container.value.clientWidth * 0.2)
    : 100;

  return scroll_container.value ? scroll_x.value > threshold : scroll_x.value > threshold;
});

const onBackToLeft = () => {
  const target = scroll_container.value || window;
  target.scrollTo({
    left: 0,
    behavior: "smooth",
  });
};

const onScroll = debounce(() => {
  if (props.loading || props.finished || props.failed) {
    return;
  }
  let inner_width = 0;
  let scroll_width = 0;

  if (scroll_container.value) {
    scroll_x.value = scroll_container.value.scrollLeft;
    inner_width = scroll_container.value.clientWidth;
    scroll_width = scroll_container.value.scrollWidth;
  } else {
    scroll_x.value = window.scrollX;
    inner_width = window.innerWidth;
    scroll_width = document.documentElement.scrollWidth;
  }

  // 当滚动到右侧边缘距离小于设定值时触发加载更多
  if (scroll_width - (scroll_x.value + inner_width) <= props.distance) {
    loadMore();
  }
}, props.debounce_interval);

const loadMore = () => {
  emit("load-more");
};

watch(
  () => scroll_container.value,
  () => {
    unRegisterWindowScroll();
    registerElementScroll();
  },
);

const registerElementScroll = () => {
  scroll_container.value?.addEventListener?.("scroll", onScroll);
};

const unRegisterElementScroll = () => {
  scroll_container.value?.removeEventListener?.("scroll", onScroll);
};

const registerWindowScroll = () => {
  window.addEventListener("scroll", onScroll);
};

const unRegisterWindowScroll = () => {
  window.removeEventListener("scroll", onScroll);
};

const register = () => {
  props.scroll_container ? registerElementScroll() : registerWindowScroll();
};

const unRegister = () => {
  props.scroll_container ? unRegisterElementScroll() : unRegisterWindowScroll();
};

onMounted(register);
onUnmounted(unRegister);
</script>
