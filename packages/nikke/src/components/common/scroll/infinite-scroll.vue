<template>
  <div ref="container_dom" class="relative">
    <slot></slot>

    <div v-if="loading_visible && loading" class="flex justify-center">
      <slot name="loading">
        <Loading></Loading>
      </slot>
    </div>

    <template v-else-if="empty_visible && empty">
      <slot name="empty">
        <Empty class="mt-[150px]"></Empty>
      </slot>
    </template>

    <template v-else-if="finished_visible && finished">
      <slot name="finished">
        <!--  <Finish class="py-4"></Finish> -->
      </slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import Loading from "@/components/common/loading.vue";
/* import { Finish } from "@/components/finish/index"; */
import Empty from "@/components/common/nodata.vue";

import { debounce } from "lodash-es";
import { computed, onActivated, onDeactivated, onMounted, onUnmounted, ref, watch } from "vue";

interface InfiniteScrollProps {
  loading?: boolean;
  finished?: boolean;
  failed?: boolean;
  empty?: boolean;
  distance?: number;
  loading_visible?: boolean;
  finished_visible?: boolean;
  empty_visible?: boolean;
  failed_visible?: boolean;
  back_to_top_visible?: boolean;
  scroll_container?: HTMLElement | "self";
  debounce_interval?: number;
}

const props = withDefaults(defineProps<InfiniteScrollProps>(), {
  distance: 500,
  loading_visible: true,
  finished_visible: true,
  empty_visible: true,
  back_to_top_visible: false,
  debounce_interval: 10,
});

const emit = defineEmits(["load-more"]);

const scroll_y = ref(0);
const container_dom = ref<HTMLDivElement>();

const scroll_container = computed(() => {
  if (props.scroll_container === "self") return container_dom.value;
  return props.scroll_container || document.getElementById("layout-content");
});

const onScroll = debounce(() => {
  if (props.loading || props.finished || props.failed) {
    return;
  }
  let inner_height = 0;
  let scroll_height = 0;

  if (!scroll_container.value) {
    return;
  }

  scroll_y.value = scroll_container.value.scrollTop;
  inner_height = scroll_container.value.clientHeight;
  scroll_height = scroll_container.value.scrollHeight;

  // scroll_y.value = window.scrollY;
  // inner_height = window.innerHeight;
  // scroll_height = document.documentElement.scrollHeight;

  // console.log(`scroll_height`, scroll_height);
  // console.log(`scroll_y.value`, scroll_y.value);
  // console.log(`inner_height`, inner_height);

  if (scroll_height - (scroll_y.value + inner_height) <= props.distance) {
    loadMore();
  }
}, props.debounce_interval);

const loadMore = () => emit("load-more");

const registerElementScroll = () => {
  scroll_container.value?.addEventListener("scroll", onScroll);
};

const unRegisterElementScroll = () => {
  scroll_container.value?.removeEventListener("scroll", onScroll);
};

watch(
  () => scroll_container.value,
  () => {
    registerElementScroll();
  },
);

onMounted(registerElementScroll);
onActivated(registerElementScroll);
onDeactivated(unRegisterElementScroll);
onUnmounted(unRegisterElementScroll);
</script>
