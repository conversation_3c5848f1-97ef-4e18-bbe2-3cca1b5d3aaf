<script setup lang="ts">
// cpnts
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";

// types
import { ColumnType, Column } from "packages/types/time-picker";

// utils
import dayjs from "dayjs";
import { computed, ref, watchEffect } from "vue";
import { t } from "@/locales";
import {
  COLUMN_HEIGHT,
  getDateObj,
  getDaysInMonth,
  getDisplayLabel,
  getTimestamp,
  MAX_COLUMN_DISPLAY,
  useColumnScrollContainerRef,
  // findValueIndex,
} from "./utils";

const props = withDefaults(
  defineProps<{
    show: boolean;
    value?: number;
    ignore_columns: ColumnType[];
  }>(),
  {},
);
const emits = defineEmits(["close", "confirm"]);

const date_obj = ref(getDateObj(props.value));
// how many days in this month
const days_in_month = computed(() => getDaysInMonth(date_obj.value.year, date_obj.value.month));
const { columnScrollContainerRef } = useColumnScrollContainerRef();

const columns = computed<Column[]>(() =>
  [
    // year
    {
      type: ColumnType.year,
      title: t("year"),
      columnScrollContainerRef,
      items: Array(10)
        .fill(0)
        .map((_, i) => i + dayjs().get(ColumnType.year)),
    },
    // month
    {
      type: ColumnType.month,
      title: t("month"),
      columnScrollContainerRef,
      items: Array(12)
        .fill(0)
        .map((_, i) => i + 1),
    },
    // day
    {
      type: ColumnType.day,
      title: t("day"),
      columnScrollContainerRef,
      items: Array(days_in_month.value)
        .fill(0)
        .map((_, i) => i + 1),
    },
    // hour
    {
      type: ColumnType.hour,
      title: t("hour"),
      columnScrollContainerRef,
      items: Array(24)
        .fill(0)
        .map((_, i) => i),
    },
    // minute
    {
      type: ColumnType.minute,
      title: t("minute"),
      columnScrollContainerRef,
      items: Array(60)
        .fill(0)
        .map((_, i) => i),
    },
    // second
    {
      type: ColumnType.second,
      title: t("second"),
      columnScrollContainerRef,
      items: Array(60)
        .fill(0)
        .map((_, i) => i),
    },
  ].filter((column: Column) =>
    props.ignore_columns ? !props.ignore_columns.includes(column.type) : true,
  ),
);

const onSelect = (options: { type: ColumnType; value: number }, evt: Event) => {
  const { type, value } = options;
  if (type === ColumnType.month) {
    // adjust days
    const pre_day = date_obj.value[ColumnType.day];
    const new_month_days = getDaysInMonth(date_obj.value.year, value);
    if (new_month_days < pre_day) {
      date_obj.value[ColumnType.day] = new_month_days;
    }
  }
  date_obj.value[type] = value;

  const el = evt.target as HTMLElement;
  el.scrollIntoView({ behavior: "smooth" });
};

const onConfirm = () => {
  emits("confirm", getTimestamp(date_obj.value), date_obj.value);
  emits("close");
};

watchEffect(() => {
  date_obj.value = getDateObj(props.value);
});
</script>

<template>
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div
        class="pt-[16px] pb-[6px] px-[12px] text-[14px] text-end leading-[16px] font-bold text-[var(--brand-1)]"
      >
        <span @click="onConfirm">{{ t("confirm") }}</span>
      </div>
    </template>

    <ul class="h-[260px] flex w-full pb-[20px] px-[6px]">
      <li
        v-for="(column, index) in columns"
        :key="index"
        class="flex flex-col flex-1 px-[4px] h-full"
      >
        <span
          class="text-[var(--color-black-55)] h-[30px] min-h-[30px] dark:text-[var(--color-white)] capitalize inline-flex rounded-md text-[14px] items-center justify-center w-full"
        >
          {{ column.title }}
        </span>
        <div
          :ref="
            (el: HTMLElement) =>
              columnScrollContainerRef(el, {
                type: column.type,
                date_obj,
                columns,
              })
          "
          class="h-0 grow overflow-y-auto no-scrollbar"
        >
          <div
            :style="{
              height: `${(column.items.length + MAX_COLUMN_DISPLAY - 1) * COLUMN_HEIGHT}px`,
            }"
          >
            <span
              v-for="(item, sub_index) in column.items"
              :key="sub_index"
              :class="[
                `inline-flex h-[30px] min-h-[30px] rounded-md text-[14px] items-center justify-center w-full tracking-[2px] cursor-pointer`,
                date_obj[column.type] === item
                  ? 'bg-[color:var(--brand-1)] text-[color:var(--color-white)]'
                  : 'text-[var(--color-black)] dark:text-[var(--color-white)]',
              ]"
              @click="
                (evt: Event) =>
                  onSelect(
                    {
                      type: column.type,
                      value: item,
                    },
                    evt,
                  )
              "
            >
              {{ getDisplayLabel(item) }}
            </span>
          </div>
        </div>
      </li>
    </ul>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
