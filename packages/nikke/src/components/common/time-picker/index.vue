<script setup lang="ts">
// cpnts
import { useTimePickerPop } from "./index";

// types
import { ColumnType } from "packages/types/time-picker";

// utils
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

const props = withDefaults(
  defineProps<{
    ignore_columns: ColumnType[];
  }>(),
  {},
);
const emits = defineEmits(["close"]);
const model = defineModel({ type: Number, default: 0 });

const { show: onShowTimePickerPop } = useTimePickerPop();

const onConfirm = (timestamp: number) => {
  model.value = Math.floor(timestamp / 1000);
};
</script>

<template>
  <div
    class="flex items-center text-[12px] border-[1px] py-[2px] px-[6px] border-[var(--line-1)] dark:border-[var(--line-1)] text-[var(--color-black-55)] dark:text-[var(--color-white)]"
    @click="
      onShowTimePickerPop({
        value: +model * 1000,
        onConfirm,
        ignore_columns: props.ignore_columns,
      })
    "
  >
    <span class="inline-flex mr-[6px]">
      {{ dayjs.unix(model).utc().format("YYYY-MM-DD HH:mm:ss") }}
    </span>
    <span>UTC +0</span>
  </div>
</template>

<style lang="scss" scoped></style>
