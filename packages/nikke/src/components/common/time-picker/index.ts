// cpnts
import TimePickerPop from "./pop.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";

export const useTimePickerPop = () => {
  let dialog: any;
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return (dialog = showDialog(
      TimePickerPop,
      Object.assign(options, {
        show: true,
        onClose: () => {
          dialog.unmount();
        },
      }),
    ));
  };
  return {
    show,
  };
};
