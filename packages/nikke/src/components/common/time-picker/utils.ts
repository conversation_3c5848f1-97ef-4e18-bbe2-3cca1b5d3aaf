// types
import { Column, ColumnType, DateObj } from "packages/types/time-picker";
// utils
import dayjs from "dayjs";
import { nextTick, VNodeRef } from "vue";

export const MAX_COLUMN_DISPLAY = 7;
export const COLUMN_HEIGHT = 30;

export const getDateObj = (timestamp: number | undefined) => {
  timestamp = timestamp ? +timestamp : Date.now();

  const obj = dayjs(timestamp).utc();

  const ret = {
    year: obj.get(ColumnType.year),
    // start with 0
    month: obj.get(ColumnType.month) + 1,
    day: obj.get("date"),
    hour: obj.get(ColumnType.hour),
    minute: obj.get(ColumnType.minute),
    second: obj.get(ColumnType.second),
  };

  return ret;
};

export const getDaysInMonth = (year: number, month: number) =>
  dayjs(`${year}-${month}`).daysInMonth();

export const getDisplayLabel = (value: number) => (value < 10 ? `0${value}` : value);

export const getTimestamp = (date_obj: DateObj) => {
  const date = `${date_obj.year}-${date_obj.month}-${date_obj.day} ${date_obj.hour}:${date_obj.minute}:${date_obj.second}`;
  const ret = dayjs.utc(date).valueOf();
  return ret;
};

export const findValueIndex = (options: {
  type: ColumnType;
  date_obj: DateObj;
  columns: Column[];
}) => {
  const { type, date_obj, columns } = options;
  const value = date_obj[type];

  const target = columns.find((column: Column) => column.type === type);
  return target?.items.findIndex((item: number) => item === value) || 0;
};

export const useColumnScrollContainerRef = () => {
  const column_mounted = new Map();

  const columnScrollContainerRef = (
    column_scroll_container: HTMLElement,
    options: {
      type: ColumnType;
      date_obj: DateObj;
      columns: Column[];
    },
  ): VNodeRef | undefined => {
    const { type, date_obj, columns } = options;
    if (column_mounted.get(type)) {
      return;
    }
    column_mounted.set(type, true);

    nextTick(() => {
      const index = findValueIndex({
        type,
        date_obj,
        columns,
      });
      const top = COLUMN_HEIGHT * index;

      column_scroll_container?.scrollTo({
        top,
        behavior: "smooth",
      });
    });
  };

  return {
    columnScrollContainerRef,
  };
};
