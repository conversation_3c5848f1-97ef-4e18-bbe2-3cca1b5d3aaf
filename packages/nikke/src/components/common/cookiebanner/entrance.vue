<script setup lang="ts">
import { useResponsive } from "@/composables/use-responsive";
import { computed, ref } from "vue";
// import SvgIcon from "@/components/common/svg-icon.vue";

const container = ref<HTMLElement | null>(null);

const { is_mobile } = useResponsive();

const offset_eft = computed(() => {
  return is_mobile.value ? -195 : -240;
});

const is_dev = import.meta.env.DEV;
</script>

<template>
  <img
    v-show="!is_dev && false"
    id="ot-sdk-btn"
    ref="container"
    src="@/assets/imgs/common/ot-persistent-cookie-icon.png"
    class="hover:!bg-[color:transparent] ot-sdk-show-settings !p-0 fixed z-[50] bottom-[30px] left-[50%] w-[8px] object-cover !border-none cursor-pointer !h-[40px]"
    :style="{ transform: `translateX(${offset_eft}px)` }"
  />
</template>

<style lang="scss" scoped></style>
