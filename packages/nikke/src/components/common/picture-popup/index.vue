<template>
  <CommBottomPopup :show="visible" @close="emits('close')">
    <template #header>
      <div
        class="font-bold text-[length:16px] leading-[19px] text-[var(--text-1)] p-[20px] pb-[14px]"
      >
        {{ t("insert_picture") }}
      </div>
    </template>

    <ul class="px-[20px] mb-[35px]">
      <li
        class="flex relative items-center px-[19px] py-[9px] bg-[color:var(--fill-3)] border-[length:0.5px] border-solid border-[color:var(--line-1)] box-border cursor-pointer"
      >
        <span class="flex items-center justify-center w-[40px] h-[40px]">
          <SvgIcon name="icon-picture2" color="var(--text-1)" class="w-[34px] h-[34px]"></SvgIcon>
        </span>
        <span class="font-normal text-[length:14px] leading-[16px] text-[var(--text-1)] ml-[40px]">
          {{ t("picture") }}
        </span>
        <Loading v-if="uploading" class="!my-0 ml-[10px]"></Loading>

        <input
          type="file"
          :accept="CONST_SUPPORTED_IMAGE_TYPES.join(',')"
          class="absolute w-[100%] h-[100%] top-0 left-0 opacity-0"
          @change="onFileSelectChange"
        />
      </li>

      <li
        v-if="!isIOS() && !isGameLogin() && is_camera_exists"
        class="flex items-center px-[19px] py-[9px] bg-[color:var(--fill-3)] border-[length:0.5px] border-solid border-[color:var(--line-1)] box-border cursor-pointer mt-[14px]"
        @click="
          () => {
            let camera_dialog = showCamera({
              onConfirm(url: string) {
                camera_dialog.close();
                emits('change', url);
              },
            });
          }
        "
      >
        <span class="flex items-center justify-center w-[40px] h-[40px]">
          <SvgIcon name="icon-camera" color="var(--text-1)" class="w-[40px] h-[40px]"></SvgIcon>
        </span>
        <span class="font-normal text-[length:14px] leading-[16px] text-[var(--text-1)] ml-[40px]">
          {{ t("camera") }}
        </span>
      </li>
    </ul>
  </CommBottomPopup>
</template>

<script setup lang="ts">
// cpnts
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useCamera } from "@/components/common/camera/index.ts";
import Loading from "@/components/common/loading.vue";

// configs
import { CONST_SUPPORTED_IMAGE_TYPES } from "packages/configs/const";

// utils
import { t } from "@/locales";
import { isGameLogin, isIOS, checkCameraExists } from "packages/utils/tools";
import { useImageUpload, ImageUploadErrorToastType } from "@/composables/use-image";
import { onMounted, ref } from "vue";

withDefaults(
  defineProps<{
    visible?: boolean;
  }>(),
  {
    visible: false,
  },
);

const emits = defineEmits(["change", "close"]);
const is_camera_exists = ref(false);
const { show: showCamera } = useCamera();
const { onCheckSizeLimit, onCheckFileType, onErrorToast, onUpload, uploading } = useImageUpload();

const onFileSelectChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (!file || uploading.value) {
    return;
  }

  if (!onCheckFileType(file.type)) {
    input.value = "";
    return onErrorToast(ImageUploadErrorToastType.file_type_not_support);
  }

  if (!onCheckSizeLimit(file.size)) {
    input.value = "";
    return onErrorToast(ImageUploadErrorToastType.exceed_size_limit);
  }

  try {
    uploading.value = true;
    const url = await onUpload(file);
    uploading.value = false;
    emits("change", url);
  } catch (error) {
    input.value = "";
    uploading.value = false;
    onErrorToast(ImageUploadErrorToastType.file_upload_failed);
  }
};

onMounted(async () => {
  is_camera_exists.value = await checkCameraExists();
});
</script>

<style lang="scss" scoped></style>
