<template>
  <div
    :class="[
      `flex w-full max-w-[var(--max-pc-w)] items-center h-[44px] min-h-[44px] justify-between px-[12px] fixed top-0 bg-[length:100%_100%]`,
      bg ? `${bg}` : `bg-[url('@/assets/imgs/points/points-head-bg.png')]`,
      zIndex ? `z-[${zIndex}]` : `z-[50]`,
      bg !== 'bg-[transparent]' && bg ? 'border-b-[1px] border-[color:var(--line-1)] ' : '',
    ]"
  >
    <div class="inline-flex items-center justify-center">
      <div v-if="!nogoBack" class="w-[24px] h-[24px] cursor-pointer" @click="$emit('goback')">
        <SvgIcon name="icon-goback" :color="color ? color : 'var(--color-white)'"></SvgIcon>
      </div>
      <div v-else class="w-[24px] h-[24px] cursor-pointer" @click="$emit('close')">
        <SvgIcon name="icon-pop-close" :color="color ? color : 'var(--color-white)'"></SvgIcon>
      </div>

      <div v-if="goHome" class="w-[24px] h-[24px] cursor-pointer ml-[12px]" @click="onClickHome">
        <SvgIcon name="icon-home" :color="color ? color : 'var(--color-white)'"></SvgIcon>
      </div>
    </div>

    <slot v-if="$slots.title" name="title" :title_class="title_class"></slot>
    <div v-else-if="title" :class="title_class">
      {{ title }}
    </div>

    <!--do not use pointer at top: https://tapd.woa.com/tapd_fe/70114077/bug/detail/1070114077135606443 -->
    <div v-if="$slots.icon" class="flex items-center">
      <slot name="icon"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { RoutesName } from "@/router/routes";
import { computed } from "vue";
import { useRouter } from "vue-router";

const props = defineProps<{
  nogoBack?: boolean;
  title?: string;
  bg?: string;
  color?: string;
  zIndex?: number;
  goHome?: boolean;
}>();

const router = useRouter();

const title_class = computed(() => {
  return [
    `font-[Inter] text-[length:16px] font-bold leading-[19px] text-center flex-1`,
    props.color ? `text-[color:${props.color}]` : "text-[color:var(--color-white)]",
  ];
});

const onClickHome = () => {
  router.push({
    name: RoutesName.HOME,
  });
};

defineEmits(["goback", "textclick", "close"]);
</script>
