<template>
  <div class="flex items-center gap-x-[4px] pl-[3px] cursor-pointer">
    <SvgIcon name="icon-link" color="var(--brand-1)" class="w-[12px] h-[12px]"></SvgIcon>
    <span class="font-normal text-[length:13px] leading-[16px] text-[color:var(--brand-1)]">
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

defineProps<{
  text?: string;
}>();
</script>

<style scoped lang="scss"></style>
