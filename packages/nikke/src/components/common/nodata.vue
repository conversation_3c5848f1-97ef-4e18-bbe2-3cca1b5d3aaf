<template>
  <div
    class="flex flex-col justify-center items-center pl-[9px] box-border my-[32px]"
    @click="emits('click')"
  >
    <template v-if="!noimg">
      <img :src="first ? src2 : src" class="w-[113px] object-contain" />
    </template>

    <p
      v-safe-html="`${text || t('there_is_not_anything')}`"
      class="text-[length:12px] font-normal leading-[14px] text-[color:var(--text-3)] text-center mt-[10px] text-balance"
    ></p>
  </div>
</template>

<script setup lang="ts">
// assets
import src from "@/assets/imgs/common/empty-img.png";
import src2 from "@/assets/imgs/common/empty-img2.png";

// utils
import { t } from "@/locales";

defineProps<{
  text?: string;
  noimg?: boolean;
  first?: boolean;
}>();

const emits = defineEmits(["click"]);
</script>
