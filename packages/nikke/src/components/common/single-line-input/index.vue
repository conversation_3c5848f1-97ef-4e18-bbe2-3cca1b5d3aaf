<template>
  <div class="relative w-full">
    <template v-if="maxLength">
      <input
        v-model="text"
        type="text"
        :placeholder="placeholder"
        class="inline-block w-full h-[34px] rounded-[1px] bg-[color:var(--fill-3)] !border-[0.5px] border-solid border-[color:var(--line-1)] focus:border focus:border-solid focus:border-[color:var(--line-1)] font-normal text-[13px] leading-[16px] text-[color:var(--text-1)] appearance-none m-0 p-0 pl-[12px] pr-[48px] box-border"
        :maxlength="maxLength"
      />
      <span
        class="absolute right-[12px] top-1/2 -translate-y-1/2 font-normal text-[length:11px] leading-[13px] text-[color:var(--text-3)]"
      >
        {{ text.length }}/{{ maxLength }}
      </span>
    </template>
    <template v-else>
      <input
        v-model="text"
        type="text"
        :placeholder="placeholder"
        class="inline-block w-full h-[34px] rounded-[1px] bg-[color:var(--fill-3)] !border-[0.5px] border-solid border-[color:var(--line-1)] focus:border focus:border-solid focus:border-[color:var(--line-1)] font-normal text-[13px] leading-[16px] text-[color:var(--text-1)] appearance-none m-0 p-0 px-[12px] box-border"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  maxLength?: string | number;
  placeholder?: string;
}>();

const text = defineModel<string>({ required: true });
</script>

<style lang="scss" scoped></style>
