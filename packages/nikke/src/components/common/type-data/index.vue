<template>
  <SubHeading :text="t('main_basic_info')" />
  <div
    class="flex justify-between h-[134px] bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-[length:100%_100%] bg-no-repeat mx-[7px] px-[5px] pt-[16px] pb-[12px] mt-[12px]"
  >
    <div
      v-if="is_basic_info_hidden"
      class="w-full h-full flex items-center justify-center text-[color:var(--color-white)]"
    >
      {{ t("shiftyspad_user_set_module_private", [t("main_basic_info")]) }}
    </div>
    <div v-else class="flex flex-wrap w-full">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="w-[33.33%] flex flex-col items-center justify-end"
        :class="[type === 0 && 'mb-[12px]', type === 1 && 'mb-[8px]']"
      >
        <div
          v-if="item?.label"
          class="h-[12px] text-[length:9px] leading-[11px] text-[color:var(--color-white)] bg-[var(--brand-3)] rounded-[1px] flex justify-center items-center px-[4px]"
          :class="[type === 0 && 'mb-[4px]', type === 1 && 'mb-[5px]']"
        >
          {{ item?.label }}
        </div>
        <div
          class="!font-[DINNextLTProBold] text-[color:var(--color-white)] leading-[1] text-nowrap"
          :class="[
            type === 0 && 'text-[length:16px] h-[11px]',
            type === 1 && 'text-[length:20px] h-[20px]',
            (item.value + '').length > 5 ? 'scale-[0.84]' : '',
          ]"
        >
          {{ item?.value }}
        </div>
        <div
          class="text-[color:var(--color-white)]"
          :class="[
            type === 0 && 'mt-[5px] text-[length:9px] leading-[11px] whitespace-nowrap',
            type === 1 && 'text-[length:13px] leading-[16px] whitespace-nowrap',
            isMobile && getLang() === 'ja' && '!text-[length:7px]',
          ]"
        >
          {{ item?.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getLang } from "@/shiftyspad/const";
import { isMobileDevice } from "packages/utils/tools";
import { UserBasicInfo, UserBattleInfo } from "packages/types/shiftyspad";
import { usePlayerBasicInfo } from "@/shiftyspad/composable/basic-info";

import { computed, toRefs } from "vue";
import { useI18n } from "vue-i18n";

import SubHeading from "@/shiftyspad/components/main/sub/sub-heading.vue";

const isMobile = isMobileDevice();

const props = withDefaults(
  defineProps<{
    type?: 0 | 1;
    user_battle_info: UserBattleInfo | null;
    user_basic_info: UserBasicInfo | null;
  }>(),
  {
    user_battle_info: null,
    user_basic_info: null,
    type: 0,
  },
);

const { user_battle_info, user_basic_info } = toRefs(props);
const {
  hard_progress,
  nikke_num,
  normal_progress,
  costume,
  avatar_frame,
  tower_floor,

  is_basic_info_hidden,
} = usePlayerBasicInfo({ user_basic_info, user_battle_info });
const { t } = useI18n();

const list = computed(() => {
  return [
    {
      label: "",
      value: tower_floor.value,
      title: t("main_tower"),
    },
    {
      label: "NORMAL",
      value: normal_progress.value,
      title: t("main_stage"),
    },
    {
      label: "HARD",
      value: hard_progress.value,
      title: t("main_stage"),
    },
    {
      label: "",
      value: nikke_num.value,
      title: t("main_nikke_num"),
    },
    {
      label: "",
      value: avatar_frame.value,
      title: t("main_avatar_frame"),
    },
    {
      label: "",
      value: costume.value,
      title: t("main_costume"),
    },
  ];
});
</script>
