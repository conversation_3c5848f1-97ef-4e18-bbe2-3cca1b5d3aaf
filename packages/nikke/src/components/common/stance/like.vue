<script setup lang="ts">
// cpnt
import { CommonImage } from "@/components/common/image";
import SvgIcon from "@/components/common/svg-icon.vue";
// assets
import LikeGif from "@/assets/imgs/post/detail/like.gif";
// utils
import { computed, watch } from "vue";
import { useStanceGift } from "@/composables/use-stance";

const props = withDefaults(
  defineProps<{
    is_star?: boolean;
    auto_increase_gift_progress?: boolean;
    icon_class?: string;
    only_icon?: boolean;
    direction?: "row" | "col";
    value?: any;
  }>(),
  {
    is_star: false,
    auto_increase_gift_progress: true,
    icon_class: "w-[20px] h-[20px]",
    only_icon: false,
    direction: "row",
    value: "",
  },
);

const emits = defineEmits(["click"]);

const { is_reaching_gift_progress, increaseGiftProgress, resetGiftProgress, startGiftTimer } =
  useStanceGift({
    gift_duration: 600,
  });

const is_gift_visible = computed(() => {
  return props.is_star && is_reaching_gift_progress.value;
});

const onClick = () => {
  // 动画期间，需要等待
  if (is_gift_visible.value) {
    return;
  }
  props.auto_increase_gift_progress && increaseGiftProgress();
  emits("click");
};

watch(
  () => props.is_star,
  (new_value) => {
    if (new_value) {
      increaseGiftProgress();
      startGiftTimer();
      return;
    }
    resetGiftProgress();
  },
);

defineExpose({
  increaseGiftProgress,
});
</script>

<template>
  <span
    v-click-interceptor.need_login.mute.sign_privacy="onClick"
    :class="[
      `inline-flex items-center justify-center cursor-pointer`,
      direction === 'row' ? 'flex-row' : 'flex-col',
    ]"
  >
    <span :class="[`inline-flex items-center justify-center relative`, icon_class]">
      <CommonImage
        v-show="is_gift_visible"
        :src="LikeGif"
        alt="img"
        class="absolute top-[1px] left-0 z-10 w-full h-full transform scale-[1.4]"
      />
      <SvgIcon
        v-show="!is_gift_visible"
        :name="is_star ? 'icon-line-like-cur' : 'icon-line-like'"
        :color="is_star ? `var(--brand-1)` : `var(--text-3)`"
        class="w-full h-full"
      />
    </span>

    <span
      v-if="!only_icon"
      :class="[
        `text-[length:12px] font-normal leading-[14px] text-[color:var(--text-3)]`,
        direction === 'row' ? 'ml-[4px] mt-[2px] min-w-[20px]' : 'mt-[8px]',
      ]"
    >
      {{ value }}
    </span>
  </span>
</template>

<style lang="scss" scoped></style>
