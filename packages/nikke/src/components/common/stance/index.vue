<script setup lang="ts">
// cpnt
import { CommonImage } from "@/components/common/image";
// types
import { StanceType, StanceItem } from "packages/types/stance";
// assets
import Like from "@/assets/imgs/post/detail/stance-like-new.png";
import HeartEyes from "@/assets/imgs/post/detail/stance-heart-eyes-new.png";
import Surprised from "@/assets/imgs/post/detail/stance-surprised-new.png";
import Fulfilled from "@/assets/imgs/post/detail/stance-fulfilled-new.png";
import Cute from "@/assets/imgs/post/detail/stance-cute-new.png";

import LikeGif from "@/assets/imgs/post/detail/stance-like-3x-new.gif";
import HeartEyesGif from "@/assets/imgs/post/detail/stance-heart-eyes-3x-new.gif";
import SurprisedGif from "@/assets/imgs/post/detail/stance-surprised-3x-new.gif";
import FulfilledGif from "@/assets/imgs/post/detail/stance-fulfilled-3x-new.gif";
import CuteGif from "@/assets/imgs/post/detail/stance-cute-3x-new.gif";
// utils
import { useResponsive } from "@/composables/use-responsive";
import { computed, watch } from "vue";
import { get } from "lodash-es";
import { abbrNumn } from "packages/utils/tools";
import { useStanceGift } from "@/composables/use-stance";

const props = withDefaults(
  defineProps<{
    type?: number;
    nums?: {
      like: number;
      heart_eyes: number;
      surprised: number;
      fulfilled: number;
      cute: number;
    };
  }>(),
  {
    type: StanceType.unset,
  },
);

const emits = defineEmits(["stance"]);

const { is_mobile } = useResponsive();
const { is_reaching_gift_progress, increaseGiftProgress, startGiftTimer } = useStanceGift();

const isGifVisible = (item: StanceItem) => {
  return item.gif && item.type === props.type && is_reaching_gift_progress.value;
};

const list = computed(() => {
  const nums = props.nums;
  return [
    {
      src: Like,
      gif: LikeGif,
      num: get(nums, "like", 0),
      type: StanceType.like,
    },
    {
      src: HeartEyes,
      gif: HeartEyesGif,
      num: get(nums, "heart_eyes", 0),
      type: StanceType.heart_eyes,
    },
    {
      src: Surprised,
      gif: SurprisedGif,
      num: get(nums, "surprised", 0),
      type: StanceType.surprised,
    },
    {
      src: Fulfilled,
      gif: FulfilledGif,
      num: get(nums, "fulfilled", 0),
      type: StanceType.fulfilled,
    },
    {
      src: Cute,
      gif: CuteGif,
      num: get(nums, "cute", 0),
      type: StanceType.cute,
    },
  ].map((item) => {
    return {
      ...item,
      gif_visible: isGifVisible(item),
    };
  });
});

const onStance = (item: any) => {
  // 动画期间，需要等待
  if (is_reaching_gift_progress.value) {
    return;
  }
  increaseGiftProgress();
  emits("stance", item);
};

watch(
  () => props.type,
  (new_value) => {
    if (new_value !== StanceType.unset) {
      increaseGiftProgress();
      startGiftTimer();
      return;
    }
  },
);
</script>

<template>
  <div class="mb-[15px] overflow-x-auto h-[90px] flex justify-between items-center">
    <div
      v-for="(item, index) in list"
      :key="index"
      v-click-interceptor.need_login.mute.sign_privacy="() => onStance(item)"
      :class="[
        `cursor-pointer flex items-center justify-center border-[1px] rounded-[4px] text-[length:10px] leading-[12px] max-w-max h-[26px] px-[7px] mr-[5px] last-of-type:mr-0 relative pl-[43px]`,
        type === item.type
          ? `text-[color:var(--brand-1)] border-[color:var(--brand-1)] bg-[var(--brand-1-20)]`
          : `text-[color:var(--text-3)] border-[transparent] bg-[var(--op-fill-white)]`,
        is_mobile ? 'min-w-[65px]' : 'min-w-[78px]',
      ]"
    >
      <CommonImage
        v-show="item.gif_visible"
        :src="item.gif"
        class="w-[53px] h-[60px] absolute bottom-0 -left-[4px] z-10"
      />
      <CommonImage
        v-show="!item.gif_visible"
        :src="item.src"
        class="w-[40px] h-[42px] absolute bottom-0 left-0"
      />
      <span class="mt-[4px]">{{ abbrNumn(+item.num) }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
