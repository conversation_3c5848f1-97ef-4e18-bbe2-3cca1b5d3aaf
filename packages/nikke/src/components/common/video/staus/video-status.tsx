// cpnts
import Loading from "@/components/common/loading.vue";
import { CommonImage } from "@/components/common/image";
// assets
import EmptyImg from "@/assets/imgs/common/empty-img.png";
// utils
import { ref } from "vue";
import { t } from "@/locales/index";

export enum VideoStatus {
  loading = "loading",
  error = "error",
  timeout = "timeout",
  ready = "ready",
}

export const useVideoStaus = () => {
  const video_status = ref(VideoStatus.loading);
  const timer = ref();

  const onTimeoutSniff = () => {
    timer.value = setTimeout(() => {
      setVideoStatus(VideoStatus.timeout);
    }, 15 * 1000);
  };

  const setVideoStatus = (new_video_status: VideoStatus) => {
    video_status.value = new_video_status;
    clearTimeout(timer.value);
  };

  onTimeoutSniff();

  return { video_status, setVideoStatus };
};

export const useVideoStatusCpnt = (props: { status: VideoStatus }) => {
  if (props.status === VideoStatus.ready) {
    return;
  }
  return (
    <div class="flex w-full h-full items-center justify-center bg-white flex-col absolute inset-0  z-10">
      {props.status === VideoStatus.loading ? (
        <Loading></Loading>
      ) : (
        <>
          <CommonImage
            class="h-[40%] max-h-[130px] object-cover mb-[12px] justify-center"
            src={EmptyImg}
          ></CommonImage>
          <span class="max-w-[70%] text-center justify-start text-neutral-400 text-xl font-normal font-[DINNextLTPro] leading-relaxed">
            {props.status === VideoStatus.timeout
              ? t("video_not_supported_on_this_network")
              : t("video_loading_failed")}
          </span>
        </>
      )}
    </div>
  );
};
