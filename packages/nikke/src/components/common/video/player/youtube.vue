<template>
  <div
    :class="[
      'rounded-lg overflow-hidden relative',
      is_youtubeshort ? 'w-[288px] h-[512px] mx-auto' : 'aspect-video w-full h-full',
    ]"
  >
    <div ref="video_player_ref" :class="['w-full h-full']"></div>
    <component :is="useVideoStatusCpnt({ status: video_status })"></component>
  </div>
</template>

<script setup lang="tsx">
// types
import { YoutubePlayerProps } from "packages/types/video";
import { Platform } from "packages/types/common";
// utils
import { Embed } from "@tencent/pa-cms-utils";
import { ref, watch, computed, onMounted, onUnmounted, onActivated } from "vue";
import { formatDuration } from "packages/utils/tools";
import { useNoCookie } from "packages/utils/cms";
import { useVideoStatusCpnt, useVideoStaus, VideoStatus } from "../staus/video-status";

type ResAwaitedType<T extends (...args: any) => any> =
  ReturnType<T> extends Promise<infer U> ? U : never;

const props = withDefaults(defineProps<YoutubePlayerProps>(), {
  autoplay: false,
  loop: false,
  retry: 3,
  platform: Platform.youtube,
});

const emits = defineEmits(["error", "ready", "state-change", "play"]);

const { video_status, setVideoStatus } = useVideoStaus();

const video_player_ref = ref();
const video_player_instance = ref<ResAwaitedType<typeof Embed.youtube.init> | null>(null);
const retry_count = ref(0);

const video_id = computed(() => {
  // 如果直接传递 vid，则使用 vid
  if (props.vid) {
    return props.vid;
  }
  // 如果通过 src 传递，则使用 src 的 videoId
  const regex = /videoId=([\w-]+)/;
  const match = props.src?.match(regex);
  return match ? match[1] : "";
});

const is_youtubeshort = computed(() => {
  return props.platform === Platform.youtubeshort;
});

const initPlayer = async () => {
  try {
    if (!video_player_ref.value) {
      return;
    }

    video_player_instance.value = await Embed.youtube.init({
      element: video_player_ref.value,
      videoId: video_id.value,
      autoplay: props.autoplay,
      loop: props.loop,
      nocookie: useNoCookie(),
    });

    if (video_player_instance.value) {
      video_player_instance.value.on("error", onError);
      video_player_instance.value.on("ready", onReady);
      video_player_instance.value.on("playing", () => emits("play"));
      video_player_instance.value.on("state-change", () => emits("state-change"));
    }
  } catch (error: any) {
    console.error("[YoutubePlayer] initPlayer error", error);
    emits("error");
  }
};

// 视频加载失败，进行重试
const onError = (event: any) => {
  setVideoStatus(VideoStatus.error);

  const data = event?.data || "";
  // 达到最大的重试次数
  // 或者是["无效视频号"、"视频被删除"、"不允许被播放"、"bot check"]
  // 将不会执行重试
  if (retry_count.value >= props.retry || ["2", "100", "101", "150"].includes(String(data))) {
    return;
  }

  retry_count.value += 1;
  emits("error");
  destroyPlayer();
  initPlayer();
};

const onReady = () => {
  setVideoStatus(VideoStatus.ready);

  const meta = { duration: "" };
  meta.duration = formatDuration(video_player_instance.value?.duration || 0);
  emits("ready", meta);
  if (props.fake) {
    // 获取原信息后注销播放器
    destroyPlayer();
  }
};

const stopPlayer = () => {
  video_player_instance.value?.player?.stopVideo?.();
};

const destroyPlayer = () => {
  video_player_instance.value?.player?.destroy?.();
  video_player_instance.value = null;
};

watch(
  () => video_id.value,
  () => {
    destroyPlayer();
    initPlayer();
  },
);

onMounted(initPlayer);
onActivated(() => {
  setVideoStatus(VideoStatus.loading);
});
onUnmounted(destroyPlayer);
defineExpose({
  stopPlayer,
  destroyPlayer,
});
</script>

<style lang="scss" scoped></style>
