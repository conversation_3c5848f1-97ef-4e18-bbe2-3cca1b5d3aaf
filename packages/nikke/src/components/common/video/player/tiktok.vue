<template>
  <div class="rounded-lg overflow-hidden relative w-full">
    <div :id="id" ref="tiktok_player" class="w-full flex justify-center"></div>
    <component :is="useVideoStatusCpnt({ status: video_status })"></component>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, onActivated } from "vue";
import EMBED from "@tencent/webibi-embed";
import { Platform } from "packages/types/common";
import { useVideoStatusCpnt, useVideoStaus, VideoStatus } from "../staus/video-status";

const tiktok_player = ref<HTMLElement>();
const embed = ref();
const { video_status, setVideoStatus } = useVideoStaus();

const id = `embed-tiktok-${Math.random()}-${Date.now()}`;

const props = defineProps<{
  width?: number;
  height?: number;
  vid: string;
}>();

/**
 * @link https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation
 * @param event
 */
const onMessage = (event: MessageEvent) => {
  if (event.origin === "https://www.tiktok.com") {
    const data = event.data;
    const map_video_status = {
      onPlayerReady: VideoStatus.ready,
      onError: VideoStatus.error,
    };
    const video_status = map_video_status[data.type as keyof typeof map_video_status];
    video_status && setVideoStatus(video_status);
  }
};
const init = async () => {
  embed.value = await EMBED.init({
    platform: Platform.tiktok,
    element: tiktok_player.value,
    videoId: props.vid,
    width: props.width || 288,
    height: props.height || 512,
  });
  // 监听来自 iframe 的消息
  window.addEventListener("message", onMessage);
};

const reset = () => {
  if (tiktok_player.value) {
    embed.value.player.destroy?.();
    tiktok_player.value.innerHTML = "";
  }
  window.removeEventListener("message", onMessage);
};

watch(
  () => props.vid,
  () => {
    reset();
    init();
  },
);

onMounted(init);
onActivated(() => {
  setVideoStatus(VideoStatus.loading);
});
onUnmounted(() => {
  reset();
});
</script>
