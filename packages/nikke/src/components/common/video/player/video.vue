<template>
  <video controls :poster="cover" class="max-h-[512px] mx-auto">
    <source :src="video_url" :type="`video/${video_type}`" />
  </video>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  video_url: string;
  video_type: "mp4";
  cover_url?: string;
}>();

const is_valid_cover = (cover_url: string) => {
  if (!cover_url) return;
  try {
    new URL(cover_url);
    return true;
  } catch (e) {
    return false;
  }
};

const cover = computed(() => {
  if (is_valid_cover(props.cover_url || "")) {
    return props.cover_url;
  }
  return undefined;
});
</script>
