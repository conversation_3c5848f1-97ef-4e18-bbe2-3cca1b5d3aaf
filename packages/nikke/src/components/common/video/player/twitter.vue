<template>
  <div ref="player" class="w-full flex justify-center relative">
    <div
      v-if="has_cover_and_url"
      class="left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform absolute w-[60px] h-[60px] bg-[rgba(0,0,0,0.5)] rounded-full flex justify-center items-center pointer-events-none"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24">
        <path
          fill="#ffffff"
          d="M6 4v16a1 1 0 0 0 1.524.852l13-8a1 1 0 0 0 0-1.704l-13-8A1 1 0 0 0 6 4"
        />
      </svg>
    </div>
    <img
      v-if="has_cover_and_url"
      :src="cover_url"
      class="max-h-[512px] mx-auto cursor-pointer"
      alt=""
      @click="jump"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import EMBED from "@tencent/webibi-embed";
import { Platform } from "packages/types/common";

const player = ref<HTMLElement>();
const embed = ref();

const props = defineProps<{
  width?: number;
  height?: number;
  /** 封面图 */
  cover_url?: string;
  /** 跳转地址 */
  original_url?: string;
  /** 优先使用封面图和跳转地址，否则尝试解析 vid 并渲染twitter卡片 */
  vid?: string;
}>();

const has_cover_and_url = computed(() => {
  return props.cover_url && props.original_url;
});

onMounted(async () => {
  if (has_cover_and_url.value || !props.vid) {
    return;
  }
  embed.value = await EMBED.init({
    platform: Platform.twitter,
    element: player.value,
    videoId: props.vid,
    width: props.width,
    height: props.height,
  });
});

onUnmounted(() => {
  if (player.value) {
    embed.value.player.destroy?.();
    player.value.innerHTML = "";
  }
});

const jump = () => {
  if (has_cover_and_url.value) {
    window.open(props.original_url, "_blank");
  }
};
</script>
