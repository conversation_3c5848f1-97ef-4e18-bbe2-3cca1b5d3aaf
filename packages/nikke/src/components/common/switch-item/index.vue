<template>
  <div
    class="flex items-center justify-between py-[14px] pl-[20px] pr-[12px] bg-[var(--op-fill-white)]"
  >
    <span class="font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)]">
      {{ title }}
    </span>
    <Switch :disabled="disable" :checked="isSwitch" @update:checked="handleChange" />
  </div>
</template>

<script setup lang="ts">
import { Switch } from "@/components/ui/switch";
import { ref, toRefs, watch } from "vue";
const emit = defineEmits(["change"]);
const props = withDefaults(
  defineProps<{
    title?: string;
    disable?: boolean;
    defaultSwitch?: boolean;
    item: unknown;
  }>(),
  {
    defaultSwitch: false,
  },
);
const { defaultSwitch, disable } = toRefs(props);
const model = defineModel();
const isSwitch = ref(defaultSwitch.value);
watch(defaultSwitch, (val) => {
  isSwitch.value = val;
});
console.log("handleChange", isSwitch);
const handleChange = (e: boolean) => {
  console.log("handleChange", e, props.item);
  isSwitch.value = e;
  model.value = e;
  emit("change", props.item);
};
</script>

<style lang="scss" scoped></style>
