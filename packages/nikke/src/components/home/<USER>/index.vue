<template>
  <div class="w-[full] relative transform" :style="{ aspectRatio: props.aspectRatio || '16/9' }">
    <Swiper
      v-if="list.length > 0"
      class="!w-[full]"
      :class="{
        'cursor-pointer': list[0]?.ext_info, // 兜底图不显示跳转鼠标
      }"
      :loop="true"
      :index="0"
      :autoplay="list.length > 1"
      :duration="5000"
    >
      <div v-for="(item, index) in list" :key="index" class="w-full h-full" @click="onClick(item)">
        <CommonImage
          class="w-full h-full"
          image_class="w-full h-full"
          fetchpriority="high"
          :src="item.pic_urls[0]"
          :thumbnail="true"
          :auto_dethumbnail="true"
        ></CommonImage>
      </div>

      <template #pagination>
        <SwiperPagination
          v-if="list.length > 1"
          :prev_visible="true"
          :next_visible="true"
          class="absolute left-0 right-0"
          :class="is_mobile ? 'top-[173px]' : 'top-[220px]'"
        ></SwiperPagination>
      </template>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from "vue";
import { Swiper, SwiperPagination } from "@/components/common/swiper";
import { CommonImage } from "@/components/common/image/index";
import { useCms } from "@/composables/use-cms.ts";
import type { IDetail } from "@tencent/pa-cms-utils";
import { report } from "packages/utils/tlog.ts";
import { isMobileDevice } from "packages/utils/tools";
// import DefaultImg from "@/assets/imgs/home/<USER>";

const props = defineProps<{
  list: IDetail[];
  /**
   * 宽高比
   * - 默认宽高比为 16/9
   */
  aspectRatio?: string;
}>();
const curIndex = ref(0);

watch(
  () => props.list,
  () => {
    reportBannerShow(0);
  },
);

const is_mobile = isMobileDevice();
const { useCmsJump } = useCms();

const onClick = (item: IDetail) => {
  const target = getReportItem();
  report.standalonesite_banner_click.cm_click(target);
  if (!item.jump_link_info && !item.jump_scheme) return;
  useCmsJump(item);
};

const getReportItem = () => {
  const target = props.list[curIndex.value];
  return {
    location: curIndex.value,
    content_id: target.content_id,
    title: target.title,
  };
};

const reportBannerShow = (index: number) => {
  curIndex.value = index;
  const item = getReportItem();
  report.standalonesite_banner_expose.cm_vshow(item);
};
</script>
