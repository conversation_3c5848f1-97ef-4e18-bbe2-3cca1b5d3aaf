<template>
  <div
    class="flex justify-between items-center mx-[15px] pb-[12px] border-b-[1px] border-[var(--line-1)] h-[24px] header"
  >
    <div
      class="text-[color:var(--text-1)] text-[length:var(--font-size-langer)] leading-[1] flex-1 flex items-center"
    >
      <svgIcon
        v-if="!anniversary"
        name="icon-post"
        class="w-[24px] h-[24px] mr-[2px] p-[3px]"
        color="var(--text-1)"
      ></svgIcon>
      <div v-else class="icon-post w-[24px] h-[24px] mr-[2px] p-[3px]"></div>
      <span class="font-Abolition text-post">{{ title || t("posts") }}</span>
    </div>
    <div class="flex items-center justify-end">
      <div v-if="showRegion" class="flex items-center justify-end mr-[6px]">
        <Switch
          :checked="allRegion"
          :class="{ 'switch-bg': anniversary }"
          @update:checked="switchChange"
        />
        <div
          class="text-[color:var(--text-3)] text-[length:9px] leading-[11px] ml-[4px] mt-[2px]"
          :class="{ 'switch-text': anniversary }"
        >
          {{ t("all_regions") }}
        </div>
      </div>

      <DropdownNormal
        v-if="showOrderBy"
        :list="orderList"
        :active="orderBy"
        side="bottom"
        align="end"
        @change="onOrderChange"
      >
        <template #trigger="{ item }">
          <SelectHead :text="item.name" />
        </template>
      </DropdownNormal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import { Switch } from "@/components/ui/switch";
import { OrderBy } from "packages/types/post";
import { useI18n } from "vue-i18n";
import { report } from "packages/utils/tlog";
import { storeToRefs } from "pinia";
import { useHomeStore } from "@/store/home/<USER>";
import { useAnniversary } from "@/store/home/<USER>";
import svgIcon from "@/components/common/svg-icon.vue";

const { anniversary } = storeToRefs(useAnniversary());

const { t } = useI18n();
const { activeId, activeKey } = storeToRefs(useHomeStore());

const orderList = ref([
  { name: t("hot"), value: 2 },
  { name: t("latest"), value: 1 },
]);

withDefaults(
  defineProps<{
    showRegion?: boolean;
    allRegion?: boolean;
    orderBy: OrderBy;
    title?: string;
    showOrderBy?: boolean;
  }>(),
  {
    showRegion: false,
    allRegion: false,
    orderBy: 2,
    title: "",
    showOrderBy: true,
  },
);

const emit = defineEmits(["regionChange", "orderChange"]);

const switchChange = (val: boolean) => {
  emit("regionChange", val);
};

const onOrderChange = (val: { name: string; value: number | string }) => {
  const target = orderList.value.find((item) => item.value === val.value)!;
  report.standalonesite_filter_btn.cm_click({
    btn_name: target.name,
    label_id: +activeId.value,
    label_name: activeKey.value,
  });
  emit("orderChange", val.value as OrderBy);
};
</script>
