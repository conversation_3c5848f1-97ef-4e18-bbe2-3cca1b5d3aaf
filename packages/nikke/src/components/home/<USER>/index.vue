<template>
  <div
    ref="container_ref"
    class="w-full cursor-pointer break-inside-avoid relative bg-[color:var(--op-fill-white)] shadow-[0_0_10px_0_var(--op-shadow-black-5)] z-[1] overflow-hidden rounded-[4px]"
    @click="goToPostDetail()"
  >
    <div class="relative">
      <div v-if="!is_video" class="w-full relative">
        <CommonImage
          class="w-full object-cover min-h-[50px]"
          image_class="w-full object-cover min-h-[50px]"
          :thumbnail="true"
          :auto_dethumbnail="false"
          :thumbnail_quality="is_mobile ? 0 : 30"
          :loading="true"
          :image_style="{
            aspectRatio: `${image_display_ratio}`,
          }"
          :src="item.fe_image_info.url"
          alt=""
        />
        <template v-if="item.pic_urls.length > 1">
          <MorePicNum :num="item.pic_urls.length"></MorePicNum>
        </template>
      </div>

      <template v-else>
        <CommonImage
          :thumbnail="true"
          :auto_dethumbnail="false"
          :thumbnail_quality="is_mobile ? 0 : 30"
          :loading="true"
          :src="item.fe_image_info.url"
          :image_style="{
            aspectRatio: `${image_display_ratio}`,
          }"
          :class="[`w-full min-h-[50px]`, is_tiktok_video ? `object-contain` : `object-cover`]"
        />

        <div
          class="absolute z-[10] top-[8px] right-[8px] w-[16px] h-[16px] bg-[length:100%_100%]"
          :style="{
            backgroundImage: `url(${is_tiktok_video ? play_icon_tiktok : play_icon_youtube})`,
          }"
        ></div>
      </template>

      <AuthoingStatementTipsCpnt
        class="absolute bottom-0"
        :ai_content_type="item.ai_content_type"
        :risk_remind_type="item.risk_remind_type"
      ></AuthoingStatementTipsCpnt>
    </div>

    <div class="py-[12px] px-[10px]">
      <div
        class="line-clamp-2 text-[color:var(--text-1)] text-[length:13px] leading-[16px] font-medium"
      >
        {{ item.title }}
      </div>
      <div class="flex items-center justify-between mt-[8px]">
        <div class="flex flex-[1] w-[1px] items-center" @click.stop="goToUserCenter">
          <Avatar
            :src="item.user?.avatar"
            :auth_type="item.user?.auth_type"
            :frame="item.user?.avatar_pendant"
            class="flex-shrink-0 !w-[20px] !h-[20px] rounded-full mr-[6px]"
          />
          <div class="truncate flex-1 text-[color:var(--text-2)] text-[length:12px] mt-[4px]">
            {{ item.user.username }}
          </div>
        </div>
        <ButtonIconText
          v-click-interceptor.need_login.mute.sign_privacy.stop="triggerStar"
          class="flex-shrink-0 justify-end ml-[2px]"
          icon="icon-line-like"
          :is_like_icon="true"
          :text="formatNumber(item.upvote_count)"
          :is-active="item.my_upvote?.is_star"
          active-icon="icon-line-like-cur"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import ButtonIconText from "@/components/common/button-icon-text/index.vue";
import MorePicNum from "@/components/common/more-pic-num/index.vue";
import { AuthoingStatementTipsCpnt } from "@/views/post/detail/authoring-statement-atomic.tsx";
// import { YoutubePlayer, TiktokPlayer } from "@/components/common/video/player/index";
import { PostItem } from "packages/types/post";
import { Routes } from "@/router/routes";
import Avatar from "@/components/common/avatar/index.vue";
import { CommonImage } from "@/components/common/image/index";
import { report } from "packages/utils/tlog";
import { useScrollVisible } from "@/composables/use-scroll-visible";
import { usePostItem } from "@/composables/use-post";
import { formatNumber } from "packages/utils/tools";
import { isMobileDevice } from "packages/utils/tools";

import play_icon_tiktok from "@/assets/imgs/home/<USER>";
import play_icon_youtube from "@/assets/imgs/home/<USER>";
// import play_icon_facebook from "@/assets/imgs/home/<USER>";

const props = withDefaults(
  defineProps<{
    index?: number; // 索引
    need_expose?: boolean; // 是否曝光上报
    plate_id?: number; // 板块id
    plate_name?: string; // 板块名称
    item: PostItem;
  }>(),
  {
    need_expose: true,
    index: 0, // 索引
    plate_id: 0, // 板块id
    plate_name: "", // 板块名称
  },
);

const emit = defineEmits(["star", "detail", "collection"]);

const is_mobile = isMobileDevice();
const router = useRouter();
const container_ref = ref<HTMLElement | null>(null);
const { visible } = useScrollVisible(container_ref);

const { useUsePostItem, getPostItemImageDisplaySize } = usePostItem();
const { is_video, is_tiktok_video } = useUsePostItem(props.item);
const image_display_ratio = computed(() => {
  const image_display_size = getPostItemImageDisplaySize(props.item);
  return image_display_size.width / image_display_size.height;
});
// 获取上报数据
const getReportData = () => {
  return {
    location: props.index,
    content_id: props.item.post_uuid,
    label_id: props.plate_id,
    label_name: props.plate_name,
  };
};

// 点赞
const triggerStar = () => {
  emit("star", props.item);

  report.standalonesite_news_praise_btn.cm_click({
    ...getReportData(),
    is_praise: props.item?.my_upvote?.is_star ? 0 : 1,
  });
};

const goToPostDetail = () => {
  router.push({
    path: Routes.POST_DETAIL,
    query: { post_uuid: props.item.post_uuid },
  });
  emit("detail", props.item);

  report.standalonesite_news_item_click.cm_click(getReportData());
};

const goToUserCenter = () => {
  router.push({
    path: Routes.USER,
    query: { openid: props.item.user.intl_openid },
  });
};

watch(
  () => visible.value,
  (val: boolean) => {
    if (!props.need_expose) return;
    // 已经曝光过的，忽略
    if (val && !props.item.is_exposed) {
      report.standalonesite_news_item_expose.cm_vshow(getReportData());
      props.item.is_exposed = true;
    }
  },
);
</script>
