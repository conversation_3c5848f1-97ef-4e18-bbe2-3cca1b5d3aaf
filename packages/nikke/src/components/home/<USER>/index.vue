<template>
  <div
    class="w-full px-[12px] h-[44px] bg-[var(--fill-0)] z-30 sticky top-0 left-0 flex items-center nav-bg"
  >
    <div
      class="h-[18px] w-[94px] bg-[url('@/assets/imgs/home/<USER>')] bg-[length:100%_100%] bg-no-repeat mr-[6px] flex-shrink-0 logo-bg cursor-pointer"
      @click="onLogoClick"
    ></div>

    <SearchBox
      class="flex-1"
      :is-anniversary="isAnniversary"
      @focus="router.push(Routes.SEARCH)"
      @click-search="router.push(Routes.SEARCH)"
    ></SearchBox>

    <div class="w-[24px] h-[24px] ml-[12px] flex-shrink-0 cursor-pointer" @click="homescreeClick">
      <div v-if="!isAnniversary">
        <SvgIcon v-if="showed_add_to_screen" name="icon-iphone" color="var(--text-1)"></SvgIcon>
        <img v-else :src="add_to_screen_gif" />
      </div>
      <div v-else class="w-[24px] h-[24px] flex-shrink-0 cursor-pointer">
        <div v-if="showed_add_to_screen" class="icon-phone"></div>
        <img v-else :src="add_to_screen_ann_gif" />

        <!-- <div v-if="showed_add_to_screen" class="icon-phone"></div> -->
      </div>
    </div>

    <div
      class="w-[24px] h-[24px] ml-[12px] relative z-[1] flex-shrink-0 cursor-pointer p-[2px]"
      @click="messageClick"
    >
      <div v-if="isAnniversary" class="icon-notification"></div>
      <SvgIcon v-else name="icon-line-notification" color="var(--text-1)"></SvgIcon>
      <i
        v-if="is_unread"
        class="absolute block z-[2] top-[2px] right-[3px] w-[8px] h-[8px] bg-[color:var(--error)] rounded-full border-[1.5px] border-[color:var(--fill-0)]"
      ></i>
    </div>

    <div class="ml-[12px] w-[28px] h-[28px] flex-shrink-0 relative z-[1] cursor-pointer">
      <Avatar
        :src="avatar"
        :auth_type="user_info.auth_type"
        :frame="user_info.avatar_pendant"
        :class="is_login && '!border-[color:var(--brand-1)]'"
        @click="userCenterClick"
      ></Avatar>
      <div
        v-if="sign_in_visible"
        class="absolute -z-[1] top-[22px] -right-[9px] h-[68px] pl-[57px] pr-[10px] cursor-pointer min-w-[110px] text-center"
        @click="onCheckIn"
      >
        <i
          class="w-[74px] h-[68px] -z-[1] absolute top-0 left-0 bg-[url('@/assets/imgs/home/<USER>')] bg-[length:100%_100%]"
        ></i>
        <i
          class="w-[calc(100%_-_108px)] h-[68px] -z-[1] absolute top-0 left-[73px] bg-[url('@/assets/imgs/home/<USER>')] bg-[length:100%_100%]"
        ></i>
        <i
          class="w-[36px] h-[68px] -z-[1] absolute top-0 right-0 bg-[url('@/assets/imgs/home/<USER>')] bg-[length:100%_100%]"
        ></i>
        <div
          class="font-bold text-[length:12px] font-[InterBold] mt-[28px] leading-[15px] text-[color:var(--other-6)] whitespace-nowrap"
        >
          {{ t("check_in") }}
        </div>
      </div>
    </div>
  </div>
  <!-- 抽取迁移 -->
  <IsHomescree
    :show="is_homescree"
    @close="
      () => {
        is_homescree = false;
        route.query.install && router.replace({});
      }
    "
  ></IsHomescree>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
// import { isInIntlBrowser, openInBrowser } from "@tencent/pa-ingame-utils";

import install from "@/boot/pwa-install";
import { useUser } from "@/store/user";
import { useSetting } from "@/store/setting";
import { getTasks } from "@/api/rewards";
import { TaskType, Task } from "packages/types/rewards";
// import { isMobileDevice } from "packages/utils/tools";

import Avatar from "@/components/common/avatar/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import SearchBox from "@/components/common/search-box/index.vue";
import IsHomescree from "@/components/home/<USER>/index.vue";
import add_to_screen_gif from "@/assets/imgs/home/<USER>";
import add_to_screen_ann_gif from "@/assets/imgs/year2-5/icon-phone-dot.gif";

import { useTask } from "@/components/points/home/<USER>/composition";
import { Routes } from "@/router/routes";
import { storeToRefs } from "pinia";
import { debounce } from "lodash-es";
import { report } from "packages/utils/tlog";
import { useLocalStorage } from "@vueuse/core";
import { STORAGE_LS_SHOWED_ADD_TO_SCREEN } from "packages/configs/storage";

defineProps({
  isAnniversary: {
    type: Boolean,
    required: true,
  },
});

const { t } = useI18n();
const { is_login, user_info, user_had_bound_lip } = storeToRefs(useUser());

const showed_add_to_screen = useLocalStorage(STORAGE_LS_SHOWED_ADD_TO_SCREEN, false);

const { checkTask } = useTask();
const route = useRoute();
const router = useRouter();
const { init, cancelPoll } = useSetting();
const { is_unread } = storeToRefs(useSetting());

const check_task = ref<Task>();
const is_homescree = ref(!!route.query.install && install.state !== "installed");
// const install_href = router.resolve({ query: { install: true } } as any).href;

const avatar = computed(() =>
  is_login.value && user_info.value?.avatar ? user_info.value?.avatar : "",
);

const onLogoClick = async () => {
  await router.push(Routes.HOME);
  window.location.reload();
};

const homescreeClick = () => {
  showed_add_to_screen.value = true;
  if (install.promptable) {
    return install.prompt();
  } else {
    is_homescree.value = !is_homescree.value;
  }
  report.standalonesite_add_desktop_confirm_btn.cm_click();
};

const messageClick = () => {
  is_login.value ? router.push(Routes.NOTIFICATION) : router.push(Routes.LOGIN);

  report.standalonesite_message_center_btn.cm_click({
    red_tag: is_unread.value,
  });
};

const userCenterClick = debounce(() => {
  if (is_login.value) {
    router.push({
      path: Routes.USER,
      query: {
        openid: user_info.value?.intl_openid,
      },
    });
  } else {
    router.push(Routes.LOGIN);
  }
  report.standalonesite_personal_btn.cm_click();
}, 200);

const fetchTasks = async () => {
  const { tasks = [] } = await getTasks(is_login.value);
  check_task.value = tasks.find((item) => item.task_type === TaskType.DailyCheckIn);
};

const sign_in_visible = computed(() => {
  if (!is_login.value) {
    report.standalonesite_sign_window.cm_vshow();
    return true;
  }
  return (check_task.value && !check_task.value.is_completed) || !user_had_bound_lip.value;
});

const onCheckIn = async () => {
  await report.standalonesite_sign_btn.cm_click().catch((err) => {
    console.log("onCheckIn err", err);
  });
  if (!is_login.value) {
    return router.push({
      path: Routes.LOGIN,
    });
  }

  if (sign_in_visible.value) {
    !user_had_bound_lip.value
      ? router.push({
          path: Routes.POINTS,
        })
      : checkTask(check_task.value as Task);
  }
};
watch(
  () => is_login.value,
  () => {
    fetchData();
  },
);
watch(
  () => install.state,
  (val) => {
    if (val === "installed") {
      is_homescree.value = false;
    }
  },
  { immediate: true },
);

watch(
  () => sign_in_visible.value,
  (val: boolean) => {
    if (val) report.standalonesite_sign_window.cm_vshow();
  },
);

const fetchData = () => {
  init();
  fetchTasks();
};

onMounted(() => {
  if (is_login.value) {
    fetchData();
  }
});

// 组件销毁，去除轮询
onUnmounted(() => {
  cancelPoll();
});
</script>
