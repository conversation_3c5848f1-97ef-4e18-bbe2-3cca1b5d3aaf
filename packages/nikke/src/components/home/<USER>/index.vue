<template>
  <!-- is_homescree -->
  <CommBottomPopup :show="show" @close="emits('close')">
    <div class="p-[24px]">
      <div
        class="font-bold text-[length:18px] text-[color:var(--text-1)] leading-[22px] text-center"
      >
        {{ t("add_to_screen_title", ["Blabla Link"]) }}
      </div>
      <div v-if="!isMobileDevice()">
        <div
          v-if="isInIntlBrowser || install.promptable"
          class="text-[color:var(--text-2)] text-[length:13px] leading-[16px] text-center mt-[6px]"
        >
          {{ t("add_to_home_screen_to_find_us_easily") }}
        </div>
        <Button
          v-if="isInIntlBrowser"
          type="primary"
          class="mx-[54px] !mt-[16px]"
          @click="invokeIngameOpen"
        >
          {{ t("open_in_browser") }}
        </Button>
        <Button
          v-else-if="install.promptable"
          type="primary"
          class="mx-[54px] !mt-[16px]"
          :loading="install.state === 'pending'"
          @click="install.prompt"
        >
          {{ t("confirm") }}
        </Button>
        <div v-else class="mt-5 py-[6px] text-2xl text-center text-[color:var(--text-2)]">
          <template v-if="install.state === 'installed'">
            {{ t("add_to_screen_finished") }}
          </template>
          <template v-else>
            {{ t("add_to_screen_on_mobile_tips") }}
          </template>
        </div>
      </div>

      <div v-else class="relative">
        <Swiper ref="swiper" :loop="false" :index="0" :autoplay="false" :duration="3000">
          <div v-for="(item, index) in list" :key="index" class="pt-[30px] pb-[24px]">
            <div class="w-full min-h-[132px]">
              <CommonImage :src="item?.gif" @click="swiper?.onChange('right')"></CommonImage>
            </div>
            <div
              v-safe-html="item.desc"
              class="mt-[17px] leading-[16px] flex items-center justify-center flex-wrap text-[length:13px] text-[color:var(--text-2)]"
            ></div>
          </div>
          <template #pagination>
            <div class="absolute top-[12px] left-1/2 -translate-x-1/2">
              <SwiperPagination
                :custom_theme="custom_theme"
                :size="Size.md"
                :direction="SwiperDirection.horizontal"
              >
              </SwiperPagination>
            </div>
          </template>
        </Swiper>
      </div>
    </div>
  </CommBottomPopup>
</template>
<script setup lang="ts">
import { computed, ref, watch } from "vue";
import Button from "@/components/ui/button/index.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import { CommonImage } from "@/components/common/image";
import { SwiperDirection } from "@/types/swiper";
import { Swiper, SwiperPagination } from "@/components/common/swiper/index.ts";
import { Size } from "packages/types/common";

import install from "@/boot/pwa-install";
import { isGameLogin, isIOS, isMobileDevice, isSafari } from "packages/utils/tools";
import { isInIntlBrowser, openInBrowser } from "@tencent/pa-ingame-utils";
import { useI18n } from "vue-i18n";
// import { useRoute, useRouter } from "vue-router";

import gif_safari from "@/assets/imgs/home/<USER>";
import gif_google from "@/assets/imgs/home/<USER>";
import gif_game from "@/assets/imgs/home/<USER>";
import gif_android from "@/assets/imgs/home/<USER>";
// import gif_step2_android from "@/assets/imgs/home/<USER>";
// import gif_step2 from "@/assets/imgs/home/<USER>";
import gif_step3 from "@/assets/imgs/home/<USER>";

import icon1 from "@/assets/imgs/home/<USER>";
import icon2 from "@/assets/imgs/home/<USER>";
import icon3 from "@/assets/imgs/home/<USER>";
import icon4 from "@/assets/imgs/home/<USER>";

import step01_game_Safari_EN from "@/assets/imgs/home/<USER>";
import step01_game_Safari_ja from "@/assets/imgs/home/<USER>";
import step01_game_Safari_kr from "@/assets/imgs/home/<USER>";
import step02_Safari_Chrome_zh_tw from "@/assets/imgs/home/<USER>";
import step02_Android_Chrome_zh_tw from "@/assets/imgs/home/<USER>";
import step01_game_Safari_zh_tw from "@/assets/imgs/home/<USER>";
import step01_game_Google_zh_tw from "@/assets/imgs/home/<USER>";
import step01_game_Safari_zh from "@/assets/imgs/home/<USER>";
import step01_game_Google_EN from "@/assets/imgs/home/<USER>";
import step01_game_Google_kr from "@/assets/imgs/home/<USER>";
import step01_game_Google_ja from "@/assets/imgs/home/<USER>";
import step02_Safari_Chrome_EN from "@/assets/imgs/home/<USER>";
import step02_Safari_Chrome_kr from "@/assets/imgs/home/<USER>";
import step02_Safari_Chrome_ja from "@/assets/imgs/home/<USER>";
import step02_Android_Chrome_EN from "@/assets/imgs/home/<USER>";
import step02_Android_Chrome_ja from "@/assets/imgs/home/<USER>";
import step02_Android_Chrome_kr from "@/assets/imgs/home/<USER>";
import step01_game_Google_zh from "@/assets/imgs/home/<USER>";
import step02_Android_Chrome_zh from "@/assets/imgs/home/<USER>";
import step02_Safari_Chrome_zh from "@/assets/imgs/home/<USER>";
import { getStandardizedLang } from "packages/utils/standard";

const props = defineProps<{
  show: boolean;
}>();

const emits = defineEmits<{
  (e: "close"): void;
}>();

const { t } = useI18n();

// const route = useRoute();
// const router = useRouter();
const currentSearch = new URLSearchParams(window.location.search);
currentSearch.set("install", "true");
const install_href =
  window.location.origin + window.location.pathname + "?" + currentSearch.toString();

const gif_step2_android = computed(() => {
  return {
    en: step02_Android_Chrome_EN,
    zh: step02_Android_Chrome_zh,
    "zh-TW": step02_Android_Chrome_zh_tw,
    ja: step02_Android_Chrome_ja,
    ko: step02_Android_Chrome_kr,
  }[getStandardizedLang()]!;
});

const gif_step2 = computed(() => {
  return {
    en: step02_Safari_Chrome_EN,
    zh: step02_Safari_Chrome_zh,
    "zh-TW": step02_Safari_Chrome_zh_tw,
    ja: step02_Safari_Chrome_ja,
    ko: step02_Safari_Chrome_kr,
  }[getStandardizedLang()]!;
});

const gif_step2_game_ios = computed(() => {
  return {
    en: step01_game_Safari_EN,
    zh: step01_game_Safari_zh,
    "zh-TW": step01_game_Safari_zh_tw,
    ja: step01_game_Safari_ja,
    ko: step01_game_Safari_kr,
  }[getStandardizedLang()]!;
});

const gif_step2_game_android = computed(() => {
  return {
    en: step01_game_Google_EN,
    zh: step01_game_Google_zh,
    "zh-TW": step01_game_Google_zh_tw,
    ja: step01_game_Google_ja,
    ko: step01_game_Google_kr,
  }[getStandardizedLang()]!;
});

const custom_theme = ref({
  default_bg: "bg-[color:var(--fill-2)]",
  active_bg: "bg-[color:var(--fill-1)]",
});

const swiper = ref<{ onChange: (key: "left" | "right") => void } | null>(null);

const list = computed(() => {
  const is_ios = isIOS();
  const in_game = isInIntlBrowser;
  const is_safari = isSafari();

  return [
    {
      gif: in_game ? gif_game : !is_ios ? gif_android : is_safari ? gif_safari : gif_google,
      desc: in_game
        ? t("add_to_screen_step_1_game", [
            `<img src="${icon2}" class="w-[16px] mx-[8px]" />`,
            "blabla link",
            `${is_ios ? "Safari" : "Google"}<img src="${icon3}" class="w-[16px] mx-[8px]" />`,
          ])
        : t("add_to_screen_step_1", [
            `<img src="${!is_ios ? icon4 : icon1}" class="w-[16px] mx-[8px]" />`,
            "blabla link",
          ]),
    },
    {
      gif: in_game
        ? is_ios
          ? gif_step2_game_ios.value
          : gif_step2_game_android.value
        : !is_ios
          ? gif_step2_android.value
          : gif_step2.value,
      desc: in_game
        ? t("add_to_screen_step_2_game", [
            `<img src="${icon1}" class="w-[16px] mx-[8px]" />`,
            is_ios ? "Safari" : "Google",
            `<span class="text-[color:var(--brand-1)] mx-[4px]">${t("add_to_home_screen")}</span>`,
          ])
        : t("add_to_screen_step_2", [
            `<span class="text-[color:var(--brand-1)] mx-[4px]">${t("add_to_home_screen")}</span>`,
          ]),
    },
    {
      gif: gif_step3,
      desc: t("add_to_screen_step_3", [
        `<span class="text-[color:var(--brand-1)] mx-[4px]">${t("desktop_shortcut")}</span>`,
        "blabla link",
      ]),
    },
  ];
});

const invokeIngameOpen = () => {
  console.log("[invokeIngameOpen]:", isGameLogin(), isInIntlBrowser, install_href);
  return openInBrowser(install_href);
};

watch(
  () => install.mode,
  (v) => {
    v === "standalone" && props.show && emits("close");
  },
);
</script>
