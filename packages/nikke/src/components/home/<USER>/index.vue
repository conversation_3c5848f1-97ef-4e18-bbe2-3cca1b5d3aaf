<template>
  <div
    v-click-interceptor.need_login.mute.sign_privacy.check_user_adult="goTo"
    class="fixed z-[50] bottom-[74px] left-[50%] w-[50px] cursor-pointer h-[58px] bg-[url('@/assets/imgs/common/comment-bubble.png')] bg-[length:100%_100%]"
    :style="{ transform: `translateX(${offset_left}px)` }"
    :class="{ 'post-bubble w-[120px] h-[150px]': anniversary }"
  ></div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useElementSize } from "@vueuse/core";
import { report } from "packages/utils/tlog.ts";
import { storeToRefs } from "pinia";
import { useAnniversary } from "@/store/home/<USER>";

const { anniversary } = storeToRefs(useAnniversary());
const emit = defineEmits(["compose"]);

const { width } = useElementSize(document.getElementById("layout-content"));

const offset_left = computed(() => {
  return width.value / 2 - 65;
});

const goTo = () => {
  report.standalonesite_posts_btn.cm_click();
  emit("compose");
};
</script>
