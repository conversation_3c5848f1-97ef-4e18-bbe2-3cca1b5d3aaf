<template>
  <div
    class="w-full h-full flex overflow-y-hidden overflow-x-auto"
    :class="{ 'no-scrollbar': is_mobile, 'justify-between': list.length === 4 && is_mobile }"
  >
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="[
        `cursor-pointer last-of-type:mr-0 flex-shrink-0`,
        list.length > 4 ? `-mr-[6px]` : `mr-[3px]`,
        is_mobile ? `w-[23.5%] min-h-[56px]` : `w-[84px] min-h-[56px]`,
      ]"
      @click="goTo(item)"
    >
      <div
        class="w-[44px] h-[50px] mx-auto bg-[length:100%_100%]"
        :style="{ backgroundImage: `url(${item.icon})` }"
      />
      <div
        class="font-Abolition mx-[5px] !mt-[-9px] relative z-[2] line-clamp-2 text-center text-[length:var(--font-size-base)] leading-[16px] text-[color:var(--text-1)] text-tools"
      >
        {{ item.tool_name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { District } from "packages/types/home";
import { useEnsureUrl, EnsureType } from "@/composables/use-ensure.ts";
import { useResponsive } from "@/composables/use-responsive";
import { safeParse } from "packages/utils/tools";
import { report } from "packages/utils/tlog.ts";
import { useWebCredential } from "@/composables/use-webcredential";

defineProps<{
  list: District[];
}>();

const { is_mobile } = useResponsive();
const { openUrlWithAuth } = useWebCredential();

const goTo = (item: District) => {
  const url = item.jump_url;
  const { ensure } = useEnsureUrl();
  const new_url = ensure(EnsureType.location, url) as string;
  // 1: 当前页面打开，2: 新页面打开
  const ext_info = safeParse<{ jump_type?: { value: "1" | "2" } }>(item.ext_info);
  report.standalonesite_small_tools_item.cm_click({
    tool_id: item.id,
    tool_name: item.tool_name,
  });

  if (ext_info?.jump_type?.value) {
    openUrlWithAuth(new_url, ext_info.jump_type.value === "2" ? "_blank" : "_self");
  } else {
    openUrlWithAuth(new_url);
  }
};
</script>
