<template>
  <div
    class="p-[16px] pb-[12px] relative bg-[color:var(--op-fill-white)] shadow-[0_0_10px_0_var(--op-shadow-black-5)] z-[1] overflow-hidden"
  >
    <div
      class="px-[4px] text-[color:var(--text-1)] opacity-[0.7] text-[length:13px] leading-[16px] line-clamp-3"
    >
      {{ item?.title }}
    </div>
    <div v-if="item?.ext_info || item?.pic_urls?.length" class="mt-[8px]">
      <Medias
        :key="item.ext_info + item.pic_urls?.join(',')"
        :img-list="item?.pic_urls"
        :ext_info="item.ext_info"
        :type="item.ext_info ? 'video' : 'image'"
        :original_url="item.original_url"
      ></Medias>
    </div>
    <!-- <div v-if="item?.content" class="mt-[8px] text-[var(--brand-1)] text-[length:11px] leading-[13px]">
      {{ item?.content }}
    </div> -->
    <slot name="task-rank"></slot>
    <div class="mt-[8px] flex items-center justify-between h-[24px]">
      <div
        v-if="item?.modified_on"
        class="text-[color:var(--text-3)] text-[length:11px] leading-[13px]"
      >
        {{ formatTime(item.created_on * 1000, t) }}
      </div>
      <div
        v-if="item?.original_url"
        class="flex items-center justify-end flex-1 cursor-pointer"
        @click="openLink(item?.original_url)"
      >
        <div class="text-[color:var(--text-3)] text-[length:11px] leading-[13px]">
          {{ t("view_original_article") }}
        </div>
        <div class="w-[16px] h-[16px] ml-[6px]">
          <!-- <i class="absolute-center"></i> -->
          <SvgIcon name="icon-arrow-right" color="var(--text-3)"></SvgIcon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Medias from "@/components/common/medias/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { PostItem } from "packages/types/post";
import { formatTime } from "@/utils/str";
import { useI18n } from "vue-i18n";
import { useWebCredential } from "@/composables/use-webcredential";
const { t } = useI18n();

defineProps<{
  item?: PostItem;
}>();

const { openUrlWithAuth } = useWebCredential();

const openLink = (url: string) => openUrlWithAuth(url, "_blank");
</script>

<style lang="scss" scoped></style>
