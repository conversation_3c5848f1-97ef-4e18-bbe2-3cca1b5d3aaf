<template>
  <div class="h-full w-full cursor-pointer">
    <template v-if="src?.length">
      <img class="aspect-video h-full w-full object-cover" :src="src?.[0]" />
    </template>

    <div
      v-if="is_video"
      class="absolute left-1/2 top-1/2 inline-flex h-12 w-12 -translate-x-1/2 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full border-[2px] border-[var(--color-border-white)] md:!h-[72px] md:!w-[72px]"
    >
      <SvgIcon name="play" class="h-2/3 w-2/3"></SvgIcon>
    </div>

    <div :class="['absolute px-2', has_pagination ? 'bottom-3 md:!bottom-6' : 'bottom-2']">
      <div
        v-if="title"
        class="line-clamp-1 font-['Lato'] text-base font-bold leading-[1.2] text-[color:var(--color-text-white)] md:!text-xl"
      >
        {{ title }}
      </div>
      <div
        v-if="desc"
        :class="[
          `leading-1 mt-0 line-clamp-1 font-['Lato'] text-sm leading-[1.2] text-[color:var(--color-text-white)] md:!text-base`,
        ]"
      >
        {{ desc }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// cpnts
import { IntegratedMediaTextCardProps } from "packages/types/cards";

defineProps<IntegratedMediaTextCardProps>();
</script>

<style lang="scss" scoped></style>
