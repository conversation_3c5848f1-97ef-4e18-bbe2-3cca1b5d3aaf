<template>
  <div class="flex w-full cursor-pointer" @click="emits('click')">
    <!-- 左侧媒体区域 -->
    <div
      v-if="has_image"
      :style="left_style ? left_style : {}"
      class="relative mr-2 flex aspect-video w-[40%] min-w-[40%] items-center justify-center overflow-hidden rounded-md md:!mr-6"
    >
      <!-- 图片类型 -->
      <img :src="src?.[0] || ''" class="aspect-video h-full w-full object-cover" alt="card img" />

      <!-- 视频类型 -->
      <div
        v-if="is_video"
        class="absolute flex h-10 w-10 transform cursor-pointer items-center justify-center overflow-hidden rounded-full bg-[var(--color-text-black-80)] transition duration-200 hover:scale-[1.18] md:!h-12 md:!w-12"
      >
        <SvgIcon name="play" class="h-4 w-4 md:!h-6 md:!w-6"></SvgIcon>
      </div>
    </div>

    <!-- 右侧文案 -->
    <div :class="[`flex flex-col`, has_image ? 'w-[56%]' : 'w-full']">
      <div
        v-if="title"
        class="ml:!mb-1 ml:!text-xs mb-2 line-clamp-2 break-all text-sm leading-[1.2] text-[color:var(--color-text-white)] md:!mb-4 md:!line-clamp-3 md:text-base"
      >
        {{ title }}
      </div>

      <div
        v-if="time || source"
        class="ml:!mb-1 mb-2 flex text-xs leading-[1.2] text-[color:var(--color-13)] md:!text-sm"
      >
        <span v-if="time"> {{ time }} </span>
        <span v-if="source" class="ml-[10px]"> {{ source }} </span>
      </div>

      <div
        v-if="desc"
        class="line-clamp-2 text-xs leading-[1.2] text-[color:var(--color-13)] md:!line-clamp-4 md:!text-sm"
      >
        {{ desc }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { LeftMediaRightTextCardProps } from "packages/types/cards";
import { computed } from "vue";

const props = defineProps<LeftMediaRightTextCardProps>();
const emits = defineEmits(["click"]);

const has_image = computed(() => props.src?.filter(Boolean)?.length);
</script>

<style lang="scss" scoped></style>
