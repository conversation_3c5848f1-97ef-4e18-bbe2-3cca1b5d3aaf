<template>
  <div class="flex flex-col rounded bg-[var(--op-fil-black-30)] p-3">
    <div
      class="relative flex w-full items-center justify-between pl-3 text-base before:absolute before:left-0 before:h-full before:w-[2px] before:bg-[var(--color-brand7)] md:!text-xl"
    >
      <slot name="title">
        <span class="font-['Lato-black'] uppercase">
          {{ title }}
        </span>
      </slot>
      <slot name="right"></slot>
    </div>

    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { PanelCardProps } from "packages/types/cards";

defineProps<PanelCardProps>();
</script>

<style lang="scss" scoped></style>
