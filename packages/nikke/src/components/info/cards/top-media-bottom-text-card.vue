<template>
  <div class="flex w-full cursor-pointer flex-col" @click="emits('click')">
    <!-- 左侧媒体区域 -->
    <template v-if="proxy_src.length">
      <div class="relative flex items-center justify-between overflow-hidden rounded-md">
        <!-- 图片类型 -->
        <img
          v-for="(item, index) in proxy_src"
          :key="index"
          :src="item"
          :style="{
            width: has_multiple_image ? `${98 / proxy_src.length}%` : '100%',
          }"
          class="aspect-video h-full w-full object-cover"
          alt="card img"
        />

        <!-- 视频类型 -->
        <div
          v-if="is_video"
          :class="[
            `absolute flex transform cursor-pointer items-center justify-center overflow-hidden rounded-full bg-[var(--color-text-black-80)] transition duration-200 hover:scale-[1.18]`,
            is_mobile ? `h-[30px] w-[30px]` : `h-[60px] w-[60px]`,
          ]"
        >
          <SvgIcon
            name="play"
            :class="[is_mobile ? `h-[15px] w-[15px]` : `h-[30px] w-[30px]`]"
          ></SvgIcon>
        </div>
      </div>
    </template>

    <!-- 底部文案 -->
    <div :class="[`flex flex-col`]">
      <div
        v-if="title"
        :class="[
          `line-clamp-1 font-['Lato'] leading-[1.2] text-[color:var(--color-text-white)]`,
          is_mobile
            ? 'my-[var(--dc-4)] break-all text-[length:var(--font-size-14)]'
            : 'my-[var(--dc-8)] text-[length:var(--font-size-18)]',
        ]"
      >
        {{ title }}
      </div>

      <div
        :class="[
          `flex font-['Lato'] leading-[1.2] text-[color:var(--color-13)]`,
          is_mobile
            ? `mt-[var(--dc-4)] text-[length:var(--font-size-12)]`
            : `mt-[var(--dc-8)] text-[length:var(--font-size-14)]`,
        ]"
      >
        <span v-if="time"> {{ time }} </span>
        <span v-if="source" class="ml-[10px]"> {{ source }} </span>
      </div>

      <div
        v-if="desc"
        :class="[
          `font-['Lato'] leading-[1.2] text-[color:var(--color-13)]`,
          is_mobile
            ? `mt-[var(--dc-8)] line-clamp-2 text-[length:var(--font-size-12)]`
            : `mt-[var(--dc-16)] line-clamp-4 text-[length:var(--font-size-14)]`,
        ]"
      >
        {{ desc }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { TopMediaBottomTextCardProps } from "packages/types/cards";

// utils
import { useResponsive } from "@/composables/use-responsive";
import { computed } from "vue";

const props = defineProps<TopMediaBottomTextCardProps>();
const emits = defineEmits(["click"]);
const { is_mobile } = useResponsive();

const proxy_src: any = computed(() => {
  const src = props.src;
  if (Array.isArray(src)) {
    return src;
  }
  return [src];
});

const has_multiple_image = computed(() => proxy_src.value.length > 1);
</script>

<style lang="scss" scoped></style>
