<template>
  <CommBottomPopup :show="visible" @close="handleClosePopup">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[14px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] leading-[21px]">
          {{ t("user_status") }}
        </div>
        <div class="cursor-pointer" @click="onConfirm">
          <span class="text-[color:var(--brand-1)] text-[length:14px] font-bold">
            {{ t("confirm") }}
          </span>
        </div>
      </div>
    </template>
    <div class="px-[20px] pb-[30px]">
      <Swiper :loop="false" :index="0" :autoplay="false" :duration="3000">
        <div
          v-for="(item, index) in moon_group"
          :key="index + checked"
          class="flex items-center flex-wrap w-full select-none"
        >
          <div
            v-for="(i, idx) in item"
            :key="idx"
            class="w-1/5 flex flex-col items-center justify-center mb-[16px]"
            @click="onCheck(i.library_data_id)"
          >
            <div
              class="mb-[6px] p-[10px]"
              :class="
                i.library_data_id === checked
                  ? 'bg-[color:var(--brand-2)] border-[1px] border-[color:var(--brand-1)] rounded-[2px] dark:bg-[color:var(--brand-1-20)]'
                  : 'border-[1px] border-transparent'
              "
            >
              <CommonImage
                :src="i.icon?.[0] || ''"
                class="w-[25px] h-[25px] flex flex-col items-center justify-center"
                draggable="false"
              />
            </div>
            <div
              class="font-normal text-[length:12px] text-[color:var(--text-3)] leading-[14px] truncate max-w-full"
            >
              {{ i.name }}
            </div>
          </div>
        </div>
        <template v-if="moon_group.length > 1" #pagination>
          <!-- 设计稿此处的更新与组件内的配置不一致，开发留意！！！ -->
          <SwiperPagination
            :custom_theme="custom_theme"
            :size="Size.md"
            :direction="SwiperDirection.horizontal"
          >
          </SwiperPagination>
        </template>
      </Swiper>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { Size } from "packages/types/common";
import { SwiperDirection } from "@/types/swiper";
import { Swiper, SwiperPagination } from "@/components/common/swiper/index.ts";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { useModifyStatus } from "@/api/user-status";
import { useUser } from "@/store/user";
import { useUserCenter } from "@/composables/use-user-center";
import { CommonImage } from "@/components/common/image";

const props = defineProps<{
  visible?: boolean;
  // status?: string;
}>();
const emits = defineEmits(["change", "close"]);
const custom_theme = ref({
  default_bg: "bg-[color:var(--fill-7)] opacity-75",
  active_bg: "bg-[color:var(--brand-1)]",
});

const { mutateAsync: modify } = useModifyStatus();

const user = useUser();
const { refetchUserInfo } = useUserCenter();

const moon_group = computed(() => {
  type Item = NonNullable<typeof user.mood_list>[number];
  return (
    user.mood_list?.reduce((pre, cur) => {
      const last = pre[pre.length - 1];
      if (last && last.length < 10) {
        last.push(cur);
      } else {
        pre.push([cur]);
      }
      return pre;
    }, [] as Item[][]) ?? []
  );
});

const checked = ref("");
const onCheck = async (id: string) => {
  checked.value = id;
};
const onConfirm = async () => {
  const status = user.mood_list?.find((item) => item.library_data_id === checked.value);
  if (status && status.library_data_id !== user.user_info?.mood) {
    await modify({
      mood: status.library_data_id,
    });
    refetchUserInfo();
    user.refetchUserInfo();
    emits("change", status);
  }
  handleClosePopup();
};
const handleClosePopup = () => {
  emits("close", false);
};
watch(
  () => [user.user_info?.mood, props.visible],
  () => {
    if (props.visible && user.user_info?.mood) {
      checked.value = user.user_info?.mood;
    }
  },
);
</script>
