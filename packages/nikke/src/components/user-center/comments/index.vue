<template>
  <div class="mx-[15px]">
    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <div v-for="(item, index) in list" :key="index" class="mb-[12px] last-of-type:mb-0">
        <CommentsItem :item="item" :original_content="is_self" @delete-click="onManage">
        </CommentsItem>
      </div>
    </InfiniteScroll>
    <Dialog
      :show="visible1"
      :title="t('delete_comment')"
      :content="t('are_you_sure_to_delete')"
      :confirm_text="t('confirm')"
      :cancel_text="t('cancel')"
      @confirm="onDeleteSub"
      @cancel="visible1 = !visible1"
      @close="visible1 = !visible1"
    >
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { InfiniteScroll } from "@/components/common/scroll/index";
import CommentsItem from "@/components/user-center/comments-item/index.vue";
import Dialog from "@/components/ui/dialog/index.vue";
import { useGetMyComments, useDeleteComment } from "@/api/comments";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { useUserCenter } from "@/composables/use-user-center";
import { GetMyCommentItem } from "packages/types/comments";
const { t } = useI18n();
const { intl_openid, is_self } = useUserCenter();
const checked = ref<GetMyCommentItem>();

const visible1 = ref(false);
const { empty, finished, list, load, loading } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    if (!intl_openid.value) {
      return Promise.resolve({ list: [], page_info: { is_finish: true } });
    }
    return useGetMyComments.run({
      plate_id: 0,
      order_by: 1,
      limit: `${limit}`,
      nextPageCursor: next_page_cursor,
      intl_openid: intl_openid.value!,
    });
  },
  item_key: "comment_uuid",
});
const onManage = (item: GetMyCommentItem) => {
  checked.value = item;
  visible1.value = true;
};
const onDeleteSub = async () => {
  await useDeleteComment.run({ comment_uuid: checked.value?.comment_uuid || "" });
  // TODO:提示
  visible1.value = false;
  console.log("delete");
  const idx = list.value.findIndex((item) => item.comment_uuid === checked.value?.comment_uuid);
  if (idx >= 0) {
    list.value.splice(idx, 1);
  }
};
</script>
