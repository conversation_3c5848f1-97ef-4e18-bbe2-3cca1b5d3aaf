<script setup lang="ts">
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import { BaseDialog } from "@/utils/dialog";
import { UserInfo } from "packages/types/user";
import { t } from "@/locales";
import { computed, ref } from "vue";
import { useUserBlock } from "@/composables/use-user-block";

interface Props extends BaseDialog {
  user_info: UserInfo;
  onBlockChange: (is_black: boolean) => void;
}

const { user_info, finish, cancel, onBlockChange } = defineProps<Props>();

const { handleBlock, handleUnblock } = useUserBlock();

const isOfficialAccount = computed(() => [1, 3].includes(user_info.auth_type));

const list = ref<
  {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    hidden?: boolean;
  }[]
>([
  {
    label: isOfficialAccount.value ? t("cannot_block_official") : t("block_this_user"),
    onClick: async () => {
      finish();
      const changed = await handleBlock(user_info.intl_openid);
      if (changed) {
        onBlockChange(true);
      }
    },
    disabled: isOfficialAccount.value,
    hidden: user_info.is_black === 1,
  },
  {
    label: t("unblock_this_user"),
    onClick: async () => {
      finish();
      const changed = await handleUnblock(user_info.intl_openid);
      if (changed) {
        onBlockChange(false);
      }
    },
    hidden: user_info.is_black === 0,
  },
]);

const visible_list = computed(() => {
  return list.value.filter((item) => !item.hidden);
});
</script>

<template>
  <CommBottomPopup :show="true" @close="cancel">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[16px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] leading-[21px]">
          {{ t("manage") }}
        </div>
      </div>
    </template>
    <div class="flex flex-col px-[20px] pb-[36px]">
      <div
        v-for="item in visible_list"
        :key="item.label"
        class="flex items-center justify-between border-t-[1px] border-[var(--line-1)] py-[10px]"
        :class="{
          'opacity-50': item.disabled,
          'cursor-pointer': !item.disabled,
        }"
        @click="!item.disabled && item.onClick()"
      >
        <div class="text-[16px] text-[color:var(--text-1)] leading-[21px]">
          {{ item.label }}
        </div>
      </div>
    </div>
  </CommBottomPopup>
</template>
