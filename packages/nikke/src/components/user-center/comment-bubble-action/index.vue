<template>
  <CommonBottomPopup :show="show" nobg @close="emits('close')">
    <!-- 头像挂件名称 -->
    <div
      v-if="selected_frame_info?.title"
      class="text-[color:var(--text-1)] text-[16px] leading-[19px] text-center mt-[20px]"
    >
      {{ selected_frame_info?.title }}
    </div>
    <div
      v-if="selected_frame_info"
      class="flex items-center justify-between px-[20px] pt-[25px] pb-[50px]"
    >
      <!-- 未锁住 -->
      <div v-if="selected_frame_info.is_owned" class="flex-1">
        <div class="flex items-center">
          <SvgIcon
            name="icon-history"
            class="w-[16px] h-[16px] mr-[4px] -mt-[2px]"
            color="var(--brand-1)"
          ></SvgIcon>
          <div class="text-[color:var(--text-1)] text-[12px] font-medium">
            {{ t("period_of_validity") }}
          </div>
        </div>
        <div
          v-if="!selected_frame_info.is_permanent"
          class="text-[color:var(--text-2)] text-[12px] font-medium mt-[4px]"
        >
          {{ dayjs.unix(selected_frame_info.valid_begin_at).format("YYYY/MM/DD") }}-{{
            dayjs.unix(selected_frame_info.valid_end_at).format("YYYY/MM/DD")
          }}
        </div>
        <div v-else class="text-[color:var(--text-2)] text-[12px] font-medium mt-[4px]">
          {{ t("permanent_valid") }}
        </div>
      </div>
      <!-- 锁住 -->
      <div v-else class="flex-1 flex items-center">
        <div class="w-[16px] h-[16px] mr-[4px] -mt-[2px]">
          <SvgIcon name="icon-lock" color="var(--text-1)"></SvgIcon>
        </div>
        <div class="text-[color:var(--text-1)] text-[12px]">{{ t("avatar_frame_locked") }}</div>
      </div>

      <Btns
        v-if="action === 'save'"
        :text="t('wear')"
        type="primary"
        size="m"
        @click="emits('save')"
      ></Btns>
      <Btns
        v-else-if="action === 'demount'"
        :text="t('demount')"
        type="primary"
        size="m"
        @click="emits('demount')"
      ></Btns>
      <Btns
        v-else-if="action === 'to-get'"
        :text="t('to_get')"
        type="primary"
        size="m"
        @click="jump(selected_frame_info?.jump_url || '')"
      ></Btns>
      <Btns v-else-if="action === 'expired'" :text="t('expired')" type="disabled" size="m"></Btns>
      <Btns
        v-else-if="action === 'locked'"
        :text="t('avatar_frame_locked')"
        type="disabled"
        size="m"
      ></Btns>
    </div>
  </CommonBottomPopup>
</template>

<script setup lang="ts">
import Btns from "@/components/common/btns/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import CommonBottomPopup from "@/components/common/comm-bottom-popup.vue";

import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { getStandardizedLang } from "packages/utils/standard";
import { UserCommentBubble } from "packages/types/user";
import { useWebCredential } from "@/composables/use-webcredential";
const { t } = useI18n();

defineProps<{
  show: boolean;
  selected_frame_info: UserCommentBubble | undefined;
  action: "save" | "demount" | "to-get" | "expired" | "locked" | null;
}>();

const emits = defineEmits(["close", "save", "demount"]);

const { openUrlWithAuth } = useWebCredential();

const jump = (url: string) => {
  try {
    const parse = new URL(url);
    parse.searchParams.set("lang", getStandardizedLang());
    openUrlWithAuth(parse.toString(), "_blank");
  } catch (e) {
    url && openUrlWithAuth(url, "_blank");
  }
};
</script>
