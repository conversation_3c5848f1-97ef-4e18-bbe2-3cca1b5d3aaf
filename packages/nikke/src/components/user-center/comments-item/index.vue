<template>
  <div
    class="p-[16px] pb-[12px] relative bg-[color:var(--op-fill-white)] shadow-[0_0_10px_0_var(--op-shadow-black-5)] z-[1] overflow-hidden"
  >
    <div class="flex items-center justify-between mb-[10px]">
      <div class="text-[color:var(--text-2)] text-[length:12px] leading-[14px]">
        {{ formatTime(+item.created_on * 1000, t) }} · {{ item.plate_name }}
      </div>
      <div
        v-if="is_self"
        class="relative w-[20px] h-[20px] cursor-pointer ml-[12px]"
        @click.stop="emit('deleteClick', item)"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-delete" color="var(--text-3)"></SvgIcon>
      </div>
    </div>

    <div class="mx-[4px]">
      <div
        v-if="item?.content"
        v-safe-html="item?.content"
        class="mb-[8px] text-[color:var(--text-1)] text-[length:12px] leading-[14px] line-clamp-2 break-all content-with-images"
      ></div>
      <div
        v-if="comment_context"
        class="mb-[8px] flex items-center text-[color:var(--text-3)] text-[length:12px] leading-[14px] overflow-hidden cursor-pointer"
        :class="[item.comment_del && '!text-[var(--text-4)]']"
        @click="toTargetComment"
      >
        <i class="self-stretch w-[3px] bg-[color:var(--fill-5)] mr-[7px] flex-none"></i>
        <div v-safe-html="comment_context" class="truncate text-nowrap content-with-images"></div>
      </div>

      <div
        v-if="post_title"
        v-safe-html="post_title"
        :class="[item.comment_del && '!text-[var(--text-4)]']"
        class="px-[10px] py-[8px] bg-[color:var(--fill-3)] dark:bg-[color:var(--fill-3)] border-[1px] border-[color:var(--line-1)] text-[color:var(--text-3)] text-[length:12px] leading-[14px] content-with-images cursor-pointer"
        @click="goToPostDetail"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { formatTime } from "@/utils/str";
import { useI18n } from "vue-i18n";
import { useUserCenter } from "@/composables/use-user-center";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { computed } from "vue";
import { GetMyCommentItem } from "packages/types/comments";
const { t } = useI18n();
const { is_self } = useUserCenter();
const props = defineProps<{
  item: GetMyCommentItem;
  original_content: boolean;
}>();

const router = useRouter();

const emit = defineEmits(["deleteClick"]);

const post_title = computed(() => {
  return props.item.post_del ? t("content_deleted") : props.item.post_title;
});

const comment_context = computed(() => {
  return props.item.comment_del ? t("content_deleted") : props.item.comment_context;
});

const toTargetComment = () => {
  if (props.item.comment_del || props.item.post_del) return;
  router.push({
    path: Routes.POST_COMMENTS,
    query: {
      comment_uuid: props.item.reply_uuid || props.item.comment_uuid,
      post_uuid: props.item.post_uuid,
    },
  });
};

const goToPostDetail = () => {
  if (props.item.post_del) return;
  router.push({
    path: Routes.POST_DETAIL,
    query: {
      post_uuid: props.item.post_uuid,
      scroll_target: "comment",
      original_content: props.original_content ? 1 : undefined,
    },
  });
};
</script>

<style lang="scss" scoped>
.content-with-images:deep(img) {
  display: inline-block;
}
</style>
