<template>
  <div class="">
    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <CardItem
        v-for="(item, index) in list"
        :key="index"
        :item="item"
        :index="index"
        :plate_id="0"
        plate_name="user-center"
        :show_plate_name="true"
        :original_content="is_self"
        @follow="onFollow(item)"
        @star="onStar(item)"
        @manage="onManage"
        @share="onShare"
        @detail="onDetail"
      ></CardItem>
    </InfiniteScroll>
    <ManageDialog
      :visible="visible"
      :can_edit="checked?.can_edit"
      @close="visible = false"
      @delete="onShowDelete"
      @edit="onEditPost"
    />
    <Dialog
      :show="visible1"
      :title="t('delete_post')"
      :content="t('are_you_sure_to_delete')"
      :confirm_text="t('confirm')"
      :cancel_text="t('cancel')"
      @confirm="onDeleteSub()"
      @cancel="visible1 = !visible1"
      @close="visible1 = !visible1"
    >
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { InfiniteScroll } from "@/components/common/scroll/index";
import { postStar, useDeletePost, useMyCollectionPostList, usePostForward } from "@/api/post";
import { useInfiniteList } from "@/composables/use-infinite-list";
import CardItem from "@/components/common/card-item/index.vue";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import Dialog from "@/components/ui/dialog/index.vue";
import { useUserCenter } from "@/composables/use-user-center";
import { useFollowUser } from "@/api/user";
import { PostItem } from "packages/types/post";
import { LikeType, StanceType } from "packages/types/stance";
import { useI18n } from "vue-i18n";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { useUser } from "@/store/user";
import { usePostsStatus } from "@/composables/use-posts-status";
import { updatePostsData, updateUserStatusInPostList } from "@/utils/home";
import router from "@/router";
import { Routes } from "@/router/routes";
import { getStandardizedLang } from "packages/utils/standard";
const { t } = useI18n();

defineProps<{
  // aaa?: any;
}>();

const user_store = useUser();
const { intl_openid, refetchUserInfo, is_self } = useUserCenter();
/** 缓存当前用户openid， 在页面 onDeactivated 后，仍然有效，和列表数据保持对齐 */
const current_user_openid = ref("");
const { empty, finished, list, load, loading } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    if (!intl_openid.value) {
      return Promise.resolve({ list: [], page_info: { is_finish: true } });
    }
    current_user_openid.value = intl_openid.value;
    return useMyCollectionPostList.run({
      limit: `${limit}`,
      nextPageCursor: next_page_cursor,
      intl_openid: intl_openid.value!,
      page_type: 0,
    });
  },
  item_key: "post_uuid",
});

const { setPostData } = usePostsStatus();

const visible = ref(false);
const visible1 = ref(false);
const checked = ref<PostItem>();

const onManage = (item: PostItem) => {
  checked.value = item;
  visible.value = true;
};
const onShowDelete = () => {
  visible.value = false;
  visible1.value = true;
};

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: checked.value?.post_uuid || ""!,
      edit_lang: getStandardizedLang(),
    },
  });
};

const onDeleteSub = async () => {
  await useDeletePost.run({ post_uuid: checked.value?.post_uuid || "" });
  // TODO:提示
  visible1.value = false;
  console.log("delete");
  const idx = list.value.findIndex((item) => item.post_uuid === checked.value?.post_uuid);
  if (idx >= 0) {
    list.value.splice(idx, 1);
  }
};

const { mutateAsync: follow } = useFollowUser();
const onFollow = async (item: PostItem) => {
  const { is_follow, is_mutual_follow } = await follow({ intl_openid: item.user.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.user.intl_openid,
    is_followed: is_follow ? 1 : 0,
    is_mutual_follow: is_mutual_follow ? 1 : 0,
  });
  refetchUserInfo();
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  if (item.my_upvote?.is_star) {
    item.my_upvote = { ...item.my_upvote, is_star: false };
    item.upvote_count = +item.upvote_count - 1;
  } else {
    item.my_upvote = { ...item.my_upvote, is_star: true };
    item.upvote_count = +item.upvote_count + 1;
  }
};

const onShare = async (item: PostItem) => {
  const { forward_count } = await usePostForward.run({
    post_uuid: item.post_uuid,
  });
  item.forward_count = forward_count;
};

const onDetail = (item: PostItem) => {
  setPostData(item, list.value);
};

onEventEmitter(EVENT_NAMES.refresh_post_list_item_info, (v) => {
  if (!v?.post_uuid) return;
  updatePostsData(v, list.value, v);

  // 如果帖子取消收藏了，而且判断当前是我的个人中心收藏列表，则移除该帖子
  if (!v.is_collection && current_user_openid.value === user_store.user_info.intl_openid) {
    const idx = list.value.findIndex((item) => item.post_uuid === v.post_uuid)!;
    if (idx >= 0) list.value.splice(idx, 1);
  }
});

onEventEmitter(EVENT_NAMES.user_status_change, (v) => updateUserStatusInPostList(list.value, v));
</script>
