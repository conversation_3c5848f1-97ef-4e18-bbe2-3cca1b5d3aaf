<template>
  <div class="">
    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <CardItem
        v-for="(item, index) in list"
        :key="index"
        :item="item"
        :show_plate_name="true"
        :original_content="is_self"
        @manage="onManage"
        @follow="onFollow(item)"
        @star="onStar(item)"
        @share="onShare"
        @detail="onDetail"
      >
      </CardItem>
    </InfiniteScroll>

    <ManageDialog
      :visible="visible"
      :can_edit="checked?.can_edit"
      @close="visible = false"
      @delete="onShowDelete"
      @edit="onEditPost"
    />

    <Dialog
      :show="visible1"
      :title="t('delete_post')"
      :content="t('are_you_sure_to_delete')"
      :confirm_text="t('confirm')"
      :cancel_text="t('cancel')"
      @confirm="onDeleteSub()"
      @cancel="visible1 = !visible1"
      @close="visible1 = !visible1"
    >
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { InfiniteScroll } from "@/components/common/scroll/index";
import { useMyPostList, useDeletePost, postStar, usePostForward } from "@/api/post";
import { useInfiniteList } from "@/composables/use-infinite-list";
import CardItem from "@/components/common/card-item/index.vue";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import Dialog from "@/components/ui/dialog/index.vue";
import { PostItem } from "packages/types/post.ts";
import { useUserCenter } from "@/composables/use-user-center";
import { useFollowUser } from "@/api/user";
import { LikeType, StanceType } from "packages/types/stance";
import router from "@/router";
import { Routes } from "@/router/routes";
import { getStandardizedLang } from "packages/utils/standard";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { usePostsStatus } from "@/composables/use-posts-status";
import { updatePostsData, updateUserStatusInPostList } from "@/utils/home";

const { t } = useI18n();
const visible = ref(false);
const visible1 = ref(false);
const checked = ref<PostItem>()!;

const { intl_openid, refetchUserInfo, is_self } = useUserCenter();
const { empty, finished, list, load, loading } = useInfiniteList({
  queryFn: ({ limit, next_page_cursor }) => {
    if (!intl_openid.value) {
      return Promise.resolve({ list: [], page_info: { is_finish: true } });
    }
    return useMyPostList.run({
      plate_id: 0,
      order_by: 1,
      limit: `${limit}`,
      nextPageCursor: next_page_cursor,
      intl_openid: intl_openid.value || "",
      page_type: 0,
      regions: undefined,
    });
  },
  item_key: "post_uuid",
});

const { setPostData } = usePostsStatus();

const onManage = (item: PostItem) => {
  checked.value = item!;
  visible.value = true;
};
const onShowDelete = () => {
  visible.value = false;
  visible1.value = true;
};

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: checked.value?.post_uuid || ""!,
      edit_lang: getStandardizedLang(),
    },
  });
};

const onDeleteSub = async () => {
  await useDeletePost.run({ post_uuid: checked.value?.post_uuid || "" })!;
  // TODO:提示
  visible1.value = false;
  console.log("delete");
  const idx = list.value.findIndex((item) => item.post_uuid === checked.value?.post_uuid)!;
  if (idx >= 0) {
    list.value.splice(idx, 1);
  }
};

const { mutateAsync: follow } = useFollowUser();
const onFollow = async (item: PostItem) => {
  const { is_follow, is_mutual_follow } = await follow({ intl_openid: item.user.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.user.intl_openid,
    is_followed: is_follow ? 1 : 0,
    is_mutual_follow: is_mutual_follow ? 1 : 0,
  });
  refetchUserInfo();
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  if (item.my_upvote?.is_star) {
    item.my_upvote = { ...item.my_upvote, is_star: false };
    item.upvote_count = +item.upvote_count - 1;
  } else {
    item.my_upvote = { ...item.my_upvote, is_star: true };
    item.upvote_count = +item.upvote_count + 1;
  }
};

const onShare = async (item: PostItem) => {
  const { forward_count } = await usePostForward.run({
    post_uuid: item.post_uuid,
  });
  item.forward_count = forward_count;
};

const onDetail = (item: PostItem) => {
  setPostData(item, list.value);
};

onEventEmitter(EVENT_NAMES.refresh_post_list_item_info, (v) => {
  if (!v?.post_uuid) return;
  updatePostsData(v, list.value, v);
});

onEventEmitter(EVENT_NAMES.user_status_change, (v) => updateUserStatusInPostList(list.value, v));
</script>
