<template>
  <div class="relative z-[1] p-[10px] bg-[var(--fill-2)]">
    <div class="h-[28px] flex items-center justify-between" @click="router.push('/points')">
      <div class="flex items-center">
        <div
          class="font-Abolition text-[color:var(--text-1)] text-[length:var(--font-size-xl)] leading-[20px] mr-[8px]"
        >
          {{ t("reward") }}
        </div>
        <i
          class="w-[20px] h-[20px] mr-[4px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%]"
        ></i>
        <div
          class="font-bold font-[DINNextLTProBold] mt-[2px] text-[color:var(--text-1)] text-[length:14px] leading-[1]"
        >
          {{ user_store.total_point }}
        </div>
      </div>
      <div
        class="bg-clip flex items-center justify-end cursor-pointer bg-[image:var(--linear-gradient-3)] h-full pl-[10px] pr-[7px]"
      >
        <i
          class="w-[20px] h-[23px] bg-[url('@/assets/imgs/user-center/icon-gift.png')] bg-[length:100%_100%] mr-[6px]"
        ></i>
        <div
          class="font-Abolition text-[color:var(--color-white)] text-[length:var(--font-size-l)] leading-[1]"
        >
          {{ t("redeem_rewards") }}
        </div>
        <SvgIcon
          name="icon-arrow-right3"
          color="var(--color-white)"
          class="w-[10px] h-[10px] ml-[2px]"
        ></SvgIcon>
      </div>
    </div>
    <DailyQuestion
      class="mt-[10px]"
      page="usercenter"
      @click="router.push('/points')"
    ></DailyQuestion>
    <RefreshTag
      class="text-[color:var(--text-3)] text-[length:10px] font-[400] mt-[10px]"
    ></RefreshTag>
  </div>
</template>

<script setup lang="ts">
import DailyQuestion from "@/components/points/home/<USER>/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useI18n } from "vue-i18n";
import { useUser } from "@/store/user";
import { useRouter } from "vue-router";
import RefreshTag from "@/components/points/home/<USER>/index.vue";

const router = useRouter();
const { t } = useI18n();
const user_store = useUser();
</script>

<style scoped lang="scss">
.bg-clip {
  clip-path: polygon(6px 0%, 100% 0, 100% calc(100% - 6px), calc(100% - 6px) 100%, 0 100%, 0% 6px);
}
</style>
