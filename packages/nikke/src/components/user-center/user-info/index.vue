<template>
  <div
    v-if="!!player_info?.has_saved_role_info || is_self"
    class="mx-[15px] z-[2] pt-[10px] min-h-[107px] bg-[var(--op-fill-white)] rounded-[8px] bg-[url('@/assets/imgs/user-center/bg-user-info2.png')] bg-[length:100%_100%] cursor-pointer"
    @click="onCardClick"
  >
    <!-- !!player_info?.has_saved_role_info -->
    <template v-if="!!player_info?.has_saved_role_info">
      <div class="flex px-[15px]">
        <div
          class="w-[45px] h-[45px] bg-[var(--fill-3)] flex-shrink-0 rounded-[4px] mr-[8px] relative z-[1]"
        >
          <!-- <img
            src="@/assets/imgs/test/avatar.png"
            class="w-full h-full rounded-full object-cover"
            alt=""
          /> -->
          <RoleAvatar
            class="role-avatar"
            :default_avatar="default_avatar"
            :avatar_id="player_info?.icon ?? 0"
          />
        </div>
        <div v-if="!!player_info?.has_saved_role_info" class="mt-[5px]">
          <div
            class="text-[length:16px] leading-[21px] font-bold text-[color:var(--color-white)] truncate"
          >
            {{ player_info?.role_name }}
          </div>
          <div class="-mt-[1px] text-[length:10px] leading-[1] text-[color:var(--color-white)]">
            <!-- {{ player_info?.role_info?.area_id }} -->
            {{ region_name }} - Lv.{{ player_info?.player_level }}
          </div>
        </div>
      </div>

      <!-- <TypeData class="flex-1" :list="user_tags"></TypeData> -->
      <div class="mt-[4px] flex">
        <div
          v-for="(item, index) in user_tags"
          :key="index"
          class="flex-1 text-center flex flex-col justify-end relative px-[2px]"
        >
          <div
            v-if="item?.label"
            class="h-[10px] min-w-[30px] max-w-max flex items-center justify-center bg-[var(--brand-1)] mx-auto px-[3px] text-[length:var(--font-size-s)] leading-[1] text-[color:var(--color-white)]"
          >
            <span class="font-Abolition">{{ item?.label }}</span>
          </div>
          <div class="font-bold text-[length:14px] leading-[1] text-[color:var(--text-1)] mt-[3px]">
            {{ item?.value }}
          </div>
          <div class="flex justify-center mt-[7px] min-h-[18px]">
            <span
              class="font-Abolition text-[length:var(--font-size-s)] leading-[1] text-[color:var(--text-3-60)]"
            >
              {{ item?.title }}
            </span>
          </div>
          <i
            v-if="index < user_tags.length - 1"
            class="w-[0.5px] h-[14px] bg-[var(--line-1)] absolute bottom-[10px] right-0"
          ></i>
        </div>
      </div>
    </template>
    <!--未绑定角色 is_self && !player_loading-->
    <template v-else-if="is_self && !player_loading">
      <div class="flex px-[15px] justify-between">
        <div
          class="w-[45px] h-[45px] bg-[var(--fill-3)] flex-shrink-0 rounded-[4px] mr-[8px] relative z-[1]"
        >
          <img
            src="@/assets/imgs/test/avatar.png"
            class="w-full h-full rounded-full object-cover"
            alt=""
          />
        </div>
        <div class="relative cursor-pointer">
          <div
            class="px-[6px] flex items-center justify-center text-[length:var(--font-size-l)] leading-[1] text-[color:var(--color-white)] w-[64px] h-[24px] bg-[url('@/assets/imgs/user-center/bg-user-info-btn.png')] bg-[length:100%_100%]"
            @click="() => makeSureBindRole()"
          >
            <span class="font-Abolition"> {{ t("bind") }}</span>
          </div>
        </div>
      </div>

      <div
        class="line-clamp-3 text-[length:13px] leading-[16px] text-[color:var(--text-3)] px-[15px] mt-[8px]"
      >
        {{ t("bind_game_character_tips") }}
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useUserCenter } from "@/composables/use-user-center";
import { useGameRegion } from "@/composables/role/use-server-info";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { base64Encode } from "packages/utils/encrypt.ts";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
import default_avatar from "@/shiftyspad/assets/images/appicon.png";

const { t } = useI18n();
const router = useRouter();

const { makeSureBindRole } = useBindRole();
const { player_info, user_tags, player_loading, intl_openid } = useUserCenter();
const { is_self } = useUserCenter();
const { server_list } = useGameRegion({
  all: true,
});

const region_name = computed(() => {
  return (
    server_list.value?.find((item) => item.area_id + "" == player_info.value?.area_id + "")
      ?.area_name ?? "-"
  );
});

const onCardClick = () => {
  if (player_info.value?.has_saved_role_info) {
    if (is_self.value) {
      router.push({ path: Routes.SHIFTYSPAD_ROOT });
    } else {
      router.push({
        path: Routes.SHIFTYSPAD_ROOT,
        query: { [COMMON_QUERY_KEYS.EncodedUid]: base64Encode(intl_openid.value) },
      });
    }
  }
};
</script>

<style scoped lang="scss">
.user-info-link-pop {
  clip-path: polygon(7px 0%, 100% 0, 100% 100%, 0 100%, 0% 7px);
}
</style>
