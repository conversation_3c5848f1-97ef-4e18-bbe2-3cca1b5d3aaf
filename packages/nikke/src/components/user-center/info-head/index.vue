<template>
  <div class="w-full h-auto relative bg-[length:100%_100%] pt-[44px]">
    <div class="relative z-[2] mt-[10px] px-[16px] pb-[10px]">
      <div class="flex items-center justify-between">
        <div class="flex items-center mr-[12px] gap-[12px] flex-1 w-0">
          <div
            class="w-[56px] h-[56px] cursor-pointer"
            :class="[user_info?.avatar_pendant && '!mr-[4px] mb-[4px]']"
            @click="onEditInfo"
          >
            <Avatar
              :src="user_info?.avatar || ``"
              :auth_type="user_info?.auth_type"
              :frame="user_info?.avatar_pendant"
            />
          </div>
          <div class="flex-1 overflow-hidden">
            <div class="flex items-center overflow-hidden">
              <span
                class="font-bold text-[length:18px] leading-[22px] truncate text-[color:var(--color-white)] mr-[8px]"
              >
                {{
                  is_self
                    ? user_store.user_info?.audit_username || user_store.user_info?.username
                    : user_info?.username
                }}
              </span>
              <div
                v-if="is_self"
                class="w-[24px] h-[24px] flex-shrink-0 cursor-pointer rounded-full bg-[var(--color-white-20)] flex justify-center items-center flex-none"
                @click="onEditInfo"
              >
                <SvgIcon name="icon-edit" color="var(--text-4)" class="w-[10px] h-[10px]"></SvgIcon>
              </div>
            </div>
            <div
              v-if="is_self"
              class="mt-[2px] text-[length:10px] leading-[13px] text-[var(--text-4)]"
            >
              <!-- 等后端提供对应的字段 -->
              ID:{{ user_info?.intl_openid?.split("-")?.[1] || user_info?.intl_openid }}
            </div>
          </div>
        </div>
        <div class="flex-none flex items-center">
          <div class="flex flex-col justify-end cursor-pointer gap-[8px]">
            <span
              class="font-[DINNextLTProBold] text-right leading-[19px] text-[color:var(--color-white)] text-[length:16px]"
              @click="onFollow"
            >
              {{ user_info?.follow_num ?? 0 }}
            </span>
            <span
              class="font-[DINNextLTProBold] text-right leading-[19px] text-[color:var(--color-white)] text-[length:16px]"
              @click="onFans"
            >
              {{ user_info?.fans_num ?? 0 }}
            </span>
          </div>
          <div class="flex flex-col justify-end cursor-pointer gap-[8px]">
            <span
              class="text-right text-[color:var(--text-4)] text-[length:13px] leading-[19px] ml-[16px]"
              @click="onFollow"
            >
              {{ t(is_self ? "following" : "his_following", user_info?.follow_num ?? 0) }}
            </span>
            <span
              class="text-right text-[color:var(--text-4)] text-[length:13px] leading-[19px] ml-[16px]"
              @click="onFans"
            >
              {{ t(is_self ? "follower" : "his_follower", user_info?.fans_num ?? 0) }}
            </span>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between mt-[4px]">
        <!-- <TypeLabel @click="handleClickShowPopup"></TypeLabel> -->

        <TypeLabel
          :game_tag="user_info?.game_tag!"
          :game_tag_num="user_info?.game_tag_num!"
          :mood="user_info?.mood"
          :is_self="is_self"
          @click="handleClickShowPopup"
        ></TypeLabel>

        <div class="flex-1 flex items-center justify-end">
          <div
            v-for="(item, index) in links"
            :key="'homelink_' + index"
            class="cursor-pointer flex items-center justify-center w-[24px] h-[24px] rounded-full bg-[var(--color-white-20)] mr-[12px] last-of-type:mr-0"
          >
            <SvgIcon
              :name="item?.icon"
              color="var(--color-white)"
              class="h-[15px]"
              :class="[item.icon === 'icon-youtube' ? 'w-[17px]' : 'w-[auto]']"
              @click="handleClickShowPopup('link', item)"
            ></SvgIcon>
          </div>
          <div
            v-if="links.length === 0 && is_self"
            class="cursor-pointer flex items-center justify-center rounded-[20px] h-[24px] bg-[var(--color-white-20)] px-[6px]"
            @click="handleClickShowPopup('link')"
          >
            <SvgIcon name="icon-add" color="var(--color-white)" class="w-[12px] h-[12px]"></SvgIcon>
            <SvgIcon
              name="icon-link"
              color="var(--color-white)"
              class="w-[16px] h-[16px]"
            ></SvgIcon>
          </div>
        </div>
      </div>
      <div v-if="auth_type_img" class="flex items-center gap-4 mt-[8px]">
        <CommonImage :src="auth_type_img" class="w-[14px] h-[14px]" />
        <div class="text-[length:12px] leading-[14px] text-[color:var(--text-4)]">
          {{ user_info?.auth_desc }}
        </div>
      </div>
      <div v-if="remark" class="flex gap-4 mt-[8px]">
        <CommonImage :src="user_remark_img" class="w-[14px] h-[14px]" />
        <div class="text-[length:12px] leading-[14px] text-[color:var(--text-4)] line-clamp-2">
          {{ remark }}
        </div>
      </div>
    </div>
  </div>
  <GameTagPopup :visible="showGameTagPopup" @close="closePopup"></GameTagPopup>
  <StatusPopup :visible="showStatusPopup" @close="closePopup"></StatusPopup>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import TypeLabel from "@/components/common/type-label/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import GameTagPopup from "@/components/user-center/gameTag-popup/index.vue";
import StatusPopup from "@/components/user-center/status-popup/index.vue";
import LinkPopup from "@/components/user-center/link-popup/index.vue";
import { useRouter } from "vue-router";
import { showDialog } from "@/utils/dialog";
import { ChannelLink } from "@/api/user-links";
import { useUserCenter } from "@/composables/use-user-center";
import { useToast } from "@/components/ui/toast";
import Avatar from "@/components/common/avatar/index.vue";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { Routes } from "@/router/routes";
import { report } from "packages/utils/tlog";
import { useUser } from "@/store/user";

import { CommonImage } from "@/components/common/image";
import auth_type_1 from "@/assets/imgs/user-center/auth-type-01.png";
import auth_type_2 from "@/assets/imgs/user-center/auth-type-02.png";
import auth_type_3 from "@/assets/imgs/user-center/auth-type-03.png";
import auth_type_4 from "@/assets/imgs/user-center/auth-type-04.png";
import user_remark_img from "@/assets/imgs/user-center/user-remark.png";
import { useWebCredential } from "@/composables/use-webcredential";
const { makeSureBindRole } = useBindRole();

const { t } = useI18n();
const router = useRouter();
withDefaults(
  defineProps<{
    // user_info?: UserInfo;
  }>(),
  {},
);
const user_store = useUser();
const { is_self, channel_links, is_show, user_info, player_info } = useUserCenter();
const { openUrlWithAuth } = useWebCredential();

const showGameTagPopup = ref(false);

const showStatusPopup = ref(false);

const showLinkPopup = ref(false);
const links = computed(() => {
  const list = channel_links.value.filter((item) => item.url);
  return list;
});
const handleClickShowPopup = (val: string, item?: ChannelLink) => {
  if (!is_self.value) {
    val === "link" && item?.url && openUrlWithAuth(item?.url, "_blank");
    return;
  }
  switch (val) {
    case "gameTag":
      report.standalonesite_usercenter_tag_btn.cm_click();
      if (!player_info.value) return;
      if (!player_info.value.has_saved_role_info) return makeSureBindRole();
      return (showGameTagPopup.value = true);
    case "status":
      report.standalonesite_usercenter_emo_btn.cm_click();
      return (showStatusPopup.value = true);
    case "link":
      report.standalonesite_usercenter_link_btn.cm_click();
      if (!links.value.length) {
        return router.push({ path: Routes.USER_LINK_MANAGE });
      }
      return showDialog(LinkPopup, { list: channel_links.value });
  }
};

const closePopup = () => {
  showGameTagPopup.value = false;
  showStatusPopup.value = false;
  showLinkPopup.value = false;
};

const { show } = useToast();
const onFollow = () => {
  if (!is_show("show_my_follow")) {
    return show({ text: t("user_already_set_private"), type: "warning" });
  }
  router.push({ path: "/user/following", query: { openid: user_info.value?.intl_openid } });
};

const onFans = () => {
  if (!is_show("show_my_fans")) {
    return show({ text: t("user_already_set_private"), type: "warning" });
  }
  router.push({ path: "/user/follower", query: { openid: user_info.value?.intl_openid } });
};

const onEditInfo = () => {
  if (is_self.value) {
    report.standalonesite_usercenter_edit_btn.cm_click();
    router.push("/user/edit-account");
  }
};

const auth_type_img = computed(() => {
  if (!user_info.value?.auth_type) return undefined;
  return (
    {
      1: auth_type_1,
      2: auth_type_2,
      3: auth_type_3,
      4: auth_type_4,
    }[user_info.value?.auth_type] || undefined
  );
});

const remark = computed(() => {
  return is_self.value
    ? user_store.user_info?.audit_remark || user_store.user_info?.remark
    : user_info.value?.remark;
});
</script>
