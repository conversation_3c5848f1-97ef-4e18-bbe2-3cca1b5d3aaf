<template>
  <CommBottomPopup :show="visible" @close="cancel()">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[14px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] leading-[21px]">
          {{ t("home_links") }}
        </div>
        <div class="cursor-pointer" @click="finish()">
          <span
            class="text-[color:var(--brand-1)] text-[length:14px]"
            @click="router.push('/user/links-manage')"
            >{{ t("manage") }}</span
          >
        </div>
      </div>
    </template>
    <div class="px-[20px] pb-[30px]">
      <LinkItem
        v-for="(item, index) in list?.filter((item) => item.url) || []"
        :key="'link' + index"
        :item="item"
        :disabled="true"
        class="mt-[16px] first:mt-0"
      ></LinkItem>
      <NoData v-if="!list?.filter((item) => item.url)?.length"></NoData>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import LinkItem from "@/components/common/link-item/index.vue";
import { BaseDialog } from "@/utils/dialog";
import { ChannelLink } from "@/api/user-links";
import { useRouter } from "vue-router";
import NoData from "@/components/common/nodata.vue";

const router = useRouter();
const { t } = useI18n();
interface Props extends BaseDialog {
  list?: ChannelLink[];
}

defineProps<Props>();
</script>
