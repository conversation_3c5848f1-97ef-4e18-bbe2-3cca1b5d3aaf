<template>
  <CommBottomPopup :show="visible" @close="handleClosePopup">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[16px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] leading-[21px]">
          {{ t("game_tag") }}
        </div>
        <div class="cursor-pointer" @click="onConfirm">
          <span class="text-[color:var(--brand-1)] text-[length:14px] font-bold">
            {{ t("confirm") }}
          </span>
        </div>
      </div>
    </template>
    <div class="items-center flex-wrap px-[20px] gap-x-[16px] grid grid-cols-3 pb-[14px]">
      <div
        v-for="(item, index) in game_tag"
        :key="'game_tag_' + index"
        :class="[
          `h-[100px] mb-[16px] pt-[16px] pb-[12px] flex flex-col items-center justify-center cursor-pointer whitespace-nowrap rounded-[8px] border-[1px]`,
          selected === item.id
            ? 'bg-[color:var(--brand-2)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)]'
            : 'bg-[color:var(--fill-3)] border-[color:transparent]',
        ]"
        @click="onCheck(item)"
      >
        <CommonImage :src="item.icon" class="p-[5px] w-[40px] h-[40px] mb-[8px] flex-none" />
        <div
          class="text-[length:12px] text-[color:var(--text-3)] leading-[15px]"
          :class="[
            getStandardizedLang() === 'ja' && '!text-[9px]',
            getStandardizedLang() === 'en' && '!text-[11px]',
          ]"
        >
          {{ t(item.i18n) }}
        </div>
      </div>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import { tags_config, PlayerTag } from "@/utils/player-tags";
import { useUserCenter } from "@/composables/use-user-center";
import { CommonImage } from "@/components/common/image";
import { getStandardizedLang } from "packages/utils/standard";
// import { useplayerInfo } from "@/store/player-info";
const { t } = useI18n();
const props = defineProps<{
  visible?: boolean;
}>();
const emits = defineEmits(["change", "close"]);
// const store = useplayerInfo();
const game_tag = ref<PlayerTag[]>(tags_config);

const { modifyPlayerTag, user_info } = useUserCenter();

// 获取选择的TAG
const current_tag = computed(() => {
  const target = game_tag.value.find((item) => item.id === user_info.value?.game_tag);
  return target;
});

const selected = ref(0);
const onCheck = async (tag: PlayerTag) => {
  selected.value = tag.id;
};
const onConfirm = async () => {
  const tag = game_tag.value.find((item) => item.id === selected.value);
  if (tag && tag.id !== current_tag.value?.id) {
    await modifyPlayerTag({ game_tag: tag.id });
    emits("change", tag);
  }
  handleClosePopup();
};
const handleClosePopup = () => {
  emits("close");
};

watch(
  () => [current_tag.value, props.visible],
  () => {
    if (props.visible && current_tag.value) {
      selected.value = current_tag.value.id;
    }
  },
);
</script>
