## usage

```vue
<template>
  <Dropdown
    :open="isOpen"
    :list="list"
    side="bottom"
    align="end"
    :active="activeId"
    @change="handleChange"
    @open="handleOpen"
  >
    <template #trigger="{ item }">
      <SelectHead :text="item.text" />
    </template>
    <template #default="{ item, index }">
      <div class="text-[color:var(--text-1)] text-[length:12px] leading-[14px]">
        {{ item?.text }}
      </div>
      <svgIcon
        v-show="activeId === index"
        name="icon-true"
        color="var(--brand-1)"
        class="w-[12px] h-[12px]"
      ></svgIcon>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
import Dropdown from "@/components/ui/dropdown/index.vue";
</script>
```
