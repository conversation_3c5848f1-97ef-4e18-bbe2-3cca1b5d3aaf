<template>
  <TooltipProvider>
    <TooltipRoot :open="open">
      <TooltipTrigger v-if="$slots.trigger" @click="handleTrigger">
        <slot name="trigger" :item="curOption" />
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          ref="target"
          :side="side"
          :align="align"
          :side-offset="sideOffset || 0"
          class="comm-dropdown z-[21] bg-[color:var(--op-fill-white)] shadow-[0_0_10px_0_var(--op-shadow-black-5)] max-w-[335px] min-w-[64px] !duration-200 origin-[50%_0%] data-[state=closed]:opacity-0 data-[side=bottom]:opacity-1"
          :class="[
            type === 'customize' ? 'z-[30]' : 'p-[12px] z-[21] max-h-[45vh] overflow-y-auto',
            open ? 'active' : '',
            border ? 'border-[1px] border-[color:var(--line-1)]' : '',
          ]"
        >
          <ul v-if="list">
            <li
              v-for="(item, index) in list"
              :key="index"
              :class="[
                `flex items-center justify-between mb-[12px] last-of-type:mb-0 cursor-pointer`,
                active === item.value ? 'active' : '',
              ]"
              @click="handleChange(item)"
            >
              <slot
                :item="item"
                :index="index"
                :active="active === item.value"
                :first="index === 0"
                :last="index === list.length - 1"
              >
                {{ item }}
              </slot>
            </li>
          </ul>
          <template v-if="type === 'customize'">
            <div class="pointer-events-none absolute z-[11] top-[-6.5px] left-0 w-full h-[7px]">
              <img src="@/assets/imgs/common/dropdown/bg-top.png" class="w-full h-full" />
            </div>
            <div
              class="pointer-events-none absolute z-[11] bottom-[-5px] left-0 w-full h-[6px] bg-[color:var(--op-fill-white)]"
            >
              <img src="@/assets/imgs/common/dropdown/bg-bottom.png" />
            </div>
          </template>
        </TooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
</template>

<script setup lang="ts">
import { computed, ref, onUnmounted } from "vue";
import { onClickOutside } from "@vueuse/core";
import { DropdownItem, Dropdown, DropdownEmits } from "packages/types/dropdown";

import {
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from "radix-vue";

const props = withDefaults(defineProps<Dropdown>(), {
  side: "bottom",
  align: "center",
  open: false,
  sideOffset: 2,
  border: true,
});
const emit = defineEmits<DropdownEmits>();
const target = ref<HTMLElement>();
let hide_timer: ReturnType<typeof setTimeout>;
const is_lock = ref(false);
onClickOutside(target, () => {
  if (props.open) {
    emit("close");
    is_lock.value = true;
    hide_timer = setTimeout(() => {
      is_lock.value = false;
    }, 100);
  }
});

const handleChange = <T = {},>(item: DropdownItem<T>) => {
  emit("change", item);
  handleTrigger();
};
const handleTrigger = () => {
  if (is_lock.value) return;
  if (props.open) {
    emit("close");
  } else {
    emit("open", true);
  }
};
const curOption = computed<DropdownItem>(() => {
  if (props.active !== undefined && props.list) {
    return props.list.find((item) => item.value === props.active) || { name: "", value: "" };
  } else {
    return { name: "", value: "" };
  }
});
onUnmounted(() => {
  hide_timer && clearTimeout(hide_timer);
});
</script>
<style lang="scss">
.comm-dropdown {
  opacity: 0;
  transform: scaleY(0);
  transform-origin: 50% 0%;
  transition: all 0.2s cubic-bezier(0, 0.57, 0.43, 1);
}
.comm-dropdown.active {
  opacity: 1;
  transform: scaleY(1);
}

.comm-dropdown .active {
  .dropdown-text {
    color: var(--brand-1);
  }
}
</style>
