<template>
  <div class="fixed top-1/2 left-1/2 !-translate-x-1/2 !-translate-y-1/2 z-[1000]">
    <div
      v-if="visible"
      ref="toast_content_ref"
      :class="[
        `flex items-center px-[12px] py-[10px] bg-[var(--color-black-85)] min-w-[125px] w-max max-w-[298px]`,
        render_type === 'success' || render_type === 'loading' || render_type === 'error'
          ? 'flex-col'
          : 'flex-row',
      ]"
    >
      <span
        v-if="computed_icon.name"
        class="p-[2px] box-content"
        :class="[
          render_type === 'success' || render_type === 'loading' || render_type === 'error'
            ? 'w-[50px] h-[50px]'
            : 'w-[20px] h-[20px] mr-[8px]',
        ]"
      >
        <SvgIcon
          :class="render_type === 'loading' ? 'block icon-loading' : ''"
          :name="computed_icon.name"
          :color="computed_icon.color"
        ></SvgIcon>
      </span>
      <div
        class="flex-1 font-normal text-[length:13px] leading-[16px] text-[color:var(--color-white)] hyphens-auto break-words"
      >
        {{ render_text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { Toast } from "packages/types/toast";
import { pop, useMotion } from "@vueuse/motion";

const props = withDefaults(defineProps<Toast>(), {
  interval: 2000,
  type: "default",
  auto_close: true,
});

const emits = defineEmits<{
  hide: [];
}>();

const visible = ref(false);
const render_text = ref(props.text);
const render_interval = ref(props.interval);
const render_type = ref(props.type);
const toast_content_ref = ref();

let hide_timer: ReturnType<typeof setTimeout>;

const computed_icon = computed(() => {
  switch (render_type.value) {
    case "success":
      return { name: "icon-success-large", color: "var(--brand-1)" };
    case "error":
      return { name: "icon-error", color: "var(--error)" };
    case "info":
      return { name: "icon-info", color: "var(--color-white)" };
    case "warning":
      return { name: "icon-warn", color: "var(--color-white)" };
    case "loading":
      return { name: "icon-loading-large", color: "var(--color-white)" };
    default:
      return { name: "", color: "" };
  }
});

const show = ({ text, interval, type, auto_close }: Toast) => {
  if (text) {
    render_text.value = text;
  }
  if (interval) {
    render_interval.value = interval;
  }
  if (type) {
    render_type.value = type;
  }
  visible.value = true;

  if (auto_close !== false) {
    hide_timer = setTimeout(() => {
      hide();
      emits("hide");
    }, render_interval.value);
  }
};

const hide = () => {
  visible.value = false;

  hide_timer && clearTimeout(hide_timer);
};

// 暴露方法
defineExpose({
  show,
  hide,
});
useMotion(toast_content_ref, pop);
</script>

<style lang="scss">
.icon-loading {
  display: block;
  animation: loading 2s linear infinite;
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
