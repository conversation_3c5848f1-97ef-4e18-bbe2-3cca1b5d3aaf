import { mountToastComponent } from "@/utils/mount";
import Toast from "./index.vue";
import { Toast as Type_Tost } from "packages/types/toast";
/**
 * 一个快捷使用Toast组件的 hooks
 * @returns
 */
export function useToast() {
  const show = (item: Type_Tost) => {
    const { instance, unmount } = mountToastComponent(Toast, {
      onHide: () => {
        unmount();
      },
    });
    instance.show(item);
  };
  return { show };
}

export { Toast };
