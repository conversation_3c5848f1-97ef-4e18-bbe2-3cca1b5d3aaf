## useage

```vue
<template>
  <Button @click="handleOpen(true)">open toast</Button>
</template>
<script lang="ts" setup>
import { ref, onUnmounted } from "vue";
import { useToast } from "@/components/ui/toast";

import Button from "@/components/ui/button/index.vue";

const { show } = useToast();
const isOpen = ref(false);

let hideTimer: ReturnType<typeof setTimeout>;

const handleOpen = (v: boolean) => {
  isOpen.value = v;
  show({ text: "测试一下", type: "warning" });
};

onUnmounted(() => {
  hideTimer && clearTimeout(hideTimer);
});
</script>
```
