<template>
  <div
    class="flex cursor-pointer justify-center h-[40px] p-[5px] text-[length:14px] leading-[16px] font-bold items-center"
    :class="computed_class"
    @click="emit('click')"
  >
    <template v-if="loading">
      <img
        src="@/assets/imgs/common/icon-ellipse.png"
        class="w-[25px] h-[25px] object-contain common-rotate"
      />
    </template>
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
const props = withDefaults(
  defineProps<{
    ghost?: boolean;
    disabled?: boolean;
    type?: "primary" | "secondary" | "default";
    shape?: "default" | "circle" | "round";
    size?: "lg" | "sm" | "md" | "full";
    linear?: boolean;
    loading?: boolean;
  }>(),
  {
    ghost: false,
    disabled: false,
    shape: "default",
    type: "secondary",
    size: "md",
    linear: false,
    loading: false,
  },
);

const emit = defineEmits<{
  (e: "click"): void;
}>();

const computed_class = computed(() => {
  if (props.disabled) return `text-[color:var(--text-4)] bg-[color:var(--fill-2)]`;
  if (props.type === "primary") {
    return `text-[color:var(--color-white)] bg-[color:var(--brand-1)]`;
  } else if (props.type === "secondary") {
    return `text-[var(--brand-1)] bg-[color:var(--op-fill-white)]`;
  } else {
    return "";
  }
});
</script>
