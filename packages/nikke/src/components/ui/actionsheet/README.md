## usage

```vue
<template>
  <Button @click="handleOpen(true)">open actionsheet</Button>
  <ActionSheet
    title="Action Sheet"
    :visible="isOpen"
    :actions="langs"
    :active-id="activeId"
    :click-mask-close="true"
    @change="handleChange"
    @close="handleClose"
  >
    <template #item="{ item, active }">
      <div class="text-[color:var(--text-1)] text-[length:12px] leading-[14px]">
        {{ item?.name }}
      </div>
      <svgIcon
        v-show="active"
        name="icon-true"
        color="var(--brand-1)"
        class="w-[12px] h-[12px]"
      ></svgIcon>
    </template>
  </ActionSheet>
</template>
<script lang="ts" setup>
import { ref, onUnmounted } from "vue";
import ActionSheet from "@/components/ui/actionsheet/index.vue";
import Button from "@/components/ui/button/index.vue";
import svgIcon from "@/components/common/svg-icon.vue";
const isOpen = ref(false);
const activeId = ref("en");
const langs = ref([
  { name: "en", label: "English" },
  { name: "jp ", label: "日本語" },
  { name: "kr", label: "한국어" },
  { name: "zh-cn", label: "简体中文" },
  { name: "zh-tw", label: "繁體中文" },
  { name: "ru", label: "한국어 RU" },
  { name: "tl", label: "简体中文 TL" },
  { name: "zh", label: "繁體中文 ZH" },
]);
let hideTimer: ReturnType<typeof setTimeout>;
const handleChange = (item: { name: string; label: string }) => {
  console.log(item);
  const findIndex = langs.value.findIndex((v) => v.name === item.name);
  if (findIndex !== -1) {
    activeId.value = langs.value[findIndex].name;
  }
  hideTimer = setTimeout(() => {
    handleClose();
  }, 300);
};
const handleOpen = (v: boolean) => {
  console.log("handleOpen", v);
  isOpen.value = v;
};
const handleClose = () => {
  isOpen.value = false;
};
onUnmounted(() => {
  hideTimer && clearTimeout(hideTimer);
});
</script>
```
