<template>
  <DialogRoot :open="visible">
    <DialogPortal>
      <Transition name="fade">
        <DialogOverlay
          class="fixed inset-0 z-50 bg-[var(--color-black-55)]"
          @click="handleMaskClick"
        />
      </Transition>
      <Transition name="slide">
        <DialogContent
          class="w-full outline-none max-h-[65vh] max-w-[var(--max-pc-w)] right-0 mx-auto overflow-x-hidden overflow-y-auto flex flex-col bg-[var(--op-fill-white)] rounded-t-[8px] fixed left-0 bottom-0 z-50"
        >
          <div class="pointer-events-none absolute z-[-1] top-[5px] right-[5px] w-[161px] h-[54px]">
            <img src="@/assets/imgs/common/actionsheet/bg-top.png" class="w-full h-full" />
          </div>
          <div class="pointer-events-none absolute z-[-1] bottom-[9px] left-[9px] w-full h-[12px]">
            <img src="@/assets/imgs/common/actionsheet/bg-bottom.png" class="w-full h-full" />
          </div>

          <DialogTitle
            class="font-bold text-[length:16px] text-[color:var(--text-1)] leading-[19px] mb-[14px] px-[20px] pt-[20px]"
            >{{ title }}</DialogTitle
          >
          <div
            v-if="text"
            class="mt-[16px] min-h-[32px] text-[length:13px] text-[color:var(--text-1)] text-center leading-[16px]"
          >
            <slot />
          </div>
          <div class="flex-1 overflow-y-auto w-full mr-[4px] mb-[35px]">
            <ul v-if="actions" class="w-full px-[20px] box-border">
              <li
                v-for="(action, index) in actions"
                :key="index"
                class="flex items-center justify-between mb-[14px] last-of-type:mb-0 cursor-pointer h-[40px] px-[10px] bg-[var(--fill-2)] border-width-1 border-[var(--line-1)]"
                @click="handleChange(action)"
              >
                <template v-if="$slots.item">
                  <slot
                    name="item"
                    :item="action"
                    :index="index"
                    :active="active_id === action.value"
                  />
                </template>
                <template v-else>
                  <span class="text-[color:var(--text-1)]">{{ action.label }}</span>
                  <span></span>
                </template>
              </li>
            </ul>
          </div>

          <Button v-if="confirm_text" type="primary" class="mb-[6px]" @click="emit('confirm')">
            {{ confirm_text }}
          </Button>
          <Button v-if="cancel_text" type="secondary" class="mb-[6px]" @click="emit('cancel')">
            {{ cancel_text }}
          </Button>
        </DialogContent>
      </Transition>
    </DialogPortal>
  </DialogRoot>
</template>

<script lang="ts" setup>
import { DialogRoot, DialogContent, DialogTitle, DialogOverlay, DialogPortal } from "radix-vue";
import Button from "@/components/ui/button/index.vue";
import { ActionItem, ActionSheet, ActionSheetEmits } from "packages/types/actionsheet";

const props = defineProps<ActionSheet>();

const emit = defineEmits<ActionSheetEmits>();

const handleChange = (item: ActionItem) => {
  emit("change", item);
};

const handleMaskClick = () => {
  if (props?.click_mask_close) {
    emit("close");
  }
};
</script>
<style lang="scss">
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform opacity 0.3s ease;
  transform: translate(0, 0, 0);
}

.slide-enter-from,
.slide-leave-to {
  opacity: 0;
  transform: translateY(0, 100%, 0);
}
</style>
