import { mountComponent, resolvedOptions, usePopupState } from "@/utils/mount";
import h from "@/utils/h";
import { ConfirmProps } from "packages/types/confirm";
const Confirm = () => import("./dialog/index.vue");

enum PopType {
  confirm = "confirm",
  GiftPop = "GiftPop",
}

type PopKey = keyof typeof PopType;

const pop_instance: any = Object.keys(PopType).reduce((acc, cur) => {
  return {
    [cur]: null,
    ...acc,
  };
}, {});

const initPopInstance = async (pop_cpnt: any, pop_key: PopKey, options: any) => {
  const PopCpnt = pop_cpnt;
  const { instance, unmount } = mountComponent({
    setup() {
      const { state, toggle } = usePopupState(options);

      const close = () => {
        toggle(false);
        unmount();
        pop_instance[pop_key] = null;
      };

      const onUpdateShow = (show: boolean) => {
        // console.log("[initPopInstance][onUpdateShow] show", show);
        if (!show) {
          close();
        }
      };

      return {
        state,
        toggle,
        close,
        onUpdateShow,
      };
    },
    render: function () {
      // console.log("[initPopInstance] this.state", JSON.stringify(this.state));
      return h(PopCpnt, {
        on: {
          updateShow: (show: boolean) => this.onUpdateShow(show),
          close: this.close,
          cancel: this.close,
          confirm: this.close,
        },
        props: this.state,
      });
    },
  });

  pop_instance[pop_key] = instance;
};

export const detroyAllPop = () => {
  Object.keys(pop_instance).forEach((key) => {
    pop_instance[key]?.close();
  });
};

export const showPop = async (options: any, pop_cpnt: any, pop_key: PopKey) => {
  const p_c = typeof pop_cpnt === "function" ? (await pop_cpnt()).default : pop_cpnt;
  return new Promise((resolve) => {
    const resolved_options = resolvedOptions(options, {
      callback: (action: number, ...args: any) => {
        // 可以通过 show({ callback(action, args) {} }) 的形式来处理回调
        options.callback?.(action, ...args);
        // 通过 await show({}) 的形式
        resolve(action);
      },
    });
    if (!pop_instance[pop_key]) {
      initPopInstance(p_c, pop_key, resolved_options);
    }
    const instance = pop_instance[pop_key];
    instance.open(resolved_options);
  });
};

export const confirm = (options: ConfirmProps) => {
  return showPop(options, Confirm, PopType.confirm);
};
