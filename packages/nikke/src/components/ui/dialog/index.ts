import { showDialog } from "@/utils/dialog";
import Dialog from "./index.vue";
import { PopCallbackValue } from "packages/types/common";
import { ConfirmProps } from "packages/types/confirm";

export const useDialog = () => {
  let dialog: any;
  const show = (
    options: ConfirmProps & {
      callback: (options: { value: PopCallbackValue; close: () => void }) => void;
    },
  ) =>
    (dialog = showDialog(
      Dialog,
      Object.assign(options as any, {
        open: true,
        onConfirm: () => {
          options.callback({
            value: PopCallbackValue.confirm,
            close: dialog.unmount,
          });
        },
        onCancel: async () => {
          options.callback({
            value: PopCallbackValue.cancel,
            close: dialog.unmount,
          });
        },
        onClose: () => {
          options.callback({
            value: PopCallbackValue.close,
            close: dialog.unmount,
          });
        },
      }),
    ));
  return {
    show,
  };
};
