<template>
  <DialogRoot :open="show">
    <DialogPortal>
      <DialogOverlay
        :style="{ 'z-index': z_index || 50 }"
        class="fixed inset-0 bg-[var(--color-black-55)] !duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"
        @click="close"
      />
      <DialogContent
        :style="{ 'z-index': z_index || 50 }"
        class="w-[289px] min-h-[189px] bg-[var(--op-fill-white)] px-[31px] py-[12px] fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 !duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-75 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] outline-none"
      >
        <div class="absolute -top-[26px] left-0 w-full h-[27px]">
          <SvgIcon name="icon-pop-line" color="var(--op-fill-white)"></SvgIcon>
        </div>
        <DialogTitle
          class="text-center font-bold text-[length:18px] text-[color:var(--text-1)] leading-[22px] capitalize"
        >
          {{ title }}
        </DialogTitle>
        <div
          class="mt-[16px] min-h-[32px] text-[length:13px] text-[color:var(--text-1)] text-center leading-[16px]"
        >
          <template v-if="content">{{ content }}</template>
          <template v-else>
            <slot />
          </template>
        </div>

        <Button v-if="confirm_text" type="primary" class="mb-[6px] mt-[9px]" @click="confirm">
          {{ confirm_text }}
        </Button>
        <Button v-if="cancel_text" type="secondary" class="mt-[6px]" @click="cancel">
          {{ cancel_text }}
        </Button>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script lang="ts" setup>
import { ConfirmProps } from "packages/types/confirm";
import { DialogRoot, DialogContent, DialogTitle, DialogOverlay, DialogPortal } from "radix-vue";
import Button from "@/components/ui/button/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

defineProps<ConfirmProps>();

const emits = defineEmits<{
  (e: "confirm"): void;
  (e: "cancel"): void;
  (e: "close"): void;
}>();

const close = () => {
  emits("close");
};

const cancel = () => {
  emits("cancel");
};

const confirm = () => {
  emits("confirm");
};
</script>
