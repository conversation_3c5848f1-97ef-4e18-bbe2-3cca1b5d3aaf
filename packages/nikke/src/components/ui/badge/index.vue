<template>
  <div class="relative inline-flex items-center justify-end" :class="classes">
    <slot></slot>
    <span
      v-if="isGreaterThanZero"
      ref="badgeRef"
      class="absolute inline-flex items-center justify-center bg-[var(--error)] top-[-2px] text-[color:var(--color-white)] text-[length:9px] h-[14px] px-[6px] rounded-[14px]"
      :class="[
        isGreaterThanTen ? 'm-w-[14px]' : 'w-[14px]',
        badgeWidth ? `-right-[12px]` : `right-0`,
      ]"
      :badgeWidth="badgeWidth"
    >
      <span v-text="content"></span>
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

const isNumber = (value: unknown) =>
  Object.prototype.toString.call(value).slice(8, -1) === "Number";
const props = withDefaults(
  defineProps<{
    isDot?: boolean;
    value: string | number;
    type?: "primary" | "success" | "error" | "warning" | "info";
    max?: number;
    isShowWhenEmpty?: boolean;
  }>(),
  {
    isDot: false,
    type: "error",
    max: 99,
    isShowWhenEmpty: false,
  },
);
const badgeRef = ref();
const content = computed<string>(() => {
  if (props.isDot) return "";

  if (isNumber(props.value) && isNumber(props.max)) {
    return props.max < Number(props.value) ? `${props.max}+` : `${props.value}`;
  }
  return `${props.value}`;
});
const isGreaterThanTen = computed(() => Number(props.value) >= 10);
const isGreaterThanZero = computed(() => Number(props.value) > 0);
const classes = computed(() => {
  const { type } = props;
  return {
    [`comm-badge-content`]: true,
    [`--${type}`]: type,
    [`--empty`]: !props.isShowWhenEmpty && typeof props.value === "number" && props.value === 0,
  };
});
const badgeWidth = computed(() => {
  if (!isGreaterThanZero.value) {
    return 0;
  } else {
    badgeRef.value;
    const ret = badgeRef.value?.getBoundingClientRect();
    return Math.floor(ret?.width) % 2 === 1
      ? Math.floor(ret?.width) + 1
      : Math.floor(ret?.width) || 0;
  }
});
</script>
