<template>
  <div
    v-if="new_is_landscape"
    class="fixed z-[9999] w-screen h-screen flex flex-col justify-center items-center bg-[color:var(--color-1)]"
  >
    <SvgIcon
      name="icon-rotate"
      color="var(--color-white)"
      class="w-[40px] h-[40px] mb-[16px] icon-rotate"
    ></SvgIcon>
    <p class="font-normal text-[length:13px] leading-[16px] text-[color:var(--color-white)]">
      {{ t("please_rotate_your_device_for_better_display") }}
    </p>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// utils
import { useResponsive } from "@/composables/use-responsive";
import { t } from "@/locales";

const { new_is_landscape } = useResponsive();
</script>

<style lang="scss" scoped>
.icon-rotate {
  @apply animate-[icon-rotate_1.8s_ease_infinite];
}
@keyframes icon-rotate {
  0%,
  30% {
    transform: rotate(-90deg);
  }
  70%,
  100% {
    transform: rotate(0deg);
  }
}
</style>
