<template>
  <div class="flex flex-col gap-[12px]">
    <div class="bg-white px-[12px] py-[10px] flex flex-col gap-[12px]">
      <div class="text-[16px] font-medium text-[color:var(--text-1)]">
        {{ t("select_language") }}
      </div>
      <div class="grid grid-cols-3 gap-x-[8px] gap-y-[12px]">
        <div
          v-for="item in lang_list"
          :key="item.value"
          class="h-[28px] flex items-center justify-center text-[14px] text-black border cursor-pointer"
          :class="[
            active_id === item.value
              ? 'bg-[color:var(--brand-2)] border-[color:var(--brand-1)]'
              : 'bg-[color:var(--fill-3)] border-[color:var(--fill-7)]',
            item.value === 'en' ? '' : 'pt-[2px]',
          ]"
          @click="active_id = item.value"
        >
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="bg-white px-[12px] py-[10px] flex flex-col gap-[12px]">
      <div class="text-[16px] font-medium text-[color:var(--text-1)]">
        {{ t("select_region") }}
      </div>
      <div class="flex items-start justify-between gap-[20px]">
        <div v-fontfix class="text-[11px] text-[color:var(--color-7)] shrink grow max-w-[60%]">
          {{ t("select_region_tips") }}
        </div>
        <div ref="popup_dom" class="relative shrink grow-[2] max-w-[45%] text-right">
          <div
            class="flex items-center gap-[12px] cursor-pointer flex-1"
            @click="toggleOptionsPopup"
          >
            <div v-fontfix class="text-black text-[11px] flex-1">
              {{ region_label }}
            </div>
            <SvgIcon
              name="icon-arrow-right2"
              class="w-[12px] h-[12px] !text-black transition-transform duration-300"
              :class="is_region_popup_visible ? 'rotate-[-90deg]' : 'rotate-90'"
            />
          </div>
          <div
            v-if="is_region_popup_visible"
            class="absolute bg-white border border-[var(--fill-7)] w-[160px] p-[12px]"
            :class="[
              popup_position === 'top'
                ? 'right-[-6px] bottom-[calc(100%+6px)]'
                : 'right-[-6px] top-[calc(100%+6px)]',
            ]"
          >
            <div class="flex flex-col gap-[18px]">
              <Checkbox
                v-for="item in region_options"
                :key="item.value"
                :label="item.name"
                size="small"
                class="text-black text-[11px] !mb-0 gap-[6px]"
                :model-value="item.value === '' ? is_select_all : regions.includes(item.value)"
                @update:model-value="(v: any) => updateRegion(item.value, v)"
              ></Checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Checkbox from "@/components/common/checkbox/checkbox.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useLang } from "@/composables/use-lang";
import { t } from "@/locales";
import { computed, ref, watch } from "vue";
import { onClickOutside } from "@vueuse/core";
import { useSetUserRegions } from "@/api/user";
import { useUser } from "@/store/user";
import { isEqual } from "lodash-es";
import { useLanguageStore } from "@/store/language";
import { storeToRefs } from "pinia";

defineProps<{
  popup_position: "top" | "bottom";
}>();

const { onLangChange, lang_list, current_lang } = useLang();
const user = useUser();

const { lang_regions } = storeToRefs(useLanguageStore());
const { saveLocalRegions } = useLanguageStore();

const active_id = ref<string | number>(current_lang.value?.value);

const region_options = computed(() => {
  return [
    { value: "", name: t("all") },
    ...lang_list.value.map((i) => ({
      value: i.value,
      name: t(`lang_region_${i.value}`),
    })),
  ];
});

const is_region_popup_visible = ref(false);

const regions = ref<string[]>([]);

watch(
  () => lang_regions.value,
  () => {
    if (lang_regions.value) {
      regions.value = [...lang_regions.value];
      if (regions.value.length === 0 || regions.value.includes("all")) {
        regions.value = lang_list.value.map((i) => i.value);
      }
    }
  },
  { immediate: true, deep: true },
);

const is_select_all = computed(() => {
  return regions.value.length === lang_list.value.length;
});

const region_label = computed(() => {
  if (regions.value.length === 0) return "";
  return regions.value
    .map((i) => {
      const option = region_options.value.find((option) => option.value === i);
      return option?.name;
    })
    .join("/\u200B"); // 使用零宽度空格来支持换行
});

const updateRegion = (value: string, checked: boolean) => {
  if (value === "") {
    if (checked) {
      regions.value = lang_list.value.map((i) => i.value);
    } else {
      regions.value = [];
    }
  } else {
    if (checked) {
      regions.value.push(value);
    } else {
      regions.value = regions.value.filter((i) => i !== value);
    }
  }
};

const toggleOptionsPopup = () => {
  if (is_region_popup_visible.value) {
    hideOptionsPopup();
  } else {
    is_region_popup_visible.value = true;
    if (regions.value.length === 0) {
      regions.value = lang_list.value.map((i) => i.value);
    }
  }
};

const hideOptionsPopup = () => {
  is_region_popup_visible.value = false;
  if (regions.value.length === 0) {
    regions.value = lang_list.value.map((i) => i.value);
  }
};

const popup_dom = ref<HTMLElement>();
onClickOutside(popup_dom, hideOptionsPopup);

const { mutateAsync: setUserRegions } = useSetUserRegions();

defineExpose({
  save: async () => {
    hideOptionsPopup();
    if (!isEqual(user.user_info.regions, regions.value)) {
      if (user.is_login) {
        await setUserRegions({ regions: regions.value });
        user.refetchUserInfo();
      }
      saveLocalRegions(regions.value);
    }
    if (!isEqual(active_id.value, current_lang.value?.value)) {
      await onLangChange(
        lang_list.value.find((i) => i.value === active_id.value) || lang_list.value[0],
      );
    }
    setTimeout(() => {
      window.location.reload();
    }, 100);
    return {
      lang: active_id.value,
      regions: regions.value,
    };
  },
  is_region_popup_visible,
  region_selection_valid: computed(() => {
    return regions.value.length > 0;
  }),
});
</script>
