<template>
  <CommBottomPopup :show="true" @close="cancel">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[16px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] leading-[21px]">
          {{ t("region_and_language") }}
        </div>
      </div>
    </template>
    <div class="border-t-[1px] border-[var(--line-1)] mx-[20px]"></div>
    <LanguageSetting ref="language_setting" class="!px-[8px] !gap-0" popup_position="top" />
    <div class="border-t-[1px] border-[var(--line-1)] mx-[20px]"></div>
    <div class="px-[20px] pb-[12px]">
      <Button
        v-if="language_setting?.region_selection_valid"
        type="primary"
        class="mb-[6px] mt-[9px]"
        @click="save"
      >
        {{ t("confirm") }}
      </Button>
      <Button
        v-else
        class="mb-[6px] mt-[9px] !bg-[color:var(--fill-2)] !text-[color:var(--text-3-50)] !cursor-default"
      >
        {{ t("confirm") }}
      </Button>
      <Button type="secondary" class="mt-[6px]" @click="cancel">
        {{ t("cancel") }}
      </Button>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { BaseDialog } from "@/utils/dialog";
import LanguageSetting from "./LanguageSetting.vue";
import { ref } from "vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Button from "@/components/ui/button/index.vue";

export interface PopupProps extends BaseDialog {}

const props = defineProps<PopupProps>();

const language_setting = ref<InstanceType<typeof LanguageSetting>>();

const save = async () => {
  await language_setting.value?.save();
  props.finish();
};
</script>
