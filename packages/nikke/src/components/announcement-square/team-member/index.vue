<template>
  <div
    v-if="list.length"
    ref="container"
    :class="[
      `relative z-[1] bg-[var(--fill-3)] border-[1px] border-[color:var(--line-1)] px-[8px] py-[6px]`,
    ]"
  >
    <div
      class="absolute top-0 left-0 w-full h-full opacity-30 -z-[1] bg-[url('@/assets/imgs/announcement-square/brief-member-bg.png')] bg-[length:100%_auto] bg-repeat"
    ></div>
    <div
      :class="[`grid gap-y-[10px]`]"
      :style="{ gridTemplateColumns: `repeat(${max_item_each_row}, minmax(0, 1fr))` }"
    >
      <div class="w-[24px] h-[24px] flex-shrink-0">
        <CommonImage :src="iconcur"></CommonImage>
      </div>
      <div
        v-for="(its, index) in list.slice(0, open ? list.length : max_item_each_row - 1)"
        :key="index"
        class="w-[24px] h-[24px] rounded-full overflow-hidden flex-shrink-0 cursor-pointer"
        @click="itemClick(its)"
      >
        <CommonImage :src="its?.icon"></CommonImage>
      </div>
    </div>
    <div
      v-if="list.length > 9"
      :class="[
        `w-[24px] h-[24px] flex items-center justify-center cursor-pointer mx-auto mt-[4px] -mb-[4px] transition-all duration-200`,
        open && `scale-y-[-1]`,
      ]"
      @click="open = !open"
    >
      <SvgIcon
        name="icon-arrow-right"
        color="var(--text-3)"
        class="w-[24px] h-[12px] rotate-90"
      ></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { CommonImage } from "@/components/common/image";

import SvgIcon from "@/components/common/svg-icon.vue";

// import icon from "@/assets/imgs/announcement-square/icon-support-stick.png";
import iconcur from "@/assets/imgs/announcement-square/icon-support-stick-cur.png";
import { useElementSize } from "@vueuse/core";
import { SupportUserInfo } from "@/api/union";
import { useUnionStore } from "@/store/union";

const open = ref(false);

const props = defineProps<{
  list: SupportUserInfo[];
  is_support: boolean;
  guild_id: string;
}>();

const union_store = useUnionStore();

const container = ref<HTMLDivElement>();
const { width } = useElementSize(container);
const max_item_each_row = computed(() => {
  return Math.floor((width.value - 16) / (24 + 8));
});

const is_my_union = computed(() => union_store.isMyUnion(props.guild_id));

const emits = defineEmits<{
  (e: "toggle-support"): void;
  (e: "viewUser", item: SupportUserInfo): void;
}>();

const itemClick = (item: SupportUserInfo) => {
  emits("viewUser", item);
};
</script>
