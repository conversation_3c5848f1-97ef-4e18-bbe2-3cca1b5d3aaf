<template>
  <div class="relative flex-1 overflow-y-scroll px-[12px] pb-[10px]">
    <div
      v-if="is_my_union && item.is_published"
      class="absolute -top-[4px] right-[10px] w-[70px] h-[70px] z-[5] cursor-pointer"
      @click="toggleSupport"
    >
      <CommonImage :src="is_supporter ? imgscur : imgs"></CommonImage>
    </div>
    <TeamInfo :item="item"></TeamInfo>
    <div class="mt-[11px] flex flex-col gap-y-[8px]">
      <div class="flex h-[24px] gap-x-[8px]">
        <div class="flex-1 flex">
          <div
            class="w-[74px] h-full flex justify-center items-center flex-shrink-0 font-medium text-[length:9px] text-[color:var(--color-white)] bg-[url('@/assets/imgs/announcement-square/brief-item-bg.png')] bg-[length:100%_100%]"
          >
            {{ t("entry_level") }}
          </div>
          <div
            class="bg-[var(--fill-3)] border-[1px] border-[color:var(--line-1)] border-l-0 flex-1 h-full flex justify-center items-center flex-shrink-0 font-bold text-[color:var(--text-1)] text-[length:9px] font-[DINNextLTProBold]"
          >
            {{ item?.guild_entry_level }}
          </div>
        </div>
        <div class="flex-1 flex">
          <div
            class="w-[74px] h-full flex justify-center items-center flex-shrink-0 font-medium text-[length:9px] text-[color:var(--color-white)] bg-[url('@/assets/imgs/announcement-square/brief-item-bg.png')] bg-[length:100%_100%]"
          >
            {{ t("language") }}
          </div>
          <div
            class="bg-[var(--fill-3)] border-[1px] border-[color:var(--line-1)] border-l-0 flex-1 h-full flex justify-center items-center flex-shrink-0 font-bold text-[color:var(--text-1)] text-[length:9px] font-[DINNextLTProBold]"
          >
            {{ getLangName(item.guild_locale) }}
          </div>
        </div>
      </div>
      <div class="flex h-[24px]">
        <div
          class="w-[74px] h-full flex justify-center items-center flex-shrink-0 font-medium text-[length:9px] text-[color:var(--color-white)] bg-[url('@/assets/imgs/announcement-square/brief-item-bg.png')] bg-[length:100%_100%]"
        >
          {{ t("certificate_type") }}
        </div>
        <div
          class="bg-[var(--fill-3)] border-[1px] border-[color:var(--line-1)] border-l-0 flex-1 h-full flex justify-center items-center flex-shrink-0 font-bold text-[color:var(--text-1)] text-[length:9px] font-[DINNextLTProBold]"
        >
          {{ join_condition }}
        </div>
      </div>
      <div
        v-if="item.guild_description"
        class="bg-[var(--fill-3)] border-[1px] leading-[13px] border-[color:var(--line-1)] p-[12px] font-[Inter] text-[length:9px] font-semibold text-[color:var(--text-3)] text-center"
      >
        {{ item.guild_description }}
      </div>
      <TeamMember
        :list="item.support_user_infos"
        :is_support="is_supporter"
        :guild_id="item.guild_id"
        @view-user="viewUser"
        @toggle-support="toggleSupport"
      ></TeamMember>
    </div>
    <div
      v-if="show_post_card"
      class="mt-[16px] font-medium text-[length:11px] leading-[14px] text-[color:var(--text-1)]"
    >
      {{ t("union_post_title") }}
    </div>
    <Card
      v-if="show_post_card && hotPost?.post_info"
      :item="hotPost.post_info"
      class="mt-[8px] border-[1px] border-[color:var(--line-1)]"
      @star="onStar"
    ></Card>
  </div>

  <!-- 加一个样式，如何没有子元素，则隐藏 -->
  <div class="flex-shrink-0 mb-[12px] px-[12px] pt-[5px] bg-[var(--op-fill-white)] empty:hidden">
    <Button v-if="can_share_to_post" type="primary" @click="union_store.shareToPost">{{
      t("share_to_post")
    }}</Button>
    <Button v-else-if="can_share_to_square" type="primary" @click="publishToSquare">{{
      t("share_to_square")
    }}</Button>
    <Button v-else-if="can_join_union && union_store.my_union" type="primary" :disabled="true">{{
      t("already_in_a_union")
    }}</Button>
    <Button
      v-else-if="can_join_union"
      type="primary"
      :disabled="union_store.isJoinUnionPending(item.guild_id)"
      @click="joinUnion"
      >{{ t("join_union") }}</Button
    >
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from "vue";
import TeamInfo from "@/components/announcement-square/team-info/index.vue";
import { CommonImage } from "@/components/common/image";
import TeamMember from "@/components/announcement-square/team-member/index.vue";
import Card from "@/components/search/card/index.vue";
import Button from "@/components/ui/button/index.vue";

import imgs from "@/assets/imgs/announcement-square/icon-support-stick2.png";
import imgscur from "@/assets/imgs/announcement-square/icon-support-stick-cur2.png";
import { SupportUserInfo, UnionDetail, UnionJoinType, useQueryGuildHotPost } from "@/api/union";
import { t } from "@/locales";
import { PostItem } from "packages/types/post";
import { useUser } from "@/store/user";
import { useUnionStore } from "@/store/union";
import { useLang } from "@/composables/use-lang";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { postStar } from "@/api/post";
import { LikeType, StanceType } from "packages/types/stance";

const emits = defineEmits<{
  (e: "viewUser", item: SupportUserInfo): void;
  (e: "toggleSupport"): void;
}>();

const props = defineProps<{
  item: UnionDetail;
  from: "square" | "post" | "shiftyspad";
  from_post_item: { post_uuid: string } | undefined;
}>();
const user_store = useUser();

const union_store = useUnionStore();

const { lang_list } = useLang();
const getLangName = (lang: string) => {
  if (lang === "zh-CN") return "简体中文";
  return lang_list.value.find((item) => item.value === lang)?.name || lang;
};

/** 是否为我的公会 */
const is_my_union = computed(() => union_store.isMyUnion(props.item.guild_id));

// 是否为应援用户
const is_supporter = computed(() => {
  if (props.item.is_supporter != null) {
    return props.item.is_supporter;
  }
  return props.item.support_user_infos.some(
    (item) => item.intl_open_id === user_store.user_info.intl_openid,
  );
});

const { data: hotPost, refetch: refetchHotPost } = useQueryGuildHotPost({
  guild_id: props.item.guild_id,
  nikke_area_id: props.item.nikke_area_id,
});

/** 是否可以加入公会 */
const can_join_union = computed(() => union_store.canJoinUnion(props.item.guild_id));

const join_condition = computed(() => {
  if (props.item.guild_join_type === UnionJoinType.DIRECT_JOIN) {
    return t("union_join_type_0");
  } else if (props.item.guild_join_type === UnionJoinType.APPLY_JOIN) {
    return t("union_join_type_1");
  } else {
    return t("union_join_type_2");
  }
});

/** 是否可以分享到广场 */
const can_share_to_square = computed(() => union_store.canShareToSquare(props.item.guild_id));

/** 是否可以分享到帖子 */
const can_share_to_post = computed(() => union_store.canShareToPost(props.item.guild_id));

const show_post_card = computed(() => {
  if (!hotPost.value?.post_info) return false;
  if (
    props.from === "post" &&
    props.from_post_item?.post_uuid === hotPost.value.post_info.post_uuid
  )
    return false;
  return true;
});

onMounted(() => {
  union_store.getMyGuildInfo({ latest: false });
});

const viewUser = (item: SupportUserInfo) => {
  emits("viewUser", item);
};

const toggleSupport = () => {
  emits("toggleSupport");
};

const joinUnion = () => {
  if (!union_store.canJoinUnion(props.item.guild_id)) return;
  union_store.joinUnion(props.item);
};

const publishToSquare = () => {
  if (!union_store.canShareToSquare(props.item.guild_id)) return;
  union_store.publishToSquare({
    guild_id: props.item.guild_id,
    nikke_area_id: props.item.nikke_area_id,
  });
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  refetchHotPost();

  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};
</script>
