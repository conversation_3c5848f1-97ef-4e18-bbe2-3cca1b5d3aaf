<template>
  <div
    ref="container"
    class="w-full bg-[var(--op-fill-white)] shadow-[0px_1px_5px_var(--op-shadow-black-5)] px-[12px] py-[8px]"
  >
    <div class="flex">
      <UnionIcon
        :guild_icon="item?.guild_icon"
        class="w-[50px] h-[50px] flex-shrink-0 mr-[8px]"
      ></UnionIcon>
      <div class="flex-1 mt-[10px] w-0">
        <div class="flex items-center">
          <div
            class="bg-[var(--brand-1)] font-[Inter] rounded-[1px] px-[5px] py-[2px] text-[length:10px] text-[color:var(--color-white)] leading-[1]"
          >
            Lv.{{ item?.guild_level }}
          </div>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:13px] font-[DINNextLTProBold] ml-[5px] leading-[16px] truncate"
          >
            {{ item?.guild_name }}
          </div>
        </div>
        <div
          class="mt-[5px] text-[color:var(--text-3)] text-[length:9px] leading-[11px] truncate w-full"
        >
          <!-- {{ item?.guild_description }} -->
          {{ t(`area_id_${item.nikke_area_id}`) }}
        </div>
      </div>
      <div class="ml-[10px] mt-[3px]">
        <div class="flex items-center">
          <CommonImage :src="test" class="w-[18px] h-[18px] flex-shrink-0 mr-[4px]"></CommonImage>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:12px] leading-[1] font-bold font-[DINNextLTProBold]"
          >
            {{ item.guild_activity }}
          </div>
        </div>
        <div class="flex items-center mt-[8px] min-w-[62px]">
          <UnionRankIcon
            :rank="item.guild_rank"
            class="w-[18px] h-[18px] flex-shrink-0 mr-[4px]"
          ></UnionRankIcon>
          <div
            class="text-[color:var(--text-1)] mt-[2px] text-[length:11px] leading-[14px] font-bold font-[Inter]"
          >
            {{ t(`union_rank_${item.guild_rank}`) }}
          </div>
        </div>
      </div>
    </div>
    <template v-if="support_user_infos.length">
      <div class="flex items-end mt-[6px]">
        <i class="flex-1 h-[1px] bg-[var(--line-1)] mx-[3px]"></i>
        <SvgIcon name="icon-card-line2" color="var(--line-1)" class="w-[25px] h-[6px]"></SvgIcon>
      </div>

      <div
        class="mt-[6px] grid relative overflow-hidden"
        :style="{ gridTemplateColumns: `repeat(${max_item_each_row}, minmax(0, 1fr))` }"
      >
        <div class="w-[24px] h-[24px] flex-shrink-0 cursor-pointer mr-[6px]">
          <CommonImage :src="iconcur"></CommonImage>
        </div>
        <div
          v-for="(its, index) in support_user_infos.slice(
            0,
            support_user_infos.length > max_item_each_row - 1
              ? max_item_each_row - 2
              : max_item_each_row - 1,
          )"
          :key="index"
          class="w-[24px] h-[24px] rounded-full overflow-hidden mr-[6px] flex-shrink-0 cursor-pointer"
        >
          <CommonImage :src="its?.icon"></CommonImage>
        </div>

        <div v-if="support_user_infos.length > max_item_each_row - 1">
          <div class="w-[24px] h-[24px] rounded-full bg-[var(--fill-3)] rotate-90">
            <SvgIcon name="icon-ellipsis" color="var(--text-3)"></SvgIcon>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { CommonImage } from "@/components/common/image";
import SvgIcon from "@/components/common/svg-icon.vue";
// import icon from "@/assets/imgs/announcement-square/icon-support-stick.png";
import iconcur from "@/assets/imgs/announcement-square/icon-support-stick-cur.png";
import { UnionCard, UnionDetail, useQueryGuildCardSupportersByTourist } from "@/api/union";
import { useElementSize } from "@vueuse/core";
import test from "@/assets/imgs/test/team-combat-icon.png";
import { useUser } from "@/store/user";
import { t } from "@/locales";
import UnionRankIcon from "../union-rank-icon";
import UnionIcon from "../union-icon";

const container = ref<HTMLDivElement>();
const { width } = useElementSize(container);
const max_item_each_row = computed(() => {
  return Math.floor((width.value - 48) / (24 + 6));
});

const user_store = useUser();

const props = defineProps<{
  item: UnionCard;
}>();

const { data: support_user_infos_data, refetch: refetchSupporters } =
  useQueryGuildCardSupportersByTourist({
    guild_card_uuid: props.item.guild_card_uuid,
    nikke_area_id: props.item.nikke_area_id,
  });

// 应援用户信息列表
const support_user_infos = computed(() => {
  return (
    support_user_infos_data.value?.support_user_infos ??
    (props.item as UnionDetail)?.support_user_infos ??
    []
  );
});

// 是否为应援用户
const is_supporter = computed(() => {
  return support_user_infos.value.some(
    (item) => item.intl_open_id === user_store.user_info.intl_openid,
  );
});

defineExpose({
  refetchSupporters,
});
</script>

<style lang="scss" scoped></style>
