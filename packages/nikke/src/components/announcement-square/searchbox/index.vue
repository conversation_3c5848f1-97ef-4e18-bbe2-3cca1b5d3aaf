<template>
  <div class="flex items-center">
    <div
      class="flex flex-1 items-center bg-[var(--op-fill-white)] w-[167px] h-[28px] px-[4px] border-[1px] border-[color:var(--op-line-black-10)]"
    >
      <input
        type="text"
        class="h-[20px] appearance-none bg-[transparent] placeholder:text-[color:var(--text-3)] bg-none m-0 p-0 text-[length:10px] leading-[20px] font-normal text-[color:var(--text-1)]"
        :value="search"
        :placeholder="t('union_search_placeholder')"
        @input="(e) => $emit('update:search', (e.target as HTMLInputElement).value)"
      />
      <div
        class="ml-[6px] w-[20px] h-[20px] flex items-center justify-center cursor-pointer"
        @click="$emit('refresh')"
      >
        <SvgIcon name="icon-search" color="var(--text-1)" class="w-[12px] h-[12px]"></SvgIcon>
      </div>
    </div>

    <div
      class="w-[28px] h-[28px] flex-shrink-0 bg-[var(--op-fill-white)] ml-[4px] border-[1px] border-[color:var(--op-line-black-10)] flex items-center justify-center cursor-pointer"
      @click="$emit('refresh')"
    >
      <SvgIcon
        name="icon-loading2"
        color="var(--text-3)"
        class="w-[16px] h-[16px]"
        :class="loading ? 'animate-spin' : ''"
      ></SvgIcon>
    </div>
    <DropdownNormal
      :key="refresh_key + '_rank'"
      :list="rank_list"
      :active="rank"
      @change="(v) => $emit('update:rank', v.value as string)"
    >
      <template #trigger="{ item }">
        <SelectHead
          :text="item?.name || t('union_rank')"
          :type="1"
          class="h-[28px] mr-[4px] flex-1 ml-[4px] max-w-[100px] min-w-[50px]"
        />
      </template>
    </DropdownNormal>
    <DropdownNormal
      :key="refresh_key + '_area'"
      :list="region_list"
      :active="area_id"
      @change="(v) => $emit('update:area_id', v.value as string)"
    >
      <template #trigger="{ item }">
        <SelectHead
          :text="item?.name || t('server')"
          :type="1"
          class="h-[28px] flex-1 max-w-[100px]"
        />
      </template>
    </DropdownNormal>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import { t } from "@/locales";
import { UnionRank } from "@/api/union";
import { useGameRegion } from "@/composables/role/use-server-info";

const props = defineProps<{
  search: string;
  rank: string | undefined;
  area_id: string;
  loading: boolean;
}>();

defineEmits<{
  (e: "update:search", v: string): void;
  (e: "update:rank", v: string): void;
  (e: "refresh"): void;
  (e: "update:area_id", v: string): void;
}>();

const refresh_key = ref(0);

const { server_list } = useGameRegion({ all: true });
const region_list = computed(() => {
  return [
    { value: "", name: t("all") },
    ...server_list.value.map((item) => ({
      value: item.area_id + "",
      name: t(`area_id_${item.area_id}`).includes("area_id_")
        ? item.area_name
        : t(`area_id_${item.area_id}`),
    })),
  ];
});

const all_ranks = [
  UnionRank.CHALLENGER,
  UnionRank.DIA,
  UnionRank.PLATINUM,
  UnionRank.GOLD,
  UnionRank.SILVER,
  UnionRank.BRONZE,
  UnionRank.BEGINNER,
];

const rank_list = computed(() => {
  return [
    { value: "", name: t("all") },
    ...all_ranks.map((rank) => ({
      value: rank.toString(),
      name: t(`union_rank_${rank}`),
    })),
  ];
});

defineExpose<{
  reset: () => void;
}>({
  reset: () => {
    refresh_key.value++;
  },
});
</script>
