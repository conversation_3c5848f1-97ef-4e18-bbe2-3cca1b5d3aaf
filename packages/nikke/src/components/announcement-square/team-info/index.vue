<template>
  <div
    class="relative min-h-[89px] w-full bg-[url('@/assets/imgs/common/bg-team.png')] bg-[length:100%_100%] pt-[8px] pl-[16px] flex"
  >
    <i class="w-full h-[calc(100%_-_40px)] bg-[var(--fill-3)] -z-[1] absolute bottom-0 left-0"></i>
    <div class="w-[66px] mr-[6px] flex-shrink-0">
      <UnionIcon :guild_icon="item?.guild_icon"></UnionIcon>
    </div>
    <div class="flex-1">
      <div class="flex items-center pr-[18px]">
        <div class="flex-1 h-[35px]">
          <div
            class="font-bold text-[color:var(--op-text-white)] text-[length:14px] truncate leading-[16px]"
          >
            {{ item?.guild_name }}
          </div>
          <div class="text-[color:var(--op-text-white)] text-[length:10px] truncate leading-[12px]">
            UID:{{ item?.guild_id }}
          </div>
        </div>
        <div class="ml-[12px]">
          <slot></slot>
        </div>
      </div>
      <div class="flex mt-[5px]">
        <div class="flex-[4] pt-[2px]">
          <div class="font-bold text-[color:var(--text-3)] leading-[1] text-[length:9px]">
            {{ t("nikke_level") }}
          </div>
          <div class="flex mt-[4px]">
            <div
              class="text-[color:var(--text-1)] text-[length:20px] font-bold font-[DINNextLTProBold] leading-[24px] mr-[7px]"
            >
              {{ (item?.guild_level ?? 0).toString().padStart(2, "0") }}
            </div>
            <!-- <div class="flex-1 mt-[2px]">
              <div class="text-[color:var(--text-3)] leading-[10px] text-[length:8px]">
                74210/900000
              </div>
              <div class="w-full h-[3px] bg-[var(--fill-1-20)] relative">
                <div
                  class="absolute top-0 left-0 h-full bg-[var(--color-black)]"
                  style="width: 30%"
                ></div>
              </div>
            </div> -->
          </div>
        </div>
        <div class="w-[1px] h-[30px] bg-[var(--line-1)] mx-[4px]"></div>
        <div class="flex-[5] pt-[4px] flex">
          <CommonImage :src="test" class="w-[24px] h-[24px] flex-shrink-0 mr-[4px]"></CommonImage>
          <div class="flex-1 pt-[1px]">
            <div class="text-[color:var(--text-3)] text-[length:8px] leading-[10px]">
              {{ t("union_activity") }}
            </div>
            <div
              class="text-[color:var(--text-1)] text-[length:13px] font-bold font-[DINNextLTProBold] leading-[16px]"
            >
              {{ item.guild_activity }}
            </div>
          </div>
        </div>
        <div class="w-[1px] h-[30px] bg-[var(--line-1)] mx-[4px]"></div>
        <div class="flex-[6] pt-[4px] flex">
          <UnionRankIcon
            :rank="item.guild_rank"
            class="w-[24px] h-[24px] flex-shrink-0 mr-[4px]"
          ></UnionRankIcon>
          <div class="flex-1 pt-[2px] overflow-hidden">
            <div class="text-[color:var(--text-3)] text-[length:8px] leading-[10px]">
              {{ t("union_rank") }}
            </div>
            <div
              class="text-[color:var(--text-1)] text-[length:13px] font-bold font-[DINNextLTProBold] leading-[16px] truncate"
            >
              {{ t(`union_rank_${item.guild_rank}`) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CommonImage } from "@/components/common/image";
import { t } from "@/locales";
import test from "@/assets/imgs/test/team-combat-icon.png";
import { UnionCard } from "@/api/union";
import UnionRankIcon from "../union-rank-icon";
import UnionIcon from "../union-icon";

defineProps<{
  item: UnionCard;
}>();
</script>
