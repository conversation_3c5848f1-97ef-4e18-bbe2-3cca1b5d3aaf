<script setup lang="ts">
import { UnionRank } from "@/api/union";
import { CommonImage } from "@/components/common/image";
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { computed } from "vue";

const props = defineProps<{
  rank: UnionRank;
}>();

const src = computed(() => {
  const configs = {
    [UnionRank.CHALLENGER]: "105",
    [UnionRank.DIA]: "104",
    [UnionRank.PLATINUM]: "103",
    [UnionRank.GOLD]: "102",
    [UnionRank.SILVER]: "101",
    [UnionRank.BRONZE]: "100",
    [UnionRank.BEGINNER]: "001",
  };
  const result = ICONS_URL({ path: "tier", name: `ti_${configs[props.rank] || "001"}` });
  return result;
});
</script>

<template>
  <CommonImage :src="src" />
</template>
