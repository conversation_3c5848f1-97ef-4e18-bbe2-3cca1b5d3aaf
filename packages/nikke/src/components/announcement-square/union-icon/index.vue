<script setup lang="ts">
import { CommonImage } from "@/components/common/image";
import { computed } from "vue";
import { GET_TABLE_URL, ICONS_URL } from "@/shiftyspad/const/urls";
import { useQuery } from "@tanstack/vue-query";

const props = defineProps<{
  guild_icon: string | number;
}>();
const guild_icon_config_json = GET_TABLE_URL("guild_icon");

const { data: guild_icon_config } = useQuery({
  queryKey: ["guild_icon_config"],
  queryFn: async () => {
    const res = await fetch(guild_icon_config_json);
    return res.json() as Promise<{ id: number; grade: number; resource_id: number }[]>;
  },
});

const src = computed(() => {
  const config = guild_icon_config.value?.find((item) => item.id === props.guild_icon);
  return ICONS_URL({ path: "emblem", name: `ig_${config?.resource_id || "001"}` });
});
</script>

<template>
  <CommonImage :src="src" />
</template>
