<template>
  <!-- @todo：此处有一个根据第一个内容的高度确认第二个内容高度的交互，拉动浮层上面的横线使得浮层高度拉到最大进行滚动，具体交互还请开发跟产品对接处理 -->
  <CommBottomPopup :visible="visible" line @close="cancel">
    <div
      v-show="!show_user_openid"
      class="min-h-[278px] max-h-[75vh] overflow-hidden pt-[24px] flex flex-col"
    >
      <TeamBrief
        :item="union_info"
        :from="from"
        :from_post_item="from_post_item"
        @view-user="seeUser"
        @toggle-support="toggleSupport"
      ></TeamBrief>
    </div>

    <div v-if="show_user_openid" class="min-h-[278px] max-h-[75vh] overflow-y-auto pt-[40px]">
      <div
        class="flex w-full items-center fixed top-0 left-0 h-[40px] z-[10] bg-[var(--op-fill-white)] px-[12px] pt-[6px]"
        @click="show_user_openid = ''"
      >
        <i
          class="absolute left-[12px] bottom-0 w-[calc(100%_-_24px)] h-[1px] border-b-[1px] border-[color:var(--line-1)]"
        ></i>
        <div class="w-[8px] h-[18px] rotate-180 mr-[8px]">
          <SvgIcon name="icon-arrow-right2" color="var(--text-1)"></SvgIcon>
        </div>
        <div class="text-[color:var(--text-1)] text-[length:16px] font-medium mt-[2px]">Back</div>
      </div>
      <PlayerPopContent
        :uid="show_user_openid"
        :hide_wrapper="true"
        :is_client="user_store.user_info.intl_openid !== show_user_openid"
        @friend-card-request="onAddFriend"
      ></PlayerPopContent>
      <!-- 此处的内容为 components\common\player-pop 的内容，去除最外层的浮层，开发迁移一下 -->
    </div>
  </CommBottomPopup>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import TeamBrief from "@/components/announcement-square/team-brief/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { BaseDialog } from "@/utils/dialog";
import {
  SupportUserInfo,
  UnionCard,
  UnionDetail,
  useGetGuildDetail,
  useQueryGuildCardSupportersByTourist,
  useSupportGuild,
} from "@/api/union";
import PlayerPopContent from "@/components/common/player-pop/index.vue";
import { useRoute } from "vue-router";
import { useToast } from "@/components/ui/toast";
import { t } from "@/locales";
import { useUser } from "@/store/user";
import { useSendAddFriendRequestWithPrivacyPermission } from "@/api/post";
import { useDialog } from "@/components/ui/dialog";
import { useGetUserGamePlayerInfo } from "@/api/user-games";

interface Props extends BaseDialog {
  item: UnionCard;
  from: "square" | "post" | "shiftyspad";
  from_post_item: { post_uuid: string } | undefined;
  onBindRole: () => void;
  onSupportToggle?: () => void;
}

const props = defineProps<Props>();
const route = useRoute();

const user_store = useUser();

const show_user_openid = ref("");

const { data: detail, refetch } = useGetGuildDetail({
  guild_id: props.item.guild_id,
  nikke_area_id: props.item.nikke_area_id + "",
});

const { data: support_user_infos_data, refetch: refetchSupporters } =
  useQueryGuildCardSupportersByTourist(
    {
      guild_card_uuid: props.item.guild_card_uuid,
      nikke_area_id: props.item.nikke_area_id,
    },
    // 存在公会卡片uuid，再请求这个用户列表接口
    { enabled: !!props.item.guild_card_uuid },
  );

/** 最完整的公会信息 */
const union_info = computed(() => {
  const support_user_infos =
    support_user_infos_data.value?.support_user_infos ??
    detail.value?.guild_detail.support_user_infos ??
    [];
  return {
    ...props.item,
    ...detail.value?.guild_detail,
    support_user_infos,
  } as UnionDetail;
});

watch(
  () => props.visible,
  () => (show_user_openid.value = ""),
);

watch(
  () => [route.path, route.query.post_uuid],
  () => props.cancel(),
);

const { show: toast } = useToast();
const seeUser = (item: SupportUserInfo) => {
  // TODO: 判断用户是否设置隐私限制
  if (!item.show_friend_card_detail) {
    toast({
      text: t("player_set_game_card_to_be_private"),
      type: "warning",
    });
    return;
  }
  show_user_openid.value = item.intl_open_id;
};

const toggle = useSupportGuild({
  onSuccess: () => {
    refetch();
    refetchSupporters();
    props.onSupportToggle?.();
  },
});

const toggleSupport = async () => {
  await toggle.mutateAsync({
    guild_id: props.item.guild_id,
    nikke_area_id: props.item.nikke_area_id,
    support_type: detail.value?.guild_detail.is_supporter ? 2 : 1,
  });
  if (detail.value?.guild_detail) {
    detail.value.guild_detail.is_supporter = !detail.value.guild_detail.is_supporter;
  }
};

const { show: showDialog } = useDialog();

const addFriend = useSendAddFriendRequestWithPrivacyPermission();

// 玩家基本信息
const { data: player_info } = useGetUserGamePlayerInfo(
  computed(() => ({ intl_openid: user_store.user_info.intl_openid })),
  { enabled: computed(() => !!user_store.user_info.intl_openid) },
);

const onAddFriend = async () => {
  if (!player_info.value?.has_saved_role_info) {
    showDialog({
      title: t("notice"),
      content: t("please_bind_role_first"),
      confirm_text: t("bind"),
      cancel_text: t("cancel"),
      callback: ({ value, close }) => {
        if (value === "confirm") {
          props.onBindRole();
          props.cancel();
        }
        close();
      },
    });
    return;
  }
  await addFriend.mutateAsync({
    friend_uid: show_user_openid.value,
  });
  toast({ text: t("friend_card_request_successfully"), type: "success" });
};
</script>
