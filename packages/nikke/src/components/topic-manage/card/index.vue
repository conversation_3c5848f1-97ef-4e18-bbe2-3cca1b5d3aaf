<template>
  <div
    :class="[
      `h-[40px] flex items-center justify-center text-[length:11px] leading-[14px] cursor-pointer relative px-[20px]`,
      cls,
    ]"
  >
    <div v-show="item?.type == 3" class="w-[14px] h-[14px] mr-[2px] flex-shrink-0">
      <SvgIcon name="icon-add" color="var(--text-1)"></SvgIcon>
    </div>
    <span> {{ item?.text }}</span>
    <div
      v-show="item?.type == 2"
      class="absolute top-[-4px] right-[-4px] w-[14px] h-[14px] cursor-pointer rounded-[50%] bg-[color:var(--color-white)]"
    >
      <i class="absolute-center !w-[30px] !h-[30px]"></i>
      <SvgIcon name="icon-close" color="var(--color-1-80)" class="w-full h-full"></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import SvgIcon from "@/components/common/svg-icon.vue";

const props = defineProps<{
  item?: any;
}>();

// type 0-正常-默认 1-系统 2-编辑态 3-添加话题
const cls = computed(() => {
  if (props.item?.type === 1) {
    return "bg-[var(--fill-3)] text-[color:var(--text-2)]";
  } else {
    return "text-[color:var(--text-1)] border-[1px] border-[color:var(--line-1)]";
  }
});
</script>
