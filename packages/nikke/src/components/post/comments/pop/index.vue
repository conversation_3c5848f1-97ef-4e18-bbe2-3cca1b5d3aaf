<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
// import ImgDownload from "@/components/common/img-download/index.vue";
import EmojiPanel from "@/components/common/emoji-panel/index.vue";
import EditorBtns from "@/components/common/editor-btns/index.vue";
import Editor from "@/components/common/editor/index.vue";
import Bubble from "@/components/common/bubble/index.vue";
import { usePicturePop } from "@/components/common/picture-popup/index.ts";
import { useToast } from "@/components/ui/toast";

// configs
import { COMPOSE_COMMENT_LEN } from "@/configs/const.ts";

// utils
import { computed, ref } from "vue";
import { t } from "@/locales";
import { useComments } from "@/store/post/comments";
import { storeToRefs } from "pinia";
import { CommentType } from "packages/types/comments";
import { urlSearchObjectify } from "packages/utils/qs";
import { report } from "packages/utils/tlog";
import { useGetUserCommentBubbleList } from "@/api/user-comment-bubble";
import Quill from "quill";

type Status = "default" | "active" | "disabled";

const props = defineProps<{
  post_uuid?: string;
  comment_uuid?: string;
  parent_uuid?: string;
  type: CommentType;
  show: boolean;
}>();
const emits = defineEmits(["close"]);

const { show: showPicturePop, dialog: picture_pop_dialog } = usePicturePop();
const { show: toast } = useToast();
const comments_store = useComments();
comments_store.onResetComposeComposeParams();

const { state, editor } = storeToRefs(comments_store);

const icon_active = ref<Status>("active");
const emoji_select_panel_visible = ref(true);
const url_object = urlSearchObjectify();

const picture_active: any = computed(() => {
  if (!state.value.compose_params.pic_urls.length) {
    return "active";
  }
  return "defalut";
});

const can_compose = computed(() => {
  return state.value.compose_params.content.length > 0;
});

const onComposeComment = async () => {
  if (can_compose.value) {
    await comments_store.onComposeComment(
      Object.assign(state.value.compose_params, {
        type: props.type || CommentType.comment,
        post_uuid: props.post_uuid || url_object.post_uuid,
        comment_uuid: props.comment_uuid || url_object.comment_uuid,
        parent_uuid: props.parent_uuid || url_object.parent_uuid,
        comment_bubble_id: props.type === CommentType.comment ? my_bubble?.value?.id : undefined,
      }),
    );

    report.standalonesite_news_comment_submit_btn.cm_click({
      location: 0,
      content_id: url_object.post_uuid,
      comment_id: props.comment_uuid || url_object.comment_uuid,
      label_id: url_object.plate_id,
      label_name: "",
      content_type: my_bubble?.value?.id ? true : false,
    } as any);

    emits("close");
    return;
  }
};

const onInsertImage = (url: string) => {
  comments_store.onInsertImage(url);
  picture_pop_dialog.value.close();
};

const onEmojiChange = (emoji: string) => {
  editor.value.insertImage(emoji, "inline");
};

const getTitle = () => {
  return {
    [CommentType.comment]: t("comment"),
    [CommentType.reply]: t("reply"),
    [CommentType.info_comment]: "",
    [CommentType.info_reply]: "",
  }[props.type];
};

const onDeleteEditorText = () => {
  const quill = editor.value?.quill;
  /**
   * @link https://quilljs.com/docs/api#deletetext
   */
  if (quill) {
    const range = quill.getSelection(true);
    quill.deleteText(range.index - 1, 1);
  }
};

const onPictureClick = () => {
  if (picture_active.value === "active") {
    showPicturePop({
      onChange: onInsertImage,
    });
  } else {
    toast({
      text: t("compose_comment_img_limit_tips"),
      type: "warning",
    });
  }
};

// 我佩戴的气泡
const { data } = useGetUserCommentBubbleList({ limit: 24 });
const my_bubble = computed(() => {
  return data.value?.user_comment_bubbles.find((item) => item.is_weared === 1);
});

const onEditroRender = (quil: Quill) => {
  setTimeout(() => {
    quil.focus();
  }, 500);
};
</script>

<template>
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div
        class="flex items-center justify-between p-[10px] border-b-[1px] border-b-[color:var(--line-1)]"
      >
        <div class="relative w-[24px] h-[24px] cursor-pointer" @click="emits('close')">
          <i class="absolute-center"></i>
          <svgIcon class="" name="icon-pop-close" color="var(--text-1)"></svgIcon>
        </div>
        <div
          class="text-[length:16px] leading-[19px] font-bold px-[12px] text-[color:var(--text-1)]"
        >
          {{ getTitle() }}
        </div>
        <div class="cursor-pointer">
          <span
            :class="[
              `text-[length:14px] leading-[16px] font-bold`,
              can_compose ? `text-[var(--brand-1)]` : `text-[var(--text-4)]`,
            ]"
            @click="onComposeComment"
          >
            {{ t("send") }}
          </span>
        </div>
      </div>
    </template>

    <div class="pt-[12px] pb-[3px] px-[12px]">
      <!-- 我佩戴的气泡 -->
      <Bubble
        :bg="type === CommentType.comment ? my_bubble?.bg_color : undefined"
        :img="type === CommentType.comment ? my_bubble?.comment_bubble : undefined"
        :is-edit="true"
        class="mt-[20px]"
      >
        <Editor
          ref="editor"
          v-model:value="state.compose_params.content"
          class="h-[98px]"
          :placeholder="t('please_enter')"
          :maxlen="COMPOSE_COMMENT_LEN"
          :focus="true"
          :disabled_pasted_image="true"
          @render="onEditroRender"
        ></Editor>
      </Bubble>
      <div
        v-if="state.compose_params.pic_urls.length"
        class="w-full overflow-x-auto overflow-y-hidden flex items-center py-[3px] border-t border-t-[color:var(--line-1)]"
      >
        <div
          v-for="(item, index) in state.compose_params.pic_urls"
          :key="index"
          class="relative mr-[6px] last-of-type:mr-0"
        >
          <div class="w-[50px] h-[50px] rounded-[4px] overflow-hidden">
            <img :src="item" alt="image" class="h-full w-full object-cover" />
          </div>
          <div
            class="w-[17px] h-[17px] z-[2] flex items-center justify-center absolute right-[-3px] top-[-2px] bg-[var(--fill-1-80)] dark:bg-[var(--fill-3)] rounded-full cursor-pointer"
            @click="comments_store.onRemoveImage(index)"
          >
            <i class="absolute-center"></i>
            <SvgIcon
              name="icon-delete"
              color="var(--color-white)"
              class="w-[10px] h-[10px]"
            ></SvgIcon>
          </div>
        </div>
      </div>
    </div>

    <EditorBtns
      :picture-status="picture_active"
      :friend-card="false"
      :authoring-statement="false"
      :union-card="false"
      @emoji-click="
        emoji_select_panel_visible = !emoji_select_panel_visible;
        icon_active = emoji_select_panel_visible ? 'active' : 'default';
      "
      @picture-click="onPictureClick"
    ></EditorBtns>

    <EmojiPanel
      v-if="emoji_select_panel_visible"
      @change="onEmojiChange"
      @delete="onDeleteEditorText"
    ></EmojiPanel>
  </CommBottomPopup>
</template>
