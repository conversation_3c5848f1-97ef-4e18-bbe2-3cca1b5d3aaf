// cpnts
import CommentsPop from "./index.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";

export const useCommentsPop = () => {
  let dialog: any;
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return (dialog = showDialog(
      CommentsPop,
      Object.assign(options, {
        show: true,
        onClose: () => {
          dialog.unmount();
        },
      }),
    ));
  };
  return {
    show,
  };
};
