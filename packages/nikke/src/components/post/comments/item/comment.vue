<template>
  <div :class="[`flex flex-col`, bg_flash && 'animate-flash']">
    <div class="flex justify-between items-center mb-[12px">
      <div
        class="flex justify-between items-center"
        @click="
          () =>
            router.push({
              path: Routes.USER,
              query: {
                openid: item.user?.intl_openid,
              },
            })
        "
      >
        <Avatar
          :src="item.user?.avatar"
          :auth_type="item.user?.auth_type"
          :frame="item.user?.avatar_pendant"
          class="!w-[32px] !h-[32px] mr-[5px] rounded-full"
        />
        <div>
          <div class="flex items-center mb-[2px]">
            <div
              class="text-[length:13px] leading-[16px] text-[color:var(--text-3)] whitespace-nowrap max-w-[160px] overflow-hidden text-ellipsis"
            >
              {{ item.user?.username }}
            </div>
            <div
              v-if="item.is_author"
              class="mx-[8px] h-[14px] px-[6px] rounded-full text-[9px] leading-[14px] text-[color:var(--text-3)] bg-[color:var(--fill-2)]"
            >
              {{ t("author") }}
            </div>
            <div
              class="text-[11px] leading-[16px] text-[color:var(--text-3)] mx-[8px] whitespace-nowrap"
            >
              {{ formatTime(+item.created_on * 1000, t) }}
            </div>
          </div>
          <TypeLabel
            :game_tag="item.user?.game_tag"
            :game_tag_num="item.user?.game_tag_num"
            :mood="item.user?.mood"
            :is_self="false"
          ></TypeLabel>
        </div>
      </div>

      <div class="flex items-center justify-center">
        <span class="cursor-pointer" @click="emits('translate')">
          <span class="h-[24px] w-[24px] inline-flex align-bottom">
            <!-- 点击评论翻译按钮，翻译中，按钮状态 -->
            <SvgIcon
              v-if="item.translate_status === PostTranslateStatus.translating"
              name="icon-loading"
              class="h-full w-full p-[4px] common-rotate"
              color="var(--text-3)"
            ></SvgIcon>
            <!-- 翻译完成状态，点击切换回原始状态（original） -->
            <SvgIcon
              v-else-if="item.translate_status === PostTranslateStatus.translated"
              name="icon-back"
              class="h-full w-full"
              color="var(--text-3)"
            ></SvgIcon>
            <SvgIcon
              v-else
              name="icon-translate"
              class="h-full w-full"
              color="var(--text-3)"
            ></SvgIcon>
          </span>
        </span>
        <span
          v-click-interceptor.need_login.mute.sign_privacy="() => emits('more')"
          class="w-[24px] h-[24px] relative cursor-pointer"
        >
          <SvgIcon name="icon-ellipsis" color="var(--text-3)"></SvgIcon>
        </span>
      </div>
    </div>

    <div class="ml-[38px] pb-[8px]" :class="[line ? 'border-b-[1px]' : '']">
      <div class="text-[length:14px] text-[color:var(--text-1)] leading-[16px] flex-1">
        <!-- 评论气泡 -->
        <Bubble :img="bubble?.comment_bubble" :bg="bubble?.bg_color" class="mb-[8px] mt-[12px]">
          <div v-click-interceptor.need_login.mute.sign_privacy="() => emits('reply')">
            <Editor
              ref="editor"
              :value="item.content"
              :disabled="true"
              :collapse_line="10"
            ></Editor>
          </div>
        </Bubble>

        <div class="grid items-center grid-cols-3 gap-[4px]" @click="onImageClick">
          <img
            v-for="(src, index) in item.pic_urls?.filter(Boolean)"
            :key="index"
            class="w-[80px] h-[80px] object-cover rounded-[8px]"
            :src="src"
          />
        </div>
      </div>

      <div class="flex items-center justify-between mt-[5px]">
        <!-- <div class="text-[11px] leading-[16px] text-[color:var(--text-3)] whitespace-nowrap">
          {{ formatTime(+item.created_on * 1000, t) }}
        </div> -->
        <div class="flex items-end">
          <SvgIcon
            v-click-interceptor.need_login.mute.sign_privacy="() => emits('reply')"
            class="w-[20px] h-[20px] mx-[5px] cursor-pointer"
            name="icon-line-comments"
            color="var(--text-3)"
          ></SvgIcon>

          <ButtonIconText
            v-click-interceptor.need_login.mute.sign_privacy="() => emits('upvote')"
            class="mx-[5px] flex items-center"
            icon="icon-line-like"
            active-icon="icon-line-like-cur"
            :is_like_icon="true"
            :is-active="item.is_star"
            :text="formatNumber(item.upvote_count)"
          />
        </div>
      </div>
    </div>

    <div
      v-if="Boolean($slots.replies)"
      class="min-w-[315px] ml-[38px] mt-[4px] bg-[var(--op-fill-white)] py-[10px] pl-[16px] pr-[7px] rounded-[10px]"
    >
      <slot name="replies" :item="item"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import TypeLabel from "@/components/common/type-label/index.vue";
import ButtonIconText from "@/components/common/button-icon-text/index.vue";
import Editor from "@/components/common/editor/index.vue";
import Bubble from "@/components/common/bubble/index.vue";
import Avatar from "@/components/common/avatar/index.vue";
import { Routes } from "@/router/routes";
import router from "@/router";

// types
import { GetPostCommentsResponseItem } from "packages/types/comments";
import { PostTranslateStatus } from "packages/types/post";

// utils
import { t } from "@/locales";
import { computed, ref, watch } from "vue";
import { formatTime } from "@/utils/str.ts";
import { useCommentBubble } from "@/store/post/comment-bubble";
import { formatNumber } from "packages/utils/tools";
import { createViewer } from "@/components/common/img-viewer/index";

const props = defineProps<{
  item: GetPostCommentsResponseItem;
  bg_flash?: boolean;
  index?: number;
  line?: boolean;
}>();

const emits = defineEmits(["upvote", "translate", "more", "reply"]);

const editor = ref();

const { getCommentBubble } = useCommentBubble();

const bubble = computed(() => {
  return getCommentBubble(props.item.comment_bubble_id);
});

const onImageClick = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target) {
    createViewer({
      image_list: props.item.pic_urls?.filter(Boolean),
      target,
    });
  }
};

watch(
  () => props.item.content,
  (content) => {
    editor.value.insertHtml(content);
  },
  {
    deep: true,
  },
);
</script>

<style lang="scss" scoped>
@keyframes flash {
  0%,
  100% {
    background-color: var(--fill-3);
  }
  50% {
    background-color: var(--fill-2);
  }
}
.animate-flash {
  animation: flash 3s ease-in-out;
}
</style>
