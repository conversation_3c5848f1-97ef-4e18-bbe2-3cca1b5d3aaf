<template>
  <div
    class="bg-[#eee] mt-[4px] px-[24px] h-[43px] flex items-center relative w-full rounded-[2px] mb-[4px]"
  >
    <div class="overflow-hidden">
      <div
        ref="scroll_container_ref"
        class="flex items-center overflow-y-hidden overflow-x-auto no-scrollbar"
      >
        <div
          v-for="(item, index) in data"
          :key="index"
          :class="[
            `w-[56px] h-[32.5px] flex-shrink-0 mr-[6px] last-of-type:mr-0 relative border-[1px] cursor-pointer rounded-[4px]`,
            index === current_index
              ? `opacity-[1] border-[color:var(--brand-1)]`
              : `opacity-[0.7] border-[color:transparent]`,
          ]"
          @click.stop="onPaginationChange(index)"
        >
          <img :src="item?.src" alt="" class="w-full h-full object-contain rounded-[4px] ignore" />
        </div>
      </div>
    </div>

    <SvgIcon
      name="icon-arrow-right"
      class="w-[12px] h-[12px] absolute z-[5] top-1/2 -translate-y-1/2 right-[4px] !text-black cursor-pointer"
      @click="onPaginationChange(current_index + 1)"
    />
    <SvgIcon
      name="icon-arrow-right"
      class="w-[12px] h-[12px] absolute z-[5] top-1/2 -translate-y-1/2 left-[4px] !text-black rotate-[180deg] cursor-pointer"
      @click="onPaginationChange(current_index - 1)"
    />

    <template v-if="data.length > 5">
      <div
        class="w-[12px] h-[12px] absolute z-[5] top-1/2 -translate-y-1/2 left-[4px] rotate-[180deg]"
        @click="onPaginationChange(current_index - 1)"
      >
        <i class="absolute-center"></i>
        <SvgIcon
          name="icon-arrow-right"
          :color="current_index == 0 ? 'var(--text-3)' : 'var(--text-1)'"
        ></SvgIcon>
      </div>

      <div
        class="w-[12px] h-[12px] absolute z-[5] top-1/2 -translate-y-1/2 right-[4px]"
        @click="onPaginationChange(current_index + 1)"
      >
        <i class="absolute-center"></i>
        <SvgIcon
          name="icon-arrow-right"
          :color="current_index == data.length - 1 ? 'var(--text-3)' : 'var(--text-1)'"
        ></SvgIcon>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { watch } from "vue";

// utils
import { ref } from "vue";

const props = defineProps<{
  data: Array<{ src: string; alt?: string }>;
  current_index: number;
  onChange: (index: number, options?: { is_from_pagination_change?: boolean }) => void;
}>();

const scroll_container_ref = ref();

const onPaginationChange = (index: number) => {
  if (index < 0 || index > props.data.length - 1) {
    return;
  }
  props.onChange(index, { is_from_pagination_change: true });
  scrollToCurrentLevelPosition(index);
};

watch(
  () => props.current_index,
  (new_value) => {
    scrollToCurrentLevelPosition(new_value);
  },
);

const scrollToCurrentLevelPosition = (index: number) => {
  const children = scroll_container_ref.value.children || [];
  const current_child = children[index];
  if (current_child) {
    let left = current_child.offsetLeft || 0;
    scroll_container_ref.value?.scrollTo?.({
      left: left - 140,
      behavior: "smooth",
    });
  }
};
</script>
