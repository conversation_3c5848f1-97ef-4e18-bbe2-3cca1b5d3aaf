<template>
  <div class="mb-[8px]">
    <Pagination :data="data" :current_index="current_index" :on-change="onChange"></Pagination>
    <div ref="swiper_ref" class="swiper w-full h-[auto]">
      <div class="swiper-wrapper text-center" @click="onImageClick">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="swiper-slide w-full !flex justify-center items-center border-[1px] border-[color:var(--line-1)] rounded-[4px] overflow-hidden relative"
        >
          <img
            :src="item.src"
            :alt="item.alt"
            class="object-cover object-center rounded-[4px]"
            :style="{
              aspectRatio: img_size_map[item.src]
                ? `${img_size_map[item.src].width}/${img_size_map[item.src].height}`
                : undefined,
            }"
          />
        </div>
      </div>
      <MorePicNum
        v-if="data.length > 1"
        :num="data.length"
        class="right-[6px] bottom-[14px]"
      ></MorePicNum>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Pagination from "@/components/post/detail/swiper-warp/pagination.vue";
import MorePicNum from "@/components/common/more-pic-num/index.vue";

// utils
import { onMounted, ref, watch } from "vue";
import { useImageSize } from "@/composables/use-image";
import { Swiper as SwiperClass } from "swiper";
import "swiper/css/bundle";
import { createViewer } from "@/components/common/img-viewer/index";

const props = defineProps<{
  data: Array<{ src: string; alt?: string }>;
}>();

const swiper_ref = ref<HTMLElement>();
const swiper = ref<SwiperClass>();

const img_size_map = ref<Record<string, { width: number; height: number }>>({});

const current_index = ref(0);
const onChange = (index: number) => {
  current_index.value = index;
  swiper.value?.slideTo(index);
};

const onImageClick = () => {
  createViewer({
    image_list: props.data.map((item) => item.src),
    index: current_index.value,
  });
};

onMounted(() => {
  swiper.value = new SwiperClass(swiper_ref.value!, {
    autoHeight: true,
    spaceBetween: 20,
    on: {
      slideChange: (swiper) => {
        current_index.value = swiper.activeIndex;
      },
    },
  });
});

const { getImageDisplaySizeByLimit } = useImageSize();

watch(
  () => props.data,
  () => {
    props.data.forEach(async (item) => {
      if (img_size_map.value[item.src]) return;
      const size = await getImageDisplaySizeByLimit(item.src);
      img_size_map.value[item.src] = size;
    });
  },
  { deep: true, immediate: true },
);
</script>
