<script setup lang="ts">
// cpnts
import Editor from "@/components/common/editor/index.vue";

// utils
import { usePostDetailStore } from "@/store/post/detail";
import { storeToRefs } from "pinia";
import { ref } from "vue";
import { createViewer } from "@/components/common/img-viewer/index";

const post_detail_store = usePostDetailStore();
const { editor, detail } = storeToRefs(post_detail_store);
const dom = ref();

const onImageClick = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target) {
    createViewer({
      dom: dom.value,
      target,
    });
  }
};
</script>

<template>
  <div ref="dom" @click="onImageClick">
    <Editor ref="editor" v-model:value="detail.content" :disabled="true" :thumbnail="true"></Editor>
  </div>
</template>

<style lang="scss" scoped></style>
