<script setup lang="ts">
// cpnts
import { YoutubePlayer, TiktokPlayer } from "@/components/common/video/player/index";
import Editor from "@/components/common/editor/index.vue";

// utils
import { usePostDetailStore } from "@/store/post/detail";
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { Platform } from "packages/types/common";

const post_detail_store = usePostDetailStore();
const { editor, detail } = storeToRefs(post_detail_store);

const ext_info = computed(() => {
  const parsed_ext_info = JSON.parse(detail.value.ext_info);
  return parsed_ext_info[0] || parsed_ext_info;
});

const vid = computed(() => {
  return ext_info.value.video_id;
});
</script>

<template>
  <div class="">
    <div class="mb-[24px]">
      <TiktokPlayer v-if="Platform.tiktok === ext_info.platform" :vid="vid"></TiktokPlayer>
      <YoutubePlayer v-else :vid="vid"></YoutubePlayer>
    </div>
    <Editor ref="editor" v-model:value="detail.content" :thumbnail="true" :disabled="true"></Editor>
  </div>
</template>

<style lang="scss" scoped></style>
