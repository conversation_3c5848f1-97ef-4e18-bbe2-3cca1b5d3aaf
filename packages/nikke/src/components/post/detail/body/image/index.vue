<script setup lang="ts">
// cpnts
import Editor from "@/components/common/editor/index.vue";
import SwiperWarp from "@/components/post/detail/swiper-warp/index.vue";

// utils
import { usePostDetailStore } from "@/store/post/detail";
import { storeToRefs } from "pinia";
import { computed } from "vue";

const post_detail_store = usePostDetailStore();
const { editor, detail } = storeToRefs(post_detail_store);

const swiper_image_list = computed(() => {
  return detail.value.pic_urls.map((src: string) => ({ src }));
});
</script>

<template>
  <div class="">
    <SwiperWarp :data="swiper_image_list"></SwiperWarp>
    <Editor ref="editor" v-model:value="detail.content" :thumbnail="true" :disabled="true"></Editor>
  </div>
</template>

<style lang="scss" scoped></style>
