<script setup lang="ts">
// cpnts
import Richtext from "./richtext/index.vue";
import Video from "./video/index.vue";
import Image from "./image/index.vue";

// types
import { ComposeContentType } from "packages/types/content";

// utils
import { computed } from "vue";
import { usePostDetailStore } from "@/store/post/detail";
import { storeToRefs } from "pinia";

const post_detail_store = usePostDetailStore();
const { detail } = storeToRefs(post_detail_store);

const Cpnt = computed(() => {
  const type = detail.value.type;
  return {
    [ComposeContentType.richtext]: Richtext,
    [ComposeContentType.video]: Video,
    [ComposeContentType.image]: Image,
  }[type];
});
</script>

<template>
  <component :is="Cpnt"></component>
</template>
