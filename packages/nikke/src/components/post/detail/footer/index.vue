<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import Stance<PERSON>ike from "@/components/common/stance/like.vue";

// types
import { CommentType } from "packages/types/comments";
import { LikeType, StanceType } from "packages/types/stance";

// utils
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { t } from "@/locales";
import { useCommentsPop } from "@/components/post/comments/pop/index.ts";
import { urlSearchObjectify } from "packages/utils/qs";
import { usePostDetailStore } from "@/store/post/detail";
import { report } from "packages/utils/tlog";
import { useCommonShare } from "@/components/common/share-pop";
import { formatNumber } from "packages/utils/tools";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";
import { useGetPost } from "@/api/post";
import { PostDetail } from "packages/types/post";

const post_detail_store = usePostDetailStore();
const { show: showCommentsPop } = useCommentsPop();

const { detail } = storeToRefs(post_detail_store);
const url_object = urlSearchObjectify();

const onShare = async (channel_name: string) => {
  report.standalonesite_share_channel_btn.cm_click({
    channel_name,
    url: location.href,
  });
  await post_detail_store.onPostForward(detail.value.post_uuid);
  onNotifyPostChange();
};

/**
 * 事件通知: 帖子详情页数据变动
 */
const onNotifyPostChange = async (v?: PostDetail) => {
  event_emitter.emit(
    EVENT_NAMES.refresh_post_list_item_info,
    v
      ? v
      : await useGetPost.run({
          post_uuid: detail.value.post_uuid,
          browse_post: 2,
          original_content: 0,
        }),
  );
};

const list = computed(() => {
  return [
    {
      icon: "icon-comment",
      type: "comment",
      value: detail.value.comment_count,
      visible: true,
      async onClick() {
        report.standalonesite_news_comment_btn.cm_click({
          location: 0,
          content_id: detail.value.post_uuid,
          label_id: detail.value.plate_id,
          label_name: "",
        } as any);

        event_emitter.emit(EVENT_NAMES.post_detail_comment_list_scroll_into_view);
      },
    },
    {
      icon: detail.value.is_collection ? "icon-collection-cur" : "icon-collection",
      type: "collection",
      value: detail.value.collection_count,
      visible: true,
      active: detail.value.is_collection,
      async onClick() {
        await post_detail_store.onCollection(detail.value.post_uuid);
        onNotifyPostChange();
      },
    },
    {
      icon: detail.value.my_upvote?.is_star ? "icon-line-like-cur" : "icon-line-like",
      type: "like",
      value: detail.value.upvote_count,
      visible: true,
      active: detail.value.my_upvote?.is_star,
      async onClick() {
        await post_detail_store.onLike(detail.value.post_uuid, StanceType.like, LikeType.like);
        onNotifyPostChange(detail.value);
      },
    },
    {
      icon: "icon-share-link",
      type: "share",
      value: detail.value.forward_count,
      visible: true, // Boolean(detail.value.is_audit === AuditType.done),
      // active: detail.value.is_forward,
      onClick() {
        useCommonShare({
          onShare,
        }).share({ text: t("share_others_text"), url: location.href });
        // post_detail_store.onPostForward(detail.value.post_uuid);

        report.standalonesite_news_share_btn.cm_click({
          location: 0,
          content_id: detail.value.post_uuid,
          label_id: detail.value.plate_id,
          label_name: "",
        } as any);
      },
    },
  ].filter((item) => item.visible);
});
</script>

<template>
  <div
    class="flex items-center max-w-[var(--max-pc-w)] w-full mx-auto fixed bottom-0 z-30 px-[12px] bg-[var(--op-fill-white)] h-[70px] border-t-[1px] border-t-[var(--line-1)]"
  >
    <div
      v-click-interceptor.need_login.mute.sign_privacy.check_user_adult="
        () =>
          showCommentsPop({
            post_uuid: url_object.post_uuid,
            type: CommentType.comment,
          })
      "
      class="flex-1 w-full h-[34px] cursor-pointer inline-flex items-center rounded-[1px] bg-[color:var(--fill-3)] border border-solid border-[color:var(--line-1)] font-normal text-[13px] px-[12px] box-border justify-start text-[color:var(--text-3)]"
    >
      {{ t("share_my_opinion") }}
    </div>

    <div class="flex gap-x-[24px] ml-[13px]">
      <span
        v-for="(item, index) in list"
        :key="index"
        class="min-w-[24px] inline-flex flex-col items-center cursor-pointer"
      >
        <SvgIcon
          v-if="item.type === 'comment'"
          class="w-[20px] h-[20px]"
          :name="item.icon"
          :color="item.active ? `var(--brand-1)` : `var(--text-3)`"
          @click="item.onClick"
        ></SvgIcon>
        <StanceLike
          v-else-if="item.type === 'like'"
          :is_star="detail.my_upvote?.is_star"
          :only_icon="true"
          direction="col"
          @click="item.onClick"
        ></StanceLike>
        <SvgIcon
          v-else
          v-click-interceptor.need_login.mute.sign_privacy="item.onClick"
          :name="item.icon"
          class="w-[20px] h-[20px]"
          :color="item.active ? `var(--brand-1)` : `var(--text-3)`"
        ></SvgIcon>
        <span class="text-[12px] font-normal leading-[14px] text-[color:var(--text-1)] mt-[8px]">
          {{ formatNumber(item.value || 0) }}
        </span>
      </span>
    </div>
  </div>
</template>
