<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Button from "@/components/ui/button/index.vue";
// import Editor from "@/components/common/editor/index.vue";

// utils
import { t } from "@/locales";
import { computed, ref } from "vue";
import { DeleteListItem } from "packages/types/post";
import { ReportContentType } from "packages/types/content";
import { getPostDeleteSelectList } from "../utils";

defineProps<{
  show: boolean;
  report_content_type: ReportContentType;
}>();

const emits = defineEmits(["close", "delete-confirm"]);

const active_type = ref();

const list = computed<DeleteListItem[]>(() => getPostDeleteSelectList());

const onConfirm = () => {
  if (!active_type.value) {
    return;
  }
  emits("delete-confirm", {
    type: active_type.value,
  });
};
</script>

<template>
  <Dialog :show="show" :title="t('select_reason_for_delete')">
    <div class="px-[4px] max-h-[60vh] overflow-y-auto">
      <div
        v-for="(item, index) in list"
        :key="index"
        :class="[
          `cursor-pointer text-[length:12px] leading-[14px] flex items-center justify-btween min-h-[38px] px-[12px] py-[4px] border-[1px] rounded-[2px] mb-[6px] last-of-type:mb-0`,
          active_type === item.type
            ? 'bg-[var(--brand-2)] border-[var(--brand-1)] dark:bg-[var(--brand-1-20)] text-[color:var(--brand-1)] '
            : 'border-[var(--line-1)] dark:bg-[var(--fill-3)] dark:border-[var(--line-1)] text-[color:var(--text-1)] ',
        ]"
        @click="active_type = item.type"
      >
        <div class="flex-1 text-left">
          {{ item.text }}
        </div>

        <SvgIcon
          v-show="active_type === item.type"
          name="icon-true"
          color="var(--brand-1)"
          class="w-[12px] h-[12px] ml-[6px]"
        ></SvgIcon>
      </div>
    </div>
    <Button
      type="primary"
      :disabled="!Boolean(active_type)"
      class="w-full !mt-[12px]"
      @click="onConfirm"
    >
      {{ t("confirm") }}
    </Button>
    <Button type="secondary" class="w-full !mt-[12px]" @click="emits('close')">
      <span class="text-[color:var(--text-1)]">{{ t("cancel") }}</span>
    </Button>
  </Dialog>
</template>

<style lang="scss" scoped></style>
