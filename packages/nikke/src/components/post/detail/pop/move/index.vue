<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import Button from "@/components/ui/button/index.vue";

// utils
import { t } from "@/locales";
import { computed, onMounted, ref } from "vue";
import { MoveType } from "./compostion";
import { cloneDeep, get } from "lodash-es";

type MoveList = Array<{
  title: string;
  active: number;
  value: string;
  key: "plate_id" | "language";
  list: Array<any>;
}>;

interface Item {
  name: string;
  active: boolean;
  value: string;
}

const getDefaultMoveConfirmValue = () => ({
  plate_id: undefined,
  language: undefined,
});

const props = defineProps<{
  show: boolean;
  move_list: MoveList;
  default_move_confirm_value?: {
    plate_id?: string;
    language?: string;
  };
}>();

const emits = defineEmits(["close", "click", "move-confirm"]);

const proxy_move_list = ref<MoveList>([] as MoveList);
const move_confirm_value = ref(getDefaultMoveConfirmValue());

const onSelectSubItem = (sub_item: Item, list: Array<Item>, key: "plate_id" | "language") => {
  if (sub_item.active) {
    sub_item.active = !sub_item.active;
    move_confirm_value.value[key] = undefined;
    return;
  }
  list.forEach((item) => {
    item.active = item.value === sub_item.value ? !sub_item.active : false;
  });

  move_confirm_value.value[key] = sub_item.value as any;
};

const isMoveTypeExist = (type: MoveType) =>
  proxy_move_list.value.findIndex((item) => item.key === type) > -1;

const is_disabled = computed(() => {
  console.log(`[is_disabled] move_confirm_value`, move_confirm_value.value);

  if (isMoveTypeExist(MoveType.language)) {
    return !move_confirm_value.value.language;
  }

  if (isMoveTypeExist(MoveType.plate_id)) {
    return !move_confirm_value.value.plate_id;
  }

  return true;
});

const onConfirm = () => {
  if (is_disabled.value) {
    return;
  }
  emits("move-confirm", move_confirm_value.value);
};

onMounted(() => {
  move_confirm_value.value = getDefaultMoveConfirmValue();
  proxy_move_list.value = cloneDeep(props.move_list).map((item) => {
    const key = item.key;
    item.list.forEach((list_item) => {
      const value = get(props.default_move_confirm_value, key);
      if (value) {
        list_item.active = list_item.value === value;
        list_item.active && (move_confirm_value.value[key] = list_item.value);
      }
      return list_item;
    });
    return item;
  });
});
</script>

<template>
  <Dialog :show="show" :title="t('move_contents_to')">
    <div class="pt-[4px]">
      <div
        v-for="(item, index) in proxy_move_list"
        :key="index"
        class="mb-[12px] last-of-type:mb-0 border-b-[1px] border-[var(--line-1)] last-of-type:border-b-0"
      >
        <div
          class="text-[length:11px] leading-[13px] text-[color:var(--text-1)] text-left mb-[6px]"
        >
          {{ item.title }}
        </div>
        <div class="flex flex-wrap">
          <div
            v-for="(sub_item, sub_index) in item.list"
            :key="sub_index"
            :class="[
              `mr-[12px] cursor-pointer mb-[12px] text-[length:11px] leading-[13px]  last-of-type:mr-0 min-w-[61px] px-[6px] rounded-[2px] flex items-center justify-center h-[28px]`,
              sub_item.active
                ? 'text-[var(--brand-1)] bg-[var(--brand-2)] dark:bg-[var(--brand-1-20)]'
                : 'text-[color:var(--text-2)] bg-[var(--fill-2)]',
            ]"
            @click="onSelectSubItem(sub_item, item.list, item.key as any)"
          >
            {{ sub_item.name }}
          </div>
        </div>
      </div>
    </div>
    <Button type="primary" class="w-full !mt-0" :disabled="is_disabled" @click="onConfirm">
      {{ t("confirm") }}
    </Button>
    <Button type="secondary" class="w-full !mt-[12px]" @click="emits('close')">
      <span class="text-[color:var(--text-1)]">{{ t("cancel") }}</span>
    </Button>
  </Dialog>
</template>

<style lang="scss" scoped></style>
