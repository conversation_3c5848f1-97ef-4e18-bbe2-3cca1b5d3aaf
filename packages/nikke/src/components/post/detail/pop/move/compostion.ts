// types
import { PlatId } from "packages/types/common";
import { ComposeContentType } from "packages/types/content";
import { UserAuthType } from "packages/types/user";

// utils
import { getPlates } from "@/api/home";
import { useLang } from "@/composables/use-lang";
import { t } from "@/locales";
import { ref } from "vue";
import { get } from "lodash-es";
import logger from "packages/utils/logger";

const debug = logger("[detail:move:logger]");

export enum MoveType {
  plate_id = "plate_id",
  language = "language",
}

export const PLATE_FILTER_MAP: Partial<
  Record<PlatId, Partial<Record<UserAuthType, Record<ComposeContentType, PlatId[]>>>>
> = {
  [PlatId.outpost]: {
    [UserAuthType.creator]: {
      [ComposeContentType.richtext]: [PlatId.guides],
      [ComposeContentType.video]: [PlatId.nikkeart, PlatId.guides],
      [ComposeContentType.image]: [],
    },
    [UserAuthType.official]: {
      [ComposeContentType.richtext]: [PlatId.official, PlatId.guides],
      [ComposeContentType.video]: [PlatId.official, PlatId.nikkeart, PlatId.guides],
      [ComposeContentType.image]: [],
    },
  },
  [PlatId.guides]: {
    [UserAuthType.creator]: {
      [ComposeContentType.richtext]: [PlatId.outpost],
      [ComposeContentType.video]: [PlatId.nikkeart, PlatId.outpost],
      [ComposeContentType.image]: [],
    },
    [UserAuthType.official]: {
      [ComposeContentType.richtext]: [PlatId.outpost, PlatId.official],
      [ComposeContentType.video]: [PlatId.nikkeart, PlatId.outpost, PlatId.official],
      [ComposeContentType.image]: [],
    },
  },
  [PlatId.nikkeart]: {
    [UserAuthType.creator]: {
      [ComposeContentType.richtext]: [],
      [ComposeContentType.image]: [],
      [ComposeContentType.video]: [PlatId.outpost, PlatId.guides],
    },
    [UserAuthType.official]: {
      [ComposeContentType.richtext]: [],
      [ComposeContentType.video]: [PlatId.outpost, PlatId.official, PlatId.guides],
      [ComposeContentType.image]: [],
    },
  },
  [PlatId.official]: {
    [UserAuthType.creator]: {
      [ComposeContentType.richtext]: [],
      [ComposeContentType.video]: [],
      [ComposeContentType.image]: [],
    },
    [UserAuthType.official]: {
      [ComposeContentType.richtext]: [PlatId.outpost, PlatId.guides],
      [ComposeContentType.video]: [PlatId.outpost, PlatId.nikkeart, PlatId.guides],
      [ComposeContentType.image]: [],
    },
  },
};

export const useMove = () => {
  const plat_list: any = ref([]);
  const { lang_list } = useLang();

  const getPlatConfigByPlatId = (plat_id?: number) => {
    return plat_id ? plat_list.value.find((item: any) => item.id === plat_id) : {};
  };

  const filterPlatList = (
    options: {
      compose_content_type?: ComposeContentType;
      plate_id?: number;
      is_official?: boolean;
    },
    item: any,
  ) => {
    const { compose_content_type, plate_id, is_official } = options;

    // 相同板块之间不能移动
    if (item.id === plate_id) {
      return false;
    }

    const post_plat_config = getPlatConfigByPlatId(plate_id);
    // 帖子板块类型
    const post_plat_plat_id_type = post_plat_config.unique_identifier;
    const user_type = is_official ? UserAuthType.official : UserAuthType.creator;

    const list =
      get(PLATE_FILTER_MAP, `${post_plat_plat_id_type}.${user_type}.${compose_content_type}`) || [];

    debug.log("当前 post 问发布的板块（outpost/nikkeart/official）", post_plat_plat_id_type);
    debug.log("当前 post 的发布用户（普通/官方）", user_type);
    debug.log("当前 post 的发布类型（图文/图片/视频）", compose_content_type);
    debug.log(`可以移动的板块列表 `, list);

    return list.includes(item.unique_identifier as PlatId);
  };

  const getMoveList = (options: {
    compose_content_type?: ComposeContentType;
    plate_id?: number;
    is_official?: boolean;
  }) => {
    const { is_official } = options;
    return [
      {
        key: MoveType.plate_id,
        title: t("module"),
        active: 0,
        list: plat_list.value.filter((item: any) => filterPlatList(options, item)),
      },
      {
        key: MoveType.language,
        title: t("area"),
        active: 0,
        list: is_official ? [] : lang_list.value.map((item) => ({ ...item, active: false })),
      },
    ].filter((item) => item.list.length > 0);
  };

  const onLoadPlats = async () => {
    const { list } = await getPlates.run({ page_type: 0, limit: 10 });
    plat_list.value = list
      .map((item) => ({
        ...item,
        active: false,
        name: item.plate_name,
        value: item.id,
      }))
      .filter((item) =>
        [PlatId.outpost, PlatId.nikkeart, PlatId.guides, PlatId.official].includes(
          item.unique_identifier as PlatId,
        ),
      );
  };

  onLoadPlats();

  return {
    // move_list,
    getMoveList,
    onLoadPlats,
  };
};
