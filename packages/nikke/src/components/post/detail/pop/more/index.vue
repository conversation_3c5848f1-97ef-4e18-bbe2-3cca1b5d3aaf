<script setup lang="ts">
// cpnts
import Dialog from "@/components/ui/dialog/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

// types
import { ActionItem, ActionType } from "packages/types/post";

// utils
import { t } from "@/locales";
import { computed } from "vue";
import { useMore } from "./composition";

const props = defineProps<{
  show: boolean;
  ignores: ActionType[];
  overrides?: Record<ActionType, { icon: string; text: string; type: ActionType }>;
}>();

const emits = defineEmits(["close", "click"]);

const { getMoreActions } = useMore();
const actions = computed(() => getMoreActions(props.ignores, props.overrides));

const onItemClick = (item: ActionItem) => {
  emits("click", item.type);
  emits("close");
};
</script>

<template>
  <Dialog :show="show" :title="t('actions')" @close="emits('close')">
    <div class="pb-[16px] pt-[5px]">
      <div
        v-for="(item, index) in actions"
        :key="index"
        class="flex items-center px-[20px] py-[8px] border-[1px] border-[var(--line-1)] dark:bg-[var(--fill-3)] dark:border-[var(--line-1)] cursor-pointer rounded-[1px] mb-[6px] last-of-type:mb-0"
        @click="onItemClick(item)"
      >
        <SvgIcon
          :name="item.icon"
          color="var(--text-1)"
          class="w-[24px] h-[24px] mr-[12px] flex-shrink-0"
        ></SvgIcon>
        <span class="text-[length:12px] leading-[14px] text-[color:var(--text-1)]">
          {{ item.text }}
        </span>
      </div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped></style>
