import { t } from "@/locales";
import { ActionType } from "packages/types/post";

export const useMore = () => {
  const getMoreActions = (
    ignores: ActionType[],
    overrides?: Record<ActionType, { icon: string; text: string; type: ActionType }>,
  ) =>
    [
      {
        icon: "icon-reporting",
        text: t("reporting"),
        type: ActionType.report,
      },
      {
        icon: "icon-editing",
        text: t("editing"),
        type: ActionType.edit,
      },
      {
        icon: "icon-delete",
        text: t("delete"),
        type: ActionType.delete,
      },
      {
        icon: "icon-move",
        text: t("move_to"),
        type: ActionType.move,
      },
      {
        icon: "icon-top",
        text: t("top_comment"),
        type: ActionType.top_comment,
      },
      {
        icon: "icon-bottom",
        text: t("bottom_comment"),
        type: ActionType.bottom_comment,
      },
      {
        icon: "icon-copy",
        text: t("copy_comment_id"),
        type: ActionType.copy_comment_id,
      },
      {
        icon: "icon-hashtag",
        text: t("hashtag_manage"),
        type: ActionType.hashtag_manage,
      },
      {
        icon: "icon-authoring-statement",
        text: t("authoring_statement"),
        type: ActionType.authoring_statement,
      },
    ]
      .map((item) => Object.assign(item, overrides?.[item.type] || {}))
      .filter((item) => (ignores && ignores.length ? !ignores.includes(item.type) : true));

  return {
    getMoreActions,
  };
};
