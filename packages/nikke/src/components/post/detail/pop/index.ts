// cpnts
import More from "./more/index.vue";
import Report from "./report/index.vue";
import Move from "./move/index.vue";
import DeleteReason from "./delete-reason/index.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";
import { ref } from "vue";

const CPNT_OBJ = {
  more: More,
  report: Report,
  move: Move,
  delete_reason: DeleteReason,
};

type CpntObj = keyof typeof CPNT_OBJ;

export const usePop = () => {
  const dialog = ref<Record<CpntObj, any>>(
    Object.keys(CPNT_OBJ).reduce(
      (acc, cur) => {
        return Object.assign(acc, { [cur]: null });
      },
      {} as Record<CpntObj, any>,
    ),
  );

  const show = (
    options: any & {
      callback: (options: { value: PopCallbackValue; close: Function }) => void;
    },
  ) => {
    const type = options.type as CpntObj;
    const Cpnt = CPNT_OBJ[type] || More;

    return (dialog.value[type] = showDialog(
      Cpnt,
      Object.assign(options, {
        show: true,
        onClose: () => {
          dialog.value[type]?.unmount();
        },
      }),
    ));
  };

  return {
    show,
    dialog,
  };
};
