<template>
  <div class="flex flex-col">
    <span
      v-if="translate_btn_visible"
      class="mb-[5px] cursor-pointer h-[27px] inline-flex items-center"
    >
      <template v-if="translate_status === PostTranslateStatus.translated">
        <span class="text-[color:var(--brand-1)]" @click="post_detail_store.onTranslateContent">
          {{ t("view_original") }}
        </span>
      </template>
      <template v-else>
        <Btns
          type="default"
          size="m"
          :icon="loading.translate ? 'icon-loading' : ''"
          iconcolor="var(--brand-1)"
          :text="loading.translate ? t('translating') : t('translate')"
          @click="post_detail_store.onTranslateContent"
        >
        </Btns>
      </template>
    </span>

    <div class="flex items-center w-full">
      <div
        class="flex items-center flex-1 overflow-hidden"
        @click="
          () =>
            router.push({
              path: Routes.USER,
              query: {
                openid: detail.user?.intl_openid,
              },
            })
        "
      >
        <div
          class="w-[56px] h-[56px] flex-shrink-0"
          :class="[detail.user?.avatar_pendant && '!mx-[6px] !my-[6px]']"
        >
          <Avatar
            :src="detail.user?.avatar"
            :auth_type="detail.user?.auth_type"
            :frame="detail.user?.avatar_pendant"
          ></Avatar>
        </div>
        <div class="ml-[10px] flex-1 overflow-hidden">
          <div class="flex items-center gap-[4px] overflow-hidden">
            <div
              class="text-[length:16px] text-[color:var(--text-1)] font-bold leading-[21px] truncate"
            >
              {{ detail.user?.username }}
            </div>
            <TypeLabel
              v-if="auth_desc"
              :game_tag="detail.user?.game_tag"
              :game_tag_num="detail.user?.game_tag_num"
              :mood="detail.user?.mood"
              :is_self="false"
            ></TypeLabel>
          </div>
          <div class="mt-[4px]">
            <div v-if="auth_desc" class="text-[length:12px] text-[color:var(--brand-1)]">
              {{ detail.user?.auth_desc }}
            </div>
            <TypeLabel
              v-if="!auth_desc"
              :game_tag="detail.user?.game_tag"
              :game_tag_num="detail.user?.game_tag_num"
              :mood="detail.user?.mood"
              :is_self="false"
            ></TypeLabel>
          </div>
        </div>
      </div>

      <div v-if="!detail.is_mine" class="ml-[10px]">
        <Btns v-if="detail.user.is_black" :text="t('blocking')" type="disabled"></Btns>
        <Btns
          v-else-if="detail.is_mutual_follow"
          v-click-interceptor.need_login.mute.sign_privacy="post_detail_store.onFollow"
          type="default"
          icon="iocn-follower-cur"
          iconcolor="var(--brand-1)"
        ></Btns>
        <Btns
          v-else-if="detail.is_follow"
          v-click-interceptor.need_login.mute.sign_privacy="post_detail_store.onFollow"
          type="default"
          icon="icon-followed"
          iconcolor="var(--brand-1)"
        ></Btns>
        <Btns
          v-else
          v-click-interceptor.need_login.mute.sign_privacy="post_detail_store.onFollow"
          type="primary"
          :m-small-font="true"
          :text="t('follow')"
        >
          <template #icon>
            <span class="text-[18px] mr-[2px]">+ </span>
          </template>
        </Btns>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import Avatar from "@/components/common/avatar/index.vue";
import TypeLabel from "@/components/common/type-label/index.vue";
import Btns from "@/components/common/btns/index.vue";

// types
import { PostTranslateStatus } from "packages/types/post";

// utils
import { usePostDetailStore } from "@/store/post/detail";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { Routes } from "@/router/routes";
import router from "@/router";
import { getStandardizedLang } from "packages/utils/standard";

import { computed } from "vue";

const { t } = useI18n();
const lang = getStandardizedLang();
const post_detail_store = usePostDetailStore();
const { detail, translate_status, loading } = storeToRefs(post_detail_store);

post_detail_store.resetTranslateStatus();

const auth_desc = computed(() => {
  return detail.value.user?.auth_desc || undefined;
});

const translate_btn_visible = computed(() => {
  return detail.value.is_original_content ? lang !== detail.value.original_language : true;
});
</script>
