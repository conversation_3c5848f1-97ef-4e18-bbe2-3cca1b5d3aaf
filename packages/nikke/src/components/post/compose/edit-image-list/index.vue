<script setup lang="ts">
// cpnts
import Draggable from "vuedraggable";
import SvgIcon from "@/components/common/svg-icon.vue";
import PicturePopup from "@/components/common/picture-popup/index.vue";

// utils
import { ref } from "vue";
import { useComposePostStore } from "@/store/post/compose";
import { COMPOSE_MAX_IMAGE_LEN } from "@/configs/const";
import { createViewer } from "@/components/common/img-viewer/index";

withDefaults(
  defineProps<{
    max?: number;
  }>(),
  {
    max: COMPOSE_MAX_IMAGE_LEN,
  },
);

const compose_store = useComposePostStore();
const picture_popup_visible = ref(false);

const togglePicturePopupVisible = () =>
  (picture_popup_visible.value = !picture_popup_visible.value);

const onChange = (url: string) => {
  compose_store.onAddPicUrls(url);
  togglePicturePopupVisible();
};

const onImageClick = (event: Event) => {
  const target = event.target as HTMLElement;
  createViewer({
    image_list: compose_store.onGetActiveContent().pic_urls,
    target,
  });
};
</script>

<template>
  <div @click="onImageClick">
    <Draggable
      v-model="compose_store.onGetActiveContent().pic_urls"
      item-key="id"
      class="drag-wrap grid grid-cols-3 gap-[8px] justify-items-center w-full overflow-hidden py-[7px] box-border"
    >
      <template #item="item">
        <div class="relative w-[100px] h-[100px]">
          <img
            :src="item.element"
            class="image w-full h-full object-cover rounded-[6px] cursor-pointer"
          />
          <div
            class="absolute top-[-6px] right-[-6px] w-[20px] h-[20px] cursor-pointer rounded-[50%] bg-[color:var(--fill-3)]"
            @click.stop="compose_store.onRemovePicUrls(item.element)"
          >
            <SvgIcon name="icon-close" color="var(--text-2)" class="w-full h-full"></SvgIcon>
          </div>
        </div>
      </template>

      <template #footer>
        <div
          v-if="compose_store.onGetActiveContent().pic_urls.length < max"
          class="w-[100px] h-[100px] bg-[color:var(--op-fill-white)] rounded-[4px] flex items-center justify-center cursor-pointer"
          @click="togglePicturePopupVisible"
        >
          <SvgIcon name="icon-add" color="var(--text-1)" class="w-[26px] h-[26px]"></SvgIcon>
        </div>
      </template>
    </Draggable>
  </div>

  <PicturePopup
    :visible="picture_popup_visible"
    @close="togglePicturePopupVisible"
    @change="onChange"
  ></PicturePopup>
</template>

<style lang="scss" scoped>
.drag-wrap {
  :deep(.sortable-ghost) {
    opacity: 0;
    overflow: hidden;
  }
  :deep(.sortable-drag) {
    opacity: 1 !important;
  }
  :deep(.sortable-chosen) {
    .image {
      transform: scale(1.08);
    }
  }
}
</style>
