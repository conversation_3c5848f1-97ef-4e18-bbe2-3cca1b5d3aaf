<script setup lang="ts">
// cpnts
import LinkInput from "@/components/post/compose/link-input/index.vue";
import Button from "@/components/ui/button/index.vue";

// assets
import IconYoutube from "@/assets/imgs/logo/icon-youtube.png";
import IconYoutubeDark from "@/assets/imgs/logo/icon-youtube-dark.png";
import IconTiktok from "@/assets/imgs/logo/icon-tiktok.png";
import IconTiktokDark from "@/assets/imgs/logo/icon-tiktok-dark.png";
import IconFacebook from "@/assets/imgs/logo/icon-facebook2.png";

// utils
import { useI18n } from "vue-i18n";
import { useComposePostStore } from "@/store/post/compose";
import { Status } from "packages/types/common";
import { ref, watch } from "vue";
import { trim } from "lodash-es";
import { CommonImage } from "@/components/common/image";

const compose_store = useComposePostStore();
const { t } = useI18n();
const { IS_DARK_MODE } = window;

const video_url = ref<string>(
  import.meta.env.DEV ? "https://www.youtube.com/watch?v=xFPLkkwBy1E" : "",
);
// const video_url = ref("https://www.youtube.com/watch?v=xFPLkkwBy1E");
// const video_url = ref("https://youtu.be.com/mWl__0Lphrg");
// const video_url = ref("https://m.youtube.com/watch?v=mWl__0Lphrg");
// const video_url = ref("https://www.youtube.com/watch?v=mWl__0Lphrg");
// const video_url = ref("https://www.youtube.com/embed/mWl__0Lphrg");
// const video_url = ref("https://www.youtube.com/watch?v=PkcWBMu3ALE");
// const video_url = ref("https://www.tiktok.com/@scout2015/video/6718335390845095173");
// const video_url = ref("https://t.tiktok.com/i18n/share/video/6718335390845095173");
// const video_url = ref("https://www.youtube.com/shorts/CCLEpnBzEFQ?feature=share");

watch(
  () => video_url.value,
  () => {
    const active_content = compose_store.onGetActiveContent();
    if (active_content.fe_video_parse_status === Status.error) {
      active_content.fe_video_parse_status = Status.undo;
    }
  },
);

const getBtnText = () => {
  const active_content = compose_store.onGetActiveContent();
  if (active_content.fe_video_parse_status === Status.loading) {
    return "";
  }
  return t("confirm");
};

const onFocus = () => {
  setTimeout(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  }, 100);
};

const onBlur = () => {
  setTimeout(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  }, 100);
};
</script>

<template>
  <div>
    <div class="font-bold text-[length:14px] leading-[18px] text-[color:var(--text-1)]">
      {{ t("video_link") }}
    </div>
    <div class="flex items-center gap-[8px] mt-[6px]">
      <span
        class="font-medium text-[length:11px] leading-[14px] text-[color:var(--text-2)] font-[Inter] -mr-[3px]"
      >
        {{ t("support") }}
      </span>

      <CommonImage
        :src="IS_DARK_MODE ? IconYoutubeDark : IconYoutube"
        class="w-[45px] h-[10px] object-contain"
      />
      <CommonImage
        :src="IS_DARK_MODE ? IconTiktokDark : IconTiktok"
        class="w-[45px] h-[13px] object-contain"
      />

      <!-- TODO: 1.3 不做，后面再做 -->
      <CommonImage v-if="false" :src="IconFacebook" class="w-[62px] h-[17px] object-contain" />
    </div>

    <LinkInput
      v-model:content="video_url"
      :placeholder="t('please_paste_the_link_here')"
      :invalid="false"
      :invalid-text="t('video_parse_failed')"
      type="link"
      clear-btn-position="bottom"
      height-property="minHeight"
      textarea-height="290px"
      class="px-[11px] pt-[8px] pb-[27px] mt-[18px] !bg-[var(--op-fill-white)]"
      @focus="onFocus"
      @blur="onBlur"
    ></LinkInput>

    <Button
      type="primary"
      :loading="compose_store.onGetActiveContent().fe_video_parse_status === Status.loading"
      :disabled="
        !video_url || compose_store.onGetActiveContent().fe_video_parse_status === Status.error
      "
      :class="[`w-[218px] mx-auto mt-[35px]`]"
      @click="trim(video_url) && compose_store.onParseLink(trim(video_url))"
    >
      {{ getBtnText() }}
    </Button>
  </div>
</template>

<style scoped lang="scss"></style>
