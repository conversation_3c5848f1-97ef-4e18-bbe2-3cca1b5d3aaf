<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { TiktokPlayer, YoutubePlayer } from "@/components/common/video/player/index.ts";

// types
import { Platform, PopCallbackValue, Status } from "packages/types/common";

// utils
import { useComposePostStore } from "@/store/post/compose";
import { t } from "@/locales";
import { computed } from "vue";

const compose_store = useComposePostStore();
const { show } = useDialog();
const active_content = computed(() => compose_store.onGetActiveContent());

const onReplaceVideo = () => {
  if (!active_content.value.title && !active_content.value.content) {
    onReplaceDialogConfirm();
    return;
  }
  toggleReplaceDialogVisible();
};

const toggleReplaceDialogVisible = () => {
  show({
    title: t("replace_the_video") + "?",
    content: t("replace_the_video_content"),
    confirm_text: t("replace_the_video"),
    cancel_text: t("cancel"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.cancel) {
        close();
        return;
      }
      close();
      onReplaceDialogConfirm();
    },
  });
};

const onReplaceDialogConfirm = () => {
  compose_store.onSetActiveContentField("fe_video_parse_status", Status.undo);
};
</script>

<template>
  <div>
    <div class="flex items-start justify-between">
      <div class="font-medium text-[length:13px] leading-[16px] text-[color:var(--text-1)]">
        {{ t("video") }}
      </div>

      <div
        class="common-btns cursor-pointer whitespace-nowrap min-w-[102px] h-[24px] px-[9px] flex items-center justify-center border-[length:1px] border-solid border-[color:var(--line-1)] relative z-[1] box-border"
        @click="onReplaceVideo"
      >
        <i
          class="absolute -bottom-[3px] right-[2px] w-[1px] h-[11px] -z-[1] rotate-[45deg] bg-[color:var(--fill-5)]"
        ></i>
        <SvgIcon
          name="icon-video"
          color="var(--text-1)"
          class="w-[12px] h-[12px] mr-[4px]"
        ></SvgIcon>
        <span class="font-medium text-[length:10px] leading-[12px] text-[color:var(--text-1)]">
          {{ t("replace_video") }}
        </span>
      </div>
    </div>

    <div class="w-full flex justify-center mt-[8px] rounded-[4px]">
      <TiktokPlayer
        v-show="active_content.fe_video_parse_info?.platform === Platform.tiktok"
        :vid="active_content.fe_video_parse_info?.video_id"
      ></TiktokPlayer>

      <YoutubePlayer
        v-show="
          [Platform.youtube, Platform.youtubeshort].includes(
            active_content.fe_video_parse_info?.platform as Platform,
          )
        "
        :vid="active_content.fe_video_parse_info?.video_id"
      ></YoutubePlayer>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
