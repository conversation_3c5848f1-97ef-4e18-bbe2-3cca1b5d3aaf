<template>
  <div
    class="relative h-[44px] w-full bg-[color:var(--fill-3)] border-t-[1px] border-[var(--line-1)] dark:border-[var(--line-1)]"
  >
    <ul
      :class="[
        `flex items-center gap-[8px] h-full w-full overflow-x-auto py-[8px] px-[12px] pr-[80px]`,
        is_mobile && 'no-scrollbar',
      ]"
    >
      <li
        v-for="(item, index) in list"
        :key="index"
        :class="[
          `px-[12px] py-[7px] bg-[color:var(--op-fill-white)] text-[length:12px] h-[28px] flex rounded-[4px] items-center flex-shrink-0 relative cursor-pointer whitespace-nowrap`,
          ids?.includes(item.id)
            ? 'text-[var(--brand-1)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)]'
            : 'text-[color:var(--text-2)] border-[color:transparent]',
        ]"
        @click="onSelect(item)"
      >
        #{{ item.name }}
      </li>
    </ul>
    <div
      class="absolute right-0 top-[1px] bottom-0 flex items-center bg-[color:var(--fill-3)] cursor-pointer"
      @click="onMore"
    >
      <span
        class="font-normal text-[length:14px] leading-[16px] text-[color:var(--brand-1)] ml-[2px]"
      >
        {{ t("more") }}
      </span>
      <SvgIcon
        name="icon-arrow-right"
        color="var(--brand-1)"
        class="w-[12px] h-[12px] ml-[4px] mr-[6px]"
      ></SvgIcon>
      <i
        class="absolute top-0 -left-[18px] w-0 h-0 border-[length:23px_10px_20px_8px] border-[color:transparent_var(--fill-3)_var(--fill-3)_transparent] border-solid"
      ></i>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { Tag } from "packages/types/post";

// utils
import { useI18n } from "vue-i18n";
import { useResponsive } from "@/composables/use-responsive";

const { t } = useI18n();

defineProps<{
  list?: Array<Tag>;
  ids?: Array<number>;
}>();
const emits = defineEmits(["more", "select"]);

const { is_mobile } = useResponsive();

const onMore = () => emits("more");
const onSelect = (item: Tag) => emits("select", item);
</script>
