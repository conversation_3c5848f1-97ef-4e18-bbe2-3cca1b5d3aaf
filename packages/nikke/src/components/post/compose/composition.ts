import { useTopicPop } from "@/components/post/compose/pop/topic";
import { useComposePostStore } from "@/store/post/compose";
import { debounce } from "lodash-es";
import { Tag } from "packages/types/post";
import { storeToRefs } from "pinia";
import Quill from "quill";
import { Delta } from "quill/core";
import { ref } from "vue";
import { useTagStore } from "@/store/tag.store";

export const useEditorTag = () => {
  const { show: showTopicPop } = useTopicPop();
  const compose_store = useComposePostStore();
  const tag_store = useTagStore();

  const had_removed_tag_symbol = ref(false);
  const dialog = ref();

  const onTagSelect = debounce((quill: Quill, _delta: Delta) => {
    if (dialog.value) {
      return;
    }
    had_removed_tag_symbol.value = false;

    const onQuillBlur = (interval: number) => {
      setTimeout(() => {
        quill.blur();
      }, interval);
    };

    const { all_tag_list, recommended_tag_list } = storeToRefs(tag_store);
    const { search_tag_list, selected_tag_list, onClearSearchTagName, onTagSearch, onTagChange } =
      compose_store.useComposeTag();

    dialog.value = showTopicPop({
      focus: true,
      all_tag_list,
      recommended_tag_list,
      search_tag_list,
      selected_tag_list,
      onChange(tag: Tag) {
        onTagChange(tag);

        if (!had_removed_tag_symbol.value) {
          had_removed_tag_symbol.value = true;
          const range = quill.getSelection(true) || { index: 0 };
          const range_index = Math.max(range.index - 1, 0);
          quill.deleteText(range_index, 1, Quill.sources.USER);
        }

        onQuillBlur(300);
      },
      onSearch: onTagSearch,
      onRemove: compose_store.onTagRemove,
      onClose() {
        quill.focus();
        onClearSearchTagName();
        dialog.value.close();
        dialog.value = null;
      },
    });

    onQuillBlur(200);
  }, 200);

  return {
    onTagSelect,
  };
};
