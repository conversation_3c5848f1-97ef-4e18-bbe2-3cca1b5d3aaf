<script setup lang="ts">
// cpnts
import ContainerHeader from "@/components/post/compose/container/header/index.vue";
import Topics from "@/components/post/compose/container/body/topics/index.vue";
import Richtext from "./richtext/index.vue";
import Video from "./video/index.vue";
import Image from "./image/index.vue";
import FriendCard from "@/components/common/friend-card/index.vue";
import UnionCard from "@/components/common/union-card/index.vue";
import { useTopicPop } from "@/components/post/compose/pop/topic";
// import { showRoleDialog } from "@/components/common/player-pop/index.ts";

// types
import { ComposeContentType } from "packages/types/content";
import { FriendCardStatus, UnionCardStatus } from "packages/types/user";

// utils
import { computed, watch } from "vue";
import { useComposePostStore } from "@/store/post/compose";
import { storeToRefs } from "pinia";
import { useUser } from "@/store/user";
import { useTagStore } from "@/store/tag.store";
import { useUnionStore } from "@/store/union";

const user_store = useUser();
const compose_store = useComposePostStore();
const { show: showTopicPop } = useTopicPop();
const tag_store = useTagStore();
const union_store = useUnionStore();
const { state } = storeToRefs(compose_store);
const { all_tag_list, recommended_tag_list } = storeToRefs(tag_store);

const Cpnt = computed(() => {
  const type = state.value.compose_params.type;
  return {
    [ComposeContentType.richtext]: Richtext,
    [ComposeContentType.video]: Video,
    [ComposeContentType.image]: Image,
  }[type];
});

const onDeleteFriendCard = () => {
  compose_store.setStateComposeParamsKey("need_friend_card", false);
  if (state.value.is_edit) {
    compose_store.setStateComposeParamsKey("need_refresh_friend_card", 2);
  }
};

const onDeleteUnionCard = () => {
  compose_store.setStateComposeParamsKey("need_guild_card", false);
  if (state.value.is_edit) {
    compose_store.setStateComposeParamsKey("need_refresh_guild_card", 2);
  }
};

const friend_card_visible = computed(() => {
  return state.value.compose_params.need_friend_card;
});

const union_card_visible = computed(() => {
  return state.value.compose_params.need_guild_card;
});

watch(
  union_card_visible,
  async (val) => {
    if (val) await union_store.getMyGuildInfo({ latest: false });
  },
  { immediate: true },
);

const user_game_player_info = computed(() => {
  if (state.value.is_edit) {
    // 有变动，采用当前的
    if (state.value.compose_params.need_refresh_friend_card === 1) {
      return user_store.user_game_player_info;
    }
    return state.value.post_detail.friend_card;
  }
  return user_store.user_game_player_info;
});

const onClickTopic = () => {
  const {
    //
    search_tag_list,
    selected_tag_list,
    onTagChange,
    onTagSearch,
  } = compose_store.useComposeTag();

  showTopicPop({
    all_tag_list,
    recommended_tag_list,
    search_tag_list,
    selected_tag_list,
    onChange: onTagChange,
    onSearch: onTagSearch,
    onRemove: onRemoveTopic,
  });
};

const onRemoveTopic = (index: number) => {
  compose_store.onTagRemove(index);
};

// const onClickFriendCard = () => {
//   showRoleDialog({
//     uid: user_store.user_info.intl_openid,
//     friend_card_status: FriendCardStatus.disabled,
//   });
// };
</script>

<template>
  <!-- bg-[var(--fill-3)] -->
  <div class="grow overflow-x-hidden overflow-y-auto flex flex-col">
    <ContainerHeader></ContainerHeader>
    <component :is="Cpnt"></component>
    <FriendCard
      v-if="friend_card_visible"
      class="my-[15px] !w-[calc(100%_-_24px)] mx-auto !cursor-default flex-shrink-0"
      :delete_visible="true"
      :status="FriendCardStatus.disabled"
      :user_game_player_info="user_game_player_info"
      @delete="onDeleteFriendCard"
    ></FriendCard>
    <UnionCard
      v-if="union_card_visible && union_store?.my_union"
      class="my-[15px] !w-[calc(100%_-_24px)] mx-auto !cursor-default flex-shrink-0"
      :class="friend_card_visible ? '!mt-[0px]' : ''"
      :delete_visible="true"
      :status="UnionCardStatus.disabled"
      :union_info="union_store?.my_union"
      @delete="onDeleteUnionCard"
    ></UnionCard>
    <Topics
      class="px-[12px]"
      :selected_tag_list="state.compose_params.tags"
      :all_tag_list="all_tag_list"
      :recommended_tag_list="recommended_tag_list"
      @click="onClickTopic"
      @remove="onRemoveTopic"
    ></Topics>
  </div>
</template>

<style lang="scss" scoped></style>
