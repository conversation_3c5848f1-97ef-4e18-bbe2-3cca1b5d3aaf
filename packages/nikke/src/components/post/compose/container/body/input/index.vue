<template>
  <div class="relative w-full text-[16px]">
    <textarea
      ref="textarea_ref"
      v-model="text"
      class="w-full bg-transparent caret-[color:var(--brand-1)] overflow-hidden resize-none outline-none font-normal appearance-none flex items-start box-border text-[color:var(--text-1)]"
      :placeholder="''"
      :maxlength="max"
      :style="{
        height,
      }"
      @keydown="onKeyDown"
      @focus="onFocus"
      @blur="onBlur"
    />

    <div
      v-if="placeholder_visible"
      class="absolute left-0 top-0 pointer-events-none items-center flex text-[length:14px] text-[color:var(--text-3)] font-bold"
    >
      <span>
        {{ placeholder }}
      </span>

      <slot name="required">
        <span class="text-[color:var(--error)]">*</span>
      </slot>
    </div>

    <div
      v-if="max"
      :class="[
        `w-full relative bottom-0 flex items-center justify-end text-[9px] leading-[11px] pointer-events-none`,
      ]"
    >
      <span class="text-[color:var(--brand-1)]">{{ text.length }}</span>
      <span class="mx-[2px] text-[color:var(--text-3)]">/</span>
      <span class="text-[color:var(--text-3)]">{{ max }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { useCompositionEvent } from "@/composables/use-composition-event";
import { useKeyboardPopupCompatible } from "@/composables/use-compatible";

const props = withDefaults(
  defineProps<{
    max?: string | number;
    placeholder?: string;
    required?: boolean;
    default_height?: number;
  }>(),
  {
    default_height: 24,
  },
);

const emits = defineEmits(["blur", "focus"]);

const text = defineModel<string>({ required: true });

const { setElement, onElementFocus, onElementBlur } = useKeyboardPopupCompatible();
const { composition, onAddCompositionsEvent, onRemoveCompositionsEvent } = useCompositionEvent();

const height = ref(props.default_height + "px");
const textarea_ref = ref<HTMLTextAreaElement | null>();

const placeholder_visible = computed(() => {
  return composition.value ? false : text.value.length === 0;
});

const onKeyDown = (e: KeyboardEvent) => {
  if (e.key === "Enter") {
    e.preventDefault();
  }
};

const onUpdateHeight = (el: HTMLElement) => {
  // NOTE: 这里重置之后会设置 textarea 的高度为 48px（在 mac chrome 上），所以在不满一行的情况下有问题
  // 重置高度为 auto
  // height.value = "auto";

  nextTick(() => {
    height.value = `${text.value ? el.scrollHeight : props.default_height}px`;
  });
};

const onFocus = () => {
  onElementFocus();
  emits("focus");
};

const onBlur = () => {
  onElementBlur();
  emits("blur");
};

watch(
  () => text.value,
  () => {
    if (text.value) {
      nextTick(() => {
        onUpdateHeight(textarea_ref.value as HTMLElement);
      });
    }
  },
  {
    immediate: true,
  },
);

onMounted(() => {
  setElement(textarea_ref.value as HTMLElement);
  onAddCompositionsEvent(textarea_ref.value as HTMLElement);
});

onUnmounted(() => {
  onRemoveCompositionsEvent(textarea_ref.value as HTMLElement);
});
</script>

<style lang="scss" scoped></style>
