<script setup lang="ts">
// cpnts
import EditImageList from "@/components/post/compose/edit-image-list/index.vue";
import ComposeInput from "@/components/post/compose/container/body/input/index.vue";
import Editor from "@/components/common/editor/index.vue";

// configs
import { EDITOR_MAX_LEN } from "@/configs/const";
import Quill from "quill";

// utils
import { useComposePostStore } from "@/store/post/compose";
import { storeToRefs } from "pinia";
import { t } from "@/locales";
import { computed } from "vue";
import { useKeyboardPopupCompatible } from "@/composables/use-compatible";
import { useEditorTag } from "@/components/post/compose/composition.ts";

const { onTagSelect } = useEditorTag();
const { setElement, onElementFocus, onElementBlur } = useKeyboardPopupCompatible();
const compose_store = useComposePostStore();
const { editor } = storeToRefs(compose_store);

const title_placeholder = computed(() => `${t("title")}(${t("required")})`);
const editor_placeholder = computed(() => `${t("editor_default_placeholder")}`);

const onRender = (quill: Quill) => {
  setElement(quill.root);
};

const onFocus = (quill: Quill) => {
  // 聚焦的时候出现表情包面板
  compose_store.onEditorFocus();
  setTimeout(() => {
    // 禁止页面滑动
    onElementFocus();
    // quill.root.scrollIntoView();
    // 聚焦到光标
    quill.focus();
  }, 300);
};
</script>

<template>
  <div class="grow flex flex-col px-[15px]">
    <div class="mb-[20px] mt-[10px]">
      <EditImageList></EditImageList>
    </div>

    <ComposeInput
      v-model="compose_store.onGetActiveContent().title"
      :placeholder="title_placeholder"
      :max="200"
    ></ComposeInput>

    <i class="block w-full h-[0.5px] bg-[color:var(--line-1)] my-[12px]">&nbsp;</i>

    <Editor
      ref="editor"
      v-model:value="compose_store.onGetActiveContent().content"
      class="h-full grow"
      :maxlen="EDITOR_MAX_LEN"
      :placeholder="editor_placeholder"
      :enable_tag="true"
      :disabled_pasted_image="
        compose_store.isNikkeart(compose_store.current_plat_config.unique_identifier)
      "
      @tag="onTagSelect"
      @render="onRender"
      @focus="onFocus"
      @blur="onElementBlur"
    ></Editor>
  </div>
</template>

<style lang="scss" scoped></style>
