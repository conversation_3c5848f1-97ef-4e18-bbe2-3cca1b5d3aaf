<script setup lang="ts">
// cpnts
import { Tag as TagCpnt } from "@/components/common/tag";

// types
import { Tag } from "packages/types/post";

const props = defineProps<{
  empty_text?: string;
  selected_tag_list: number[];
  all_tag_list: Tag[];
  recommended_tag_list: Tag[];
}>();

const emits = defineEmits(["click", "remove"]);

const idToName = (id: number) => {
  return (
    props.all_tag_list.find((item) => item.id === id)?.name ||
    //
    props.recommended_tag_list.find((item) => item.id === id)?.name ||
    id
  );
};

const onClick = (index: number) => {
  emits("click", index);
};

const onRemove = (index: number) => {
  emits("remove", index);
};
</script>

<template>
  <div v-if="selected_tag_list.length" class="py-[4px] flex my-[10px] flex-wrap">
    <TagCpnt
      v-for="(id, index) in selected_tag_list"
      :key="index"
      :closable="true"
      class="mr-[12px] mb-[8px] dark:border-[1px] dark:border-[color:var(--brand-1)]"
      @click="onClick(index)"
      @close="onRemove(index)"
    >
      #{{ idToName(id) }}
    </TagCpnt>
  </div>

  <template v-else-if="empty_text">
    <div
      class="text-[var(--color-3)] my-[23px] flex items-center justify-center text-[14px] font-normal"
    >
      {{ empty_text }}
    </div>
  </template>
</template>

<style lang="scss" scoped></style>
