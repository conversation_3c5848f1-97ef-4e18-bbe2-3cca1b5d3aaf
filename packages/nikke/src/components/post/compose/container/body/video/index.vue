<script setup lang="ts">
// cpnts
import EditVideo from "@/components/post/compose/edit-video/index.vue";
import VideoLink from "@/components/post/compose/video-link/index.vue";
import ComposeInput from "@/components/post/compose/container/body/input/index.vue";
import Editor from "@/components/common/editor/index.vue";

// configs
import { EDITOR_MAX_LEN } from "@/configs/const";

// types
import Quill from "quill";

// utils
import { useComposePostStore } from "@/store/post/compose";
import { Status } from "packages/types/common";
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { t } from "@/locales";
import { useKeyboardPopupCompatible } from "@/composables/use-compatible";
import { useEditorTag } from "@/components/post/compose/composition.ts";

const { onTagSelect } = useEditorTag();
const { setElement, onElementFocus, onElementBlur } = useKeyboardPopupCompatible();
const compose_store = useComposePostStore();
const { editor, computed_active_content } = storeToRefs(compose_store);

const title_placeholder = computed(() => `${t("title")}(${t("required")})`);
const editor_placeholder = computed(() => `${t("editor_default_placeholder")}`);

const onRender = (quill: Quill) => {
  setElement(quill.root);
};
</script>

<template>
  <div :class="[`grow flex flex-col px-[15px]  mt-[20px]`]">
    <template v-if="computed_active_content.fe_video_parse_status !== Status.success">
      <VideoLink></VideoLink>
    </template>

    <template v-else>
      <EditVideo class="mb-[20px]"></EditVideo>

      <ComposeInput
        v-model="compose_store.onGetActiveContent().title"
        :placeholder="title_placeholder"
        :max="200"
      ></ComposeInput>

      <i class="block w-full h-[0.5px] bg-[color:var(--line-1)] my-[12px]">&nbsp;</i>

      <Editor
        ref="editor"
        v-model:value="compose_store.onGetActiveContent().content"
        class="h-full grow"
        :maxlen="EDITOR_MAX_LEN"
        :placeholder="editor_placeholder"
        :enable_tag="true"
        :disabled_pasted_image="
          compose_store.isNikkeart(compose_store.current_plat_config.unique_identifier)
        "
        @tag="onTagSelect"
        @render="onRender"
        @focus="onElementFocus"
        @blur="onElementBlur"
      ></Editor>
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
