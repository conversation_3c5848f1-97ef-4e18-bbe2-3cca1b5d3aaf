<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { usePlatPop } from "@/components/post/compose/pop/plat/index.ts";
import { useTypePop } from "@/components/post/compose/pop/type/index.ts";
import { useLangPop } from "@/components/post/compose/pop/lang/index.ts";

// utils
import { useComposePostStore } from "@/store/post/compose";
import { storeToRefs } from "pinia";
import { useLang } from "@/composables/use-lang";
import { t } from "@/locales";
import { useUser } from "@/store/user";
import { computed } from "vue";

const user_store = useUser();
const compose_store = useComposePostStore();
const {
  state,
  current_type_config,
  type_list,
  plat_list,
  current_plat_config,
  computed_active_content,
} = storeToRefs(compose_store);

const { lang_list } = useLang();

const { show: showPlatPop } = usePlatPop();
const { show: showTypePop } = useTypePop();
const { show: showLangPop } = useLangPop();

const getLangName = (lang: string) => {
  return lang_list.value.find((item) => item.value === lang)?.name || lang;
};

const can_select_lang_list = computed(() => {
  const langs = state.value.compose_params.contents.map((item) => item.language);
  return lang_list.value.filter((item) => !langs.includes(item.value));
});
</script>

<template>
  <div class="w-full flex items-center flex-col box-border max-w-[var(--max-pc-w)]">
    <div
      class="flex w-full border-b-[length:0.5px] pb-[12px] gap-x-[12px] px-[12px] pt-[12px] bg-[var(--op-fill-white)] border-solid border-[color:var(--line-1)]"
    >
      <div
        class="relative flex flex-1 items-center gap-[4px] cursor-pointer h-[34px] pl-[5px] pr-[28px] border-[length:0.5px] border-solid border-[color:var(--line-1)] bg-[color:var(--fill-3)] box-border"
        @click="
          showPlatPop({
            plat_list,
            plat_id: state.compose_params.plate_id,
            onChange(id: number) {
              compose_store.onChangePlat(id);
            },
          })
        "
      >
        <SvgIcon
          v-if="current_plat_config.icon"
          :name="current_plat_config.icon"
          color="var(--text-1)"
          class="w-[20px] h-[20px]"
        ></SvgIcon>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] mt-[4px]"
        >
          {{ current_plat_config.title }}
        </span>
        <SvgIcon
          name="icon-arrow-down"
          color="var(--text-1)"
          class="absolute right-[10px] top-1/2 -translate-y-1/2 w-[7px] h-[8px]"
        ></SvgIcon>
      </div>

      <div
        class="relative flex items-center gap-[4px] cursor-pointer h-[34px] pl-[5px] pr-[30px] border-[length:0.5px] border-solid border-[color:var(--line-1)] bg-[color:var(--fill-3)] box-border"
        @click="
          showTypePop({
            type_list,
            type: state.compose_params.type,
            onChange(type: number) {
              compose_store.onChangeType(type);
            },
          })
        "
      >
        <SvgIcon
          v-if="current_type_config.icon"
          :name="current_type_config.icon"
          color="var(--text-1)"
          class="w-[20px] h-[20px]"
        ></SvgIcon>

        <SvgIcon
          name="icon-arrow-down"
          color="var(--text-1)"
          class="absolute right-[10px] top-1/2 -translate-y-1/2 w-[7px] h-[8px]"
        ></SvgIcon>
      </div>

      <div
        v-if="!user_store.user_can_publish_multiple_language"
        class="relative flex flex-1 items-center gap-[4px] cursor-pointer h-[34px] pl-[5px] pr-[28px] border-[length:0.5px] border-solid border-[color:var(--line-1)] bg-[color:var(--fill-3)] box-border"
        @click="
          showLangPop({
            lang: computed_active_content.language,
            lang_list,
            onChange(lang: string) {
              compose_store.onChangeLang(lang);
            },
          })
        "
      >
        <SvgIcon name="icon-language" color="var(--text-1)" class="w-[20px] h-[20px]"></SvgIcon>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] w-[28px] flex-1 line-clamp-1"
        >
          {{ getLangName(computed_active_content.language) }}
        </span>
        <SvgIcon
          name="icon-arrow-down"
          color="var(--text-1)"
          class="absolute right-[10px] top-1/2 -translate-y-1/2 w-[7px] h-[8px]"
        ></SvgIcon>
      </div>
    </div>

    <div
      v-if="user_store.user_can_publish_multiple_language"
      class="min-h-[34px] w-full px-[12px] gap-x-[6px] gap-y-[6px] pt-[12px] flex flex-wrap"
    >
      <div
        v-for="(item, index) in state.compose_params.contents"
        :key="index"
        :class="[
          `relative inline-flex items-center cursor-pointer h-[34px] pl-[5px] pr-[28px] border-[length:0.5px] border-solid border-[color:var(--line-1)] box-border`,
          item.language === compose_store.onGetActiveContent().language
            ? `bg-[var(--fill-0)] dark:bg-transparent`
            : `bg-transparent dark:bg-[var(--fill-3)]`,
        ]"
        @click="compose_store.onSetActiveContentItem(item)"
      >
        <SvgIcon name="icon-language" color="var(--text-1)" class="w-[20px] h-[17px]"></SvgIcon>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] flex-1 line-clamp-1 mt-[4px]"
        >
          {{ getLangName(item.language) }}
        </span>
        <div
          class="absolute top-[-6px] right-[-6px] w-[20px] h-[20px] cursor-pointer rounded-[50%] bg-[color:var(--fill-3)]"
          @click.stop="compose_store.onRemoveContentItemByLang(item.language)"
        >
          <SvgIcon name="icon-close" color="var(--text-2)" class="w-full h-full"></SvgIcon>
        </div>
      </div>

      <div
        v-if="can_select_lang_list.length > 0"
        class="border inline-flex min-h-[34px] ml-[6px] items-center justify-center border-dashed border-[color:var(--brand-1)] text-[var(--brand-1)] text-[14px] px-[6px] cursor-pointer"
        @click="
          showLangPop({
            lang: '',
            lang_list: can_select_lang_list,
            onChange(lang: string) {
              compose_store.onAddContentItem(lang);
            },
          })
        "
      >
        + {{ t("add_new_language_version") }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
