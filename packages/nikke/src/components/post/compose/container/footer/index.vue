<script setup lang="ts">
// cpnts
import EmojiPanel from "@/components/common/emoji-panel/index.vue";
import EditorBtns from "@/components/common/editor-btns/index.vue";
import TopicList from "@/components/post/compose/topic-list/index.vue";
import { usePicturePop } from "@/components/common/picture-popup/index.ts";
import { useLinkPop } from "@/components/post/compose/pop/link";
import { useTopicPop } from "@/components/post/compose/pop/topic";
import { useAuthoringStatementPop } from "@/components/post/compose/pop/authoring-statement";
import { Switch } from "@/components/ui/switch";
import TimePicker from "@/components/common/time-picker/index.vue";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { useToast } from "@/components/ui/toast";

// configs
import { STORAGE_LS_POST_COMOPOSE_UNION_CARD_NEW } from "packages/configs/storage";

// types
import { ComposeContentType } from "packages/types/content";
import { PopCallbackValue } from "packages/types/common";
import { Tag } from "packages/types/post";

// utils
import { onMounted, ref } from "vue";
import { useComposePostStore } from "@/store/post/compose";
import { storeToRefs } from "pinia";
import { useUser } from "@/store/user";
import { t } from "@/locales";
import logger from "packages/utils/logger";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useTagStore } from "@/store/tag.store";
import { useUnionStore } from "@/store/union";
import { get } from "lodash-es";
import { useLSLoginMetaStorage, useLSStorage } from "packages/utils/storage";

const debug = logger("[compose:footer]");

let union_card_new = ref("hidden");
const { getOpenID } = useLSLoginMetaStorage();
const { getStorage, setStorage } = useLSStorage();
const { show: toast } = useToast();
const { show: showDialog } = useDialog();
const user_store = useUser();
const { show: showLinkPop, dialog: link_pop_dialog } = useLinkPop();
const { show: showPicturePop, dialog: picture_pop_dialog } = usePicturePop();
const { show: showTopicPop } = useTopicPop();
const { show: showAuthoringStatementPop } = useAuthoringStatementPop();
const tag_store = useTagStore();
const union_store = useUnionStore();
// const { getServerName } = useGameRegion();
const {
  bindRole,
  // makeSureBindRole,
} = useBindRole();

const compose_store = useComposePostStore();
const { state, editor, footer_invisible, emoji_select_panel_visible, emoji_select_panel_style } =
  storeToRefs(compose_store);
const { all_tag_list, recommended_tag_list } = storeToRefs(tag_store);

const { search_tag_list, selected_tag_list, onClearSearchTagName, onTagSearch, onTagChange } =
  compose_store.useComposeTag();

const old_publish_on = ref(Math.floor(Date.now() / 1000));

const onChangeIsScheduleRelease = (bool: boolean) => {
  if (!bool) {
    old_publish_on.value = compose_store.getStateComposeParamsKey("publish_on");
    compose_store.setStateComposeParamsKey("publish_on", 0);
  } else {
    compose_store.setStateComposeParamsKey("publish_on", old_publish_on.value);
  }
};

const onInsertLink = (link_config: { text: string; url: string }) => {
  editor.value.insertLink(`${link_config.text} `, link_config.url);
  link_pop_dialog.value.close();
};

const onInsertImage = (url: string) => {
  editor.value.insertImage(url, "block");
  picture_pop_dialog.value.close();
};

const onEmojiChange = (emoji: string) => {
  // if (!editor.value.quill.isEnabled()) {
  //   editor.value.quill.setSelection(null);
  //   editor.value.quill.enable(true);
  // }
  editor.value.insertImage(emoji, "inline");
  // editor.value.quill.enable(false);
};

const onDeleteEditorText = () => {
  const quill = editor.value?.quill;
  /**
   * @link https://quilljs.com/docs/api#deletetext
   */
  if (quill) {
    // if (!editor.value.quill.isEnabled()) {
    //   editor.value.quill.setSelection(null);
    //   editor.value.quill.enable(true);
    // }
    const range = quill.getSelection(true);
    quill.deleteText(range.index - 1, 1);
    // editor.value.quill.enable(false);
  }
};

const onFriendCardClick = () => {
  const user_game_player_info = user_store.user_game_player_info;
  debug.log("[onFriendCardClick] user_game_player_info: ", user_game_player_info);

  if (!user_game_player_info.area_id) {
    showDialog({
      title: t("account_not_bind_game_role_title"),
      content: t("account_not_bind_game_role_tips"),
      confirm_text: t("account_not_bind_game_role_confirm_btn_text"),
      cancel_text: t("close"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.confirm) {
          close();
          await bindRole({ reload_page: false });
          user_store.onGetUserGamePlayerInfo();
          return;
        }
        close();
      },
    });
    return;
  }

  if (compose_store.state.compose_params.need_friend_card) {
    toast({
      text: t("had_add_friend_card"),
      type: "info",
    });
    return;
  }

  compose_store.setStateComposeParamsKey("need_friend_card", true);

  // 编辑的时候需要更新这个字段
  if (state.value.is_edit) {
    compose_store.setStateComposeParamsKey("need_refresh_friend_card", 1);
  }
};

const onUnionCardClick = async () => {
  // 隐藏 new tag
  const openid = getOpenID();
  if (union_card_new.value !== "hidden" && openid) {
    const key = `${STORAGE_LS_POST_COMOPOSE_UNION_CARD_NEW}_${openid}`;
    union_card_new.value = "hidden";
    setStorage(key, "hidden");
  }
  const union = await union_store.getMyGuildInfo({ latest: true, ignore_toast: false });
  if (!union) {
    toast({ text: t("api_code_1303009"), type: "info" });
    return;
  }
  if (compose_store.state.compose_params.need_guild_card) {
    toast({ text: t("had_add_union_card"), type: "info" });
    return;
  }
  compose_store.setStateComposeParamsKey("need_guild_card", true);

  // 编辑的时候需要更新这个字段
  if (state.value.is_edit) {
    compose_store.setStateComposeParamsKey("need_refresh_guild_card", 1);
  }
};

const onLabelClick = () => {
  const dialog = showTopicPop({
    all_tag_list,
    recommended_tag_list,
    search_tag_list,
    selected_tag_list,
    onChange: onTagChange,
    onSearch: onTagSearch,
    onRemove: compose_store.onTagRemove,
    onClose() {
      onClearSearchTagName();
      dialog.close();
    },
  });
};

const onAuthoringStatementClick = () => {
  const authoring_statement_keys = [
    "creator_statement_type",
    "risk_remind_type",
    "ai_content_type",
    "original_url",
  ];
  const authoring_statement_pop = showAuthoringStatementPop({
    btn_visible: true,
    authoring_statement: authoring_statement_keys.reduce((acc, key: string) => {
      return {
        ...acc,
        [key]: get(compose_store.state.compose_params, key),
      };
    }, {}),
    onConfirm: (new_authoring_statement: any) => {
      authoring_statement_pop.close();
      console.log(`[onConfirm] new_authoring_statement`, new_authoring_statement);

      authoring_statement_keys.forEach((key: any) => {
        compose_store.setStateComposeParamsKey(key, new_authoring_statement[key]);
      });
    },
  });
};

const onUserInfoChange = () => {
  const openid = getOpenID();
  if (openid) {
    const key = `${STORAGE_LS_POST_COMOPOSE_UNION_CARD_NEW}_${openid}`;
    union_card_new.value = getStorage(key) || "visible";
  }
};

onMounted(() => {
  onUserInfoChange();
});
</script>

<template>
  <div v-if="!footer_invisible" class="w-full z-[40]">
    <div
      v-if="user_store.user_can_publish_multiple_language"
      class="h-[38px] px-[12px] flex items-center border-t-[1px] border-[var(--line-1)]"
    >
      <span class="text-[var(--brand-1)] text-[14px] mr-[5px]">
        {{ t("scheduled_release") }}
      </span>
      <div class="flex items-center">
        <Switch
          class="mr-[12px]"
          :checked="Boolean(compose_store.state.compose_params.publish_on)"
          @update:checked="onChangeIsScheduleRelease"
        />
        <TimePicker
          v-if="Boolean(compose_store.state.compose_params.publish_on)"
          v-model="compose_store.state.compose_params.publish_on"
          :ignore_columns="[]"
        ></TimePicker>
      </div>
    </div>

    <!-- 推荐话题栏  -->
    <template v-if="recommended_tag_list.length">
      <TopicList
        :list="
          recommended_tag_list.filter(
            (item: Tag) => !selected_tag_list.some((tag_id: number) => tag_id === item.id),
          )
        "
        @more="onLabelClick"
        @select="onTagChange"
      ></TopicList>
    </template>

    <!-- 正文编辑器 -->
    <EditorBtns
      :link="true"
      :label="true"
      :picture="compose_store.state.compose_params.type === ComposeContentType.richtext"
      :union-card="!!union_store.my_union"
      :union-card-new="union_card_new !== 'hidden'"
      :authoring-statement="true"
      @emoji-click="compose_store.setEmojiSelectPanelVisible(!emoji_select_panel_visible)"
      @picture-click="
        showPicturePop({
          onChange: onInsertImage,
        })
      "
      @link-click="
        showLinkPop({
          onConfirm: onInsertLink,
        })
      "
      @label-click="onLabelClick"
      @friend-card-click="onFriendCardClick"
      @union-card-click="onUnionCardClick"
      @authoring-statement-click="onAuthoringStatementClick"
    ></EditorBtns>

    <!-- 表情面板 -->
    <EmojiPanel
      v-show="emoji_select_panel_visible"
      :panel_style="emoji_select_panel_style"
      @change="onEmojiChange"
      @delete="onDeleteEditorText"
    ></EmojiPanel>
  </div>
</template>

<style lang="scss" scoped></style>
