<template>
  <div class="relative bg-[color:var(--fill-3)] box-border" :class="$attrs.class">
    <slot name="header"></slot>
    <div class="relative w-full">
      <div
        class="w-full font-normal text-[length:13px] leading-[16px] text-[color:var(--text-1)] invisible pointer-events-none whitespace-pre-wrap break-words"
        :style="{ [heightProperty]: textareaHeight }"
      >
        {{ text + "\n" }}
      </div>
      <textarea
        ref="textarea_ref"
        class="absolute top-0 outline-none left-0 h-full w-full font-normal text-[length:13px] leading-[16px] appearance-none resize-none m-0 p-0 bg-none bg-[color:transparent] placeholder:text-[color:var(--text-3)] whitespace-pre-wrap break-words"
        :class="computed_class"
        :placeholder="placeholder"
        :value="text"
        @input="handleInput"
        @focus="onFocus"
        @blur="onBlur"
      ></textarea>
    </div>
    <span
      v-show="text.length"
      class="absolute font-normal text-[length:12px] leading-[14px] text-[var(--brand-1)] cursor-pointer"
      :class="computed_clear_btn_pos"
      @click="clear"
    >
      {{ t("clear_all") }}
    </span>
  </div>
  <div
    v-if="type === 'link' && invalid"
    class="font-normal text-[length:12px] leading-[14px] text-[color:var(--error)] mt-[6px]"
  >
    {{ invalidText }}
  </div>
</template>

<script setup lang="ts">
// utils
import { ref, computed } from "vue";
import { t } from "@/locales";

const props = withDefaults(
  defineProps<{
    type?: "desc" | "link";
    placeholder?: string;
    content?: string;
    clearBtnPosition?: "top" | "bottom";
    heightProperty?: "height" | "minHeight";
    textareaHeight?: string;
    invalidText?: string;
    invalid?: boolean;
  }>(),
  {
    type: "desc",
    placeholder: "",
    content: "",
    clearBtnPosition: "bottom",
    heightProperty: "height",
    textareaHeight: "auto",
    invalidText: "Link is invalid",
    invalid: false,
  },
);

const emits = defineEmits(["change", "update:content", "focus", "blur"]);

const text = ref(props.content);
const textarea_ref = ref<HTMLTextAreaElement | null>(null);

const computed_class = computed(() => {
  if (props.type === "link") {
    if (props.invalid) {
      return "text-[color:var(--text-3)]";
    }
    return "text-[color:var(--brand-1)]";
  }
  return "text-[color:var(--text-1)]";
});

const computed_clear_btn_pos = computed(() => {
  if (props.clearBtnPosition === "top") {
    return "top-[10px] right-[20px]";
  } else {
    return "bottom-[10px] right-[10px]";
  }
});

const onFocus = () => {
  emits("focus");
};

const onBlur = () => {
  emits("blur");
};

const handleInput = (e: Event) => {
  const value = (e.target as HTMLTextAreaElement)?.value;
  text.value = value ? value : "";
  emits("update:content", text.value);
  emits("change", text.value);
};

const clear = () => {
  text.value = "";
  emits("update:content", text.value);
  emits("change", "");
};
</script>

<style lang="scss" scoped></style>
