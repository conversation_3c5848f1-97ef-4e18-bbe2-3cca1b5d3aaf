// cpnts
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Button from "@/components/ui/button/index.vue";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/common/checkbox/index";
// import { useToast } from "@/components/ui/toast";

// types
import {
  ComposeNewAuthoringStatement,
  ComposeNewAuthoringStatementAIContentType,
  ComposeNewAuthoringStatementRiskRemindType,
  ComposeNewAuthoringStatementType,
} from "packages/types/post";

// utils
import { computed, defineComponent, Ref, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { isValidURL } from "packages/utils/tools";

interface SubCategoryItem {
  title: string;
  value: number;
  checked: boolean;
}

interface UpdateSubCategoriesCheckedParams {
  list: SubCategoryItem[];
  target_item: SubCategoryItem;
  bool: boolean;
  is_multi: boolean;
}

interface ListItem {
  type: string;
  title: string;
  checked: boolean;
  sub_categories: any;
  initChecked: () => void;
  checkedSubCategoriesSlot: () => void;
  uncheckedSubCategoriesSlot: () => void;
  onUpdateSubCategoriesChecked: (options: UpdateSubCategoriesCheckedParams) => void;
  onChangeChecked: (bool: boolean) => void;
}

const getDefaultAuthoringStatement = (): ComposeNewAuthoringStatement => {
  return {
    creator_statement_type: ComposeNewAuthoringStatementType.no,
    risk_remind_type: ComposeNewAuthoringStatementRiskRemindType.no,
    ai_content_type: ComposeNewAuthoringStatementAIContentType.no,
    original_url: "",
  };
};

const isSomeChecked = (sub_catetories: SubCategoryItem[]) =>
  sub_catetories.some((sub_item: SubCategoryItem) => sub_item.checked);
const isAllChecked = (sub_catetories: SubCategoryItem[]) =>
  sub_catetories.every((sub_item: SubCategoryItem) => sub_item.checked);

export default defineComponent({
  props: {
    show: Boolean,
    btn_visible: Boolean,
    authoring_statement: {
      type: Object,
      required: true,
    },
  },
  emits: ["close", "confirm"],
  setup(props, { emit }) {
    const { t } = useI18n();
    // const { show: toast } = useToast();

    const original_url = ref(props.authoring_statement.original_url);

    const creator_statement_sub_catetories: Ref<SubCategoryItem[]> = ref([
      {
        title: t("authoring_statement_category1_2"),
        value: ComposeNewAuthoringStatementType.repost_prohibited,
        checked: false,
      },
      {
        title: t("authoring_statement_category1_3"),
        value: ComposeNewAuthoringStatementType.repost_allowed,
        checked: false,
      },
    ]);

    const risk_remind_sub_catetories: Ref<SubCategoryItem[]> = ref([
      {
        title: t("authoring_statement_category2_1"),
        value: ComposeNewAuthoringStatementRiskRemindType.reveal_the_plot_risk,
        checked: false,
      },
      {
        title: t("authoring_statement_category2_2"),
        value: ComposeNewAuthoringStatementRiskRemindType.risk_content,
        checked: false,
      },
    ]);

    const is_invalid_url = computed(() => {
      if (original_url.value) {
        return !isValidURL(original_url.value);
      }
      return false;
    });

    const can_confirm = computed(() => {
      if (is_invalid_url.value) {
        return false;
      }
      return true;
    });

    const onUpdateSubCategoriesChecked = (options: {
      list: SubCategoryItem[];
      target_item: SubCategoryItem;
      bool: boolean;
      is_multi: boolean;
    }) => {
      // cancel
      if (!options.bool) {
        options.target_item.checked = false;
        return;
      }

      // select
      // is multi
      if (options.is_multi) {
        options.target_item.checked = true;
        return;
      }

      options.list.forEach((item: SubCategoryItem) => {
        item.checked = item.value === options.target_item.value;
      });
    };

    const renderCheckedSubCategoriesSlot = (
      sub_categories: SubCategoryItem[],
      onUpdateSubCategoriesChecked: ListItem["onUpdateSubCategoriesChecked"],
      options?: Partial<UpdateSubCategoriesCheckedParams>,
    ) => {
      return (
        <>
          <ul class="flex flex-col">
            {sub_categories.map((item: SubCategoryItem, index: number) => {
              return (
                <li class="flex justify-between items-center pl-[8px] my-[12px]" key={index}>
                  <span class="text-[var(--text-1)] text-[12px]">{item.title}</span>
                  <Checkbox
                    modelValue={item.checked}
                    onChange={(bool: boolean) =>
                      onUpdateSubCategoriesChecked({
                        list: sub_categories,
                        target_item: item,
                        bool: bool,
                        is_multi: false,
                        ...options,
                      })
                    }
                  ></Checkbox>
                </li>
              );
            })}
          </ul>
          <i class="line"></i>
        </>
      );
    };

    const list: Ref<ListItem[]> = ref([
      {
        type: "creator_statement_type",
        title: t("authoring_statement_category1"),
        checked: false as boolean,
        sub_categories: creator_statement_sub_catetories.value,
        initChecked() {
          this.checked = [
            ComposeNewAuthoringStatementType.repost_prohibited,
            ComposeNewAuthoringStatementType.repost_allowed,
          ].includes(props.authoring_statement.creator_statement_type);
        },
        onUpdateSubCategoriesChecked: onUpdateSubCategoriesChecked,
        checkedSubCategoriesSlot() {
          return (
            <>
              {renderCheckedSubCategoriesSlot(
                this.sub_categories,
                this.onUpdateSubCategoriesChecked,
              )}
            </>
          );
        },
        uncheckedSubCategoriesSlot() {
          return (
            <div class="flex flex-col w-full">
              <input
                type="text"
                placeholder={t("authoring_statement_category1_1")}
                v-model={original_url.value}
                class="w-full h-[34px] cursor-pointer flex items-center rounded-[1px] bg-[color:var(--fill-3)] font-normal text-[13px] px-[12px] box-border justify-start text-[color:var(--text-3)]"
              />
              {is_invalid_url.value && (
                <div class="font-normal px-[12px] text-[length:12px] leading-[14px] text-[color:var(--error)] mt-[6px]">
                  {t("invalid_link")}
                </div>
              )}
            </div>
          );
        },
        onChangeChecked(bool: boolean) {
          this.checked = bool;
          // clear
          if (this.checked) {
            original_url.value = "";
          }
        },
      },
      {
        type: "risk_remind_type",
        title: t("authoring_statement_category2"),
        checked: false as boolean,
        sub_categories: risk_remind_sub_catetories.value,
        initChecked() {
          this.checked = Boolean(
            ComposeNewAuthoringStatementRiskRemindType.all &
              props.authoring_statement.risk_remind_type,
          );
        },
        onUpdateSubCategoriesChecked: onUpdateSubCategoriesChecked,
        checkedSubCategoriesSlot() {
          return (
            <>
              {renderCheckedSubCategoriesSlot(
                this.sub_categories,
                this.onUpdateSubCategoriesChecked,
                {
                  is_multi: true,
                },
              )}
            </>
          );
        },
        uncheckedSubCategoriesSlot() {},
        onChangeChecked(bool: boolean) {
          this.checked = bool;
        },
      },
      {
        type: "ai_content_type",
        title: t("authoring_statement_category3"),
        checked: false as boolean,
        sub_categories: ref([]),
        initChecked() {
          this.checked = [ComposeNewAuthoringStatementAIContentType.yes].includes(
            props.authoring_statement.ai_content_type,
          );
        },
        checkedSubCategoriesSlot() {},
        uncheckedSubCategoriesSlot() {},
        onUpdateSubCategoriesChecked() {},
        onChangeChecked(bool: boolean) {
          this.checked = bool;
        },
      },
    ]);

    const renderHeader = () => (
      <>
        <div class="flex text-[16px] font-bold">{t("authoring_statement")}</div>
        <i class="block w-full h-[0.5px] bg-[color:var(--fill-7)] my-[16px]"></i>
      </>
    );

    const renderList = () => (
      <>
        {list.value.map((item: ListItem, index: number) => {
          return (
            <>
              <div class="flex flex-col" key={index}>
                <div class="flex justify-between items-center mb-[8px]">
                  <span class="text-[var(--text-1)] text-[14px] font-bold">{item.title}</span>
                  <Switch
                    checked={item.checked}
                    onUpdate:checked={item.onChangeChecked.bind(item)}
                  ></Switch>
                </div>
                {item.checked ? item.checkedSubCategoriesSlot() : item.uncheckedSubCategoriesSlot()}
              </div>
              <i class="block w-full h-[0.5px] bg-[color:var(--fill-7)] my-[16px]"></i>
            </>
          );
        })}
      </>
    );

    const renderBtns = () =>
      props.btn_visible && (
        <>
          <Button
            class="mb-[6px] font-bold"
            type="primary"
            disabled={!can_confirm.value}
            onClick={onConfirm}
          >
            {t("confirm")}
          </Button>

          <Button onClick={onCancel}>{t("cancel")}</Button>
        </>
      );

    const checkParams = (authoring_statement: ComposeNewAuthoringStatement) => {
      let bool = true;

      if (
        [
          ComposeNewAuthoringStatementType.no,
          ComposeNewAuthoringStatementType.transport_content,
        ].includes(authoring_statement.creator_statement_type) &&
        authoring_statement.original_url &&
        is_invalid_url.value
      ) {
        bool = false;

        // toast({
        //   text: t("invalid_link"),
        //   type: "error",
        // });
      }

      console.log(`[checkParams] bool`, bool);

      return bool;
    };

    const onClose = () => {
      emit("close");
    };

    const onConfirm = () => {
      const authoring_statement = toDataTransform();
      checkParams(authoring_statement);
      emit("confirm", authoring_statement);
    };

    const onCancel = () => {
      onClose();
    };

    const fromDataTransform = () => {
      list.value.forEach((item: ListItem) => item.initChecked());
      original_url.value = props.authoring_statement.original_url;
      creator_statement_sub_catetories.value.forEach((item: SubCategoryItem) => {
        item.checked = item.value === props.authoring_statement.creator_statement_type;
      });
      risk_remind_sub_catetories.value.forEach((item: SubCategoryItem) => {
        item.checked = Boolean(item.value & props.authoring_statement.risk_remind_type);
      });
    };

    const toDataTransform = (): ComposeNewAuthoringStatement => {
      const authoring_statement = getDefaultAuthoringStatement();

      authoring_statement.original_url = original_url.value;
      list.value.forEach((item: ListItem) => {
        switch (item.type) {
          case "ai_content_type":
            authoring_statement.ai_content_type = item.checked
              ? ComposeNewAuthoringStatementAIContentType.yes
              : ComposeNewAuthoringStatementAIContentType.no;
            break;

          case "creator_statement_type":
            if (!item.checked) {
              authoring_statement.creator_statement_type = authoring_statement.original_url
                ? ComposeNewAuthoringStatementType.transport_content
                : ComposeNewAuthoringStatementType.no;
            } else {
              // 清空
              authoring_statement.original_url = "";
              // 是否有勾选
              if (isSomeChecked(creator_statement_sub_catetories.value)) {
                creator_statement_sub_catetories.value.forEach((sub_item: SubCategoryItem) => {
                  if (sub_item.checked) {
                    authoring_statement.creator_statement_type = sub_item.value;
                  }
                });
              } else {
                // 没有勾选，则为无声明
                authoring_statement.creator_statement_type = ComposeNewAuthoringStatementType.no;
              }
            }
            break;

          case "risk_remind_type":
            if (!item.checked) {
              authoring_statement.risk_remind_type = ComposeNewAuthoringStatementRiskRemindType.no;
            } else {
              if (isAllChecked(risk_remind_sub_catetories.value)) {
                authoring_statement.risk_remind_type =
                  ComposeNewAuthoringStatementRiskRemindType.all;
                return;
              }

              risk_remind_sub_catetories.value.forEach((sub_item: SubCategoryItem) => {
                if (sub_item.checked) {
                  authoring_statement.risk_remind_type =
                    authoring_statement.risk_remind_type | sub_item.value;
                }
              });
            }
            break;

          default:
        }
      });

      return authoring_statement;
    };

    watch(
      () => props.authoring_statement,
      () => {
        // console.log("props.authoring_statement", props.authoring_statement);
        fromDataTransform();
      },
      {
        immediate: true,
        deep: true,
      },
    );

    return () => (
      <CommBottomPopup show={props.show} onClose={onClose}>
        <div class="p-[20px]">
          {renderHeader()}
          {renderList()}
          {renderBtns()}
        </div>
      </CommBottomPopup>
    );
  },
});
