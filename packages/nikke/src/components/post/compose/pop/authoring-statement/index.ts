// cpnts
import AuthoringStatementPop from "./authoring-statement.tsx";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";
import { ref } from "vue";

export const useAuthoringStatementPop = () => {
  const dialog: any = ref();
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return (dialog.value = showDialog(
      AuthoringStatementPop,
      Object.assign(options, {
        show: true,
        onClose:
          options.onClose ??
          (() => {
            dialog.value.unmount();
          }),
      }),
    ));
  };
  return {
    show,
    dialog,
  };
};
