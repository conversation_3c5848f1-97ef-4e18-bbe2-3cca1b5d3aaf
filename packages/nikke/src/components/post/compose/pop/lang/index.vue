<script setup lang="ts">
// cpnts
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";

// utils
import { t } from "@/locales";

defineProps<{
  show: boolean;
  lang: number;
  lang_list: any[];
}>();

const emits = defineEmits(["close", "change"]);

const onChangeLang = (type: number) => {
  emits("change", type);
  emits("close");
};
</script>

<template>
  <!-- 选择发布区域弹窗 -->
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div
        class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)] p-[20px] pb-[14px]"
      >
        {{ t("select_publishing_lang") }}
      </div>
    </template>
    <ul
      class="grid grid-cols-[repeat(3,minmax(103px,1fr))] justify-items-center text-center gap-x-[12px] gap-y-[14px] px-[20px] pb-[35px]"
    >
      <li
        v-for="(item, index) in lang_list"
        :key="index"
        class="w-full font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] border-[length:0.5px] border-solid py-[5px] cursor-pointer"
        :class="[
          lang === item.value
            ? 'bg-[color:var(--brand-2)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)]'
            : 'bg-[color:var(--fill-3)] border-[color:var(--line-1)]',
        ]"
        @click="onChangeLang(item.value)"
      >
        <span class="pt-[2px] inline-block">{{ item.name }}</span>
      </li>
    </ul>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
