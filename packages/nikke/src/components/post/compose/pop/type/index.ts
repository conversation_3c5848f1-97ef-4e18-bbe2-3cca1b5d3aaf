// cpnts
import TypePop from "./index.vue";
// types
import { PopCallbackValue } from "packages/types/common";
// utils
import { showDialog } from "@/utils/dialog";
import { ref } from "vue";

export const useTypePop = () => {
  const dialog: any = ref();
  const show = (
    options: any & { callback: (options: { value: PopCallbackValue; close: Function }) => void },
  ) => {
    return (dialog.value = showDialog(
      TypePop,
      Object.assign(options, {
        show: true,
        onClose: () => {
          dialog.value.unmount();
        },
      }),
    ));
  };
  return {
    show,
    dialog,
  };
};
