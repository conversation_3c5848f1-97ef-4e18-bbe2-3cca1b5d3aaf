<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";

// utils
import { t } from "@/locales";

defineProps<{
  show: boolean;
  type: number;
  type_list: any[];
}>();

const emits = defineEmits(["close", "change"]);

const onChangeType = (type: number) => {
  emits("change", type);
  emits("close");
};
</script>

<template>
  <!-- 选择发布内容类型弹窗 -->
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div
        class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)] p-[20px] pb-[14px]"
      >
        {{ t("select_publishing_section") }}
      </div>
    </template>
    <div class="px-[20px] pb-[35px]">
      <div
        v-for="(item, index) in type_list"
        :key="index"
        class="flex items-center py-[10px] px-[20px] border-[length:0.5px] border-solid cursor-pointer mt-[14px] first:mt-0"
        :class="[
          type === item.value
            ? 'bg-[color:var(--brand-2)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)]'
            : 'bg-[color:var(--fill-3)] border-[color:var(--line-1)]',
        ]"
        @click="onChangeType(item.value)"
      >
        <SvgIcon :name="item.icon" color="var(--text-1)" class="w-[40px] h-[40px]"></SvgIcon>
        <span
          class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)] ml-[40px]"
        >
          {{ item.title }}
        </span>
      </div>
    </div>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
