<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import Button from "@/components/ui/button/index.vue";
import Topics from "@/components/post/compose/container/body/topics/index.vue";

// utils
import { useI18n } from "vue-i18n";
import { Tag } from "packages/types/post";
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import { useCompositionEvent } from "@/composables/use-composition-event";
import { debounce } from "lodash-es";

const props = defineProps<{
  show: boolean;
  all_tag_list: Tag[];
  recommended_tag_list: Tag[];
  search_tag_list: Tag[];
  selected_tag_list: number[];
  focus?: boolean;
  btn_visible?: boolean;
}>();

const emits = defineEmits(["close", "change", "search", "remove", "confirm"]);

const { composition, onAddCompositionsEvent, onRemoveCompositionsEvent } = useCompositionEvent();
const { t } = useI18n();

const input_ref = ref<HTMLTextAreaElement | null>();
const search_tag_name = ref();

const tag_list = computed(() => {
  const list = search_tag_name.value ? props.search_tag_list : props.recommended_tag_list;

  return list;
});

const isTagSelected = (tag: Tag) => {
  return props.selected_tag_list.some((tag_id) => tag_id === tag.id);
};

const onInput = debounce(() => {
  if (composition.value) {
    return;
  }
  emits("search", search_tag_name.value);
}, 200);

const onAssignInputRef = (el: any) => {
  if (input_ref.value) {
    return;
  }
  nextTick(() => {
    input_ref.value = el;
    onAddCompositionsEvent(input_ref.value as HTMLElement);
  });
};

const onFocus = () => {
  if (props.focus) {
    nextTick(() => {
      input_ref.value?.focus();
    });
  }
};

const onClose = () => {
  emits("close");
};

const onConfirm = () => {
  emits("confirm");
};

const onCancel = () => {
  onClose();
};

const onRemove = (index: number) => {
  emits("remove", index);
};

onMounted(() => {
  onFocus();
});

onUnmounted(() => {
  onRemoveCompositionsEvent(input_ref.value as HTMLElement);
});
</script>

<template>
  <CommBottomPopup :show="show" @close="onClose">
    <div class="p-[20px]">
      <div class="flex text-[16px] font-normal">{{ t("hashtag_manage") }}</div>
      <Topics
        :selected_tag_list="selected_tag_list"
        :all_tag_list="all_tag_list"
        :recommended_tag_list="recommended_tag_list"
        class="max-h-[20vh] overflow-y-auto"
        :empty_text="t('no_hashtag_for_the_post')"
        @remove="onRemove"
      ></Topics>

      <div
        class="flex items-center h-[42px] mb-[8px] dark:bg-[color:var(--fill-3)] border-[length:0.5px] border-solid border-[color:var(--line-1)] p-[4px]"
      >
        <SvgIcon
          name="icon-search"
          color="var(--color-3)"
          class="w-[16px] h-[16px] flex-shrink-0 ml-[2px] mr-[5px]"
        ></SvgIcon>

        <input
          :ref="onAssignInputRef"
          v-model="search_tag_name"
          type="text"
          class="flex-1 h-full font-normal text-[length:13px] leading-[16px] text-[color:var(--text-1)] placeholder:text-[color:var(--color-3)] appearance-none m-0 p-0 bg-none bg-[color:transparent]"
          :placeholder="t('search_more_topics')"
          @input="onInput"
        />

        <div
          v-show="search_tag_name"
          :class="[
            'w-[16px] h-[16px] relative flex-shrink-0 bg-[var(--color-4)] dark:bg-[var(--fill-1)] ml-[4px] cursor-pointer rounded-full flex justify-center items-center',
          ]"
          @click="() => (search_tag_name = '')"
        >
          <i class="absolute-center !w-[30px] !h-[30px]"></i>
          <SvgIcon name="icon-close3" color="var(--fill-0)"></SvgIcon>
        </div>
      </div>

      <ul v-if="tag_list.length" class="h-[30vh] overflow-y-auto">
        <li
          v-for="(item, index) in tag_list"
          :key="index"
          :class="[
            `font-normal text-[length:13px] leading-[16px] text-[color:var(--text-1)] py-[8px] px-[8px] mt-[4px] first:mt-0`,
            isTagSelected(item) ? '!text-[color:var(--text-4)]' : 'cursor-pointer',
          ]"
          @click="isTagSelected(item) ? null : emits('change', item)"
        >
          #{{ item.name }}
        </li>
      </ul>

      <div v-else-if="search_tag_name" class="flex items-center justify-center h-[30vh] w-full">
        <span class="font-normal text-[length:13px] leading-[16px] text-[color:var(--text-3)]">
          {{ t("could_not_find_any_results") }}
        </span>
      </div>

      <i class="block w-full h-[0.5px] bg-[color:var(--fill-7)] my-[16px]"></i>

      <template v-if="btn_visible">
        <Button class="mb-[6px] font-bold" type="primary" @click="onConfirm">
          {{ t("confirm") }}
        </Button>

        <Button @click="onCancel">
          {{ t("cancel") }}
        </Button>
      </template>
    </div>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
