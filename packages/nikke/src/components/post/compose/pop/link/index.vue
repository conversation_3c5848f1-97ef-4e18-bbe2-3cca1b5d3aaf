<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import LinkInput from "@/components/post/compose/link-input/index.vue";

// utils
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { isValidURL } from "packages/utils/tools";

defineProps<{
  show: boolean;
}>();
const emits = defineEmits(["close", "confirm"]);

const { t } = useI18n();

const link_config = ref({
  text: "", //"qq.com",
  url: "", //"https://qq.com",
});

const is_disabled = computed(() => !link_config.value.url || is_invalid_url.value);

const is_invalid_url = computed(() => {
  if (link_config.value.url) {
    return !isValidURL(link_config.value.url);
  }
  return false;
});

const onConfirm = () => {
  if (!is_disabled.value) {
    emits("confirm", {
      text: link_config.value.text || link_config.value.url,
      url: link_config.value.url,
    });
  }
};
</script>

<template>
  <!-- 插入链接弹窗 -->
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div class="flex items-center justify-between p-[20px] pb-[14px]">
        <span class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)]">
          {{ t("insert_link") }}
        </span>
        <span
          class="text-[length:14px] leading-[16px]"
          :class="[
            !is_disabled
              ? 'font-bold text-[var(--brand-1)] cursor-pointer'
              : 'font-normal text-[color:var(--text-3)]',
          ]"
          @click="onConfirm"
        >
          {{ t("confirm") }}
        </span>
      </div>
    </template>
    <div class="px-[20px] mb-[35px]">
      <LinkInput
        type="desc"
        :placeholder="t('editor_default_placeholder')"
        clear-btn-position="top"
        textarea-height="40px"
        class="border-[length:0.5px] border-solid border-[color:var(--line-1)] px-[20px] py-[9px]"
        :content="link_config.text"
        @change="(value: string) => (link_config.text = value)"
      >
        <template #header>
          <div class="flex items-center mb-[8px]">
            <span class="flex items-center justify-center w-[12px] h-[12px] ml-[-1px]">
              <SvgIcon name="icon-text" color="var(--text-1)" class="w-[9px] h-[9px]"></SvgIcon>
            </span>
            <span
              class="font-normal text-[length:12px] leading-[14px] text-[color:var(--text-2)] ml-[5px]"
            >
              {{ t("description_optional") }}
            </span>
          </div>
        </template>
      </LinkInput>

      <LinkInput
        v-model:content="link_config.url"
        type="link"
        placeholder="Please paste the link address here"
        clear-btn-position="top"
        textarea-height="40px"
        class="border-[length:0.5px] border-solid border-[color:var(--line-1)] px-[20px] py-[9px] mt-[14px]"
        :invalid="is_invalid_url"
        :invalid-text="t('invalid_link')"
      >
        <template #header>
          <div class="flex items-center mb-[8px]">
            <span class="flex items-center justify-center w-[12px] h-[12px] ml-[-1px]">
              <SvgIcon name="icon-link" color="var(--text-1)" class="w-[12px] h-[12px]"></SvgIcon>
            </span>
            <span
              class="font-normal text-[length:12px] leading-[14px] text-[color:var(--text-2)] ml-[5px]"
            >
              {{ t("link_required") }}
            </span>
          </div>
        </template>
      </LinkInput>
    </div>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
