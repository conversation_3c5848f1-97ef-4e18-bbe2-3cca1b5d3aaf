<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";

// utils
import { t } from "@/locales";

defineProps<{
  show: boolean;
  plat_id: number;
  plat_list: any[];
}>();

const emits = defineEmits(["close", "change"]);

const onChangePlat = (id: number) => {
  emits("change", id);
  emits("close");
};
</script>

<template>
  <!-- 选择发布板块弹窗 -->
  <CommBottomPopup :show="show" @close="emits('close')">
    <template #header>
      <div
        class="font-bold text-[length:16px] leading-[19px] text-[color:var(--text-1)] p-[20px] pb-[14px]"
      >
        {{ t("select_publishing_section") }}
      </div>
    </template>

    <div v-if="plat_list.length" class="px-[20px] pb-[35px]">
      <div
        v-for="(item, index) in plat_list"
        :key="index"
        class="flex items-center py-[10px] px-[20px] border-[length:0.5px] border-solid cursor-pointer mt-[14px] first:mt-0"
        :class="[
          plat_id === item.id
            ? 'bg-[color:var(--brand-2)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)]'
            : 'bg-[color:var(--fill-3)] border-[color:var(--line-1)]',
        ]"
        @click="onChangePlat(item.id)"
      >
        <SvgIcon
          v-if="item.icon"
          :name="item.icon"
          color="var(--text-1)"
          class="w-[40px] h-[40px]"
        ></SvgIcon>
        <div class="flex flex-col ml-[40px]">
          <span class="font-normal text-[length:14px] leading-[16px] text-[color:var(--text-1)]">
            {{ item.title }}
          </span>
          <span
            class="font-normal text-[length:11px] leading-[13px] text-[color:var(--text-3)] mt-[6px]"
          >
            {{ item.desc }}
          </span>
        </div>
      </div>
    </div>
  </CommBottomPopup>
</template>

<style lang="scss" scoped></style>
