<template>
  <div
    class="h-[45px] bg-left bg-cover bg-no-repeat bg-white flex items-center justify-between px-[16px] py-[12.5px] cursor-pointer"
    :style="{ backgroundImage: `url(${creatorhub_entry_bg})` }"
    @click="router.push(Routes.CREATOR_HUB)"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="94.5"
      height="16"
      viewBox="0 0 189 32"
      fill="none"
    >
      <path
        d="M82.5 4C83 4 82 8 81 8H75.5V14H81L79.5 19H75.5V24H82.5C83.5 24 82 29 81 29H72C71 29 71 28 71 28V5C71 5 71 4 72 4H82.5Z"
        fill="#141416"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M57 4H67.5C68.5 4 68.5 5.5 68.5 5.5V17.5C68.5 18 66.5 19 66.5 19L69.5 29H64.5L63 21H61.5V29H57V4ZM64.5 8.5H61V16.7521L64.5 15V8.5Z"
        fill="#141416"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M127 4H137.5C138.5 4 138.5 5.5 138.5 5.5V17.5C138.5 18 136.5 19 136.5 19L139.5 29H134.5L133 21H131.5V29H127V4ZM134.5 8.5H131V16.7521L134.5 15V8.5Z"
        fill="#141416"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M88 4L84 29H88.5L89 25H92.5L93 29H97.5L93.5 4H88ZM89.5 20.5L90.5 14.5H91L92 20.5H89.5Z"
        fill="#141416"
      />
      <path d="M103 8V29H107V8H111L112.5 4H99V8H103Z" fill="#141416" />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M114 4V29H125.5V4H114ZM118.5 24.5V8.5H121.5V24.5H118.5Z"
        fill="#141416"
      />
      <path d="M163 29V4H167.5V24.5H171V4H175.5V29H163Z" fill="#141416" />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M187.747 4C187 4 177 4 177 4V29C177 29 186.994 29 187.747 29C188.5 29 189 28 189 27.5C189 27 189 17.5 189 17.5L187.747 17L189 16C189 16 189 6 189 5.5C189 5 188.494 4 187.747 4ZM181.5 19H185V25H181.5V19ZM185 8.5H181.5V14.5H185V8.5Z"
        fill="#141416"
      />
      <path
        d="M55.1515 4C55.6799 4 54.6232 8.08163 53.5666 8.08163H47.2266V24.4082H55.1515C56.2082 24.4082 54.6232 29 53.5666 29H44.0567C43 29 43 27.9796 43 27.9796V5.02041C43 5.02041 43 4 44.0567 4H55.1515Z"
        fill="#141416"
      />
      <rect width="32" height="32" rx="6" fill="#141416" />
      <path
        d="M14.7491 7C15.1295 7 14.3687 9.93878 13.6079 9.93878H9.04317V21.6939H14.7491C15.5099 21.6939 14.3687 25 13.6079 25H6.76079C6 25 6 24.2653 6 24.2653V7.73469C6 7.73469 6 7 6.76079 7H14.7491Z"
        fill="white"
      />
      <path
        d="M16 25V7H19.3061V14.3469H22.9796V7H26.2857V25H22.9796V17.6531H19.3061V25H16Z"
        fill="white"
      />
      <path
        d="M147 29V4H151.592V14.2041H156.694V4H161.286V29H156.694V18.7959H151.592V29H147Z"
        fill="#141416"
      />
    </svg>
    <div class="flex items-center gap-[4px]">
      <div class="text-[var(--text-1)] text-[11px] font-[Inter] flex-shrink-0">
        {{ t("view") }}
      </div>
      <div class="w-[12px] h-[12px] flex items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="3.5"
          height="7"
          viewBox="0 0 7 14"
          fill="none"
        >
          <path
            d="M0 0.759899V3.31951C0.00078392 3.4008 0.0384359 3.47719 0.102071 3.5266L3.84296 6.99637L0.106153 10.4589C0.0425186 10.5083 0.0048666 10.5847 0.00408268 10.666V13.236C0.00576496 13.3831 0.124419 13.5011 0.269466 13.5H1.40449C1.45958 13.5003 1.51341 13.4833 1.55862 13.4513L6.89793 8.50294C6.96193 8.45384 6.99968 8.37726 7 8.29585V5.70725C6.99968 5.62584 6.96193 5.54926 6.89793 5.50016L1.56372 0.548669C1.51851 0.51673 1.46468 0.499733 1.40959 0.500003H0.263342C0.120647 0.49995 0.00387144 0.615198 0 0.759899Z"
            fill="#141416"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import creatorhub_entry_bg from "@/assets/imgs/creatorhub/entry-bg.png";
import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";

const router = useRouter();
</script>
