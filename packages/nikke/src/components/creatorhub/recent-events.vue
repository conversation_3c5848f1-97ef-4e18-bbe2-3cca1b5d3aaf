<template>
  <div v-if="list.length" class="mt-[20px]">
    <div class="text-[18px] leading-[22px] font-[700] text-[color:var(--text-1)] mb-[10px]">
      {{ t("recent_events") }}
    </div>

    <HorizontalInfiniteScroll
      :empty="empty"
      :finished="finished"
      :loading="loading"
      :loading_visible="list.length > 0"
      :finished_visible="true"
      scroll_container="self"
      class="flex gap-[8px] bg-[var(--fill-0)] px-[16px] py-[11px] overflow-x-auto"
      @load-more="load"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        class="relative aspect-[202/160] w-[calc((min(100vw,480px)-72px)/3)] bg-[var(--color-5)] rounded-[3px] overflow-hidden flex-none bg-contain bg-center bg-no-repeat"
        :class="{ 'cursor-pointer': item.task_url }"
        :style="{
          backgroundImage: item.image_url ? `url(${item.image_url})` : undefined,
        }"
        @click="handleRouterToEvent(item)"
      >
        <div
          v-if="dayjs.unix(item.end_time ?? 0).isAfter(dayjs())"
          class="absolute top-[0] right-[0] h-[15px] p-[4px] event-tag flex items-center justify-center"
        >
          <span class="text-[9px]">{{ t("on_going") }}</span>
        </div>
      </div>
    </HorizontalInfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { t } from "@/locales";
import { useWebCredential } from "@/composables/use-webcredential";
import { EventTaskInfo, useGetRecentEvents } from "@/api/creatorhub";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { HorizontalInfiniteScroll } from "@/components/common/scroll/index";
import { getStandardizedLang } from "packages/utils/standard";
import { CREATORHUB_LANG_MAP } from "packages/configs/creatorhub";

const { list, empty, finished, load, loading } = useInfiniteList({
  queryFn: async ({ limit, offset }) => {
    const res = await useGetRecentEvents.run({ limit, offset });
    return { list: res.task_list, is_finish: res.next_offset === 0 };
  },
  item_key: (i) => i.name + i.task_url,
});

const { openUrlWithAuth } = useWebCredential();

const handleRouterToEvent = (item: Partial<EventTaskInfo>) => {
  if (!item.task_url) return;
  // 加上网红的 lang 参数
  try {
    const url = new URL(item.task_url);
    const lang = getStandardizedLang();
    const creatorhub_lang = CREATORHUB_LANG_MAP[lang as keyof typeof CREATORHUB_LANG_MAP] || "en";
    url.searchParams.set("lang", creatorhub_lang);
    openUrlWithAuth(url.toString());
  } catch (error) {
    openUrlWithAuth(item.task_url);
  }
};
</script>

<style lang="scss" scoped>
.event-tag {
  background: linear-gradient(90deg, #ffba49 0%, #fc6a37 100%);
  border-bottom-left-radius: 6px;
}
</style>
