<template>
  <div
    class="pl-[16px] pr-[12px] py-[12px] flex justify-btween items-center bg-[color:var(--other-5-1)] dark:bg-[color:var(--other-5-30)] border-[1px] border-[color:var(--other-5-1)] dark:border-[color:var(--other-5-10)]"
  >
    <div class="flex-1">
      <div
        class="font-medium leading-[16px] line-clamp-3 text-[color:var(--text-1)] text-[length:13px]"
      >
        {{ renderText() }}
      </div>
      <div class="mt-[8px] text-[length:9px] leading-[11px] text-[color:var(--text-3)]">
        {{ dayjs(+item.create_time * 1000).format("MM/DD/YYYY HH:mm:ss") }}
      </div>
    </div>
    <div class="flex items-center justify-end ml-[16px] h-[20px]">
      <div
        class="text-[color:var(--other-6)] dark:text-[color:var(--other-2)] min-w-[46px] text-[length:20px] leading-[1] font-[DINNextLTProBold] mt-[6px]"
      >
        {{ getDisplayPoints() }}
      </div>
      <div
        v-if="item.is_details"
        class="w-[16px] h-[16px] mt-[2px] -mr-[5px] relative cursor-pointer"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-arrow-right" color="var(--other-5)"></SvgIcon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";
import { t } from "@/locales";
// types
import { PointsType, RecordListItem } from "packages/types/record";
// utils
import dayjs from "dayjs";

const props = defineProps<{
  item: RecordListItem;
}>();

const renderText = () => {
  switch (props.item.type) {
    case PointsType.consume:
    case PointsType.zero_points_consumption:
      return t("redeemed_x", [props.item.name]);
    case PointsType.rollback:
      return t("failed_to_redeem_tips", [props.item.name]);
    case PointsType.expire:
      return t("expired");
    case PointsType.zero_points_increase:
      return `${t("gift")} ${props.item.name}`;
    case PointsType.lottery_consumption:
      return `${t("participate_activity")} ${props.item.name}`;
    case PointsType.lottery_increase:
      return `${t("lucky_draw_reward")}`;
    default:
      return props.item.name;
  }
};

const getDisplayPoints = () => {
  const symbol = [
    PointsType.increase,
    PointsType.rollback,
    PointsType.zero_points_increase,
    PointsType.lottery_increase,
  ].includes(props.item.type)
    ? "+"
    : "-";
  return `${symbol}  ${props.item.points}`;
};
</script>
