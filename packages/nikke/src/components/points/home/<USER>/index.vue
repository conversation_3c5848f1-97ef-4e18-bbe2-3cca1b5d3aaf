<template>
  <div class="cursor-pointer pt-[36px] break-inside-avoid relative z-[1] pb-[14px]">
    <i
      class="absolute top-0 left-0 w-full h-[145px] -z-[1] bg-[url('@/assets/imgs/points/commodity-item-bg-01.png')] bg-[length:100%_100%]"
    ></i>
    <i
      class="absolute bottom-[18px] left-0 w-full h-[calc(100%_-_160px)] -z-[1] bg-[url('@/assets/imgs/points/commodity-item-bg-02.png')] bg-[length:100%_100%]"
    ></i>
    <i
      class="absolute bottom-0 left-0 w-full h-[20px] -z-[1] bg-[url('@/assets/imgs/points/commodity-item-bg-03.png')] bg-[length:100%_100%]"
    ></i>

    <i
      class="absolute right-[4px] top-[70px] -z-[1] w-[54px] h-[53px] bg-[url('@/assets/imgs/points/commodity-item-mask.png')] bg-[length:100%_100%]"
    ></i>

    <div
      v-if="is_just_today_sold_out"
      class="absolute top-0 left-0 w-full h-full bg-black/50 clip-path-cut z-[8] flex items-center justify-center"
    >
      <span
        class="relative z-[19] rounded-[2px] px-[0px] text-[color:var(--color-white)] text-[length:14px] bg-[var(--text-3)] text-center text-balance pt-[2px]"
        >{{ just_today_sold_out_text }}</span
      >
    </div>
    <div
      v-else-if="is_sold_out"
      class="absolute top-0 left-0 w-full h-full bg-black/50 clip-path-cut z-[8] flex items-center justify-center"
    >
      <span
        class="relative z-[19] rounded-[2px] px-[6px] text-[color:var(--color-white)] text-[length:14px] bg-[var(--text-3)]"
        >{{ t("sold_out") }}</span
      >
    </div>
    <div
      v-else-if="is_gift_card_coming_soon"
      class="absolute top-[128px] translate-y-[-100%] w-[calc(100%-2px)] left-[1px] min-h-[19px] px-[4px] py-[2px] bg-[#F9150599] flex items-center justify-center text-white"
    >
      <SvgIcon
        name="icon-info"
        color="var(--color-white)"
        class="w-[12px] h-[12px] mr-[3px] flex-none"
        :class="{ 'mb-[2px]': lang === 'en' }"
      />
      <span v-fontfix class="text-[length:10px] leading-[10px] line-clamp-2">{{
        t("open_for_redemption_soon")
      }}</span>
    </div>
    <div
      v-else-if="is_redeem_limit"
      class="absolute top-[128px] translate-y-[-100%] w-[calc(100%-2px)] left-[1px] min-h-[19px] px-[4px] py-[2px] bg-[var(--fill-1-40)] flex items-center justify-center text-white"
    >
      <SvgIcon
        name="icon-info"
        color="var(--color-white)"
        class="w-[12px] h-[12px] mr-[3px] flex-none"
      />
      <span v-fontfix class="text-[length:10px] leading-[10px] line-clamp-2">{{
        t("purchase_user_limit")
      }}</span>
    </div>

    <div
      class="absolute left-[8px] top-[8px] z-[5] h-[auto] px-[6px] flex items-start justify-center flex-col w-[50px] min-h-[26px]"
    >
      <i
        class="absolute left-0 top-0 w-[46px] -z-[1] h-full bg-[url('@/assets/imgs/points/commodity-item-label-01.png')] bg-[length:100%_100%]"
      ></i>
      <i
        class="absolute left-[40px] top-[0] -z-[1] w-[calc(100%_-_40px)] h-full bg-[url('@/assets/imgs/points/commodity-item-label-02.png')] bg-[length:100%_100%]"
      ></i>
      <div
        class="text-center mt-[4px] text-[length:11px] font-bold text-[color:var(--color-white)] leading-[8px] w-full"
      >
        {{ item?.has_exchange_num }} / {{ item?.commodity_left_num }}
      </div>
      <div
        v-if="exchange_limit_type"
        class="text-center mt-[1px] text-[length:9px] font-bold text-[color:var(--color-white)] leading-[11px] w-full"
      >
        {{ exchange_limit_type }}
      </div>
    </div>

    <div
      v-if="item?.commodity_tag"
      :class="[
        `absolute right-[0] top-[0] z-[6] w-[53px] h-[21px]  bg-[length:100%_100%]`,
        item?.commodity_tag === 2 ? `bg-[url('@/assets/imgs/points/commodity-item-hot.png')]` : ``,
        item?.commodity_tag === 1 ? `bg-[url('@/assets/imgs/points/commodity-item-new.png')]` : ``,
      ]"
    ></div>

    <div class="h-[75px] mx-auto relative z-[3] flex items-center justify-center">
      <img :src="item?.commodity_pic_url" alt="" class="h-full" />
    </div>

    <div class="mt-[24px] px-[8px]">
      <div
        class="text-[length:11px] line-clamp-2 mb-[10px] font-bold text-[color:var(--other-6)] leading-[14px]"
      >
        {{ item?.commodity_name }}
      </div>
      <div class="flex items-center h-[16px]">
        <i
          class="w-[16px] h-[16px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%] mr-[4px]"
        ></i>
        <span
          class="font-[DINNextLTProBold] mt-[4px] text-[color:var(--other-6)] text-[length:20px] leading-[1]"
        >
          {{ item?.commodity_discount_price ?? item?.commodity_price }}
        </span>
        <DiscountTag
          :original_price="item.commodity_price"
          :current_price="item.commodity_discount_price ?? item.commodity_price"
        />
        <!-- TODO: 折扣过期时间判断 -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Commodity } from "packages/types/commodity";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import DiscountTag from "./discount-tag.vue";
import { usePointsGiftCardEventsStore } from "@/store/points-gift-card";
import SvgIcon from "@/components/common/svg-icon.vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { getStandardizedLang } from "packages/utils/standard";

dayjs.extend(utc);

const { t } = useI18n();
const props = defineProps<{
  item: Commodity;
}>();

const gift_card_store = usePointsGiftCardEventsStore();
const lang = getStandardizedLang();

const exchange_limit_type = computed(() => {
  const { limit_type } = props.item.account_exchange_limit;
  if (limit_type === "day") {
    return t("purchase_limit_day");
  }
  if (limit_type === "week") {
    return t("purchase_limit_week");
  }
  if (limit_type === "month") {
    return t("purchase_limit_month");
  }
  return "";
});

/** 礼品卡今天的兑换完了，但是明天还有 */
const is_just_today_sold_out = computed(() => {
  return (
    gift_card_store.isGiftCardCommodity(props.item) &&
    !props.item.commodity_has_left &&
    gift_card_store.next_redeem_open_time &&
    gift_card_store.is_in_events
  );
});

const just_today_sold_out_text = computed(() => {
  const time = dayjs(gift_card_store.next_redeem_open_time).format("MM/DD HH:mm");
  const timezone_offset = dayjs(gift_card_store.next_redeem_open_time).format("ZZ");
  const sign = timezone_offset[0]; // "+" 或 "-"
  const hours = timezone_offset.substring(1, 3); // 如: 08
  const timezoneTag = ` (UTC${sign}${parseInt(hours)}) `;
  return t("next_redeem_open_time", [time + timezoneTag]);
});

/** 彻底兑换完了 */
const is_sold_out = computed(
  () =>
    !is_just_today_sold_out.value &&
    (gift_card_store.isGiftCardCommodity(props.item)
      ? !props.item.commodity_has_left && !gift_card_store.next_redeem_open_time
      : !props.item.commodity_has_left),
);

/** 礼品卡活动未开始 */
const is_gift_card_coming_soon = computed(
  () => gift_card_store.isGiftCardCommodity(props.item) && gift_card_store.is_before_events,
);

/** 已达个人兑换限制 */
const is_redeem_limit = computed(() => {
  return props.item.has_exchange_num >= props.item.account_exchange_limit.limit_num;
});
</script>

<style scoped>
.clip-path-cut {
  clip-path: polygon(
    15px 1px,
    calc(100% - 1px) 1px,
    calc(100% - 1px) calc(100% - 11px),
    calc(100% - 14px) calc(100% - 1px),
    1px calc(100% - 1px),
    1px 15px
  );
}
</style>
