<template>
  <div class="z-[1] relative">
    <TaskItem v-for="task in filtered_tasks" :key="'task_' + task.task_id" :data="task"></TaskItem>
    <div v-if="page === 'points'" class="flex items-center justify-center">
      <div
        class="flex items-center justify-center h-[16px] w-[43px] bg-black btn-mask cursor-pointer"
        @click="toggleCollapsed"
      >
        <SvgIcon
          name="icon-arrow-top"
          color="white"
          class="w-[10px] h-[10px]"
          :class="[collapsed ? 'rotate-180' : '']"
        ></SvgIcon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { Routes } from "@/router/routes";
import { useRoute } from "vue-router";
// import TaskItem from "./task-item.vue";
import TaskItem from "@/components/mission/task-item/index.vue";
import { TaskType } from "packages/types/rewards";
import { useUser } from "@/store/user";
import { useMissionStore } from "@/store/mission";
import SvgIcon from "@/components/common/svg-icon.vue";

const props = defineProps<{
  page: "usercenter" | "points";
}>();

const user_store = useUser();

const route = useRoute();
const is_reward_page = route.path.includes(Routes.POINTS);

const mission_store = useMissionStore();

const collapsed = ref(true);

const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

const filtered_tasks = computed(() => {
  const list = mission_store.reward_task_list.filter((task) => {
    if (props.page === "usercenter") {
      return [TaskType.DailyCheckIn, TaskType.GameLogin].includes(task.task_type);
    }
    return true;
  });
  return list.slice(0, collapsed.value ? 3 : undefined);
});

// 此时再发起请求：1. 确认未登录； 2. 确认是已登录而且查询到 LIP 否绑定状态（是否绑定都行）
const canFetchRewardTasks = computed(() => {
  if (user_store.loading) return false;
  if (user_store.user_had_bind_lip_loading) return false;
  if (user_store.is_login && user_store.user_had_bind_lip_loading) return false;
  return true;
});
watch(canFetchRewardTasks, (v) => v && mission_store.refetchRewardTask(), { immediate: true });

onMounted(async () => {
  if (!is_reward_page) {
    try {
      await user_store.checkHasLipAccount();
    } catch {
      console.warn("task onMounted checkHasLipAccount failed");
    }
  }
});
</script>

<style scoped>
.btn-mask {
  clip-path: polygon(0px 0px, 100% 0px, 100% calc(100% - 4px), calc(100% - 4px) 100%, 0px 100%);
}
</style>
