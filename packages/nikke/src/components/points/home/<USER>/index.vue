<template>
  <div v-if="list?.length > 0" class="points-home-banner relative h-[164px] z-[1]">
    <i
      class="absolute-center z-[1] pointer-events-none !w-full !h-full bg-[url('@/assets/imgs/points/banner-mask.png')] bg-[length:100%_100%]"
    ></i>
    <div class="w-[full] relative !h-[164px]">
      <!-- 本期一张图后期会衍生到多张 -->
      <Swiper
        class="!w-[full]"
        :loop="false"
        :index="0"
        :autoplay="list.length > 1"
        :duration="3000"
        @change="onChange"
      >
        <div
          v-for="item in list"
          :key="'banner_' + item.content_id"
          class="w-full h-full cursor-pointer"
          @click="onClickItem(item)"
        >
          <img :src="getImage(item)" />
        </div>
        <template #pagination>
          <SwiperPagination
            v-if="list.length > 1"
            class="absolute top-[144px] left-0 right-0 points-home-banner-pagination"
          ></SwiperPagination>
        </template>
      </Swiper>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperPagination } from "@/components/common/swiper";
import { CMS_COLUMN_NAME_REWARDS, CMS_COLUMN_NAME_REWARDS_BANNER } from "packages/configs/cms";
import { CmsContentClass } from "packages/types/cms";
import { useBsCmsListCpnt, useCms } from "@/composables/use-cms";
import { resovledCmsConfig } from "packages/utils/cms";
import { report } from "packages/utils/tlog";
import { IDetail } from "@tencent/pa-cms-utils";
import { watch } from "vue";
const { list, loading } = useBsCmsListCpnt({
  cms_config: resovledCmsConfig(),
  primary_column_name: CMS_COLUMN_NAME_REWARDS,
  second_column_name: CMS_COLUMN_NAME_REWARDS_BANNER,
  content_class: CmsContentClass.banner,
});
console.log("list", list.value.length, loading);
const { useCmsJump, getImage } = useCms();

const onChange = (info: { current_index: number }) => {
  const item = list.value[info.current_index] as IDetail;
  item && reportBannerItem(item);
};

const onClickItem = (item: IDetail) => {
  useCmsJump(item);
  report.standalonesite_usercenter_reward_banner_item_exposeclick.cm_vshow({
    content_id: item.content_id,
    title: item.title,
    location: 0,
    dst_url: item.jump_link_info?.jump_url, // TODO: 兼容文件跳转
  });
};

watch(
  () => list.value.length,
  () => {
    list.value?.[0] && reportBannerItem(list.value[0] as IDetail);
  },
);

const reportBannerItem = (item: IDetail) => {
  report.standalonesite_usercenter_reward_banner_item_expose.cm_vshow({
    content_id: item.content_id,
    title: item.title,
    location: 0,
    dst_url: item.jump_link_info?.jump_url, // TODO: 兼容文件跳转
  });
};
</script>
<style lang="scss" scoped>
.points-home-banner {
  clip-path: polygon(
    7.5px 0%,
    100% 0,
    100% calc(100% - 7px),
    calc(100% - 7px) 100%,
    14.5px 100%,
    0 calc(100% - 14.5px),
    0% 7.5px
  );
}
:deep(.points-home-banner-pagination div) {
  height: 8px;
  width: 8px;
}
:deep(.swiper-pagination-item-active) {
  background: var(--other-2);
}
</style>
