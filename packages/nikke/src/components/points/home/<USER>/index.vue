<template>
  <div>
    {{ t("daily_task_refresh_at", [refresh_time + timezoneTag]) }}
  </div>
</template>

<script setup lang="ts">
import { t } from "@/locales";

import dayjs from "dayjs";

// UTC+0 的 00:00 对应的本地时间 HH:mm
const refresh_time = dayjs().utc().startOf("day").local().format("HH:mm");

const timezone_offset = dayjs().format("ZZ");
const sign = timezone_offset[0]; // "+" 或 "-"
const hours = timezone_offset.substring(1, 3); // 如: 08
const timezoneTag = ` (UTC${sign}${parseInt(hours)}) `;
</script>
