<template>
  <CommBottomPopup :show="true" @close="$emit('close')">
    <template #header>
      <div class="flex items-center justify-between px-[20px] py-[12px]">
        <div class="text-[16px] font-bold text-[color:var(--text-1)] pt-[4px]">
          {{ t("redeem_deatil") }}
        </div>
        <div class="w-[20px] h-[20px] relative cursor-pointer" @click="$emit('close')">
          <i class="absolute-center"></i>
          <SvgIcon class="w-[40px] h-[40px]" name="icon-pop-close" color="var(--text-1)"></SvgIcon>
        </div>
      </div>
    </template>
    <div class="px-[20px]">
      <div class="flex items-top justify-between my-[10px] text-[13px] text-[color:var(--text-1)]">
        <div class="font-medium">{{ t("reward") }}</div>
        <div class="font-medium line-clamp-4 max-w-[250px]">
          {{ props.item.commodity_name }}
        </div>
      </div>
      <div
        class="redeem w-full px-[10px] py-[12px] bg-[var(--fill-3)] border-[1px] border-[color:var(--line-1)] dark:border-[color:var(--line-1)]"
      >
        <!--         <div class="flex items-center justify-start mb-[2px]">
          <div
            class="w-[18px] h-[16px] bg-[url('@/assets/imgs/points/contribution.png')] bg-[length:100%_100%]"
          ></div>
          <div class="text-[13px] font-bold mx-[5px] leading-[18px]">Stardust</div>
          <div class="text-[12px] font-bold leading-[22px] font-[DINNextLTPro]">X30</div>
        </div> -->
        <div class="overflow-y-auto px-[5px]">
          <div
            v-safe-html="desc"
            class="text-[color:var(--text-3)] text-[11px] mb-[8px] max-h-[64px] leading-[18px]"
          ></div>
          <!--  <div class="text-[color:var(--text-3)] text-[11px]">
            Prizes inventory is limited, first come first served basis
          </div> -->
        </div>
      </div>
      <div class="flex items-center justify-between mt-[10px]">
        <div class="font-bold text-[color:var(--text-1)] text-[length:13px]">{{ t("server") }}</div>
        <div
          ref="server_ref"
          class="relative w-[150px] h-[28px] p-[5px] border border-[color:var(--line-1)] bg-[color:var(--fill-3)] flex items-center justify-between"
          @click="open"
        >
          <div class="text-[11px] text-[color:var(--text-3)] line-clamp-1">{{ label }}</div>
          <SvgIcon
            class="p-[10px] w-[26px] h-[26px] flex-shrink-0"
            name="icon-arrow-down"
            color="var(--text-1)"
          ></SvgIcon>
          <SelectOption
            v-show="is_show"
            :list="regions"
            :active-id="checked?.index"
            class="top-[26px] right-[-1px] w-[151px] border border-[color:var(--line-1)] z-10"
            @change="change"
          >
          </SelectOption>
        </div>
      </div>
      <div
        v-if="!checked?.value"
        class="mt-[8px] mb-[8px] text-[color:var(--error)] text-[12px] leading-[14px]"
      >
        {{ t("please_choose_region") }}
      </div>
      <div
        class="flex items-center justify-between mt-[10px]"
        :class="[`flex items-center justify-between mt-[10px]`, has_character ? '' : 'pb-[12px]']"
      >
        <div class="flex flex-col">
          <div class="font-bold text-[color:var(--text-1)] text-[length:13px]">
            {{ t("character") }}
          </div>
        </div>
        <div
          ref="role_ref"
          :class="[
            `relative w-[150px] h-[28px] p-[5px] border border-[color:var(--line-1)] bg-[color:var(--fill-3)] flex items-center justify-between`,
            has_character ? 'bg-[color:var(--op-fill-white)]' : 'bg-[color:var(--fill-3)] ',
          ]"
          @click="open_role"
        >
          <div
            :class="[
              `text-[11px] line-clamp-1`,
              has_character ? 'text-[color:var(--text-1)]' : 'text-[color:var(--color-4)]',
            ]"
          >
            {{ role_label }}
          </div>
          <SvgIcon
            class="p-[10px] w-[26px] h-[26px] flex-shrink-0"
            name="icon-arrow-down"
            color="var(--text-1)"
          ></SvgIcon>
          <SelectOption
            v-show="is_show_role"
            :list="roles"
            :active-id="checked_role?.index"
            class="top-[26px] right-[-1px] w-[151px] border border-[color:var(--line-1)] z-10"
            @change="change_role"
          >
          </SelectOption>
        </div>
      </div>
      <div
        v-if="!has_character"
        class="mt-[8px] mb-[8px] text-[color:var(--error)] text-[12px] leading-[14px]"
      >
        {{ character_tips }}
      </div>
      <SelectSku
        v-if="is_gift_card"
        :tag_id="gift_card_tag_id"
        @update:value="gift_card_tag_id = $event"
      ></SelectSku>
      <div class="border-b-[1px] mt-[10px] border-b-[color:var(--line-1)]"></div>
      <div class="flex items-center justify-between my-[10px]">
        <div class="font-bold text-[color:var(--text-1)] text-[length:13px]">{{ t("total") }}</div>
        <div class="flex items-center justify-between">
          <div
            class="w-[18px] h-[18px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%] mr-[8px]"
          ></div>
          <div
            class="font-bold text-[length:16px] leading-[16px] text-[color:var(--text-1)] mt-[2px]"
          >
            {{ price }}
          </div>
        </div>
      </div>
      <div
        class="w-full px-[20px] py-[6px] border border-[color:var(--line-1)] bg-[color:var(--fill-3)] flex items-center justify-between box-border mb-[8px]"
      >
        <div class="text-[color:var(--text-3)] text-[12px] leading-[13px]">
          {{ t("available") }}
        </div>
        <div class="flex items-center justify-between">
          <div
            class="w-[18px] h-[18px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%] mr-[4px]"
          ></div>
          <div class="font-bold text-[12px] leading-[13px] text-[color:var(--error)] pt-[3px]">
            <span class="font-[DINNextLTPro]">{{ user_store.total_point }} </span>
            <span v-if="is_available_price_not_enough" class="font-[DINNextLTPro] ml-[6px]">
              ({{ t("points_no_enouch") }})
            </span>
          </div>
        </div>
      </div>
      <div
        v-if="!user_can_ex"
        class="flex cursor-pointer justify-start h-[36px] text-[length:13px] leading-[16px] font-medium items-center mt-[8px] text-[color:var(--error)] bg-[color:none]"
      >
        <SvgIcon
          class="pl-[12px] pt-[9px] pb-[10px] pr-[8px] w-[36px] h-[36px]"
          name="icon-warn"
          color="var(--error)"
        >
        </SvgIcon>
        <span>{{ t("purchase_user_limit") }}</span>
      </div>
      <div>
        <Button class="mb-[6px]" type="primary" :disabled="disabled" @click="onRedeem">
          {{ redeem_button_text }}
        </Button>
        <Button type="secondary" @click="$emit('close')">{{ t("close") }}</Button>
      </div>
    </div>
  </CommBottomPopup>
</template>

<script setup lang="ts">
import { onMounted, computed, watch } from "vue";
import SelectOption from "@/components/common/select-option/index.vue";
import Button from "@/components/ui/button/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useDialog } from "@/components/ui/dialog/index.ts";
import CommBottomPopup from "@/components/common/comm-bottom-popup.vue";
import { useToast } from "@/components/ui/toast";
import { Commodity } from "packages/types/commodity";
import { useUser } from "@/store/user";
import { useDiscount } from "@/composables/use-commodity";
import { useGames } from "@/composables/use-games";
import { useSelect, Options } from "@/composables/use-select";
import { getDetail, checkUserCanExchange, exchangeCommodity } from "@/api/commodity";
import { Role, GameRegion } from "packages/types/games";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { report } from "packages/utils/tlog";
import { CODE_ALL_CONFIGS } from "packages/configs/code";
import { useRouter } from "vue-router";
import SelectSku from "./select-sku.vue";
import { usePointsGiftCardEventsStore } from "@/store/points-gift-card";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { useCaptcha } from "@/composables/use-captcha";
import { PopCallbackValue } from "packages/types/common";

dayjs.extend(utc);

const { t, te } = useI18n();
const { show } = useToast();
const { show: showDialog } = useDialog();
const user_store = useUser();
const { getServerByGame, getRoles, games, getSavedRole } = useGames();
const submit_loading = ref(false);

const props = defineProps<{
  item: Commodity;
}>();
/**说明：
 * 1、先用prop的数据渲染弹窗，保证 静态数据 先出来
 * 2、随后请求刷新动态数据， 保证 状态数据 正确；
 */
const loading = ref(false);
const loading_role = ref(true);
const emits = defineEmits(["close", "success", "share"]);
const has_ex_num = ref(0);
// 是否有兑换资格
const user_can_ex = ref(true);
// 是否售罄
const is_sold_out = ref(!props.item?.commodity_has_left);
// 真实价格
const price = ref(props.item.commodity_price);
// 金币是否足够
const is_available_price_not_enough = computed(() => price.value > user_store.total_point);
// 不可兑换场景，复合条件：商品数量不足 || 无兑换资格(限量上限) || 金币不够
const disabled = computed(() => {
  // return is_sold_out.value || !user_can_ex.value || is_available_price_not_enough.value;
  return (
    !checked_role.value ||
    is_sold_out.value ||
    !user_can_ex.value ||
    is_available_price_not_enough.value ||
    (is_gift_card.value && !gift_card_tag_id.value) ||
    (is_gift_card.value && gift_card_store.is_before_events)
  );
});
const desc = computed(() => props.item.commodity_desc.replace(/&nbsp;/g, " "));

const is_gift_card = computed(() => gift_card_store.isGiftCardCommodity(props.item));

const redeem_button_text = computed(() => {
  if (
    is_gift_card.value &&
    gift_card_store.next_redeem_open_time &&
    (is_sold_out.value || gift_card_store.is_before_events)
  ) {
    const time = dayjs(gift_card_store.next_redeem_open_time).format("MM/DD HH:mm");
    const timezone_offset = dayjs(gift_card_store.next_redeem_open_time).format("ZZ");
    const sign = timezone_offset[0]; // "+" 或 "-"
    const hours = timezone_offset.substring(1, 3); // 如: 08
    const timezoneTag = ` (UTC${sign}${parseInt(hours)}) `;
    return t("redeem_open_time", [time + timezoneTag]);
  }
  if (is_sold_out.value) return t("sold_out");
  if (!user_can_ex.value) return t("redeemed");
  return t("redeem");
});

const data = ref<Commodity | undefined>();
type Regions = Options<{ ext: GameRegion }>;
const regions = ref<Regions[]>([]);
const roles = ref<Options<{ ext: Role }>[]>([]);
const server_ref = ref<HTMLElement>();
const role_ref = ref<HTMLElement>();
const router = useRouter();
const gift_card_tag_id = ref("");
const {
  label: role_label,
  is_show: is_show_role,
  change: change_role,
  open: open_role,
  checked: checked_role,
  reset: resetRole,
} = useSelect({
  list: roles,
  placeholder: t("choose_character"),
  content_ref: role_ref,
});
const gift_card_store = usePointsGiftCardEventsStore();

const { label, is_show, change, open, checked, reset } = useSelect({
  list: regions,
  placeholder: t("choose_server"),
  content_ref: server_ref,
  handle: async (option) => {
    resetRole();
    loading_role.value = true;
    const res = await getRoles(`${option.value}`, data.value?.game_id || "", "");
    roles.value = res;
    loading_role.value = false;
  },
});

const has_character = computed(() => checked_role?.value && roles.value.length > 0);
const character_tips = computed(() => {
  // 加载角色信息中
  if (loading_role.value) {
    return t("loading_role");
  }
  // 有角色 但未选择角色 提示 选择
  if (roles.value.length > 0 && !checked_role?.value) {
    return t("choose_character_tips");
  }
  // 选择区服 但 无角色
  if (checked.value && roles.value.length === 0) {
    return t("role_not_found");
  }
  return "";
});
const exchange_id = props.item.exchange_commodity_id;
const fetchData = async () => {
  loading.value = true;
  try {
    const res = is_gift_card.value ? props.item : await getDetail(exchange_id);
    data.value = res;
    if (res.commodity_left_num === 0) {
      // TODO: 商品数量不足 提示；
    }
    const { can = false, has_num } = is_gift_card.value
      ? await gift_card_store.checkGiftCardUserCanExchange({ refetch: false })
      : await checkUserCanExchange(exchange_id);
    // 限额【每x兑换】的剩余次数? 暂时用不上 不知道具体含义
    has_ex_num.value = has_num;
    user_can_ex.value = can;
    // 实时获取用户积分 根据积分判断是否可以兑换
    user_store.refreshPoints();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => gift_card_store.events_day_index,
  () => {
    if (is_gift_card.value) {
      is_sold_out.value = false;
    }
  },
);

const onRedeem = async () => {
  report.standalonesite_usercenter_reward_redmeet.cm_click({
    product_id: data.value?.exchange_commodity_id!,
  });
  if (
    submit_loading.value ||
    is_available_price_not_enough.value ||
    !checked_role.value?.value ||
    !user_can_ex.value
  ) {
    return;
  }
  if (is_gift_card.value && !gift_card_tag_id.value) {
    return;
  }
  if (is_gift_card.value) {
    if (gift_card_store.is_before_events) return;
    await gift_card_store.checkGiftCardUserCanExchange({ refetch: false });
    if (!gift_card_store.has_left_info.present_group_left?.[gift_card_tag_id.value]) {
      gift_card_tag_id.value = "";
      return;
    }
    if (disabled.value) return;
  }
  const { getCaptchaCode } = useCaptcha();
  const { ticket, randstr } = is_gift_card.value
    ? await getCaptchaCode()
    : { ticket: "", randstr: "" };
  try {
    submit_loading.value = true;
    const res = is_gift_card.value
      ? await gift_card_store
          .exchangeGiftCard({
            tag_id: gift_card_tag_id.value,
            game_id: checked_role.value?.ext.game_id,
            area_id: checked_role.value?.ext.area_id,
            role_id: checked_role.value?.ext.role_id,
            _headers: {
              "X-Captcha": JSON.stringify({ randstr, ticket }),
            },
          })
          .then((res) => ({ order_id: res.order_serial_number }))
      : await exchangeCommodity({
          exchange_commodity_id: data.value?.exchange_commodity_id!,
          exchange_commodity_price: price.value,
          role_info: checked_role.value?.ext as Role,
          save_role: false,
        });
    report.standalonesite_usercenter_reward_redmeet_ret.cm_click({
      product_id: data.value?.exchange_commodity_id!,
      ret: res.order_id,
    });
    showDialog({
      title: t("success_redeemed"),
      content: is_gift_card.value
        ? t("success_redeemed_gift_card_tips")
        : t("success_redeemed_tips"),
      confirm_text: t("confirm"),
      cancel_text: t("close"),
      callback(options) {
        options.close();
        if (options.value === PopCallbackValue.confirm && is_gift_card.value) {
          router.push(`/points/order?id=${res.order_id}`);
        }
      },
    });
    user_store.refreshPoints();
    emits("success", exchange_id);
    emits("close");
  } catch (error: any) {
    const { code = "" } = error;
    console.warn(error);
    const key = (`${code}` || "").toLowerCase();
    code !== CODE_ALL_CONFIGS.GAME_NOT_LOGIN &&
      showDialog({
        title: t("failed_redeemed"),
        content: te(`api_code_${key}`) ? t(`api_code_${key}`) : "",
        confirm_text: t("close"),
        callback(options: { value: any; close: () => void }) {
          options.close();
        },
      });
    emits("close");
    // TODO:兑换失败上报异常
  } finally {
    submit_loading.value = false;
    if (is_gift_card.value) {
      await gift_card_store.checkGiftCardUserCanExchange({ refetch: true });
    }
  }
};

onMounted(async () => {
  if (!user_store.is_login) {
    show({ text: t("play_games_tips") });
    return;
  }
  await fetchData();
  const { is_discount_show, is_sold_out: sold_out } = useDiscount(data);
  price.value = is_discount_show.value
    ? data.value!.commodity_discount_price!
    : data.value!.commodity_price;

  is_sold_out.value = !!sold_out.value;
  const servers = await getServerByGame(data.value!.game_id);
  regions.value = servers;
  loading_role.value = true;
  const save_role = await getSavedRole();
  loading_role.value = false;
  if (save_role && save_role.ext.game_id === data.value?.game_id) {
    roles.value = [save_role];
    reset(parseInt(save_role.ext.area_id + ""));
    resetRole(save_role.ext.role_id);
  }
});
</script>

<style lang="scss" scoped>
.redeem ::-webkit-scrollbar {
  width: 2px;
}

.redeem ::-webkit-scrollbar-track {
  background-color: var(--fill-3);
  border-radius: 0;
}

.redeem ::-webkit-scrollbar-thumb {
  background-color: var(--op-fill-10);
  border-radius: 6px;
}

.redeem ::-webkit-scrollbar-thumb:hover {
  background-color: var(--op-fill-20);
}
</style>
