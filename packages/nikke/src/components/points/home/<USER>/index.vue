<template>
  <div class="relative w-full">
    <div class="flex p-[12px] justify-between sticky top-[43px] left-0 z-[20]">
      <i
        class="absolute left-0 top-0 -z-[1] w-full h-full bg-[url('@/assets/imgs/points/list-head-bg.png')] bg-[length:100%_100%]"
      ></i>
      <div class="font-bold text-[length:18px] text-[color:var(--color-1)] leading-[18px]">
        {{ t("redeem_rewards") }}
      </div>
    </div>

    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="state.loading"
      :empty="state.empty"
      :finished="state.finished"
      :debounce_interval="10"
      class="w-full relative bg-[var(--color-white)] min-h-[300px]"
      @load-more="loadMore"
    >
      <MasonryWall :items="commodity_list" :min-columns="2" :gap="14" class="px-[12px]">
        <template #default="{ item, index }">
          <CommodityItem
            :key="index"
            v-click-interceptor.need_login.mute.sign_privacy.stop="() => onClick(item)"
            :item="item"
            class="mb-[12px]"
          ></CommodityItem>
        </template>
      </MasonryWall>
    </InfiniteScroll>
  </div>

  <RedeemDetail
    v-if="redeemDetail?.exchange_commodity_id"
    :item="redeemDetail"
    @success="onRedeemSuccess"
    @close="handleCloseRedeemDetail"
  ></RedeemDetail>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import MasonryWall from "@/components/ui/masonry/masonry-wall.vue";
import { InfiniteScroll } from "@/components/common/scroll/index";
import CommodityItem from "@/components/points/home/<USER>/index.vue";
import RedeemDetail from "@/components/points/home/<USER>/index.vue";
import { Commodity } from "packages/types/commodity";
import { useList } from "@/composables/use-list";
import { useCheck } from "@/components/points/use-check-user";
import { getList, checkUserCanExchange } from "@/api/commodity";
import { useUser } from "@/store/user";

import { useI18n } from "vue-i18n";
import { useGames } from "@/composables/use-games";
import { getStandardizedGameId } from "packages/utils/standard";
import { report } from "packages/utils/tlog";
import { usePointsGiftCardEventsStore } from "@/store/points-gift-card";
import { storeToRefs } from "pinia";
const gameid = getStandardizedGameId();
const user_store = useUser();
const { t } = useI18n();
const { games } = useGames();
const { check, go } = useCheck();
const emits = defineEmits(["share"]);
const game_id_list = gameid ? [gameid] : games.value.map((item) => item.value);
const { gift_card_commodity } = storeToRefs(usePointsGiftCardEventsStore());
const gift_card_store = usePointsGiftCardEventsStore();

const { state, load, loadMore } = useList(getList, {
  query: {
    game_id_list,
    page_num: 1,
    page_size: 10,
    is_bind_lip: user_store.user_had_bound_lip,
  },
  list_key: "commodity_list",
  total_key: "total_num",
});

const commodity_list = computed(() => {
  return gift_card_commodity.value ? [gift_card_commodity.value, ...state.list] : state.list;
});

onMounted(async () => {
  load();
  await gift_card_store.refetchHasLeftInfo();
});
const redeemDetail = ref<Commodity | null>();
const onClick = (item: Commodity) => {
  report.standalonesite_usercenter_reward_pruduct_item.cm_click({
    product_id: item.exchange_commodity_id,
  });
  if (!item.commodity_has_left) {
    // return;
  }
  if (check()) {
    go();
    return;
  }
  redeemDetail.value = item;
};
const onRedeemSuccess = async (id: string) => {
  const { has_num } = await checkUserCanExchange(id);
  state.list.forEach((item) => {
    if (item.exchange_commodity_id === id) {
      item.has_exchange_num = has_num;
    }
  });
};
const handleCloseRedeemDetail = () => {
  redeemDetail.value = null;
};
</script>

<style lang="scss" scoped></style>
