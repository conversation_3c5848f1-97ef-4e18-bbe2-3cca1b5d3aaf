<template>
  <div class="flex items-center my-[10px]">
    <div class="font-bold text-[color:var(--text-1)] text-[length:13px]">
      {{ `${t("sku")}（${sku_list.length}）` }}
    </div>
  </div>
  <div class="flex gap-[6px] items-center flex-wrap">
    <div
      v-for="item in sku_list"
      :key="item.tag_id"
      class="text-[color:var(--text-2)] text-[11px] flex justify-center items-center h-[30px] min-w-[80px] px-[16px] bg-[color:var(--fill-3)] border-[color:var(--fill-2)] border rounded-[5px] relative"
      :class="{
        'cursor-pointer': !item.disabled,
        '!bg-[color:var(--brand-2)] !border-[color:var(--brand-1)] !text-[color:var(--brand-1)]':
          item.tag_id === tag_id,
      }"
      @click="item.disabled ? null : emit('update:value', item.tag_id)"
    >
      <!-- <SvgIcon
        v-if="item.disabled"
        name="icon-sold-out"
        class="absolute left-0 top-0 w-[14px] h-[14px]"
      /> -->
      <span
        v-if="item.disabled"
        class="absolute right-0 -top-[6px] px-[4px] pt-[3px] pb-[1px] rounded-[2px] text-[8px] leading-[9px] text-white bg-[color:var(--text-3)]"
      >
        {{ t("sold_out") }}
      </span>
      <div
        v-fontfix
        class="leading-none"
        :class="{ '!text-[color:var(--color-4)]': item.disabled }"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
  <div
    v-if="!tag_id"
    class="mt-[8px] mb-[8px] text-[color:var(--error)] text-[12px] leading-[14px]"
  >
    {{ t("please_select_sku_type") }}
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { t } from "@/locales";
import SvgIcon from "@/components/common/svg-icon.vue";
import { usePointsGiftCardEventsStore } from "@/store/points-gift-card";
const props = defineProps<{
  tag_id: string;
}>();

const gift_card_store = usePointsGiftCardEventsStore();

const sku_list = computed(() => {
  return [
    { tag_id: "1", label: t("sku_1") },
    { tag_id: "2", label: t("sku_2") },
    { tag_id: "3", label: t("sku_3") },
    { tag_id: "4", label: t("sku_4") },
  ].map((item) => ({
    ...item,
    disabled: isTagDisabled(item.tag_id),
  }));
});

const isTagDisabled = (tag_id: string) => {
  const has_left =
    gift_card_store.has_left_info?.present_group_left?.[tag_id as "1" | "2" | "3" | "4"] ?? false;
  return has_left === false;
};

const emit = defineEmits<{
  (e: "update:value", value: string): void;
}>();
</script>
