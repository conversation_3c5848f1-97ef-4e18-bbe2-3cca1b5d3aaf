<template>
  <span
    v-if="is_discount"
    class="text-[length:14px] leading-[1] line-through text-[color:var(--color-3)] mt-[2px] ml-[4px]"
  >
    {{ original_price }}
  </span>
  <div
    v-if="is_discount"
    class="relative flex items-center justify-center min-w-[33px] h-[16px] ml-[6px]"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 66 32"
      fill="none"
      class="absolute inset-0"
      preserveAspectRatio="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0 4.6985V32H56.23L66 22.4712V0H4.81745L0 4.6985Z"
        fill="url(#paint0_linear_146_30165)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_146_30165"
          x1="0"
          y1="20"
          x2="67.5"
          y2="20"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#37E6FA" />
          <stop offset="1" stop-color="#04C7DC" />
        </linearGradient>
      </defs>
    </svg>
    <span class="text-white text-[length:11.5px] z-[1] pt-[2px] px-[4px]">-{{ discount }}%</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  /** 原价 */
  original_price: number;
  /** 折扣价 */
  current_price: number;
}>();

const discount = computed(() => {
  return (
    Math.floor(((props.original_price - props.current_price) / props.original_price) * 1000) / 10
  );
});

const is_discount = computed(() => {
  return props.original_price > props.current_price;
});
</script>
