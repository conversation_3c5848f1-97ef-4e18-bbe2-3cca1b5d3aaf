import { nextTick, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useToast } from "@/components/ui/toast";
import GiftPop from "@/components/common/gift-pop/index.vue";
import { showPop } from "@/components/ui/index";
import { STORAGE_LS_GAME_CHECK_IN } from "packages/configs/storage";
import { useLSStorage } from "packages/utils/storage";
import { isInIntlBrowser } from "packages/utils/tools";
import { Task, TaskType } from "packages/types/rewards";
import { useUser } from "@/store/user";
import { useDailyCheckIn } from "@/api/rewards";
import { Routes } from "@/router/routes";
import { useCollection } from "@/composables/use-collection";
import { useRoute, useRouter } from "vue-router";
import { PopCallbackValue } from "packages/types/common";
import { launch, download } from "@/utils/launch";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { useMissionStore } from "@/store/mission";
const { checkCollection } = useCollection();

export const useTask = () => {
  const route = useRoute();
  const { show } = useToast();
  const { show: showDialog } = useDialog();
  const { t } = useI18n();
  const router = useRouter();
  const user_store = useUser();
  const { setItemWithExpiry, getItemWithExpiry } = useLSStorage();
  const daily_check_loading = ref(false);

  const mission_store = useMissionStore();

  // 当日首次登录判断是否进行过游戏登录
  // 如有则弹出提示
  const checkPlayGame = async (task: Task) => {
    if (task.task_type !== TaskType.GameLogin || !task.is_completed) {
      return;
    }
    const ids = getItemWithExpiry<string[]>(STORAGE_LS_GAME_CHECK_IN) || [];
    if (ids.includes(task.task_id)) {
      // 今天已经弹出过
      return;
    }
    const refreshPoints = () => {
      onRefreshPoints(-task.points);
      nextTick(() => {
        onRefreshPoints(task.points);
      });
    };

    showPop(
      {
        visible: true,
        is_suc: true,
        data: task,
        title: t("check_in_suc"),
        desc: t("play_game_tomorrow", [task.points]),
        confirmText: t("confirm"),
        onConfirm: () => {
          refreshPoints();
        },
      },
      GiftPop,
      "GiftPop",
    );
    ids.push(task.task_id);
    setItemWithExpiry(STORAGE_LS_GAME_CHECK_IN, ids, getExpiry());
  };

  const checkTask = async (task: Task) => {
    onDailyCheck(task);
    onGameLogin(task);
  };

  // 每日签到任务
  const onDailyCheck = async (task: Task) => {
    if (task.task_type !== TaskType.DailyCheckIn || daily_check_loading.value) {
      return;
    }

    if (task.is_completed) {
      show({ text: t("please_check_in_tomorrow") });
      return;
    }

    const desc = `${t("check_in_tomorrow", [task.points])}`;
    const is_reward_page = route.path.includes(Routes.POINTS);
    try {
      daily_check_loading.value = true;
      await useDailyCheckIn.run({ task_id: task.task_id });
      task.is_completed = true;
      mission_store.refetchRewardTask();
      showPop(
        {
          visible: true,
          is_suc: true,
          data: task,
          title: t("check_in_suc"),
          desc,
          confirmText: t(is_reward_page ? "confirm" : "reward_center"),
          callback(value: PopCallbackValue) {
            if (is_reward_page) {
              checkCollection(task);
              return;
            }
            value === PopCallbackValue.confirm && router.push({ path: Routes.POINTS });
          },
        },
        GiftPop,
        "GiftPop",
      );
      onRefreshPoints(task.points);
    } catch (error: any) {
      let desc = t("sign_in_failed");
      if (error.code === 1001009) {
        // 暂时设置领取状态为已经完成
        task.is_completed = true;
        onRefreshPoints(task.points);
        desc = t("please_check_in_tomorrow");
      }
      if (error.code === 300001) {
        show({ text: t("play_games_tips") });
        return;
      }
      showPop(
        {
          visible: true,
          is_suc: false,
          data: task,
          title: t("sign_in_failed"),
          desc,
          confirmText: t("confirm"),
        },
        GiftPop,
        "GiftPop",
      );
    } finally {
      daily_check_loading.value = false;
    }
  };

  // 游戏登录签到任务
  const onGameLogin = async (task: Task) => {
    if (task?.task_type !== TaskType.GameLogin || isInIntlBrowser()) {
      return;
    }
    if (task.is_completed) {
      launch().catch(() => {
        console.log("is_completed launch catch");
        download();
      });
    } else {
      showDialog({
        title: t("task_tips"),
        content: t("play_games_tips", [task.points]),
        confirm_text: t("game_login"),
        callback(options: { value: any; close: () => void }) {
          if (options.value === PopCallbackValue.confirm) {
            launch().catch(() => {
              console.log("launch catch");
              download();
            });
          }
          options.close();
        },
      });
    }
  };
  const onRefreshPoints = (points: number) => {
    user_store.refreshPoints(user_store.total_point + points);
  };

  return {
    checkTask,
    checkPlayGame,
  };
};

const getExpiry = () => {
  let now = new Date();

  // 设置当前时间的第二天
  now.setDate(now.getDate() + 1);

  // 设置时间为0点
  now.setHours(0, 0, 0, 0);
  return now;
};
