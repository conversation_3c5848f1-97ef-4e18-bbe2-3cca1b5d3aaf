<template>
  <div
    class="flex items-center justify-between mb-[8px] last-of-type:mb-0 bg-[var(--op-fill-white)] overflow-hidden"
  >
    <div class="flex-1 mr-[10px] py-[8px] pl-[10px]">
      <div
        class="font-bold text-[length:14px] text-[color:var(--text-1)] font-[DINNextLTProBold] leading-[18px]"
      >
        {{ data.task_name }}
      </div>
      <div class="flex items-center">
        <i
          class="w-[16px] h-[16px] mr-[4px] bg-[url('@/assets/imgs/common/icon-gold.png')] bg-[length:100%_100%]"
        ></i>
        <span class="text-[length:12px] mt-[4px] text-[color:var(--text-1)] leading-[1]">
          +{{ renderPoints() }}
        </span>
      </div>
    </div>
    <div
      class="w-[87px] cursor-pointer text-center h-[50px] relative z-[2] flex items-center justify-end"
    >
      <SvgIcon
        name="icon-gift-mask"
        color="var(--fill-1-20)"
        class="w-[61px] h-full absolute top-0 -left-[30px] -z-[1] icon-gift-mask"
      ></SvgIcon>
      <i
        :class="[
          `absolute top-0 -right-[15px] w-full h-full -z-[1] -skew-x-[30deg]`,
          data?.is_completed ? `bg-[var(--fill-1-60)]` : `bg-[var(--brand-1)]`,
        ]"
      ></i>
      <div
        v-if="data.task_type === 1"
        v-click-interceptor.need_login.mute.sign_privacy.stop="() => onClick(data)"
        :class="[
          `w-[24px] h-[24px] bg-[length:100%_100%] mr-[19px]`,
          data.is_completed
            ? `bg-[url('@/assets/imgs/common/icon-gift-true.png')]`
            : `bg-[url('@/assets/imgs/common/icon-gift.png')]`,
        ]"
      >
        <!-- {{ t("check_in") }} -->
      </div>
      <div
        v-else-if="data.task_type === 2"
        v-click-interceptor.need_login.mute.sign_privacy.stop="() => onClick(data)"
        :class="[
          `w-[24px] h-[24px] bg-[length:100%_100%] mr-[19px]`,
          data.is_completed
            ? `bg-[url('@/assets/imgs/common/icon-gift-true.png')]`
            : ` px-[4px] py-[6px]`,
        ]"
      >
        <!-- {{ t("game_login") }} -->
        <SvgIcon
          v-if="!data.is_completed"
          name="icon-arrow-right3"
          color="var(--color-white)"
        ></SvgIcon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
// import { useI18n } from "vue-i18n";
import { Task, TaskType } from "packages/types/rewards";
import { useCheck } from "@/components/points/use-check-user";
const { check, go } = useCheck();
import { useTask } from "./composition";
import { report } from "packages/utils/tlog";
// const { t } = useI18n();
const props = defineProps<{
  data: Task;
}>();
const { checkTask } = useTask();
const renderPoints = () => {
  if (props.data.task_type === TaskType.Shop) {
    return `${props.data.points}/$${props.data.amount}`;
  }
  return props.data.points;
};
const onClick = (data: Task) => {
  if (data.task_type === TaskType.DailyCheckIn) {
    report.standalonesite_usercenterpage_checkin.cm_click();
  } else if (data.task_type === TaskType.GameLogin) {
    report.standalonesite_usercenter_playgame_btn.cm_click();
  }
  if (check()) {
    go();
    return;
  }
  checkTask(data);
};
</script>

<style lang="scss" scoped>
.icon-gift-mask {
  clip-path: polygon(0 0, 100% 0%, 50% 100%, 0% 100%);
}
</style>
