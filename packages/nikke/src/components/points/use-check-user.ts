import { useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { useUser } from "@/store/user";
import { useI18n } from "vue-i18n";
import { useAccountSwitch } from "@/composables/use-bind-lip";
import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { useQueryUserAward } from "@/api/rewards";
export const useCheck = () => {
  const user_store = useUser();
  const router = useRouter();
  const { show } = useToast();
  const { show: showDialog } = useDialog();
  const { t } = useI18n();
  const { lipLogin, thirdBindLip, updateBindedListInfo } = useAccountSwitch();
  const bindLipAccount = async () => {
    const lip_res = await lipLogin();
    const { channel_info } = lip_res;
    const res = await thirdBindLip({
      ...channel_info,
      uid: (channel_info as any).openid,
    });
    // show({ text: res.msg, type: "success" });
    if (res.ret === 0) {
      useQueryUserAward.run({});
    }
    await updateBindedListInfo();
    return show({ text: res.msg, type: "success" });
  };
  const go_login = () => {
    return router.push(Routes.LOGIN);
  };
  const go_bind = () => {
    showDialog({
      title: t("bind_lip_title"),
      content: t("bind_lip_tips"),
      confirm_text: t("bind"),
      callback(options: { value: any; close: () => void }) {
        if (["confirm"].includes(options.value)) {
          bindLipAccount();
        }
        options.close();
      },
    });
  };
  // 未登录 或 未绑定第三方账号
  const check = () => !user_store.is_login || !user_store.user_had_bound_lip;
  const go = () => {
    if (!user_store.is_login) {
      go_login();
    }
    if (!user_store.user_had_bound_lip) {
      go_bind();
    }
  };
  return {
    go,
    check,
    bindLipAccount,
  };
};
