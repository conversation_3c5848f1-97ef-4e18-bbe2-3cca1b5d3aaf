/// <reference types="vite-plugin-pwa/vue" />
/// <reference types="vite-plugin-pwa/vanillajs" />
/// <reference types="vite-plugin-pwa/info" />

import type { SpinePlayer } from "@esotericsoftware/spine-player";
import type * as Cmssdk from "@tencent/pa-cms-utils";
import Aegis from "aegis-web-sdk";

declare global {
  interface Window {
    VConsole: any;
    Cmssdk?: Cmssdk;
    OneTrust: any;
    aegis: Aegis;
    gtag?: any;
    spine: {
      SpinePlayer: typeof SpinePlayer;
    };
    spine4120?: {
      SpinePlayer: typeof SpinePlayer;
    };
    spine4028?: {
      SpinePlayer: typeof SpinePlayer;
    };
    // 在 index.html 定义
    ERROR_OPERATION_INTERCEPTE_MESSAGE: string;
    // 在 index.html 定义
    STANDALONE_SITE_VERSION: string;
    // 在 index.html 定义
    setCSSRootVHVariable: () => void;
    // 在 index.html 定义
    APP_BUILD_TIME: number | undefined;
    // 在 index.html 定义
    IS_DARK_MODE: boolean;
  }
}
