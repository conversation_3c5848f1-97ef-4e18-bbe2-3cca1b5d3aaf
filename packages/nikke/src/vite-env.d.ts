/// <reference types="vite/client" />
/// <reference path="node_modules/vue-i18n/dist/vue-i18n.d.ts" />
declare module "vue-i18n";
declare module "*.vue" {
  import { DefineComponent } from "vue";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

interface ImportMetaEnv {
  readonly VITE_APP_CDN_BASE_URL: string;
  readonly VITE_APP_TOOLS_CDN: string;
  readonly VITE_APP_BUILD_TIME: number;
  readonly VITE_VERSIOIN: string;
  readonly VITE_MODE: string;
  readonly VITE_APP_DISABLE_OBSPATH: "1" | "0";
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
