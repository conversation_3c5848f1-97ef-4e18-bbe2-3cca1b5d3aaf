import { defineStore } from "pinia";
import dayjs from "dayjs";
import { computed, ref, watch, watchEffect } from "vue";
import { useTimestamp } from "@vueuse/core";
import { Commodity, CommodityTag, CommodityType } from "packages/types/commodity";
import img_commodity from "@/assets/imgs/points/gift-card-events/commodity.png";
import { t } from "@/locales";
import { getStandardizedGameId } from "packages/utils/standard";
import {
  useGiftCardExchange,
  useGiftCardHasLeft,
  useUserGiftCardExchangeCount,
} from "@/api/gift-card";
import qs from "query-string";
import { useServerTime } from "@/api/system";

const MOCK_EVENTS_START_TIME = qs.parse(window.location.search)?.mock_events_start_time as
  | string
  | undefined;

const MOCK_EVENTS_VISIBLE_TIME = qs.parse(window.location.search)?.mock_events_visible_time as
  | string
  | undefined;

const EVENTS_VISIBLE_TIME = MOCK_EVENTS_VISIBLE_TIME ?? "2025-04-22T17:00:00+09:00"; // 活动可见时间(在活动开启日期之前)

const EVENTS_START_TIME = MOCK_EVENTS_START_TIME ?? "2025-04-25T11:00:00+09:00"; // 活动开始时间

const EVENTS_DAYS = 6;
export const GIFT_CARD_COMMODITY_ID = "__gift_card_commodity_id__";
const GIFT_CARD_PRICE = 10000;
const GIFT_CARD_DISCOUNT_PRICE = 1000;

export const usePointsGiftCardEventsStore = defineStore("points-gift-card-events", () => {
  const timestamp = useTimestamp();

  const server_time_query = useServerTime();
  const server_time_diff = computed(() => {
    if (!server_time_query.data.value || !server_time_query.dataUpdatedAt.value) return 0;
    const diff =
      server_time_query.dataUpdatedAt.value - server_time_query.data.value?.server_time * 1000;
    if (Math.abs(diff) < 1000) return 0;
    return diff;
  });

  /** 时间戳修正,确保时间与服务器时间一致 */
  const timestamp_fix = computed(() => {
    return timestamp.value - server_time_diff.value;
  });

  watchEffect(() => {
    if (server_time_diff.value !== 0) {
      console.log("[server_time_diff]", server_time_diff.value);
    }
  });

  /** 是否在活动中 */
  const is_in_events = computed(() => {
    return (
      dayjs(timestamp_fix.value).isAfter(dayjs(EVENTS_START_TIME)) &&
      dayjs(timestamp_fix.value).isBefore(dayjs(EVENTS_START_TIME).add(EVENTS_DAYS, "day"))
    );
  });

  /** 今天是活动的第几天 */
  const events_day_index = computed(() => {
    if (!is_in_events.value) return -1;
    return dayjs(timestamp_fix.value).diff(dayjs(EVENTS_START_TIME), "day");
  });

  const is_after_events = computed(() => {
    return dayjs(timestamp_fix.value).isAfter(dayjs(EVENTS_START_TIME).add(EVENTS_DAYS, "day"));
  });

  const is_before_events = computed(() => {
    return dayjs(timestamp_fix.value).isBefore(dayjs(EVENTS_START_TIME));
  });

  const is_before_events_visible = computed(() => {
    return dayjs(timestamp_fix.value).isBefore(dayjs(EVENTS_VISIBLE_TIME));
  });

  /**
   * 下一天的兑换时间
   * - 如果未开始，返回开始时间
   * - 如果活动中，返回下一天的开始时间
   * - 如果已经是最后一天，返回null
   * - 如果活动结束，返回null
   */
  const next_redeem_open_time = computed(() => {
    const is_before_start_day = dayjs(timestamp_fix.value).isBefore(dayjs(EVENTS_START_TIME));
    if (is_before_start_day) return dayjs(EVENTS_START_TIME);
    if (!is_in_events.value) return null;
    if (events_day_index.value === EVENTS_DAYS - 1) return null;
    return dayjs(EVENTS_START_TIME).add(events_day_index.value + 1, "day");
  });

  const has_left_info = ref<{
    present_group_left: { [key: string]: boolean };
  }>({
    present_group_left: { "1": false, "2": false, "3": false, "4": false },
  });

  const has_left_query = useGiftCardHasLeft({}, { enabled: false });
  const refetchHasLeftInfo = async () => {
    // if (is_before_events.value) return;
    const res = await has_left_query.refetch();
    has_left_info.value = res.data ?? {
      present_group_left: { "1": false, "2": false, "3": false, "4": false },
    };
  };

  watch(
    () => events_day_index.value,
    () => {
      has_left_info.value = {
        present_group_left: { "1": true, "2": true, "3": true, "4": true },
      };
    },
  );

  const {
    data: user_exchange_info,
    refetch: refetchUserExchangeInfo,
    isFetching: is_fetching_user_exchange_info,
  } = useUserGiftCardExchangeCount({});

  const gift_card_commodity = computed<Commodity | null>(() => {
    if (is_after_events.value || is_before_events_visible.value) return null;
    const has_left =
      has_left_info.value?.present_group_left?.[1] ||
      has_left_info.value?.present_group_left?.[2] ||
      has_left_info.value?.present_group_left?.[3] ||
      has_left_info.value?.present_group_left?.[4];
    return {
      account_exchange_limit: { limit_num: 1, limit_type: "gift-card-events" },
      commodity_desc: `<div class="whitespace-pre-wrap">${t("gift_card_commodity_desc")}</div>`,
      commodity_discount_price: GIFT_CARD_DISCOUNT_PRICE,
      commodity_has_left: has_left,
      commodity_is_discount: true,
      commodity_left_num: 1,
      commodity_name: t("gift_card_commodity_name"),
      commodity_pic_url: img_commodity,
      commodity_price: GIFT_CARD_PRICE,
      commodity_tag: CommodityTag.New,
      commodity_type: CommodityType.GiftCard,
      exchange_commodity_id: GIFT_CARD_COMMODITY_ID,
      exchange_commodity_type: 0,
      game_id: getStandardizedGameId(),
      has_collect: false,
      has_exchange_num: user_exchange_info.value?.user_has_present_count ?? 0,
      multiple_game_id_list: [""],
      commodity_discount_end_time: **********, // 25年年底，确保折扣能生效
    } satisfies Commodity;
  });

  const { mutateAsync: exchangeGiftCard, isPending: is_exchange_gift_card_loading } =
    useGiftCardExchange({
      onSuccess: () => {
        refetchUserExchangeInfo();
        refetchHasLeftInfo();
      },
    });

  const checkGiftCardUserCanExchange = async (config: { refetch: boolean }) => {
    if (is_fetching_user_exchange_info.value) await refetchUserExchangeInfo();
    if (config.refetch) {
      await Promise.all([refetchUserExchangeInfo(), refetchHasLeftInfo()]);
    }
    return {
      can: (user_exchange_info.value?.user_has_present_count ?? 0) < 1,
      has_num: user_exchange_info.value?.user_has_present_count ?? 0,
    };
  };

  const isGiftCardCommodity = (commodity: Commodity) => {
    return commodity.exchange_commodity_id === GIFT_CARD_COMMODITY_ID;
  };

  return {
    is_in_events,
    events_day_index,
    gift_card_commodity,
    isGiftCardCommodity,
    checkGiftCardUserCanExchange,
    has_left_info,
    user_exchange_info,
    exchangeGiftCard,
    is_exchange_gift_card_loading,
    next_redeem_open_time,
    refetchHasLeftInfo,
    is_before_events,
    is_after_events,
  };
});
