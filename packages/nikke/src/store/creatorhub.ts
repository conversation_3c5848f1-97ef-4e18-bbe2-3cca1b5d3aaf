import { defineStore } from "pinia";
import { useUser } from "./user";
import { useGetCreatorHubUserInfo, useChangeSyncStatus } from "@/api/creatorhub";
import { computed, ref, watch } from "vue";
export const useCreatorHubStore = defineStore("creatorhub", () => {
  const user_store = useUser();

  const {
    data: creatorhub_user,
    isLoading: creatorhub_user_loading,
    refetch: refetchCreatorHubUser,
  } = useGetCreatorHubUserInfo(
    {},
    { enabled: computed(() => !!user_store.user_info?.intl_openid) },
  );

  const changeSync = useChangeSyncStatus();

  const sync_status = ref(false);

  const handleChangeSyncStatus = async (status: boolean) => {
    sync_status.value = status;
    try {
      await changeSync.mutateAsync({ status: status ? 1 : 0 });
    } catch (error) {
      sync_status.value = !status;
    }
    await refetchCreatorHubUser();
  };

  watch(
    () => creatorhub_user.value?.is_auto_sync,
    (data) => {
      sync_status.value = data === 1;
    },
    { immediate: true },
  );

  return {
    creatorhub_user,
    creatorhub_user_loading,
    refetchCreatorHubUser,
    sync_status,
    handleChangeSyncStatus,
    async_status_loading: changeSync.isPending,
  };
});
