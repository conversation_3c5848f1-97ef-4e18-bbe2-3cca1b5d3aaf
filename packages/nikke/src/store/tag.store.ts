import { ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { usePostList } from "@/api/post";
import { PageInfo } from "packages/types/common";
import { Tag, TagParams } from "packages/types/home";
import { PostItem } from "packages/types/post";
import { getTag, useSearchTopic } from "@/api/topic";
import { dealPostData, removeDuplicatePost } from "@/utils/home";
import { cloneDeep } from "lodash-es";
import { getTags } from "@/api/home";
import { Tag as PostTag } from "packages/types/post";
import { useLanguageStore } from "./language";

export const useTagStore = defineStore(STORE_KEY.TAG, () => {
  const tagId = ref<string>("");
  const tagInfo = ref<Tag>();
  const all_tag_list = ref<PostTag[]>([]);
  const recommended_tag_list = ref<PostTag[]>([]);
  const search_tag_name = ref("");
  const search_tag_list = ref<PostTag[]>([]);
  const { lang_regions } = storeToRefs(useLanguageStore());
  const orderBy = ref<number>(2); // 0-tim 2-hot

  const postLoading = ref(false);

  const initPageInfo = {
    is_finish: false,
    next_page_cursor: "",
    previous_page_cursor: "",
  };
  const pageInfo = ref<PageInfo>(cloneDeep(initPageInfo));

  const init = async (id: string) => {
    tagId.value = id;
    getPostList();
    const detail = await getTag.run({ id });
    tagInfo.value = detail;
  };

  const resetData = (id: string) => {
    pageInfo.value = cloneDeep(initPageInfo);
    init(id);
  };

  const postList = ref<PostItem[]>([]);

  const getPostList = async (isMore = false) => {
    if (!isMore) pageInfo.value.next_page_cursor = "";

    postLoading.value = true;
    try {
      let { list, page_info } = await usePostList.run({
        nextPageCursor: pageInfo.value.next_page_cursor,
        search_type: 1,
        order_by: orderBy.value,
        tag_id: Number(tagId.value),
        limit: "10",
        regions: lang_regions.value,
      });
      list = dealPostData(list);
      postLoading.value = false;
      if (isMore) {
        postList.value = postList.value.concat(list);
      } else {
        postList.value = list;
      }
      postList.value = removeDuplicatePost(postList.value);
      pageInfo.value = page_info;
    } catch (err) {
      postList.value = [];
    }
  };

  const onLoadAllTagList = async () => {
    const { list } = await getTags.run({ limit: 100 });
    all_tag_list.value = list
      .map((item) => ({ id: +item.id, name: item.tag_name }))
      .filter((item) => Boolean(item.name));

    // console.log("[onLoadAllTagList] list", all_tag_list.value);
  };

  const onLoadRecommendedTagList = async (params: TagParams) => {
    const { list } = await getTags.run(params);

    recommended_tag_list.value = list
      .map((item) => ({ id: +item.id, name: item.tag_name }))
      .filter((item) => Boolean(item.name));

    // console.log("[onLoadRecommendedTagList] list", recommended_tag_list.value);
  };

  const onSearchTag = async (tag_name: string) => {
    search_tag_name.value = tag_name;

    const params = {
      tag_name: search_tag_name.value,
      next_page_cursor: "",
      limit: 50,
    };
    const { list } = await useSearchTopic.run(params);
    search_tag_list.value = list.map((item) => ({ id: +item.id, name: item.tag_name }));

    // console.log(`search_tag_list.value`, search_tag_list.value);
  };

  return {
    tagId,
    tagInfo,
    postList,
    postLoading,
    pageInfo,
    orderBy,
    all_tag_list,
    recommended_tag_list,
    search_tag_name,
    search_tag_list,
    init,
    getPostList,
    resetData,
    onLoadAllTagList,
    onLoadRecommendedTagList,
    onSearchTag,
  };
});
