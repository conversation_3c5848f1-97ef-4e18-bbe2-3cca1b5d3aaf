/**
 * 系统状态态
 */
import { ref } from "vue";
import { defineStore } from "pinia";
import { getStandardizedLang } from "packages/utils/standard";
export const useSystem = defineStore("system", () => {
  const filter_games = ref<string[]>(getInitGames()); // 筛选的game
  const language = getStandardizedLang();
  return {
    filter_games,
    language,
  };
});

const getInitGames = (): string[] => {
  // TODO gameid获取逻辑
  return ["29080"];
};
