import { Setting, PrivacySettings, ShiftyspadSetting } from "packages/types/setting";
import { ShiftyspadSettingValue } from "packages/types/setting";
import { UnReadMessageCounts } from "@/types/user";
import { RoutesName } from "@/router/routes";

import { useUnReadMessage } from "@/api/user";
import {
  useGetPrivacySetting,
  useModifyPrivacySetting,
  useModifyShiftyspadPrivacySetting,
} from "@/api/user-privacy";
import { useUser } from "./user";

import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { defineStore, storeToRefs } from "pinia";

const map = {
  show_my_posts: 1,
  show_my_collection: 2,
  show_my_follow: 3,
  show_my_fans: 4,
  show_my_game_card: 5,
  receive_tweet_email: 6,
  msg_comment_notify: 7,
  msg_like_notify: 8,
  msg_follow_notify: 9,
  msg_system_notify: 10,
  msg_activity_notify: 11,
  show_my_comment: 12,
  allow_friend_request_via_game_card: 13,
};

export const useSetting = defineStore("userSetting", () => {
  const { is_login } = storeToRefs(useUser());
  const user = useUser();
  const router = useRouter();

  const unReadMessageCounts = ref<UnReadMessageCounts>({
    comment_count: 0,
    follow_count: 0,
    like_count: 0,
    site_msg_count: 0,
  });

  // 是否有维度消息
  const is_unread = computed(() => {
    const counts = unReadMessageCounts.value;
    return (
      counts.comment_count + counts.follow_count + counts.like_count + counts.site_msg_count > 0
    );
  });

  const { data: privacy_settings, refetch: refresh } = useGetPrivacySetting(
    computed(() => ({ intl_openid: user.user_info?.intl_openid! })),
    { enabled: computed(() => !!user.user_info?.intl_openid) },
  );

  // 轮询获取消息数量
  const pollId = ref();
  const pollUnread = async () => {
    unReadMessageCounts.value = await useUnReadMessage.run({});
  };
  const cancelPoll = () => {
    clearInterval(pollId.value);
  };

  const init = async () => {
    if (!is_login.value) return; // 登录状态下才请求
    pollUnread();
    pollId.value = setInterval(() => pollUnread(), 60 * 1000);
  };

  const setting = computed(() => {
    const {
      msg_comment_notify = 0,
      msg_like_notify = 0,
      msg_follow_notify = 0,
      msg_system_notify = 1,
      msg_activity_notify = 0,
      show_my_posts = 0,
      show_my_collection = 0,
      show_my_fans = 0,
      show_my_follow = 0,
      show_my_game_card = 0,
      receive_tweet_email = 0,
      show_my_comment = 0,
      show_daily_info = ShiftyspadSettingValue.NoneVisible,
      show_outpost_info = ShiftyspadSettingValue.NoneVisible,
      show_resource_info = ShiftyspadSettingValue.NoneVisible,
      show_nikke_info = ShiftyspadSettingValue.NoneVisible,
      allow_friend_request_via_game_card = 0,
      show_union_info = ShiftyspadSettingValue.NoneVisible,
    } = privacy_settings.value || {};
    return {
      shiftyspad_settings: {
        show_daily_info,
        show_outpost_info,
        show_resource_info,
        show_nikke_info,
        show_union_info,
      },
      privacy_shows: {
        show_my_posts,
        show_my_comment,
        show_my_collection,
        show_my_fans,
        show_my_follow,
        show_my_game_card,
        allow_friend_request_via_game_card,
        receive_tweet_email,
      },
      notifications: {
        msg_comment_notify,
        msg_like_notify,
        msg_follow_notify,
        msg_system_notify,
        msg_activity_notify,
      },
    };
  });

  const check = () => {
    if (!is_login.value) {
      router.replace({
        name: RoutesName.LOGIN,
      });
      throw new Error("Invalid visit in setting page.");
    }
  };

  const updateShiftyspadSetting = async (params: ShiftyspadSetting) => {
    check();
    await useModifyShiftyspadPrivacySetting.run(params);
    await refresh();
  };

  const updateSetting = async (
    target: keyof Setting,
    key: keyof PrivacySettings,
    value: number,
  ) => {
    check();
    console.log("updateSetting", target, key, value);
    //@ts-ignore
    setting.value[target][key] = value;
    console.log("setting", setting.value);
    // 注意传参是是否关闭，而不是是否开启
    await useModifyPrivacySetting.run({ type: map[key], is_off: value === 1 ? 0 : 1 });
    refresh();
  };
  // refresh();
  return {
    setting,
    updateSetting,
    updateShiftyspadSetting,
    init,
    cancelPoll,
    refresh,
    unReadMessageCounts,
    is_unread,
  };
});
