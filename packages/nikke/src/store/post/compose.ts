// cpnts
import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog/index.ts";

// types
import { ComposeContentType } from "packages/types/content";
import { Platform, PlatId, PopCallbackValue, Status } from "packages/types/common";
import {
  ComposeNewAuthoringStatementAIContentType,
  ComposeNewAuthoringStatementRiskRemindType,
  ComposeNewAuthoringStatementType,
  ComposeNewContentItem,
  ComposeNewRequestParams,
  PostDetail,
  // GetVideoInfoByURLResponse,
  Tag,
} from "packages/types/post";

// configs
import {
  COMPOSE_MAX_TAG_LEN,
  EDITOR_EMOJI_IMAGE_HEIGHT,
  EDITOR_EMOJI_IMAGE_WIDTH,
  EMOJI_IMAGE_SYMBOL,
} from "@/configs/const";
import { STANDARD_CMS_LANG_MAP } from "packages/configs/standard";

// utils
import { defineStore } from "pinia";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { getStandardizedLang } from "packages/utils/standard";
import {
  decodeHTMLEntities,
  getTikTokVideoData,
  getTikTokVideoId,
  getVideoUrlPlatform,
  loadImageSize,
  safeJSONParse,
} from "packages/utils/tools";
import { extractImageSrc, extractTextFromHtml, filterTagFromHtml } from "packages/utils/dom";
import { useGetVideoInfoByURL, useCreatePostNew, useGetPost, useUpdatePost } from "@/api/post";
import router from "@/router";
import { Routes } from "@/router/routes";
import { cloneDeep, delay, get, isEmpty, omit, pick, set } from "lodash-es";
import { getPlates } from "@/api/home.ts";
import { urlSearchObjectify } from "packages/utils/qs";
import { report } from "packages/utils/tlog";
import { isSiteEmojiImage } from "@/utils/tools";
import { useSearchTopic } from "@/api/topic";
import { useUser } from "@/store/user";
import { usePostItem } from "@/composables/use-post.ts";
import { useTagStore } from "@/store/tag.store";

interface State {
  compose_params: ComposeNewRequestParams;
  compose_status: Status;
  active_content: ComposeNewContentItem;
  is_edit: boolean;
  post_detail: PostDetail;
}

// 获取参数填充默认值
const getDefaultPicUrls = () => {
  const pic_urls = urlSearchObjectify().pic_urls;
  if (pic_urls) {
    return pic_urls.split(",");
  }
  return [];
};

const getDefaultFrom = () => {
  const from = +urlSearchObjectify().from;
  if (Number.isInteger(from)) {
    return from;
  }
  return 0;
};

const getDefaultTags = () => {
  const tags = urlSearchObjectify().tags;
  if (tags) {
    return tags.split(",").map((tag: string) => +tag);
  }
  return [];
};

const getDefaultContentItem = (options?: { language: string }): ComposeNewContentItem => ({
  language: options?.language || getStandardizedLang(),
  title: "", //"Collaboration Commemoration Event",
  content: "", // "NIKKE x NieR:Automata Collab Commemoration Event begins!",
  ext_info: "",
  platform: Platform.youtube,
  pic_urls: getDefaultPicUrls() as string[],
  content_summary: "",

  // 前端字段
  fe_video_parse_status: Status.undo,
  fe_video_parse_info: {
    video_id: "",
    video_title: "",
    video_desc: "",
    video_cover: "",
    platform: "",
  },
});

// const getDefaultAuthoringStatement = (): ComposeNewAuthoringStatement => {
//   return {
//     creator_statement_type: ComposeNewAuthoringStatementType.no,
//     risk_remind_type: ComposeNewAuthoringStatementRiskRemindType.no,
//     ai_content_type: ComposeNewAuthoringStatementAIContentType.no,
//     original_url: "",
//   };
// };

const getDefaultState = (options?: { language: string }): State => ({
  compose_params: {
    plate_id: 0,
    type: "" as any as ComposeContentType,
    tags: getDefaultTags() as number[],
    publish_on: 0,
    contents: [getDefaultContentItem(options)],
    post_uuid: "",
    need_friend_card: false,
    from: getDefaultFrom(),
    need_refresh_friend_card: 0,
    creator_statement_type: ComposeNewAuthoringStatementType.no,
    risk_remind_type: ComposeNewAuthoringStatementRiskRemindType.no,
    ai_content_type: ComposeNewAuthoringStatementAIContentType.no,
    original_url: "",
    need_guild_card: false,
    need_refresh_guild_card: 0,
  } as ComposeNewRequestParams,

  compose_status: Status.undo,
  active_content: getDefaultContentItem(options),
  is_edit: false,
  post_detail: {} as PostDetail,
});

export const useComposePostStore = defineStore("post/compose", () => {
  const { t } = useI18n();
  const { show: toast } = useToast();
  const { show: showDialog } = useDialog();
  const user_store = useUser();
  const { getPostIsRelease } = usePostItem();
  const tag_store = useTagStore();

  const state = ref<State>(getDefaultState());
  const editor = ref();
  const plat_list = ref<any[]>([]);
  const emoji_select_panel_visible = ref(false);
  // for edit
  const original_post_detail = ref({});

  const computed_active_content = computed(() => onGetActiveContent());

  const emoji_select_panel_style = computed(() => {
    return {
      [ComposeContentType.richtext]: {},
      [ComposeContentType.image]: {},
      [ComposeContentType.video]: {},
    }[state.value.compose_params.type];
  });

  const is_straightly_close = computed(() => {
    let pick_arr_key: Array<string> = [];
    let key = `compose_params`;
    let bool = true;

    const getTypeHandleRes = (type: ComposeContentType, pick_arr_key: Array<string>) => {
      const check = () => {
        const obj = state.value.is_edit
          ? original_post_detail.value
          : getDefaultState({ language: getDefaultEditLang() });
        const default_compose_params = pick(get(obj, key), pick_arr_key);
        const state_compose_params = pick(get(state.value, key), pick_arr_key);

        const bool =
          JSON.stringify(default_compose_params) === JSON.stringify(state_compose_params);
        return bool;
      };

      const config = {
        [ComposeContentType.richtext]: check,
        [ComposeContentType.image]: check,
        [ComposeContentType.video]: check,
      };

      const handler = get(config, type, () => false);

      return handler();
    };

    switch (state.value.compose_params.type) {
      case ComposeContentType.richtext:
        pick_arr_key = ["title", "contents", "tags"];
        bool = getTypeHandleRes(ComposeContentType.richtext, pick_arr_key);
        break;

      case ComposeContentType.image:
        pick_arr_key = ["title", "contents", "tags", "pic_urls"];
        bool = getTypeHandleRes(ComposeContentType.image, pick_arr_key);
        break;

      case ComposeContentType.video:
        pick_arr_key = ["title", "contents", "tags", "ext_info"];
        bool = getTypeHandleRes(ComposeContentType.video, pick_arr_key);
        break;

      default:
    }

    return bool;
  });

  const can_publish = computed(() => {
    let bool = false;

    if (isEmpty(state.value.compose_params.contents)) {
      return false;
    }

    switch (state.value.compose_params.type) {
      case ComposeContentType.image:
        bool = state.value.compose_params.contents.every(
          (content_item: ComposeNewContentItem) =>
            Boolean(content_item.title) && !isEmpty(content_item.pic_urls),
        );
        break;
      case ComposeContentType.video:
        bool = state.value.compose_params.contents.every(
          (content_item: ComposeNewContentItem) =>
            Boolean(content_item.title) && content_item.fe_video_parse_status === Status.success,
        );
        break;

      case ComposeContentType.richtext:
        bool = state.value.compose_params.contents.every(
          (content_item: ComposeNewContentItem) =>
            Boolean(content_item.title) && Boolean(content_item.content),
        );
        break;

      default:
    }

    return bool;
  });

  const type_list = computed(() => {
    const graphic_item = {
      icon: "icon-picture2",
      title: t("graphics"),
      value: ComposeContentType.image,
    };
    const graphic_text_item = {
      icon: "icon-graphics-text",
      title: t("graphics_and_text"),
      value: ComposeContentType.richtext,
    };
    const video_item = {
      icon: "icon-video",
      title: t("video"),
      value: ComposeContentType.video,
    };

    const ret = {
      // outpost 支持图文和视频
      [PlatId.outpost]: [graphic_text_item, video_item],
      // guides 支持图文和视频
      [PlatId.guides]: [graphic_text_item, video_item],
      // nikkeart 支持图片和视频
      [PlatId.nikkeart]: [graphic_item, video_item],
      // outpost 支持图文和视频
      [PlatId.creatorhub]: [],
      // nikkeart 支持图片和视频
      [PlatId.event]: [],
      // official 支持图片和视频
      [PlatId.official]: [graphic_text_item, video_item],
      // recommend 模块
      [PlatId.recommend]: [],
    }[(current_plat_config.value.unique_identifier as PlatId) || PlatId.outpost];

    return ret;
  });

  const footer_invisible = computed(() => {
    return (
      state.value.compose_params.type === ComposeContentType.video &&
      onGetActiveContent().fe_video_parse_status !== Status.success
    );
  });

  const current_plat_config = computed(() => {
    return (
      plat_list.value.find((item: any) => item.id === state.value.compose_params.plate_id) || {}
    );
  });

  const current_type_config = computed(() => {
    return (
      type_list.value?.find((item) => item.value === state.value.compose_params.type) ||
      type_list.value?.[0]
    );
  });

  const getStateComposeParamsKey = (key: string) => get(state.value.compose_params, key);

  const setStateComposeParamsKey = (key: string, value: any) =>
    set(state.value.compose_params, key, value);

  const setStateKey = (key: string, value: any) => set(state.value, key, value);

  const onFindContentItemIndexByLang = (lang: string): number => {
    const index = state.value.compose_params.contents.findIndex(
      (content_item: ComposeNewContentItem) => content_item.language === lang,
    );
    return index;
  };

  const getDefaultEditLang = () => {
    const url_obj = urlSearchObjectify();
    const edit_lang = get(STANDARD_CMS_LANG_MAP, url_obj.edit_lang);
    if (edit_lang) {
      const index = onFindContentItemIndexByLang(edit_lang);
      return index > -1 ? edit_lang : index;
    }
    return getStandardizedLang();
  };

  const onEidtorInsertHtml = (html: string) => {
    editor.value?.insertHtml(html);
  };

  const onGetActiveContent = () => {
    return (
      (state.value.compose_params.contents.find(
        (item) => item.language === state.value.active_content.language,
      ) as ComposeNewContentItem) ||
      state.value.compose_params.contents?.[0] ||
      // 存在清空的情况，所以需要有一个默认值避免外部报错
      getDefaultContentItem()
    );
  };

  const onRemoveContentItemByLang = (lang: string) => {
    const active_content = onGetActiveContent();
    const index = onFindContentItemIndexByLang(lang);
    // delete
    state.value.compose_params.contents.splice(index, 1);
    if (active_content.language === lang) {
      // set new active content item
      onSetActiveContentItem(state.value.compose_params.contents[0] || getDefaultContentItem());
    }
  };

  const onSetActiveContentField = (key: string, value: string) =>
    set(onGetActiveContent(), key, value);

  const onSetActiveContentItem = (content_item: ComposeNewContentItem) => {
    state.value.active_content = content_item;
    onEidtorInsertHtml(content_item?.content);
  };

  const onAddContentItem = (language: string) => {
    const new_content_item = Object.assign(
      getDefaultContentItem({ language: getDefaultEditLang() }),
      {
        language,
      },
    );
    state.value.active_content = new_content_item;
    state.value.compose_params.contents.push(new_content_item);
    onEidtorInsertHtml(new_content_item.content);

    if (state.value.compose_params.type === ComposeContentType.video) {
      onGetActiveContent().fe_video_parse_status = Status.undo;
    }
  };

  const onChangeLang = (
    language: string,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    active_content.language = language;
  };

  const onAddPicUrls = (
    url: string,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    active_content.pic_urls.push(url);
  };

  const onRemovePicUrls = (
    url: string,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    const index = active_content.pic_urls.indexOf(url);
    active_content.pic_urls.splice(index, 1);
  };

  const onPicUrlsChange = (
    urls: Array<string>,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    active_content.pic_urls = urls;
  };

  const onTitleChange = (
    title: string,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    active_content.title = title;
  };

  const onContentChange = (
    content: string,
    active_content: ComposeNewContentItem | undefined = onGetActiveContent(),
  ) => {
    active_content.content = content;
  };

  const isNikkeart = (plat_type: PlatId) => plat_type === PlatId.nikkeart;

  const onChangePlat = (plate_id: number) => {
    console.log("[onChangePlat] plate_id", plate_id);

    const old_current_plat_config = { ...current_plat_config.value };
    const old_compose_params = { ...state.value.compose_params };
    const default_conetent_item = getDefaultContentItem({ language: getDefaultEditLang() });

    const new_plat_type = plat_list.value.find((item) => item.id === plate_id)?.unique_identifier;

    const is_between_outpost_and_guides =
      [PlatId.outpost, PlatId.guides].includes(
        old_current_plat_config.unique_identifier as PlatId,
      ) && [PlatId.outpost, PlatId.guides].includes(new_plat_type as PlatId);

    const isVideoContentType = (type: ComposeContentType) => type === ComposeContentType.video;
    const isOutpostOrGuides = (plat_type: PlatId) =>
      [PlatId.outpost, PlatId.guides].includes(plat_type);
    const isBetweenRichTextAndImage = (type: ComposeContentType) =>
      [ComposeContentType.richtext, ComposeContentType.image].includes(type);

    const handler = () => {
      // console.log(`[onChangePlat] hanlder plate_id`, plate_id);
      setEmojiSelectPanelVisible(false);
      state.value.compose_params.plate_id = plate_id;
      tag_store.onLoadRecommendedTagList({ limit: 10, plate_id: plate_id });

      if (
        // outpost/guides video <=> nikkeart video
        isVideoContentType(old_compose_params.type) ||
        // outpost <=> guides
        is_between_outpost_and_guides
      ) {
        return;
      }

      // 联动设置 type
      state.value.compose_params.type = get(type_list.value, "0.value") as ComposeContentType;

      console.log("[old plat]", old_current_plat_config.unique_identifier);
      console.log("[new plat]", current_plat_config.value.unique_identifier);

      // outpost/guides => nikkeart
      if (
        isOutpostOrGuides(old_current_plat_config.unique_identifier) &&
        isNikkeart(current_plat_config.value.unique_identifier)
      ) {
        // outpost/guides video => nikkeart image
        if (
          old_compose_params.type === ComposeContentType.video &&
          state.value.compose_params.type === ComposeContentType.image
        ) {
          state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
            onTitleChange(default_conetent_item.title, content_item);
            onContentChange(default_conetent_item.content, content_item);
          });
          return;
        }

        // outpost/guides richtext => nikkeart image
        if (
          old_compose_params.type === ComposeContentType.richtext &&
          state.value.compose_params.type === ComposeContentType.image
        ) {
          state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
            const pic_urls = extractImageSrc(content_item.content).filter(
              (url: string) => !isSiteEmojiImage(url),
            );
            pic_urls.splice(0, 10).forEach((url: string) => {
              if (!content_item.pic_urls.includes(url)) onAddPicUrls(url, content_item);
            });

            const new_content = filterTagFromHtml(content_item.content, "img");

            onContentChange(new_content, content_item);
          });

          return;
        }
      }

      if (
        isNikkeart(old_current_plat_config.unique_identifier) &&
        isOutpostOrGuides(current_plat_config.value.unique_identifier)
      ) {
        // nikkeart video => outpost/guides image
        if (
          old_compose_params.type === ComposeContentType.video &&
          state.value.compose_params.type === ComposeContentType.image
        ) {
          state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
            onTitleChange(default_conetent_item.title, content_item);
            onContentChange(default_conetent_item.content, content_item);
          });
          return;
        }

        // nikkeart image => outpost/guides richtext
        if (
          old_compose_params.type === ComposeContentType.image &&
          state.value.compose_params.type === ComposeContentType.richtext
        ) {
          state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
            onContentChange(
              content_item.pic_urls
                .map((url) =>
                  url.indexOf(EMOJI_IMAGE_SYMBOL) > -1
                    ? // emoji
                      `<img src="${url}" width="${EDITOR_EMOJI_IMAGE_WIDTH}" height="${EDITOR_EMOJI_IMAGE_HEIGHT}" />`
                    : // image
                      `<p><img src="${url}" /><p>`,
                )
                .join("") + content_item.content,
              content_item,
            );
            onPicUrlsChange([], content_item);
          });
          return;
        }
      }
    };

    if (
      is_straightly_close.value ||
      is_between_outpost_and_guides ||
      isVideoContentType(old_compose_params.type)
    ) {
      handler();
      return;
    }

    const dialog_title = t("alert");
    const dialog_content = isBetweenRichTextAndImage(old_compose_params.type)
      ? t("keep_the_current_content")
      : t("content_will_no_be_saved");
    const dialog_confirm_text = isBetweenRichTextAndImage(old_compose_params.type)
      ? t("keep")
      : t("sure");
    const dialog_cancel_text = isBetweenRichTextAndImage(old_compose_params.type)
      ? t("abandon")
      : t("cancel");

    showDialog({
      title: dialog_title,
      content: dialog_content,
      confirm_text: dialog_confirm_text,
      cancel_text: dialog_cancel_text,
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.confirm) {
          handler();
          close();
          return;
        }
        close();
      },
    });
  };

  const onChangeType = (
    type: ComposeContentType,
    config?: {
      /** 是否静默切换（不弹出确认框），默认：false */
      silent?: boolean;
      /** 是否隐藏 emoji 面板，默认：true */
      hide_emoji_panel?: boolean;
    },
  ) => {
    const old_current_type_config = { ...current_type_config.value };

    const isSilentSwithch = () => {
      return (
        config?.silent ||
        (!computed_active_content.value.title && !computed_active_content.value.content)
      );
    };

    const handler = () => {
      const default_state = getDefaultState({ language: getDefaultEditLang() });
      if (config?.hide_emoji_panel ?? true) {
        setEmojiSelectPanelVisible(false);
      }
      state.value.compose_params.type = type;

      // resest
      ["contents", "title", "pic_urls", "ext_info", "tags"].forEach((key) => {
        if (key === "contents") {
          state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
            // NOTE: language 必须是保留语言而不是默认语言
            Object.assign(
              content_item,
              omit(getDefaultContentItem({ language: getDefaultEditLang() }), ["language"]),
            );
          });
          return;
        }
        set(state.value.compose_params, key, get(default_state.compose_params, key));
      });

      if (
        (old_current_type_config.value === ComposeContentType.richtext &&
          current_type_config.value?.value === ComposeContentType.video) ||
        (old_current_type_config.value === ComposeContentType.image &&
          current_type_config.value?.value === ComposeContentType.video)
      ) {
        // 切换到 video 的时候，如果之前已经有解析过了，这里需要重置下状态回到解析页面
        state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
          if (content_item.fe_video_parse_status === Status.success) {
            content_item.fe_video_parse_status =
              default_state.compose_params.contents[0].fe_video_parse_status;
          }
        });
      }
    };

    if (isSilentSwithch()) {
      handler();
      return;
    }

    showDialog({
      title: t("alert"),
      content: t("content_will_no_be_saved"),
      confirm_text: t("sure"),
      cancel_text: t("cancel"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.confirm) {
          handler();
          close();
          return;
        }
        close();
      },
    });
  };

  const onTagChange = (tag: Tag) => {
    const index = state.value.compose_params.tags.indexOf(tag.id);
    // remove
    if (index > -1) {
      state.value.compose_params.tags.splice(index, 1);
      return;
    }

    if (state.value.compose_params.tags.length >= COMPOSE_MAX_TAG_LEN) {
      toast({
        text: t("exceed_max_topic_tips", [COMPOSE_MAX_TAG_LEN]),
        type: "error",
      });
      return;
    }
    // add
    state.value.compose_params.tags.push(tag.id);
  };

  const onTagRemove = (index: number) => {
    state.value.compose_params.tags.splice(index, 1);
  };

  const onParseLink = async (video_url: string) => {
    const active_content = onGetActiveContent();

    try {
      if (active_content.fe_video_parse_status === Status.loading) {
        return;
      }

      const platform = getVideoUrlPlatform(video_url);

      if (![Platform.youtube, Platform.tiktok].includes(platform)) {
        active_content.fe_video_parse_status = Status.error;
        toast({
          text: t("error_video_platform_parse_tips"),
          type: "error",
        });
        return;
      }

      active_content.fe_video_parse_status = Status.loading;

      if (platform === Platform.tiktok) {
        active_content.fe_video_parse_info = await getTikTokVideoData(video_url).then((json) => ({
          video_id: getTikTokVideoId(video_url) || "",
          video_title: json.title,
          video_desc: "",
          video_cover: json.thumbnail_url,
          platform: Platform.tiktok,
        }));
      } else {
        active_content.fe_video_parse_info = await useGetVideoInfoByURL.run({
          video_url,
          platform,
        });
      }

      // FIXME: 这样可以确保包含标签，youtube 解析到的文本是没有 html 标签的，可能会导致后台的热度计算复杂
      active_content.fe_video_parse_info.video_desc = `<p>${active_content.fe_video_parse_info.video_desc}</p>`;

      active_content.title = active_content.fe_video_parse_info.video_title;
      active_content.ext_info = JSON.stringify([active_content.fe_video_parse_info]);
      active_content.content = active_content.fe_video_parse_info.video_desc;

      state.value.active_content = active_content;
      onEidtorInsertHtml(active_content.content);

      active_content.fe_video_parse_status = Status.success;
    } catch (error) {
      active_content.fe_video_parse_status = Status.error;
      console.log("[onParseLink] error", error);
    }
  };

  const onLoadPlats = async () => {
    await user_store.waitingGetUserInfoFinish();

    const { list } = await getPlates.run({ page_type: 0, limit: 10 });
    const plate_type = urlSearchObjectify().plate_type || PlatId.outpost;
    // reset
    plat_list.value = [];

    const unique_identifier_index_map = {
      [PlatId.outpost]: 0,
      [PlatId.nikkeart]: 1,
      [PlatId.guides]: 2,
      [PlatId.official]: 3,
    };

    list
      .filter((item) => {
        return [PlatId.nikkeart, PlatId.outpost, PlatId.guides]
          .concat(
            // 只有官方和机构账号可以发布 official
            user_store.user_can_publish_multiple_language ? [PlatId.official] : [],
          )
          .includes(item.unique_identifier as PlatId);
      })
      .forEach((item) => {
        const new_item = Object.assign(item, {
          icon: `icon-${item.unique_identifier.toLocaleLowerCase()}`,
          title: item.plate_name,
          desc: t(`plat_${item.unique_identifier}_desc`) || item.plate_name,
        });

        // console.log("new_item", new_item);

        const index =
          unique_identifier_index_map[
            item.unique_identifier as keyof typeof unique_identifier_index_map
          ];
        plat_list.value.splice(index, 0, new_item);

        if (new_item.unique_identifier === plate_type) {
          onChangePlat(new_item.id);
        }
      });

    // console.log(`plat_list.value`, plat_list.value);
  };

  const onResetState = () => {
    state.value = getDefaultState({ language: getDefaultEditLang() });
  };

  const useComposeTag = () => {
    const search_tag_name = ref("");
    const search_tag_list = ref<Tag[]>([]);

    return {
      selected_tag_list: computed(() => state.value.compose_params.tags),
      search_tag_list: computed(() => search_tag_list.value),
      onTagChange(tag: Tag) {
        onTagChange(tag);
      },
      async onTagSearch(tag_name: string) {
        search_tag_name.value = tag_name;

        const params = {
          tag_name: search_tag_name.value,
          next_page_cursor: "",
          limit: 50,
        };
        const { list } = await useSearchTopic.run(params);
        search_tag_list.value = list.map((item) => ({ id: +item.id, name: item.tag_name }));

        // console.log(`search_tag_list.value`, search_tag_list.value);
      },
      onClearSearchTagName() {
        search_tag_name.value = "";
      },
    };
  };

  const setEmojiSelectPanelVisible = (bool: boolean) => {
    emoji_select_panel_visible.value = bool;
  };

  const onEditorFocus = () => setEmojiSelectPanelVisible(true);
  const onEditorBlur = () => setEmojiSelectPanelVisible(false);

  const resolvedPicUrl = async (pic_url: string) => {
    pic_url = decodeHTMLEntities(pic_url);
    const url_image_info = urlSearchObjectify(pic_url);
    if (url_image_info.height && url_image_info.width) {
      return pic_url;
    }
    const image_info = await loadImageSize(pic_url);
    return (pic_url += `?height=${image_info.height}&width=${image_info.width}`);
  };

  const onGetPostDetail = async (post_uuid: string) => {
    const data = await useGetPost.run({
      post_uuid,
      is_all_language: 1,
      browse_post: 2,
      original_content: 1,
    });
    state.value.post_detail = data;

    Object.keys(state.value.compose_params).forEach((key: string) => {
      if (key === "contents") {
        Object.assign(state.value.compose_params, {
          [key]: state.value.post_detail.content_languages,
        });
        const editor_lang = getDefaultEditLang();
        const index = onFindContentItemIndexByLang(editor_lang);

        // NOTE: 为图片加上宽高，方便在 nikkeart 瀑布流获取而非通过解析图片再获取
        state.value.compose_params.contents.forEach(async (item: ComposeNewContentItem) => {
          try {
            const resolved_pic_urls = await Promise.all(
              item.pic_urls.map(async (image_url: string) => await resolvedPicUrl(image_url)),
            );
            item.pic_urls = resolved_pic_urls;
          } catch (error) {
            console.error(`[resolvedPicUrl] catch error`, error);
          }
        });

        onSetActiveContentItem(state.value.compose_params.contents[index > -1 ? index : 0]);
        return;
      }

      if (key === "tags") {
        Object.assign(state.value.compose_params, {
          [key]: get(state.value.post_detail, key).map((item) => +item.id),
        });
        return;
      }

      if (key === "need_friend_card") {
        const value = get(state.value.post_detail, "friend_card.show_friend_card", false);
        setStateComposeParamsKey(key, value);
        if (value) {
          setStateComposeParamsKey("need_refresh_friend_card", 0);
        }
        return;
      }

      if (key === "need_guild_card") {
        const value = !!get(state.value.post_detail, "guild_card.guild_id", false);
        setStateComposeParamsKey(key, value);
        if (value) {
          setStateComposeParamsKey("need_refresh_guild_card", 0);
        }
        return;
      }

      if (key === "publish_on") {
        const is_release = getPostIsRelease(state.value.post_detail as any);
        setStateComposeParamsKey(key, is_release ? 0 : get(state.value.post_detail, key));
        return;
      }

      Object.assign(state.value.compose_params, { [key]: get(state.value.post_detail, key) });
    });

    state.value.is_edit = true;
    onEidtorInsertHtml(onGetActiveContent().content);

    // 如果是视频，那么需要切换到编辑状态
    if (state.value.compose_params.type === ComposeContentType.video) {
      state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
        content_item.fe_video_parse_status = Status.success;
        content_item.fe_video_parse_info =
          get(safeJSONParse(content_item.ext_info), 0) ||
          safeJSONParse(content_item.ext_info) ||
          null;
      });
    }

    original_post_detail.value = cloneDeep(state.value);

    console.log(`[onGetPostDetail] state.value.compose_params`, state.value.compose_params);
  };

  const onCompose = async () => {
    if (!can_publish.value) {
      return;
    }

    try {
      if (state.value.compose_status === Status.loading) {
        return;
      }
      state.value.compose_status = Status.loading;

      state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
        if (state.value.compose_params.type === ComposeContentType.richtext) {
          content_item.pic_urls = extractImageSrc(content_item.content).filter(
            (url: string) => !isSiteEmojiImage(url),
          );
          content_item.content_summary = extractTextFromHtml(content_item.content);
        }

        report.standalonesite_news_post_btn.cm_click({
          content: content_item.content,
          content_type: state.value.compose_params.type,
          label_id: state.value.compose_params.plate_id,
          label_name: current_plat_config.value.plate_name,
        });
      });

      const api = state.value.is_edit ? useUpdatePost : useCreatePostNew;
      const { post_data } = await api.run(state.value.compose_params);

      state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
        report.standalonesite_news_post_ret.cm_click({
          ret: 0,
          content: content_item.content,
          content_type: state.value.compose_params.type,
          label_id: state.value.compose_params.plate_id,
          label_name: current_plat_config.value.plate_name,
          content_id: post_data.post_uuid,
        });
      });

      toast({
        text: t("post_successfully"),
        type: "success",
      });

      state.value.compose_status = Status.success;

      delay(() => {
        // reset
        onResetState();
        router.replace({
          path: Routes.POST_DETAIL,
          query: {
            post_uuid: post_data.post_uuid,
          },
        });
      }, 1000);
    } catch (error: any) {
      state.value.compose_status = Status.error;

      state.value.compose_params.contents.forEach((content_item: ComposeNewContentItem) => {
        report.standalonesite_news_post_ret.cm_click({
          ret: error.code,
          content: content_item.content,
          content_type: state.value.compose_params.type,
          label_id: state.value.compose_params.plate_id,
          label_name: current_plat_config.value.plate_name,
          content_id: "",
        });
      });
    }
  };

  return {
    state,
    type_list,
    current_type_config,
    plat_list,
    current_plat_config,
    editor,
    footer_invisible,
    can_publish,
    is_straightly_close,
    emoji_select_panel_visible,
    emoji_select_panel_style,
    computed_active_content,

    onGetActiveContent,
    setEmojiSelectPanelVisible,
    onEditorFocus,
    onEditorBlur,
    onAddPicUrls,
    onRemovePicUrls,
    onTitleChange,
    onContentChange,
    onTagChange,
    onParseLink,
    onTagRemove,
    onCompose,
    onLoadPlats,
    onChangePlat,
    onChangeType,
    onChangeLang,
    onResetState,
    onAddContentItem,
    onSetActiveContentItem,
    onSetActiveContentField,
    onGetPostDetail,
    onRemoveContentItemByLang,
    getStateComposeParamsKey,
    setStateComposeParamsKey,
    setStateKey,
    onEidtorInsertHtml,
    isNikkeart,

    useComposeTag,
  };
});
