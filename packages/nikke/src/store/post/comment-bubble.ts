import { useGetAllCommentBubbleList } from "@/api/user-comment-bubble";
// import { UserCommentBubble } from "packages/types/user";
import { defineStore } from "pinia";
import { computed } from "vue";
// import mock_bubble_img from "@/assets/imgs/common/edit-avatarframe-bg.png";

/**
 * 评论气泡
 * - 获取所有的评论气泡配置
 * - 匹配目标评论的气泡配置
 * - 获取当前的评论气泡配置
 * - 设置当前的评论气泡配置
 */
export const useCommentBubble = defineStore("comment-bubble", () => {
  const { data, refetch } = useGetAllCommentBubbleList({});

  const all_comment_bubble = computed(() => {
    return data.value?.comment_bubbles ?? [];
  });

  const getCommentBubble = (comment_bubble_id: number | string | undefined) => {
    return all_comment_bubble.value?.find((item) => item.id == comment_bubble_id);
  };

  return {
    all_comment_bubble,
    getCommentBubble,
    refetch,
  };
});
