// cpnts
import { useToast } from "@/components/ui/toast";
import { usePop } from "@/components/post/detail/pop/index.ts";
import { useTopicPop } from "@/components/post/compose/pop/topic";
import { useAuthoringStatementPop } from "@/components/post/compose/pop/authoring-statement";

// types
import {
  ActionType,
  MovePostRequestParams,
  PostDeleteReason,
  PostDetail,
  PostTranslateStatus,
  Tag,
  TranslateContentType,
  ReportContentType,
} from "packages/types/post";
import { LikeType, StanceType } from "packages/types/stance";
import { PopCallbackValue } from "packages/types/common";
import { UserInfo } from "packages/types/user";
import { Plate } from "packages/types/home";

// configs
import { CODE_ALL_CONFIGS } from "packages/configs/code";

// utils
import { defineStore } from "pinia";
import {
  postCollection,
  postStar,
  useContentReport,
  useDeletePost,
  useGetPost,
  useMovePost,
  usePostChangeTagBind,
  useP<PERSON><PERSON>or<PERSON>,
  useSendAddFriendRequestByFriendCard,
  useTranslateContent,
  useUpdateStatement,
} from "@/api/post";
import { computed, ref } from "vue";
import { useFollowUser } from "@/api/user";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { t } from "@/locales";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { useRouter } from "vue-router";
import { getPlates } from "@/api/home";
import { useLSStorage } from "packages/utils/storage";
import { report } from "packages/utils/tlog";
import { getStandardizedLang } from "packages/utils/standard";
import { useMove } from "@/components/post/detail/pop/move/compostion.ts";
import { Routes } from "@/router/routes";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useTagStore } from "@/store/tag.store";
import { COMPOSE_MAX_TAG_LEN } from "@/configs/const";
import { get } from "lodash-es";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";
import { urlSearchObjectify } from "packages/utils/qs";
// import { t } from "@/locales";

const { show: showDialog } = useDialog();
const { show: toast } = useToast();
const { show: showPop } = usePop();
const { setStorage } = useLSStorage();
const { bindRole } = useBindRole();

export const getDefaultDetail = () =>
  ({
    user: {
      avatar: "",
      username: "",
      mood: "",
      tag_id: "",
      intl_openid: "",
    } as any as UserInfo,
    content: "",
    comment_count: 0,
    collection_count: 0,
    upvote_count: 0,
    tags: [] as Tag[],
    post_uuid: "",
    plate_id: 0,
    plate_name: "",
  }) as PostDetail;

export const usePostDetailStore = defineStore("post/detail", () => {
  const { show: showTopicPop } = useTopicPop();
  const { show: showAuthoringStatementPop } = useAuthoringStatementPop();
  const router = useRouter();
  const { getMoveList } = useMove();
  const tag_store = useTagStore();
  const editor = ref();
  const detail = ref<PostDetail>(getDefaultDetail());
  const plat_list: any = ref([]);
  const loading = ref({
    detail: false,
    like: false,
    compose: false,
    translate: false,
  });
  const translate_status = ref(PostTranslateStatus.un_translate);

  const stance_nums: any = computed(() => {
    return Object.entries(StanceType)
      .filter((item) => !Number.isInteger(+item[0]))
      .reduce((acc, cur: any) => {
        return {
          ...acc,
          [cur[0]]: detail.value.upvote_map?.[cur[1]],
        };
      }, {});
  });

  const post_detail_title = computed(() => {
    const target = plat_list.value.find((item: Plate) => +item.id === detail.value.plate_id);

    return target?.plate_name || "";
  });

  const onEidtorInsertHtml = (html: string) => {
    editor.value?.insertHtml(html);
  };

  const onLoadPlats = async () => {
    const { list } = await getPlates.run({ page_type: 0, limit: 10 });

    plat_list.value = list;
  };

  const onRestDetail = () => {
    detail.value = getDefaultDetail();
  };

  const onGetMoreIgnoreItemList = (item: PostDetail) => {
    const ret: any = [
      ActionType.top_comment,
      ActionType.bottom_comment,
      ActionType.copy_comment_id,
    ];

    const move_list = getMoveList({
      compose_content_type: item.type,
      plate_id: item.plate_id,
      is_official: item.is_official,
    });

    if (!move_list.length || !item.can_move) {
      ret.push(ActionType.move);
    }

    if (!item.can_edit_statement) {
      ret.push(ActionType.authoring_statement);
    }

    if (!item.can_update_tags) {
      ret.push(ActionType.hashtag_manage);
    }

    if (!item.can_edit) {
      ret.push(ActionType.edit);
    }

    if (!item.can_delete) {
      ret.push(ActionType.delete);
    }

    if (!item.can_report) {
      ret.push(ActionType.report);
    }

    return ret;
  };

  const onMoreItemClick = (item: PostDetail, type: ActionType) => {
    if (type === ActionType.delete) {
      // 自己删除，直接 confirm 确认
      if (item.is_mine) {
        return showDialog({
          title: t("delete"),
          content: t("are_you_sure_to_delete"),
          confirm_text: t("confirm"),
          cancel_text: t("cancel"),
          async callback(options: { value: PopCallbackValue; close: () => void }) {
            const { value, close } = options;
            if (value === PopCallbackValue.confirm) {
              await onDelete(item.post_uuid);
              close();
              router.back();
              return;
            }
            close();
          },
        });
      }

      // 管理员删除非自己发的帖子
      const dialog = showPop({
        type: "delete_reason",
        // 举报帖子
        async onDeleteConfirm(res: { text: string; type: PostDeleteReason }) {
          await onDelete(item.post_uuid, res.type);
          dialog.close();
          router.back();
        },
      });

      return;
    }

    if (type === ActionType.edit) {
      router.push({
        path: Routes.POST_COMPOSE,
        query: {
          post_uuid: item.post_uuid,
          edit_lang: getStandardizedLang(),
        },
      });
      return;
    }

    if (type === ActionType.hashtag_manage) {
      const selected_tag_list = ref(detail.value.tags.map((item) => +item.id));
      const topic_pop = showTopicPop({
        btn_visible: true,
        all_tag_list: tag_store.all_tag_list,
        recommended_tag_list: tag_store.recommended_tag_list,
        search_tag_list: computed(() => tag_store.search_tag_list),
        selected_tag_list,
        async onConfirm() {
          showDialog({
            title: t("save_the_hashtags"),
            content: t("confirm_to_edit_hashtag"),
            confirm_text: t("confirm"),
            cancel_text: t("cancel"),
            async callback(options: { value: PopCallbackValue; close: () => void }) {
              const { value, close } = options;
              if (value === PopCallbackValue.confirm) {
                await usePostChangeTagBind.run({
                  post_uuid: detail.value.post_uuid,
                  tag_ids: selected_tag_list.value,
                });
                toast({
                  text: t("successfully"),
                  type: "success",
                });
                close();
                onLoadDetail(detail.value.post_uuid, false);

                setTimeout(() => {
                  topic_pop.close();
                  event_emitter.emit(EVENT_NAMES.topic_detail_reset_list);
                }, 1000);
                return;
              }
              close();
            },
          });
        },
        onSearch: tag_store.onSearchTag,
        onChange(tag: Tag) {
          const index = selected_tag_list.value.indexOf(tag.id);
          // remove
          if (index > -1) {
            selected_tag_list.value.splice(index, 1);
            return;
          }

          if (selected_tag_list.value.length >= COMPOSE_MAX_TAG_LEN) {
            toast({
              text: t("exceed_max_topic_tips", [COMPOSE_MAX_TAG_LEN]),
              type: "error",
            });
            return;
          }
          // add
          selected_tag_list.value.push(tag.id);
        },
        onRemove(index: number) {
          selected_tag_list.value.splice(index, 1);
        },
      });
      return;
    }

    if (type === ActionType.authoring_statement) {
      const authoring_statement_keys = [
        "creator_statement_type",
        "risk_remind_type",
        "ai_content_type",
        "original_url",
      ];
      const authoring_statement_pop = showAuthoringStatementPop({
        btn_visible: true,
        authoring_statement: authoring_statement_keys.reduce((acc, key: string) => {
          return {
            ...acc,
            [key]: get(detail.value, key),
          };
        }, {}),
        async onConfirm(new_authoring_statement: any) {
          console.log(`[onConfirm] new_authoring_statement`, new_authoring_statement);

          await useUpdateStatement.run({
            post_uuid: detail.value.post_uuid,
            ...new_authoring_statement,
          });

          onLoadDetail(detail.value.post_uuid, false);
          authoring_statement_pop.close();
        },
      });
      return;
    }

    const dialog = showPop({
      type,
      // 举报帖子
      async onReportConfirm(res: { reason: string; type: PostDeleteReason }) {
        await useContentReport.run({
          content_type: ReportContentType.content,
          content_uuid: detail.value.post_uuid,
          report_type: res.type,
          reason: res.reason,
        });
        toast({
          text: t("report_successfully"),
          type: "success",
        });
        dialog.close();
      },

      // 移动帖子
      move_list: getMoveList({
        compose_content_type: detail.value.type,
        plate_id: detail.value.plate_id,
        is_official: detail.value.is_official,
      }),
      // 移动帖子的默认选中项
      default_move_confirm_value: {
        language: detail.value.language,
      },

      async onMoveConfirm(res: { plate_id: string; language: string }) {
        await useMovePost.run({
          post_uuid: detail.value.post_uuid,
          plate_id: res.plate_id || undefined,
          language: res.language,
        });
        toast({
          text: t("move_successfully"),
          type: "success",
        });
        dialog.close();

        onLoadDetail(detail.value.post_uuid, false);
      },
    });
  };

  /**
   * @description 加载帖子详情
   * @param post_uuid 帖子uuid
   * @param add_view_count 是否增加浏览量
   */
  const onLoadDetail = async (post_uuid: string, add_view_count: boolean) => {
    if (!post_uuid) {
      throw new Error("post_uuid is required");
    }

    if (loading.value.detail) {
      return;
    }

    const url_object = urlSearchObjectify();
    const original_content = url_object.original_content;

    try {
      loading.value.detail = true;
      detail.value = await useGetPost.run({
        post_uuid,
        browse_post: add_view_count ? 1 : 2,
        original_content: original_content ? 1 : 0,
      });
      translate_status.value = detail.value.is_original_content
        ? PostTranslateStatus.un_translate
        : PostTranslateStatus.translated;
    } catch (error) {
      console.error("[onLoadDetail] error: ", error);
    } finally {
      loading.value.detail = false;
    }

    tag_store.onLoadAllTagList();
    tag_store.onLoadRecommendedTagList({
      limit: 10,
      plate_id: detail.value.plate_id,
    });
  };

  const onFollow = async () => {
    const handler = async () => {
      const { is_follow, is_mutual_follow } = await useFollowUser.run({
        intl_openid: detail.value.user.intl_openid,
      });
      event_emitter.emit(EVENT_NAMES.user_status_change, {
        intl_openid: detail.value.user.intl_openid,
        is_followed: is_follow ? 1 : 0,
        is_mutual_follow: is_mutual_follow ? 1 : 0,
      });
      // reset
      detail.value.is_follow = is_follow;
      detail.value.is_mutual_follow = is_mutual_follow;

      report.standalonesite_news_follow_btn.cm_click({
        dst_open_id: detail.value.user.intl_openid,
        location: 0,
        content_id: detail.value.post_uuid,
        is_follow: Boolean(is_follow),
        label_id: detail.value.plate_id,
        label_name: "",
      } as any);
    };

    if (detail.value.is_follow || detail.value.is_mutual_follow) {
      return showDialog({
        title: t("unfollowed"),
        content: t("are_you_sure_to_unfollow"),
        confirm_text: t("keep_follow"),
        cancel_text: t("unfollow"),
        async callback(options: { value: PopCallbackValue; close: Function }) {
          const { value, close } = options;
          // 确认取消关注
          if (value === PopCallbackValue.cancel) {
            await handler();
          }
          close?.();
        },
      });
    }

    handler();
  };

  const onLike = async (post_uuid: string, type: StanceType, like_type?: LikeType | null) => {
    if (loading.value.like) {
      return;
    }
    try {
      loading.value.like = true;

      const params: any = {
        post_uuid,
        type: type || StanceType.like,
      };

      like_type && (params.like_type = like_type);

      await postStar.run(params);
      // refetch
      await onLoadDetail(post_uuid, false);

      report.standalonesite_news_praise_btn.cm_click({
        location: 0,
        content_id: detail.value.post_uuid,
        is_praise: Boolean(detail.value.my_upvote.is_star),
        label_id: detail.value.plate_id,
        label_name: "",
      } as any);
    } catch (error) {
      console.error(`[onLike] catch error :`, error);
    } finally {
      loading.value.like = false;
    }
  };

  const onCollection = async (post_uuid: string) => {
    await postCollection.run({ post_uuid });
    itemStatusAndCountHandler(detail.value, "is_collection", "collection_count");

    report.standalonesite_news_collect_btn.cm_click({
      location: 0,
      content_id: detail.value.post_uuid,
      label_id: detail.value.plate_id,
      label_name: "",
    });
  };

  const onPostForward = async (post_uuid: string) => {
    const { forward_count } = await usePostForward.run({ post_uuid });
    detail.value.forward_count = forward_count;
  };

  const onDelete = async (post_uuid: string, del_reason?: PostDeleteReason) => {
    await useDeletePost.run({ post_uuid, del_reason });
    setStorage("delete_post_uuid", post_uuid);
    toast({
      text: t("delete_successfully"),
      type: "success",
    });
  };

  const onMovePost = async (params: MovePostRequestParams) => {
    await useMovePost.run(params);

    toast({
      text: t("move_successfully"),
      type: "success",
    });
  };

  const toggleTranslateStatus = () => {
    translate_status.value =
      translate_status.value === PostTranslateStatus.un_translate
        ? PostTranslateStatus.translated
        : PostTranslateStatus.un_translate;
  };

  const resetTranslateStatus = () => {
    translate_status.value = PostTranslateStatus.un_translate;
  };

  const onTranslateContent = async () => {
    // 已经翻译过了
    if (detail.value.has_translated) {
      Object.assign(
        detail.value,
        detail.value.content !== detail.value.original_content ||
          // FIXME: 出现 content 为空的情况，也需要比较 title
          detail.value.title !== detail.value.original_title
          ? {
              title: detail.value.original_title,
              content: detail.value.original_content,
            }
          : {
              title: detail.value.translate_title,
              content: detail.value.translate_content,
            },
      );
      onEidtorInsertHtml(detail.value.content);
      toggleTranslateStatus();
      return;
    }

    try {
      loading.value.translate = true;

      Object.assign(detail.value, {
        original_title: detail.value.title,
        original_content: detail.value.content,
      });

      const lang = getStandardizedLang();

      const { title, content } = await useTranslateContent.run({
        type: TranslateContentType.post,
        content_uuid: detail.value.post_uuid,
        language: detail.value.is_original_content ? lang : detail.value.original_language,
      });

      Object.assign(detail.value, {
        title,
        content,
        has_translated: true,
        translate_title: title,
        translate_content: content,
      });

      onEidtorInsertHtml(detail.value.content);
      toggleTranslateStatus();
      loading.value.translate = false;
    } catch (error) {
      loading.value.translate = false;
      console.error("[onTranslateContent] catch an error: ", error);
    }
  };

  const onFriendCardRequest = async () => {
    // check is myself
    if (detail.value.is_mine) {
      toast({
        text: t("api_code_1200235"),
        type: "error",
      });
      return;
    }

    // 绑定角色
    const bindRoleHandler = () => {
      showDialog({
        title: t("account_not_bind_game_role_title"),
        content: t("account_not_bind_game_role_tips"),
        confirm_text: t("account_not_bind_game_role_confirm_btn_text"),
        cancel_text: t("close"),
        async callback(options: { value: PopCallbackValue; close: () => void }) {
          const { value, close } = options;
          if (value === PopCallbackValue.confirm) {
            close();
            await bindRole({ reload_page: false });
            onLoadDetail(detail.value.post_uuid, false);
            return;
          }
          close();
        },
      });
    };

    if (!detail.value.friend_card.role_name) {
      bindRoleHandler();
      return;
    }

    try {
      await useSendAddFriendRequestByFriendCard.run({
        post_uuid: detail.value.post_uuid,
      });

      detail.value.friend_card.is_send_friend_request = true;
      toast({
        text: t("friend_card_request_successfully"),
        type: "success",
      });
    } catch (error: any) {
      if (error?.code === CODE_ALL_CONFIGS.NotBindGameRole) {
        bindRoleHandler();
      }
    }
  };

  return {
    detail,
    translate_status,
    editor,
    stance_nums,
    post_detail_title,
    loading,
    onFollow,
    onLoadDetail,
    onLike,
    onCollection,
    onDelete,
    onGetMoreIgnoreItemList,
    onMoreItemClick,
    onPostForward,
    onMovePost,
    onRestDetail,
    onLoadPlats,
    onTranslateContent,
    resetTranslateStatus,
    onFriendCardRequest,
  };
});
