// cpnts
import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { usePop } from "@/components/post/detail/pop/index.ts";

// types
import {
  ComposeCommentParams,
  GetPostCommentsParams,
  GetPostCommentsResponseItem,
  GetPostCommentRepliesResponseItem,
  GetPostCommentRepliesParams,
  CommentType,
  TopBottomStatus,
} from "packages/types/comments";
import { ReportContentType, PostDeleteReason } from "packages/types/post";
import { ActionType, PostTranslateStatus, TranslateContentType } from "packages/types/post";
import { PageInfo, PopCallbackValue } from "packages/types/common";

// utils
import { defineStore, storeToRefs } from "pinia";
import {
  useBatchGetPostCommentReplies,
  useCommentStar,
  useDeleteComment,
  useGetCommentReplies,
  // useGetCommentsByPost,
  useGetCommentsByPostV2,
  useGetPostComment,
  usePostComment,
  useSetCommentTopOrBottom,
} from "@/api/comments";
import { nextTick, Ref, ref } from "vue";
import { useInfiniteList } from "@/composables/use-infinite-list";
import { urlSearchObjectify } from "packages/utils/qs";
import { t } from "@/locales";
import { itemStatusAndCountHandler, moveArrayItem } from "packages/utils/tools";
import { useContentReport, useTranslateContent } from "@/api/post";
import { isEmpty, set } from "lodash-es";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { Routes } from "@/router/routes";
import { getStandardizedLang } from "packages/utils/standard";
import { report } from "packages/utils/tlog";
import { usePostDetailStore } from "@/store/post/detail";
// import { useClipboard } from "@vueuse/core";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";
import { useStorage } from "@vueuse/core";
import { STORAGE_LS_POST_DETAIL_COMMENT_SORT } from "packages/configs/storage";
import { useUser } from "@/store/user";
interface State {
  compose_params: ComposeCommentParams;
  get_post_comments_params: GetPostCommentsParams;
  get_post_comments_page_info: PageInfo;
  comment_list: Array<GetPostCommentsResponseItem>;

  get_post_comment_replies_params: GetPostCommentRepliesParams;
  get_post_comment_replies_page_info: PageInfo;
  reply_list: Array<GetPostCommentRepliesResponseItem>;
}

const { show: toast } = useToast();
const { show: showDialog } = useDialog();
const { show: showPop } = usePop();
const { setIsDeletedValue } = useIsDeleted();

export const getDefaultState = (): State => ({
  compose_params: {
    pic_urls: [],
    content: "",
    post_uuid: "",
    comment_uuid: "",
    type: 0,
    users: [],
    comment_bubble_id: undefined,
  } as any as ComposeCommentParams,

  get_post_comments_params: {
    post_uuid: "",
    page_type: 0,
    previous_page_cursor: "",
    next_page_cursor: "",
    limit: 10,
    comment_reply_limit: 10,
  },
  get_post_comments_page_info: {
    next_page_cursor: "",
    previous_page_cursor: "",
    total: 0,
    is_finish: false,
  },
  comment_list: [],

  get_post_comment_replies_params: {
    comment_uuid: "",
    page_type: 0,
    previous_page_cursor: "",
    next_page_cursor: "",
    limit: 10,
  },
  get_post_comment_replies_page_info: {
    next_page_cursor: "",
    previous_page_cursor: "",
    total: 0,
    is_finish: false,
  },
  reply_list: [],
});

export const useComments = defineStore("comments", () => {
  const editor = ref();
  const state = ref<State>(getDefaultState());
  const post_detail_store = usePostDetailStore();
  const { user_info, is_login } = storeToRefs(useUser());

  const { detail } = storeToRefs(post_detail_store);

  const resolvedCommentList = async (list: GetPostCommentsResponseItem[]) => {
    list.forEach((item: GetPostCommentsResponseItem) => {
      setIsDeletedValue(item, false);
      set(item, "replies", null);
    });

    const comment_uuids = list.map((item) => item.comment_uuid);

    const { replies_map } = await useBatchGetPostCommentReplies.run({
      comment_uuids,
      limit: 3,
    });

    list.forEach((comment_item: GetPostCommentsResponseItem) => {
      const replies = replies_map[comment_item.comment_uuid];
      if (replies) {
        replies.data_list.forEach((item: GetPostCommentRepliesResponseItem) => {
          setIsDeletedValue(item, false);
        });
        comment_item.replies = replies;
      }
    });

    return list;
  };

  const resolvedReplyList = (list: GetPostCommentRepliesResponseItem[]) => {
    list.forEach((item: GetPostCommentRepliesResponseItem) => {
      setIsDeletedValue(item, false);
    });

    return list;
  };

  const onUpdateItemTopBottomStatus = (
    item: GetPostCommentsResponseItem | GetPostCommentRepliesResponseItem,
    top_bottom_status: TopBottomStatus,
  ) => {
    item.top_bottom_status = top_bottom_status;
  };

  const onGetMoreIgnoreItemList = (
    item: GetPostCommentsResponseItem | GetPostCommentRepliesResponseItem,
  ) => {
    const ret = [
      ActionType.move,
      ActionType.edit,
      ActionType.authoring_statement,
      ActionType.hashtag_manage,
    ];
    if (!item.can_delete) {
      ret.push(ActionType.delete);
    }

    if (!item.can_report) {
      ret.push(ActionType.report);
    }

    if (!item.can_bottom) {
      ret.push(ActionType.bottom_comment);
    }

    if (!item.can_top) {
      ret.push(ActionType.top_comment);
    }

    if (!is_login.value || (is_login.value && !user_info.value?.is_admin)) {
      ret.push(ActionType.copy_comment_id);
    }

    return ret;
  };

  const onMoreItemClick = (
    item: GetPostCommentsResponseItem | GetPostCommentRepliesResponseItem,
    type: ActionType,
    options?: {
      report_content_type?: ReportContentType;
      item_index?: number;
    },
  ) => {
    console.log(`[onMoreItemClick] options`, options);

    if (type === ActionType.copy_comment_id) {
      console.log(`[onMoreItemClick] comment_uuid`, item.comment_uuid);
      navigator.clipboard.writeText(item.comment_uuid).then(() => {
        toast({
          text: t("copy_link_success"),
          type: "success",
        });
      });
      return;
    }

    if ([ActionType.top_comment, ActionType.bottom_comment].includes(type)) {
      const current_sort = useStorage(STORAGE_LS_POST_DETAIL_COMMENT_SORT, 2);

      const content =
        type === ActionType.top_comment
          ? item.top_bottom_status === TopBottomStatus.top
            ? t("sure_to_cancel_toping")
            : t("sure_to_top_comment")
          : item.top_bottom_status === TopBottomStatus.bottom
            ? t("sure_to_cancel_bottoming")
            : t("sure_to_bottom_comment");

      return showDialog({
        title: t("actions"),
        content: content,
        confirm_text: t("confirm"),
        cancel_text: t("cancel"),
        async callback(callback_options: { value: PopCallbackValue; close: () => void }) {
          const { value, close } = callback_options;
          if (value === PopCallbackValue.confirm) {
            const item_index = options?.item_index || 0;
            if (type === ActionType.top_comment) {
              const new_top_bottom_status =
                item.top_bottom_status === TopBottomStatus.top
                  ? TopBottomStatus.unset
                  : TopBottomStatus.top;

              await onSetCommentTopOrBottom({
                comment_uuid: item.comment_uuid,
                top_bottom_status: new_top_bottom_status,
              });
              onUpdateItemTopBottomStatus(item, new_top_bottom_status);
              // 只有按照热度排序才需要更新列表
              +current_sort.value === 2 && moveArrayItem(state.value.comment_list, item_index, 0);
              item.top_bottom_status === TopBottomStatus.unset &&
                event_emitter.emit(EVENT_NAMES.post_detail_reset_comment_list);
            } else if (type === ActionType.bottom_comment) {
              const new_top_bottom_status =
                item.top_bottom_status === TopBottomStatus.bottom
                  ? TopBottomStatus.unset
                  : TopBottomStatus.bottom;
              await onSetCommentTopOrBottom({
                comment_uuid: item.comment_uuid,
                top_bottom_status: new_top_bottom_status,
              });
              onUpdateItemTopBottomStatus(item, new_top_bottom_status);
              // 只有按照热度排序才需要更新列表
              +current_sort.value === 2 &&
                (state.value.get_post_comments_page_info.is_finish
                  ? moveArrayItem(
                      state.value.comment_list,
                      item_index,
                      state.value.comment_list.length - 1,
                    )
                  : state.value.comment_list.splice(item_index, 1));
              item.top_bottom_status === TopBottomStatus.unset &&
                event_emitter.emit(EVENT_NAMES.post_detail_reset_comment_list);
            }

            nextTick(() => {
              toast({
                text:
                  +current_sort.value === 1
                    ? t("operation_succeeds_and_switch_to_hot_view")
                    : t("successfully"),
                type: "success",
              });
              close();
              // event_emitter.emit(EVENT_NAMES.post_detail_comment_head_scroll_into_view);
            });
            return;
          }
          close();
        },
      });
    }

    if (type === ActionType.delete) {
      if (item.is_mine) {
        return showDialog({
          title: t("delete"),
          content: t("are_you_sure_to_delete"),
          confirm_text: t("confirm"),
          cancel_text: t("close"),
          async callback(callback_options: { value: PopCallbackValue; close: () => void }) {
            const { value, close } = callback_options;
            if (value === PopCallbackValue.confirm) {
              await onDeleteComment(item.comment_uuid);
              setIsDeletedValue(item, true);
              close();
              return;
            }
            close();
          },
        });
      }

      // 管理员删除非自己发的帖子
      const dialog = showPop({
        type: "delete_reason",
        // 举报帖子
        async onDeleteConfirm(res: { text: string; type: PostDeleteReason }) {
          await onDeleteComment(item.comment_uuid, res.type);
          setIsDeletedValue(item, true);
          dialog.close();
        },
      });

      return;
    }

    const dialog = showPop({
      type,
      async onReportConfirm(res: { reason: string; type: PostDeleteReason }) {
        // const report_content_type = options?.report_content_type;
        // if (!report_content_type) {
        //   throw new Error("content type is empty");
        // }
        await useContentReport.run({
          content_type: ReportContentType.comment,
          content_uuid: item.comment_uuid,
          report_type: res.type,
          reason: res.reason,
        });
        toast({
          text: t("report_successfully"),
          type: "success",
        });
        dialog.close();
      },
    });
  };

  const onDeleteComment = async (comment_uuid: string, del_reason?: PostDeleteReason) => {
    await useDeleteComment.run({ comment_uuid, del_reason });
    toast({
      text: t("delete_successfully"),
      type: "success",
    });
  };

  const onSetCommentTopOrBottom = async (options: {
    comment_uuid: string;
    top_bottom_status: TopBottomStatus;
  }) => {
    await useSetCommentTopOrBottom.run(options);
  };

  const onGetPostCommment = async (comment_uuid: string) => useGetPostComment.run({ comment_uuid });

  const onGetPostCommments = (config?: { order_by?: Ref<number> }) =>
    useInfiniteList({
      queryFn: async ({ page_size, next_page_cursor, clear }) => {
        try {
          const url_object = urlSearchObjectify();
          const { list = [], page_info } = await useGetCommentsByPostV2.run(
            Object.assign(state.value.get_post_comments_params, {
              post_uuid: url_object.post_uuid,
              limit: page_size,
              order_by: config?.order_by?.value || 2, //{ time: 1, hot: 2 }[config?.sort?.value || "hot"] || 2,
              next_page_cursor: next_page_cursor,
            }),
          );

          state.value.comment_list = [
            //
            ...(clear ? [] : state.value.comment_list),
            ...(await resolvedCommentList(list)),
          ];
          state.value.get_post_comments_params.next_page_cursor = page_info.next_page_cursor;
          state.value.get_post_comments_page_info = page_info;

          return { list, page_info };
          // return { list, is_finish: page_info.is_finish, total: page_info.total || 0 };
        } catch (error: any) {
          console.error(error);
          return { list: [], total: 0, is_finish: true };
        }
      },
      item_key: "comment_uuid",
    });

  const onGetPostCommentReplies = () => {
    state.value.reply_list = [];
    return useInfiniteList({
      queryFn: async () => {
        try {
          const url_object = urlSearchObjectify();
          const { list = [], page_info } = await useGetCommentReplies.run(
            Object.assign(state.value.get_post_comment_replies_params, {
              comment_uuid: url_object.comment_uuid,
            }),
          );

          state.value.reply_list = [
            //
            ...state.value.reply_list,
            ...resolvedReplyList(list),
          ];
          state.value.get_post_comment_replies_params.next_page_cursor = page_info.next_page_cursor;
          state.value.get_post_comment_replies_page_info = page_info;
          return { list, is_finish: page_info.is_finish, total: page_info.total || 0 };
        } catch (error: any) {
          console.error(error);
          return { list: [], total: 0, is_finish: false };
        }
      },
      item_key: "comment_uuid",
    });
  };

  const onComposeComment = async (compose_params: ComposeCommentParams) => {
    const { comment } = await usePostComment.run(compose_params);

    toast({
      text: t("comment_successfully"),
      type: "success",
    });

    await post_detail_store.onLoadDetail(detail.value.post_uuid, false);

    // TODO: refactor code
    state.value.compose_params = getDefaultState().compose_params;

    if (compose_params.type === CommentType.comment) {
      state.value.comment_list.splice(0, 0, comment);
      return;
    }

    if (compose_params.type === CommentType.reply) {
      // 评论回复列表
      if (location.pathname === Routes.POST_COMMENTS) {
        state.value.reply_list.splice(0, 0, comment as any);
        return;
      }

      const target_comment = state.value.comment_list.find(
        (item) =>
          // @ts-ignore
          item.comment_uuid === (compose_params?.parent_uuid || compose_params.comment_uuid),
      );

      if (target_comment) {
        if (!target_comment.replies || isEmpty(target_comment.replies)) {
          target_comment.replies = {
            data_list: [],
            page_info: { total: 0 },
          } as any;
        }
        target_comment.replies.data_list.splice(0, 0, comment as any);
        target_comment.replies.page_info.total! += 1;
      }

      return;
    }

    console.error("error compose_params", compose_params);
  };

  const onCommentStar = async (
    item: GetPostCommentsResponseItem | GetPostCommentRepliesResponseItem,
    type: CommentType,
  ) => {
    await useCommentStar.run({ comment_uuid: item.comment_uuid, type });
    itemStatusAndCountHandler(item, "is_star", "upvote_count");

    report.standalonesite_news_praise_btn.cm_click({
      location: 0,
      content_id: item.post_uuid,
      comment_uuid: item.comment_uuid,
      is_praise: item.is_star,
      label_id: detail.value.plate_id,
      label_name: "",
    } as any);
  };

  const onInsertEmoji = (url: string) => {
    // console.log(`[onInsertEmoji] url`, url);
    editor.value.insertImage(url, "inline");
  };

  const onInsertImage = (url: string) => {
    state.value.compose_params.pic_urls.push(url);
  };

  const onRemoveImage = (index: number) => {
    state.value.compose_params.pic_urls.splice(index, 1);
  };

  const onResetState = () => {
    state.value = getDefaultState();
  };

  const onResetComposeComposeParams = () => {
    state.value.compose_params = getDefaultState().compose_params;
  };

  const toggleItemTranslateStatus = (item: GetPostCommentsResponseItem) => {
    item.translate_status =
      item.translate_status === PostTranslateStatus.un_translate
        ? PostTranslateStatus.translated
        : PostTranslateStatus.un_translate;
  };

  const onTranslateComment = async (item: GetPostCommentsResponseItem) => {
    // 已经翻译过了
    if (item.has_translated) {
      Object.assign(
        item,
        item.content !== item.original_content
          ? {
              content: item.original_content,
            }
          : {
              content: item.translate_content,
            },
      );
      toggleItemTranslateStatus(item);
      return;
    } else {
      Object.assign(item, {
        original_content: item.content,
        translate_status: PostTranslateStatus.translating,
      });
    }

    const { content } = await useTranslateContent.run({
      type: TranslateContentType.comment,
      content_uuid: item.comment_uuid,
      language: getStandardizedLang(),
    });

    Object.assign(item, {
      content,
      has_translated: true,
      translate_content: content,
      translate_status: PostTranslateStatus.translated,
    });
  };

  return {
    state,
    editor,
    onResetState,
    onComposeComment,
    onInsertEmoji,
    onInsertImage,
    onRemoveImage,
    onGetPostCommments,
    onCommentStar,
    onGetPostCommentReplies,
    onGetPostCommment,
    onDeleteComment,
    onMoreItemClick,
    onGetMoreIgnoreItemList,
    onResetComposeComposeParams,
    onTranslateComment,
  };
});
