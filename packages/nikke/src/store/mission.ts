// 一次性任务状态

import {
  MissionStatus,
  MissionTagId,
  useAddFinishOnboardingMission,
  useGetTaskListV2,
  useGetTaskListWithStatusV2,
  useHasFinishOnboardingMissionList,
  useOnboardingMissionGiftCollection,
} from "@/api/mission";
import { defineStore } from "pinia";
import { useUser } from "./user";
import { computed, ref, watch } from "vue";
import { useGetUserGamePlayerInfo } from "@/api/user-games";
import { useCms } from "@/composables/use-cms";
import DefaultImg from "@/assets/imgs/home/<USER>";
import default_banner_en from "@/assets/imgs/mission/banner-en.png";
import default_banner_ja from "@/assets/imgs/mission/banner-jp.png";
import default_banner_ko from "@/assets/imgs/mission/banner-kr.png";
import default_banner_tw from "@/assets/imgs/mission/banner-tw.png";
import default_banner_zh from "@/assets/imgs/mission/banner-cn.png";

import { CmsContentClass } from "packages/types/cms";
import { CMS_COLUMN_NAME_MISSION, CMS_COLUMN_NAME_MISSION_BANNER } from "packages/configs/cms";
import { IDetail } from "@tencent/pa-cms-utils";
import { filterCmsItem } from "packages/utils/cms";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { useQuery } from "@tanstack/vue-query";
import install from "@/boot/pwa-install";

export const useMissionStore = defineStore("mission", () => {
  const user = useUser();

  const lang = getStandardizedLang();
  const game_id = getStandardizedGameId();

  const {
    data: mission_status,
    isLoading: mission_status_loading,
    refetch: refetchMissionStatus,
  } = useHasFinishOnboardingMissionList(
    {},
    { enabled: computed(() => !!user.is_login), queryKey: [user.is_login] },
  );

  const receive = useOnboardingMissionGiftCollection();

  const receiveMissionGift = async (tag_id: MissionTagId) => {
    if (receive.isPending.value) return;
    await receive.mutateAsync({ tag_id }).finally(() => {
      // 刷新列表
      refetchMissionStatus();
    });
  };

  const finish_mission = useAddFinishOnboardingMission();
  const finishMission = async (tag_id: MissionTagId) => {
    if (finish_mission.isPending.value) return;
    await finish_mission.mutateAsync({ tag_id }).finally(() => {
      // 刷新列表
      refetchMissionStatus();
    });
  };

  // 玩家基本信息
  const {
    data: player_info,
    refetch: refetchPlayerInfo,
    isLoading: player_loading,
  } = useGetUserGamePlayerInfo(
    computed(() => ({ intl_openid: user.user_info.intl_openid })),
    { enabled: computed(() => !!user.user_info.intl_openid) },
  );

  const bannerList = ref<IDetail[]>([]); // 轮播图
  const { getSecondColumnData } = useCms({ cms_config: {} });

  const getBanners = async () => {
    const lang = getStandardizedLang();
    const default_banner = {
      en: default_banner_en,
      ja: default_banner_ja,
      zh: default_banner_zh,
      ko: default_banner_ko,
      "zh-TW": default_banner_tw,
    }[lang];
    const defaultList: any = [{ pic_urls: [default_banner], ext_info: { default: true } }];
    try {
      const { data } = await getSecondColumnData(
        {
          offset: 0,
          content_class: CmsContentClass.banner,
          get_num: 10,
        },
        {
          primary: CMS_COLUMN_NAME_MISSION,
          second: CMS_COLUMN_NAME_MISSION_BANNER,
        },
      );
      const items = (data?.info_content as IDetail[])?.filter((item) =>
        filterCmsItem({ ext_info: item.ext_info, lang, game_id }),
      );
      // 没有配置，手动添加兜底图
      if (!items?.length) {
        bannerList.value = defaultList;
      } else {
        bannerList.value = items;
      }
    } catch (err) {
      bannerList.value = defaultList;
    }
  };

  const {
    data: reward_task,
    isLoading: reward_task_list_loading,
    refetch: refetchRewardTask,
  } = useQuery({
    queryKey: ["reward_task"],
    enabled: false,
    queryFn: () => {
      // console.log({
      //   loading: user.loading,
      //   is_login: user.is_login,
      //   user_had_bound_lip: user.user_had_bound_lip,
      //   user_had_bind_lip_loading: user.user_had_bind_lip_loading,
      // });
      return user.is_login && user.user_had_bound_lip
        ? useGetTaskListWithStatusV2.run({ get_top: false, intl_game_id: game_id })
        : useGetTaskListV2.run({ get_top: false, intl_game_id: game_id });
    },
  });

  const reward_task_list = computed(() => {
    return reward_task?.value?.tasks ?? [];
  });

  // // 如果识别到从 PWA 打开，自动完成 添加到桌面任务（目前改为从任务中心点击去完成拉取浮层则立即完成）
  // watch(
  //   () => [mission_status.value, install.mode],
  //   () => {
  //     if (install.mode !== 'standalone') return;
  //     if (
  //       mission_status.value?.mission_done_list.find(
  //         (item) => item.tag_id === MissionTagId.ADD_TO_DESKTOP,
  //       )?.mission_status === MissionStatus.UNFINISHED
  //     ) {
  //       console.log("add to desktop mission auto finish!");
  //       finishMission(MissionTagId.ADD_TO_DESKTOP);
  //     }
  //   },
  //   { immediate: true },
  // );

  return {
    mission_list: computed(() => mission_status?.value?.mission_done_list ?? []),
    mission_status_loading,
    receiveMissionGift,
    refetchMissionStatus,
    finishMission,
    player_info,
    player_loading,
    refetchPlayerInfo,
    bannerList,
    getBanners,
    reward_task_list_loading,
    reward_task_list,
    refetchRewardTask: () => {
      if (reward_task_list_loading.value) return;
      return refetchRewardTask();
    },
  };
});
