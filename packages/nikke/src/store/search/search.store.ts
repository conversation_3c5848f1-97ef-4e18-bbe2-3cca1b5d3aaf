import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { ref, computed, watch } from "vue";
import { t } from "@/locales";
import { useHomeStore } from "@/store/home/<USER>";
import { PageInfo, PlatId } from "packages/types/common";
import { PostItem } from "packages/types/post.ts";
import { usePostList } from "@/api/post.ts";
import { useSearchUser } from "@/api/user";
import { useSearchTopic } from "@/api/topic";
import { highlight } from "@/utils/tools.ts";
import { useLSStorage } from "packages/utils/storage";
import { UserInfo } from "packages/types/user";
import { Tag } from "packages/types/home";
import { replaceUrlParams } from "packages/utils/qs.ts";
import { useLanguageStore } from "../language";
import { removeDuplicatePost } from "@/utils/home";

interface OrderByItem {
  name: string;
  value: number | string;
}

export const useSearchStore = defineStore(STORE_KEY.SEARCH.INDEX, () => {
  const storage = useLSStorage();
  const { lang_regions } = storeToRefs(useLanguageStore());
  const { tabList } = storeToRefs(useHomeStore());
  const postList = ref<PostItem[]>([]); // 动态列表(outpost or nikkeart)
  const topicList = ref<Tag[]>([]); // 搜索话题列表
  const userList = ref<UserInfo[]>([]); // 搜索用户列表

  const keyword = ref<string>("");
  const activePlate = ref();
  const activeOrderby = ref(2);
  const orderByList = ref<OrderByItem[]>([
    { name: t("hot"), value: 2 },
    { name: t("latest"), value: 1 },
  ]);

  const loading = ref(false);
  const pageInfo = ref<PageInfo>({
    is_finish: false,
    next_page_cursor: "",
  });

  // 下发的板块列表
  const allPlates = computed(() => {
    return [
      ...tabList.value.filter((item) => item.key !== PlatId.official),
      { name: "Topic", label: t("topic"), key: "Topic", value: 3 },
      { name: "User", label: t("user"), key: "User", value: 4 },
    ].filter((v) => !([PlatId.creatorhub, PlatId.event] as string[]).includes(v.key));
  });

  const current_acitve_plate = computed(() => {
    const target = allPlates.value.find((item) => item.value === activePlate.value)!;
    return target || {};
  });

  const activePlateKey = computed(() => {
    const target = allPlates.value.find((item) => item.value === activePlate.value)!;
    return target?.key || allPlates.value[0]?.key || "";
  });

  const queryKeyword = computed(() => {
    return (keyword.value as string)?.replace(/^\s+|\s+$/g, "");
  });

  const queryParams = computed(() => {
    return {
      search_type: 3, // 0：默认按时间降序，1：根据tag搜索，2：根据热度搜索，3：根据关键字搜索
      keyword: queryKeyword.value, // 关键字
      order_by: activeOrderby.value, // 排序类型，1：创建时间排序，2：按热度排序
      need_all_region: false, // 是否需要全部国家，false则会查询当前用户语言对应的动态数据
      plate_id: +activePlate.value, // 版块id,
      limit: "10",
      plate_unique_id: current_acitve_plate.value.key,
    };
  });

  const clickSearchHandle = (str: string) => {
    if (!str) return clearSearchResult();
    if (keyword.value !== str) {
      keyword.value = str;
      replaceUrlParams({ keyword: keyword.value });
    }
  };

  const loadMore = async () => {
    if (!pageInfo.value?.is_finish) {
      searchHandle(true);
    }
  };

  const clearSearchResult = () => {
    keyword.value = "";
    postList.value = [];
    topicList.value = [];
    userList.value = [];
    pageInfo.value.next_page_cursor = "";
  };

  const searchHandle = async (isHard = false) => {
    if (!keyword.value) return clearSearchResult();
    loading.value = true;
    if (!isHard) {
      postList.value = [];
      topicList.value = [];
      userList.value = [];
      pageInfo.value.next_page_cursor = "";
    }

    if (!["Topic", "User"].includes(activePlateKey.value)) {
      const { list = [], page_info } = await searchPost();
      postList.value = isHard ? postList.value.concat(list) : list;
      postList.value = removeDuplicatePost(postList.value);

      pageInfo.value = page_info;
    } else if (activePlateKey.value === "Topic") {
      const { list = [], page_info } = await searchTopic();

      topicList.value = isHard ? topicList.value.concat(list) : list;
      pageInfo.value = page_info;
    } else if (activePlateKey.value === "User") {
      const { list = [], page_info } = await searchUser();

      userList.value = isHard ? userList.value.concat(list) : list;
      pageInfo.value = page_info;
    }
    loading.value = false;

    historyHandle();
  };

  // 搜索帖子
  const searchPost = async () => {
    const params = {
      ...queryParams.value,
      ...pageInfo.value,
      regions: lang_regions.value,
    };
    let { list = [], page_info } = await usePostList.run(params);

    // 高亮处理
    list = list.map((item) => {
      item.title = highlight(item.title, queryKeyword.value, "text-[--brand-1] font-bold");
      item.content_summary = highlight(
        item.content_summary,
        queryKeyword.value,
        "text-[--brand-1] font-normal",
      );
      return item;
    });

    return { list, page_info };
  };

  // 搜索话题
  const searchTopic = async () => {
    const params = {
      tag_name: queryKeyword.value,
      next_page_cursor: pageInfo.value.next_page_cursor,
      limit: 20,
    };
    let { list = [], page_info } = await useSearchTopic.run(params);
    // 高亮处理
    list = list.map((item) => {
      const tag_name = `#${item.tag_name}`;
      item.tag_name = highlight(tag_name, queryKeyword.value, "text-[--brand-1] font-bold");
      return item;
    });
    return { list, page_info };
  };

  // 搜索用户
  const searchUser = async () => {
    const params = {
      user_name: queryKeyword.value,
      next_page_cursor: pageInfo.value.next_page_cursor,
      limit: 20,
    };
    let { list = [], page_info } = await useSearchUser.run(params);
    // 高亮处理
    list = list.map((item) => {
      item.username = highlight(item.username, queryKeyword.value, "text-[--brand-1] font-bold");
      return item;
    });
    return { list, page_info };
  };

  const historyHandle = () => {
    // 如果关键词为空，不记录到历史
    if (!queryKeyword.value) return;

    let historyKeyword = storage.getStorage("searchHistoryKeywordList");
    let historyKeywordList = historyKeyword ? JSON.parse(historyKeyword) : [];
    historyKeywordList = historyKeywordList.filter((item: string) => item !== queryKeyword.value);
    historyKeywordList.unshift(queryKeyword.value);
    historyKeywordList = historyKeywordList.slice(0, 10);
    storage.setStorage("searchHistoryKeywordList", JSON.stringify(historyKeywordList));
  };

  const changeOrderByHandle = (item: OrderByItem) => {
    activeOrderby.value = item.value as number;
  };

  const initActivePlates = () => {
    activePlate.value = allPlates.value[0]?.value || "";
  };

  watch(allPlates, initActivePlates);

  return {
    postList,
    keyword,
    queryParams,
    queryKeyword,
    activePlateKey,
    allPlates,
    pageInfo,
    loading,
    orderByList,
    activeOrderby,
    activePlate,
    topicList,
    userList,
    searchHandle,
    clickSearchHandle,
    loadMore,
    changeOrderByHandle,
    historyHandle,
    initActivePlates,
  };
});
