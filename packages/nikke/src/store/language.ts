import { useLang } from "@/composables/use-lang";
import { defineStore } from "pinia";
import { useUser } from "./user";
import { computed } from "vue";
import { useLocalStorage } from "@vueuse/core";
import { watch } from "vue";
import { STORAGE_LS_LOCAL_SAVED_REGIONS } from "packages/configs/storage";

export const useLanguageStore = defineStore("language", () => {
  const { lang_list } = useLang();
  const user = useUser();

  const local_saved_regions = useLocalStorage(STORAGE_LS_LOCAL_SAVED_REGIONS, [] as string[]);

  const lang_regions = computed(() => {
    if (user.is_login && user.user_info) {
      return user.user_info.regions ?? [];
    } else if (!user.is_login && !user.loading) {
      return local_saved_regions.value ?? [];
    } else {
      return undefined;
    }
  });

  const saveLocalRegions = (regions: string[]) => {
    local_saved_regions.value = regions.filter((i) => lang_list.value.some((j) => j.value === i));
  };

  // 用户信息变化时，保存到本地
  watch(
    () => JSON.stringify(user.user_info?.regions),
    () => {
      if (user.user_info?.regions) {
        saveLocalRegions(user.user_info.regions);
      }
    },
  );

  return {
    /**
     * 用户可见的语言地区列表
     * - 已登录并拿到用户信息时：使用用户设置
     * - 验证登录态和用户信息过程中: 留空（此时后端自己尝试去读取用户设置，发现是游客态时，认为是 all）
     * - 确认是游客态：读取本地保存
     */
    lang_regions,
    /**
     * 保存本地语言地区
     */
    saveLocalRegions,
  };
});
