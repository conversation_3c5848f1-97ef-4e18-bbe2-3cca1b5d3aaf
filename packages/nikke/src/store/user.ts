/**
 * 用户态
 */
// cpnts
import { useToast } from "@/components/ui/toast";

// configs
import {
  STANDARD_GAME_ID_KEY,
  STANDARD_URL_INGAME_ENCODE_TICKET,
  STANDARD_URL_INGAME_CHANNEL_ID,
  STANDARD_ACCOUNT_LANG_MAP,
} from "packages/configs/standard";
import { STORAGE_COOKIE_GAME_ID, STORAGE_LS_LOGIN_META } from "packages/configs/storage";

// apis
import { getPoints } from "@/api/rewards";
import { useApiCheckLogin, useApiLogin, useCheckHasLipAccount } from "@/api/login";
import { useGetIntlGameUserStatus, useGetMyProfile } from "@/api/user";
import { useGetUserGamePlayerInfo } from "@/api/user-games";

// types
import {
  GetUserGamePlayerInfoResponse,
  UserAuthType,
  UserGameAdultStatus,
  UserInfo,
  UserLoginStatus,
  UserParentCertificateStatus,
} from "packages/types/user";

// composables
import { useCms } from "@/composables/use-cms";

// utils
import { computed, ref, toRaw, watch } from "vue";
import { defineStore } from "pinia";
import { get, isEmpty, isUndefined, set } from "lodash-es";
import { useQuery } from "@tanstack/vue-query";
import qs from "query-string";
import { urlSearchObjectify } from "packages/utils/qs";
import { useLSStorage } from "packages/utils/storage";
import { getAccountConfigByGameId, useAccount } from "packages/utils/login";
import { getStandardizedLang, setStandardizedCookie } from "packages/utils/standard";

// import { useGrayscale } from "packages/utils/grayscale";
// import { GrayscaleKey } from "packages/types/grayscale";
// const { getGrayscale } = useGrayscale();
// const vconsole_visible = await getGrayscale(GrayscaleKey.vconsole, {
//   openid: user_info.value?.intl_openid || "",
// });
// if (vconsole_visible) {
//   console.log(`vconsole_visible`, vconsole_visible);
// }

export const useUser = defineStore("user", () => {
  const { cms_helper } = useCms();
  const { mutateAsync: apiLogin } = useApiLogin();
  const { setStorage } = useLSStorage();

  /** 检测登录状态中 */
  const is_checking = ref<boolean>(true);
  /** 是否已登录（未登录或正在检查登录状态时，为false，检测为已登录时为true） */
  const is_login = ref<boolean>(false as boolean);
  const total_point = ref<number>(0); // 总积分
  const start_point = ref<number>(0);
  const user_had_bind_lip_loading = ref<boolean>(true);
  const user_had_bound_lip = ref<boolean>(true); // 是否绑定过 lip
  const user_follow_config = ref({} as any);
  /** 对比 is_login，login status 可以扩展更多的状态 */
  const login_status = ref<UserLoginStatus>(UserLoginStatus.unlogin);
  const user_game_player_info = ref<GetUserGamePlayerInfoResponse>(
    {} as GetUserGamePlayerInfoResponse,
  );

  const intl_user_status = ref<{
    adult_check_status: UserGameAdultStatus;
    parent_certificate_status: UserParentCertificateStatus;
  }>({
    adult_check_status: UserGameAdultStatus.not_set,
    parent_certificate_status: UserParentCertificateStatus.parent_not_set,
  });

  const onUpdateIntlUserStatus = async () => {
    Object.assign(intl_user_status.value, await useGetIntlGameUserStatus.run({}));
  };

  const isFollow = (item: { intl_openid: string; is_follow: boolean }) => {
    const ret = user_follow_config.value[item.intl_openid];
    return isUndefined(ret) ? item.is_follow : ret;
  };

  const setFollowConfig = (item: { intl_openid: string; is_follow: boolean }) => {
    Object.assign(user_follow_config.value, {
      [item.intl_openid]: isUndefined(user_follow_config.value[item.intl_openid])
        ? !item.is_follow
        : !user_follow_config.value[item.intl_openid],
    });
  };

  // 心情状态列表
  const { data: mood_list, refetch: refetchMoodList } = useQuery({
    queryKey: ["mood_status_list"],
    queryFn: async () => {
      const res = await cms_helper.getCommonLibrary({
        page_seq: 1,
        page_size: 100,
        library_name: "nikke站状态管理",
      });
      const list = res.data.data_list as { json_data: string; library_data_id: number }[];
      return list.map((i) => ({
        library_data_id: i.library_data_id + "",
        ...paseJson<{ icon: string[]; name: string }>(i.json_data),
      }));
    },
  });

  const {
    data,
    isLoading: loading,
    refetch: refetchUserInfo,
  } = useGetMyProfile({}, { enabled: computed(() => is_login.value) });

  const user_info = ref({} as UserInfo);
  watch(
    () => data.value,
    async (val) => {
      if (val && val.info) {
        user_info.value = toRaw(val.info);
        window.aegis.setConfig({
          uin: user_info.value.intl_openid,
        });
        // 登录后立即查询积分
        refreshPoints();
        // 查询用户未成年人、家长认证等相关信息
        // onUpdateIntlUserStatus();

        wait_get_user_info_callback.forEach((cb) => cb(val.info));
        wait_get_user_info_callback.length = 0;
      }
    },
    { immediate: true },
  );

  // 设置是否登录
  const setIsLogin = (status: boolean) => (is_login.value = status);
  // 设置登录态状态
  const setLoginStatus = (status: UserLoginStatus) => (login_status.value = status);

  // 登录
  const checkLogin = async () => {
    is_checking.value = true;
    const res = await useApiCheckLogin
      .run({}, { ignore_toast: true })
      .catch((e) => {
        // 如果未登录，则不用再查询 LIP 是否绑定
        user_had_bind_lip_loading.value = false;
        throw e;
      })
      .finally(() => {
        is_checking.value = false;
        wait_check_login_callback.forEach((cb) => cb(is_login.value));
        wait_check_login_callback.length = 0;
      });

    console.log(`[checkLogin] res`, res);
    setIsLogin(true);
    setLoginStatus(UserLoginStatus.logined);
    checkHasLipAccount();
  };

  const checkHasLipAccount = async () => {
    user_had_bind_lip_loading.value = true;
    try {
      const { has_lip_account } = await useCheckHasLipAccount.run({});
      user_had_bound_lip.value = has_lip_account;
    } catch (error) {
      user_had_bound_lip.value = false;
    } finally {
      user_had_bind_lip_loading.value = false;
    }
  };

  const refreshPoints = async (pointer?: number) => {
    if (typeof pointer === "number") {
      start_point.value = total_point.value;
      total_point.value = Math.max(pointer, 0);
      return;
    }

    const res = await getPoints();
    total_point.value = Number(get(res, "total_points", 0));
    start_point.value = total_point.value;
  };

  const loginInGame = async () => {
    const query = urlSearchObjectify();
    const log_res = await apiLogin({
      game_openid: get(query, "openid"),
      game_token: get(query, "token"),
      game_channelid: +get(query, STANDARD_URL_INGAME_CHANNEL_ID),
      game_encodeparam: get(query, STANDARD_URL_INGAME_ENCODE_TICKET),
      game_id: get(query, STANDARD_GAME_ID_KEY),
      game_user_name: query.role_name || query.user_name,
    });
    console.log(`[loginInGame] log_res`, log_res);

    // 登录成功之后设置缓存
    setStandardizedCookie(STORAGE_COOKIE_GAME_ID, get(query, STANDARD_GAME_ID_KEY));
    setStorage(STORAGE_LS_LOGIN_META, JSON.stringify(log_res));
  };

  const loginFromWebCredential = async () => {
    const url_query = urlSearchObjectify();
    if (!url_query.web_credential) {
      throw new Error("web_credential is required");
    }
    const login_config: any = getAccountConfigByGameId(get(url_query, STANDARD_GAME_ID_KEY));

    console.log(`[loginWithWebCredential] login_config`, login_config);

    const { getAccountSdk } = useAccount(login_config);
    const account_api = await getAccountSdk({ accountPlatType: 131 });

    const web_credential = url_query.web_credential;

    const lip_lang = getStandardizedLang() as keyof typeof STANDARD_ACCOUNT_LANG_MAP;
    const captcha_lang = STANDARD_ACCOUNT_LANG_MAP[lip_lang];
    console.log(`[loginWithWebCredential] lip_lang`, lip_lang, captcha_lang);
    console.log(`[loginWithWebCredential] account_api`, account_api);
    console.log(account_api);
    const res: any = await account_api.credentialFromApp({
      captchaOption: {
        userLanguage: captcha_lang,
      },
    });

    console.log(`[loginWithWebCredential] login_config: `, login_config);
    console.log(`[loginWithWebCredential] web_credential`, web_credential);
    console.log(`[loginWithWebCredential] res`, res);

    if (!get(res, "channel_info.channelId", "")) {
      // 写死 131
      set(res, "channel_info.channelId", 131);
    }

    if (res.ret === 0) {
      await useApiLogin.run({
        game_channelid: res.channel_info?.channelId,
        game_openid: res.channel_info?.openid,
        game_token: res.channel_info?.token,
        game_id: login_config.gameID,
        game_expire_time: res.channel_info?.expire_ts,
      });

      // 登录成功后保存必要信息
      setStorage(STORAGE_LS_LOGIN_META, JSON.stringify(res));

      // 解析 URL
      const parsed_url = qs.parseUrl(window.location.href);
      // 删除 web_credential 参数
      delete parsed_url.query.web_credential;
      // 重新构建 URL
      const new_url = qs.stringifyUrl(parsed_url);
      window.location.replace(new_url);
    } else {
      const { show } = useToast();
      console.error(`[loginWithWebCredential] login failed`, res);
      show({ text: `web_credential 登录失败: ${res.msg}`, type: "error", interval: 4000 });
      throw new Error("login failed");
    }
  };

  const wait_check_login_callback = [] as ((is_login: boolean) => void)[];
  /** 等待获取是否登录的结果，返回 is_login */
  const waitLoginCheckFinish = async () => {
    if (is_checking.value) {
      return new Promise<boolean>((resolve) => {
        wait_check_login_callback.push((v) => resolve(v));
      });
    } else {
      return is_login.value;
    }
  };

  const wait_get_user_info_callback = [] as ((user_info: UserInfo) => void)[];
  const waitingGetUserInfoFinish = async () => {
    if (!isEmpty(user_info.value)) {
      return user_info.value;
    }
    return new Promise<UserInfo>((resolve) => {
      wait_get_user_info_callback.push((user_info: UserInfo) => resolve(user_info));
    });
  };

  const user_can_publish_multiple_language = computed(() =>
    [UserAuthType.official, UserAuthType.institution].includes(user_info.value.auth_type),
  );

  const onGetUserGamePlayerInfo = async () => {
    await waitingGetUserInfoFinish();

    if (!is_login.value) {
      return;
    }

    const res = await useGetUserGamePlayerInfo.run({
      intl_openid: user_info.value.intl_openid,
    });

    user_game_player_info.value = res;
  };

  return {
    is_checking,
    total_point,
    start_point,
    user_info,
    is_login,
    login_status,
    loading,
    // setUserInfo,
    mood_list,
    user_had_bound_lip,
    user_can_publish_multiple_language,
    user_game_player_info,
    user_had_bind_lip_loading,
    intl_user_status,

    checkHasLipAccount,
    setIsLogin,
    setLoginStatus,
    refreshPoints,
    checkLogin,
    loginInGame,
    refetchUserInfo,
    refetchMoodList,
    isFollow,
    setFollowConfig,
    waitLoginCheckFinish,
    waitingGetUserInfoFinish,
    loginFromWebCredential,
    onGetUserGamePlayerInfo,
    onUpdateIntlUserStatus,
  };
});

const paseJson = <T = unknown>(str: string) => {
  try {
    return JSON.parse(str) as T;
  } catch (error) {
    return null;
  }
};
