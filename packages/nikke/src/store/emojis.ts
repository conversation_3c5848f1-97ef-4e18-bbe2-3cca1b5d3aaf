// api
import { useGetAllEmoticons } from "@/api/emojis";
import { EmojisType, IconListItem, ListItem } from "packages/types/emojis";

// utils
import { defineStore } from "pinia";
import { computed, ref, toRaw } from "vue";
import { useIndexDB } from "packages/utils/storage.ts";
import { STORAGE_INDEXDB_HISTORY_EMOJIS } from "packages/configs/storage";
import { EMOJI_HISTORY_MAX } from "@/configs/const";

export const useEmojis = defineStore("emojis", () => {
  const { updateCache, getCache } = useIndexDB();

  // flat
  const emojis = ref<string[]>([]);
  const history_emojis = ref<string[]>([]);
  const emojis_select_index = ref(-1);
  const history_emojis_select_index = ref(-1);
  const list = ref<Array<ListItem>>([]);
  const groups = ref<Array<{ id: string; pic_url?: string; icon_name?: string }>>([
    {
      id: EmojisType.history,
      pic_url: "",
      icon_name: "icon-history-clock",
    },
  ]);
  const group_id = ref("");

  const current_group_list = computed(() => {
    if (group_id.value === EmojisType.history) {
      return history_emojis.value;
    }
    const target = list.value.find((item) => item.id === group_id.value);
    if (target) {
      return target.icon_list.map((icon: IconListItem) => icon.pic_url);
    }
    return [];
  });

  getCache(STORAGE_INDEXDB_HISTORY_EMOJIS).then((options) => {
    options?.value && (history_emojis.value = options?.value);
  });

  const onChangeGroupId = (id: string) => {
    group_id.value = id;
  };

  const onLoadEmojis = async () => {
    emojis.value = [];
    list.value = [];
    groups.value = [
      {
        id: EmojisType.history,
        pic_url: "",
        icon_name: "icon-history-clock",
      },
    ];
    group_id.value = "";

    const { list: res } = await useGetAllEmoticons.run({});

    list.value = res;
    list.value.forEach((item: ListItem) => {
      const icon_list = item.icon_list.map((icon: IconListItem) => icon.pic_url);
      emojis.value.push(...icon_list);
      groups.value.push({ id: item.id, pic_url: item.pic_url });
    });

    group_id.value = res.length ? res[0].id : "history";
  };

  const onSelectEmoji = (item: { emoji: string; index: number }, type: EmojisType) => {
    if (type === EmojisType.list) {
      emojis_select_index.value = item.index;

      const index = history_emojis.value.findIndex((emojis) => emojis === item.emoji);
      index > -1
        ? history_emojis.value.splice(index, 1)
        : history_emojis.value.splice(EMOJI_HISTORY_MAX - 1);
      history_emojis.value.unshift(item.emoji);
    } else {
      history_emojis_select_index.value = item.index;
    }

    updateCache({
      key: STORAGE_INDEXDB_HISTORY_EMOJIS,
      value: toRaw(history_emojis.value),
    });
  };

  return {
    groups,
    group_id,
    emojis,
    history_emojis,
    emojis_select_index,
    history_emojis_select_index,
    current_group_list,
    onChangeGroupId,
    onLoadEmojis,
    onSelectEmoji,
  };
});
