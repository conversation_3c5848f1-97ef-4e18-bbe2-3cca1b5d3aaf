import {
  JoinGuildRequest,
  UnionCard,
  useGetMyGuildInfo,
  useJoinGuild,
  usePublishGuildCard,
} from "@/api/union";
import { defineStore } from "pinia";
import { useUser } from "./user";
import { computed, ref, watch } from "vue";
import { useDialog } from "@/components/ui/dialog";
import { t } from "@/locales";
import { PopCallbackValue } from "packages/types/common";
import { useUserStore as useShiftyspadUserStore } from "@/shiftyspad/stores/user";
import { useToast } from "@/components/ui/toast";
import { CODE_ALL_CONFIGS } from "packages/configs/code";
import { RoutesName } from "@/router/routes";
import { useComposePostStore } from "./post/compose";
import { useRouter } from "vue-router";
import { useGetActivityPostTags } from "@/api/topic";
import { sleep } from "packages/utils/tools";

export const useUnionStore = defineStore("union", () => {
  const user_store = useUser();
  const shiftyspad_user_store = useShiftyspadUserStore();
  const router = useRouter();
  const compose_store = useComposePostStore();
  const { show: showToast } = useToast();
  const join_union_pending_set = ref(new Set<string>());

  const { data: static_tags } = useGetActivityPostTags(
    {},
    { enabled: computed(() => user_store.is_login) },
  );
  const { show: showConfirm } = useDialog();
  const refetch_my_guild_ignore_toast = ref(true);
  const {
    data: my_guild_data,
    refetch: refetchMyGuildInfo,
    isFetched: is_my_guild_info_fetched,
    error: my_guild_error,
  } = useGetMyGuildInfo(
    computed(() => ({ ignore_toast: refetch_my_guild_ignore_toast.value })),
    { enabled: false },
  );

  const getMyGuildInfo = async (
    config: { latest: boolean; ignore_toast?: boolean } = { latest: false, ignore_toast: true },
  ) => {
    if (!config.latest && is_my_guild_info_fetched.value) return my_guild_data.value?.card;
    await user_store.waitLoginCheckFinish();
    if (!user_store.is_login) return undefined;
    refetch_my_guild_ignore_toast.value = config.ignore_toast ?? true;
    const query = await refetchMyGuildInfo();
    refetch_my_guild_ignore_toast.value = true;
    if (query.error) {
      throw query.error;
    }
    return query.data?.card;
  };

  const my_union = computed(() => my_guild_data.value?.card);

  /** 是否为我的公会 */
  const isMyUnion = (guild_id: string) => {
    return my_union?.value?.guild_id === guild_id;
  };

  const canShareToSquare = (guild_id: string) => {
    return isMyUnion(guild_id) && my_union?.value?.is_published === false;
  };

  const canShareToPost = (guild_id: string) => {
    return isMyUnion(guild_id) && my_union?.value?.is_published === true;
  };

  const canJoinUnion = (guild_id: string) => {
    return !isMyUnion(guild_id) && shiftyspad_user_store.self_shifty_user.has_role;
  };

  const { mutateAsync: publishToSquare } = usePublishGuildCard({
    onSuccess: () => {
      refetchMyGuildInfo();
      showConfirm({
        title: t("union_post_success"),
        content: t("share_to_post_detail"),
        confirm_text: t("share_to_post"),
        cancel_text: t("close"),
        callback(options) {
          options.close();
          if (options.value === PopCallbackValue.confirm) {
            shareToPost();
          }
        },
      });
    },
  });

  const { mutateAsync: join } = useJoinGuild({
    onSuccess: (_data, variables: JoinGuildRequest) => {
      join_union_pending_set.value.add(variables.guild_id);
      refetchMyGuildInfo();
      showToast({
        text: t("join_union_detail", [variables.guild_name]),
        type: "success",
      });
    },
    onError(error: { code: number }, variables) {
      join_union_pending_set.value.add(variables.guild_id);
      if (error.code === CODE_ALL_CONFIGS.JoinGuildNeedApproval) {
        showToast({
          text: t("api_code_1303019", [variables.guild_name]),
          type: "success",
        });
      }
    },
  });
  const joinUnion = (guild: UnionCard) => {
    const role_id = shiftyspad_user_store.self_shifty_user.user_role_info?.role_id;
    const area_id = shiftyspad_user_store.self_shifty_user.user_role_info?.area_id;
    if (!guild.guild_id || !role_id || !area_id) {
      return;
    }
    if (join_union_pending_set.value.has(guild.guild_id)) return;
    return join({
      guild_id: guild.guild_id,
      nikke_area_id: guild.nikke_area_id,
      guild_name: guild.guild_name,
    });
  };

  const role_info = computed(() => {
    return shiftyspad_user_store.self_shifty_user.user_role_info?.role_id
      ? shiftyspad_user_store.self_shifty_user.user_role_info
      : null;
  });

  const shareToPost = async () => {
    await router.push({ name: RoutesName.POST_COMPOSE });
    await waitPlatInit();
    const tag_id = static_tags.value?.tags.find((item) => item.tag_code === "guild_post")?.tag_id;
    compose_store.state.compose_params.tags = tag_id ? [Number(tag_id)] : [];
    compose_store.setStateComposeParamsKey("need_guild_card", true);
  };

  /** 等待帖子发布页 Plat 初始化 */
  const waitPlatInit = async () => {
    await waitCondition({
      condition: () => compose_store.state.compose_params.plate_id !== 0,
      interval: 50,
      timeout: 10000,
    });
  };

  const isJoinUnionPending = (guild_id: string) => {
    return join_union_pending_set.value.has(guild_id);
  };

  watch(
    () => user_store.is_login,
    async () => {
      if (user_store.is_login) shiftyspad_user_store.initSelfRole();
    },
    { immediate: true },
  );

  return {
    my_union,
    isMyUnion,
    canShareToSquare,
    canShareToPost,
    canJoinUnion,
    publishToSquare,
    joinUnion,
    role_info,
    shareToPost,
    isJoinUnionPending,
    getMyGuildInfo,
    my_guild_error,
  };
});

const waitCondition = async (config: {
  condition: () => boolean;
  interval: number;
  timeout: number;
}) => {
  const start_time = Date.now();
  while (Date.now() - start_time < config.timeout) {
    if (config.condition()) {
      console.log(`wait condition success in ${Date.now() - start_time}ms`);
      return true;
    }
    await sleep(config.interval);
  }
  return false;
};
