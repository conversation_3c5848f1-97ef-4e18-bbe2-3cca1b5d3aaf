// types
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "packages/types/grayscale";
// utils
import { ref } from "vue";
import { defineStore } from "pinia";
import { get } from "lodash-es";
import { useUser } from "@/store/user";
import { useCDNConfigs } from "packages/utils/cdn";
import { useGrayscale } from "packages/utils/grayscale";
import { getStandardizedGameId } from "packages/utils/standard";

export const useLaboratory = defineStore("laboratory", () => {
  const user_store = useUser();

  const laboratory_entrance_visible = ref(false);
  const laboratory_midas_visible = ref(false);
  const laboratory_midas_url = ref("");

  const { getCDNConfigs } = useCDNConfigs();
  const intl_game_id = getStandardizedGameId();

  const checkLaboratoryEntranceVisible = async () => {
    await user_store.waitingGetUserInfoFinish();
    const { getGrayscale } = useGrayscale();
    const visible = await getGrayscale(GrayscaleKey.laboratory, {
      openid: user_store.user_info.intl_openid,
    });
    console.log("[checkLaboratoryEntranceVisible] visible", visible);
    laboratory_entrance_visible.value = visible;
  };

  const checkLaboratoryMidas = async () => {
    await user_store.waitingGetUserInfoFinish();
    const { laboratory } = getCDNConfigs();
    const { openids, url } = get(laboratory, "midas", {
      openids: [""],
      url: {},
    });

    console.log("[checkLaboratoryMidas] openids", openids);
    console.log("[checkLaboratoryMidas] url", url);
    console.log("[checkLaboratoryMidas] user_info.intl_openid", user_store.user_info.intl_openid);

    laboratory_midas_visible.value = openids.includes(user_store.user_info.intl_openid);
    laboratory_midas_url.value = get(url, intl_game_id);

    console.log("[checkLaboratoryMidas] laboratory_midas_visible", laboratory_midas_visible.value);
    console.log("[checkLaboratoryMidas] laboratory_midas_url", laboratory_midas_url.value);
  };

  return {
    checkLaboratoryEntranceVisible,
    checkLaboratoryMidas,

    laboratory_entrance_visible,
    laboratory_midas_visible,
    laboratory_midas_url,
  };
});
