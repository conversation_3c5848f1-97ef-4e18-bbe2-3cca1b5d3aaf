import { ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { District, Tag } from "packages/types/home";
import { getDistrictList, getTags } from "@/api/home";
import { usePostList } from "@/api/post";
import { useHomeStore } from "./home.store";
import { PageInfo } from "packages/types/common";
import { OrderBy, PostItem } from "packages/types/post";
import { dealPostData, removeDuplicatePost } from "@/utils/home";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { filterCmsItem } from "packages/utils/cms";
import { useLanguageStore } from "../language";

export const useHomeOutPostStore = defineStore(STORE_KEY.HOME.OUTPOST, () => {
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const districtList = ref<District[]>([]); // 金刚区配置
  const { lang_regions } = storeToRefs(useLanguageStore());

  // 金刚区配置
  const kingList = ref<District[]>([]);

  // 获取金刚区数据
  const getDistrictData = async () => {
    const res = await getDistrictList.run({
      plate_id: Number(plateId.value),
      limit: 10,
    });
    const game_id = getStandardizedGameId();
    const lang = getStandardizedLang() || "en";
    const list = res.list
      .sort((x, y) => Number(x.order) - Number(y.order))
      .filter((item) => filterCmsItem({ ext_info: item.ext_info, lang, game_id }));
    kingList.value = list;
  };

  const orderBy = ref<OrderBy>(2); // 0-tim 2-hot

  const tagList = ref<Tag[]>([]);
  const activeTag = ref<string>("");

  // 获取标签数据
  const getTagData = async () => {
    const res = await getTags.run({
      plate_id: Number(plateId.value),
      limit: 100,
    });
    tagList.value = res.list.map((item) => {
      return {
        id: item.id,
        tag_name: item.tag_name,
      };
    });
  };

  const postLoading = ref(false);
  const pageInfo = ref<PageInfo>({
    is_finish: false,
    next_page_cursor: "",
    previous_page_cursor: "",
  });

  const postList = ref<PostItem[]>([]);

  const getPostList = async (isMore = false) => {
    if (!isMore) pageInfo.value.next_page_cursor = "";

    postLoading.value = true;
    try {
      let { list, page_info } = await usePostList.run({
        search_type: +activeTag.value ? 1 : 0, // 选择了tag，search_type=1
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: pageInfo.value.next_page_cursor,
        order_by: orderBy.value,
        tag_id: +activeTag.value || undefined,
        limit: "10",
        regions: lang_regions.value,
      });
      list = dealPostData(list);

      if (isMore) {
        postList.value = postList.value.concat(list);
      } else {
        postList.value = list;
      }
      postList.value = removeDuplicatePost(postList.value);
      pageInfo.value = page_info;
    } catch (error) {
      console.error(error);
    } finally {
      postLoading.value = false;
    }
  };

  return {
    districtList,
    kingList,
    tagList,
    postList,
    postLoading,
    activeTag,
    pageInfo,
    orderBy,
    getPostList,
    getDistrictData,
    getTagData,
  };
});
