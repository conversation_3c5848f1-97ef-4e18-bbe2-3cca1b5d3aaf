import { ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { Tag } from "packages/types/home";
import { getTags } from "@/api/home";
import { usePostList } from "@/api/post";
import { useHomeStore } from "./home.store";
import { PageInfo } from "packages/types/common";
import { OrderBy, PostItem } from "packages/types/post";
import { dealPostData, removeDuplicatePost } from "@/utils/home";
import { useLanguageStore } from "../language";

export const useHomeEventStore = defineStore(STORE_KEY.HOME.EVENT, () => {
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const { lang_regions } = storeToRefs(useLanguageStore());

  const orderBy = ref<OrderBy>(1); // 1-tim 2-hot

  const tagList = ref<Tag[]>([]);
  const activeTag = ref<string>("");

  // 获取标签数据
  const getTagData = async () => {
    const res = await getTags.run({
      plate_id: Number(plateId.value),
      limit: 100,
    });
    tagList.value = res.list.map((item) => {
      return {
        id: item.id,
        tag_name: item.tag_name,
      };
    });
  };

  const postLoading = ref(false);
  const pageInfo = ref<PageInfo>({
    is_finish: false,
    next_page_cursor: "",
    previous_page_cursor: "",
  });

  const postList = ref<PostItem[]>([]);

  const getPostList = async (isMore = false) => {
    if (!isMore) pageInfo.value.next_page_cursor = "";

    postLoading.value = true;
    try {
      let { list, page_info } = await usePostList.run({
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: pageInfo.value.next_page_cursor,
        order_by: orderBy.value,
        limit: "10",
        platform: "youtube",
        regions: lang_regions.value,
      });
      list = dealPostData(list);

      if (isMore) {
        postList.value = postList.value.concat(list);
      } else {
        postList.value = list;
      }
      postList.value = removeDuplicatePost(postList.value);
      pageInfo.value = page_info;
    } catch (error) {
      console.error(error);
    } finally {
      postLoading.value = false;
    }
  };

  return {
    tagList,
    postList,
    postLoading,
    activeTag,
    pageInfo,
    orderBy,
    getPostList,
    getTagData,
  };
});
