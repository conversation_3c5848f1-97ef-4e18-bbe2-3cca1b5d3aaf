import { ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store";
import { Tag } from "packages/types/home";
import { useHomeStore } from "@/store/home/<USER>";
import { getTags } from "@/api/home";
import { PageInfo } from "packages/types/common";
import { usePostList } from "@/api/post";
import { dealPostData, removeDuplicatePost } from "@/utils/home";
import { OrderBy, PostItem } from "packages/types/post";
import { usePostItem } from "@/composables/use-post.ts";
import { useLanguageStore } from "../language";

export const useHomeNikkeArtStore = defineStore(STORE_KEY.HOME.NIKKE_ART, () => {
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const { resolvePostItemImageInfo } = usePostItem();
  const { lang_regions } = storeToRefs(useLanguageStore());
  const tagList = ref<Tag[]>([]);

  // 获取标签数据
  const activeTag = ref<string>("");
  const getTagData = async () => {
    const res = await getTags.run({
      plate_id: Number(plateId.value),
      limit: 100,
    });
    tagList.value = res.list.map((item) => {
      return { id: item.id, tag_name: item.tag_name };
    });
  };

  const orderBy = ref<OrderBy>(2); // 0-tim 2-hot
  const allRegion = ref(false);
  const postLoading = ref(false);
  const pageInfo = ref<PageInfo>({
    is_finish: false,
    next_page_cursor: "",
    previous_page_cursor: "",
  });
  const postList = ref<PostItem[]>([]);

  const getPostList = async (isMore = false) => {
    if (!isMore) pageInfo.value.next_page_cursor = "";
    postLoading.value = true;
    try {
      let { list, page_info } = await usePostList.run({
        search_type: +activeTag.value ? 1 : 0, // 选择了tag，search_type=1
        // need_all_region: allRegion.value,
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: pageInfo.value.next_page_cursor,
        order_by: orderBy.value,
        tag_id: +activeTag.value || undefined,
        limit: "10",
        regions: lang_regions.value,
      });

      list = await Promise.all(dealPostData(list).map(resolvePostItemImageInfo));

      if (isMore) {
        postList.value = postList.value.concat(list);
      } else {
        postList.value = list;
      }
      postList.value = removeDuplicatePost(postList.value);
      pageInfo.value = page_info;
    } catch (error) {
      console.error(error);
    } finally {
      postLoading.value = false;
    }
  };

  const resetPostList = () => {
    postList.value = [];
  };

  return {
    tagList,
    postList,
    postLoading,
    activeTag,
    allRegion,
    pageInfo,
    orderBy,
    resetPostList,
    getPostList,
    getTagData,
  };
});
