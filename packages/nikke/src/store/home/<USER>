import { ref } from "vue";
import { defineStore } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { useGrayscale } from "packages/utils/grayscale";
import { useCDNConfigs } from "packages/utils/cdn";
import { Gray<PERSON>leKey } from "packages/types/grayscale";

export const useAnniversary = defineStore(STORE_KEY.HOME.ANN, () => {
  // 是否是周年庆
  const anniversary = ref(true);
  const anniversary_bg_config = ref({
    bg_image: "",
    bg_color: "",
  });

  const { getCDNConfigs } = useCDNConfigs();

  const handleAnniversary = async () => {
    const { getGrayscale } = useGrayscale();
    const visible = await getGrayscale(GrayscaleKey.annual_event, {});
    console.log("[handleAnniversary] visible", visible);
    anniversary.value = visible;

    const { annual_event } = await getCDNConfigs();
    if (annual_event) {
      const { value, configs } = annual_event;
      const target_confg = configs.find((config) => config.value === value);
      target_confg && (anniversary_bg_config.value = target_confg?.bg_config);
    }

    console.log(`[handleAnniversary] anniversary_bg_config`, anniversary_bg_config.value);
  };

  handleAnniversary();

  return {
    anniversary,
    anniversary_bg_config,
  };
});
