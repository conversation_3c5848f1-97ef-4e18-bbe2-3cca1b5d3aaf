import { ref } from "vue";
import { defineStore } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { getPlates } from "@/api/home.ts";
import { useCms } from "@/composables/use-cms.ts";
import { CMS_COLUMN_NAME_HOME, CMS_COLUMN_NAME_HOME_BANNER } from "packages/configs/cms.ts";
import { Notice, Plate, PlateLangItem } from "packages/types/home";
import { CmsContentClass } from "packages/types/cms";
import { IDetail } from "@tencent/pa-cms-utils/dist/types/cms";
import { TabItem } from "packages/types/tabs";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import DefaultImg from "@/assets/imgs/home/<USER>";
import { useRoute, useRouter } from "vue-router";
import { filterCmsItem } from "packages/utils/cms";

export const useHomeStore = defineStore(STORE_KEY.HOME.INDEX, () => {
  const route = useRoute();
  const router = useRouter();

  const lang = getStandardizedLang();
  const game_id = getStandardizedGameId();

  const bannerList = ref<IDetail[]>([]); // 轮播图

  const allNotices = ref<{ key: string; notices: Notice[] }[]>([]); // 所有模块公告

  // 首页板块
  const activeId = ref(""); // 当前板块id
  const activeKey = ref(""); // 当前板块key
  const tabList = ref<TabItem[]>([]);
  const plates = ref<Plate[]>([]);

  const getConfig = async () => {
    let { list } = await getPlates.run({ page_type: 0, limit: 10 });

    list = list.sort((x, y) => x.order - y.order);
    plates.value = list;

    const tabs = list.map((item) => {
      return {
        label: item.plate_name, // 后台根据语言自动返回
        value: String(item.id),
        key: item.unique_identifier,
      };
    });

    list.forEach((item) => {
      const languageData = JSON.parse(item.language_data) as PlateLangItem[];
      let target = languageData.find((item) => item.language === lang);
      if (!target) target = languageData[0];

      const contents = target.content_msgs.sort((x, y) => x.order - y.order);
      const notices = contents
        .map((c, index) => {
          return {
            content: c.talk_name,
            link: c.url,
            new: index === 0,
          };
        })
        .filter((x) => x.link);

      allNotices.value.push({
        key: item.unique_identifier,
        notices,
      });
    });

    tabList.value = tabs;

    // url有hash，优先从hash获取
    if (route.query.plate_type) {
      const key = route.query.plate_type;
      const tab = tabs.find((item) => item.key === key);
      if (tab) {
        activeId.value = tab.value;
        activeKey.value = tab.key;
        return;
      }
    }

    activeId.value = tabs[0]?.value; // 默认第一个tab
    activeKey.value = tabs[0]?.key; // 默认第一个tab
  };

  const changeTab = (id: string) => {
    const target = tabList.value.find((item) => item.value === id)!;
    activeId.value = id;
    activeKey.value = target.key;

    router.replace({
      query: {
        ...route.query,
        plate_type: target.key,
      },
    });
  };

  const getNotices = (plateKey: string) => {
    const target = allNotices.value.find((item) => item.key === plateKey);
    return target?.notices || [];
  };

  const { getSecondColumnData } = useCms({ cms_config: {} });

  const getBanners = async () => {
    const defaultList: any = [{ pic_urls: [DefaultImg], ext_info: "" }];
    try {
      const { data } = await getSecondColumnData(
        {
          offset: 0,
          content_class: CmsContentClass.banner,
          get_num: 10,
        },
        {
          primary: CMS_COLUMN_NAME_HOME,
          second: CMS_COLUMN_NAME_HOME_BANNER,
        },
      );
      const items = (data?.info_content as IDetail[])?.filter((item) =>
        filterCmsItem({ ext_info: item.ext_info, lang, game_id }),
      );
      // 没有配置，手动添加兜底图
      if (!items?.length) {
        bannerList.value = defaultList;
      } else {
        bannerList.value = items;
      }
    } catch (err) {
      bannerList.value = defaultList;
    }
  };

  return {
    bannerList,
    plates,
    tabList,
    activeId,
    activeKey,
    allNotices,
    getConfig,
    getBanners,
    getNotices,
    changeTab,
  };
});
