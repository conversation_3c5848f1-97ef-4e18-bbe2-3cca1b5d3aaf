import { computed, ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { useGetCreatorHubTaskList } from "@/api/creatorhub";
import { useHomeStore } from "./home.store";
import { PageInfo } from "packages/types/common";
import { OrderBy, PostItem } from "packages/types/post";
import { usePostList } from "@/api/post";
import { dealPostData, removeDuplicatePost } from "@/utils/home";
import image_error_url from "@/assets/imgs/common/image-error.png";
import { t } from "@/locales";
import { useLanguageStore } from "../language";

const SELECT_ALL_KEY = -1;

export const useHomeCreatorHubStore = defineStore(STORE_KEY.HOME.CREATOR_HUB, () => {
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const { lang_regions } = storeToRefs(useLanguageStore());

  const orderBy = ref<OrderBy>(2); // 1-tim 2-hot

  const { data: tasks, refetch: refetchTasks } = useGetCreatorHubTaskList({
    limit: 100,
    plate_id: Number(plateId.value),
  });

  const selected_task_id = ref<number>(SELECT_ALL_KEY);
  const selected_rank_id = ref<number>(SELECT_ALL_KEY);

  const task_options = computed(() => {
    const list =
      tasks.value?.list.map((item) => {
        return { name: item.task_name, value: item.task_id };
      }) ?? [];
    return [{ name: t("all_events"), value: SELECT_ALL_KEY }, ...list];
  });

  const rank_options = computed(() => {
    const list =
      tasks.value?.list
        .find((item) => item.task_id === selected_task_id.value)
        ?.ranks.map((item) => {
          return { name: item.rank_name, value: item.id };
        }) ?? [];
    return [{ name: t("all_categories"), value: SELECT_ALL_KEY }, ...list];
  });

  const onChangeTask = (id: number) => {
    if (selected_task_id.value === id) return;
    selected_task_id.value = id;
    selected_rank_id.value = SELECT_ALL_KEY;
    getPostList();
  };

  const onChangeRank = (id: number) => {
    if (selected_rank_id.value === id) return;
    selected_rank_id.value = id;
    getPostList();
  };

  const postLoading = ref(false);
  const pageInfo = ref<PageInfo>({
    is_finish: false,
    next_page_cursor: "",
    previous_page_cursor: "",
  });

  const postList = ref<PostItem[]>([]);

  const getPostList = async (isMore = false) => {
    if (!isMore) pageInfo.value.next_page_cursor = "";

    postLoading.value = true;
    try {
      let { list, page_info } = await usePostList.run({
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: pageInfo.value.next_page_cursor,
        order_by: orderBy.value,
        need_all_region: true,
        limit: "10",
        rank_id:
          selected_rank_id.value === SELECT_ALL_KEY
            ? undefined
            : selected_rank_id.value || undefined,
        task_id:
          selected_task_id.value === SELECT_ALL_KEY
            ? undefined
            : selected_task_id.value || undefined,
        regions: lang_regions.value,
      });
      list = dealPostData(list);

      if (isMore) {
        postList.value = postList.value.concat(list);
      } else {
        postList.value = list;
      }
      postList.value = removeDuplicatePost(postList.value);
      // test code
      // postList.value.forEach((item) => {
      //   item.pic_urls?.forEach((url, index) => {
      //     item.pic_urls[index] = url + "/xxx.png";
      //   });
      // });
      postList.value.forEach((item) => {
        item.pic_urls?.forEach((url, index) => {
          handleInvalidImage({ url, onError: () => (item.pic_urls[index] = image_error_url) });
        });
      });
      pageInfo.value = page_info;
    } catch (error) {
      console.error(error);
    } finally {
      postLoading.value = false;
    }
  };

  const banner = computed(() => {
    for (let item of tasks.value?.list ?? []) {
      // TODO: 等待后端提供活动对应跳转地址
      if (item.image_url) return { img: item.image_url, href: item.task_page_url };
    }
    return null;
  });

  const isFilterValid = (info: { task_id: number; rank_id?: number }) => {
    if (!info.task_id) return false;
    if (!info.rank_id) return tasks.value?.list.some((item) => item.task_id === info.task_id);
    return tasks.value?.list
      .find((item) => item.task_id === info.task_id)
      ?.ranks.some((item) => item.id === info.rank_id);
  };

  const onFilter = (info: { task_id: number; rank_id?: number }) => {
    if (!isFilterValid(info)) return;
    if (!info.rank_id) {
      if (info.task_id === selected_task_id.value && selected_rank_id.value === SELECT_ALL_KEY)
        return;
      selected_task_id.value = info.task_id;
      selected_rank_id.value = SELECT_ALL_KEY;
    } else {
      if (info.task_id === selected_task_id.value && info.rank_id === selected_rank_id.value)
        return;
      selected_task_id.value = info.task_id;
      selected_rank_id.value = info.rank_id;
    }
    getPostList();
  };

  return {
    postList,
    postLoading,
    pageInfo,
    getPostList,
    selected_task_id,
    selected_rank_id,
    task_options,
    rank_options,
    onChangeTask,
    onChangeRank,
    banner,
    onFilter,
    isFilterValid,
    refetchTasks,
  };
});

const handleInvalidImageMap = new Map<string, { pending: boolean; error: boolean }>();
/** 替换无效的image */
const handleInvalidImage = async (info: { url: string; onError: () => void }) => {
  const record = handleInvalidImageMap.get(info.url);
  if (record) {
    if (record.error) {
      return info.onError();
    }
    return;
  } else {
    handleInvalidImageMap.set(info.url, { pending: true, error: false });
    const img = new Image();
    img.src = info.url;
    img.onload = () => {
      // console.log("load image success", info.url);
      handleInvalidImageMap.set(info.url, { pending: false, error: false });
    };
    img.onerror = () => {
      // console.info("load image error", info.url);
      handleInvalidImageMap.set(info.url, { pending: false, error: true });
      info.onError();
    };
  }
};
