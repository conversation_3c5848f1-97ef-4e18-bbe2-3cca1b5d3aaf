import { SizeType } from "../../../types/common";

export enum SwiperDirection {
  vertical = "vertical",
  horizontal = "horizontal",
}

export interface SwiperProps {
  loop?: boolean;
  // 初始化时显示内容的下标
  index?: number;
  rotate?: number;
  scale?: number;
  autoplay?: boolean;
  duration?: number;
  width?: number;
  direction?: SwiperDirection;
}

export enum SwiperPaginationTheme {
  default = "default",
  primary = "primary",
  light = "light",
}

export type SwiperPaginationThemeType = keyof typeof SwiperPaginationTheme;

export enum MediaTextCardType {
  vertical = "vertical",
  horizontal = "horizontal",
  integrated = "integrated",
}

export interface SwiperPaginationProps {
  default_bg?: string;
  active_bg?: string;
  theme?: keyof typeof SwiperPaginationTheme;
  size?: SizeType;
  disable_click?: boolean;
  direction?: SwiperDirection;
  custom_theme?: { default_bg: string; active_bg: string };
  prev_visible?: boolean;
  next_visible?: boolean;
}

export interface SwiperCardItemProps {
  src?: string;
  title?: string;
  desc?: string;
  is_video?: boolean;
  time?: string;
  source?: string;
}
