import { UserInfo } from "packages/types/user";

export interface User {
  id: number;
  name: string;
  avatar: string;
}

/**
 * 我的粉丝 或者 我的关注者
 */
export type Follower = {
  /** 主键id */
  id: string;
  /** 是否关注我 */
  is_follow: boolean;
  /** 是否互相关注 */
  is_mutual_follow: boolean;
  /** 被关注的用户openid */
  to_intl_openid: string;
  /** 被关注用户的信息 */
  user_info: UserInfo;
  /** 用户openid */
  intl_openid: string;
};

export type Titles = {
  area_id: string;
  game_id: string;
  id: number;
  status: number; // 是否生效1：生效2：失效
  title: {
    area_id?: string;
    avatar?: string; // 称号icon
    down_time?: string;
    game_id?: string;
    id?: number; // 称号id
    init_host?: string;
    language?: Language; // 语言
    possess_num?: number;
    status?: number; // 1:待上架 2:已上架
    up_time?: number;
  };
};

export type Language = {
  id: string;
  introduce: string;
  language: string;
  title: string;
  title_id: number;
};

export interface UnReadMessageCounts {
  comment_count: number;
  follow_count: number;
  like_count: number;
  site_msg_count: number;
}

export enum ReadMessageAllType {
  COMMENT = 1,
  FOLLOW = 2,
  LIKE = 3,
  SITE_MSG = 4,
}
