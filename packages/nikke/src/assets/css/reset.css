body,
dl,
dd,
ul,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
header,
section,
article,
footer {
  margin: 0;
}

body,
button,
input,
select,
textarea {
  font:
    12px/1.5 tahoma,
    sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

em,
b {
  font-style: normal;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

img {
  border: 0;
}

html {
  width: 100%;
}

body {
  /* background: #110f10; */
  width: 100%;
}

button,
input,
select,
textarea {
  font-size: 100%;
  outline: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th,
ul,
ol {
  padding: 0;
  list-style: none;
}
a {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  outline: none;
}
.clearfix:after {
  content: "\0020";
  height: 0;
  display: block;
  clear: both;
}
