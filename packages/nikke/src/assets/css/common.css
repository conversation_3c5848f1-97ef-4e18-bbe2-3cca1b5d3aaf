body,
div,
span,
button,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  font-family:
    "DINNextLTPro",
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    <PERSON>,
    <PERSON>,
    "Segoe UI",
    Roboto,
    Helvetica,
    "Microsoft Yahei",
    <PERSON><PERSON>,
    sans-serif;
}

* {
  margin: 0;
  padding: 0;
  border-width: none;
  outline-width: 0;
}

input {
  outline: 0;
  border: 0;
  width: 100%;
}

input:focus {
  outline: none;
  border: none;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  width: 4px;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

@font-face {
  font-family: "InterLight";
  src:
    url("../font/Inter_28pt-Light.woff2") format("woff2"),
    url("../font/Inter_28pt-Light.woff") format("woff"),
    url("../font/Inter_28pt-Light.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src:
    url("../font/Inter_28pt-Regular.woff2") format("woff2"),
    url("../font/Inter_28pt-Regular.woff") format("woff"),
    url("../font/Inter_28pt-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "InterMedium";
  src:
    url("../font/Inter_28pt-Medium.woff2") format("woff2"),
    url("../font/Inter_28pt-Medium.woff") format("woff"),
    url("../font/Inter_28pt-Medium.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "InterBold";
  src:
    url("../font/Inter_28pt-Bold.woff2") format("woff2"),
    url("../font/Inter_28pt-Bold.woff") format("woff"),
    url("../font/Inter_28pt-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "DINNextLTProBold";
  src:
    url("../font/DINNextLTPro-Bold.woff2") format("woff2"),
    url("../font/DINNextLTPro-Bold.woff") format("woff"),
    url("../font/DINNextLTPro-Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "DINNextLTPro";
  src:
    url("../font/DINNextLTPro-Regular.woff2") format("woff2"),
    url("../font/DINNextLTPro-Regular.woff") format("woff"),
    url("../font/DINNextLTPro-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Abolition";
  src: url("../font/AbolitionTest-Regular.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("../font/SOURCEHANSANSSC-REGULAR.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Source Han Sans CN MEDIUM";
  src: url("../font/SOURCEHANSANSSC-MEDIUM.otf.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Mplus 1p Bold";
  src: url("../font/MPLUS1p-ExtraBold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sandoll GyeokdongG2";
  src: url("../font/Sandoll-GyeokdongG2.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* 中文 */
.font-Abolition {
  font-family: "Source Han Sans CN";
  font-weight: 700;
}

/* 英文en */
html[lang="en"]:root {
  --font-size-s: 10px;
  --font-size-m: 12px;
  --font-size-base: 14px;
  --font-size-l: 15px;
  --font-size-xl: 16px;
  --font-size-langer: 19px;
}
html[lang="en"] .font-Abolition {
  margin-top: -4px;
  font-family: "Abolition";
  font-weight: 400;
}

/* 日语ja */
html[lang="ja"] .font-Abolition {
  font-weight: 700;
  font-family: "Mplus 1p Bold";
}

/* 韩语ko */
html[lang="ko"] .font-Abolition {
  font-weight: 600;
  font-family: "Sandoll GyeokdongG2";
}

/* 泰语 */

/* 弹窗动画 */
.popbox-ani-enter-active,
.popbox-ani-leave-active {
  transition: opacity 0.2s ease-out;
  opacity: 1 !important;
}

.popbox-ani-enter,
.popbox-ani-leave-to {
  opacity: 0;
}

.absolute-center {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: transparent;
  width: 30px;
  height: 30px;
  cursor: pointer;
}

::-webkit-scrollbar {
  background-color: transparent;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}
.no-scrollbar::-webkit-scrollbar {
  height: 0;
  width: 0;
}
.no-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.common-rotate {
  animation: roate 1.6s linear infinite;
}

.comm-clip {
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 6px), calc(100% - 6px) 100%, 0 100%);
}

@keyframes roate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.common-btns {
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 6px), calc(100% - 6px) 100%, 0 100%);
}

#onetrust-consent-sdk {
  pointer-events: auto;
}
