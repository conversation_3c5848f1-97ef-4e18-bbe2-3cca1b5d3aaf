.year2-5 {
  // background-color: red;
  background-color: #fff;
  .nav-bg {
    background-image: url("@/assets/imgs/year2-5/nav-bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .logo-bg {
    background-image: url("@/assets/imgs/year2-5/app-logo2.png");
  }
  .search-bg {
    background-color: #8cbff9;
  }
  .search-line {
    background-color: #fff;
  }
  .search-icon {
    .fill-current {
      color: #fff !important;
    }
  }
  .icon-language-origin {
    display: none;
  }
  .icon-language-ann {
    width: 100%;
    height: 100%;
    background-image: url("@/assets/imgs/year2-5/icon-language.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .icon-notification {
    width: 100%;
    height: 100%;
    background-image: url("@/assets/imgs/year2-5/icon-notification.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .icon-phone {
    width: 100%;
    height: 100%;
    background-image: url("@/assets/imgs/year2-5/icon-phone.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  // .tabs
  .tabs-content {
    // min-height: calc(100vh - 300px );
    min-height: calc(100vh - 236px);
    position: relative;
    // background-color: #fff;
    border-radius: 30px 30px 0 0;
    display: flex;
    flex-direction: column;
    &-hd {
      &.fixed-nav {
        .tabs {
          border-radius: 0 0 0 0;
          background-color: #2b6bd7;
        }
      }
      &.is-stick {
        // +.tabs-content-bd{
        //     background-attachment: fixed;
        // }
        // .tabs{
        //     border-radius: 0 0 0 0;
        //     background-color: #2b6bd7;
        // }
      }

      .tabs {
        background-color: transparent;
        border-color: #79a3f6;
        background-image: url("@/assets/imgs/year2-5/tabs-hd-bg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .select-text {
        color: #84f2ff;
      }
      .text-active {
        background: linear-gradient(to bottom, #84f2ff 10%, #ffffff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: #84f2ff;
      }
      .text-normal {
        color: rgba($color: #ffffff, $alpha: 0.6);
      }
      .tabs-line {
        background-color: #84f2ff;
      }
      .tabs-border-color {
        border-color: #79a3f6;
      }
    }
    .tabs-text {
      // color: #fff;
    }
    &-bd {
      width: 100%;
      height: 100%;
      position: relative;
      background-color: #fff;
      flex: 1;

      &.bg-img {
        background-image: url("@/assets/imgs/year2-5/tabs-bd-bg1.png");
        background-position: center top;
        background-size: 100% 542px;
        background-repeat: no-repeat;
      }
      &.fixed-background {
        background-attachment: fixed;
        // background-position: center calc(30px + 50%);
      }
    }
    .fixed-bg {
      display: none;
    }
  }

  // new
  .news-item {
    background-image: url("@/assets/imgs/year2-5/bg-news.png");
    .new-img {
      object-fit: cover;
    }
    .news-text {
      color: #3b65c9;
    }
    .text-more {
      color: #3a64c8;
    }
    .more-item {
      .fill-current {
        color: #3a64c8 !important;
      }
    }
  }
  .text-tools {
    background: linear-gradient(to bottom, #cff7fd 80%, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  // header
  .header {
    border-color: rgba($color: #c2deff, $alpha: 0.4);
    position: relative;
    z-index: 3;
    margin-top: 0;
    height: 48px;
    padding-top: 12px;
    box-sizing: border-box;
    // color: rgba($color: #ffffff, $alpha: 0.6);
    .icon-post {
      background-image: url("@/assets/imgs/year2-5/icon-post.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .text-post {
      background: linear-gradient(to bottom, #cff7fd 50%, #ffffff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .select-header {
      border: 1px solid transparent;
      &-title {
        color: rgba($color: #ffffff, $alpha: 0.6);
        font-weight: 100;
        letter-spacing: 0.5px;
      }
    }
    .switch-text {
      color: rgba($color: #ffffff, $alpha: 0.6);
    }
  }
  .switch-bg[data-state="unchecked"] {
    background-color: var(--color-white-60);
  }
  .tag-list-anniversary {
    .tag-item-ann {
      background-image: url("@/assets/imgs/year2-5/tag-list-select.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-color: transparent;
      box-sizing: border-box;
    }
    .tag-select {
      border-color: transparent;
    }
  }
  .card-item {
    // border: none;
    // margin-bottom: 6px;
  }
  // btn
  .anniversary-btn {
    &.btn-primary {
         width: 64px;
      height: 32px;
      background-image: url("@/assets/imgs/year2-5/btns-bg.png");
      background-color: transparent;
    }
    &.btn-default {
        background-image: url("@/assets/imgs/year2-5/btn-bg.png");
        background-color: transparent;
        background-size: 100% 100%;
    }
    .font-Abolition {
      // font-size: 8px;
    }
  }
  .post-bubble {
    background-image: url("@/assets/imgs/year2-5/post-bubble.gif");
  }
  .icon-color-ann {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .con-z {
    position: relative;
    z-index: 2;
  }
  .icon-ring {
    position: relative;
    z-index: 2;
  }
}

@media (max-width: 750px) {
  .year2-5 {
    .tabs-content {
      &-bd {
        &.bg-att {
          // background-attachment: scroll;
        }
      }
      .fixed-bg {
        position: fixed;
        top: 81px;
        left: 50%;
        width: 100%;
        max-width: 480px;
        display: block;
        transform: translateX(-50%);
        height: 542px;
        background-image: url("@/assets/imgs/year2-5/tabs-bd-bg1.png");
        background-position: center top;
        background-size: 100% 542px;
        background-repeat: no-repeat;
        z-index: 1;
      }
    }
  }
}
.year2-5 {
  // --line-1: #C2DEFF;
  --color-white-60: rgba(255, 255, 255, 0.6);
}
html[lang="ja"] .year2-5 {
  .anniversary-btn {
    .font-Abolition {
      font-size: 8px;
      letter-spacing: normal;
      font-size: 9px;
    }
  }
}
html[lang="ko"] .year2-5 {
  .anniversary-btn {
    .font-Abolition {
      font-size: 8px;
      letter-spacing: normal;
      font-size: 9px;
    }
  }
}
