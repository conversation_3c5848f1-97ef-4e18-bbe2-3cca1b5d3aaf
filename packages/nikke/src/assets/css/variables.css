:root {
  /* !!! 颜色一一对应，不要混用，除非特殊情况！！！！！！！！！
         字体用--text-?
         背景用 --fill-?
         线条、边框用 --line-?
         渐变色用 --linear-gradient-?
         阴影用 --op-shadow-?
         里面的色值不要私自添加，如果遇到里面没有的色值，采用对应色值的相似色值，如果实在没有对应的需要和设计沟通是否需要添加新色值
  */

  /* 针对 Abolition 字体进行不同语言的适配*/
  --font-size-s: 8px;
  --font-size-m: 10px;
  --font-size-base: 10px;
  --font-size-l: 13px;
  --font-size-xl: 14px;
  --font-size-langer: 15px;

  /* 深浅不变的颜色 */
  --color-white: #fff;
  --color-white-10: rgba(255, 255, 255, 0.1);
  --color-white-20: rgba(255, 255, 255, 0.2);
  --color-white-40: rgba(255, 255, 255, 0.4);
  --color-white-50: rgba(255, 255, 255, 0.5);

  --color-black: #000;
  --color-black-55: rgba(0, 0, 0, 0.55);
  --color-black-70: rgba(0, 0, 0, 0.7);
  --color-black-80: rgba(0, 0, 0, 0.8);
  --color-black-85: rgba(0, 0, 0, 0.85);

  --color-1: #141416;
  --color-1-80: rgba(20, 20, 22, 0.8);
  --color-1-56: rgba(20, 20, 22, 0.56);
  --color-2: #282829;
  --color-3: #959596;
  --color-4: #d0d0d0;
  --color-5: #ecedf1;
  --color-6: #f1f1f1;
  --color-7: #5a5a5b;

  /* 品牌色-深浅不变色*/
  --brand-1: #3eafff;
  --brand-1-12: rgba(62, 175, 255, 0.12);
  --brand-1-20: rgba(62, 175, 255, 0.2);
  --brand-1-30: rgba(62, 175, 255, 0.3);
  --brand-2: #d8efff;
  --brand-2-20: rgba(216, 239, 255, 0.2);
  --brand-3: #3d5885;
  --brand-3-50: rgba(61, 88, 133, 0.5);

  --brand-4: #bfc6f2;
  --brand-4-24: rgba(191, 198, 242, 0.24);

  /* 品牌色深浅变色 */
  --brand-type-1: #3eafff;

  /* 报错 */
  --error: #ff145b;
  --error-10: rgba(255, 20, 91, 0.1);
  --error-80: rgba(255, 20, 91, 0.8);

  /* 其他颜色-深浅不变色*/
  --other-1: #70e4ef;
  --other-2: #e8b442;
  --other-3: #c57eeb;

  --other-4: #ffda95;
  --other-5: #ea9a29;
  --other-5-1: #fcf7ec;
  --other-5-10: rgba(234, 154, 41, 0.3);
  --other-5-30: rgba(234, 154, 41, 0.1);
  --other-6: #6a1919;
  --other-6-1: #e8b442;
  --other-7: #ac5716;
  --other-7-1: #f4b971;

  --other-8: #fbb253;
  --other-9: #ffe58c;
  --other-10: #ccc;
  --other-11: #F9E9C6;

  /* 渐变色 */
  --linear-gradient-1: linear-gradient(227deg, rgba(239, 242, 246, 0) 16.37%, #f5f5f5 63.65%);
  --linear-gradient-2: linear-gradient(227deg, rgba(239, 242, 246, 0) 16.37%, #f5f5f5 63.65%);
  --linear-gradient-3: linear-gradient(90deg, #fbb253 54.5%, #ffe58c 100%);

  /* 字体颜色 */
  --text-1: #141416;
  --text-1-50: rgba(20, 20, 22, 0.5);
  --text-2: #3a3a3a;
  --text-3: #585859;
  --text-3-50: rgba(88, 88, 89, 0.5);
  --text-3-60: rgba(88, 88, 89, 0.6);
  --text-3-20: rgba(88, 88, 89, 0.2);
  --text-4: #d0d0d0;
  --text-4-60: rgba(208, 208, 208, 0.6);
  --op-text-white: #fff;

  /* 线条/边框 */
  --line-1: #e5e5e5;
  --line-1-40: rgba(229, 229, 229, 0.4);

  --op-line-white: #fff;

  --op-line-black-10: rgba(0, 0, 0, 0.1);

  /* 背景色 */
  --fill-0: #fff;
  --fill-1: #141416;
  --fill-1-80: rgba(20, 20, 22, 0.8);
  --fill-1-60: rgba(20, 20, 22, 0.6);
  --fill-1-40: rgba(20, 20, 22, 0.4);
  --fill-1-20: rgba(20, 20, 22, 0.2);
  --fill-2: #e5e5e5;
  --fill-3: #f5f5f5;
  --fill-3-80: rgba(245, 245, 245, 0.8);

  --fill-4: #eff2f6;
  /* --fill-4-80: rgba(239, 242, 246, 0.8); */
  --fill-5: #dbdbdb;
  --fill-6: #5a5a5b;
  --fill-7: #e7e7e7;

  --op-fill-white: #fff;

  --op-fill-white-20: rgba(255, 255, 255, 0.2);
  --op-fill-white-40: rgba(255, 255, 255, 0.4);
  --op-fill-white-60: rgba(255, 255, 255, 0.6);

  /* 阴影 */
  --op-shadow-white: #fff;

  --op-shadow-black: #000;
  --op-shadow-black-5: rgba(0, 0, 0, 0.05);
  --op-shadow-black-10: rgba(0, 0, 0, 0.1);
  --op-shadow-black-15: rgba(0, 0, 0, 0.15);
  --op-shadow-black-20: rgba(0, 0, 0, 0.2);

  /* 分界线 */
  --max-pc-w: 480px;
}

/* .dark {
  --brand-type-1: var(--brand-3);
  --brand-2: #3eafff;

  --text-1: #f1f1f1;
  --text-1-50: rgba(241, 241, 241, 0.5);
  --text-2: #b1b1b2;
  --text-3: #818183;
  --text-4: #5e5e5e;

  --line-1: #4b4b4d;
  --line-2: #323233;

  --line-3: #4a4b50;
  --line-4: #f1f1f1;

  --op-line-black-10: rgba(255, 255, 255, 0.1);

  --fill-0: #101113;
  --fill-1: #f1f1f1;
  --fill-1-80: rgba(241, 241, 241, 0.8);
  --fill-2: #4a4b50;
  --fill-3: #343638;
  --fill-4: #101113;
  --fill-5: #4b4b4d;
  --fill-6: #101113;
  --fill-7: #323233;

  --op-fill-white: #1f1f21;

  --op-shadow-black: #000;
  --op-shadow-black-5: rgba(255, 255, 255, 0.05);
  --op-shadow-black-10: rgba(255, 255, 255, 0.1);
  --op-shadow-black-15: rgba(255, 255, 255, 0.15);

  --linear-gradient-1: linear-gradient(227deg, rgba(16, 17, 19, 0) 16.37%, #1f1f21 63.65%);
  --linear-gradient-2: linear-gradient(227deg, rgba(16, 17, 19, 0) 16.37%, #101113 63.65%);
} */
