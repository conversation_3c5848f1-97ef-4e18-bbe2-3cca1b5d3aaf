/* rtl:begin:ignore */
@tailwind base;
/* rtl:end:ignore */
@tailwind utilities;

@layer utilities {
  .text-stroke {
    -webkit-text-stroke-width: 0.5px;
    -webkit-text-stroke-color: rgba(20, 20, 22, 0.6);
  }
  .text-stroke1 {
    -webkit-text-stroke-width: 0.4px;
    -webkit-text-stroke-color: rgba(20, 20, 22, 0.8);
  }
}
@layer utilities {
  .home-position {
    left: 12px;
  }
  @media screen and (min-width: 480px) {
    .home-position {
      left: calc(50% - (var(--max-pc-w) / 2) + 34px);
      transform: translate(-50%, 0);
    }
  }
}

@layer utilities {
  .team-member-bg {
    width: 30px;
  }
  @media screen and (min-width: 480px) {
    .team-member-bg {
      width: 44px;
    }
  }
}
