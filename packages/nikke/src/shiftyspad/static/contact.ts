import { getLang } from "@/shiftyspad/const";

export function getContactUs(lang: string = getLang()) {
  const is_mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent);
  let href = "https://global.yzfchat.com/newgames/scene_product.html?scene_id=1691745941999428";
  const gameid = "29080";
  href = href + "&lang_type=" + lang + "&gameid=" + gameid;
  if (!is_mobile) {
    href = href + "&topc=1";
  }
  return href;
}

export function getLicense() {
  return "https://esotericsoftware.com/spine-editor-license";
}
