// 后台字段转为重构class字段
import { BurstType, JobType, ManufacturerType, RarityType } from "packages/types/shiftyspad";

export { WeaponType } from "packages/types/shiftyspad";

export enum BurstField {
  Step1 = "Step1",
  Step2 = "Step2",
  Step3 = "Step3",
  AllStep = "AllStep",
}

export const BurstMap = {
  [BurstField.Step1]: BurstType.FIRST,
  [BurstField.Step2]: BurstType.SECOND,
  [BurstField.Step3]: BurstType.THIRD,
  [BurstField.AllStep]: BurstType.GENERAL,
};

export enum ClassField {
  Attacker = "Attacker",
  Defender = "Defender",
  Supporter = "Supporter",
}

export const ClassMap = {
  [ClassField.Attacker]: JobType.ATTACK,
  [ClassField.Supporter]: JobType.SUPPORT,
  [ClassField.Defender]: JobType.DEFENCE,
};

export enum CoperationField {
  ELYSION = "ELYSION", // 极乐净土
  MISSILIS = "MISSILIS", // 米西利斯
  TETRA = "TETRA", // 泰特拉
  PILGRIM = "PILGRIM", // 朝圣者
  ABNORMAL = "ABNORMAL", // 反常
}

export const CoperationMap = {
  [CoperationField.ELYSION]: ManufacturerType.Elysion,
  [CoperationField.MISSILIS]: ManufacturerType.Missilis_Industry,
  [CoperationField.ABNORMAL]: ManufacturerType.Abnormal,
  [CoperationField.PILGRIM]: ManufacturerType.Pilgrim,
  [CoperationField.TETRA]: ManufacturerType.Tetra_Line,
};

export const RarityColorMap = {
  [RarityType.EMPTY]: "",
  [RarityType.R]: "blue",
  [RarityType.SR]: "purple",
  [RarityType.SSR]: "yellow",
};

export const RarityValueMap: any = {
  [RarityType.EMPTY]: 0,
  [RarityType.R]: 0,
  [RarityType.SR]: 1,
  [RarityType.SSR]: 2,
};
