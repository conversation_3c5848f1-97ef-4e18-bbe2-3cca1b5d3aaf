export type {
  Skill,
  LevelCost,
  EquipOptions,
  LevelInfo,
  EquipFunctions,
  EquipData,
  Speech,
  CharacterData,
  SkillCost,
  UltiSkill,
} from "packages/types/shiftyspad";

export { ViewModePose, WeaponType, Rare } from "packages/types/shiftyspad";

export type AniParams = {
  resource_id: number;
  skin_index?: number;
  action?: string;
  additional_skins?: string[];
};

export enum WeaponSlotItemSize {
  SMALL = "small",
  NORMAL = "normal",
  LARGE = "large",
}
export enum WeaponSlotItemMode {
  HEX = "hex", // 六边形样式
  CIRCLE = "circle", // 圆形样式
}
