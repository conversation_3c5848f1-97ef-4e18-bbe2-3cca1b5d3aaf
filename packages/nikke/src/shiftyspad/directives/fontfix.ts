// import { waitForRouterViewShown } from './autofontsizeheight'

import { getStandardizedLang } from "packages/utils/standard";
import { Directive, DirectiveBinding } from "vue";

const fontFixDirective: Directive<HTMLElement, number | undefined> = {
  mounted(el: HTMLElement, binding: DirectiveBinding<number | undefined>) {
    if (getStandardizedLang() !== "en") {
      // 获取指令的参数值，如果未提供或不是数字，则使用默认值
      let paddingTopValue = 2;
      if (typeof binding.value === "number" && !isNaN(binding.value)) {
        paddingTopValue = binding.value;
      }

      el.classList.add(`pt-[${paddingTopValue}px]`);
    }
  },
};

export default fontFixDirective;
