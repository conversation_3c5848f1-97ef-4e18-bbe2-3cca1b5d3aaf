// 文字超框，添加 marquee 效果

import { Ref } from "vue";

const addScroll = (el: HTMLElement) => {
  const parent: HTMLElement = el.parentNode as HTMLElement;
  if (!parent) {
    return;
  }
  const hdWidth = parent.getBoundingClientRect().width;
  if (!hdWidth) return;
  const textWidth = el.offsetWidth;
  if (textWidth > hdWidth) {
    parent.classList.add("marquee");
    el.classList.add("marquee-inner");
    el.style.top = "";
    el.style.transform = "";
    el.style.animationDuration = Math.max((textWidth / 800) * 25, 15) + "s";
  }
};

export default {
  updated(el: HTMLElement) {
    addScroll(el);
  },
  mounted(el: HTMLElement) {
    addScroll(el);
  },
};
