// import { waitForRouterViewShown } from './autofontsizeheight'

export default {
  mounted(el: HTMLElement) {
    const parent: HTMLElement = el.parentNode as HTMLElement;
    if (!parent) {
      return;
    }
    // waitForRouterViewShown(() => {
    const hdWidth = parent.getBoundingClientRect().width;
    if (!hdWidth) return;
    const textWidth = el.offsetWidth;
    let scale = hdWidth / textWidth;
    scale = Math.min(scale, 1);
    const style = window.getComputedStyle(el);
    const oFontSize = style.fontSize;
    const oFontSizeVal = oFontSize.replace("px", "");
    const fontSize = Number(oFontSizeVal);

    // el.style.fontSize = isNaN(fontSize) ? oFontSize : fontSize * scale + 'px'
    if (!isNaN(fontSize)) {
      const newFontSize = fontSize * scale;
      // Chrome minimum font size setting
      if (newFontSize < 12) {
        // @ts-ignore
        el.style.zoom = scale * 0.9;
      } else {
        el.style.fontSize = newFontSize + "px";
      }
    }
    // })
  },
};
