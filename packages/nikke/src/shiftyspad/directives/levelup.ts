// 点击lv上升的效果

import { logger } from "@/shiftyspad/const";

export default {
  mounted(el: HTMLElement) {
    el.addEventListener("click", () => {
      logger.log("levelup");
      const span = document.createElement("span");
      span.className = "level-up";
      span.style.position = "absolute";
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      const rect = el.getBoundingClientRect();
      span.style.top = rect.top - rect.height * 0.5 + scrollTop + "px";
      span.style.left = rect.left + rect.width * 0.2 + "px";
      document.body.appendChild(span);
      setTimeout(() => {
        document.body.removeChild(span);
      }, 1200);
    });
  },
};
