import { useLoadingStore } from "@/shiftyspad/stores/loading";
import { nextTick, watch } from "vue";

export function waitForRouterViewShown(callback: Function) {
  // App.vue RouterView v-show 导致mounted时，获取不到父容器尺寸，导致失效。
  // watch loading改变，RouterView show之后再执行。
  const store = useLoadingStore();
  watch(
    () => store.loading,
    (v) => {
      if (!v) {
        nextTick(() => {
          callback && callback();
        });
      }
    },
    { immediate: true },
  );
}

export default {
  mounted(el: HTMLElement) {
    const parent: HTMLElement = el.parentNode as HTMLElement;
    if (!parent) {
      return;
    }
    // waitForRouterViewShown(() => {
    const hdHeight = parent.getBoundingClientRect().height;
    if (!hdHeight) return;
    const textHeight = el.offsetHeight;
    let scale = hdHeight / textHeight;
    scale = Math.min(scale, 1);
    const style = window.getComputedStyle(el);
    const oFontSize = style.fontSize;
    const oFontSizeVal = oFontSize.replace("px", "");
    const fontSize = Number(oFontSizeVal);

    if (!isNaN(fontSize)) {
      const newFontSize = fontSize * scale;
      // Chrome minimum font size setting
      if (newFontSize <= 12) {
        el.style.transform = `scale(${scale * 0.9})`;
      } else {
        el.style.fontSize = newFontSize + "px";
      }
    }
    // })
  },
};
