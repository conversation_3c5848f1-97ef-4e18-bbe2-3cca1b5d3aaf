<script setup lang="ts">
import { RoutesName } from "@/router/routes";
import { ENV_DEVELOPMENT, ENV_PRE, ENV_TEST } from "packages/configs/env.js";

/** tools */
import { getLang, getQuery } from "./const";
import { isInGame } from "packages/utils/tools";
import { promiseWrap } from "@/shiftyspad/utils/comm";
import DevtoolsDefender from "@/shiftyspad/utils/defender";

/** state */
import { useUser } from "@/store/user";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useCheckOnTop } from "@/shiftyspad/composable/interact/position";

/** deps */
import router from "@/router";
import { storeToRefs } from "pinia";
import { RouterView, useRoute } from "vue-router";
import { computed, onActivated, onDeactivated, onMounted, onUnmounted, watch } from "vue";

/** components */
import SvgIcon from "@/components/common/svg-icon.vue";
import HeadNav from "@/shiftyspad/components/common/HeadNav.vue";
import Footer from "@/shiftyspad/components/common/Footer.vue";
import HotcoolFooter from "@/shiftyspad/components/common/HotcoolFooter.vue";

/** style */
import "@/shiftyspad/assets/scss/comm.scss";
import "@/shiftyspad/assets/scss/_popups.scss";
import "@/shiftyspad/assets/scss/anims.scss";
import "@/shiftyspad/assets/scss/cookies.scss";

const route = useRoute();
const user = useUser();
const shifty_store = useUserStore();
const defender = DevtoolsDefender.getInstance();
const { is_top } = useCheckOnTop("#layout-content");
const { user_info } = storeToRefs(user);
const show_title = computed(() =>
  [
    RoutesName.SHIFTYSPAD_NIKKE_LIST,
    RoutesName.SHIFTYSPAD_NIKKE_LIST_ALL,
    RoutesName.SHIFTYSPAD_SCENE,
  ].includes(route.name as any),
);
const { makeSureBindRole, getRoleBonus } = useBindRole();
const show_back_home = computed(
  () => ![RoutesName.SHIFTYSPAD, RoutesName.SHIFTYSPAD_ROOT].includes(route.name as any),
);
const is_root = computed(() =>
  ([RoutesName.SHIFTYSPAD_ROOT, RoutesName.SHIFTYSPAD] as any).includes(route.name),
);
const use_default_nav = computed(
  () => ![RoutesName.SHIFTYSPAD_EDIT_NIKKE_LIST].includes(route.name as any),
);

const disable_debugger = () => {
  return (
    [ENV_DEVELOPMENT, ENV_TEST, ENV_PRE].includes(import.meta.env.MODE) &&
    !getQuery("debugger_test")
  );
};
const init = promiseWrap(async () => {
  !disable_debugger() && defender.start();
  if (!user_info.value?.intl_openid) return;
  if (!isInGame()) {
    const role = await makeSureBindRole();
    // 绑定过角色, 且进入shiftyspad, 领奖.
    role && getRoleBonus();
  } else {
    // 游戏内已自动绑定角色;
    getRoleBonus();
  }
  shifty_store.initAllUserData();
});

onDeactivated(() => defender.disable());
onUnmounted(() => defender.disable());

onMounted(() => {
  init();
});
onActivated(() => {
  init();
});

watch(
  () => user_info.value,
  () => {
    if (user_info.value?.intl_openid) init();
  },
  { immediate: true },
);
</script>

<template>
  <div class="container shiftypad">
    <HeadNav
      v-if="use_default_nav"
      :showhint="is_root"
      :transparent="is_root && is_top"
      :is-show-title="show_title"
    ></HeadNav>
    <div class="h-full relative">
      <div
        v-if="show_back_home"
        class="home-position fixed z-[100] bottom-[16px] w-[43px] h-[43px] flex items-center justify-center bg-[#00B5FF] rounded-[3px] cursor-pointer shadow-[0_1px_5px_0_rgba(0,0,0,0.1)]"
        @click="
          router.push({
            name: RoutesName.SHIFTYSPAD,
          })
        "
      >
        <SvgIcon name="icon-home" color="white" class="w-[28px] h-[28px]"></SvgIcon>
      </div>
      <!-- <Loading :loading="false"></Loading> -->
      <RouterView :key="route.path" />
    </div>
    <!-- use jsx to fix that -->
    <Footer v-if="getLang() !== 'zh-TW'"></Footer>
    <HotcoolFooter v-if="getLang() === 'zh-TW'"></HotcoolFooter>
  </div>
</template>

<style lang="scss">
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
html[lang="zh"] {
  .chapter-index {
    span {
      padding: px2rem(4) px2rem(4) px2rem(0) px2rem(4);
      font-size: px2rem(20);
    }
  }
}
.storyline-mitem {
  width: px2rem(166);
  height: px2rem(237);
  background: #fff url(@/shiftyspad/assets/images/storyline/storyline-deco-icon.png) no-repeat right
    bottom;
  background-size: px2rem(106) px2rem(87);
  margin: px2rem(30) px2rem(4);
  padding: px2rem(5);
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  .chapter-img {
    display: block;
    width: px2rem(155);
    height: px2rem(155);
    margin: 0 auto;
    object-fit: cover;
  }
  .chapter-index {
    margin-top: px2rem(4);
    span {
      color: #fff;
      font-size: px2rem(22);
      height: px2rem(24);
      line-height: px2rem(24);
      background: #141416;
      padding: 0 px2rem(4);
    }
  }
  .chapter-name {
    color: #333333;
    font-size: px2rem(22);
  }
  .chapter-prog {
    font-size: px2rem(22);
    color: #84817d;
    white-space: nowrap;
    em {
      color: #333333;
      font-size: px2rem(24);
      font-style: normal;
    }
  }
  .item-deco-line {
    background: url(@/shiftyspad/assets/images/storyline/storyline-deco-line.png) no-repeat center
      top / 100%;
    width: px2rem(16);
    height: px2rem(43);
    bottom: px2rem(-40);
    left: 0;
    pointer-events: none;
    z-index: 2;
  }
  &::after {
    display: block;
    content: "";
    width: px2rem(166);
    height: px2rem(2);
    position: absolute;
    left: 0;
    bottom: px2rem(-32);
    background: #d2d0ce;
  }
  .item-lock {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5) url(@/shiftyspad/assets/images/icon-lock.png) no-repeat center
      center;
    background-size: px2rem(42);
    display: none;
  }
  &.locked .item-lock {
    display: block;
  }
}
</style>
<style>
.chapter-name,
.chapter-prog,
.cursor-default {
  cursor: default;
}
</style>
