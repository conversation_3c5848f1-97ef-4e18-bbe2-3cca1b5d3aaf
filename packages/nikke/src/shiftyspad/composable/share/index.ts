import { RoutesName } from "@/router/routes";
import { useI18n } from "vue-i18n";
import { useCommonShare } from "@/components/common/share-pop/index";
// share
import NikkeShareMain from "@/shiftyspad/components/share/index.vue";
import NikkeShareList from "@/shiftyspad/components/share/list.vue";
import { useRoute } from "vue-router";
import { useUserStore } from "@/shiftyspad/stores/user";
import { getClientType, getSpecialBrowserType, isInGame } from "packages/utils/tools";
import { useUser } from "@/store/user";
import { COMMON_QUERY_KEYS, SHARE_WHITE_LIST_QUERY_KEYS } from "@/configs/const";
import { base64Encode } from "packages/utils/encrypt.ts";
import { storeToRefs } from "pinia";

export function useShiftysShare() {
  const route = useRoute();
  const user = useUser();

  const { t } = useI18n();
  const { query } = route;
  const { logined, has_role, user_nikkelist_info } = storeToRefs(useUserStore());

  // 是否支持长按保存图片
  const getSupportLongPressSave = () => {
    if (getClientType() === "Android") {
      if (isInGame()) return false;
      if (getSpecialBrowserType() === "facebook") return false;
      if (getSpecialBrowserType() === "line") return false;
    }
    return true;
  };

  const canNotScreenShotShare = () => {
    return (
      !logined.value ||
      !has_role.value ||
      !getSupportLongPressSave() ||
      !user_nikkelist_info.value?.length
    );
  };

  const getShareUrlObject = () => {
    const url = new URL(
      user.user_info?.intl_openid
        ? `${location.origin}${location.pathname}?${COMMON_QUERY_KEYS.EncodedUid}=${base64Encode(user.user_info?.intl_openid)}`
        : `${location.origin}${location.pathname}`,
      location.origin,
    );

    Object.entries(route.query).forEach(([key, value]) => {
      if (SHARE_WHITE_LIST_QUERY_KEYS.includes(key)) {
        url.searchParams.set(key, value as string);
      }
    });

    return url.toString();
  };

  const share = () => {
    const text = t("shiftyspad_root_share");
    if (canNotScreenShotShare()) {
      return useCommonShare({}).share({
        text,
        url: getShareUrlObject(),
      });
    }
    switch (route.name) {
      case RoutesName.SHIFTYSPAD:
      case RoutesName.SHIFTYSPAD_ROOT: {
        return useCommonShare({
          component: NikkeShareMain,
        }).share({
          text,
          url: getShareUrlObject(),
        });
      }
      case RoutesName.SHIFTYSPAD_NIKKE_LIST_PLAYER:
      case RoutesName.SHIFTYSPAD_NIKKE_LIST_ALL:
      case RoutesName.SHIFTYSPAD_NIKKE_LIST: {
        return useCommonShare({
          component: NikkeShareList,
        }).share({
          text,
          url: getShareUrlObject(),
        });
      }
      default: {
        return useCommonShare({}).share({
          text,
          url: getShareUrlObject(),
        });
      }
    }
  };

  return {
    getSupportLongPressSave,
    share,
  };
}
