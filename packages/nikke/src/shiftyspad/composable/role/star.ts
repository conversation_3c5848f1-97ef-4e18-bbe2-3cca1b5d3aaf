import { RarityType } from "packages/types/shiftyspad";

export function useStar(params: { rarity: RarityType; limit_break: number }) {
  const { rarity, limit_break } = params;
  const max_stars = (() => {
    switch (rarity) {
      case RarityType.EMPTY:
      case RarityType.R:
        return 0;
      case RarityType.SR:
        return 2;
      case RarityType.SSR:
        return 3;
    }
  })();

  const break_num = (() => {
    if (limit_break >= 10) {
      return "MAX";
    }
    return limit_break > max_stars ? limit_break - max_stars : 0;
  })();

  const active_star = (() => {
    return limit_break >= max_stars ? max_stars : limit_break;
  })();

  return {
    active_star,
    break_num,
    max_stars,
  };
}
