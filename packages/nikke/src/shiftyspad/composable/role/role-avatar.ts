/**
 * @description 获取玩家的 * 角色的皮肤状态;

 */

import { MI_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { useJsonStore } from "@/shiftyspad/stores/json";
import { useUserStore } from "@/shiftyspad/stores/user";
import { storeToRefs } from "pinia";
import { computed } from "vue";

export function useRoleImage(resource_id: number) {
  const { nikke_list } = storeToRefs(useJsonStore());
  const { user_nikkelist_info } = storeToRefs(useUserStore());

  const nikke = computed(() =>
    user_nikkelist_info.value.find((val) => val.resource_id === resource_id),
  );

  const mi_image = computed(() => {
    const { costume_id } = nikke.value ?? {};
    const default_img = MI_CHARACTER_URL({ resource_id: Number(resource_id) });
    if (!costume_id) return default_img;

    const target_info = nikke_list.value.find((val) => val.resource_id === resource_id);

    const target_costume = target_info?.costumes?.find((val) => val.id === costume_id);
    return MI_CHARACTER_URL({
      resource_id: Number(resource_id),
      skin_index: target_costume?.costume_index,
    });
  });

  return { mi_image };
}
