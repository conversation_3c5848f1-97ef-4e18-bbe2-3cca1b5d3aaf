import { EquipFunctions } from "packages/types/shiftyspad";
import { useUserEquipContent } from "@/api/shiftyspad";
import { queryIdsByResourceId } from "@/shiftyspad/service/character";
import { logger } from "@/shiftyspad/const";

type ResAwaitedType<T extends (...args: any) => any> =
  ReturnType<T> extends Promise<infer U> ? U : never;

type EquipReqData = ResAwaitedType<(typeof useUserEquipContent)["run"]>;

export function useEquipRequest(options: { patchRoleReqParams: <T>(params: T) => Promise<T> }) {
  const { patchRoleReqParams } = options;
  const user_nikke_equip_info = new Map<
    number,
    {
      promise: ReturnType<(typeof useUserEquipContent)["run"]> | null;
      data: {
        character_id: number;
        equip_contents: (EquipFunctions | void)[];
      } | null;
    }
  >();

  const batchQueryEquipData = async (resource_ids: number[]) => {
    const character_ids = await Promise.all(
      resource_ids.map(async (id) => {
        const ids = await queryIdsByResourceId(id);
        return {
          ids,
          resource_id: id,
        };
      }),
    );
    const all_ids = character_ids.map((v) => v.ids).flat();
    if (!all_ids.length) {
      return;
    }
    const req_data = await patchRoleReqParams({ character_ids: all_ids });
    const { player_equip_contents = [] } = await useUserEquipContent.run(req_data);
    // 取消合并请求重试.
    // if (result !== 0) {
    //   return;
    // }
    // 分组
    const equip_data_all = new Map<
      number,
      EquipReqData["player_equip_contents"][number]["equip_contents"]
    >();
    player_equip_contents.forEach((val) => {
      const { character_id, equip_contents = [] } = val;
      equip_data_all.set(character_id, equip_contents);
    });
    character_ids.forEach((id_data) => {
      const { resource_id, ids } = id_data;
      const res: Omit<EquipReqData, "result"> = {
        player_equip_contents: [],
      };
      ids.forEach((id) => {
        res.player_equip_contents.push({
          character_id: id,
          equip_contents: equip_data_all.get(id)!,
        });
      });
      user_nikke_equip_info.set(resource_id, {
        data: {
          character_id: resource_id,
          equip_contents: mergeEquipContents(res),
        },
        promise: null,
      });
    });
  };

  const mergeEquipContents = (data: Omit<EquipReqData, "result">) => {
    const equip_contents = new Array<EquipFunctions | void>(4);
    const { player_equip_contents = [] } = data;
    const tlog_equip_contents = player_equip_contents.slice().sort((pre, next) => {
      return Number(pre.character_id) - Number(next.character_id);
    });
    for (let i = 0; i < tlog_equip_contents.length; i++) {
      const historical_data: { equip_contents: EquipFunctions[] } = player_equip_contents[i];
      const { equip_contents: t_equip_contents = [] } = historical_data ?? {};
      for (let j = 0; j < equip_contents.length; j++) {
        const part_data = t_equip_contents[j];
        if (part_data?.equip_effects.length > 0 && part_data?.equip_modify_effect) {
          equip_contents[j] = part_data;
        }
      }
    }
    return equip_contents;
  };

  // 记录某个角色下的装备数据
  const getPlayerEquipContent = async (params: { resource_id: number }) => {
    const { resource_id: id } = params;
    const character_ids = await queryIdsByResourceId(id);
    if (!user_nikke_equip_info.get(id)) {
      user_nikke_equip_info.set(id, {
        promise: null,
        data: null,
      });
    }
    const request_record = user_nikke_equip_info.get(id)!;
    if (request_record.data) {
      return request_record.data;
    }
    if (!request_record.promise) {
      request_record.promise = (async () => {
        const req_data = await patchRoleReqParams({ character_ids });
        return useUserEquipContent.run(req_data);
      })();
    }
    const data = await request_record.promise;
    const equip_contents = mergeEquipContents(data);
    request_record.data = {
      character_id: id,
      equip_contents,
    };
    return request_record.data;
  };

  return {
    user_nikke_equip_info,
    batchQueryEquipData,
    getPlayerEquipContent,
  };
}
