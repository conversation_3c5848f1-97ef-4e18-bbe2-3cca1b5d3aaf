import { computed, toRefs } from "vue";

import { useToast } from "@/components/ui/toast";
import { useDialog } from "@/components/ui/dialog";

import { t } from "@/locales";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useUserStore } from "@/shiftyspad/stores/user";
import { ShiftyUserInstance } from "@/shiftyspad/composable/game-data";
import { useSendAddFriendRequestWithPrivacyPermission } from "@/api/post";
import { useGetPrivacySetting } from "@/api/user-privacy";
import { storeToRefs } from "pinia";

export function useAddFriendIngame(shiftys_user: ShiftyUserInstance) {
  const userStore = useUserStore();
  const { bindRole } = useBindRole();
  const { self_user_id, has_role } = storeToRefs(userStore);
  const { user_role_info: self_role } = toRefs(userStore.self_shifty_user);

  const { show: toast } = useToast();
  const { show: showDialog } = useDialog();
  const addFriend = useSendAddFriendRequestWithPrivacyPermission();

  const show_add_friend = computed(() => {
    return (
      shiftys_user.is_client.value &&
      privacy_settings.value?.allow_friend_request_via_game_card === 1 &&
      self_role.value?.area_id === shiftys_user.user_role_info.value?.area_id
    );
  });

  const { data: privacy_settings } = useGetPrivacySetting(
    computed(() => ({ intl_openid: shiftys_user.id.value })),
    {
      enabled: computed(
        () => !!self_user_id.value && has_role.value && shiftys_user.id.value !== "self",
      ),
    },
  );

  const onAddFriend = async () => {
    if (!userStore.self_shifty_user.has_role) {
      showDialog({
        title: t("notice"),
        content: t("please_bind_role_first"),
        confirm_text: t("bind"),
        cancel_text: t("cancel"),
        callback: ({ value, close }) => {
          if (value === "confirm") bindRole();
          close();
        },
      });
      return;
    }
    await addFriend.mutateAsync({
      friend_uid: shiftys_user.id.value,
    });
    toast({ text: t("friend_card_request_successfully"), type: "success" });
  };

  return {
    onAddFriend,
    addFriend,
    show_add_friend,
  };
}
