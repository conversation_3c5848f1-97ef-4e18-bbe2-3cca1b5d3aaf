/**
 * @description 获取玩家的 * 角色的好感度剧情状态;
 *
 * 用于列表页入口控制
 */

import { useCharacterData } from "@/shiftyspad/composable/character";
import { useUserStore } from "@/shiftyspad/stores/user";
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";

import { t } from "@/locales";

import { computed } from "vue";
import { storeToRefs } from "pinia";

export function useRoleScene(resource_id: number) {
  const user = useUserStore();

  // 开个口子让玩家可以通过客态链接访问到角色的好感度剧情
  const { user_nikkelist_info } = storeToRefs(user);
  const { character_data } = useCharacterData(String(resource_id));

  user.initUser().then(async () => {
    await user.initUserNikkeInfo();
  });

  const user_nikke = computed(() =>
    user_nikkelist_info.value.find((val) => val.resource_id === resource_id),
  );

  const scenario_list = computed(() => {
    const list = (character_data.value?.attractive_scenario_list ?? []).slice();

    return list.map((item) => {
      return Object.assign({}, item, {
        is_lock: (user_nikke.value?.attractive_level ?? 0) < item.attractive_level,
      });
    });
  });

  const showBlock = (options?: { onClose?: () => void; onOk?: () => void }) => {
    NoticePopup.info({
      title: t("notice"),
      message: t("scene_unlock_tip"),
      confirm: t("confirm"),
      onOk: () => {
        options?.onOk?.();
      },
      onClose: () => {
        options?.onClose?.();
      },
    });
  };

  return {
    scenario_list,
    showBlock,
  };
}
