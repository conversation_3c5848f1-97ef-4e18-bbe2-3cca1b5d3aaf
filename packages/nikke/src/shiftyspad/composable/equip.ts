import {
  format_level,
  settings_equip_increase_bouns,
  settings_equip_corp_bounus,
} from "@/shiftyspad/const/setting";
import { CharacterData } from "@/shiftyspad/types/character";
import { NikkeEquip } from "packages/types/shiftyspad";

export function getEquipCorp(equip: any) {
  const corp_map = {
    1: "elysion",
    2: "missilis",
    3: "tetra",
    4: "pilgrim",
    7: "abnormal",
  } as const;
  // @ts-ignore
  const corp = corp_map[equip.equip_manufacturer_bonus];
  if (!corp || Number(equip.equip_manufacturer_bonus) === 0) {
    return null;
  }
  return corp as (typeof corp_map)[keyof typeof corp_map];
}

/**
 * 获取装备对战斗力的计算属性
 */
export function getEquipAttr(params: {
  equip: NikkeEquip;
  type: "Atk" | "Hp" | "Defence" | "None";
  character: CharacterData;
}) {
  const { equip, type, character } = params;
  const equip_level = format_level(equip?.equip_level);
  if (type === "None") return 0;
  // const bonus = format_level(equip?.equip_manufacturer_bonus)
  // @ts-ignore
  const stat_sum = (equip.stat ?? []).reduce((prev, cur) => {
    if (cur?.stat_type === type) {
      return prev + cur.stat_value;
    }
    return prev;
  }, 0);

  const corp = getEquipCorp(equip);
  const character_corp = character.corporation;
  const corp_bonus = character_corp.toLowerCase() === corp ? settings_equip_corp_bounus : 0;

  // [1+企业增益 （ConfigGameTable EquipCorporationStatBonus）+（强化等级 * ConfigGameTable EquipIncreaseStatBonus）]
  const result = stat_sum * (1 + corp_bonus + equip_level * settings_equip_increase_bouns);
  return Math.round(result);
}
