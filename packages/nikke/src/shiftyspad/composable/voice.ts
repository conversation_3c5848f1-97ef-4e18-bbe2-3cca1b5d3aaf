import { ref } from "vue";
import { useCV } from "@/shiftyspad/stores/role/cv";
import { GET_VOICE_URL } from "@/shiftyspad/const/urls";
import { useToast } from "@/components/ui/toast";
import { getSpecialBrowserType, getClientType } from "packages/utils/tools";
import { t } from "@/locales";

export function useVoice(speech_id: string) {
  const { show } = useToast();
  const loading = ref<boolean>(false);
  const playing = ref<boolean>(false);
  const store = useCV();
  const progress_bar = ref(0);
  const player = ref<HTMLAudioElement>();
  const cbs = ref<Map<string, Set<Function>>>(new Map());
  const callCb =
    (key: string) =>
    (...args: any[]) => {
      const set = cbs.value.get(key);
      if (set) {
        set.forEach((cb) => {
          cb(...args);
        });
      }
    };
  const addCb = (key: string) => (cb: Function) => {
    if (!cbs.value.get(key)) {
      cbs.value.set(key, new Set());
    }
    const cb_map = cbs.value.get(key);
    cb_map?.add(cb);
  };
  const onError = addCb("error");
  const onCancel = addCb("cancel");

  const updateProgress = () => {
    const currentTime = player.value?.currentTime;
    const duration = player.value?.duration;
    if (duration && currentTime) {
      progress_bar.value = (currentTime / duration) * 100;
    } else {
      progress_bar.value = 0;
    }
  };

  const playVoice = () => {
    if (getSpecialBrowserType() === "line" && getClientType() === "Android") {
      show({ text: t("load_content_error"), type: "info" });
      return;
    }
    const voice_url = GET_VOICE_URL({
      cv_lang: store.cv_lang.lang,
      speech_id,
      format: "mp3",
    });
    if (store.current_play !== voice_url) {
      if (store.current_player) {
        store.current_player.pause();
        store.current_player.currentTime = 0;
      }
      store.aborter?.();
      playing.value = false;
    }
    if (store.current_play !== voice_url || !store.current_play) {
      loading.value = true;
      player.value = new Audio();
      player.value.autoplay = true;
      player.value.src = voice_url;
      store.setVoice({
        url: voice_url,
        player: player.value,
        abortcontrol: () => {
          callCb("cancel");
          loading.value = false;
        },
      });
    }
    if (!player.value?.paused) {
      player.value?.pause();
      playing.value = false;
      return;
    } else if (!loading.value) {
      player.value?.play().catch((_err) => {
        playing.value = false;
      });
      playing.value = true;
    }

    player.value?.addEventListener("error", (err: any) => {
      loading.value = false;
      playing.value = false;
      cbs.value.forEach((cb) => callCb("error")(err));
    });
    player.value?.addEventListener("timeupdate", () => {
      loading.value = false;
      updateProgress();
    });
    player.value?.addEventListener("pause", () => {
      playing.value = false;
      updateProgress();
    });
    player.value?.addEventListener("play", () => {
      playing.value = true;
    });
    player.value?.addEventListener("load", () => {
      loading.value = false;
      player.value?.play().catch((_err) => {
        playing.value = false;
      });
    });
    player.value?.addEventListener("ended", () => {
      progress_bar.value = 0;
      playing.value = false;
      loading.value = false;
      store.setVoice(null);
    });
  };

  document.addEventListener("visibilitychange", () => {
    if (document.hidden) {
      store.current_player?.pause();
    } else {
      store.current_player?.play();
    }
  });

  return {
    loading,
    playing,
    player,
    progress_bar,
    updateProgress,
    playVoice,
    onError,
    onCancel,
  };
}
