import { get } from "lodash-es";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import { ref, computed, watch, onMounted, Ref } from "vue";

import {
  BurstField,
  ClassField,
  CoperationField,
  RarityValueMap,
} from "@/shiftyspad/types/assets_field_map";
import { useUser } from "@/store/user";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import { useJsonStore } from "@/shiftyspad/stores/json";
import { CODE_ALL_CONFIGS } from "packages/configs/code";
import { getStandardizedGameId } from "packages/utils/standard";
import { Nikke, ElementField, NikkeListData } from "packages/types/shiftyspad";
import { getSelfSortSetting, getUserSortSetting, setUserSortSetting } from "@/api/shiftyspad";
import { isTypeStructureEqual } from "packages/utils/tools";
import { base64Decode } from "packages/utils/encrypt";
import { useRoleDisplayState } from "@/shiftyspad/stores/role/display";

export enum SortType {
  // power = 'nikke_power',
  level = "nikke_level",
  break = "nikke_break",
  rare = "nikke_rarity",
  attr_level = "nikke_attract_level",
}

export enum FilterType {
  burst = "brust_stage",
  class = "class",
  element = "element",
  coperation = "coperation",
  weaponType = "weaponType",
}

export type FilterItem = {
  label: string;
  value: string;
  selected: boolean;
};

const burstFilterTextMap = {
  [BurstField.Step1]: "Ⅰ",
  [BurstField.Step2]: "Ⅱ",
  [BurstField.Step3]: "Ⅲ",
};

export function useCustomSortNikkeList(params: {
  user_nikkelist_info: Ref<Nikke[]>;
  is_client: boolean;
  uid: string;
}) {
  const { is_client, uid } = params;
  const { user_info } = storeToRefs(useUser());
  const { filter_player_nikke_list, is_nikke_list_loading } = useSortNikkeList({
    ...params,
    disable_ss: true,
  });
  const { isLoading: is_loading_self_sort, data: self_setting, refetch } = getSelfSortSetting({});
  const { isLoading: is_loading_sort, data: client_setting } = getUserSortSetting(
    {
      game_id: (() => {
        if (uid.includes("-")) return uid.split("-")[0];
        return getStandardizedGameId();
      })(),
      openid: (() => {
        if (uid.includes("-")) return uid.split("-")[1];
        return uid;
      })(),
    },
    {
      enabled: computed(() => is_client && Boolean(user_info.value?.intl_openid)),
    },
  );

  const max_num = 5;
  const default_sort_list = ref<number[]>([]);
  watch(
    () => self_setting.value,
    () => {
      default_sort_list.value = (self_setting.value?.list ?? []).slice();
    },
  );
  onMounted(() => {
    default_sort_list.value = (self_setting.value?.list ?? []).slice();
  });

  const reach_max = computed(() => default_sort_list.value.length >= max_num);
  const is_hidden = computed(
    () =>
      client_setting.value?.code === CODE_ALL_CONFIGS.NoPermissionVisitShiftyspad &&
      filter_player_nikke_list.value.length === 0,
  );

  const changeSort = (nikke: { resource_id: number }) => {
    const cur_index = default_sort_list.value.findIndex((v) => v === nikke.resource_id);
    if (cur_index >= 0) {
      default_sort_list.value.splice(cur_index, 1);
    } else {
      if (default_sort_list.value.length >= max_num) {
        return;
      }
      default_sort_list.value.push(nikke.resource_id);
    }
  };

  const updateSettings = async (sort_list: { resource_id: number }[]) => {
    await setUserSortSetting.run({ list: sort_list.map((nikke) => nikke.resource_id) });
    await refetch();
  };

  const main_page_sorted_nikke_list = computed(() => {
    const list = (() => {
      if (is_client) return client_setting.value?.list ?? [];
      return self_setting.value?.list ?? [];
    })();
    const result = [];
    const temp = (filter_player_nikke_list.value ?? []).slice();
    for (let index = 0, old_order = 0; index < max_num; index++) {
      const resource_id = list[index];
      const custom_index = temp.findIndex((nikke) => Number(nikke.resource_id) === resource_id);
      if (!resource_id || custom_index === -1) result.unshift(temp[old_order++]);
      result.unshift(temp[custom_index]);
    }
    return result.filter(Boolean).slice(0, max_num);
  });

  return {
    refetch,
    changeSort,
    updateSettings,
    reach_max,
    is_hidden,
    self_setting,
    default_sort_list,
    filter_player_nikke_list,
    main_page_sorted_nikke_list,
    is_nikke_list_loading,
    is_loading_self_sort,
    is_loading_sort,
  };
}

// 此处可以拓展默认初始拍序逻辑;
export function useSortNikkeList(params: {
  user_nikkelist_info: Ref<Nikke[]>;
  disable_ss?: boolean; // 禁用 ss 的持久记录
}) {
  const route = useRoute();
  const jsonStore = useJsonStore();
  const { getValue, setValue } = useRoleDisplayState();
  const { user_nikkelist_info } = params;
  const { nikke_list, is_nikke_list_loading } = storeToRefs(jsonStore);

  const getRouteUid = () => {
    const route_uid =
      route.query[COMMON_QUERY_KEYS.EncodedUid] || route.query[COMMON_QUERY_KEYS.OpenId];
    if (!route_uid) return "self";
    try {
      return base64Decode(String(route_uid));
    } catch (err) {
      console.error(err);
      return String(route_uid);
    }
  };
  /**
   * Obtained
   */
  const obtained_filter_key = `obtainer_filter_${getRouteUid()}`;
  const obtainer_filter = ref(Boolean(getValue(obtained_filter_key)));
  const changeFilter = (value: boolean) => {
    obtainer_filter.value = value;
    setValue(obtained_filter_key, obtainer_filter.value);
  };

  /**
   * SORT
   */
  type SortItem = { name: SortType; selected: boolean; sort: 1 | 0 };
  const sort_filter_key = `sort_filter_${getRouteUid()}`;
  const getInitialSortConfig = (): SortItem[] => {
    const default_sort: SortItem[] = [
      {
        name: SortType.level,
        selected: true,
        sort: 1,
      },
      {
        name: SortType.break,
        selected: false,
        sort: 1,
      },
      {
        name: SortType.rare,
        selected: false,
        sort: 1,
      },
      {
        name: SortType.attr_level,
        selected: false,
        sort: 1,
      },
    ];
    const ss_sort_record = getValue(sort_filter_key);
    if (!ss_sort_record || params.disable_ss) {
      return default_sort;
    }
    return isTypeStructureEqual(ss_sort_record, default_sort) ? ss_sort_record : default_sort;
  };

  const sort_config = ref(getInitialSortConfig());

  const resetSort = () => {
    sort_config.value.forEach((v) => {
      if (v.name !== "nikke_level") {
        v.selected = false;
      } else {
        v.selected = true;
      }
    });
    setValue(sort_filter_key, null);
  };
  const setSort = (name: string) => {
    let index = -1;
    sort_config.value.forEach((sort, i) => {
      if (sort.name === name) {
        index = i;
      } else {
        sort_config.value[i].selected = false;
      }
    });
    if (index > -1) {
      if (sort_config.value[index].selected) {
        // 已选，切换升序降序
        sort_config.value[index].sort = sort_config.value[index].sort === 1 ? 0 : 1;
      } else {
        sort_config.value[index].sort = 1;
        sort_config.value[index].selected = true;
      }
      setValue(sort_filter_key, sort_config.value);
    }
  };

  /**
   * FILTER
   */
  const filter_key = `filter_${getRouteUid()}`;
  // 创建一个filter 数组; data_map, key 为定位Icon或者显示为text的内容, value为筛选值
  const createFilter = (data_map: Record<string, string>) => {
    return Object.entries(data_map).map(([key, value]) => {
      return {
        label: key,
        selected: false,
        value: value as any,
      };
    });
  };

  const getInitialFilters = () => {
    const init_state = {
      [FilterType.class]: createFilter(ClassField),
      [FilterType.coperation]: createFilter(CoperationField),
      [FilterType.weaponType]: createFilter({
        SMG: "SMG",
        RL: "RL",
        AR: "AR",
        SG: "SG",
        SR: "SR",
        MG: "MG",
      }),
      [FilterType.element]: createFilter(
        Object.fromEntries(
          Object.entries(ElementField).map(([key, val]) => [
            `character_type_${key.toLowerCase()}`,
            val,
          ]),
        ),
      ),
      [FilterType.burst]: createFilter(
        Object.fromEntries(
          Object.entries(BurstField)
            .filter(([key]) => key !== BurstField.AllStep)
            .map(([key]) => [
              burstFilterTextMap[key as keyof typeof burstFilterTextMap],
              key.toLowerCase(),
            ]),
        ),
      ),
    };
    const lss_filter_record = getValue(filter_key);
    if (!lss_filter_record || params.disable_ss) {
      return init_state;
    }
    return isTypeStructureEqual(lss_filter_record, init_state) ? lss_filter_record : init_state;
  };

  const resetFilter = (name?: FilterType) => {
    // 如果不传参数===全部清空
    if (!name) {
      Object.values(filters.value).forEach((val) => {
        val.forEach((item) => (item.selected = false));
      });
      setValue(filter_key, null);
    } else {
      filters.value[name]?.forEach((f) => {
        f.selected = false;
      });
      setValue(filter_key, filters.value);
    }
  };
  const is_use_filter = computed(() => {
    let show = false;
    for (let key in filters.value) {
      const some = filters.value[key as FilterType].filter((f) => f.selected);
      if (some.length) {
        show = true;
        break;
      }
    }
    return show;
  });

  const filters = ref<Record<FilterType, FilterItem[]>>(getInitialFilters());

  const setFilterValue = (type: FilterType, value?: string) => {
    filters.value[type]?.forEach((item) => {
      if (item.selected) {
        item.selected = !(item.value === value);
      } else {
        item.selected = item.value === value;
      }
    });
    setValue(filter_key, filters.value);
  };

  /**
   * NAME
   */
  const name_search = ref("");
  const resetSearch = () => {
    name_search.value = "";
  };

  /**
   * NIKKE_LIST
   */
  const filter = <T extends NikkeListData>(list: T[]): T[] => {
    const field_map = {
      [FilterType.class]: "class",
      [FilterType.burst]: "use_burst_skill",
      [FilterType.coperation]: "corporation",
      [FilterType.element]: "element_id.element.element",
      [FilterType.weaponType]: "shot_id.element.weapon_type",
    } as const;
    if (!is_use_filter.value) {
      return (list ?? []).slice();
    }
    const result = (list ?? []).slice();
    const result_set = new Set(result.map((v) => v.resource_id));
    for (const key of Object.keys(filters.value) as FilterType[]) {
      const filter_group = filters.value[key];
      const filter_type = key;

      // 同类别筛选, 并集
      const sub_set = new Set<number>();
      // 忽略未选
      if (filter_group.every((single_filt) => !single_filt.selected)) continue;
      for (const single_filt of filter_group) {
        if (!single_filt.selected) continue;
        const template = (list ?? []).slice();
        template.forEach((nikke) => {
          const key = field_map[filter_type];
          const nikke_value = get(nikke, key);
          // 小红帽特殊逻辑
          if (key === "use_burst_skill") {
            if (nikke_value === BurstField.AllStep) {
              sub_set.add(nikke.resource_id);
            }
          }
          if (
            single_filt.selected &&
            String(nikke_value).toLowerCase() === single_filt.value?.toLowerCase()
          ) {
            // 同类别取并集
            sub_set.add(nikke.resource_id);
          }
        });
      }
      const arr = Array.from(result_set);
      arr.forEach((exist) => {
        if (!sub_set.has(exist)) result_set.delete(exist);
      });
    }
    return result.filter((item) => result_set.has(item.resource_id));
  };

  const searcher = <T extends NikkeListData>(list: T[]): T[] => {
    const result = (list ?? []).slice();
    return result.filter((nikke) =>
      nikke.name_localkey.name.toLowerCase().includes(name_search.value.toLowerCase()),
    );
  };

  const sorter = <T extends NikkeListData>(list: T[]) => {
    const result = (list ?? []).slice();
    const sort_map: Record<SortType, string> = {
      [SortType.attr_level]: "attractive_level",
      [SortType.rare]: "original_rare",
      [SortType.level]: "level",
      [SortType.break]: "limit_break",
    };

    const cur_sort = sort_config.value.find((v) => v.selected);
    if (!cur_sort) {
      return result;
    }
    const getSortVal = (pre: number, next: number) => {
      return {
        desc: Number(pre) - Number(next),
        asc: Number(next) - Number(pre),
      };
    };
    return result.sort((pre, next) => {
      let pre_val = get(pre, sort_map[cur_sort.name]),
        next_val = get(next, sort_map[cur_sort.name]);
      if (cur_sort.name === SortType.rare) {
        pre_val = RarityValueMap[pre_val];
        next_val = RarityValueMap[next_val];
      }
      const { desc, asc } = getSortVal(pre_val, next_val);
      const { desc: id_desc, asc: id_asc } = getSortVal(pre.id, next.id);
      const sort_result = cur_sort.sort === 1 ? asc : desc;
      const id_result = cur_sort.sort === 1 ? id_asc : id_desc;
      return sort_result === 0 ? id_result : sort_result;
    });
  };

  // 经过过滤, 玩家 nikke & 全部nikke混合的
  type MixedListItem =
    | (NikkeListData & { is_obtained: false })
    | (Nikke & NikkeListData & { is_obtained: true });
  // @ts-ignore
  const filter_combine_nikke_list = computed<MixedListItem[]>(() => {
    const obtained_set = new Set<number>(filter_player_nikke_list.value.map((v) => v.resource_id));
    return [
      ...filter_player_nikke_list.value.map((v) => Object.assign({ is_obtained: true }, v)),
      ...filter_nikke_list.value
        .filter((nikke) => !obtained_set.has(nikke.resource_id))
        .map((v) => Object.assign({ is_obtained: false }, v)),
    ];
  });

  // 经过过滤, 全部nikke
  const filter_nikke_list = computed(() => {
    return filter(sorter(searcher(nikke_list.value)));
  });

  // 经过过滤, 玩家nikke
  const filter_player_nikke_list = computed(() => {
    const nikke_data_map: Map<number, NikkeListData> = new Map();
    (nikke_list.value || []).forEach((nikke) => {
      nikke_data_map.set(nikke.resource_id, nikke);
    });
    const user_nikke_list = user_nikkelist_info.value || [];
    const player_nikke_list = user_nikke_list
      .map((n) => {
        const nikke = nikke_data_map.get(n.resource_id);
        if (!nikke) return null;
        return Object.assign({}, nikke, n);
      })
      .filter(Boolean) as (Nikke & NikkeListData)[];
    return filter(sorter(searcher(player_nikke_list)));
  });

  return {
    // sort
    resetSort,
    setSort,
    sort_config,

    // filter
    setFilterValue,
    resetFilter,
    filters,
    is_use_filter,

    // search
    resetSearch,
    name_search,

    // obtained
    obtainer_filter,
    changeFilter,

    is_nikke_list_loading,

    // list data
    filter_combine_nikke_list,
    filter_player_nikke_list,
    filter_nikke_list,
  };
}
