import { useUserStore } from "@/shiftyspad/stores/user";
import { Role } from "packages/types/games";
import {
  useClientVisitInfo,
  queryBindAccountBonusStatus,
  getBindAccountBonus,
  useUserSavedInfo,
} from "@/api/shiftyspad";
import { getRoleInfo, saveRoleInfo } from "@/api/games";
import { mountDialogComponent } from "@/utils/mount";
import { computed, onUnmounted, ref, toRefs } from "vue";
import { report } from "packages/utils/tlog";

import { promiseWrap } from "@/shiftyspad/utils/comm";

import { get } from "lodash-es";
import { STANDARD_URL_ROLE_ID } from "packages/configs/standard";
import { replaceUrlParams, urlSearchObjectify } from "packages/utils/qs";
import {
  STANDARD_GAME_ID_KEY,
  STANDARD_URL_ZONE_ID_KEY,
  STANDARD_URL_AREA_ID_KEY,
} from "packages/configs/standard";

import SelectRole from "@/shiftyspad/components/common/popups/SelectServer.vue";
import { isInGame } from "packages/utils/tools";
import { useRoute } from "vue-router";

export type GameRoleQuery = {
  is_client: boolean;
  uid: string;

  // uid + areaid 组合查询
  area_id?: string;
};

export function useGameRole(query: GameRoleQuery) {
  const uid_ref = ref(query.uid ?? "");
  const is_client_ref = ref(query.is_client ?? false);
  const user_role_info = ref<Role | null>({} as any);
  const has_role = computed(() =>
    Boolean(user_role_info.value?.role_id || user_role_info.value?.role_name),
  );

  const get_user_ingame_roleid = (): string => {
    if (!isInGame()) return "";
    const query = urlSearchObjectify();
    return get(query, STANDARD_URL_ROLE_ID) ?? "";
  };
  /**
   * @description ingame rebind;
   * 用户游戏外绑定的角色是A, 进入角色B的游戏内页面, 此时需要换绑为游戏内角色B
   */
  const ingameReBindRole = async (cur_role?: Role | null): Promise<Role | null> => {
    if (get_user_ingame_roleid() !== cur_role?.role_id) {
      const ingame_role = await getIngameRoleInfo();
      if (!ingame_role) return null;
      await saveRoleInfo(ingame_role);
      return ingame_role;
    }
    return cur_role!;
  };

  /**
   * 获取游戏内对应的角色信息
   */
  const getIngameRoleInfo = async () => {
    const query = urlSearchObjectify();
    const area_id = Number(get(query, STANDARD_URL_AREA_ID_KEY));
    // 场景: 玩家在选区前就进入, 带登录态
    if (!area_id) {
      return null;
    }
    const role_info = await getRoleInfo({
      area_id,
      game_id: get(query, STANDARD_GAME_ID_KEY),
      zone_id: get(query, STANDARD_URL_ZONE_ID_KEY),
    });
    return role_info?.role_list?.[0] ?? null;
  };

  /**
   * 初始化当前游戏角色状态; 可能存在重新绑定的场景.
   */
  const initRoleInfo = promiseWrap(
    async (): Promise<{
      role_info: Role | null;
      is_rebinded: boolean;
    }> => {
      if (!user_role_info.value) {
        // hacked: user_role_info 初始为 {}
        return Promise.resolve({
          role_info: null,
          is_rebinded: false,
        });
      }
      if (has_role.value) {
        return Promise.resolve({
          role_info: user_role_info.value,
          is_rebinded: false,
        });
      }
      // 主态/客态当前绑定的角色
      const binded_role = await getBindedRole();
      // 主态游戏内访问自己, 可能需要重新绑定
      if (isInGame() && !is_client_ref.value) {
        const ingame_real_role = await ingameReBindRole(binded_role);
        // ingame 可能不带角色信息; 此时不重新绑定
        user_role_info.value = ingame_real_role ?? binded_role ?? null;
        return {
          role_info: ingame_real_role ?? binded_role,
          is_rebinded: ingame_real_role
            ? binded_role?.role_id !== ingame_real_role?.role_id
            : false,
        };
      }
      user_role_info.value = binded_role;
      return {
        role_info: user_role_info.value,
        is_rebinded: false,
      };
    },
  );

  const updateState = (state: { is_client: boolean; uid: string }) => {
    const { is_client, uid } = state;
    is_client_ref.value = is_client;
    uid_ref.value = uid;
  };

  const queryClientRole = () => {
    return useClientVisitInfo.run(
      Object.assign({ uid: uid_ref.value }, query.area_id ? { area_id: query.area_id } : {}),
    );
  };

  const resetRoleInfo = () => {
    user_role_info.value = {} as any;
  };

  /**
   * 查询 shiftysuser 绑定的角色
   */
  const getBindedRole = async () => {
    const role_res = await (async () => {
      return is_client_ref.value ? await queryClientRole() : await useUserSavedInfo.run({});
    })();
    const { icon, role_info: remote_role_info } = role_res ?? {};
    if (!remote_role_info) return null;
    return Object.assign({ icon }, remote_role_info);
  };

  return {
    uid_ref,
    has_role,
    is_client_ref,
    user_role_info,

    get_user_ingame_roleid,
    getIngameRoleInfo,
    getBindedRole,
    initRoleInfo,
    updateState,
    resetRoleInfo,
  };
}

export function useBindRole(params?: { select_hint?: string }) {
  const route = useRoute();
  const user_store = useUserStore();
  const { user_role_info, is_client, has_role } = toRefs(user_store);
  const { unmount, instance } = mountDialogComponent(SelectRole, {
    hint: params?.select_hint,
  });

  const selectGameRole = (): Promise<Role> => (instance as any).invoke();

  onUnmounted(() => {
    unmount();
  });

  const show_bind_role = computed(() => {
    // 游戏外 或者 游戏内 + areaid 不存在 + 新用户无绑定角色
    return (
      !isInGame() ||
      (isInGame() && !Number(route.query?.[STANDARD_URL_AREA_ID_KEY]) && !has_role.value)
    );
  });

  const getRoleBonus = async () => {
    const { mission_has_done = false } = await queryBindAccountBonusStatus().run({});
    if (mission_has_done) {
      return;
    }
    await getBindAccountBonus().run(
      {
        tag_id: "3", // nikke 独立站写死.
      },
      {
        ignore_toast: true,
      },
    );
  };

  /**
   * 默认登录完成后刷新页面
   */
  const bindRole = async (options?: { reload_page: boolean }) => {
    const { reload_page = true } = options ?? {};
    const role_info = await selectGameRole();
    const query = urlSearchObjectify();

    report.standalonesite_user_bind_ret.cm_click({ ret: 0, roleid: role_info?.role_id });
    const refresh = () => {
      setTimeout(() => {
        location.reload();
      }, 500);
    };

    await saveRoleInfo(role_info);

    // 特殊case, 游戏内公告公告处area_id为0, 替换url
    if (!Number(get(query, STANDARD_URL_AREA_ID_KEY)) && isInGame()) {
      replaceUrlParams({
        [STANDARD_URL_AREA_ID_KEY]: String(role_info.area_id),
      });
    }

    reload_page && refresh();
    return role_info;
  };

  const makeSureBindRole = async (options?: { reload_page: boolean }) => {
    if (is_client.value) {
      return;
    }
    // initRoleInfo: 游戏内自动绑定; 如无绑定, 游戏外会返回空;
    return user_store.initRoleInfo().finally(async () => {
      if (!has_role.value) {
        return bindRole(options);
      } else {
        return user_role_info.value;
      }
    });
  };

  return {
    // 可否展示绑角色
    show_bind_role,
    // 绑定角色领奖
    getRoleBonus,
    // 仅获取角色信息, 不绑定
    selectGameRole,
    // 获取角色信息并绑定角色（默认会刷新页面）
    bindRole,
    // 检查有无绑定角色, 如果无, 就让绑定角色（默认会刷新页面）
    makeSureBindRole,
  };
}
