import { storeToRefs } from "pinia";
import { useJsonStore } from "../stores/json";
import { computed } from "vue";

export function useNikkeSettings() {
  const jsonStore = useJsonStore();
  const { nikke_list } = storeToRefs(jsonStore);
  const ssr_nikkes = computed(
    () => nikke_list.value.filter((nikke) => nikke.original_rare === "SSR").length ?? 0,
  );
  const sr_nikkes = computed(
    () => nikke_list.value.filter((nikke) => nikke.original_rare === "SR").length ?? 0,
  );
  const r_nikkes = computed(
    () => nikke_list.value.filter((nikke) => nikke.original_rare === "R").length ?? 0,
  );
  const total_nikkes = computed(() => ssr_nikkes.value + sr_nikkes.value + r_nikkes.value);
  //200+nikke总数+[(3*SSRnikke数+2*SRnikke数*1.334](默认全部3星) +50
  const max_level = computed(() =>
    Math.round(
      200 + total_nikkes.value + 50 + (3 * ssr_nikkes.value + 2 * sr_nikkes.value) * 1.334,
    ),
  );
  return {
    max_level,
    ssr_nikkes,
    sr_nikkes,
    r_nikkes,
    total_nikkes,
  };
}
