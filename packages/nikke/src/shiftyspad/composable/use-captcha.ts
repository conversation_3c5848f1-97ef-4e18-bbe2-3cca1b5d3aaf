import { STANDARD_CAPTCH_LANG_MAP } from "packages/configs/standard";
import { injectScript } from "packages/utils/tools";
import { getStandardizedLang } from "packages/utils/standard";
import { useToast } from "@/components/ui/toast";

/**
 * 商品兑换业务申请的 captcha appId
 */
const REWARD_CAPTCHA_APP_ID = "188950686";

/**
 * 腾讯验证码脚本源
 * @see https://iwiki.woa.com/p/1794654729
 */
const CAPTCHA_SCRIPT_SRC = "https://global.captcha.gtimg.com/TCaptcha-global.js";

/**
 * 使用 captcha 验证码
 * @see https://iwiki.woa.com/p/1794654729
 */
export const useCaptcha = (appId: string = REWARD_CAPTCHA_APP_ID) => {
  const lang = getStandardizedLang();
  const toast = useToast();

  const initCaptcha = async () => {
    const TencentCaptcha = window.parent.TencentCaptcha;
    if (!TencentCaptcha) {
      await injectScript(CAPTCHA_SCRIPT_SRC, window.parent.document.head);
    }
  };

  /**
   * 获取 captcha 验证码
   */
  const getCaptchaCode = async () => {
    await initCaptcha();
    return new Promise<{ ticket: string; randstr: string }>((resolve, reject) => {
      const TencentCaptcha = window.parent.TencentCaptcha;
      try {
        const captcha = new TencentCaptcha(
          appId,
          (res: {
            ret: number;
            ticket: string;
            randstr: string;
            errorCode: number;
            errorMessage: string;
          }) => {
            console.log(res);
            if (res.ret === 0 && res.ticket) {
              resolve({
                ticket: res.ticket,
                randstr: res.randstr,
              });
            } else {
              // 用户取消
              if (res.ret === 2) {
                reject(new Error("cancel"));
              } else {
                toast.show({ text: res.errorMessage, type: "error" });
                reject(new Error(res.errorMessage));
              }
            }
          },
          {
            needFeedBack: false,
            loading: true,
            userLanguage:
              STANDARD_CAPTCH_LANG_MAP[lang as keyof typeof STANDARD_CAPTCH_LANG_MAP] || "en",
            // enableDarkMode: "force",
          },
        );
        captcha.show();
      } catch (error) {
        reject(error);
      }
    });
  };

  return {
    getCaptchaCode,
  };
};
