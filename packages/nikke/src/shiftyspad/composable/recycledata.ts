import { getRecycleData, getAttractiveData } from "@/shiftyspad/service/character";
import { CharacterData } from "@/shiftyspad/types/character";
import { computed, Ref, ref } from "vue";
import { format_level } from "@/shiftyspad/const/setting";
import { Nikke, UserBattleInfo } from "packages/types/shiftyspad";

export const useRecycle = (params: {
  character_data: CharacterData;
  user_nikkelist_info: Ref<Nikke[]>;
  user_battle_info: Ref<UserBattleInfo | null>;
}) => {
  const { character_data, user_battle_info, user_nikkelist_info } = params;
  const class_key = character_data.class.toLowerCase();
  const enterprise_key = (() => {
    const result = character_data.corporation.toLowerCase();
    if (result === "missilis") {
      return "missills";
    }
    return result;
  })();
  const user_character = computed<Nikke | undefined>(() => {
    return (user_nikkelist_info.value ?? []).find(
      (v: any) => v.name_code === character_data.name_code,
    );
  });

  const class_level_key = `${class_key}_level`;
  const enterprise_level_key = `${enterprise_key}_level`;

  const recycle_data = ref<any[]>([]);
  const attractive_data = ref<any[]>([]);

  Promise.all([getRecycleData(), getAttractiveData()]).then(([a, b]) => {
    recycle_data.value = a;
    attractive_data.value = b;
  });

  const enterprise_level = computed(() => {
    const outpost = user_battle_info.value?.outpost_detail ?? {};
    return format_level(outpost[enterprise_level_key as keyof typeof outpost]);
  });
  const enterprise_buff = computed(() => {
    const level = recycle_data.value.find(
      (v) => v.recycle_sub_type === character_data.corporation.toUpperCase(),
    );
    if (!level || enterprise_level.value < 0 || !enterprise_level.value) {
      return {
        hp: 0,
        atk: 0,
        def: 0,
      };
    }
    return {
      hp: level.hp * enterprise_level.value,
      atk: level.attack * enterprise_level.value,
      def: level.defence * enterprise_level.value,
    };
  });

  /**
   * 基础循环室, 只加生命值
   */
  const basic_recycle_level = computed(() => {
    const outpost = user_battle_info.value?.outpost_detail ?? { recyle_level: 0 };
    return format_level(outpost["recyle_level"]);
  });
  const basic_recycle_buff = computed(() => {
    const level = recycle_data.value.find((v) => v.recycle_sub_type === "Personal");
    if (!level || class_level.value < 0 || !class_level.value) {
      return {
        hp: 0,
        atk: 0,
        def: 0,
      };
    }
    return {
      hp: level.hp * basic_recycle_level.value,
      atk: level.attack * basic_recycle_level.value,
      def: level.defence * basic_recycle_level.value,
    };
  });

  const class_level = computed(() => {
    const outpost = user_battle_info.value?.outpost_detail ?? {};
    return format_level(outpost[class_level_key as keyof typeof outpost]);
  });
  const class_buff = computed(() => {
    const level = recycle_data.value.find((v) => v.recycle_sub_type === character_data.class);
    if (!level || class_level.value < 0 || !class_level.value) {
      return {
        hp: 0,
        atk: 0,
        def: 0,
      };
    }
    return {
      hp: level.hp * class_level.value,
      atk: level.attack * class_level.value,
      def: level.defence * class_level.value,
    };
  });

  const attractive_level = computed(() => {
    return format_level(user_character.value?.attractive_level);
  });

  const attractive_buff = computed(() => {
    if (!attractive_data.value.length || !user_character.value) {
      return {
        hp: 0,
        atk: 0,
        def: 0,
      };
    } else {
      const level = attractive_data.value.find(
        (v) => v.attractive_level === attractive_level.value,
      );
      if (!level) {
        return {
          hp: 0,
          atk: 0,
          def: 0,
        };
      }
      return {
        hp: level[`${class_key}_hp_rate`],
        atk: level[`${class_key}_attack_rate`],
        def: level[`${class_key}_defence_rate`],
      };
    }
  });

  const top2_level = computed(() => {
    const outpost = user_battle_info.value?.outpost_detail ?? [];

    return [
      {
        key: class_level_key,
        val: format_level(outpost[class_level_key as keyof typeof outpost]),
      },
      {
        key: enterprise_level_key,
        val: format_level(outpost[enterprise_level_key as keyof typeof outpost]),
      },
    ];
  });

  return {
    enterprise_level,
    class_level,
    enterprise_key,
    class_key,
    class_level_key,
    enterprise_level_key,
    top2_level,
    enterprise_buff,
    attractive_buff,
    class_buff,
    attractive_level,
    basic_recycle_level,
    basic_recycle_buff,
  };
};
