/**
 * useQuery 改写 玩家数据
 */

import {
  CharacterData,
  EquipData,
  EquipOptions,
  EquipFunctions,
} from "@/shiftyspad/types/character";
import {
  getEquipmentOptions,
  getEquipData,
  getCubeByCubeId,
  getFavoriteItemId,
} from "@/shiftyspad/service/character";
import { ref, computed, Ref } from "vue";
import { CubeItem, FavoriteItem, Nikke, NikkeEquip } from "packages/types/shiftyspad";
import { aegis } from "@/shiftyspad/service/rum";
import { ShiftyUserInstance } from "@/shiftyspad/composable/game-data";
import { getEquipAttr } from "./equip";
import { promiseWrap } from "@/shiftyspad/utils/comm";
import { useQuery } from "@tanstack/vue-query";
import { storeToRefs } from "pinia";
import { useUserStore } from "../stores/user";

export type PlayerEquipData = NikkeEquip &
  EquipData & {
    buffs: EquipFunctions["equip_effects"];
  };

/**
 * @description 获取某个用户某个角色的数据
 * 装备、魔方、珍藏品
 */
export function usePlayerRoleData(params: {
  character: CharacterData;
  shiftys_user: Ref<ShiftyUserInstance>;
}) {
  const { shiftys_user, character } = params;
  const { self_user_id, uid } = storeToRefs(useUserStore());
  const { user_nikkelist_info, getPlayerEquipContent } = shiftys_user.value;

  const user_character = computed<Nikke | undefined>(() => {
    return (user_nikkelist_info.value ?? []).find((v: any) => v.name_code === character.name_code);
  });
  const wrap = (v?: number) => v ?? 0;

  const loadUserNikkes = async () => {
    await shiftys_user.value.initUserNikkeInfo();
  };

  useQuery({
    queryKey: [user_character.value?.resource_id ?? 0],
    queryFn: async () => {
      await loadUserNikkes();
      await updateEquipData();
      return equip_data.value;
    },
    enabled: computed(
      () => Boolean(user_character.value?.resource_id ?? 0 > 0) && Boolean(self_user_id.value),
    ),
  });

  /**
   * cube
   * 注意: 魔方相关等级要 - 1
   * TODO: use game-data types
   */
  const cube_data = computed<{
    atk: number;
    def: number;
    hp: number;
    level1: number;
    level2: number;
    power: number;
  } | null>(() => {
    if (!cube_item.value) return null;
    const target = cube_item.value;
    const level = user_character.value!.cube_level;
    return {
      atk: wrap(target.atk.at(level - 1)),
      def: wrap(target.def.at(level - 1)),
      hp: wrap(target.hp.at(level - 1)),
      power: wrap(target.powers.at(level - 1)),
      level1: wrap(target.level1.at(level - 1)),
      level2: wrap(target.level2.at(level - 1)),
      level3: wrap(target.level3.at(level - 1)),
    };
  });
  const { data: cube_item } = useQuery<CubeItem>({
    queryKey: ["cube", user_character.value?.cube_id],
    queryFn: async () => {
      return await getCubeByCubeId(user_character.value!.cube_id);
    },
    enabled: computed(() => Boolean((user_character.value?.cube_id ?? 0) > 0)),
  });

  /**
   * favoriteitem
   */
  const favorite_data = computed<{
    atk: number;
    def: number;
    hp: number;
    level1: number;
    level2: number;
    power: number;
  } | null>(() => {
    if (!favorite_item.value) return null;
    const target = favorite_item.value;
    const level = user_character.value!.item_level;
    console.error(favorite_item.value);
    return {
      atk: wrap(target.atk.at(level)),
      def: wrap(target.def.at(level)),
      hp: wrap(target.hp.at(level)),
      power: wrap(target.powers.at(level)),
      level1: wrap(target.level1.at(level)),
      level2: wrap(target.level2.at(level)),
    };
  });

  const { data: favorite_item } = useQuery<FavoriteItem>({
    queryKey: ["favorite", user_character.value?.item_id],
    queryFn: async () => {
      return await getFavoriteItemId(user_character.value!.item_id);
    },
    enabled: computed(() => Boolean((user_character.value?.item_id ?? 0) > 0)),
  });

  /**
   * equipment
   */
  // TODO: refactor with computed
  const equip_option = ref<EquipOptions[]>([]);
  const equip_data = ref<EquipData[]>([]);
  const equip_info = ref<EquipFunctions[]>([]);

  const equip_attr = computed(() => {
    const get_equip_attr = (type: "Atk" | "Hp" | "Defence") => {
      const result = (equips.value ?? []).reduce((sum, equip) => {
        return (
          sum +
          getEquipAttr({
            equip,
            character,
            type: type,
          })
        );
      }, 0);
      return result;
    };
    return {
      atk: get_equip_attr("Atk"),
      def: get_equip_attr("Defence"),
      hp: get_equip_attr("Hp"),
    };
  });

  const updateEquipData = promiseWrap(() => {
    return Promise.all([
      getEquipData(),
      getEquipmentOptions(),
      getPlayerEquipContent({
        resource_id: user_character.value!.resource_id,
      }),
    ])
      .then(([equipData, equipOption, equipContent]) => {
        equip_data.value = equipData || [];
        equip_option.value = equipOption || [];
        equip_info.value = (equipContent?.equip_contents ?? []) as any;
        // computed to ref
        update_equips();
      })
      .catch((error) => {
        aegis.error("[init-data-failed]:", error);
      });
  });

  const equips = computed(() => _equips.value ?? []);
  const { data: _equips } = useQuery<PlayerEquipData[]>({
    queryKey: [uid.value, self_user_id.value, user_character.value?.resource_id],
    queryFn: async () => {
      await updateEquipData();
      return update_equips() ?? [];
    },
    enabled: computed(
      () => Boolean(user_character.value?.resource_id ?? 0 > 0) && Boolean(self_user_id.value),
    ),
  });

  const update_equips = () => {
    return user_character.value?.player_equips.map((equip: NikkeEquip, index: number) => {
      const result = (() => {
        const info = equip_info.value[index];
        if (info) {
          const { equip_effects } = info;
          return Object.assign({}, equip, {
            buffs: equip_effects.map((v) => {
              const { equipment_option_id } = v;
              const option_detail = equip_option.value.find(
                (opt) => opt.state_effect_group_id === Number(equipment_option_id),
              );
              return Object.assign({}, option_detail, v);
            }),
          });
        } else {
          return Object.assign({}, equip, {
            buffs: [],
          });
        }
      })();
      const temp = equip_data.value.find((item) => item.id === equip.equip_id);
      return Object.assign({}, result, JSON.parse(JSON.stringify(temp ?? {})) as EquipData);
    });
  };

  return {
    user_character,
    equip_info,
    equips,
    equip_attr,
    equip_data,
    loadUserNikkes,

    // v1.6 展示
    favorite_item,
    favorite_data,
    cube_data,
    cube_item,
  };
}
