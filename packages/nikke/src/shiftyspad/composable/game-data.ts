import { promiseWrap } from "@/shiftyspad/utils/comm";
import { queryResouceIdByCode } from "@/shiftyspad/service/character";
import { useUserBaiscGameInfo, useUserBattleInfo, useUserNikkesInfo } from "@/api/shiftyspad";

import { useEquipRequest } from "./role/equip-data";
import { GameRoleQuery, useGameRole } from "./game-role";

import { isInGame } from "packages/utils/tools";
import { Nikke, UserBattleInfo, UserBasicInfo } from "packages/types/shiftyspad";

import { computed, ref } from "vue";
import { Logger } from "m-web-logger";

export type ShiftyUserInstance = ReturnType<typeof createShiftysUser>;

/**
 * 获取一个 uid (gameid-openid)对应的游戏角色数据
 *
 * TODO:
 *
 * change it to class?
 *
 * - usePlayerBasicInfo done
 * - nikke-role-list/index.vue: useSortNikkeList + useCustomSortNikkeList done
 * - useEquipData(getPlayerEquipContent) done
 * - useRecycle done
 * - usePlayerRoleData done
 * - useLevelData done
 * - mockstore
 */
export function createShiftysUser(query: GameRoleQuery) {
  // basic
  const logger = new Logger({ label: "shiftys_user" });
  const {
    uid_ref,
    has_role,
    user_role_info,
    is_client_ref,
    getIngameRoleInfo,
    resetRoleInfo,
    initRoleInfo,
    updateState,
  } = useGameRole(query);

  // data
  const user_basic_info = ref<UserBasicInfo | null>(null);
  const user_battle_info = ref<UserBattleInfo | null>(null);
  const user_nikkelist_info = ref<Nikke[]>([]);

  const log_status = (method_name: string) =>
    logger.info(method_name, ` client - ${is_client_ref.value}`, `uid - ${uid_ref.value}`);

  const validate = () => {
    if (is_client_ref.value) return Boolean(uid_ref.value);
    return !is_client_ref.value;
  };

  // methods
  const patchRoleReqParams = async <T>(params: T) => {
    const { area_id } = query;
    if (area_id && is_client_ref.value) {
      return Object.assign({}, params, {
        uid: uid_ref.value,
        role_info: { area_id: Number(area_id) },
      });
    }
    if (is_client_ref.value) {
      return Object.assign({}, params, { uid: uid_ref.value });
    }
    if (isInGame()) {
      const role_info = await getIngameRoleInfo();
      return Object.assign({}, params, { role_info });
    }
    return params;
  };

  const { getPlayerEquipContent, batchQueryEquipData, user_nikke_equip_info } = useEquipRequest({
    patchRoleReqParams,
  });

  const resetUserInfo = () => {
    resetRoleInfo();
    user_nikke_equip_info.clear();
    user_basic_info.value = {} as any;
    user_battle_info.value = {} as any;
    user_nikkelist_info.value = [] as any;
  };

  // 基本信息
  const initUserBasicInfo = promiseWrap(async () => {
    if (Object.entries(user_basic_info.value ?? {}).length > 0 || !validate()) {
      return;
    }
    log_status("initUserBasicInfo");
    const req_data = await patchRoleReqParams({});
    const data = await useUserBaiscGameInfo.run(req_data);
    user_basic_info.value = data ?? {};
  });

  // 通关信息
  const initUserBattleInfo = promiseWrap(async () => {
    if (Object.entries(user_battle_info.value ?? {}).length || !validate()) {
      return;
    }
    log_status("initUserBattleInfo");
    const req_data = await patchRoleReqParams({});
    const data = await useUserBattleInfo.run(req_data);
    user_battle_info.value = data;
  });

  // 用户尼姬
  const initUserNikkeInfo = promiseWrap(async () => {
    if (user_nikkelist_info.value?.length || !validate()) {
      return;
    }
    log_status("initUserNikkeInfo");
    const req_data = await patchRoleReqParams({});
    const data = await useUserNikkesInfo.run(req_data);
    const { player_nikkes = [] } = data;

    const getIds = player_nikkes.map(async (nikke) => {
      nikke.resource_id = await queryResouceIdByCode(nikke);
      return nikke as Nikke & { resource_id: number };
    });
    user_nikkelist_info.value = (await Promise.all(getIds)).filter(
      (nikke: Nikke & { resource_id: number }) => {
        return nikke.resource_id !== 0;
      },
    );
  });

  const initAllData = () => {
    return Promise.all([
      initRoleInfo(),
      initUserNikkeInfo(),
      initUserBattleInfo(),
      initUserBasicInfo(),
    ]);
  };

  return {
    initAllData,
    updateState,
    resetUserInfo,
    initRoleInfo,
    initUserBasicInfo,
    initUserBattleInfo,
    initUserNikkeInfo,
    getPlayerEquipContent,
    batchQueryEquipData,

    has_role,
    user_role_info,
    user_basic_info,
    user_battle_info,
    user_nikkelist_info,
    is_client: computed(() => is_client_ref.value),
    id: computed(() => (is_client_ref.value ? uid_ref.value : "self")),
  };
}
