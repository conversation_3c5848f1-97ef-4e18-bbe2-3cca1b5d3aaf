/**
 * @description 获取玩家的 * 角色的好感度剧情状态;
 *
 * 用于直接获取详情的情况
 */

import {
  getScene,
  getCharacterData,
  getSceneCharacterSettings,
} from "@/shiftyspad/service/character";
import { Speech } from "@/shiftyspad/types/character";
import { aegis } from "@/shiftyspad/service/rum";
import { getLang } from "@/shiftyspad/const";

import { useUserStore } from "@/shiftyspad/stores/user";
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";

import { ref } from "vue";

import { t } from "@/locales";
import router from "@/router";
import { storeToRefs } from "pinia";

export function useAttractiveScene(scene_id: string) {
  const user = useUserStore();
  const is_loading = ref(true);
  const { shiftys_user, user_nikkelist_info } = storeToRefs(user);

  const scene_list = ref<Speech[]>([]);
  const shown_list = ref<Speech[]>([]);

  const stop_index = ref<number>(0);
  const selected = ref<Set<number>>(new Set());
  const cur_choices = ref<number[]>([]);

  const init = () => {
    user
      .initUser()
      .then(() => validateAuth())
      .catch(() => showBlock())
      .finally(() => (is_loading.value = false));
  };

  const validateAuth = async () => {
    const character_id = getCharacterId();
    if (!character_id) {
      aegis.error("[get-scene-error]: invalid scene id - ", scene_id);
      throw new Error("invalid scene id");
    }

    const character_scene_settings = await getSceneCharacterSettings();
    const character_target = character_scene_settings.find((val) => val.id === character_id);
    const { resource_id } = character_target ?? {};
    if (!resource_id) {
      aegis.error("[get-scene-error]: invalid resource_id - ", resource_id);
      throw new Error(`invalid resource_id - ${resource_id} - character_id - ${character_id}`);
    }
    // 防止并发请求，先让当前页面的请求完
    await shiftys_user.value.initUserNikkeInfo();
    const user_nikke = user_nikkelist_info.value.find((val) => val.resource_id === resource_id);
    if (!user_nikke) {
      return showBlock();
    }
    const character_data = await getCharacterData(String(resource_id));
    const target_scene = character_data.attractive_scenario_list?.find(
      (val) => val.attractive_scenario_group_id === scene_id,
    );

    if (user_nikke.attractive_level < (target_scene?.attractive_level ?? 0)) {
      return showBlock();
    }
    initSpeech();
  };

  /**
   * data-hanlder 已测试通过全部符合;
   *
   * scene_characeter_list 通过 scene_character_name 反查resource_id
   */
  const getCharacterId = () => {
    const attractive_scene_reg = /^d_nikke_([a-zA-Z0-9_]*)_0\d$/;
    const match_result = attractive_scene_reg.exec(scene_id);
    return match_result?.at(1);
  };

  const initSpeech = () => {
    getScene({ scene_id: scene_id, lang: getLang() })
      .then((data) => {
        scene_list.value = data;
        insertSpeech();
      })
      .catch((err) => {
        aegis.error("[get-scene-error]:", scene_id, err);
      });
  };

  const insertSpeech = () => {
    for (let i = stop_index.value; i < scene_list.value.length; i++) {
      const cur = scene_list.value[i];
      cur.selected = false;
      shown_list.value.push(cur);

      if (cur.speech_window === "Choice") {
        cur_choices.value.push(i);
      }
      if (cur.speech_window === "Choice" && scene_list.value[i + 1]?.speech_window !== "Choice") {
        stop_index.value = i;
        break;
      }
      if (cur.jump_target && cur.speech_window !== "Choice") {
        i = scene_list.value.findIndex((v) => v.id === cur.jump_target);
        // for循环最后会+1，这里要-1
        i--;
      }
    }
  };

  const jump = (talk: Speech) => {
    if (!talk.jump_target || talk.selected) {
      return;
    }
    const selected_index = scene_list.value.findIndex((v) => v === talk);
    selected.value.add(selected_index);
    talk.selected = true;
    cur_choices.value
      .filter((i) => !selected.value.has(i))
      .forEach((i) => (scene_list.value[i].id = ""));
    const next_index = scene_list.value.findIndex((v) => v.id === talk.jump_target);
    stop_index.value = next_index;
    insertSpeech();
  };

  const showBlock = () => {
    NoticePopup.info({
      title: t("notice"),
      message: t("scene_unlock_tip"),
      confirm: t("confirm"),
      onOk: () => {
        router.back();
      },
      onClose: () => {
        router.back();
      },
    });
  };

  init();

  return {
    scene_list,
    shown_list,
    is_loading,
    jump,
  };
}
