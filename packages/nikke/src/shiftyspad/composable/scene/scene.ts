import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { computed, ref, toRaw, toRefs } from "vue";

import { ICONS_URL } from "@/shiftyspad/const/urls";
import { useJsonStore } from "@/shiftyspad/stores/json";
import { useUserStore } from "@/shiftyspad/stores/user";
import { NO_VALUE_OR_ZERO, VALUE_IS_HIDDEN } from "@/shiftyspad/const/setting";

import { StroylineMainItem, StageData, SceneListData } from "packages/types/game-scene";

export function findValidScene(progress: number, stage_list: StageData[]) {
  // 要在stagelist找到对应的chapter_id
  let stage_index = stage_list.findIndex((stage) => {
    return stage.id === progress;
  });
  if (stage_index === -1) {
    // 没有的可以认为是当前json数据最后一关
    stage_index = stage_list.length - 1;
    // return {}
  }
  let stage = stage_list[stage_index];
  const stage_scene = (stage ? stage.exit_scenario || stage.enter_scenario : "") || "";
  if (stage_scene === "" || stage_scene.indexOf("d_main") !== 0) {
    // 注意，staget_list的scenario不一定存在，也有可能是无效剧情，所以需要往前倒推到一个有效的剧情
    // 以 “d_main”开头的
    let found = false;
    while (!found && stage_index > 0) {
      stage_index -= 1;
      stage = stage_list[stage_index];
      const stage_scene = (stage ? stage.exit_scenario || stage.enter_scenario : "") || "";
      if (stage_scene === "" || stage_scene.indexOf("d_main") !== 0) {
        continue;
      } else {
        found = true;
      }
    }
  }
  return stage_list[stage_index] || {};
}

export function useScene() {
  const json = useJsonStore();
  const user = useUserStore();
  const { t } = useI18n();
  const { scene_main_list, stage_list, scene_sudden_list } = storeToRefs(json);
  // 永远使用自己的数据
  const { self_shifty_user } = toRaw(user);
  const { user_battle_info } = toRefs(self_shifty_user);

  const loading = ref(false);

  const init = async () => {
    loading.value = true;
    try {
      // 防止限频率; 先让客态的跑完
      await user.initUser();
      await user.initUserBattleInfo();
      await self_shifty_user.initUserBattleInfo();
    } finally {
      loading.value = false;
    }
  };

  const showMainList = computed(() => {
    // 通关进度
    const unlock = user_battle_info.value?.normal_progress ?? 0;
    const stage = findValidScene(unlock, stage_list.value) as StageData;
    // 这里要注意，stage_list的chapter_id!==scene_list里的，相差1
    let unlock_chapter = stage ? stage.chapter_id - 1 : 0;
    // exit和enter都不一定有，也有可能只有其中一个
    const unlock_section = stage ? stage.exit_scenario || stage.enter_scenario : "";

    if (unlock === NO_VALUE_OR_ZERO || unlock === 0 || unlock === VALUE_IS_HIDDEN) {
      // 没有进度的新号，特殊处理
      unlock_chapter = -1;
    }
    const list = scene_main_list.value.map((scene: SceneListData) => {
      // const unlock_chapter_all = scene.sub_category_id.value > unlock_chapter;
      const unlock_chapter_all = scene.sub_category_id.value - unlock_chapter;
      let unlock_section_index = -1;
      if (unlock_chapter_all > 0) {
        // 设置-1把所有的小节都设置为lock=true
        unlock_section_index = -1;
      } else if (unlock_chapter_all < 0) {
        // 设置999把所有的小节都设置为lock=false
        unlock_section_index = 999;
      } else {
        // 相同的情况要找小节
        unlock_section_index = scene.sub_category_id.scenes.value.findIndex((s) => {
          return s.value.scenario_group_id === unlock_section;
        });
        if (stage.name_localkey && stage.name_localkey.name.match(/BOSS$/)) {
          // 以boss结尾的通关了表示这一章解锁完毕，后边同一章节有些 d_main_xx_af_xx的after章节直接解锁
          unlock_section_index = scene.sub_category_id.scenes.value.length - 1;
        }
      }
      const sections = scene.sub_category_id.scenes.value.map((s, index) => {
        return {
          id: s.value.id,
          name: s.scenario_name_localkey.scenario_name,
          scenario_group_id: s.value.scenario_group_id,
          locked: index > unlock_section_index,
        };
      });
      return {
        id: scene.id,
        locked: unlock_chapter_all > 0,
        img: ICONS_URL({ path: "album/chapter", name: scene.sub_category_thumbnail }),
        order: t("chapter", {
          num:
            scene.sub_category_id.value >= 10
              ? scene.sub_category_id.value
              : `0${scene.sub_category_id.value}`,
        }),
        name: scene.sub_category_name_localkey.chapter_name,
        sectionCur: sections.filter((s) => s.locked === false).length,
        sectionTotal: sections.length,
        sections: sections,
      } as StroylineMainItem;
    });
    return list;
  });

  const showSuddenList = computed(() => {
    const list = scene_sudden_list.value.map((scene: SceneListData) => {
      return {
        id: scene.id,
        locked: false,
        img: ICONS_URL({ path: "album/outpost", name: scene.sub_category_thumbnail }),
        // order: `${scene.sub_category_id.value}`,
        order: "OUTPOST",
        name: scene.sub_category_name_localkey.chapter_name,
        sectionCur: 0,
        sectionTotal: scene.sub_category_id.scenes.value.length,
        sections: scene.sub_category_id.scenes.value.map((s) => {
          return {
            id: s.value.id,
            name: s.scenario_name_localkey.scenario_name,
            scenario_group_id: s.value.scenario_group_id,
            locked: false,
          };
        }),
      } as StroylineMainItem;
    });
    return list;
  });

  return {
    init,
    loading,
    showMainList,
    showSuddenList,
  };
}
