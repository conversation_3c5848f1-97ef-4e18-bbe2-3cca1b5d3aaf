import { useJsonStore } from "@/shiftyspad/stores/json";
import { storeToRefs } from "pinia";
import { computed, Ref } from "vue";
import { useI18n } from "vue-i18n";
import { NO_VALUE_OR_ZERO, VALUE_IS_HIDDEN } from "@/shiftyspad/const/setting";
import { UserBasicInfo, UserBattleInfo } from "packages/types/shiftyspad";
import { CODE_ALL_CONFIGS } from "packages/configs/code";

export function usePlayerBasicInfo(data: {
  user_battle_info: Ref<UserBattleInfo | null>;
  user_basic_info: Ref<UserBasicInfo | null>;
}) {
  const { t } = useI18n();
  const { user_basic_info: basic_info, user_battle_info: n_battle_info } = data;

  const battle_info = computed(() => {
    if (n_battle_info.value?.outpost_detail) {
      // 后台拼写错误
      n_battle_info.value.outpost_detail.missilis_level =
        n_battle_info.value.outpost_detail.missills_level;
    }
    return n_battle_info.value;
  });

  const jsonStore = useJsonStore();
  const { stage_list, stage_list_hard } = storeToRefs(jsonStore);
  const remain_interception = computed(() => {
    const remain = basic_info.value?.remain_interception || 0;
    if (remain === NO_VALUE_OR_ZERO) {
      // 没有解锁
      return "3/3";
    } else if (remain === VALUE_IS_HIDDEN) {
      return "-";
    } else {
      return remain + "/3";
    }
  });

  const remain_rookie_arena = computed(() => {
    return tlog_warp(basic_info.value?.used_rookie_arena || 0);
  });

  const remain_special_arena = computed(() => {
    return tlog_warp(basic_info.value?.used_special_arena || 0);
  });

  const material_key_ordered_list = [
    "gem",
    "credit",
    "gold_mileage_ticket",
    "silver_mileage_ticket",
    "battle_data_set",
    "core_dust",
    "recruit_voucher",
    "advanced_recruit_voucher",
  ] as const;
  const material_list = computed(() => {
    return material_key_ordered_list.map((key) => {
      const material_info: any = basic_info.value?.material_detail ?? {};
      const material = Number(
        (basic_info.value as any)?.[key] || (material_info ? material_info[key] : 0) || 0,
      ) as number;
      if (material <= 0) {
        return {
          label: key,
          num: "--",
        };
      }
      // 最多5个字符
      if (material <= 99999) {
        // 5个
        return {
          label: key,
          num: material,
        };
      }
      if (material <= 9999999) {
        // 4个+一个K
        return {
          label: key,
          num: `${Math.floor(material / 1000)}K`,
        };
      }
      if (material <= 9999999999) {
        return {
          // 4个+一个M
          label: key,
          num: `${Math.floor(material / 1000000)}M`,
        };
      }
      return {
        label: key,
        num: `9999+M`,
      };
    });
  });

  const player_activity = computed(() => {
    return basic_info.value?.player_activity;
  });

  const tlog_warp = (num: number) => {
    if (num === NO_VALUE_OR_ZERO) return 0;
    if (num === VALUE_IS_HIDDEN) return "-";
    return num;
  };

  // 头像数量
  const avatar_frame = computed(() => {
    return tlog_warp(basic_info.value?.avatar_frame || 0);
  });

  // 时装数量
  const costume = computed(() => {
    return tlog_warp(battle_info.value?.costume || 0);
  });

  const tower_floor = computed(() => {
    return tlog_warp(battle_info.value?.tower_floor || 0);
  });

  // 前哨基地
  const outpost_detail = computed(() => {
    const outpostType = [
      "elysion_level",
      "attacker_level",
      "tetra_level",
      "defender_level",
      "pilgrim_level",
      "supporter_level",
      "missilis_level",
      "sychro_level",
      "abnormal_level",
      "recyle_level",
    ] as const;
    const detail = battle_info.value?.outpost_detail || ({} as any);
    const obj: Record<string, { label: string; value: number | string }> = {};
    outpostType.forEach((d) => {
      const level = detail[d] || NO_VALUE_OR_ZERO;
      obj[d] = {
        label: t(d),
        value: tlog_warp(level),
      };
    });
    return obj;
  });

  // 通关信息
  const normal_progress = computed(() => {
    const remain = battle_info.value?.normal_progress || 0;
    if (remain === NO_VALUE_OR_ZERO || remain === 0) {
      return "0-0";
    } else if (remain === VALUE_IS_HIDDEN) {
      return "-";
    } else {
      const stage = stage_list.value.filter((s) => s.id === remain)[0];
      if (stage) {
        const name = stage.name_localkey.name;
        return name.replace("STAGE", "").replace("BOSS", "");
      } else {
        // 没有的展示当前json数据最后一关
        const lastStage = stage_list.value[stage_list.value.length - 1];
        const name = lastStage?.name_localkey.name;
        return name?.replace("STAGE", "").replace("BOSS", "") ?? "-";
        // return remain
      }
    }
  });
  const hard_progress = computed(() => {
    const remain = battle_info.value?.hard_progress || 0;
    if (remain === NO_VALUE_OR_ZERO || remain === 0) {
      return "0-0";
    } else if (remain === VALUE_IS_HIDDEN) {
      return "-";
    } else {
      const stage = stage_list_hard.value.filter((s) => s.id === remain)[0];
      if (stage) {
        const name = stage.name_localkey.name;
        return name.replace("STAGE", "").replace("BOSS", "").replace("HARD", "");
      } else {
        // 没有的展示当前json数据最后一关
        const lastStage = stage_list_hard.value[stage_list_hard.value.length - 1];
        const name = lastStage?.name_localkey.name;
        return name?.replace("STAGE", "").replace("BOSS", "") ?? "-";
        // return remain
      }
    }
  });

  // nikke数量
  const nikke_num = computed(() => {
    return (basic_info.value?.own_nikke_cnt ?? 0 > 0) ? basic_info.value!.own_nikke_cnt : 0;
  });

  // 是否已解锁outpost
  const unlockOutpost = computed(() => {
    // 要看主线进度是否通关了6002016
    if (battle_info.value?.normal_progress ?? 0 >= 6002016) {
      return true;
    }
    return false;
  });

  // 展示outpost进度
  const outpost_defense = computed(() => {
    if (unlockOutpost.value) {
      if (basic_info.value?.outpost_defense === VALUE_IS_HIDDEN) return "0";
      return (basic_info.value?.outpost_defense ?? 0 > 0) ? basic_info.value?.outpost_defense : 0;
    } else {
      // 未解锁返回0
      return 0;
    }
  });

  /**
   * @description 各模块隐藏状况
   *
   * @see {link https://tapd.woa.com/tapd_fe/10019121/story/detail/1010019121122730026?menu_workitem_type_id=0}
   * @see {link https://www.figma.com/design/Dwq94QeRNOuCwoQvoB6GtE/%E3%80%90V1.4%E3%80%91%E7%8B%AC%E7%AB%8B%E7%AB%99%E8%AE%BE%E8%AE%A1%E7%A8%BF?node-id=309-17577&p=f&t=dZ1bF24tmHiF9Sqb-0}
   */

  // packages/nikke/src/api/index.ts, use_non_zero_ret
  const is_gamecard_close = computed(() => {
    return (
      // @ts-ignore
      battle_info.value?.code === CODE_ALL_CONFIGS.NoPermissionVisitShiftyspad &&
      // @ts-ignore
      basic_info.value?.code === CODE_ALL_CONFIGS.NoPermissionVisitShiftyspad
    );
  });

  const getHiidenStatus = (arr: any[]) => {
    return (
      is_gamecard_close.value ||
      (arr.length > 0 && arr.every((val) => String(val) === String(VALUE_IS_HIDDEN)))
    );
  };

  const is_battle_info_hidden = computed(() =>
    getHiidenStatus([battle_info.value?.normal_progress, battle_info.value?.hard_progress]),
  );

  const is_daily_mission_hidden = computed(() =>
    getHiidenStatus([basic_info.value?.outpost_defense, basic_info.value?.remain_interception]),
  );

  const is_outpost_info_hidden = computed(() =>
    getHiidenStatus(Object.values(battle_info.value?.outpost_detail ?? {})),
  );

  const is_material_info_hidden = computed(() =>
    getHiidenStatus(Object.values(basic_info.value?.material_detail ?? {})),
  );

  const is_basic_info_hidden = computed(() =>
    getHiidenStatus([battle_info.value?.normal_progress, basic_info.value?.own_nikke_cnt]),
  );

  return {
    hard_progress,
    nikke_num,
    unlockOutpost,
    outpost_defense,
    normal_progress,
    costume,
    avatar_frame,
    tower_floor,
    outpost_detail,
    player_activity,
    remain_special_arena,
    remain_rookie_arena,
    material_list,
    remain_interception,

    // 数据隐藏
    is_gamecard_close,
    is_outpost_info_hidden,
    is_basic_info_hidden,
    is_battle_info_hidden,
    is_material_info_hidden,
    is_daily_mission_hidden,
  };
}
