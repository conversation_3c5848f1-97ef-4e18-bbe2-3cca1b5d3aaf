import { ICONS_URL } from "@/shiftyspad/const/urls";
import { CharacterData } from "@/shiftyspad/types/character";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { ElementField, ClassField, WeaponType, BurstField } from "packages/types/shiftyspad";
import { WeaponIconMap } from "@/shiftyspad/const/assets-map";

type Corp = "abnormal" | "pilgrim" | "tetra" | "missilis" | "elysion";

export function getIconByClass(class_name: string) {
  return ICONS_URL({
    path: "atlas_common_class",
    name: `icn_class_${class_name?.toLowerCase()}`,
  });
}

export function getCleanIconByCorp(corp: Corp) {
  const map: Record<Corp, string> = {
    abnormal: "05",
    pilgrim: "04",
    tetra: "03",
    missilis: "02",
    elysion: "01",
  };
  return ICONS_URL({
    path: "atlas_common_corp",
    name: `icn_corp_${map[corp] ?? "unknow"}`,
  });
}

export function getIconByCorp(corp: Corp) {
  return ICONS_URL({
    path: "atlas_common_corp",
    name: `img_logo_${corp.toLowerCase()}`,
  });
}

export function getWeaponAssetIcon(weapon: WeaponType) {
  return getAssetsImage(`icon-weapon-${WeaponIconMap[weapon]}.png`);
}

export function getClassAssetIcon(class_name: ClassField) {
  return getAssetsImage(`icon-job-${class_name.toLowerCase()}.png`);
}

export function getElementAssetIcon(element: ElementField) {
  return getAssetsImage(`icon-code-${element.toLowerCase()}.png`);
}

export function useCharacterIcon(character_data: CharacterData) {
  const class_icon = getIconByClass(character_data.class);

  const element_icon = ICONS_URL({
    path: "atlas_common_class",
    name: character_data.element_details[0].element_icon,
  });
  const element_icon_asset = getElementAssetIcon(character_data.element_details[0].element);

  const corp_icon = getIconByCorp(character_data.corporation.toLowerCase() as Corp);
  const clean_corp_icon = (() => {
    const corp = character_data.corporation.toLowerCase() as Corp;
    return getCleanIconByCorp(corp);
  })();

  const burst_icon = (() => {
    const getBurstIcon = (v: string) => {
      if (v === BurstField.AllStep) {
        return `icn_burst_all`;
      }
      return `icn_burst_0${character_data.use_burst_skill[4]}`;
    };
    return ICONS_URL({
      path: "atlas_common_class",
      name: getBurstIcon(character_data.use_burst_skill),
    });
  })();

  const rareness_icon = (() => {
    const getRareIconPath = () => {
      const icon_map: Record<string, string> = {
        ssr: "003",
        sr: "002",
        r: "001",
      };
      const original_rare = character_data.original_rare.toLocaleLowerCase();
      return `ele_grade_icon_${icon_map[original_rare]}`;
    };
    return ICONS_URL({
      path: "atlas_common_grade",
      name: getRareIconPath(),
    });
  })();

  return {
    class_icon,
    rareness_icon,
    element_icon,
    element_icon_asset,
    corp_icon,
    burst_icon,
    clean_corp_icon,
  };
}
