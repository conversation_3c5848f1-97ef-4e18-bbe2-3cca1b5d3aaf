import { getCharacterData, getLevelCost } from "@/shiftyspad/service/character";
import { CharacterData, LevelCost, LevelInfo } from "@/shiftyspad/types/character";
import { useMockDataStore } from "@/shiftyspad/stores/character";
import { computed, Ref, ref } from "vue";
import {
  default_grade,
  default_level,
  format_level,
  settings_attribute_skill1,
  settings_attribute_base_skll,
  settings_attribute_hp,
  settings_attribute_attack_ratio,
  settings_attribute_burst,
  settings_attribute_def,
  settings_attribute_skill2,
} from "@/shiftyspad/const/setting";
import { useRecycle } from "./recycledata";
import { usePlayerRoleData } from "./player";
import { aegis } from "@/shiftyspad/service/rum";
import { useNikkeSettings } from "@/shiftyspad/composable/nikke-leve";
import { ShiftyUserInstance } from "@/shiftyspad/composable/game-data";
import { useQuery } from "@tanstack/vue-query";

/**
 * FIXME: this is piece of shit. fix me later.
 */
export function useUserRoleLevelData(params: {
  character_data: CharacterData;
  shiftys_user: Ref<ShiftyUserInstance>;
  options?: {
    use_mock?: boolean;
  };
}) {
  const { shiftys_user, character_data, options } = params;
  const { use_mock = true } = options ?? {};
  const mockStore = useMockDataStore();
  const { max_level } = useNikkeSettings();
  const { user_battle_info, user_nikkelist_info } = shiftys_user.value;
  const {
    user_character,
    equips,
    cube_data,
    equip_attr,
    favorite_data,
    loadUserNikkes,
    init_player_equip_data,
  } = usePlayerRoleData({
    character: character_data,
    shiftys_user: shiftys_user,
  });
  const { enterprise_buff, attractive_buff, class_buff, basic_recycle_buff } = useRecycle({
    character_data,
    user_battle_info,
    user_nikkelist_info,
  });

  const level_cost_data = ref<LevelCost[]>([]);

  /**
   * 角色相关属性值: limit-break, core, grade, 规则
   * NIKKE 0X
    grade=0 core=0
    NIKKE 3X
    grade=3 core=0
    NIKKE +1
    grade=3 core=1
    NIKKE+7
    GRADE=3 CORE=7
   */
  const max_stars = computed(() => {
    const rare = character_data.original_rare?.toLowerCase();
    if (rare === "ssr") return 3;
    if (rare === "sr") return 2;
    return 0;
  });
  const max_limit_break = computed(() => {
    const rare = character_data.original_rare?.toLowerCase();
    if (rare === "ssr") return 10;
    if (rare === "sr") return 2;
    return 0;
  });
  const get_grade_core = (limit_break: number) => {
    const v = limit_break > max_limit_break.value ? max_limit_break.value : limit_break;
    if (v >= max_stars.value) {
      return {
        limit_break,
        grade: max_stars.value,
        core: v - max_stars.value,
      };
    }
    if (v <= 0) {
      return {
        core: 0,
        grade: 0,
        limit_break: 0,
      };
    } else {
      return {
        core: 0,
        limit_break,
        grade: limit_break,
      };
    }
  };
  const change_limit_break = (val: number) => {
    const { grade, core } = get_grade_core(val);
    mockStore.change({ key: "limit_break", min: 0, max: max_limit_break.value, val });
    mockStore.change({ key: "grade", min: 0, max: grade, val: grade });
    mockStore.change({
      key: "core",
      min: 0,
      max: max_limit_break.value - max_stars.value,
      val: core,
    });
  };

  // 等级消耗
  const cost_pending_state = getLevelCost()
    .then((v) => {
      level_cost_data.value = v;
    })
    .catch((err) => {
      aegis.error("[get-level-cost-failed]:", err);
    });

  /**
   * @description 各类角色属性的计算逻辑
   * grade -> 突破等级/星级, 仅适用于 HP、ATK、DEF, max = 3
   * level -> 角色等级
   * core -> 核心强化
   *
   * 前缀说明
   * basic_attribute 角色基础属性, hp, atk, def
   * equip_attribute 装备属性
   * settings_attribute 默认属性, 写死的
   */
  const level = computed(() => user_character.value?.level ?? default_level);
  const limit_break = computed(() => user_character.value?.limit_break ?? default_grade);
  const skill1_level = computed(() => user_character.value?.skill1_level ?? default_level);
  const skill2_level = computed(() => user_character.value?.skill2_level ?? default_level);
  const ulti_skill_level = computed(() => user_character.value?.skill_burst_level ?? default_level);
  const grade_info = computed(() => get_grade_core(limit_break.value));

  const initMockState = () => {
    const real_data = {
      real_limit_break: format_level(limit_break.value),
      real_level: format_level(level.value),
      real_grade: format_level(grade_info.value.grade),
      real_core: format_level(grade_info.value.core),
      real_skill1_level: format_level(skill1_level.value),
      real_skill2_level: format_level(skill2_level.value),
      real_ulti_skill_level: format_level(ulti_skill_level.value),
    };
    const mock_data = {
      limit_break: format_level(limit_break.value),
      core: format_level(grade_info.value.core),
      grade: format_level(grade_info.value.grade),
      level: format_level(level.value),
      ulti_skill_level: format_level(ulti_skill_level.value),
      skill1_level: format_level(skill1_level.value),
      skill2_level: format_level(skill2_level.value),
    };
    if (use_mock) {
      mockStore.init_state(Object.assign({}, real_data, mock_data));
    } else {
      mockStore.init_state(real_data);
    }
  };

  // 等级模拟数据初始化
  loadUserNikkes().then(() => {
    initMockState();
  });

  const get_level_val = (mock?: boolean) => (mock ? mockStore.level : level.value);
  const get_grade_val = (mock?: boolean) => (mock ? mockStore.grade : grade_info.value.grade);
  const get_core_val = (mock?: boolean) => (mock ? mockStore.core : grade_info.value.core);
  const skill1_val = (mock?: boolean) => (mock ? mockStore.skill1_level : skill1_level.value);
  const skill2_val = (mock?: boolean) => (mock ? mockStore.skill2_level : skill2_level.value);
  const ulti_skill_val = (mock?: boolean) =>
    mock ? mockStore.ulti_skill_level : ulti_skill_level.value;

  const level_basic_info = computed(() => {
    const level_index = get_level_val(true) - 1;
    return {
      level: get_level_val(true),
      level_hp: character_data.character_level_hp_list[level_index],
      level_attack: character_data.character_level_attack_list[level_index],
      level_defence: character_data.character_level_defence_list[level_index],
    };
  });
  const real_level_basic_info = computed(() => {
    const level_index = get_level_val() - 1;
    return {
      level: get_level_val(),
      level_hp: character_data.character_level_hp_list[level_index],
      level_attack: character_data.character_level_attack_list[level_index],
      level_defence: character_data.character_level_defence_list[level_index],
    };
  });

  const upgrade_material = computed<any>(() => {
    if (!level_cost_data.value) {
      return {
        gold: 0,
        character_exp: 0,
        character_exp_2: 0,
      };
    }
    const result = {
      gold: 0,
      character_exp: 0,
      character_exp_2: 0,
    };
    const start = Math.min(mockStore.level, level.value);
    const end = Math.max(mockStore.level, level.value);
    for (let i = start; i < end; i++) {
      const single_cost = level_cost_data.value.find((single_cost) => single_cost.level === i);
      if (single_cost) {
        result.gold += single_cost.gold;
        result.character_exp += single_cost.character_exp;
        result.character_exp_2 += single_cost.character_exp_2;
      }
    }
    if (mockStore.level < level.value) {
      Object.keys(result).forEach((key) => {
        // @ts-ignore
        result[key] = result[key] * -1;
      });
    }
    return result;
  });

  /**
   * 基础属性计算, hp, atk, def
   *
   *  // recycledata
   *  循环室等级*循环室加成（RecycleResearchStat  hp/def/atk)
   *
   *  + [角色基础属性 +循环室属性(RecycleResearchStat ） done
   *  + 好感度属性 （AttractiveLevel）] *[ 1+(core * CharacterStatEnhance core_hp/atk/dev*0.0001) ] done
   *  + 装备属性
   *  + 魔方属性（ItemHarmonyCubeLevel对应atk/def/hp）
   */

  /**
   * 各类数值计算
   */

  // 1+(core * CharacterStatEnhance core_hp/atk/dev*0.0001)
  const get_total_coefficient = (params: { mock?: boolean; type: "defence" | "attack" | "hp" }) => {
    const { mock, type } = params;
    return 1 + get_core_val(mock) * character_data.stat_enhance_detail[`core_${type}`] * 0.0001;
  };

  const get_recycle_buff_data = (type: "atk" | "def" | "hp") => {
    return class_buff.value[type] + enterprise_buff.value[type] + basic_recycle_buff.value[type];
  };

  const get_attractive_buff = (type: "atk" | "def" | "hp") => {
    return attractive_buff.value[type];
  };

  // 按妮姬级别(CharacterStat level)划分的角色属性（CharacterStat 表atk,hp,def）* (1+grade *CharacterStatEnhance grade_ratio * 0.0001）
  // + （grade * CharacterStatEnhance grade_hp/atk/def）
  const get_basic_attr = (params: {
    mock?: boolean;
    level: LevelInfo;
    type: "defence" | "attack" | "hp";
  }) => {
    const { mock, type, level } = params;
    const basic_attr_grade_effient =
      1 + get_grade_val(mock) * character_data.stat_enhance_detail.grade_ratio * 0.0001;
    return Number(
      level[`level_${type}`] * basic_attr_grade_effient +
        get_grade_val(mock) * character_data.stat_enhance_detail[`grade_${type}`],
    );
  };

  /**
   * 系数 end
   */

  /**
   * 属性计算:
   基础  - [角色基础属性 +循环室属性(RecycleResearchStat ） + 好感度属性 （AttractiveLevel）]
   系数  - [1+(core * CharacterStatEnhance core_hp/atk/dev*0.0001) + (服装加成CharacterCostumeCollection*0.0001)]
   buff - 装备属性 + 魔方属性

   (基础 * 系数) + buff

   * Noted: 整個過程中全部捨棄小數，(基础 * 系数) 的结果四捨五入
   */

  const { floor, round } = Math;
  const unDefZero = (v?: number) => v ?? 0;

  const get_level_data = (mock?: boolean) =>
    mock ? level_basic_info.value : real_level_basic_info.value;

  /**
   * ATK
   */
  const get_atk_attr = (mock?: boolean) => {
    const level = get_level_data(mock);
    if (!level || !character_data) {
      return 0;
    }
    const base_attr =
      floor(get_basic_attr({ mock, level, type: "attack" })) +
      floor(get_recycle_buff_data("atk")) +
      round(get_attractive_buff("atk"));

    return (
      round(base_attr * get_total_coefficient({ mock, type: "attack" })) +
      unDefZero(equip_attr.value?.atk) +
      unDefZero(cube_data.value?.atk) +
      unDefZero(favorite_data.value?.atk)
    );
  };
  const basic_attribute_atk = computed(() => get_atk_attr(true));
  const real_basic_attribute_atk = computed(() => get_atk_attr());

  /**
   * HP
   */
  const get_hp_attr = (mock?: boolean) => {
    const level = get_level_data(mock);
    if (!level || !character_data) {
      return 0;
    }

    const base_attr =
      floor(get_basic_attr({ mock, level, type: "hp" })) +
      floor(get_recycle_buff_data("hp")) +
      round(get_attractive_buff("hp"));

    return (
      round(base_attr * get_total_coefficient({ mock, type: "hp" })) +
      unDefZero(equip_attr.value?.hp) +
      unDefZero(cube_data.value?.hp) +
      unDefZero(favorite_data.value?.hp)
    );
  };
  const basic_attribute_hp = computed(() => get_hp_attr(true));
  const real_basic_attribute_hp = computed(() => get_hp_attr());

  /**
   * DEF
   */
  const get_def_attr = (mock?: boolean) => {
    const level = get_level_data(mock);
    if (!level || !character_data) {
      return 0;
    }

    const base_attr =
      floor(get_basic_attr({ mock, level, type: "defence" })) +
      floor(get_recycle_buff_data("def")) +
      round(get_attractive_buff("def"));
    return (
      round(base_attr * get_total_coefficient({ mock, type: "defence" })) +
      unDefZero(equip_attr.value?.def) +
      unDefZero(cube_data.value?.def) +
      unDefZero(favorite_data.value?.def)
    );
  };
  const basic_attribute_def = computed(() => get_def_attr(true));
  const real_basic_attribute_def = computed(() => get_def_attr());

  /**
   * 魔方属性
   * @see data-handler src/equip/cube.ts
   */
  const get_cube_coefficient = computed(() => {
    if (!cube_data.value) return 0;
    return Number((cube_data.value.power * 0.0001).toFixed(8));
  });

  /**
   * 珍藏属性
   * @see data-handler src/equip/favorite.ts
   */
  const get_favoriteitem_coefficient = computed(() => {
    if (!favorite_data.value) return 0;
    return Number((favorite_data.value.power * 0.0001).toFixed(8));
  });

  /**
   * 装备属性
   * 所有词条 (function_battlepower 相加) / 1000 / 10
   * 官方的: reduce( function_battlepower / 1000 )
   */
  const get_t10_equip_battle_power = computed(() => {
    // 装备求和
    const result = equips.value.reduce((pre, cur_equip) => {
      // 词条求和
      return (
        pre +
        cur_equip.buffs.reduce((pre2, buff) => {
          return (
            pre2 +
            buff.function_details.reduce((pre3, func) => {
              return pre3 + (func.function_battlepower < 0 ? 0 : func.function_battlepower) / 1000;
            }, 0)
          );
        }, 0)
      );
    }, 0);
    return Number((result * 0.1).toFixed(8));
  });

  /**
   * 总战斗力计算 fight attr
   * (攻击能力+生存能力)*
   * (（技能基础乘数(settings_attribute_base_skll)
   *  + (技能1 LV * 技能1乘数+技能2 LV *技能2乘数+爆发技能LV *爆发乘数+功能总功率 + 魔方val + t10val + favoriteitemval）
   * ）* 0.01
   */
  const get_character_fight_attribute = (mock?: boolean) => {
    if (!level_basic_info.value || !character_data) {
      return 0;
    }
    const critical_rate = character_data.critical_ratio * 0.0001;
    const critical_times = character_data.critical_damage * 0.0001 - 1;
    const hp = get_hp_attr(mock);
    const def = get_def_attr(mock);
    const atk = get_atk_attr(mock);

    // 生存能力 = （HP + DEF*防御倍数） * 生存倍数
    const survive_ability = floor(
      (Number(hp) + Number(def) * settings_attribute_def) * settings_attribute_hp,
    );

    //攻击能力0 = 角色攻击力 * （1+ 暴击率 * 暴击倍率） * 攻击倍数
    // 暴击率：Character    critical_ratio * 0.0001
    // 暴击倍率：Character    critical_damage * 0.0001 - 1
    const attack_ability = floor(
      Number(atk) * (1 + critical_rate * critical_times) * settings_attribute_attack_ratio,
    );

    const skill1_coefficient = skill1_val(mock) * settings_attribute_skill1;
    const skill2_coefficient = skill2_val(mock) * settings_attribute_skill2;
    const ulti_skill_coefficient = ulti_skill_val(mock) * settings_attribute_burst;

    return round(
      Number(
        (survive_ability + attack_ability) *
          (settings_attribute_base_skll +
            (skill1_coefficient + skill2_coefficient + ulti_skill_coefficient) +
            get_favoriteitem_coefficient.value + // 藏品
            get_cube_coefficient.value + // 魔方
            get_t10_equip_battle_power.value + // 优越装备
            // 功能总功率, 0
            0) *
          0.01,
      ),
    );
  };
  const character_fight_attribute = computed(() => get_character_fight_attribute(true));
  const real_character_fight_attribute = computed(() => get_character_fight_attribute());

  return {
    // 基础字段

    level,
    grade_info,
    skill1_level,
    skill2_level,
    ulti_skill_level,

    // 核心指标数据
    level_basic_info,
    basic_attribute_hp,
    basic_attribute_def,
    basic_attribute_atk,

    // 玩家/标准核心指标数据
    real_level_basic_info,
    real_basic_attribute_hp,
    real_basic_attribute_def,
    real_basic_attribute_atk,

    // 战斗力
    character_fight_attribute,
    real_character_fight_attribute,

    // 可操作的值, mock的
    change_limit_break,
    change_level: (val: number) =>
      mockStore.change({ key: "level", min: 1, max: max_level.value, val }),
    change_skill1: (val: number) =>
      mockStore.change({ key: "skill1_level", min: 0, max: max_level.value, val }),
    change_skill2: (val: number) =>
      mockStore.change({ key: "skill2_level", min: 0, max: max_level.value, val }),
    change_ulti_skill: (val: number) =>
      mockStore.change({ key: "ulti_skill_level", min: 0, max: max_level.value, val }),

    // 升级相关
    upgrade_material,

    // 定值
    max_level,
    max_stars,
    max_limit_break,

    // 加载装备等数据
    cost_pending_state,
    init_player_equip_data,
  };
}

export function useCharacterData(
  character: string,
  options?: {
    onError?: (err: any) => boolean;
  },
) {
  const { data: character_data, isLoading: is_character_data_loading } = useQuery({
    queryKey: [character],
    throwOnError: options?.onError ?? false,
    queryFn: async (): Promise<CharacterData> => {
      try {
        return await getCharacterData(character);
      } catch (err) {
        if (options?.onError) {
          options.onError(err);
        }
        throw err;
      }
    },
  });

  return {
    character_data,
    is_character_data_loading,
  };
}
