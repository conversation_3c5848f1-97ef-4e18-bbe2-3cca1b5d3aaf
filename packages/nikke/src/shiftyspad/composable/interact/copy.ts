import { useClipboard } from "@vueuse/core";
import { useToast } from "@/components/ui/toast";
import { t } from "@/locales";
import { computed } from "vue";

export function useCopy(options?: { onError?: () => void; onCopy?: () => void }) {
  const { show: toast } = useToast();
  const { onCopy, onError } = options ?? {};
  const { isSupported, copy: c } = useClipboard();
  const support_copy = computed(() => isSupported.value);

  const copy = (str: string) => {
    if (support_copy.value) {
      c(str);
      toast({
        text: t("copy_link_success"),
        type: "success",
      });
      onCopy?.();
    } else {
      toast({
        text: t("copy_failed"),
        type: "info",
      });
      onError?.();
    }
  };

  return {
    copy,
    support_copy,
  };
}
