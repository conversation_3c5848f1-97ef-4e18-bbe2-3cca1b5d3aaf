import { ref, onMounted, nextTick } from "vue";

export function useExpandBox() {
  const expandBoxRef = ref<HTMLElement>();
  const expandBtnRef = ref<HTMLElement>();

  const onItemTransitionEnter = (el: Element, done: () => void) => {
    const top = (el as HTMLElement).offsetTop;
    const left = (el as HTMLElement).offsetLeft;
    const width = (el as HTMLElement).offsetWidth;
    const height = (el as HTMLElement).offsetHeight;
    el.setAttribute("data-size", [top, left, width, height].join("|"));
    done();
  };
  const onItemTransitionLeave = (el: Element, done: () => void) => {
    const size: string | null = el.getAttribute("data-size");
    if (size == null) return;
    const styles = ["top", "left", "width", "height"];
    size.split("|").forEach((v, i: number) => {
      const _v = Number(v);
      if (!isNaN(_v)) {
        // @ts-ignore
        (el as HTMLElement).style[styles[i]] = _v + "px";
      }
      (el as HTMLElement).style.marginTop = "0px";
    });
    setTimeout(() => {
      done();
    }, 500);
  };

  // 高度无缝动画方法
  function transitionHeight(element: HTMLElement | undefined, time: number) {
    // time, 数值，可缺省
    if (!element) return;
    if (typeof window.getComputedStyle == "undefined") return;

    const height = window.getComputedStyle(element).height;

    element.style.transition = "none";

    element.style.height = "auto";
    const targetHeight = window.getComputedStyle(element).height;
    element.style.height = height;
    element.offsetWidth;
    if (time !== undefined) element.style.transition = "height " + time + "ms";
    element.style.height = targetHeight;
  }

  const initExpandBox = () => {
    if (!expandBoxRef.value) return;
    expandBtnRef.value?.addEventListener("click", () => {
      transitionHeight(expandBoxRef.value, 500);
    });
    transitionHeight(expandBoxRef.value, 0);
  };
  const updateExpandBox = () => {
    if (!expandBoxRef.value) return;
    nextTick(() => {
      transitionHeight(expandBoxRef.value, 0);
    });
  };

  onMounted(() => {
    nextTick(() => {
      initExpandBox();
    });
    window.addEventListener("resize", () => {
      transitionHeight(expandBoxRef.value, 0);
    });
  });
  return {
    expandBoxRef,
    expandBtnRef,
    initExpandBox,
    updateExpandBox,
    onItemTransitionEnter,
    onItemTransitionLeave,
  };
}
