import { onUnmounted, onMounted, ref } from "vue";
import { throttle } from "@/shiftyspad/utils/comm";

export function useCheckOnTop(el_query?: string) {
  const scrollPosition = ref(0);
  const is_top = ref(true);

  const el = el_query ? document.querySelector(el_query) : window;

  const handleScroll = throttle(() => {
    scrollPosition.value = el_query ? ((el as HTMLElement)?.scrollTop ?? 0) : window.scrollY;
    is_top.value = scrollPosition.value <= 10;
  }, 10);

  onMounted(() => {
    el?.addEventListener("scroll", handleScroll);
  });

  onUnmounted(() => {
    el?.removeEventListener("scroll", handleScroll);
  });

  return {
    is_top,
  };
}
