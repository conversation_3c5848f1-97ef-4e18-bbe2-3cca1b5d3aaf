import { ref, onMounted, onUnmounted } from "vue";

export function useProgresBar() {
  /**
   * 组件中动态计算
   */
  const aborter = new AbortController();
  const change_cb: Function[] = [];
  const progressBar = ref();
  const barclick = (event: Event) => {
    // @ts-ignore
    const clickX = event.clientX;
    const progressBarRect = progressBar.value.getBoundingClientRect();
    const progressBarWidth = progressBarRect.width;
    const clickPositionInBar = clickX - progressBarRect.left;

    const percentage = clickPositionInBar / progressBarWidth;

    change_cb.forEach((cb) => {
      cb(percentage);
    });
  };

  const onchange = (cb: Function) => {
    change_cb.push(cb);
  };

  const up = () => {
    is_dragging.value = false;
    document.removeEventListener("mousemove", move);
    document.removeEventListener("touchmove", move);
  };

  const down = (event: Event) => {
    is_dragging.value = true;
    let startX;
    const touches = (event as TouchEvent).touches;
    if (touches) {
      event.preventDefault();
      startX = touches[0].clientX;
    } else {
      startX = (event as MouseEvent).clientX;
    }
    const progressBarRect = progressBar.value.getBoundingClientRect();
    start_position.value = startX - progressBarRect.left;
    document.addEventListener("mousemove", move);
    document.addEventListener("touchmove", move);
  };

  const move = (event: Event) => {
    if (!is_dragging.value) return;
    const progressBarRect = progressBar.value.getBoundingClientRect();
    let clickX;
    const touches = (event as TouchEvent).touches;
    if (touches) {
      event.preventDefault();
      clickX = touches[0].clientX;
    } else {
      clickX = (event as MouseEvent).clientX;
    }
    const clickPositionInBar = clickX - progressBarRect.left;

    const percentage = clickPositionInBar / progressBarRect.width;

    change_cb.forEach((cb) => {
      cb(percentage);
    });
  };

  const start_position = ref(0);
  const is_dragging = ref(false);
  const progressDot = ref<HTMLElement>();
  onMounted(() => {
    document.addEventListener("click", up, { signal: aborter.signal });
    document.addEventListener("mouseleave", up, { signal: aborter.signal });
    document.addEventListener("mouseup", up, { signal: aborter.signal });
    document.addEventListener("touchcancel", up, { signal: aborter.signal });
    document.addEventListener("touchend", up, { signal: aborter.signal });

    progressDot.value?.addEventListener("mousedown", down, { signal: aborter.signal });
    progressDot.value?.addEventListener("touchstart", down, { signal: aborter.signal });
    progressBar.value?.addEventListener("click", barclick, { signal: aborter.signal });
  });

  onUnmounted(() => {
    aborter.abort();
  });

  return {
    progressBar,
    progressDot,

    onchange,
  };
}
