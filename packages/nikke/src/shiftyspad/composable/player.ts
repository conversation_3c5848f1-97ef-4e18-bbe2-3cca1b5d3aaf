import {
  CharacterData,
  EquipData,
  EquipOptions,
  EquipFunctions,
} from "@/shiftyspad/types/character";
import {
  getEquipmentOptions,
  getEquipData,
  getCubeByCubeId,
  getFavoriteItemId,
} from "@/shiftyspad/service/character";
import { ref, computed, Ref } from "vue";
import { CubeItem, FavoriteItem, Nikke, NikkeEquip } from "packages/types/shiftyspad";
import { aegis } from "@/shiftyspad/service/rum";
import { ShiftyUserInstance } from "@/shiftyspad/composable/game-data";
import { getEquipAttr } from "./equip";
import { promiseWrap } from "@/shiftyspad/utils/comm";
import { logger } from "@/shiftyspad/const";

export type PlayerEquipData = NikkeEquip &
  EquipData & {
    buffs: EquipFunctions["equip_effects"];
  };

type SkillUnlockStatus = {
  skill_level: number;
  unlock_level: number;
  is_unlock: boolean;
  exist?: boolean;
};

/**
 * @description 获取某个用户某个角色的数据
 * 装备、魔方、珍藏品
 */
export function usePlayerRoleData(params: {
  character: CharacterData;
  shiftys_user: Ref<ShiftyUserInstance>;
}) {
  const { shiftys_user, character } = params;
  const { user_nikkelist_info, getPlayerEquipContent } = shiftys_user.value;

  const user_character = computed<Nikke | undefined>(() => {
    return (user_nikkelist_info.value ?? []).find((v: any) => v.name_code === character.name_code);
  });
  const wrap = (v?: number) => v ?? 0;
  const full_character_data = Object.assign({}, user_character, character);

  const loadUserNikkes = async () => {
    await shiftys_user.value.initUserNikkeInfo();
  };

  const init_player_equip_data = promiseWrap(async () => {
    await loadUserNikkes();
    if (!user_character.value) {
      return;
      // throw new Error("supposed to be called after loading user data!");
    }
    return Promise.all([updateEquipData(), updateCubeData(), updateFavoriteItem()]);
  });
  /**
   * cube
   * 注意: 魔方相关等级要 - 1
   * TODO: use game-data types
   */
  const cube_item = ref<CubeItem | null>(null);
  const cube_data = computed<{
    atk: number;
    def: number;
    hp: number;
    level1: number;
    level2: number;
    power: number;
  } | null>(() => {
    if (!cube_item.value) return null;
    const target = cube_item.value;
    const level = user_character.value!.cube_level - 1;
    logger.info(`cube_level: ${level}`);
    if (level < 0) return null;
    return {
      atk: wrap(target.atk.at(level)),
      def: wrap(target.def.at(level)),
      hp: wrap(target.hp.at(level)),
      power: wrap(target.powers.at(level)),
      level1: wrap(target.level1.at(level)),
      level2: wrap(target.level2.at(level)),
      level3: wrap(target.level3.at(level)),
    };
  });
  const updateCubeData = async () => {
    if (!user_character.value?.cube_id || user_character.value?.cube_id < 0) return;
    cube_item.value = await getCubeByCubeId(user_character.value?.cube_id);
  };
  const cube_skills = computed<
    (null | (CubeItem["harmonycube_skill_group"][number] & SkillUnlockStatus))[]
  >(() => {
    const current_cube_level = (user_character.value?.cube_level ?? 1) - 1;
    if (!current_cube_level || current_cube_level < 0) {
      return new Array(3).fill(null);
    }
    return (cube_item.value?.harmonycube_skill_group ?? []).map((skill, index) => {
      // @ts-ignore
      const skill_level = cube_item.value[`level${index + 1}`]?.[current_cube_level] ?? 0;
      // @ts-ignore
      const unlock_level = cube_item.value[`level${index + 1}`]?.findIndex((v) => v > 0);
      return Object.assign({}, skill, {
        skill_level,
        unlock_level,
        exist: Boolean(skill),
        is_unlock: skill_level > 0,
      });
    });
  });

  /**
   * favoriteitem
   */
  const favorite_item = ref<FavoriteItem | null>(null);
  const favorite_data = computed<{
    atk: number;
    def: number;
    hp: number;
    level1: number;
    level2: number;
    power: number;
  } | null>(() => {
    const target = favorite_item.value;
    if (!target) return null;
    const level = user_character.value!.item_level;
    logger.info(`favorite_level: ${level}`);
    return {
      atk: wrap(target.atk.at(level)),
      def: wrap(target.def.at(level)),
      hp: wrap(target.hp.at(level)),
      power: wrap(target.powers.at(level)),
      level1: wrap(target.level1.at(level)),
      level2: wrap(target.level2.at(level)),
    };
  });
  // 佩戴给专属角色的藏品
  const is_player_equip_special_collection = computed(
    () => favorite_item.value?.name_code === user_character.value?.name_code,
  );

  const collection_has_player_skill = computed(() => {
    return (
      user_character.value &&
      (user_character.value.item_level ?? 0) > 0 &&
      (user_character.value.item_id ?? 0) > 0 &&
      (favorite_item.value?.favoriteitem_skill_group_data?.length ?? 0) > 0 &&
      is_player_equip_special_collection.value
    );
  });

  // 人物专属装备技能, null 表示无技能
  const favorite_player_skills = computed<
    (
      | null
      | (FavoriteItem["favoriteitem_skill_group_data"][number] & {
          skill_level: number;
          is_unlock: boolean;
        })
    )[]
  >(() => {
    const item_level = user_character.value?.item_level ?? 0;
    if (!collection_has_player_skill.value) {
      return new Array(3).fill(null);
    }
    const grade = favorite_item.value?.grade[item_level] ?? 0;
    return (favorite_item.value?.favoriteitem_skill_group_data ?? [])
      .slice()
      .sort((pre, next) => pre.skill_change_slot - next.skill_change_slot)
      .map((skill, index) => {
        const { skill1_level, skill2_level, skill_burst_level } = user_character.value!;
        const skill_level = (() => {
          if (skill.skill_change_slot === 1) return skill1_level;
          if (skill.skill_change_slot === 2) return skill2_level;
          if (skill.skill_change_slot === 3) return skill_burst_level;
        })();
        return Object.assign({}, skill, {
          skill_level,
          is_unlock: grade >= index,
        });
      });
  });

  // 装备技能
  const favorite_item_skills = computed(() => {
    const item_level = user_character.value?.item_level ?? 0;
    if (!favorite_item.value || item_level < 0 || (user_character.value?.item_id ?? 0) < 0) {
      return [];
    }
    return favorite_item.value.collection_skill_group_data?.map((skill, index) => {
      // @ts-ignore
      const skill_level = favorite_item.value[`level${index + 1}`]?.[item_level] ?? 0;
      // @ts-ignore
      const unlock_level = favorite_item.value[`level${index + 1}`]?.findIndex((v) => v > 0);
      return Object.assign({}, skill, {
        skill_level,
        unlock_level,
        is_unlock: skill_level > 0,
      });
    });
  });
  const updateFavoriteItem = async () => {
    const item_id = user_character.value?.item_id;
    if (!item_id || item_id < 0) return;
    favorite_item.value = await getFavoriteItemId(item_id);
  };
  /**
   * equipment
   */
  // TODO: refactor with computed
  const equip_option = ref<EquipOptions[]>([]);
  const equip_data = ref<EquipData[]>([]);
  const equip_info = ref<EquipFunctions[]>([]);

  const equip_attr = computed(() => {
    const get_equip_attr = (type: "Atk" | "Hp" | "Defence") => {
      const result = equips.value.reduce((sum, equip) => {
        return (
          sum +
          getEquipAttr({
            equip,
            character,
            type: type,
          })
        );
      }, 0);
      return result;
    };
    return {
      atk: get_equip_attr("Atk"),
      def: get_equip_attr("Defence"),
      hp: get_equip_attr("Hp"),
    };
  });

  const updateEquipData = promiseWrap(() => {
    return Promise.all([
      getEquipData(),
      getEquipmentOptions(),
      getPlayerEquipContent({
        resource_id: user_character.value!.resource_id,
      }),
    ])
      .then(([equipData, equipOption, equipContent]) => {
        equip_data.value = equipData || [];
        equip_option.value = equipOption || [];
        equip_info.value = (equipContent?.equip_contents ?? []) as any;
        // computed to ref
        update_equips();
      })
      .catch((error) => {
        aegis.error("[init-data-failed]:", error);
      });
  });

  const equips = ref<PlayerEquipData[]>([]);
  const update_equips = () => {
    const equip_result = user_character.value?.player_equips.map(
      (equip: NikkeEquip, index: number) => {
        const result = (() => {
          const info = equip_info.value[index];
          if (info) {
            const { equip_effects } = info;
            return Object.assign({}, equip, {
              buffs: equip_effects.map((v) => {
                const { equipment_option_id } = v;
                const option_detail = equip_option.value.find(
                  (opt) => opt.state_effect_group_id === Number(equipment_option_id),
                );
                return Object.assign({}, option_detail, v);
              }),
            });
          } else {
            return Object.assign({}, equip, {
              buffs: [],
            });
          }
        })();
        const temp = equip_data.value.find((item) => item.id === equip.equip_id);
        return Object.assign({}, result, JSON.parse(JSON.stringify(temp ?? {})) as EquipData);
      },
    );
    equips.value = equip_result ?? [];
  };

  return {
    full_character_data,
    user_character,
    equip_info,
    equips,
    equip_attr,
    equip_data,
    loadUserNikkes,
    init_player_equip_data,

    // favorite
    favorite_data,
    favorite_item,
    favorite_item_skills,
    favorite_player_skills,
    collection_has_player_skill,

    // cube
    cube_data,
    cube_item,
    cube_skills,
  };
}
