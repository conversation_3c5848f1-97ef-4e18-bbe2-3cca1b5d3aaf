import { useJsonStore } from "@/shiftyspad/stores/json";
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { BG_URL } from "@/shiftyspad/const/urls";

export function useArchive() {
  const json = useJsonStore();
  const { scene_archive_list } = storeToRefs(json);
  const showArchiveList = computed(() => {
    const list = scene_archive_list.value.map((archive, index) => {
      return {
        id: archive.id,
        img: BG_URL({ path: "archive_bg", name: archive.record_slot_bg_addressable }),
        bg: BG_URL({ path: "archive_bg", name: archive.record_unlock_bg_addressable }),
        align: index % 2 ? "right" : "left",
        record_main_archive_event_id: archive.record_main_archive_event_id,
      };
    });
    return list;
  });
  return {
    showArchiveList,
  };
}
