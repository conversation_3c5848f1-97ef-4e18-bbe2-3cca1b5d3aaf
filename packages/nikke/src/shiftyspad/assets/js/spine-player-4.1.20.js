"use strict";var spine=(()=>{var gt=Object.defineProperty,Ns=Object.getOwnPropertyDescriptor,Us=Object.getOwnPropertyNames,zs=Object.prototype.hasOwnProperty,Ws=(e,t,i)=>t in e?gt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,_s=(e,t)=>{for(var i in t)gt(e,i,{get:t[i],enumerable:!0})},qs=(e,t,i,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Us(t))!zs.call(e,n)&&n!==i&&gt(e,n,{get:()=>t[n],enumerable:!(s=Ns(t,n))||s.enumerable});return e},Hs=e=>qs(gt({},"__esModule",{value:!0}),e),P=(e,t,i)=>(Ws(e,typeof t!="symbol"?t+"":t,i),i),qi={};_s(qi,{AlphaTimeline:()=>si,Animation:()=>xt,AnimationState:()=>ui,AnimationStateAdapter:()=>Ks,AnimationStateData:()=>ns,AssetManager:()=>Xi,AssetManagerBase:()=>cs,AtlasAttachmentLoader:()=>bi,Attachment:()=>_t,AttachmentTimeline:()=>He,BinaryInput:()=>ws,BlendMode:()=>Yt,Bone:()=>yi,BoneData:()=>vi,BoundingBoxAttachment:()=>Ct,CURRENT:()=>rs,CameraController:()=>Sr,ClippingAttachment:()=>dt,Color:()=>D,Color2Attribute:()=>As,ColorAttribute:()=>Bt,ConstraintData:()=>kt,CurveTimeline:()=>Ee,CurveTimeline1:()=>Ie,CurveTimeline2:()=>bt,DebugUtils:()=>Zs,DeformTimeline:()=>ai,Downloader:()=>Ai,DrawOrderTimeline:()=>Ge,Event:()=>Ci,EventData:()=>Si,EventQueue:()=>is,EventTimeline:()=>ht,EventType:()=>ve,FIRST:()=>fi,FakeTexture:()=>Qs,GLTexture:()=>ct,HOLD_FIRST:()=>At,HOLD_MIX:()=>ss,HOLD_SUBSEQUENT:()=>mi,IkConstraint:()=>us,IkConstraintData:()=>Ti,IkConstraintTimeline:()=>li,Input:()=>ut,IntSet:()=>Gs,Interpolation:()=>Hi,LoadingScreen:()=>Rs,M00:()=>V,M01:()=>q,M02:()=>H,M03:()=>O,M10:()=>G,M11:()=>N,M12:()=>j,M13:()=>U,M20:()=>Z,M21:()=>J,M22:()=>z,M23:()=>W,M30:()=>Q,M31:()=>$,M32:()=>ee,M33:()=>K,ManagedWebGLRenderingContext:()=>ge,MathUtils:()=>F,Matrix4:()=>De,Mesh:()=>Bi,MeshAttachment:()=>Ue,MixBlend:()=>Ki,MixDirection:()=>Qi,OrthoCamera:()=>vs,PathAttachment:()=>je,PathConstraint:()=>et,PathConstraintData:()=>ki,PathConstraintMixTimeline:()=>ci,PathConstraintPositionTimeline:()=>hi,PathConstraintSpacingTimeline:()=>di,PointAttachment:()=>xi,PolygonBatcher:()=>it,Pool:()=>lt,Position2Attribute:()=>Xt,Position3Attribute:()=>Tr,PositionMode:()=>Et,Pow:()=>Gi,PowOut:()=>js,RGB2Timeline:()=>ni,RGBA2Timeline:()=>ri,RGBATimeline:()=>ti,RGBTimeline:()=>ii,RegionAttachment:()=>ae,ResizeMode:()=>Ot,RotateMode:()=>Mt,RotateTimeline:()=>ot,SETUP:()=>gi,SUBSEQUENT:()=>yt,ScaleTimeline:()=>Zt,ScaleXTimeline:()=>Jt,ScaleYTimeline:()=>Kt,SceneRenderer:()=>Ni,SequenceTimeline:()=>$e,Shader:()=>pe,ShapeRenderer:()=>Di,ShapeType:()=>be,ShearTimeline:()=>Qt,ShearXTimeline:()=>$t,ShearYTimeline:()=>ei,Skeleton:()=>Ei,SkeletonBinary:()=>ps,SkeletonBounds:()=>xs,SkeletonClipping:()=>Ft,SkeletonData:()=>Ii,SkeletonDebugRenderer:()=>Pt,SkeletonJson:()=>bs,SkeletonRenderer:()=>Oi,Skin:()=>Rt,SkinEntry:()=>Mi,Slot:()=>fs,SlotData:()=>Ri,SpacingMode:()=>It,SpineCanvas:()=>Yr,SpinePlayer:()=>Fr,SpinePlayerEditor:()=>Xs,StringSet:()=>Wt,TexCoordAttribute:()=>Pi,Texture:()=>pi,TextureAtlas:()=>os,TextureAtlasPage:()=>hs,TextureAtlasRegion:()=>wi,TextureFilter:()=>St,TextureRegion:()=>ls,TextureWrap:()=>as,TimeKeeper:()=>pt,Timeline:()=>me,Touch:()=>ys,TrackEntry:()=>ts,TransformConstraint:()=>ms,TransformConstraintData:()=>Yi,TransformConstraintTimeline:()=>oi,TransformMode:()=>Tt,TranslateTimeline:()=>Ht,TranslateXTimeline:()=>Gt,TranslateYTimeline:()=>jt,Triangulator:()=>ye,Utils:()=>B,Vector2:()=>Te,Vector3:()=>Ae,VertexAttachment:()=>ke,VertexAttribute:()=>tt,VertexAttributeType:()=>Ze,WindowedMean:()=>Js});var Gs=class{array=new Array;add(e){let t=this.contains(e);return this.array[e|0]=e|0,!t}contains(e){return this.array[e|0]!=null}remove(e){this.array[e|0]=void 0}clear(){this.array.length=0}},Wt=class{entries={};size=0;add(e){let t=this.entries[e];return this.entries[e]=!0,t?!1:(this.size++,!0)}addAll(e){let t=this.size;for(var i=0,s=e.length;i<s;i++)this.add(e[i]);return t!=this.size}contains(e){return this.entries[e]}clear(){this.entries={},this.size=0}},qe=class{constructor(e=0,t=0,i=0,s=0){this.r=e,this.g=t,this.b=i,this.a=s}set(e,t,i,s){return this.r=e,this.g=t,this.b=i,this.a=s,this.clamp()}setFromColor(e){return this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this}setFromString(e){return e=e.charAt(0)=="#"?e.substr(1):e,this.r=parseInt(e.substr(0,2),16)/255,this.g=parseInt(e.substr(2,2),16)/255,this.b=parseInt(e.substr(4,2),16)/255,this.a=e.length!=8?1:parseInt(e.substr(6,2),16)/255,this}add(e,t,i,s){return this.r+=e,this.g+=t,this.b+=i,this.a+=s,this.clamp()}clamp(){return this.r<0?this.r=0:this.r>1&&(this.r=1),this.g<0?this.g=0:this.g>1&&(this.g=1),this.b<0?this.b=0:this.b>1&&(this.b=1),this.a<0?this.a=0:this.a>1&&(this.a=1),this}static rgba8888ToColor(e,t){e.r=((t&4278190080)>>>24)/255,e.g=((t&16711680)>>>16)/255,e.b=((t&65280)>>>8)/255,e.a=(t&255)/255}static rgb888ToColor(e,t){e.r=((t&16711680)>>>16)/255,e.g=((t&65280)>>>8)/255,e.b=(t&255)/255}static fromString(e){return new qe().setFromString(e)}},D=qe;P(D,"WHITE",new qe(1,1,1,1)),P(D,"RED",new qe(1,0,0,1)),P(D,"GREEN",new qe(0,1,0,1)),P(D,"BLUE",new qe(0,0,1,1)),P(D,"MAGENTA",new qe(1,0,1,1));var Xe=class{static clamp(e,t,i){return e<t?t:e>i?i:e}static cosDeg(e){return Math.cos(e*Xe.degRad)}static sinDeg(e){return Math.sin(e*Xe.degRad)}static signum(e){return e>0?1:e<0?-1:0}static toInt(e){return e>0?Math.floor(e):Math.ceil(e)}static cbrt(e){let t=Math.pow(Math.abs(e),.3333333333333333);return e<0?-t:t}static randomTriangular(e,t){return Xe.randomTriangularWith(e,t,(e+t)*.5)}static randomTriangularWith(e,t,i){let s=Math.random(),n=t-e;return s<=(i-e)/n?e+Math.sqrt(s*n*(i-e)):t-Math.sqrt((1-s)*n*(t-i))}static isPowerOfTwo(e){return e&&(e&e-1)===0}},F=Xe;P(F,"PI",3.1415927),P(F,"PI2",Xe.PI*2),P(F,"radiansToDegrees",180/Xe.PI),P(F,"radDeg",Xe.radiansToDegrees),P(F,"degreesToRadians",Xe.PI/180),P(F,"degRad",Xe.degreesToRadians);var Hi=class{apply(e,t,i){return e+(t-e)*this.applyInternal(i)}},Gi=class extends Hi{power=2;constructor(e){super(),this.power=e}applyInternal(e){return e<=.5?Math.pow(e*2,this.power)/2:Math.pow((e-1)*2,this.power)/(this.power%2==0?-2:2)+1}},js=class extends Gi{constructor(e){super(e)}applyInternal(e){return Math.pow(e-1,this.power)*(this.power%2==0?-1:1)+1}},Qe=class{static arrayCopy(e,t,i,s,n){for(let d=t,l=s;d<t+n;d++,l++)i[l]=e[d]}static arrayFill(e,t,i,s){for(let n=t;n<i;n++)e[n]=s}static setArraySize(e,t,i=0){let s=e.length;if(s==t)return e;if(e.length=t,s<t)for(let n=s;n<t;n++)e[n]=i;return e}static ensureArrayCapacity(e,t,i=0){return e.length>=t?e:Qe.setArraySize(e,t,i)}static newArray(e,t){let i=new Array(e);for(let s=0;s<e;s++)i[s]=t;return i}static newFloatArray(e){if(Qe.SUPPORTS_TYPED_ARRAYS)return new Float32Array(e);{let t=new Array(e);for(let i=0;i<t.length;i++)t[i]=0;return t}}static newShortArray(e){if(Qe.SUPPORTS_TYPED_ARRAYS)return new Int16Array(e);{let t=new Array(e);for(let i=0;i<t.length;i++)t[i]=0;return t}}static toFloatArray(e){return Qe.SUPPORTS_TYPED_ARRAYS?new Float32Array(e):e}static toSinglePrecision(e){return Qe.SUPPORTS_TYPED_ARRAYS?Math.fround(e):e}static webkit602BugfixHelper(e,t){}static contains(e,t,i=!0){for(var s=0;s<e.length;s++)if(e[s]==t)return!0;return!1}static enumValue(e,t){return e[t[0].toUpperCase()+t.slice(1)]}},B=Qe;P(B,"SUPPORTS_TYPED_ARRAYS",typeof Float32Array<"u");var Zs=class{static logBones(e){for(let t=0;t<e.bones.length;t++){let i=e.bones[t];console.log(i.data.name+", "+i.a+", "+i.b+", "+i.c+", "+i.d+", "+i.worldX+", "+i.worldY)}}},lt=class{items=new Array;instantiator;constructor(e){this.instantiator=e}obtain(){return this.items.length>0?this.items.pop():this.instantiator()}free(e){e.reset&&e.reset(),this.items.push(e)}freeAll(e){for(let t=0;t<e.length;t++)this.free(e[t])}clear(){this.items.length=0}},Te=class{constructor(e=0,t=0){this.x=e,this.y=t}set(e,t){return this.x=e,this.y=t,this}length(){let e=this.x,t=this.y;return Math.sqrt(e*e+t*t)}normalize(){let e=this.length();return e!=0&&(this.x/=e,this.y/=e),this}},pt=class{maxDelta=.064;framesPerSecond=0;delta=0;totalTime=0;lastTime=Date.now()/1e3;frameCount=0;frameTime=0;update(){let e=Date.now()/1e3;this.delta=e-this.lastTime,this.frameTime+=this.delta,this.totalTime+=this.delta,this.delta>this.maxDelta&&(this.delta=this.maxDelta),this.lastTime=e,this.frameCount++,this.frameTime>1&&(this.framesPerSecond=this.frameCount/this.frameTime,this.frameTime=0,this.frameCount=0)}},Js=class{values;addedValues=0;lastValue=0;mean=0;dirty=!0;constructor(e=32){this.values=new Array(e)}hasEnoughData(){return this.addedValues>=this.values.length}addValue(e){this.addedValues<this.values.length&&this.addedValues++,this.values[this.lastValue++]=e,this.lastValue>this.values.length-1&&(this.lastValue=0),this.dirty=!0}getMean(){if(this.hasEnoughData()){if(this.dirty){let e=0;for(let t=0;t<this.values.length;t++)e+=this.values[t];this.mean=e/this.values.length,this.dirty=!1}return this.mean}return 0}},_t=class{name;constructor(e){if(!e)throw new Error("name cannot be null.");this.name=e}},ji=class extends _t{id=ji.nextID++;bones=null;vertices=[];worldVerticesLength=0;timelineAttachment=this;constructor(e){super(e)}computeWorldVertices(e,t,i,s,n,d){i=n+(i>>1)*d;let l=e.bone.skeleton,r=e.deform,h=this.vertices,a=this.bones;if(!a){r.length>0&&(h=r);let f=e.bone,m=f.worldX,g=f.worldY,x=f.a,v=f.b,p=f.c,w=f.d;for(let b=t,y=n;y<i;b+=2,y+=d){let C=h[b],A=h[b+1];s[y]=C*x+A*v+m,s[y+1]=C*p+A*w+g}return}let o=0,c=0;for(let f=0;f<t;f+=2){let m=a[o];o+=m+1,c+=m}let u=l.bones;if(r.length==0)for(let f=n,m=c*3;f<i;f+=d){let g=0,x=0,v=a[o++];for(v+=o;o<v;o++,m+=3){let p=u[a[o]],w=h[m],b=h[m+1],y=h[m+2];g+=(w*p.a+b*p.b+p.worldX)*y,x+=(w*p.c+b*p.d+p.worldY)*y}s[f]=g,s[f+1]=x}else{let f=r;for(let m=n,g=c*3,x=c<<1;m<i;m+=d){let v=0,p=0,w=a[o++];for(w+=o;o<w;o++,g+=3,x+=2){let b=u[a[o]],y=h[g]+f[x],C=h[g+1]+f[x+1],A=h[g+2];v+=(y*b.a+C*b.b+b.worldX)*A,p+=(y*b.c+C*b.d+b.worldY)*A}s[m]=v,s[m+1]=p}}}copyTo(e){this.bones?(e.bones=new Array(this.bones.length),B.arrayCopy(this.bones,0,e.bones,0,this.bones.length)):e.bones=null,this.vertices&&(e.vertices=B.newFloatArray(this.vertices.length),B.arrayCopy(this.vertices,0,e.vertices,0,this.vertices.length)),e.worldVerticesLength=this.worldVerticesLength,e.timelineAttachment=this.timelineAttachment}},ke=ji;P(ke,"nextID",0);var wt=class{id=wt.nextID();regions;start=0;digits=0;setupIndex=0;constructor(e){this.regions=new Array(e)}copy(){let e=new wt(this.regions.length);return B.arrayCopy(this.regions,0,e.regions,0,this.regions.length),e.start=this.start,e.digits=this.digits,e.setupIndex=this.setupIndex,e}apply(e,t){let i=e.sequenceIndex;i==-1&&(i=this.setupIndex),i>=this.regions.length&&(i=this.regions.length-1);let s=this.regions[i];t.region!=s&&(t.region=s,t.updateRegion())}getPath(e,t){let i=e,s=(this.start+t).toString();for(let n=this.digits-s.length;n>0;n--)i+="0";return i+=s,i}static nextID(){return wt._nextID++}},qt=wt;P(qt,"_nextID",0);var Zi=(e=>(e[e.hold=0]="hold",e[e.once=1]="once",e[e.loop=2]="loop",e[e.pingpong=3]="pingpong",e[e.onceReverse=4]="onceReverse",e[e.loopReverse=5]="loopReverse",e[e.pingpongReverse=6]="pingpongReverse",e))(Zi||{}),Ji=[0,1,2,3,4,5,6],xt=class{name;timelines=[];timelineIds=new Wt;duration;constructor(e,t,i){if(!e)throw new Error("name cannot be null.");this.name=e,this.setTimelines(t),this.duration=i}setTimelines(e){if(!e)throw new Error("timelines cannot be null.");this.timelines=e,this.timelineIds.clear();for(var t=0;t<e.length;t++)this.timelineIds.addAll(e[t].getPropertyIds())}hasTimeline(e){for(let t=0;t<e.length;t++)if(this.timelineIds.contains(e[t]))return!0;return!1}apply(e,t,i,s,n,d,l,r){if(!e)throw new Error("skeleton cannot be null.");s&&this.duration!=0&&(i%=this.duration,t>0&&(t%=this.duration));let h=this.timelines;for(let a=0,o=h.length;a<o;a++)h[a].apply(e,t,i,n,d,l,r)}},Ki=(e=>(e[e.setup=0]="setup",e[e.first=1]="first",e[e.replace=2]="replace",e[e.add=3]="add",e))(Ki||{}),Qi=(e=>(e[e.mixIn=0]="mixIn",e[e.mixOut=1]="mixOut",e))(Qi||{}),ce={rotate:0,x:1,y:2,scaleX:3,scaleY:4,shearX:5,shearY:6,rgb:7,alpha:8,rgb2:9,attachment:10,deform:11,event:12,drawOrder:13,ikConstraint:14,transformConstraint:15,pathConstraintPosition:16,pathConstraintSpacing:17,pathConstraintMix:18,sequence:19},me=class{propertyIds;frames;constructor(e,t){this.propertyIds=t,this.frames=B.newFloatArray(e*this.getFrameEntries())}getPropertyIds(){return this.propertyIds}getFrameEntries(){return 1}getFrameCount(){return this.frames.length/this.getFrameEntries()}getDuration(){return this.frames[this.frames.length-this.getFrameEntries()]}static search1(e,t){let i=e.length;for(let s=1;s<i;s++)if(e[s]>t)return s-1;return i-1}static search(e,t,i){let s=e.length;for(let n=i;n<s;n+=i)if(e[n]>t)return n-i;return s-i}},Ee=class extends me{curves;constructor(e,t,i){super(e,i),this.curves=B.newFloatArray(e+t*18),this.curves[e-1]=1}setLinear(e){this.curves[e]=0}setStepped(e){this.curves[e]=1}shrink(e){let t=this.getFrameCount()+e*18;if(this.curves.length>t){let i=B.newFloatArray(t);B.arrayCopy(this.curves,0,i,0,t),this.curves=i}}setBezier(e,t,i,s,n,d,l,r,h,a,o){let c=this.curves,u=this.getFrameCount()+e*18;i==0&&(c[t]=2+u);let f=(s-d*2+r)*.03,m=(n-l*2+h)*.03,g=((d-r)*3-s+a)*.006,x=((l-h)*3-n+o)*.006,v=f*2+g,p=m*2+x,w=(d-s)*.3+f+g*.16666667,b=(l-n)*.3+m+x*.16666667,y=s+w,C=n+b;for(let A=u+18;u<A;u+=2)c[u]=y,c[u+1]=C,w+=v,b+=p,v+=g,p+=x,y+=w,C+=b}getBezierValue(e,t,i,s){let n=this.curves;if(n[s]>e){let h=this.frames[t],a=this.frames[t+i];return a+(e-h)/(n[s]-h)*(n[s+1]-a)}let d=s+18;for(s+=2;s<d;s+=2)if(n[s]>=e){let h=n[s-2],a=n[s-1];return a+(e-h)/(n[s]-h)*(n[s+1]-a)}t+=this.getFrameEntries();let l=n[d-2],r=n[d-1];return r+(e-l)/(this.frames[t]-l)*(this.frames[t+i]-r)}},Ie=class extends Ee{constructor(e,t,i){super(e,t,[i])}getFrameEntries(){return 2}setFrame(e,t,i){e<<=1,this.frames[e]=t,this.frames[e+1]=i}getCurveValue(e){let t=this.frames,i=t.length-2;for(let n=2;n<=i;n+=2)if(t[n]>e){i=n-2;break}let s=this.curves[i>>1];switch(s){case 0:let n=t[i],d=t[i+1];return d+(e-n)/(t[i+2]-n)*(t[i+2+1]-d);case 1:return t[i+1]}return this.getBezierValue(e,i,1,s-2)}},bt=class extends Ee{constructor(e,t,i,s){super(e,t,[i,s])}getFrameEntries(){return 3}setFrame(e,t,i,s){e*=3,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s}},ot=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.rotate+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.rotation=r.data.rotation;return;case 1:r.rotation+=(r.data.rotation-r.rotation)*n}return}let a=this.getCurveValue(i);switch(d){case 0:r.rotation=r.data.rotation+a*n;break;case 1:case 2:a+=r.data.rotation-r.rotation;case 3:r.rotation+=a*n}}},Ht=class extends bt{boneIndex=0;constructor(e,t,i){super(e,t,ce.x+"|"+i,ce.y+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.x=r.data.x,r.y=r.data.y;return;case 1:r.x+=(r.data.x-r.x)*n,r.y+=(r.data.y-r.y)*n}return}let a=0,o=0,c=me.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let f=h[c];a=h[c+1],o=h[c+2];let m=(i-f)/(h[c+3]-f);a+=(h[c+3+1]-a)*m,o+=(h[c+3+2]-o)*m;break;case 1:a=h[c+1],o=h[c+2];break;default:a=this.getBezierValue(i,c,1,u-2),o=this.getBezierValue(i,c,2,u+18-2)}switch(d){case 0:r.x=r.data.x+a*n,r.y=r.data.y+o*n;break;case 1:case 2:r.x+=(r.data.x+a-r.x)*n,r.y+=(r.data.y+o-r.y)*n;break;case 3:r.x+=a*n,r.y+=o*n}}},Gt=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.x+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.x=r.data.x;return;case 1:r.x+=(r.data.x-r.x)*n}return}let a=this.getCurveValue(i);switch(d){case 0:r.x=r.data.x+a*n;break;case 1:case 2:r.x+=(r.data.x+a-r.x)*n;break;case 3:r.x+=a*n}}},jt=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.y+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.y=r.data.y;return;case 1:r.y+=(r.data.y-r.y)*n}return}let a=this.getCurveValue(i);switch(d){case 0:r.y=r.data.y+a*n;break;case 1:case 2:r.y+=(r.data.y+a-r.y)*n;break;case 3:r.y+=a*n}}},Zt=class extends bt{boneIndex=0;constructor(e,t,i){super(e,t,ce.scaleX+"|"+i,ce.scaleY+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleX=r.data.scaleX,r.scaleY=r.data.scaleY;return;case 1:r.scaleX+=(r.data.scaleX-r.scaleX)*n,r.scaleY+=(r.data.scaleY-r.scaleY)*n}return}let a,o,c=me.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let f=h[c];a=h[c+1],o=h[c+2];let m=(i-f)/(h[c+3]-f);a+=(h[c+3+1]-a)*m,o+=(h[c+3+2]-o)*m;break;case 1:a=h[c+1],o=h[c+2];break;default:a=this.getBezierValue(i,c,1,u-2),o=this.getBezierValue(i,c,2,u+18-2)}if(a*=r.data.scaleX,o*=r.data.scaleY,n==1)d==3?(r.scaleX+=a-r.data.scaleX,r.scaleY+=o-r.data.scaleY):(r.scaleX=a,r.scaleY=o);else{let f=0,m=0;if(l==1)switch(d){case 0:f=r.data.scaleX,m=r.data.scaleY,r.scaleX=f+(Math.abs(a)*F.signum(f)-f)*n,r.scaleY=m+(Math.abs(o)*F.signum(m)-m)*n;break;case 1:case 2:f=r.scaleX,m=r.scaleY,r.scaleX=f+(Math.abs(a)*F.signum(f)-f)*n,r.scaleY=m+(Math.abs(o)*F.signum(m)-m)*n;break;case 3:r.scaleX+=(a-r.data.scaleX)*n,r.scaleY+=(o-r.data.scaleY)*n}else switch(d){case 0:f=Math.abs(r.data.scaleX)*F.signum(a),m=Math.abs(r.data.scaleY)*F.signum(o),r.scaleX=f+(a-f)*n,r.scaleY=m+(o-m)*n;break;case 1:case 2:f=Math.abs(r.scaleX)*F.signum(a),m=Math.abs(r.scaleY)*F.signum(o),r.scaleX=f+(a-f)*n,r.scaleY=m+(o-m)*n;break;case 3:r.scaleX+=(a-r.data.scaleX)*n,r.scaleY+=(o-r.data.scaleY)*n}}}},Jt=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.scaleX+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleX=r.data.scaleX;return;case 1:r.scaleX+=(r.data.scaleX-r.scaleX)*n}return}let a=this.getCurveValue(i)*r.data.scaleX;if(n==1)d==3?r.scaleX+=a-r.data.scaleX:r.scaleX=a;else{let o=0;if(l==1)switch(d){case 0:o=r.data.scaleX,r.scaleX=o+(Math.abs(a)*F.signum(o)-o)*n;break;case 1:case 2:o=r.scaleX,r.scaleX=o+(Math.abs(a)*F.signum(o)-o)*n;break;case 3:r.scaleX+=(a-r.data.scaleX)*n}else switch(d){case 0:o=Math.abs(r.data.scaleX)*F.signum(a),r.scaleX=o+(a-o)*n;break;case 1:case 2:o=Math.abs(r.scaleX)*F.signum(a),r.scaleX=o+(a-o)*n;break;case 3:r.scaleX+=(a-r.data.scaleX)*n}}}},Kt=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.scaleY+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleY=r.data.scaleY;return;case 1:r.scaleY+=(r.data.scaleY-r.scaleY)*n}return}let a=this.getCurveValue(i)*r.data.scaleY;if(n==1)d==3?r.scaleY+=a-r.data.scaleY:r.scaleY=a;else{let o=0;if(l==1)switch(d){case 0:o=r.data.scaleY,r.scaleY=o+(Math.abs(a)*F.signum(o)-o)*n;break;case 1:case 2:o=r.scaleY,r.scaleY=o+(Math.abs(a)*F.signum(o)-o)*n;break;case 3:r.scaleY+=(a-r.data.scaleY)*n}else switch(d){case 0:o=Math.abs(r.data.scaleY)*F.signum(a),r.scaleY=o+(a-o)*n;break;case 1:case 2:o=Math.abs(r.scaleY)*F.signum(a),r.scaleY=o+(a-o)*n;break;case 3:r.scaleY+=(a-r.data.scaleY)*n}}}},Qt=class extends bt{boneIndex=0;constructor(e,t,i){super(e,t,ce.shearX+"|"+i,ce.shearY+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearX=r.data.shearX,r.shearY=r.data.shearY;return;case 1:r.shearX+=(r.data.shearX-r.shearX)*n,r.shearY+=(r.data.shearY-r.shearY)*n}return}let a=0,o=0,c=me.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let f=h[c];a=h[c+1],o=h[c+2];let m=(i-f)/(h[c+3]-f);a+=(h[c+3+1]-a)*m,o+=(h[c+3+2]-o)*m;break;case 1:a=h[c+1],o=h[c+2];break;default:a=this.getBezierValue(i,c,1,u-2),o=this.getBezierValue(i,c,2,u+18-2)}switch(d){case 0:r.shearX=r.data.shearX+a*n,r.shearY=r.data.shearY+o*n;break;case 1:case 2:r.shearX+=(r.data.shearX+a-r.shearX)*n,r.shearY+=(r.data.shearY+o-r.shearY)*n;break;case 3:r.shearX+=a*n,r.shearY+=o*n}}},$t=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.shearX+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearX=r.data.shearX;return;case 1:r.shearX+=(r.data.shearX-r.shearX)*n}return}let a=this.getCurveValue(i);switch(d){case 0:r.shearX=r.data.shearX+a*n;break;case 1:case 2:r.shearX+=(r.data.shearX+a-r.shearX)*n;break;case 3:r.shearX+=a*n}}},ei=class extends Ie{boneIndex=0;constructor(e,t,i){super(e,t,ce.shearY+"|"+i),this.boneIndex=i}apply(e,t,i,s,n,d,l){let r=e.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearY=r.data.shearY;return;case 1:r.shearY+=(r.data.shearY-r.shearY)*n}return}let a=this.getCurveValue(i);switch(d){case 0:r.shearY=r.data.shearY+a*n;break;case 1:case 2:r.shearY+=(r.data.shearY+a-r.shearY)*n;break;case 3:r.shearY+=a*n}}},ti=class extends Ee{slotIndex=0;constructor(e,t,i){super(e,t,[ce.rgb+"|"+i,ce.alpha+"|"+i]),this.slotIndex=i}getFrameEntries(){return 5}setFrame(e,t,i,s,n,d){e*=5,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s,this.frames[e+3]=n,this.frames[e+4]=d}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,a=r.color;if(i<h[0]){let x=r.data.color;switch(d){case 0:a.setFromColor(x);return;case 1:a.add((x.r-a.r)*n,(x.g-a.g)*n,(x.b-a.b)*n,(x.a-a.a)*n)}return}let o=0,c=0,u=0,f=0,m=me.search(h,i,5),g=this.curves[m/5];switch(g){case 0:let x=h[m];o=h[m+1],c=h[m+2],u=h[m+3],f=h[m+4];let v=(i-x)/(h[m+5]-x);o+=(h[m+5+1]-o)*v,c+=(h[m+5+2]-c)*v,u+=(h[m+5+3]-u)*v,f+=(h[m+5+4]-f)*v;break;case 1:o=h[m+1],c=h[m+2],u=h[m+3],f=h[m+4];break;default:o=this.getBezierValue(i,m,1,g-2),c=this.getBezierValue(i,m,2,g+18-2),u=this.getBezierValue(i,m,3,g+18*2-2),f=this.getBezierValue(i,m,4,g+18*3-2)}n==1?a.set(o,c,u,f):(d==0&&a.setFromColor(r.data.color),a.add((o-a.r)*n,(c-a.g)*n,(u-a.b)*n,(f-a.a)*n))}},ii=class extends Ee{slotIndex=0;constructor(e,t,i){super(e,t,[ce.rgb+"|"+i]),this.slotIndex=i}getFrameEntries(){return 4}setFrame(e,t,i,s,n){e<<=2,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s,this.frames[e+3]=n}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,a=r.color;if(i<h[0]){let g=r.data.color;switch(d){case 0:a.r=g.r,a.g=g.g,a.b=g.b;return;case 1:a.r+=(g.r-a.r)*n,a.g+=(g.g-a.g)*n,a.b+=(g.b-a.b)*n}return}let o=0,c=0,u=0,f=me.search(h,i,4),m=this.curves[f>>2];switch(m){case 0:let g=h[f];o=h[f+1],c=h[f+2],u=h[f+3];let x=(i-g)/(h[f+4]-g);o+=(h[f+4+1]-o)*x,c+=(h[f+4+2]-c)*x,u+=(h[f+4+3]-u)*x;break;case 1:o=h[f+1],c=h[f+2],u=h[f+3];break;default:o=this.getBezierValue(i,f,1,m-2),c=this.getBezierValue(i,f,2,m+18-2),u=this.getBezierValue(i,f,3,m+18*2-2)}if(n==1)a.r=o,a.g=c,a.b=u;else{if(d==0){let g=r.data.color;a.r=g.r,a.g=g.g,a.b=g.b}a.r+=(o-a.r)*n,a.g+=(c-a.g)*n,a.b+=(u-a.b)*n}}},si=class extends Ie{slotIndex=0;constructor(e,t,i){super(e,t,ce.alpha+"|"+i),this.slotIndex=i}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=r.color;if(i<this.frames[0]){let o=r.data.color;switch(d){case 0:h.a=o.a;return;case 1:h.a+=(o.a-h.a)*n}return}let a=this.getCurveValue(i);n==1?h.a=a:(d==0&&(h.a=r.data.color.a),h.a+=(a-h.a)*n)}},ri=class extends Ee{slotIndex=0;constructor(e,t,i){super(e,t,[ce.rgb+"|"+i,ce.alpha+"|"+i,ce.rgb2+"|"+i]),this.slotIndex=i}getFrameEntries(){return 8}setFrame(e,t,i,s,n,d,l,r,h){e<<=3,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s,this.frames[e+3]=n,this.frames[e+4]=d,this.frames[e+5]=l,this.frames[e+6]=r,this.frames[e+7]=h}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,a=r.color,o=r.darkColor;if(i<h[0]){let b=r.data.color,y=r.data.darkColor;switch(d){case 0:a.setFromColor(b),o.r=y.r,o.g=y.g,o.b=y.b;return;case 1:a.add((b.r-a.r)*n,(b.g-a.g)*n,(b.b-a.b)*n,(b.a-a.a)*n),o.r+=(y.r-o.r)*n,o.g+=(y.g-o.g)*n,o.b+=(y.b-o.b)*n}return}let c=0,u=0,f=0,m=0,g=0,x=0,v=0,p=me.search(h,i,8),w=this.curves[p>>3];switch(w){case 0:let b=h[p];c=h[p+1],u=h[p+2],f=h[p+3],m=h[p+4],g=h[p+5],x=h[p+6],v=h[p+7];let y=(i-b)/(h[p+8]-b);c+=(h[p+8+1]-c)*y,u+=(h[p+8+2]-u)*y,f+=(h[p+8+3]-f)*y,m+=(h[p+8+4]-m)*y,g+=(h[p+8+5]-g)*y,x+=(h[p+8+6]-x)*y,v+=(h[p+8+7]-v)*y;break;case 1:c=h[p+1],u=h[p+2],f=h[p+3],m=h[p+4],g=h[p+5],x=h[p+6],v=h[p+7];break;default:c=this.getBezierValue(i,p,1,w-2),u=this.getBezierValue(i,p,2,w+18-2),f=this.getBezierValue(i,p,3,w+18*2-2),m=this.getBezierValue(i,p,4,w+18*3-2),g=this.getBezierValue(i,p,5,w+18*4-2),x=this.getBezierValue(i,p,6,w+18*5-2),v=this.getBezierValue(i,p,7,w+18*6-2)}if(n==1)a.set(c,u,f,m),o.r=g,o.g=x,o.b=v;else{if(d==0){a.setFromColor(r.data.color);let b=r.data.darkColor;o.r=b.r,o.g=b.g,o.b=b.b}a.add((c-a.r)*n,(u-a.g)*n,(f-a.b)*n,(m-a.a)*n),o.r+=(g-o.r)*n,o.g+=(x-o.g)*n,o.b+=(v-o.b)*n}}},ni=class extends Ee{slotIndex=0;constructor(e,t,i){super(e,t,[ce.rgb+"|"+i,ce.rgb2+"|"+i]),this.slotIndex=i}getFrameEntries(){return 7}setFrame(e,t,i,s,n,d,l,r){e*=7,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s,this.frames[e+3]=n,this.frames[e+4]=d,this.frames[e+5]=l,this.frames[e+6]=r}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,a=r.color,o=r.darkColor;if(i<h[0]){let b=r.data.color,y=r.data.darkColor;switch(d){case 0:a.r=b.r,a.g=b.g,a.b=b.b,o.r=y.r,o.g=y.g,o.b=y.b;return;case 1:a.r+=(b.r-a.r)*n,a.g+=(b.g-a.g)*n,a.b+=(b.b-a.b)*n,o.r+=(y.r-o.r)*n,o.g+=(y.g-o.g)*n,o.b+=(y.b-o.b)*n}return}let c=0,u=0,f=0,m=0,g=0,x=0,v=0,p=me.search(h,i,7),w=this.curves[p/7];switch(w){case 0:let b=h[p];c=h[p+1],u=h[p+2],f=h[p+3],g=h[p+4],x=h[p+5],v=h[p+6];let y=(i-b)/(h[p+7]-b);c+=(h[p+7+1]-c)*y,u+=(h[p+7+2]-u)*y,f+=(h[p+7+3]-f)*y,g+=(h[p+7+4]-g)*y,x+=(h[p+7+5]-x)*y,v+=(h[p+7+6]-v)*y;break;case 1:c=h[p+1],u=h[p+2],f=h[p+3],g=h[p+4],x=h[p+5],v=h[p+6];break;default:c=this.getBezierValue(i,p,1,w-2),u=this.getBezierValue(i,p,2,w+18-2),f=this.getBezierValue(i,p,3,w+18*2-2),g=this.getBezierValue(i,p,4,w+18*3-2),x=this.getBezierValue(i,p,5,w+18*4-2),v=this.getBezierValue(i,p,6,w+18*5-2)}if(n==1)a.r=c,a.g=u,a.b=f,o.r=g,o.g=x,o.b=v;else{if(d==0){let b=r.data.color,y=r.data.darkColor;a.r=b.r,a.g=b.g,a.b=b.b,o.r=y.r,o.g=y.g,o.b=y.b}a.r+=(c-a.r)*n,a.g+=(u-a.g)*n,a.b+=(f-a.b)*n,o.r+=(g-o.r)*n,o.g+=(x-o.g)*n,o.b+=(v-o.b)*n}}},He=class extends me{slotIndex=0;attachmentNames;constructor(e,t){super(e,[ce.attachment+"|"+t]),this.slotIndex=t,this.attachmentNames=new Array(e)}getFrameCount(){return this.frames.length}setFrame(e,t,i){this.frames[e]=t,this.attachmentNames[e]=i}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(r.bone.active){if(l==1){d==0&&this.setAttachment(e,r,r.data.attachmentName);return}if(i<this.frames[0]){(d==0||d==1)&&this.setAttachment(e,r,r.data.attachmentName);return}this.setAttachment(e,r,this.attachmentNames[me.search1(this.frames,i)])}}setAttachment(e,t,i){t.setAttachment(i?e.getAttachment(this.slotIndex,i):null)}},ai=class extends Ee{slotIndex=0;attachment;vertices;constructor(e,t,i,s){super(e,t,[ce.deform+"|"+i+"|"+s.id]),this.slotIndex=i,this.attachment=s,this.vertices=new Array(e)}getFrameCount(){return this.frames.length}setFrame(e,t,i){this.frames[e]=t,this.vertices[e]=i}setBezier(e,t,i,s,n,d,l,r,h,a,o){let c=this.curves,u=this.getFrameCount()+e*18;i==0&&(c[t]=2+u);let f=(s-d*2+r)*.03,m=h*.03-l*.06,g=((d-r)*3-s+a)*.006,x=(l-h+.33333333)*.018,v=f*2+g,p=m*2+x,w=(d-s)*.3+f+g*.16666667,b=l*.3+m+x*.16666667,y=s+w,C=b;for(let A=u+18;u<A;u+=2)c[u]=y,c[u+1]=C,w+=v,b+=p,v+=g,p+=x,y+=w,C+=b}getCurvePercent(e,t){let i=this.curves,s=i[t];switch(s){case 0:let r=this.frames[t];return(e-r)/(this.frames[t+this.getFrameEntries()]-r);case 1:return 0}if(s-=2,i[s]>e){let r=this.frames[t];return i[s+1]*(e-r)/(i[s]-r)}let n=s+18;for(s+=2;s<n;s+=2)if(i[s]>=e){let r=i[s-2],h=i[s-1];return h+(e-r)/(i[s]-r)*(i[s+1]-h)}let d=i[n-2],l=i[n-1];return l+(1-l)*(e-d)/(this.frames[t+this.getFrameEntries()]-d)}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=r.getAttachment();if(!h||!(h instanceof ke)||h.timelineAttachment!=this.attachment)return;let a=r.deform;a.length==0&&(d=0);let o=this.vertices,c=o[0].length,u=this.frames;if(i<u[0]){switch(d){case 0:a.length=0;return;case 1:if(n==1){a.length=0;return}a.length=c;let p=h;if(p.bones){n=1-n;for(var f=0;f<c;f++)a[f]*=n}else{let w=p.vertices;for(var f=0;f<c;f++)a[f]+=(w[f]-a[f])*n}}return}if(a.length=c,i>=u[u.length-1]){let p=o[u.length-1];if(n==1)if(d==3){let w=h;if(w.bones)for(let b=0;b<c;b++)a[b]+=p[b];else{let b=w.vertices;for(let y=0;y<c;y++)a[y]+=p[y]-b[y]}}else B.arrayCopy(p,0,a,0,c);else switch(d){case 0:{let b=h;if(b.bones)for(let y=0;y<c;y++)a[y]=p[y]*n;else{let y=b.vertices;for(let C=0;C<c;C++){let A=y[C];a[C]=A+(p[C]-A)*n}}break}case 1:case 2:for(let b=0;b<c;b++)a[b]+=(p[b]-a[b])*n;break;case 3:let w=h;if(w.bones)for(let b=0;b<c;b++)a[b]+=p[b]*n;else{let b=w.vertices;for(let y=0;y<c;y++)a[y]+=(p[y]-b[y])*n}}return}let m=me.search1(u,i),g=this.getCurvePercent(i,m),x=o[m],v=o[m+1];if(n==1)if(d==3){let p=h;if(p.bones)for(let w=0;w<c;w++){let b=x[w];a[w]+=b+(v[w]-b)*g}else{let w=p.vertices;for(let b=0;b<c;b++){let y=x[b];a[b]+=y+(v[b]-y)*g-w[b]}}}else for(let p=0;p<c;p++){let w=x[p];a[p]=w+(v[p]-w)*g}else switch(d){case 0:{let w=h;if(w.bones)for(let b=0;b<c;b++){let y=x[b];a[b]=(y+(v[b]-y)*g)*n}else{let b=w.vertices;for(let y=0;y<c;y++){let C=x[y],A=b[y];a[y]=A+(C+(v[y]-C)*g-A)*n}}break}case 1:case 2:for(let w=0;w<c;w++){let b=x[w];a[w]+=(b+(v[w]-b)*g-a[w])*n}break;case 3:let p=h;if(p.bones)for(let w=0;w<c;w++){let b=x[w];a[w]+=(b+(v[w]-b)*g)*n}else{let w=p.vertices;for(let b=0;b<c;b++){let y=x[b];a[b]+=(y+(v[b]-y)*g-w[b])*n}}}}},$i=class extends me{events;constructor(e){super(e,$i.propertyIds),this.events=new Array(e)}getFrameCount(){return this.frames.length}setFrame(e,t){this.frames[e]=t.time,this.events[e]=t}apply(e,t,i,s,n,d,l){if(!s)return;let r=this.frames,h=this.frames.length;if(t>i)this.apply(e,t,Number.MAX_VALUE,s,n,d,l),t=-1;else if(t>=r[h-1])return;if(i<r[0])return;let a=0;if(t<r[0])a=0;else{a=me.search1(r,t)+1;let o=r[a];for(;a>0&&r[a-1]==o;)a--}for(;a<h&&i>=r[a];a++)s.push(this.events[a])}},ht=$i;P(ht,"propertyIds",[""+ce.event]);var es=class extends me{drawOrders;constructor(e){super(e,es.propertyIds),this.drawOrders=new Array(e)}getFrameCount(){return this.frames.length}setFrame(e,t,i){this.frames[e]=t,this.drawOrders[e]=i}apply(e,t,i,s,n,d,l){if(l==1){d==0&&B.arrayCopy(e.slots,0,e.drawOrder,0,e.slots.length);return}if(i<this.frames[0]){(d==0||d==1)&&B.arrayCopy(e.slots,0,e.drawOrder,0,e.slots.length);return}let r=me.search1(this.frames,i),h=this.drawOrders[r];if(!h)B.arrayCopy(e.slots,0,e.drawOrder,0,e.slots.length);else{let a=e.drawOrder,o=e.slots;for(let c=0,u=h.length;c<u;c++)a[c]=o[h[c]]}}},Ge=es;P(Ge,"propertyIds",[""+ce.drawOrder]);var li=class extends Ee{ikConstraintIndex=0;constructor(e,t,i){super(e,t,[ce.ikConstraint+"|"+i]),this.ikConstraintIndex=i}getFrameEntries(){return 6}setFrame(e,t,i,s,n,d,l){e*=6,this.frames[e]=t,this.frames[e+1]=i,this.frames[e+2]=s,this.frames[e+3]=n,this.frames[e+4]=d?1:0,this.frames[e+5]=l?1:0}apply(e,t,i,s,n,d,l){let r=e.ikConstraints[this.ikConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.mix=r.data.mix,r.softness=r.data.softness,r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch;return;case 1:r.mix+=(r.data.mix-r.mix)*n,r.softness+=(r.data.softness-r.softness)*n,r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch}return}let a=0,o=0,c=me.search(h,i,6),u=this.curves[c/6];switch(u){case 0:let f=h[c];a=h[c+1],o=h[c+2];let m=(i-f)/(h[c+6]-f);a+=(h[c+6+1]-a)*m,o+=(h[c+6+2]-o)*m;break;case 1:a=h[c+1],o=h[c+2];break;default:a=this.getBezierValue(i,c,1,u-2),o=this.getBezierValue(i,c,2,u+18-2)}d==0?(r.mix=r.data.mix+(a-r.data.mix)*n,r.softness=r.data.softness+(o-r.data.softness)*n,l==1?(r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch):(r.bendDirection=h[c+3],r.compress=h[c+4]!=0,r.stretch=h[c+5]!=0)):(r.mix+=(a-r.mix)*n,r.softness+=(o-r.softness)*n,l==0&&(r.bendDirection=h[c+3],r.compress=h[c+4]!=0,r.stretch=h[c+5]!=0))}},oi=class extends Ee{transformConstraintIndex=0;constructor(e,t,i){super(e,t,[ce.transformConstraint+"|"+i]),this.transformConstraintIndex=i}getFrameEntries(){return 7}setFrame(e,t,i,s,n,d,l,r){let h=this.frames;e*=7,h[e]=t,h[e+1]=i,h[e+2]=s,h[e+3]=n,h[e+4]=d,h[e+5]=l,h[e+6]=r}apply(e,t,i,s,n,d,l){let r=e.transformConstraints[this.transformConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){let v=r.data;switch(d){case 0:r.mixRotate=v.mixRotate,r.mixX=v.mixX,r.mixY=v.mixY,r.mixScaleX=v.mixScaleX,r.mixScaleY=v.mixScaleY,r.mixShearY=v.mixShearY;return;case 1:r.mixRotate+=(v.mixRotate-r.mixRotate)*n,r.mixX+=(v.mixX-r.mixX)*n,r.mixY+=(v.mixY-r.mixY)*n,r.mixScaleX+=(v.mixScaleX-r.mixScaleX)*n,r.mixScaleY+=(v.mixScaleY-r.mixScaleY)*n,r.mixShearY+=(v.mixShearY-r.mixShearY)*n}return}let a,o,c,u,f,m,g=me.search(h,i,7),x=this.curves[g/7];switch(x){case 0:let v=h[g];a=h[g+1],o=h[g+2],c=h[g+3],u=h[g+4],f=h[g+5],m=h[g+6];let p=(i-v)/(h[g+7]-v);a+=(h[g+7+1]-a)*p,o+=(h[g+7+2]-o)*p,c+=(h[g+7+3]-c)*p,u+=(h[g+7+4]-u)*p,f+=(h[g+7+5]-f)*p,m+=(h[g+7+6]-m)*p;break;case 1:a=h[g+1],o=h[g+2],c=h[g+3],u=h[g+4],f=h[g+5],m=h[g+6];break;default:a=this.getBezierValue(i,g,1,x-2),o=this.getBezierValue(i,g,2,x+18-2),c=this.getBezierValue(i,g,3,x+18*2-2),u=this.getBezierValue(i,g,4,x+18*3-2),f=this.getBezierValue(i,g,5,x+18*4-2),m=this.getBezierValue(i,g,6,x+18*5-2)}if(d==0){let v=r.data;r.mixRotate=v.mixRotate+(a-v.mixRotate)*n,r.mixX=v.mixX+(o-v.mixX)*n,r.mixY=v.mixY+(c-v.mixY)*n,r.mixScaleX=v.mixScaleX+(u-v.mixScaleX)*n,r.mixScaleY=v.mixScaleY+(f-v.mixScaleY)*n,r.mixShearY=v.mixShearY+(m-v.mixShearY)*n}else r.mixRotate+=(a-r.mixRotate)*n,r.mixX+=(o-r.mixX)*n,r.mixY+=(c-r.mixY)*n,r.mixScaleX+=(u-r.mixScaleX)*n,r.mixScaleY+=(f-r.mixScaleY)*n,r.mixShearY+=(m-r.mixShearY)*n}},hi=class extends Ie{pathConstraintIndex=0;constructor(e,t,i){super(e,t,ce.pathConstraintPosition+"|"+i),this.pathConstraintIndex=i}apply(e,t,i,s,n,d,l){let r=e.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.position=r.data.position;return;case 1:r.position+=(r.data.position-r.position)*n}return}let a=this.getCurveValue(i);d==0?r.position=r.data.position+(a-r.data.position)*n:r.position+=(a-r.position)*n}},di=class extends Ie{pathConstraintIndex=0;constructor(e,t,i){super(e,t,ce.pathConstraintSpacing+"|"+i),this.pathConstraintIndex=i}apply(e,t,i,s,n,d,l){let r=e.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.spacing=r.data.spacing;return;case 1:r.spacing+=(r.data.spacing-r.spacing)*n}return}let a=this.getCurveValue(i);d==0?r.spacing=r.data.spacing+(a-r.data.spacing)*n:r.spacing+=(a-r.spacing)*n}},ci=class extends Ee{pathConstraintIndex=0;constructor(e,t,i){super(e,t,[ce.pathConstraintMix+"|"+i]),this.pathConstraintIndex=i}getFrameEntries(){return 4}setFrame(e,t,i,s,n){let d=this.frames;e<<=2,d[e]=t,d[e+1]=i,d[e+2]=s,d[e+3]=n}apply(e,t,i,s,n,d,l){let r=e.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.mixRotate=r.data.mixRotate,r.mixX=r.data.mixX,r.mixY=r.data.mixY;return;case 1:r.mixRotate+=(r.data.mixRotate-r.mixRotate)*n,r.mixX+=(r.data.mixX-r.mixX)*n,r.mixY+=(r.data.mixY-r.mixY)*n}return}let a,o,c,u=me.search(h,i,4),f=this.curves[u>>2];switch(f){case 0:let m=h[u];a=h[u+1],o=h[u+2],c=h[u+3];let g=(i-m)/(h[u+4]-m);a+=(h[u+4+1]-a)*g,o+=(h[u+4+2]-o)*g,c+=(h[u+4+3]-c)*g;break;case 1:a=h[u+1],o=h[u+2],c=h[u+3];break;default:a=this.getBezierValue(i,u,1,f-2),o=this.getBezierValue(i,u,2,f+18-2),c=this.getBezierValue(i,u,3,f+18*2-2)}if(d==0){let m=r.data;r.mixRotate=m.mixRotate+(a-m.mixRotate)*n,r.mixX=m.mixX+(o-m.mixX)*n,r.mixY=m.mixY+(c-m.mixY)*n}else r.mixRotate+=(a-r.mixRotate)*n,r.mixX+=(o-r.mixX)*n,r.mixY+=(c-r.mixY)*n}},Ne=class extends me{slotIndex;attachment;constructor(e,t,i){super(e,[ce.sequence+"|"+t+"|"+i.sequence.id]),this.slotIndex=t,this.attachment=i}getFrameEntries(){return Ne.ENTRIES}getSlotIndex(){return this.slotIndex}getAttachment(){return this.attachment}setFrame(e,t,i,s,n){let d=this.frames;e*=Ne.ENTRIES,d[e]=t,d[e+Ne.MODE]=i|s<<4,d[e+Ne.DELAY]=n}apply(e,t,i,s,n,d,l){let r=e.slots[this.slotIndex];if(!r.bone.active)return;let h=r.attachment,a=this.attachment;if(h!=a&&(!(h instanceof ke)||h.timelineAttachment!=a))return;let o=this.frames;if(i<o[0]){(d==0||d==1)&&(r.sequenceIndex=-1);return}let c=me.search(o,i,Ne.ENTRIES),u=o[c],f=o[c+Ne.MODE],m=o[c+Ne.DELAY];if(!this.attachment.sequence)return;let g=f>>4,x=this.attachment.sequence.regions.length,v=Ji[f&15];if(v!=0)switch(g+=(i-u)/m+1e-5|0,v){case 1:g=Math.min(x-1,g);break;case 2:g%=x;break;case 3:{let p=(x<<1)-2;g=p==0?0:g%p,g>=x&&(g=p-g);break}case 4:g=Math.max(x-1-g,0);break;case 5:g=x-1-g%x;break;case 6:{let p=(x<<1)-2;g=p==0?0:(g+x-1)%p,g>=x&&(g=p-g)}}r.sequenceIndex=g}},$e=Ne;P($e,"ENTRIES",3),P($e,"MODE",1),P($e,"DELAY",2);var vt=class{static emptyAnimation(){return vt._emptyAnimation}data;tracks=new Array;timeScale=1;unkeyedState=0;events=new Array;listeners=new Array;queue=new is(this);propertyIDs=new Wt;animationsChanged=!1;trackEntryPool=new lt(()=>new ts);constructor(e){this.data=e}update(e){e*=this.timeScale;let t=this.tracks;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(!n)continue;n.animationLast=n.nextAnimationLast,n.trackLast=n.nextTrackLast;let d=e*n.timeScale;if(n.delay>0){if(n.delay-=d,n.delay>0)continue;d=-n.delay,n.delay=0}let l=n.next;if(l){let r=n.trackLast-l.delay;if(r>=0){for(l.delay=0,l.trackTime+=n.timeScale==0?0:(r/n.timeScale+e)*l.timeScale,n.trackTime+=d,this.setCurrent(i,l,!0);l.mixingFrom;)l.mixTime+=e,l=l.mixingFrom;continue}}else if(n.trackLast>=n.trackEnd&&!n.mixingFrom){t[i]=null,this.queue.end(n),this.clearNext(n);continue}if(n.mixingFrom&&this.updateMixingFrom(n,e)){let r=n.mixingFrom;for(n.mixingFrom=null,r&&(r.mixingTo=null);r;)this.queue.end(r),r=r.mixingFrom}n.trackTime+=d}this.queue.drain()}updateMixingFrom(e,t){let i=e.mixingFrom;if(!i)return!0;let s=this.updateMixingFrom(i,t);return i.animationLast=i.nextAnimationLast,i.trackLast=i.nextTrackLast,e.mixTime>0&&e.mixTime>=e.mixDuration?((i.totalAlpha==0||e.mixDuration==0)&&(e.mixingFrom=i.mixingFrom,i.mixingFrom&&(i.mixingFrom.mixingTo=e),e.interruptAlpha=i.interruptAlpha,this.queue.end(i)),s):(i.trackTime+=t*i.timeScale,e.mixTime+=t,!1)}apply(e){if(!e)throw new Error("skeleton cannot be null.");this.animationsChanged&&this._animationsChanged();let t=this.events,i=this.tracks,s=!1;for(let c=0,u=i.length;c<u;c++){let f=i[c];if(!f||f.delay>0)continue;s=!0;let m=c==0?1:f.mixBlend,g=f.alpha;f.mixingFrom?g*=this.applyMixingFrom(f,e,m):f.trackTime>=f.trackEnd&&!f.next&&(g=0);let x=f.animationLast,v=f.getAnimationTime(),p=v,w=t;f.reverse&&(p=f.animation.duration-p,w=null);let b=f.animation.timelines,y=b.length;if(c==0&&g==1||m==3)for(let C=0;C<y;C++){B.webkit602BugfixHelper(g,m);var n=b[C];n instanceof He?this.applyAttachmentTimeline(n,e,p,m,!0):n.apply(e,x,p,w,g,m,0)}else{let C=f.timelineMode,A=f.shortestRotation,T=!A&&f.timelinesRotation.length!=y<<1;T&&(f.timelinesRotation.length=y<<1);for(let M=0;M<y;M++){let Y=b[M],X=C[M]==yt?m:0;!A&&Y instanceof ot?this.applyRotateTimeline(Y,e,p,g,X,f.timelinesRotation,M<<1,T):Y instanceof He?this.applyAttachmentTimeline(Y,e,p,m,!0):(B.webkit602BugfixHelper(g,m),Y.apply(e,x,p,w,g,X,0))}}this.queueEvents(f,v),t.length=0,f.nextAnimationLast=v,f.nextTrackLast=f.trackTime}for(var d=this.unkeyedState+gi,l=e.slots,r=0,h=e.slots.length;r<h;r++){var a=l[r];if(a.attachmentState==d){var o=a.data.attachmentName;a.setAttachment(o?e.getAttachment(a.data.index,o):null)}}return this.unkeyedState+=2,this.queue.drain(),s}applyMixingFrom(e,t,i){let s=e.mixingFrom;s.mixingFrom&&this.applyMixingFrom(s,t,i);let n=0;e.mixDuration==0?(n=1,i==1&&(i=0)):(n=e.mixTime/e.mixDuration,n>1&&(n=1),i!=1&&(i=s.mixBlend));let d=n<s.attachmentThreshold,l=n<s.drawOrderThreshold,r=s.animation.timelines,h=r.length,a=s.alpha*e.interruptAlpha,o=a*(1-n),c=s.animationLast,u=s.getAnimationTime(),f=u,m=null;if(s.reverse?f=s.animation.duration-f:n<s.eventThreshold&&(m=this.events),i==3)for(let g=0;g<h;g++)r[g].apply(t,c,f,m,o,i,1);else{let g=s.timelineMode,x=s.timelineHoldMix,v=s.shortestRotation,p=!v&&s.timelinesRotation.length!=h<<1;p&&(s.timelinesRotation.length=h<<1),s.totalAlpha=0;for(let w=0;w<h;w++){let b=r[w],y=1,C,A=0;switch(g[w]){case yt:if(!l&&b instanceof Ge)continue;C=i,A=o;break;case fi:C=0,A=o;break;case mi:C=i,A=a;break;case At:C=0,A=a;break;default:C=0;let T=x[w];A=a*Math.max(0,1-T.mixTime/T.mixDuration);break}s.totalAlpha+=A,!v&&b instanceof ot?this.applyRotateTimeline(b,t,f,A,C,s.timelinesRotation,w<<1,p):b instanceof He?this.applyAttachmentTimeline(b,t,f,C,d):(B.webkit602BugfixHelper(A,i),l&&b instanceof Ge&&C==0&&(y=0),b.apply(t,c,f,m,A,C,y))}}return e.mixDuration>0&&this.queueEvents(s,u),this.events.length=0,s.nextAnimationLast=u,s.nextTrackLast=s.trackTime,n}applyAttachmentTimeline(e,t,i,s,n){var d=t.slots[e.slotIndex];d.bone.active&&(i<e.frames[0]?(s==0||s==1)&&this.setAttachment(t,d,d.data.attachmentName,n):this.setAttachment(t,d,e.attachmentNames[me.search1(e.frames,i)],n),d.attachmentState<=this.unkeyedState&&(d.attachmentState=this.unkeyedState+gi))}setAttachment(e,t,i,s){t.setAttachment(i?e.getAttachment(t.data.index,i):null),s&&(t.attachmentState=this.unkeyedState+rs)}applyRotateTimeline(e,t,i,s,n,d,l,r){if(r&&(d[l]=0),s==1){e.apply(t,0,i,null,1,n,0);return}let h=t.bones[e.boneIndex];if(!h.active)return;let a=e.frames,o=0,c=0;if(i<a[0])switch(n){case 0:h.rotation=h.data.rotation;default:return;case 1:o=h.rotation,c=h.data.rotation}else o=n==0?h.data.rotation:h.rotation,c=h.data.rotation+e.getCurveValue(i);let u=0,f=c-o;if(f-=(16384-(16384.499999999996-f/360|0))*360,f==0)u=d[l];else{let m=0,g=0;r?(m=0,g=f):(m=d[l],g=d[l+1]);let x=f>0,v=m>=0;F.signum(g)!=F.signum(f)&&Math.abs(g)<=90&&(Math.abs(m)>180&&(m+=360*F.signum(m)),v=x),u=f+m-m%360,v!=x&&(u+=360*F.signum(m)),d[l]=u}d[l+1]=f,h.rotation=o+u*s}queueEvents(e,t){let i=e.animationStart,s=e.animationEnd,n=s-i,d=e.trackLast%n,l=this.events,r=0,h=l.length;for(;r<h;r++){let o=l[r];if(o.time<d)break;o.time>s||this.queue.event(e,o)}let a=!1;for(e.loop?a=n==0||d>e.trackTime%n:a=t>=s&&e.animationLast<s,a&&this.queue.complete(e);r<h;r++){let o=l[r];o.time<i||this.queue.event(e,o)}}clearTracks(){let e=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let t=0,i=this.tracks.length;t<i;t++)this.clearTrack(t);this.tracks.length=0,this.queue.drainDisabled=e,this.queue.drain()}clearTrack(e){if(e>=this.tracks.length)return;let t=this.tracks[e];if(!t)return;this.queue.end(t),this.clearNext(t);let i=t;for(;;){let s=i.mixingFrom;if(!s)break;this.queue.end(s),i.mixingFrom=null,i.mixingTo=null,i=s}this.tracks[t.trackIndex]=null,this.queue.drain()}setCurrent(e,t,i){let s=this.expandToIndex(e);this.tracks[e]=t,t.previous=null,s&&(i&&this.queue.interrupt(s),t.mixingFrom=s,s.mixingTo=t,t.mixTime=0,s.mixingFrom&&s.mixDuration>0&&(t.interruptAlpha*=Math.min(1,s.mixTime/s.mixDuration)),s.timelinesRotation.length=0),this.queue.start(t)}setAnimation(e,t,i=!1){let s=this.data.skeletonData.findAnimation(t);if(!s)throw new Error("Animation not found: "+t);return this.setAnimationWith(e,s,i)}setAnimationWith(e,t,i=!1){if(!t)throw new Error("animation cannot be null.");let s=!0,n=this.expandToIndex(e);n&&(n.nextTrackLast==-1?(this.tracks[e]=n.mixingFrom,this.queue.interrupt(n),this.queue.end(n),this.clearNext(n),n=n.mixingFrom,s=!1):this.clearNext(n));let d=this.trackEntry(e,t,i,n);return this.setCurrent(e,d,s),this.queue.drain(),d}addAnimation(e,t,i=!1,s=0){let n=this.data.skeletonData.findAnimation(t);if(!n)throw new Error("Animation not found: "+t);return this.addAnimationWith(e,n,i,s)}addAnimationWith(e,t,i=!1,s=0){if(!t)throw new Error("animation cannot be null.");let n=this.expandToIndex(e);if(n)for(;n.next;)n=n.next;let d=this.trackEntry(e,t,i,n);return n?(n.next=d,d.previous=n,s<=0&&(s+=n.getTrackComplete()-d.mixDuration)):(this.setCurrent(e,d,!0),this.queue.drain()),d.delay=s,d}setEmptyAnimation(e,t=0){let i=this.setAnimationWith(e,vt.emptyAnimation(),!1);return i.mixDuration=t,i.trackEnd=t,i}addEmptyAnimation(e,t=0,i=0){let s=this.addAnimationWith(e,vt.emptyAnimation(),!1,i);return i<=0&&(s.delay+=s.mixDuration-t),s.mixDuration=t,s.trackEnd=t,s}setEmptyAnimations(e=0){let t=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let i=0,s=this.tracks.length;i<s;i++){let n=this.tracks[i];n&&this.setEmptyAnimation(n.trackIndex,e)}this.queue.drainDisabled=t,this.queue.drain()}expandToIndex(e){return e<this.tracks.length?this.tracks[e]:(B.ensureArrayCapacity(this.tracks,e+1,null),this.tracks.length=e+1,null)}trackEntry(e,t,i,s){let n=this.trackEntryPool.obtain();return n.reset(),n.trackIndex=e,n.animation=t,n.loop=i,n.holdPrevious=!1,n.reverse=!1,n.shortestRotation=!1,n.eventThreshold=0,n.attachmentThreshold=0,n.drawOrderThreshold=0,n.animationStart=0,n.animationEnd=t.duration,n.animationLast=-1,n.nextAnimationLast=-1,n.delay=0,n.trackTime=0,n.trackLast=-1,n.nextTrackLast=-1,n.trackEnd=Number.MAX_VALUE,n.timeScale=1,n.alpha=1,n.mixTime=0,n.mixDuration=s?this.data.getMix(s.animation,t):0,n.interruptAlpha=1,n.totalAlpha=0,n.mixBlend=2,n}clearNext(e){let t=e.next;for(;t;)this.queue.dispose(t),t=t.next;e.next=null}_animationsChanged(){this.animationsChanged=!1,this.propertyIDs.clear();let e=this.tracks;for(let t=0,i=e.length;t<i;t++){let s=e[t];if(s){for(;s.mixingFrom;)s=s.mixingFrom;do(!s.mixingTo||s.mixBlend!=3)&&this.computeHold(s),s=s.mixingTo;while(s)}}}computeHold(e){let t=e.mixingTo,i=e.animation.timelines,s=e.animation.timelines.length,n=e.timelineMode;n.length=s;let d=e.timelineHoldMix;d.length=0;let l=this.propertyIDs;if(t&&t.holdPrevious){for(let r=0;r<s;r++)n[r]=l.addAll(i[r].getPropertyIds())?At:mi;return}e:for(let r=0;r<s;r++){let h=i[r],a=h.getPropertyIds();if(!l.addAll(a))n[r]=yt;else if(!t||h instanceof He||h instanceof Ge||h instanceof ht||!t.animation.hasTimeline(a))n[r]=fi;else{for(let o=t.mixingTo;o;o=o.mixingTo)if(!o.animation.hasTimeline(a)){if(e.mixDuration>0){n[r]=ss,d[r]=o;continue e}break}n[r]=At}}}getCurrent(e){return e>=this.tracks.length?null:this.tracks[e]}addListener(e){if(!e)throw new Error("listener cannot be null.");this.listeners.push(e)}removeListener(e){let t=this.listeners.indexOf(e);t>=0&&this.listeners.splice(t,1)}clearListeners(){this.listeners.length=0}clearListenerNotifications(){this.queue.clear()}},ui=vt;P(ui,"_emptyAnimation",new xt("<empty>",[],0));var ts=class{animation=null;previous=null;next=null;mixingFrom=null;mixingTo=null;listener=null;trackIndex=0;loop=!1;holdPrevious=!1;reverse=!1;shortestRotation=!1;eventThreshold=0;attachmentThreshold=0;drawOrderThreshold=0;animationStart=0;animationEnd=0;animationLast=0;nextAnimationLast=0;delay=0;trackTime=0;trackLast=0;nextTrackLast=0;trackEnd=0;timeScale=0;alpha=0;mixTime=0;mixDuration=0;interruptAlpha=0;totalAlpha=0;mixBlend=2;timelineMode=new Array;timelineHoldMix=new Array;timelinesRotation=new Array;reset(){this.next=null,this.previous=null,this.mixingFrom=null,this.mixingTo=null,this.animation=null,this.listener=null,this.timelineMode.length=0,this.timelineHoldMix.length=0,this.timelinesRotation.length=0}getAnimationTime(){if(this.loop){let e=this.animationEnd-this.animationStart;return e==0?this.animationStart:this.trackTime%e+this.animationStart}return Math.min(this.trackTime+this.animationStart,this.animationEnd)}setAnimationLast(e){this.animationLast=e,this.nextAnimationLast=e}isComplete(){return this.trackTime>=this.animationEnd-this.animationStart}resetRotationDirections(){this.timelinesRotation.length=0}getTrackComplete(){let e=this.animationEnd-this.animationStart;if(e!=0){if(this.loop)return e*(1+(this.trackTime/e|0));if(this.trackTime<e)return e}return this.trackTime}},is=class{objects=[];drainDisabled=!1;animState;constructor(e){this.animState=e}start(e){this.objects.push(ve.start),this.objects.push(e),this.animState.animationsChanged=!0}interrupt(e){this.objects.push(ve.interrupt),this.objects.push(e)}end(e){this.objects.push(ve.end),this.objects.push(e),this.animState.animationsChanged=!0}dispose(e){this.objects.push(ve.dispose),this.objects.push(e)}complete(e){this.objects.push(ve.complete),this.objects.push(e)}event(e,t){this.objects.push(ve.event),this.objects.push(e),this.objects.push(t)}drain(){if(this.drainDisabled)return;this.drainDisabled=!0;let e=this.objects,t=this.animState.listeners;for(let i=0;i<e.length;i+=2){let s=e[i],n=e[i+1];switch(s){case ve.start:n.listener&&n.listener.start&&n.listener.start(n);for(let l=0;l<t.length;l++){let r=t[l];r.start&&r.start(n)}break;case ve.interrupt:n.listener&&n.listener.interrupt&&n.listener.interrupt(n);for(let l=0;l<t.length;l++){let r=t[l];r.interrupt&&r.interrupt(n)}break;case ve.end:n.listener&&n.listener.end&&n.listener.end(n);for(let l=0;l<t.length;l++){let r=t[l];r.end&&r.end(n)}case ve.dispose:n.listener&&n.listener.dispose&&n.listener.dispose(n);for(let l=0;l<t.length;l++){let r=t[l];r.dispose&&r.dispose(n)}this.animState.trackEntryPool.free(n);break;case ve.complete:n.listener&&n.listener.complete&&n.listener.complete(n);for(let l=0;l<t.length;l++){let r=t[l];r.complete&&r.complete(n)}break;case ve.event:let d=e[i+++2];n.listener&&n.listener.event&&n.listener.event(n,d);for(let l=0;l<t.length;l++){let r=t[l];r.event&&r.event(n,d)}break}}this.clear(),this.drainDisabled=!1}clear(){this.objects.length=0}},ve=(e=>(e[e.start=0]="start",e[e.interrupt=1]="interrupt",e[e.end=2]="end",e[e.dispose=3]="dispose",e[e.complete=4]="complete",e[e.event=5]="event",e))(ve||{}),Ks=class{start(e){}interrupt(e){}end(e){}dispose(e){}complete(e){}event(e,t){}},yt=0,fi=1,mi=2,At=3,ss=4,gi=1,rs=2,ns=class{skeletonData;animationToMixTime={};defaultMix=0;constructor(e){if(!e)throw new Error("skeletonData cannot be null.");this.skeletonData=e}setMix(e,t,i){let s=this.skeletonData.findAnimation(e);if(!s)throw new Error("Animation not found: "+e);let n=this.skeletonData.findAnimation(t);if(!n)throw new Error("Animation not found: "+t);this.setMixWith(s,n,i)}setMixWith(e,t,i){if(!e)throw new Error("from cannot be null.");if(!t)throw new Error("to cannot be null.");let s=e.name+"."+t.name;this.animationToMixTime[s]=i}getMix(e,t){let i=e.name+"."+t.name,s=this.animationToMixTime[i];return s===void 0?this.defaultMix:s}},Ct=class extends ke{color=new D(1,1,1,1);constructor(e){super(e)}copy(){let e=new Ct(this.name);return this.copyTo(e),e.color.setFromColor(this.color),e}},dt=class extends ke{endSlot=null;color=new D(.2275,.2275,.8078,1);constructor(e){super(e)}copy(){let e=new dt(this.name);return this.copyTo(e),e.endSlot=this.endSlot,e.color.setFromColor(this.color),e}},pi=class{_image;constructor(e){this._image=e}getImage(){return this._image}},St=(e=>(e[e.Nearest=9728]="Nearest",e[e.Linear=9729]="Linear",e[e.MipMap=9987]="MipMap",e[e.MipMapNearestNearest=9984]="MipMapNearestNearest",e[e.MipMapLinearNearest=9985]="MipMapLinearNearest",e[e.MipMapNearestLinear=9986]="MipMapNearestLinear",e[e.MipMapLinearLinear=9987]="MipMapLinearLinear",e))(St||{}),as=(e=>(e[e.MirroredRepeat=33648]="MirroredRepeat",e[e.ClampToEdge=33071]="ClampToEdge",e[e.Repeat=10497]="Repeat",e))(as||{}),ls=class{texture;u=0;v=0;u2=0;v2=0;width=0;height=0;degrees=0;offsetX=0;offsetY=0;originalWidth=0;originalHeight=0},Qs=class extends pi{setFilters(e,t){}setWraps(e,t){}dispose(){}},os=class{pages=new Array;regions=new Array;constructor(e){let t=new $s(e),i=new Array(4),s={};s.size=a=>{a.width=parseInt(i[1]),a.height=parseInt(i[2])},s.format=()=>{},s.filter=a=>{a.minFilter=B.enumValue(St,i[1]),a.magFilter=B.enumValue(St,i[2])},s.repeat=a=>{i[1].indexOf("x")!=-1&&(a.uWrap=10497),i[1].indexOf("y")!=-1&&(a.vWrap=10497)},s.pma=a=>{a.pma=i[1]=="true"};var n={};n.xy=a=>{a.x=parseInt(i[1]),a.y=parseInt(i[2])},n.size=a=>{a.width=parseInt(i[1]),a.height=parseInt(i[2])},n.bounds=a=>{a.x=parseInt(i[1]),a.y=parseInt(i[2]),a.width=parseInt(i[3]),a.height=parseInt(i[4])},n.offset=a=>{a.offsetX=parseInt(i[1]),a.offsetY=parseInt(i[2])},n.orig=a=>{a.originalWidth=parseInt(i[1]),a.originalHeight=parseInt(i[2])},n.offsets=a=>{a.offsetX=parseInt(i[1]),a.offsetY=parseInt(i[2]),a.originalWidth=parseInt(i[3]),a.originalHeight=parseInt(i[4])},n.rotate=a=>{let o=i[1];o=="true"?a.degrees=90:o!="false"&&(a.degrees=parseInt(o))},n.index=a=>{a.index=parseInt(i[1])};let d=t.readLine();for(;d&&d.trim().length==0;)d=t.readLine();for(;!(!d||d.trim().length==0||t.readEntry(i,d)==0);)d=t.readLine();let l=null,r=null,h=null;for(;d!==null;)if(d.trim().length==0)l=null,d=t.readLine();else if(l){let a=new wi(l,d);for(;;){let o=t.readEntry(i,d=t.readLine());if(o==0)break;let c=n[i[0]];if(c)c(a);else{r||(r=[]),h||(h=[]),r.push(i[0]);let u=[];for(let f=0;f<o;f++)u.push(parseInt(i[f+1]));h.push(u)}}a.originalWidth==0&&a.originalHeight==0&&(a.originalWidth=a.width,a.originalHeight=a.height),r&&r.length>0&&h&&h.length>0&&(a.names=r,a.values=h,r=null,h=null),a.u=a.x/l.width,a.v=a.y/l.height,a.degrees==90?(a.u2=(a.x+a.height)/l.width,a.v2=(a.y+a.width)/l.height):(a.u2=(a.x+a.width)/l.width,a.v2=(a.y+a.height)/l.height),this.regions.push(a)}else{for(l=new hs(d.trim());t.readEntry(i,d=t.readLine())!=0;){let a=s[i[0]];a&&a(l)}this.pages.push(l)}}findRegion(e){for(let t=0;t<this.regions.length;t++)if(this.regions[t].name==e)return this.regions[t];return null}setTextures(e,t=""){for(let i of this.pages)i.setTexture(e.get(t+i.name))}dispose(){for(let e=0;e<this.pages.length;e++)this.pages[e].texture?.dispose()}},$s=class{lines;index=0;constructor(e){this.lines=e.split(/\r\n|\r|\n/)}readLine(){return this.index>=this.lines.length?null:this.lines[this.index++]}readEntry(e,t){if(!t||(t=t.trim(),t.length==0))return 0;let i=t.indexOf(":");if(i==-1)return 0;e[0]=t.substr(0,i).trim();for(let s=1,n=i+1;;s++){let d=t.indexOf(",",n);if(d==-1)return e[s]=t.substr(n).trim(),s;if(e[s]=t.substr(n,d-n).trim(),n=d+1,s==4)return 4}}},hs=class{name;minFilter=9728;magFilter=9728;uWrap=33071;vWrap=33071;texture=null;width=0;height=0;pma=!1;regions=new Array;constructor(e){this.name=e}setTexture(e){this.texture=e,e.setFilters(this.minFilter,this.magFilter),e.setWraps(this.uWrap,this.vWrap);for(let t of this.regions)t.texture=e}},wi=class extends ls{page;name;x=0;y=0;offsetX=0;offsetY=0;originalWidth=0;originalHeight=0;index=0;degrees=0;names=null;values=null;constructor(e,t){super(),this.page=e,this.name=t,e.regions.push(this)}},Ue=class extends ke{region=null;path;regionUVs=[];uvs=[];triangles=[];color=new D(1,1,1,1);width=0;height=0;hullLength=0;edges=[];parentMesh=null;sequence=null;tempColor=new D(0,0,0,0);constructor(e,t){super(e),this.path=t}updateRegion(){if(!this.region)throw new Error("Region not set.");let e=this.regionUVs;(!this.uvs||this.uvs.length!=e.length)&&(this.uvs=B.newFloatArray(e.length));let t=this.uvs,i=this.uvs.length,s=this.region.u,n=this.region.v,d=0,l=0;if(this.region instanceof wi){let r=this.region,h=r.page.texture.getImage(),a=h.width,o=h.height;switch(r.degrees){case 90:s-=(r.originalHeight-r.offsetY-r.height)/a,n-=(r.originalWidth-r.offsetX-r.width)/o,d=r.originalHeight/a,l=r.originalWidth/o;for(let c=0;c<i;c+=2)t[c]=s+e[c+1]*d,t[c+1]=n+(1-e[c])*l;return;case 180:s-=(r.originalWidth-r.offsetX-r.width)/a,n-=r.offsetY/o,d=r.originalWidth/a,l=r.originalHeight/o;for(let c=0;c<i;c+=2)t[c]=s+(1-e[c])*d,t[c+1]=n+(1-e[c+1])*l;return;case 270:s-=r.offsetY/a,n-=r.offsetX/o,d=r.originalHeight/a,l=r.originalWidth/o;for(let c=0;c<i;c+=2)t[c]=s+(1-e[c+1])*d,t[c+1]=n+e[c]*l;return}s-=r.offsetX/a,n-=(r.originalHeight-r.offsetY-r.height)/o,d=r.originalWidth/a,l=r.originalHeight/o}else this.region?(d=this.region.u2-s,l=this.region.v2-n):(s=n=0,d=l=1);for(let r=0;r<i;r+=2)t[r]=s+e[r]*d,t[r+1]=n+e[r+1]*l}getParentMesh(){return this.parentMesh}setParentMesh(e){this.parentMesh=e,e&&(this.bones=e.bones,this.vertices=e.vertices,this.worldVerticesLength=e.worldVerticesLength,this.regionUVs=e.regionUVs,this.triangles=e.triangles,this.hullLength=e.hullLength,this.worldVerticesLength=e.worldVerticesLength)}copy(){if(this.parentMesh)return this.newLinkedMesh();let e=new Ue(this.name,this.path);return e.region=this.region,e.color.setFromColor(this.color),this.copyTo(e),e.regionUVs=new Array(this.regionUVs.length),B.arrayCopy(this.regionUVs,0,e.regionUVs,0,this.regionUVs.length),e.uvs=new Array(this.uvs.length),B.arrayCopy(this.uvs,0,e.uvs,0,this.uvs.length),e.triangles=new Array(this.triangles.length),B.arrayCopy(this.triangles,0,e.triangles,0,this.triangles.length),e.hullLength=this.hullLength,e.sequence=this.sequence!=null?this.sequence.copy():null,this.edges&&(e.edges=new Array(this.edges.length),B.arrayCopy(this.edges,0,e.edges,0,this.edges.length)),e.width=this.width,e.height=this.height,e}computeWorldVertices(e,t,i,s,n,d){this.sequence!=null&&this.sequence.apply(e,this),super.computeWorldVertices(e,t,i,s,n,d)}newLinkedMesh(){let e=new Ue(this.name,this.path);return e.region=this.region,e.color.setFromColor(this.color),e.timelineAttachment=this.timelineAttachment,e.setParentMesh(this.parentMesh?this.parentMesh:this),e.region!=null&&e.updateRegion(),e}},je=class extends ke{lengths=[];closed=!1;constantSpeed=!1;color=new D(1,1,1,1);constructor(e){super(e)}copy(){let e=new je(this.name);return this.copyTo(e),e.lengths=new Array(this.lengths.length),B.arrayCopy(this.lengths,0,e.lengths,0,this.lengths.length),e.closed=closed,e.constantSpeed=this.constantSpeed,e.color.setFromColor(this.color),e}},xi=class extends ke{x=0;y=0;rotation=0;color=new D(.38,.94,0,1);constructor(e){super(e)}computeWorldPosition(e,t){return t.x=this.x*e.a+this.y*e.b+e.worldX,t.y=this.x*e.c+this.y*e.d+e.worldY,t}computeWorldRotation(e){let t=F.cosDeg(this.rotation),i=F.sinDeg(this.rotation),s=t*e.a+i*e.b,n=t*e.c+i*e.d;return Math.atan2(n,s)*F.radDeg}copy(){let e=new xi(this.name);return e.x=this.x,e.y=this.y,e.rotation=this.rotation,e.color.setFromColor(this.color),e}},ds=class extends _t{x=0;y=0;scaleX=1;scaleY=1;rotation=0;width=0;height=0;color=new D(1,1,1,1);path;region=null;sequence=null;offset=B.newFloatArray(8);uvs=B.newFloatArray(8);tempColor=new D(1,1,1,1);constructor(e,t){super(e),this.path=t}updateRegion(){if(!this.region)throw new Error("Region not set.");let e=this.region,t=this.uvs;if(e==null){t[0]=0,t[1]=0,t[2]=0,t[3]=1,t[4]=1,t[5]=1,t[6]=1,t[7]=0;return}let i=this.width/this.region.originalWidth*this.scaleX,s=this.height/this.region.originalHeight*this.scaleY,n=-this.width/2*this.scaleX+this.region.offsetX*i,d=-this.height/2*this.scaleY+this.region.offsetY*s,l=n+this.region.width*i,r=d+this.region.height*s,h=this.rotation*Math.PI/180,a=Math.cos(h),o=Math.sin(h),c=this.x,u=this.y,f=n*a+c,m=n*o,g=d*a+u,x=d*o,v=l*a+c,p=l*o,w=r*a+u,b=r*o,y=this.offset;y[0]=f-x,y[1]=g+m,y[2]=f-b,y[3]=w+m,y[4]=v-b,y[5]=w+p,y[6]=v-x,y[7]=g+p,e.degrees==90?(t[0]=e.u2,t[1]=e.v2,t[2]=e.u,t[3]=e.v2,t[4]=e.u,t[5]=e.v,t[6]=e.u2,t[7]=e.v):(t[0]=e.u,t[1]=e.v2,t[2]=e.u,t[3]=e.v,t[4]=e.u2,t[5]=e.v,t[6]=e.u2,t[7]=e.v2)}computeWorldVertices(e,t,i,s){this.sequence!=null&&this.sequence.apply(e,this);let n=e.bone,d=this.offset,l=n.worldX,r=n.worldY,h=n.a,a=n.b,o=n.c,c=n.d,u=0,f=0;u=d[0],f=d[1],t[i]=u*h+f*a+l,t[i+1]=u*o+f*c+r,i+=s,u=d[2],f=d[3],t[i]=u*h+f*a+l,t[i+1]=u*o+f*c+r,i+=s,u=d[4],f=d[5],t[i]=u*h+f*a+l,t[i+1]=u*o+f*c+r,i+=s,u=d[6],f=d[7],t[i]=u*h+f*a+l,t[i+1]=u*o+f*c+r}copy(){let e=new ds(this.name,this.path);return e.region=this.region,e.x=this.x,e.y=this.y,e.scaleX=this.scaleX,e.scaleY=this.scaleY,e.rotation=this.rotation,e.width=this.width,e.height=this.height,B.arrayCopy(this.uvs,0,e.uvs,0,8),B.arrayCopy(this.offset,0,e.offset,0,8),e.color.setFromColor(this.color),e.sequence=this.sequence!=null?this.sequence.copy():null,e}},ae=ds;P(ae,"X1",0),P(ae,"Y1",1),P(ae,"C1R",2),P(ae,"C1G",3),P(ae,"C1B",4),P(ae,"C1A",5),P(ae,"U1",6),P(ae,"V1",7),P(ae,"X2",8),P(ae,"Y2",9),P(ae,"C2R",10),P(ae,"C2G",11),P(ae,"C2B",12),P(ae,"C2A",13),P(ae,"U2",14),P(ae,"V2",15),P(ae,"X3",16),P(ae,"Y3",17),P(ae,"C3R",18),P(ae,"C3G",19),P(ae,"C3B",20),P(ae,"C3A",21),P(ae,"U3",22),P(ae,"V3",23),P(ae,"X4",24),P(ae,"Y4",25),P(ae,"C4R",26),P(ae,"C4G",27),P(ae,"C4B",28),P(ae,"C4A",29),P(ae,"U4",30),P(ae,"V4",31);var bi=class{atlas;constructor(e){this.atlas=e}loadSequence(e,t,i){let s=i.regions;for(let n=0,d=s.length;n<d;n++){let l=i.getPath(t,n),r=this.atlas.findRegion(l);if(r==null)throw new Error("Region not found in atlas: "+l+" (sequence: "+e+")");s[n]=r}}newRegionAttachment(e,t,i,s){let n=new ae(t,i);if(s!=null)this.loadSequence(t,i,s);else{let d=this.atlas.findRegion(i);if(!d)throw new Error("Region not found in atlas: "+i+" (region attachment: "+t+")");n.region=d}return n}newMeshAttachment(e,t,i,s){let n=new Ue(t,i);if(s!=null)this.loadSequence(t,i,s);else{let d=this.atlas.findRegion(i);if(!d)throw new Error("Region not found in atlas: "+i+" (mesh attachment: "+t+")");n.region=d}return n}newBoundingBoxAttachment(e,t){return new Ct(t)}newPathAttachment(e,t){return new je(t)}newPointAttachment(e,t){return new xi(t)}newClippingAttachment(e,t){return new dt(t)}},vi=class{index=0;name;parent=null;length=0;x=0;y=0;rotation=0;scaleX=1;scaleY=1;shearX=0;shearY=0;transformMode=Tt.Normal;skinRequired=!1;color=new D;constructor(e,t,i){if(e<0)throw new Error("index must be >= 0.");if(!t)throw new Error("name cannot be null.");this.index=e,this.name=t,this.parent=i}},Tt=(e=>(e[e.Normal=0]="Normal",e[e.OnlyTranslation=1]="OnlyTranslation",e[e.NoRotationOrReflection=2]="NoRotationOrReflection",e[e.NoScale=3]="NoScale",e[e.NoScaleOrReflection=4]="NoScaleOrReflection",e))(Tt||{}),yi=class{data;skeleton;parent=null;children=new Array;x=0;y=0;rotation=0;scaleX=0;scaleY=0;shearX=0;shearY=0;ax=0;ay=0;arotation=0;ascaleX=0;ascaleY=0;ashearX=0;ashearY=0;a=0;b=0;c=0;d=0;worldY=0;worldX=0;sorted=!1;active=!1;constructor(e,t,i){if(!e)throw new Error("data cannot be null.");if(!t)throw new Error("skeleton cannot be null.");this.data=e,this.skeleton=t,this.parent=i,this.setToSetupPose()}isActive(){return this.active}update(){this.updateWorldTransformWith(this.ax,this.ay,this.arotation,this.ascaleX,this.ascaleY,this.ashearX,this.ashearY)}updateWorldTransform(){this.updateWorldTransformWith(this.x,this.y,this.rotation,this.scaleX,this.scaleY,this.shearX,this.shearY)}updateWorldTransformWith(e,t,i,s,n,d,l){this.ax=e,this.ay=t,this.arotation=i,this.ascaleX=s,this.ascaleY=n,this.ashearX=d,this.ashearY=l;let r=this.parent;if(!r){let u=this.skeleton,f=i+90+l,m=u.scaleX,g=u.scaleY;this.a=F.cosDeg(i+d)*s*m,this.b=F.cosDeg(f)*n*m,this.c=F.sinDeg(i+d)*s*g,this.d=F.sinDeg(f)*n*g,this.worldX=e*m+u.x,this.worldY=t*g+u.y;return}let h=r.a,a=r.b,o=r.c,c=r.d;switch(this.worldX=h*e+a*t+r.worldX,this.worldY=o*e+c*t+r.worldY,this.data.transformMode){case 0:{let u=i+90+l,f=F.cosDeg(i+d)*s,m=F.cosDeg(u)*n,g=F.sinDeg(i+d)*s,x=F.sinDeg(u)*n;this.a=h*f+a*g,this.b=h*m+a*x,this.c=o*f+c*g,this.d=o*m+c*x;return}case 1:{let u=i+90+l;this.a=F.cosDeg(i+d)*s,this.b=F.cosDeg(u)*n,this.c=F.sinDeg(i+d)*s,this.d=F.sinDeg(u)*n;break}case 2:{let u=h*h+o*o,f=0;u>1e-4?(u=Math.abs(h*c-a*o)/u,h/=this.skeleton.scaleX,o/=this.skeleton.scaleY,a=o*u,c=h*u,f=Math.atan2(o,h)*F.radDeg):(h=0,o=0,f=90-Math.atan2(c,a)*F.radDeg);let m=i+d-f,g=i+l-f+90,x=F.cosDeg(m)*s,v=F.cosDeg(g)*n,p=F.sinDeg(m)*s,w=F.sinDeg(g)*n;this.a=h*x-a*p,this.b=h*v-a*w,this.c=o*x+c*p,this.d=o*v+c*w;break}case 3:case 4:{let u=F.cosDeg(i),f=F.sinDeg(i),m=(h*u+a*f)/this.skeleton.scaleX,g=(o*u+c*f)/this.skeleton.scaleY,x=Math.sqrt(m*m+g*g);x>1e-5&&(x=1/x),m*=x,g*=x,x=Math.sqrt(m*m+g*g),this.data.transformMode==3&&h*c-a*o<0!=(this.skeleton.scaleX<0!=this.skeleton.scaleY<0)&&(x=-x);let v=Math.PI/2+Math.atan2(g,m),p=Math.cos(v)*x,w=Math.sin(v)*x,b=F.cosDeg(d)*s,y=F.cosDeg(90+l)*n,C=F.sinDeg(d)*s,A=F.sinDeg(90+l)*n;this.a=m*b+p*C,this.b=m*y+p*A,this.c=g*b+w*C,this.d=g*y+w*A;break}}this.a*=this.skeleton.scaleX,this.b*=this.skeleton.scaleX,this.c*=this.skeleton.scaleY,this.d*=this.skeleton.scaleY}setToSetupPose(){let e=this.data;this.x=e.x,this.y=e.y,this.rotation=e.rotation,this.scaleX=e.scaleX,this.scaleY=e.scaleY,this.shearX=e.shearX,this.shearY=e.shearY}getWorldRotationX(){return Math.atan2(this.c,this.a)*F.radDeg}getWorldRotationY(){return Math.atan2(this.d,this.b)*F.radDeg}getWorldScaleX(){return Math.sqrt(this.a*this.a+this.c*this.c)}getWorldScaleY(){return Math.sqrt(this.b*this.b+this.d*this.d)}updateAppliedTransform(){let e=this.parent;if(!e){this.ax=this.worldX-this.skeleton.x,this.ay=this.worldY-this.skeleton.y,this.arotation=Math.atan2(this.c,this.a)*F.radDeg,this.ascaleX=Math.sqrt(this.a*this.a+this.c*this.c),this.ascaleY=Math.sqrt(this.b*this.b+this.d*this.d),this.ashearX=0,this.ashearY=Math.atan2(this.a*this.b+this.c*this.d,this.a*this.d-this.b*this.c)*F.radDeg;return}let t=e.a,i=e.b,s=e.c,n=e.d,d=1/(t*n-i*s),l=this.worldX-e.worldX,r=this.worldY-e.worldY;this.ax=l*n*d-r*i*d,this.ay=r*t*d-l*s*d;let h=d*n,a=d*t,o=d*i,c=d*s,u=h*this.a-o*this.c,f=h*this.b-o*this.d,m=a*this.c-c*this.a,g=a*this.d-c*this.b;if(this.ashearX=0,this.ascaleX=Math.sqrt(u*u+m*m),this.ascaleX>1e-4){let x=u*g-f*m;this.ascaleY=x/this.ascaleX,this.ashearY=Math.atan2(u*f+m*g,x)*F.radDeg,this.arotation=Math.atan2(m,u)*F.radDeg}else this.ascaleX=0,this.ascaleY=Math.sqrt(f*f+g*g),this.ashearY=0,this.arotation=90-Math.atan2(g,f)*F.radDeg}worldToLocal(e){let t=1/(this.a*this.d-this.b*this.c),i=e.x-this.worldX,s=e.y-this.worldY;return e.x=i*this.d*t-s*this.b*t,e.y=s*this.a*t-i*this.c*t,e}localToWorld(e){let t=e.x,i=e.y;return e.x=t*this.a+i*this.b+this.worldX,e.y=t*this.c+i*this.d+this.worldY,e}worldToLocalRotation(e){let t=F.sinDeg(e),i=F.cosDeg(e);return Math.atan2(this.a*t-this.c*i,this.d*i-this.b*t)*F.radDeg+this.rotation-this.shearX}localToWorldRotation(e){e-=this.rotation-this.shearX;let t=F.sinDeg(e),i=F.cosDeg(e);return Math.atan2(i*this.c+t*this.d,i*this.a+t*this.b)*F.radDeg}rotateWorld(e){let t=this.a,i=this.b,s=this.c,n=this.d,d=F.cosDeg(e),l=F.sinDeg(e);this.a=d*t-l*s,this.b=d*i-l*n,this.c=l*t+d*s,this.d=l*i+d*n}},kt=class{constructor(e,t,i){this.name=e,this.order=t,this.skinRequired=i}},cs=class{pathPrefix="";textureLoader;downloader;assets={};errors={};toLoad=0;loaded=0;constructor(e,t="",i=new Ai){this.textureLoader=e,this.pathPrefix=t,this.downloader=i}start(e){return this.toLoad++,this.pathPrefix+e}success(e,t,i){this.toLoad--,this.loaded++,this.assets[t]=i,e&&e(t,i)}error(e,t,i){this.toLoad--,this.loaded++,this.errors[t]=i,e&&e(t,i)}loadAll(){return new Promise((t,i)=>{let s=()=>{if(this.isLoadingComplete()){this.hasErrors()?i(this.errors):t(this);return}requestAnimationFrame(s)};requestAnimationFrame(s)})}setRawDataURI(e,t){this.downloader.rawDataUris[this.pathPrefix+e]=t}loadBinary(e,t=()=>{},i=()=>{}){e=this.start(e),this.downloader.downloadBinary(e,s=>{this.success(t,e,s)},(s,n)=>{this.error(i,e,`Couldn't load binary ${e}: status ${s}, ${n}`)})}loadText(e,t=()=>{},i=()=>{}){e=this.start(e),this.downloader.downloadText(e,s=>{this.success(t,e,s)},(s,n)=>{this.error(i,e,`Couldn't load text ${e}: status ${s}, ${n}`)})}loadJson(e,t=()=>{},i=()=>{}){e=this.start(e),this.downloader.downloadJson(e,s=>{this.success(t,e,s)},(s,n)=>{this.error(i,e,`Couldn't load JSON ${e}: status ${s}, ${n}`)})}loadTexture(e,t=()=>{},i=()=>{}){if(e=this.start(e),!!!(typeof window<"u"&&typeof navigator<"u"&&window.document))fetch(e,{mode:"cors"}).then(d=>d.ok?d.blob():(this.error(i,e,`Couldn't load image: ${e}`),null)).then(d=>d?createImageBitmap(d,{premultiplyAlpha:"none",colorSpaceConversion:"none"}):null).then(d=>{d&&this.success(t,e,this.textureLoader(d))});else{let d=new Image;d.crossOrigin="anonymous",d.onload=()=>{this.success(t,e,this.textureLoader(d))},d.onerror=()=>{this.error(i,e,`Couldn't load image: ${e}`)},this.downloader.rawDataUris[e]&&(e=this.downloader.rawDataUris[e]),d.src=e}}loadTextureAtlas(e,t=()=>{},i=()=>{},s){let n=e.lastIndexOf("/"),d=n>=0?e.substring(0,n+1):"";e=this.start(e),this.downloader.downloadText(e,l=>{try{let r=new os(l),h=r.pages.length,a=!1;for(let o of r.pages)this.loadTexture(s?s[o.name]:d+o.name,(c,u)=>{a||(o.setTexture(u),--h==0&&this.success(t,e,r))},(c,u)=>{a||this.error(i,e,`Couldn't load texture atlas ${e} page image: ${c}`),a=!0})}catch(r){this.error(i,e,`Couldn't parse texture atlas ${e}: ${r.message}`)}},(l,r)=>{this.error(i,e,`Couldn't load texture atlas ${e}: status ${l}, ${r}`)})}get(e){return this.assets[this.pathPrefix+e]}require(e){e=this.pathPrefix+e;let t=this.assets[e];if(t)return t;let i=this.errors[e];throw Error("Asset not found: "+e+(i?`
`+i:""))}remove(e){e=this.pathPrefix+e;let t=this.assets[e];return t.dispose&&t.dispose(),delete this.assets[e],t}removeAll(){for(let e in this.assets){let t=this.assets[e];t.dispose&&t.dispose()}this.assets={}}isLoadingComplete(){return this.toLoad==0}getToLoad(){return this.toLoad}getLoaded(){return this.loaded}dispose(){this.removeAll()}hasErrors(){return Object.keys(this.errors).length>0}getErrors(){return this.errors}},Ai=class{callbacks={};rawDataUris={};dataUriToString(e){if(!e.startsWith("data:"))throw new Error("Not a data URI.");let t=e.indexOf("base64,");return t!=-1?(t+=7,atob(e.substr(t))):e.substr(e.indexOf(",")+1)}base64ToUint8Array(e){for(var t=window.atob(e),i=t.length,s=new Uint8Array(i),n=0;n<i;n++)s[n]=t.charCodeAt(n);return s}dataUriToUint8Array(e){if(!e.startsWith("data:"))throw new Error("Not a data URI.");let t=e.indexOf("base64,");if(t==-1)throw new Error("Not a binary data URI.");return t+=7,this.base64ToUint8Array(e.substr(t))}downloadText(e,t,i){if(this.start(e,t,i))return;if(this.rawDataUris[e]){try{let d=this.rawDataUris[e];this.finish(e,200,this.dataUriToString(d))}catch(d){this.finish(e,400,JSON.stringify(d))}return}let s=new XMLHttpRequest;s.overrideMimeType("text/html"),s.open("GET",e,!0);let n=()=>{this.finish(e,s.status,s.responseText)};s.onload=n,s.onerror=n,s.send()}downloadJson(e,t,i){this.downloadText(e,s=>{t(JSON.parse(s))},i)}downloadBinary(e,t,i){if(this.start(e,t,i))return;if(this.rawDataUris[e]){try{let d=this.rawDataUris[e];this.finish(e,200,this.dataUriToUint8Array(d))}catch(d){this.finish(e,400,JSON.stringify(d))}return}let s=new XMLHttpRequest;s.open("GET",e,!0),s.responseType="arraybuffer";let n=()=>{this.finish(e,s.status,s.response)};s.onload=()=>{s.status==200||s.status==0?this.finish(e,200,new Uint8Array(s.response)):n()},s.onerror=n,s.send()}start(e,t,i){let s=this.callbacks[e];try{if(s)return!0;this.callbacks[e]=s=[]}finally{s.push(t,i)}}finish(e,t,i){let s=this.callbacks[e];delete this.callbacks[e];let n=t==200||t==0?[i]:[t,i];for(let d=n.length-1,l=s.length;d<l;d+=2)s[d].apply(null,n)}},Ci=class{data;intValue=0;floatValue=0;stringValue=null;time=0;volume=0;balance=0;constructor(e,t){if(!t)throw new Error("data cannot be null.");this.time=e,this.data=t}},Si=class{name;intValue=0;floatValue=0;stringValue=null;audioPath=null;volume=0;balance=0;constructor(e){this.name=e}},us=class{data;bones;target;bendDirection=0;compress=!1;stretch=!1;mix=1;softness=0;active=!1;constructor(e,t){if(!e)throw new Error("data cannot be null.");if(!t)throw new Error("skeleton cannot be null.");this.data=e,this.mix=e.mix,this.softness=e.softness,this.bendDirection=e.bendDirection,this.compress=e.compress,this.stretch=e.stretch,this.bones=new Array;for(let s=0;s<e.bones.length;s++){let n=t.findBone(e.bones[s].name);if(!n)throw new Error(`Couldn't find bone ${e.bones[s].name}`);this.bones.push(n)}let i=t.findBone(e.target.name);if(!i)throw new Error(`Couldn't find bone ${e.target.name}`);this.target=i}isActive(){return this.active}update(){if(this.mix==0)return;let e=this.target,t=this.bones;switch(t.length){case 1:this.apply1(t[0],e.worldX,e.worldY,this.compress,this.stretch,this.data.uniform,this.mix);break;case 2:this.apply2(t[0],t[1],e.worldX,e.worldY,this.bendDirection,this.stretch,this.data.uniform,this.softness,this.mix);break}}apply1(e,t,i,s,n,d,l){let r=e.parent;if(!r)throw new Error("IK bone must have parent.");let h=r.a,a=r.b,o=r.c,c=r.d,u=-e.ashearX-e.arotation,f=0,m=0;switch(e.data.transformMode){case 1:f=(t-e.worldX)*F.signum(e.skeleton.scaleX),m=(i-e.worldY)*F.signum(e.skeleton.scaleY);break;case 2:let v=Math.abs(h*c-a*o)/Math.max(1e-4,h*h+o*o),p=h/e.skeleton.scaleX,w=o/e.skeleton.scaleY;a=-w*v*e.skeleton.scaleX,c=p*v*e.skeleton.scaleY,u+=Math.atan2(w,p)*F.radDeg;default:let b=t-r.worldX,y=i-r.worldY,C=h*c-a*o;Math.abs(C)<=1e-4?(f=0,m=0):(f=(b*c-y*a)/C-e.ax,m=(y*h-b*o)/C-e.ay)}u+=Math.atan2(m,f)*F.radDeg,e.ascaleX<0&&(u+=180),u>180?u-=360:u<-180&&(u+=360);let g=e.ascaleX,x=e.ascaleY;if(s||n){switch(e.data.transformMode){case 3:case 4:f=t-e.worldX,m=i-e.worldY}let v=e.data.length*g,p=Math.sqrt(f*f+m*m);if(s&&p<v||n&&p>v&&v>1e-4){let w=(p/v-1)*l+1;g*=w,d&&(x*=w)}}e.updateWorldTransformWith(e.ax,e.ay,e.arotation+u*l,g,x,e.ashearX,e.ashearY)}apply2(e,t,i,s,n,d,l,r,h){let a=e.ax,o=e.ay,c=e.ascaleX,u=e.ascaleY,f=c,m=u,g=t.ascaleX,x=0,v=0,p=0;c<0?(c=-c,x=180,p=-1):(x=0,p=1),u<0&&(u=-u,p=-p),g<0?(g=-g,v=180):v=0;let w=t.ax,b=0,y=0,C=0,A=e.a,T=e.b,M=e.c,Y=e.d,X=Math.abs(c-u)<=1e-4;!X||d?(b=0,y=A*w+e.worldX,C=M*w+e.worldY):(b=t.ay,y=A*w+T*b+e.worldX,C=M*w+Y*b+e.worldY);let L=e.parent;if(!L)throw new Error("IK parent must itself have a parent.");A=L.a,T=L.b,M=L.c,Y=L.d;let R=A*Y-T*M,I=y-L.worldX,k=C-L.worldY;R=Math.abs(R)<=1e-4?0:1/R;let _=(I*Y-k*T)*R-a,se=(k*A-I*M)*R-o,te=Math.sqrt(_*_+se*se),oe=t.data.length*g,re,de;if(te<1e-4){this.apply1(e,i,s,!1,d,!1,h),t.updateWorldTransformWith(w,b,0,t.ascaleX,t.ascaleY,t.ashearX,t.ashearY);return}I=i-L.worldX,k=s-L.worldY;let ne=(I*Y-k*T)*R-a,ie=(k*A-I*M)*R-o,ue=ne*ne+ie*ie;if(r!=0){r*=c*(g+1)*.5;let we=Math.sqrt(ue),Ce=we-te-oe*c+r;if(Ce>0){let Se=Math.min(1,Ce/(r*2))-1;Se=(Ce-r*(1-Se*Se))/we,ne-=Se*ne,ie-=Se*ie,ue=ne*ne+ie*ie}}e:if(X){oe*=c;let we=(ue-te*te-oe*oe)/(2*te*oe);we<-1?(we=-1,de=Math.PI*n):we>1?(we=1,de=0,d&&(A=(Math.sqrt(ue)/(te+oe)-1)*h+1,f*=A,l&&(m*=A))):de=Math.acos(we)*n,A=te+oe*we,T=oe*Math.sin(de),re=Math.atan2(ie*A-ne*T,ne*A+ie*T)}else{A=c*oe,T=u*oe;let we=A*A,Ce=T*T,Se=Math.atan2(ie,ne);M=Ce*te*te+we*ue-we*Ce;let _e=-2*Ce*te,rt=Ce-we;if(Y=_e*_e-4*rt*M,Y>=0){let nt=Math.sqrt(Y);_e<0&&(nt=-nt),nt=-(_e+nt)*.5;let Vs=nt/rt,Os=M/nt,at=Math.abs(Vs)<Math.abs(Os)?Vs:Os;if(at*at<=ue){k=Math.sqrt(ue-at*at)*n,re=Se-Math.atan2(k,at),de=Math.atan2(k/u,(at-te)/c);break e}}let mt=F.PI,Ut=te-A,Wi=Ut*Ut,Bs=0,Ps=0,zt=te+A,_i=zt*zt,Ds=0;M=-A*te/(we-Ce),M>=-1&&M<=1&&(M=Math.acos(M),I=A*Math.cos(M)+te,k=T*Math.sin(M),Y=I*I+k*k,Y<Wi&&(mt=M,Wi=Y,Ut=I,Bs=k),Y>_i&&(Ps=M,_i=Y,zt=I,Ds=k)),ue<=(Wi+_i)*.5?(re=Se-Math.atan2(Bs*n,Ut),de=mt*n):(re=Se-Math.atan2(Ds*n,zt),de=Ps*n)}let xe=Math.atan2(b,w)*p,Fe=e.arotation;re=(re-xe)*F.radDeg+x-Fe,re>180?re-=360:re<-180&&(re+=360),e.updateWorldTransformWith(a,o,Fe+re*h,f,m,0,0),Fe=t.arotation,de=((de+xe)*F.radDeg-t.ashearX)*p+v-Fe,de>180?de-=360:de<-180&&(de+=360),t.updateWorldTransformWith(w,b,Fe+de*h,t.ascaleX,t.ascaleY,t.ashearX,t.ashearY)}},Ti=class extends kt{bones=new Array;_target=null;set target(e){this._target=e}get target(){if(this._target)return this._target;throw new Error("BoneData not set.")}bendDirection=1;compress=!1;stretch=!1;uniform=!1;mix=1;softness=0;constructor(e){super(e,0,!1)}},ki=class extends kt{bones=new Array;_target=null;set target(e){this._target=e}get target(){if(this._target)return this._target;throw new Error("SlotData not set.")}positionMode=Et.Fixed;spacingMode=It.Fixed;rotateMode=Mt.Chain;offsetRotation=0;position=0;spacing=0;mixRotate=0;mixX=0;mixY=0;constructor(e){super(e,0,!1)}},Et=(e=>(e[e.Fixed=0]="Fixed",e[e.Percent=1]="Percent",e))(Et||{}),It=(e=>(e[e.Length=0]="Length",e[e.Fixed=1]="Fixed",e[e.Percent=2]="Percent",e[e.Proportional=3]="Proportional",e))(It||{}),Mt=(e=>(e[e.Tangent=0]="Tangent",e[e.Chain=1]="Chain",e[e.ChainScale=2]="ChainScale",e))(Mt||{}),Be=class{data;bones;target;position=0;spacing=0;mixRotate=0;mixX=0;mixY=0;spaces=new Array;positions=new Array;world=new Array;curves=new Array;lengths=new Array;segments=new Array;active=!1;constructor(e,t){if(!e)throw new Error("data cannot be null.");if(!t)throw new Error("skeleton cannot be null.");this.data=e,this.bones=new Array;for(let s=0,n=e.bones.length;s<n;s++){let d=t.findBone(e.bones[s].name);if(!d)throw new Error(`Couldn't find bone ${e.bones[s].name}.`);this.bones.push(d)}let i=t.findSlot(e.target.name);if(!i)throw new Error(`Couldn't find target bone ${e.target.name}`);this.target=i,this.position=e.position,this.spacing=e.spacing,this.mixRotate=e.mixRotate,this.mixX=e.mixX,this.mixY=e.mixY}isActive(){return this.active}update(){let e=this.target.getAttachment();if(!(e instanceof je))return;let t=this.mixRotate,i=this.mixX,s=this.mixY;if(t==0&&i==0&&s==0)return;let n=this.data,d=n.rotateMode==0,l=n.rotateMode==2,r=this.bones,h=r.length,a=d?h:h+1,o=B.setArraySize(this.spaces,a),c=l?this.lengths=B.setArraySize(this.lengths,h):[],u=this.spacing;switch(n.spacingMode){case 2:if(l)for(let b=0,y=a-1;b<y;b++){let C=r[b],A=C.data.length;if(A<Be.epsilon)c[b]=0;else{let T=A*C.a,M=A*C.c;c[b]=Math.sqrt(T*T+M*M)}}B.arrayFill(o,1,a,u);break;case 3:let p=0;for(let b=0,y=a-1;b<y;){let C=r[b],A=C.data.length;if(A<Be.epsilon)l&&(c[b]=0),o[++b]=u;else{let T=A*C.a,M=A*C.c,Y=Math.sqrt(T*T+M*M);l&&(c[b]=Y),o[++b]=Y,p+=Y}}if(p>0){p=a/p*u;for(let b=1;b<a;b++)o[b]*=p}break;default:let w=n.spacingMode==0;for(let b=0,y=a-1;b<y;){let C=r[b],A=C.data.length;if(A<Be.epsilon)l&&(c[b]=0),o[++b]=u;else{let T=A*C.a,M=A*C.c,Y=Math.sqrt(T*T+M*M);l&&(c[b]=Y),o[++b]=(w?A+u:u)*Y/A}}}let f=this.computeWorldPositions(e,a,d),m=f[0],g=f[1],x=n.offsetRotation,v=!1;if(x==0)v=n.rotateMode==1;else{v=!1;let p=this.target.bone;x*=p.a*p.d-p.b*p.c>0?F.degRad:-F.degRad}for(let p=0,w=3;p<h;p++,w+=3){let b=r[p];b.worldX+=(m-b.worldX)*i,b.worldY+=(g-b.worldY)*s;let y=f[w],C=f[w+1],A=y-m,T=C-g;if(l){let M=c[p];if(M!=0){let Y=(Math.sqrt(A*A+T*T)/M-1)*t+1;b.a*=Y,b.c*=Y}}if(m=y,g=C,t>0){let M=b.a,Y=b.b,X=b.c,L=b.d,R=0,I=0,k=0;if(d?R=f[w-1]:o[p+1]==0?R=f[w+2]:R=Math.atan2(T,A),R-=Math.atan2(X,M),v){I=Math.cos(R),k=Math.sin(R);let _=b.data.length;m+=(_*(I*M-k*X)-A)*t,g+=(_*(k*M+I*X)-T)*t}else R+=x;R>F.PI?R-=F.PI2:R<-F.PI&&(R+=F.PI2),R*=t,I=Math.cos(R),k=Math.sin(R),b.a=I*M-k*X,b.b=I*Y-k*L,b.c=k*M+I*X,b.d=k*Y+I*L}b.updateAppliedTransform()}}computeWorldPositions(e,t,i){let s=this.target,n=this.position,d=this.spaces,l=B.setArraySize(this.positions,t*3+2),r=this.world,h=e.closed,a=e.worldVerticesLength,o=a/6,c=Be.NONE;if(!e.constantSpeed){let se=e.lengths;o-=h?1:2;let te=se[o];this.data.positionMode==1&&(n*=te);let oe;switch(this.data.spacingMode){case 2:oe=te;break;case 3:oe=te/t;break;default:oe=1}r=B.setArraySize(this.world,8);for(let re=0,de=0,ne=0;re<t;re++,de+=3){let ie=d[re]*oe;n+=ie;let ue=n;if(h)ue%=te,ue<0&&(ue+=te),ne=0;else if(ue<0){c!=Be.BEFORE&&(c=Be.BEFORE,e.computeWorldVertices(s,2,4,r,0,2)),this.addBeforePosition(ue,r,0,l,de);continue}else if(ue>te){c!=Be.AFTER&&(c=Be.AFTER,e.computeWorldVertices(s,a-6,4,r,0,2)),this.addAfterPosition(ue-te,r,0,l,de);continue}for(;;ne++){let xe=se[ne];if(!(ue>xe)){if(ne==0)ue/=xe;else{let Fe=se[ne-1];ue=(ue-Fe)/(xe-Fe)}break}}ne!=c&&(c=ne,h&&ne==o?(e.computeWorldVertices(s,a-4,4,r,0,2),e.computeWorldVertices(s,0,4,r,4,2)):e.computeWorldVertices(s,ne*6+2,8,r,0,2)),this.addCurvePosition(ue,r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],l,de,i||re>0&&ie==0)}return l}h?(a+=2,r=B.setArraySize(this.world,a),e.computeWorldVertices(s,2,a-4,r,0,2),e.computeWorldVertices(s,0,2,r,a-4,2),r[a-2]=r[0],r[a-1]=r[1]):(o--,a-=4,r=B.setArraySize(this.world,a),e.computeWorldVertices(s,2,a,r,0,2));let u=B.setArraySize(this.curves,o),f=0,m=r[0],g=r[1],x=0,v=0,p=0,w=0,b=0,y=0,C=0,A=0,T=0,M=0,Y=0,X=0,L=0,R=0;for(let se=0,te=2;se<o;se++,te+=6)x=r[te],v=r[te+1],p=r[te+2],w=r[te+3],b=r[te+4],y=r[te+5],C=(m-x*2+p)*.1875,A=(g-v*2+w)*.1875,T=((x-p)*3-m+b)*.09375,M=((v-w)*3-g+y)*.09375,Y=C*2+T,X=A*2+M,L=(x-m)*.75+C+T*.16666667,R=(v-g)*.75+A+M*.16666667,f+=Math.sqrt(L*L+R*R),L+=Y,R+=X,Y+=T,X+=M,f+=Math.sqrt(L*L+R*R),L+=Y,R+=X,f+=Math.sqrt(L*L+R*R),L+=Y+T,R+=X+M,f+=Math.sqrt(L*L+R*R),u[se]=f,m=b,g=y;this.data.positionMode==1&&(n*=f);let I;switch(this.data.spacingMode){case 2:I=f;break;case 3:I=f/t;break;default:I=1}let k=this.segments,_=0;for(let se=0,te=0,oe=0,re=0;se<t;se++,te+=3){let de=d[se]*I;n+=de;let ne=n;if(h)ne%=f,ne<0&&(ne+=f),oe=0;else if(ne<0){this.addBeforePosition(ne,r,0,l,te);continue}else if(ne>f){this.addAfterPosition(ne-f,r,a-4,l,te);continue}for(;;oe++){let ie=u[oe];if(!(ne>ie)){if(oe==0)ne/=ie;else{let ue=u[oe-1];ne=(ne-ue)/(ie-ue)}break}}if(oe!=c){c=oe;let ie=oe*6;for(m=r[ie],g=r[ie+1],x=r[ie+2],v=r[ie+3],p=r[ie+4],w=r[ie+5],b=r[ie+6],y=r[ie+7],C=(m-x*2+p)*.03,A=(g-v*2+w)*.03,T=((x-p)*3-m+b)*.006,M=((v-w)*3-g+y)*.006,Y=C*2+T,X=A*2+M,L=(x-m)*.3+C+T*.16666667,R=(v-g)*.3+A+M*.16666667,_=Math.sqrt(L*L+R*R),k[0]=_,ie=1;ie<8;ie++)L+=Y,R+=X,Y+=T,X+=M,_+=Math.sqrt(L*L+R*R),k[ie]=_;L+=Y,R+=X,_+=Math.sqrt(L*L+R*R),k[8]=_,L+=Y+T,R+=X+M,_+=Math.sqrt(L*L+R*R),k[9]=_,re=0}for(ne*=_;;re++){let ie=k[re];if(!(ne>ie)){if(re==0)ne/=ie;else{let ue=k[re-1];ne=re+(ne-ue)/(ie-ue)}break}}this.addCurvePosition(ne*.1,m,g,x,v,p,w,b,y,l,te,i||se>0&&de==0)}return l}addBeforePosition(e,t,i,s,n){let d=t[i],l=t[i+1],r=t[i+2]-d,h=t[i+3]-l,a=Math.atan2(h,r);s[n]=d+e*Math.cos(a),s[n+1]=l+e*Math.sin(a),s[n+2]=a}addAfterPosition(e,t,i,s,n){let d=t[i+2],l=t[i+3],r=d-t[i],h=l-t[i+1],a=Math.atan2(h,r);s[n]=d+e*Math.cos(a),s[n+1]=l+e*Math.sin(a),s[n+2]=a}addCurvePosition(e,t,i,s,n,d,l,r,h,a,o,c){if(e==0||isNaN(e)){a[o]=t,a[o+1]=i,a[o+2]=Math.atan2(n-i,s-t);return}let u=e*e,f=u*e,m=1-e,g=m*m,x=g*m,v=m*e,p=v*3,w=m*p,b=p*e,y=t*x+s*w+d*b+r*f,C=i*x+n*w+l*b+h*f;a[o]=y,a[o+1]=C,c&&(e<.001?a[o+2]=Math.atan2(n-i,s-t):a[o+2]=Math.atan2(C-(i*g+n*v*2+l*u),y-(t*g+s*v*2+d*u)))}},et=Be;P(et,"NONE",-1),P(et,"BEFORE",-2),P(et,"AFTER",-3),P(et,"epsilon",1e-5);var fs=class{data;bone;color;darkColor=null;attachment=null;attachmentState=0;sequenceIndex=-1;deform=new Array;constructor(e,t){if(!e)throw new Error("data cannot be null.");if(!t)throw new Error("bone cannot be null.");this.data=e,this.bone=t,this.color=new D,this.darkColor=e.darkColor?new D:null,this.setToSetupPose()}getSkeleton(){return this.bone.skeleton}getAttachment(){return this.attachment}setAttachment(e){this.attachment!=e&&((!(e instanceof ke)||!(this.attachment instanceof ke)||e.timelineAttachment!=this.attachment.timelineAttachment)&&(this.deform.length=0),this.attachment=e,this.sequenceIndex=-1)}setToSetupPose(){this.color.setFromColor(this.data.color),this.darkColor&&this.darkColor.setFromColor(this.data.darkColor),this.data.attachmentName?(this.attachment=null,this.setAttachment(this.bone.skeleton.getAttachment(this.data.index,this.data.attachmentName))):this.attachment=null}},ms=class{data;bones;target;mixRotate=0;mixX=0;mixY=0;mixScaleX=0;mixScaleY=0;mixShearY=0;temp=new Te;active=!1;constructor(e,t){if(!e)throw new Error("data cannot be null.");if(!t)throw new Error("skeleton cannot be null.");this.data=e,this.mixRotate=e.mixRotate,this.mixX=e.mixX,this.mixY=e.mixY,this.mixScaleX=e.mixScaleX,this.mixScaleY=e.mixScaleY,this.mixShearY=e.mixShearY,this.bones=new Array;for(let s=0;s<e.bones.length;s++){let n=t.findBone(e.bones[s].name);if(!n)throw new Error(`Couldn't find bone ${e.bones[s].name}.`);this.bones.push(n)}let i=t.findBone(e.target.name);if(!i)throw new Error(`Couldn't find target bone ${e.target.name}.`);this.target=i}isActive(){return this.active}update(){this.mixRotate==0&&this.mixX==0&&this.mixY==0&&this.mixScaleX==0&&this.mixScaleY==0&&this.mixShearY==0||(this.data.local?this.data.relative?this.applyRelativeLocal():this.applyAbsoluteLocal():this.data.relative?this.applyRelativeWorld():this.applyAbsoluteWorld())}applyAbsoluteWorld(){let e=this.mixRotate,t=this.mixX,i=this.mixY,s=this.mixScaleX,n=this.mixScaleY,d=this.mixShearY,l=t!=0||i!=0,r=this.target,h=r.a,a=r.b,o=r.c,c=r.d,u=h*c-a*o>0?F.degRad:-F.degRad,f=this.data.offsetRotation*u,m=this.data.offsetShearY*u,g=this.bones;for(let x=0,v=g.length;x<v;x++){let p=g[x];if(e!=0){let w=p.a,b=p.b,y=p.c,C=p.d,A=Math.atan2(o,h)-Math.atan2(y,w)+f;A>F.PI?A-=F.PI2:A<-F.PI&&(A+=F.PI2),A*=e;let T=Math.cos(A),M=Math.sin(A);p.a=T*w-M*y,p.b=T*b-M*C,p.c=M*w+T*y,p.d=M*b+T*C}if(l){let w=this.temp;r.localToWorld(w.set(this.data.offsetX,this.data.offsetY)),p.worldX+=(w.x-p.worldX)*t,p.worldY+=(w.y-p.worldY)*i}if(s!=0){let w=Math.sqrt(p.a*p.a+p.c*p.c);w!=0&&(w=(w+(Math.sqrt(h*h+o*o)-w+this.data.offsetScaleX)*s)/w),p.a*=w,p.c*=w}if(n!=0){let w=Math.sqrt(p.b*p.b+p.d*p.d);w!=0&&(w=(w+(Math.sqrt(a*a+c*c)-w+this.data.offsetScaleY)*n)/w),p.b*=w,p.d*=w}if(d>0){let w=p.b,b=p.d,y=Math.atan2(b,w),C=Math.atan2(c,a)-Math.atan2(o,h)-(y-Math.atan2(p.c,p.a));C>F.PI?C-=F.PI2:C<-F.PI&&(C+=F.PI2),C=y+(C+m)*d;let A=Math.sqrt(w*w+b*b);p.b=Math.cos(C)*A,p.d=Math.sin(C)*A}p.updateAppliedTransform()}}applyRelativeWorld(){let e=this.mixRotate,t=this.mixX,i=this.mixY,s=this.mixScaleX,n=this.mixScaleY,d=this.mixShearY,l=t!=0||i!=0,r=this.target,h=r.a,a=r.b,o=r.c,c=r.d,u=h*c-a*o>0?F.degRad:-F.degRad,f=this.data.offsetRotation*u,m=this.data.offsetShearY*u,g=this.bones;for(let x=0,v=g.length;x<v;x++){let p=g[x];if(e!=0){let w=p.a,b=p.b,y=p.c,C=p.d,A=Math.atan2(o,h)+f;A>F.PI?A-=F.PI2:A<-F.PI&&(A+=F.PI2),A*=e;let T=Math.cos(A),M=Math.sin(A);p.a=T*w-M*y,p.b=T*b-M*C,p.c=M*w+T*y,p.d=M*b+T*C}if(l){let w=this.temp;r.localToWorld(w.set(this.data.offsetX,this.data.offsetY)),p.worldX+=w.x*t,p.worldY+=w.y*i}if(s!=0){let w=(Math.sqrt(h*h+o*o)-1+this.data.offsetScaleX)*s+1;p.a*=w,p.c*=w}if(n!=0){let w=(Math.sqrt(a*a+c*c)-1+this.data.offsetScaleY)*n+1;p.b*=w,p.d*=w}if(d>0){let w=Math.atan2(c,a)-Math.atan2(o,h);w>F.PI?w-=F.PI2:w<-F.PI&&(w+=F.PI2);let b=p.b,y=p.d;w=Math.atan2(y,b)+(w-F.PI/2+m)*d;let C=Math.sqrt(b*b+y*y);p.b=Math.cos(w)*C,p.d=Math.sin(w)*C}p.updateAppliedTransform()}}applyAbsoluteLocal(){let e=this.mixRotate,t=this.mixX,i=this.mixY,s=this.mixScaleX,n=this.mixScaleY,d=this.mixShearY,l=this.target,r=this.bones;for(let h=0,a=r.length;h<a;h++){let o=r[h],c=o.arotation;if(e!=0){let v=l.arotation-c+this.data.offsetRotation;v-=(16384-(16384.499999999996-v/360|0))*360,c+=v*e}let u=o.ax,f=o.ay;u+=(l.ax-u+this.data.offsetX)*t,f+=(l.ay-f+this.data.offsetY)*i;let m=o.ascaleX,g=o.ascaleY;s!=0&&m!=0&&(m=(m+(l.ascaleX-m+this.data.offsetScaleX)*s)/m),n!=0&&g!=0&&(g=(g+(l.ascaleY-g+this.data.offsetScaleY)*n)/g);let x=o.ashearY;if(d!=0){let v=l.ashearY-x+this.data.offsetShearY;v-=(16384-(16384.499999999996-v/360|0))*360,x+=v*d}o.updateWorldTransformWith(u,f,c,m,g,o.ashearX,x)}}applyRelativeLocal(){let e=this.mixRotate,t=this.mixX,i=this.mixY,s=this.mixScaleX,n=this.mixScaleY,d=this.mixShearY,l=this.target,r=this.bones;for(let h=0,a=r.length;h<a;h++){let o=r[h],c=o.arotation+(l.arotation+this.data.offsetRotation)*e,u=o.ax+(l.ax+this.data.offsetX)*t,f=o.ay+(l.ay+this.data.offsetY)*i,m=o.ascaleX*((l.ascaleX-1+this.data.offsetScaleX)*s+1),g=o.ascaleY*((l.ascaleY-1+this.data.offsetScaleY)*n+1),x=o.ashearY+(l.ashearY+this.data.offsetShearY)*d;o.updateWorldTransformWith(u,f,c,m,g,o.ashearX,x)}}},gs=class{data;bones;slots;drawOrder;ikConstraints;transformConstraints;pathConstraints;_updateCache=new Array;skin=null;color;scaleX=1;_scaleY=1;get scaleY(){return gs.yDown?-this._scaleY:this._scaleY}set scaleY(e){this._scaleY=e}x=0;y=0;constructor(e){if(!e)throw new Error("data cannot be null.");this.data=e,this.bones=new Array;for(let t=0;t<e.bones.length;t++){let i=e.bones[t],s;if(!i.parent)s=new yi(i,this,null);else{let n=this.bones[i.parent.index];s=new yi(i,this,n),n.children.push(s)}this.bones.push(s)}this.slots=new Array,this.drawOrder=new Array;for(let t=0;t<e.slots.length;t++){let i=e.slots[t],s=this.bones[i.boneData.index],n=new fs(i,s);this.slots.push(n),this.drawOrder.push(n)}this.ikConstraints=new Array;for(let t=0;t<e.ikConstraints.length;t++){let i=e.ikConstraints[t];this.ikConstraints.push(new us(i,this))}this.transformConstraints=new Array;for(let t=0;t<e.transformConstraints.length;t++){let i=e.transformConstraints[t];this.transformConstraints.push(new ms(i,this))}this.pathConstraints=new Array;for(let t=0;t<e.pathConstraints.length;t++){let i=e.pathConstraints[t];this.pathConstraints.push(new et(i,this))}this.color=new D(1,1,1,1),this.updateCache()}updateCache(){let e=this._updateCache;e.length=0;let t=this.bones;for(let a=0,o=t.length;a<o;a++){let c=t[a];c.sorted=c.data.skinRequired,c.active=!c.sorted}if(this.skin){let a=this.skin.bones;for(let o=0,c=this.skin.bones.length;o<c;o++){let u=this.bones[a[o].index];do u.sorted=!1,u.active=!0,u=u.parent;while(u)}}let i=this.ikConstraints,s=this.transformConstraints,n=this.pathConstraints,d=i.length,l=s.length,r=n.length,h=d+l+r;e:for(let a=0;a<h;a++){for(let o=0;o<d;o++){let c=i[o];if(c.data.order==a){this.sortIkConstraint(c);continue e}}for(let o=0;o<l;o++){let c=s[o];if(c.data.order==a){this.sortTransformConstraint(c);continue e}}for(let o=0;o<r;o++){let c=n[o];if(c.data.order==a){this.sortPathConstraint(c);continue e}}}for(let a=0,o=t.length;a<o;a++)this.sortBone(t[a])}sortIkConstraint(e){if(e.active=e.target.isActive()&&(!e.data.skinRequired||this.skin&&B.contains(this.skin.constraints,e.data,!0)),!e.active)return;let t=e.target;this.sortBone(t);let i=e.bones,s=i[0];if(this.sortBone(s),i.length==1)this._updateCache.push(e),this.sortReset(s.children);else{let n=i[i.length-1];this.sortBone(n),this._updateCache.push(e),this.sortReset(s.children),n.sorted=!0}}sortPathConstraint(e){if(e.active=e.target.bone.isActive()&&(!e.data.skinRequired||this.skin&&B.contains(this.skin.constraints,e.data,!0)),!e.active)return;let t=e.target,i=t.data.index,s=t.bone;this.skin&&this.sortPathConstraintAttachment(this.skin,i,s),this.data.defaultSkin&&this.data.defaultSkin!=this.skin&&this.sortPathConstraintAttachment(this.data.defaultSkin,i,s);for(let r=0,h=this.data.skins.length;r<h;r++)this.sortPathConstraintAttachment(this.data.skins[r],i,s);let n=t.getAttachment();n instanceof je&&this.sortPathConstraintAttachmentWith(n,s);let d=e.bones,l=d.length;for(let r=0;r<l;r++)this.sortBone(d[r]);this._updateCache.push(e);for(let r=0;r<l;r++)this.sortReset(d[r].children);for(let r=0;r<l;r++)d[r].sorted=!0}sortTransformConstraint(e){if(e.active=e.target.isActive()&&(!e.data.skinRequired||this.skin&&B.contains(this.skin.constraints,e.data,!0)),!e.active)return;this.sortBone(e.target);let t=e.bones,i=t.length;if(e.data.local)for(let s=0;s<i;s++){let n=t[s];this.sortBone(n.parent),this.sortBone(n)}else for(let s=0;s<i;s++)this.sortBone(t[s]);this._updateCache.push(e);for(let s=0;s<i;s++)this.sortReset(t[s].children);for(let s=0;s<i;s++)t[s].sorted=!0}sortPathConstraintAttachment(e,t,i){let s=e.attachments[t];if(s)for(let n in s)this.sortPathConstraintAttachmentWith(s[n],i)}sortPathConstraintAttachmentWith(e,t){if(!(e instanceof je))return;let i=e.bones;if(!i)this.sortBone(t);else{let s=this.bones;for(let n=0,d=i.length;n<d;){let l=i[n++];for(l+=n;n<l;)this.sortBone(s[i[n++]])}}}sortBone(e){if(!e||e.sorted)return;let t=e.parent;t&&this.sortBone(t),e.sorted=!0,this._updateCache.push(e)}sortReset(e){for(let t=0,i=e.length;t<i;t++){let s=e[t];s.active&&(s.sorted&&this.sortReset(s.children),s.sorted=!1)}}updateWorldTransform(){let e=this.bones;for(let i=0,s=e.length;i<s;i++){let n=e[i];n.ax=n.x,n.ay=n.y,n.arotation=n.rotation,n.ascaleX=n.scaleX,n.ascaleY=n.scaleY,n.ashearX=n.shearX,n.ashearY=n.shearY}let t=this._updateCache;for(let i=0,s=t.length;i<s;i++)t[i].update()}updateWorldTransformWith(e){let t=this.getRootBone();if(!t)throw new Error("Root bone must not be null.");let i=e.a,s=e.b,n=e.c,d=e.d;t.worldX=i*this.x+s*this.y+e.worldX,t.worldY=n*this.x+d*this.y+e.worldY;let l=t.rotation+90+t.shearY,r=F.cosDeg(t.rotation+t.shearX)*t.scaleX,h=F.cosDeg(l)*t.scaleY,a=F.sinDeg(t.rotation+t.shearX)*t.scaleX,o=F.sinDeg(l)*t.scaleY;t.a=(i*r+s*a)*this.scaleX,t.b=(i*h+s*o)*this.scaleX,t.c=(n*r+d*a)*this.scaleY,t.d=(n*h+d*o)*this.scaleY;let c=this._updateCache;for(let u=0,f=c.length;u<f;u++){let m=c[u];m!=t&&m.update()}}setToSetupPose(){this.setBonesToSetupPose(),this.setSlotsToSetupPose()}setBonesToSetupPose(){let e=this.bones;for(let n=0,d=e.length;n<d;n++)e[n].setToSetupPose();let t=this.ikConstraints;for(let n=0,d=t.length;n<d;n++){let l=t[n];l.mix=l.data.mix,l.softness=l.data.softness,l.bendDirection=l.data.bendDirection,l.compress=l.data.compress,l.stretch=l.data.stretch}let i=this.transformConstraints;for(let n=0,d=i.length;n<d;n++){let l=i[n],r=l.data;l.mixRotate=r.mixRotate,l.mixX=r.mixX,l.mixY=r.mixY,l.mixScaleX=r.mixScaleX,l.mixScaleY=r.mixScaleY,l.mixShearY=r.mixShearY}let s=this.pathConstraints;for(let n=0,d=s.length;n<d;n++){let l=s[n],r=l.data;l.position=r.position,l.spacing=r.spacing,l.mixRotate=r.mixRotate,l.mixX=r.mixX,l.mixY=r.mixY}}setSlotsToSetupPose(){let e=this.slots;B.arrayCopy(e,0,this.drawOrder,0,e.length);for(let t=0,i=e.length;t<i;t++)e[t].setToSetupPose()}getRootBone(){return this.bones.length==0?null:this.bones[0]}findBone(e){if(!e)throw new Error("boneName cannot be null.");let t=this.bones;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.data.name==e)return n}return null}findSlot(e){if(!e)throw new Error("slotName cannot be null.");let t=this.slots;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.data.name==e)return n}return null}setSkinByName(e){let t=this.data.findSkin(e);if(!t)throw new Error("Skin not found: "+e);this.setSkin(t)}setSkin(e){if(e!=this.skin){if(e)if(this.skin)e.attachAll(this,this.skin);else{let t=this.slots;for(let i=0,s=t.length;i<s;i++){let n=t[i],d=n.data.attachmentName;if(d){let l=e.getAttachment(i,d);l&&n.setAttachment(l)}}}this.skin=e,this.updateCache()}}getAttachmentByName(e,t){let i=this.data.findSlot(e);if(!i)throw new Error(`Can't find slot with name ${e}`);return this.getAttachment(i.index,t)}getAttachment(e,t){if(!t)throw new Error("attachmentName cannot be null.");if(this.skin){let i=this.skin.getAttachment(e,t);if(i)return i}return this.data.defaultSkin?this.data.defaultSkin.getAttachment(e,t):null}setAttachment(e,t){if(!e)throw new Error("slotName cannot be null.");let i=this.slots;for(let s=0,n=i.length;s<n;s++){let d=i[s];if(d.data.name==e){let l=null;if(t&&(l=this.getAttachment(s,t),!l))throw new Error("Attachment not found: "+t+", for slot: "+e);d.setAttachment(l);return}}throw new Error("Slot not found: "+e)}findIkConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.ikConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.data.name==e)return n}return null}findTransformConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.transformConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.data.name==e)return n}return null}findPathConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.pathConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.data.name==e)return n}return null}getBoundsRect(){let e=new Te,t=new Te;return this.getBounds(e,t),{x:e.x,y:e.y,width:t.x,height:t.y}}getBounds(e,t,i=new Array(2)){if(!e)throw new Error("offset cannot be null.");if(!t)throw new Error("size cannot be null.");let s=this.drawOrder,n=Number.POSITIVE_INFINITY,d=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,r=Number.NEGATIVE_INFINITY;for(let h=0,a=s.length;h<a;h++){let o=s[h];if(!o.bone.active)continue;let c=0,u=null,f=o.getAttachment();if(f instanceof ae)c=8,u=B.setArraySize(i,c,0),f.computeWorldVertices(o,u,0,2);else if(f instanceof Ue){let m=f;c=m.worldVerticesLength,u=B.setArraySize(i,c,0),m.computeWorldVertices(o,0,c,u,0,2)}if(u)for(let m=0,g=u.length;m<g;m+=2){let x=u[m],v=u[m+1];n=Math.min(n,x),d=Math.min(d,v),l=Math.max(l,x),r=Math.max(r,v)}}e.set(n,d),t.set(l-n,r-d)}},Ei=gs;P(Ei,"yDown",!1);var Ii=class{name=null;bones=new Array;slots=new Array;skins=new Array;defaultSkin=null;events=new Array;animations=new Array;ikConstraints=new Array;transformConstraints=new Array;pathConstraints=new Array;x=0;y=0;width=0;height=0;version=null;hash=null;fps=0;imagesPath=null;audioPath=null;findBone(e){if(!e)throw new Error("boneName cannot be null.");let t=this.bones;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findSlot(e){if(!e)throw new Error("slotName cannot be null.");let t=this.slots;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findSkin(e){if(!e)throw new Error("skinName cannot be null.");let t=this.skins;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findEvent(e){if(!e)throw new Error("eventDataName cannot be null.");let t=this.events;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findAnimation(e){if(!e)throw new Error("animationName cannot be null.");let t=this.animations;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findIkConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.ikConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findTransformConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.transformConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}findPathConstraint(e){if(!e)throw new Error("constraintName cannot be null.");let t=this.pathConstraints;for(let i=0,s=t.length;i<s;i++){let n=t[i];if(n.name==e)return n}return null}},Mi=class{constructor(e=0,t,i){this.slotIndex=e,this.name=t,this.attachment=i}},Rt=class{name;attachments=new Array;bones=Array();constraints=new Array;constructor(e){if(!e)throw new Error("name cannot be null.");this.name=e}setAttachment(e,t,i){if(!i)throw new Error("attachment cannot be null.");let s=this.attachments;e>=s.length&&(s.length=e+1),s[e]||(s[e]={}),s[e][t]=i}addSkin(e){for(let s=0;s<e.bones.length;s++){let n=e.bones[s],d=!1;for(let l=0;l<this.bones.length;l++)if(this.bones[l]==n){d=!0;break}d||this.bones.push(n)}for(let s=0;s<e.constraints.length;s++){let n=e.constraints[s],d=!1;for(let l=0;l<this.constraints.length;l++)if(this.constraints[l]==n){d=!0;break}d||this.constraints.push(n)}let t=e.getAttachments();for(let s=0;s<t.length;s++){var i=t[s];this.setAttachment(i.slotIndex,i.name,i.attachment)}}copySkin(e){for(let s=0;s<e.bones.length;s++){let n=e.bones[s],d=!1;for(let l=0;l<this.bones.length;l++)if(this.bones[l]==n){d=!0;break}d||this.bones.push(n)}for(let s=0;s<e.constraints.length;s++){let n=e.constraints[s],d=!1;for(let l=0;l<this.constraints.length;l++)if(this.constraints[l]==n){d=!0;break}d||this.constraints.push(n)}let t=e.getAttachments();for(let s=0;s<t.length;s++){var i=t[s];i.attachment&&(i.attachment instanceof Ue?(i.attachment=i.attachment.newLinkedMesh(),this.setAttachment(i.slotIndex,i.name,i.attachment)):(i.attachment=i.attachment.copy(),this.setAttachment(i.slotIndex,i.name,i.attachment)))}}getAttachment(e,t){let i=this.attachments[e];return i?i[t]:null}removeAttachment(e,t){let i=this.attachments[e];i&&delete i[t]}getAttachments(){let e=new Array;for(var t=0;t<this.attachments.length;t++){let i=this.attachments[t];if(i)for(let s in i){let n=i[s];n&&e.push(new Mi(t,s,n))}}return e}getAttachmentsForSlot(e,t){let i=this.attachments[e];if(i)for(let s in i){let n=i[s];n&&t.push(new Mi(e,s,n))}}clear(){this.attachments.length=0,this.bones.length=0,this.constraints.length=0}attachAll(e,t){let i=0;for(let s=0;s<e.slots.length;s++){let n=e.slots[s],d=n.getAttachment();if(d&&i<t.attachments.length){let l=t.attachments[i];for(let r in l){let h=l[r];if(d==h){let a=this.getAttachment(i,r);a&&n.setAttachment(a);break}}}i++}}},Ri=class{index=0;name;boneData;color=new D(1,1,1,1);darkColor=null;attachmentName=null;blendMode=Yt.Normal;constructor(e,t,i){if(e<0)throw new Error("index must be >= 0.");if(!t)throw new Error("name cannot be null.");if(!i)throw new Error("boneData cannot be null.");this.index=e,this.name=t,this.boneData=i}},Yt=(e=>(e[e.Normal=0]="Normal",e[e.Additive=1]="Additive",e[e.Multiply=2]="Multiply",e[e.Screen=3]="Screen",e))(Yt||{}),Yi=class extends kt{bones=new Array;_target=null;set target(e){this._target=e}get target(){if(this._target)return this._target;throw new Error("BoneData not set.")}mixRotate=0;mixX=0;mixY=0;mixScaleX=0;mixScaleY=0;mixShearY=0;offsetRotation=0;offsetX=0;offsetY=0;offsetScaleX=0;offsetScaleY=0;offsetShearY=0;relative=!1;local=!1;constructor(e){super(e,0,!1)}},ps=class{scale=1;attachmentLoader;linkedMeshes=new Array;constructor(e){this.attachmentLoader=e}readSkeletonData(e){let t=this.scale,i=new Ii;i.name="";let s=new ws(e),n=s.readInt32(),d=s.readInt32();i.hash=d==0&&n==0?null:d.toString(16)+n.toString(16),i.version=s.readString(),i.x=s.readFloat(),i.y=s.readFloat(),i.width=s.readFloat(),i.height=s.readFloat();let l=s.readBoolean();l&&(i.fps=s.readFloat(),i.imagesPath=s.readString(),i.audioPath=s.readString());let r=0;r=s.readInt(!0);for(let a=0;a<r;a++){let o=s.readString();if(!o)throw new Error("String in string table must not be null.");s.strings.push(o)}r=s.readInt(!0);for(let a=0;a<r;a++){let o=s.readString();if(!o)throw new Error("Bone name must not be null.");let c=a==0?null:i.bones[s.readInt(!0)],u=new vi(a,o,c);u.rotation=s.readFloat(),u.x=s.readFloat()*t,u.y=s.readFloat()*t,u.scaleX=s.readFloat(),u.scaleY=s.readFloat(),u.shearX=s.readFloat(),u.shearY=s.readFloat(),u.length=s.readFloat()*t,u.transformMode=s.readInt(!0),u.skinRequired=s.readBoolean(),l&&D.rgba8888ToColor(u.color,s.readInt32()),i.bones.push(u)}r=s.readInt(!0);for(let a=0;a<r;a++){let o=s.readString();if(!o)throw new Error("Slot name must not be null.");let c=i.bones[s.readInt(!0)],u=new Ri(a,o,c);D.rgba8888ToColor(u.color,s.readInt32());let f=s.readInt32();f!=-1&&D.rgb888ToColor(u.darkColor=new D,f),u.attachmentName=s.readStringRef(),u.blendMode=s.readInt(!0),i.slots.push(u)}r=s.readInt(!0);for(let a=0,o;a<r;a++){let c=s.readString();if(!c)throw new Error("IK constraint data name must not be null.");let u=new Ti(c);u.order=s.readInt(!0),u.skinRequired=s.readBoolean(),o=s.readInt(!0);for(let f=0;f<o;f++)u.bones.push(i.bones[s.readInt(!0)]);u.target=i.bones[s.readInt(!0)],u.mix=s.readFloat(),u.softness=s.readFloat()*t,u.bendDirection=s.readByte(),u.compress=s.readBoolean(),u.stretch=s.readBoolean(),u.uniform=s.readBoolean(),i.ikConstraints.push(u)}r=s.readInt(!0);for(let a=0,o;a<r;a++){let c=s.readString();if(!c)throw new Error("Transform constraint data name must not be null.");let u=new Yi(c);u.order=s.readInt(!0),u.skinRequired=s.readBoolean(),o=s.readInt(!0);for(let f=0;f<o;f++)u.bones.push(i.bones[s.readInt(!0)]);u.target=i.bones[s.readInt(!0)],u.local=s.readBoolean(),u.relative=s.readBoolean(),u.offsetRotation=s.readFloat(),u.offsetX=s.readFloat()*t,u.offsetY=s.readFloat()*t,u.offsetScaleX=s.readFloat(),u.offsetScaleY=s.readFloat(),u.offsetShearY=s.readFloat(),u.mixRotate=s.readFloat(),u.mixX=s.readFloat(),u.mixY=s.readFloat(),u.mixScaleX=s.readFloat(),u.mixScaleY=s.readFloat(),u.mixShearY=s.readFloat(),i.transformConstraints.push(u)}r=s.readInt(!0);for(let a=0,o;a<r;a++){let c=s.readString();if(!c)throw new Error("Path constraint data name must not be null.");let u=new ki(c);u.order=s.readInt(!0),u.skinRequired=s.readBoolean(),o=s.readInt(!0);for(let f=0;f<o;f++)u.bones.push(i.bones[s.readInt(!0)]);u.target=i.slots[s.readInt(!0)],u.positionMode=s.readInt(!0),u.spacingMode=s.readInt(!0),u.rotateMode=s.readInt(!0),u.offsetRotation=s.readFloat(),u.position=s.readFloat(),u.positionMode==0&&(u.position*=t),u.spacing=s.readFloat(),(u.spacingMode==0||u.spacingMode==1)&&(u.spacing*=t),u.mixRotate=s.readFloat(),u.mixX=s.readFloat(),u.mixY=s.readFloat(),i.pathConstraints.push(u)}let h=this.readSkin(s,i,!0,l);h&&(i.defaultSkin=h,i.skins.push(h));{let a=i.skins.length;for(B.setArraySize(i.skins,r=a+s.readInt(!0));a<r;a++){let o=this.readSkin(s,i,!1,l);if(!o)throw new Error("readSkin() should not have returned null.");i.skins[a]=o}}r=this.linkedMeshes.length;for(let a=0;a<r;a++){let o=this.linkedMeshes[a],c=o.skin?i.findSkin(o.skin):i.defaultSkin;if(!c)throw new Error("Not skin found for linked mesh.");if(!o.parent)throw new Error("Linked mesh parent must not be null");let u=c.getAttachment(o.slotIndex,o.parent);if(!u)throw new Error(`Parent mesh not found: ${o.parent}`);o.mesh.timelineAttachment=o.inheritTimeline?u:o.mesh,o.mesh.setParentMesh(u),o.mesh.region!=null&&o.mesh.updateRegion()}this.linkedMeshes.length=0,r=s.readInt(!0);for(let a=0;a<r;a++){let o=s.readStringRef();if(!o)throw new Error;let c=new Si(o);c.intValue=s.readInt(!1),c.floatValue=s.readFloat(),c.stringValue=s.readString(),c.audioPath=s.readString(),c.audioPath&&(c.volume=s.readFloat(),c.balance=s.readFloat()),i.events.push(c)}r=s.readInt(!0);for(let a=0;a<r;a++){let o=s.readString();if(!o)throw new Error("Animatio name must not be null.");i.animations.push(this.readAnimation(s,o,i))}return i}readSkin(e,t,i,s){let n=null,d=0;if(i){if(d=e.readInt(!0),d==0)return null;n=new Rt("default")}else{let l=e.readStringRef();if(!l)throw new Error("Skin name must not be null.");n=new Rt(l),n.bones.length=e.readInt(!0);for(let r=0,h=n.bones.length;r<h;r++)n.bones[r]=t.bones[e.readInt(!0)];for(let r=0,h=e.readInt(!0);r<h;r++)n.constraints.push(t.ikConstraints[e.readInt(!0)]);for(let r=0,h=e.readInt(!0);r<h;r++)n.constraints.push(t.transformConstraints[e.readInt(!0)]);for(let r=0,h=e.readInt(!0);r<h;r++)n.constraints.push(t.pathConstraints[e.readInt(!0)]);d=e.readInt(!0)}for(let l=0;l<d;l++){let r=e.readInt(!0);for(let h=0,a=e.readInt(!0);h<a;h++){let o=e.readStringRef();if(!o)throw new Error("Attachment name must not be null");let c=this.readAttachment(e,t,n,r,o,s);c&&n.setAttachment(r,o,c)}}return n}readAttachment(e,t,i,s,n,d){let l=this.scale,r=e.readStringRef();switch(r||(r=n),e.readByte()){case ze.Region:{let h=e.readStringRef(),a=e.readFloat(),o=e.readFloat(),c=e.readFloat(),u=e.readFloat(),f=e.readFloat(),m=e.readFloat(),g=e.readFloat(),x=e.readInt32(),v=this.readSequence(e);h||(h=r);let p=this.attachmentLoader.newRegionAttachment(i,r,h,v);return p?(p.path=h,p.x=o*l,p.y=c*l,p.scaleX=u,p.scaleY=f,p.rotation=a,p.width=m*l,p.height=g*l,D.rgba8888ToColor(p.color,x),p.sequence=v,v==null&&p.updateRegion(),p):null}case ze.BoundingBox:{let h=e.readInt(!0),a=this.readVertices(e,h),o=d?e.readInt32():0,c=this.attachmentLoader.newBoundingBoxAttachment(i,r);return c?(c.worldVerticesLength=h<<1,c.vertices=a.vertices,c.bones=a.bones,d&&D.rgba8888ToColor(c.color,o),c):null}case ze.Mesh:{let h=e.readStringRef(),a=e.readInt32(),o=e.readInt(!0),c=this.readFloatArray(e,o<<1,1),u=this.readShortArray(e),f=this.readVertices(e,o),m=e.readInt(!0),g=this.readSequence(e),x=[],v=0,p=0;d&&(x=this.readShortArray(e),v=e.readFloat(),p=e.readFloat()),h||(h=r);let w=this.attachmentLoader.newMeshAttachment(i,r,h,g);return w?(w.path=h,D.rgba8888ToColor(w.color,a),w.bones=f.bones,w.vertices=f.vertices,w.worldVerticesLength=o<<1,w.triangles=u,w.regionUVs=c,g==null&&w.updateRegion(),w.hullLength=m<<1,w.sequence=g,d&&(w.edges=x,w.width=v*l,w.height=p*l),w):null}case ze.LinkedMesh:{let h=e.readStringRef(),a=e.readInt32(),o=e.readStringRef(),c=e.readStringRef(),u=e.readBoolean(),f=this.readSequence(e),m=0,g=0;d&&(m=e.readFloat(),g=e.readFloat()),h||(h=r);let x=this.attachmentLoader.newMeshAttachment(i,r,h,f);return x?(x.path=h,D.rgba8888ToColor(x.color,a),x.sequence=f,d&&(x.width=m*l,x.height=g*l),this.linkedMeshes.push(new er(x,o,s,c,u)),x):null}case ze.Path:{let h=e.readBoolean(),a=e.readBoolean(),o=e.readInt(!0),c=this.readVertices(e,o),u=B.newArray(o/3,0);for(let g=0,x=u.length;g<x;g++)u[g]=e.readFloat()*l;let f=d?e.readInt32():0,m=this.attachmentLoader.newPathAttachment(i,r);return m?(m.closed=h,m.constantSpeed=a,m.worldVerticesLength=o<<1,m.vertices=c.vertices,m.bones=c.bones,m.lengths=u,d&&D.rgba8888ToColor(m.color,f),m):null}case ze.Point:{let h=e.readFloat(),a=e.readFloat(),o=e.readFloat(),c=d?e.readInt32():0,u=this.attachmentLoader.newPointAttachment(i,r);return u?(u.x=a*l,u.y=o*l,u.rotation=h,d&&D.rgba8888ToColor(u.color,c),u):null}case ze.Clipping:{let h=e.readInt(!0),a=e.readInt(!0),o=this.readVertices(e,a),c=d?e.readInt32():0,u=this.attachmentLoader.newClippingAttachment(i,r);return u?(u.endSlot=t.slots[h],u.worldVerticesLength=a<<1,u.vertices=o.vertices,u.bones=o.bones,d&&D.rgba8888ToColor(u.color,c),u):null}}return null}readSequence(e){if(!e.readBoolean())return null;let t=new qt(e.readInt(!0));return t.start=e.readInt(!0),t.digits=e.readInt(!0),t.setupIndex=e.readInt(!0),t}readVertices(e,t){let i=this.scale,s=t<<1,n=new tr;if(!e.readBoolean())return n.vertices=this.readFloatArray(e,s,i),n;let d=new Array,l=new Array;for(let r=0;r<t;r++){let h=e.readInt(!0);l.push(h);for(let a=0;a<h;a++)l.push(e.readInt(!0)),d.push(e.readFloat()*i),d.push(e.readFloat()*i),d.push(e.readFloat())}return n.vertices=B.toFloatArray(d),n.bones=l,n}readFloatArray(e,t,i){let s=new Array(t);if(i==1)for(let n=0;n<t;n++)s[n]=e.readFloat();else for(let n=0;n<t;n++)s[n]=e.readFloat()*i;return s}readShortArray(e){let t=e.readInt(!0),i=new Array(t);for(let s=0;s<t;s++)i[s]=e.readShort();return i}readAnimation(e,t,i){e.readInt(!0);let s=new Array,n=this.scale,d=new D,l=new D;for(let o=0,c=e.readInt(!0);o<c;o++){let u=e.readInt(!0);for(let f=0,m=e.readInt(!0);f<m;f++){let g=e.readByte(),x=e.readInt(!0),v=x-1;switch(g){case ur:{let p=new He(x,u);for(let w=0;w<x;w++)p.setFrame(w,e.readFloat(),e.readStringRef());s.push(p);break}case fr:{let p=e.readInt(!0),w=new ti(x,p,u),b=e.readFloat(),y=e.readUnsignedByte()/255,C=e.readUnsignedByte()/255,A=e.readUnsignedByte()/255,T=e.readUnsignedByte()/255;for(let M=0,Y=0;w.setFrame(M,b,y,C,A,T),M!=v;M++){let X=e.readFloat(),L=e.readUnsignedByte()/255,R=e.readUnsignedByte()/255,I=e.readUnsignedByte()/255,k=e.readUnsignedByte()/255;switch(e.readByte()){case Me:w.setStepped(M);break;case Re:le(e,w,Y++,M,0,b,X,y,L,1),le(e,w,Y++,M,1,b,X,C,R,1),le(e,w,Y++,M,2,b,X,A,I,1),le(e,w,Y++,M,3,b,X,T,k,1)}b=X,y=L,C=R,A=I,T=k}s.push(w);break}case mr:{let p=e.readInt(!0),w=new ii(x,p,u),b=e.readFloat(),y=e.readUnsignedByte()/255,C=e.readUnsignedByte()/255,A=e.readUnsignedByte()/255;for(let T=0,M=0;w.setFrame(T,b,y,C,A),T!=v;T++){let Y=e.readFloat(),X=e.readUnsignedByte()/255,L=e.readUnsignedByte()/255,R=e.readUnsignedByte()/255;switch(e.readByte()){case Me:w.setStepped(T);break;case Re:le(e,w,M++,T,0,b,Y,y,X,1),le(e,w,M++,T,1,b,Y,C,L,1),le(e,w,M++,T,2,b,Y,A,R,1)}b=Y,y=X,C=L,A=R}s.push(w);break}case gr:{let p=e.readInt(!0),w=new ri(x,p,u),b=e.readFloat(),y=e.readUnsignedByte()/255,C=e.readUnsignedByte()/255,A=e.readUnsignedByte()/255,T=e.readUnsignedByte()/255,M=e.readUnsignedByte()/255,Y=e.readUnsignedByte()/255,X=e.readUnsignedByte()/255;for(let L=0,R=0;w.setFrame(L,b,y,C,A,T,M,Y,X),L!=v;L++){let I=e.readFloat(),k=e.readUnsignedByte()/255,_=e.readUnsignedByte()/255,se=e.readUnsignedByte()/255,te=e.readUnsignedByte()/255,oe=e.readUnsignedByte()/255,re=e.readUnsignedByte()/255,de=e.readUnsignedByte()/255;switch(e.readByte()){case Me:w.setStepped(L);break;case Re:le(e,w,R++,L,0,b,I,y,k,1),le(e,w,R++,L,1,b,I,C,_,1),le(e,w,R++,L,2,b,I,A,se,1),le(e,w,R++,L,3,b,I,T,te,1),le(e,w,R++,L,4,b,I,M,oe,1),le(e,w,R++,L,5,b,I,Y,re,1),le(e,w,R++,L,6,b,I,X,de,1)}b=I,y=k,C=_,A=se,T=te,M=oe,Y=re,X=de}s.push(w);break}case pr:{let p=e.readInt(!0),w=new ni(x,p,u),b=e.readFloat(),y=e.readUnsignedByte()/255,C=e.readUnsignedByte()/255,A=e.readUnsignedByte()/255,T=e.readUnsignedByte()/255,M=e.readUnsignedByte()/255,Y=e.readUnsignedByte()/255;for(let X=0,L=0;w.setFrame(X,b,y,C,A,T,M,Y),X!=v;X++){let R=e.readFloat(),I=e.readUnsignedByte()/255,k=e.readUnsignedByte()/255,_=e.readUnsignedByte()/255,se=e.readUnsignedByte()/255,te=e.readUnsignedByte()/255,oe=e.readUnsignedByte()/255;switch(e.readByte()){case Me:w.setStepped(X);break;case Re:le(e,w,L++,X,0,b,R,y,I,1),le(e,w,L++,X,1,b,R,C,k,1),le(e,w,L++,X,2,b,R,A,_,1),le(e,w,L++,X,3,b,R,T,se,1),le(e,w,L++,X,4,b,R,M,te,1),le(e,w,L++,X,5,b,R,Y,oe,1)}b=R,y=I,C=k,A=_,T=se,M=te,Y=oe}s.push(w);break}case wr:{let p=new si(x,e.readInt(!0),u),w=e.readFloat(),b=e.readUnsignedByte()/255;for(let y=0,C=0;p.setFrame(y,w,b),y!=v;y++){let A=e.readFloat(),T=e.readUnsignedByte()/255;switch(e.readByte()){case Me:p.setStepped(y);break;case Re:le(e,p,C++,y,0,w,A,b,T,1)}w=A,b=T}s.push(p)}}}}for(let o=0,c=e.readInt(!0);o<c;o++){let u=e.readInt(!0);for(let f=0,m=e.readInt(!0);f<m;f++){let g=e.readByte(),x=e.readInt(!0),v=e.readInt(!0);switch(g){case ir:s.push(Pe(e,new ot(x,v,u),1));break;case sr:s.push(Fi(e,new Ht(x,v,u),n));break;case rr:s.push(Pe(e,new Gt(x,v,u),n));break;case nr:s.push(Pe(e,new jt(x,v,u),n));break;case ar:s.push(Fi(e,new Zt(x,v,u),1));break;case lr:s.push(Pe(e,new Jt(x,v,u),1));break;case or:s.push(Pe(e,new Kt(x,v,u),1));break;case hr:s.push(Fi(e,new Qt(x,v,u),1));break;case dr:s.push(Pe(e,new $t(x,v,u),1));break;case cr:s.push(Pe(e,new ei(x,v,u),1))}}}for(let o=0,c=e.readInt(!0);o<c;o++){let u=e.readInt(!0),f=e.readInt(!0),m=f-1,g=new li(f,e.readInt(!0),u),x=e.readFloat(),v=e.readFloat(),p=e.readFloat()*n;for(let w=0,b=0;g.setFrame(w,x,v,p,e.readByte(),e.readBoolean(),e.readBoolean()),w!=m;w++){let y=e.readFloat(),C=e.readFloat(),A=e.readFloat()*n;switch(e.readByte()){case Me:g.setStepped(w);break;case Re:le(e,g,b++,w,0,x,y,v,C,1),le(e,g,b++,w,1,x,y,p,A,n)}x=y,v=C,p=A}s.push(g)}for(let o=0,c=e.readInt(!0);o<c;o++){let u=e.readInt(!0),f=e.readInt(!0),m=f-1,g=new oi(f,e.readInt(!0),u),x=e.readFloat(),v=e.readFloat(),p=e.readFloat(),w=e.readFloat(),b=e.readFloat(),y=e.readFloat(),C=e.readFloat();for(let A=0,T=0;g.setFrame(A,x,v,p,w,b,y,C),A!=m;A++){let M=e.readFloat(),Y=e.readFloat(),X=e.readFloat(),L=e.readFloat(),R=e.readFloat(),I=e.readFloat(),k=e.readFloat();switch(e.readByte()){case Me:g.setStepped(A);break;case Re:le(e,g,T++,A,0,x,M,v,Y,1),le(e,g,T++,A,1,x,M,p,X,1),le(e,g,T++,A,2,x,M,w,L,1),le(e,g,T++,A,3,x,M,b,R,1),le(e,g,T++,A,4,x,M,y,I,1),le(e,g,T++,A,5,x,M,C,k,1)}x=M,v=Y,p=X,w=L,b=R,y=I,C=k}s.push(g)}for(let o=0,c=e.readInt(!0);o<c;o++){let u=e.readInt(!0),f=i.pathConstraints[u];for(let m=0,g=e.readInt(!0);m<g;m++)switch(e.readByte()){case vr:s.push(Pe(e,new hi(e.readInt(!0),e.readInt(!0),u),f.positionMode==0?n:1));break;case yr:s.push(Pe(e,new di(e.readInt(!0),e.readInt(!0),u),f.spacingMode==0||f.spacingMode==1?n:1));break;case Ar:let x=new ci(e.readInt(!0),e.readInt(!0),u),v=e.readFloat(),p=e.readFloat(),w=e.readFloat(),b=e.readFloat();for(let y=0,C=0,A=x.getFrameCount()-1;x.setFrame(y,v,p,w,b),y!=A;y++){let T=e.readFloat(),M=e.readFloat(),Y=e.readFloat(),X=e.readFloat();switch(e.readByte()){case Me:x.setStepped(y);break;case Re:le(e,x,C++,y,0,v,T,p,M,1),le(e,x,C++,y,1,v,T,w,Y,1),le(e,x,C++,y,2,v,T,b,X,1)}v=T,p=M,w=Y,b=X}s.push(x)}}for(let o=0,c=e.readInt(!0);o<c;o++){let u=i.skins[e.readInt(!0)];for(let f=0,m=e.readInt(!0);f<m;f++){let g=e.readInt(!0);for(let x=0,v=e.readInt(!0);x<v;x++){let p=e.readStringRef();if(!p)throw new Error("attachmentName must not be null.");let w=u.getAttachment(g,p),b=e.readByte(),y=e.readInt(!0),C=y-1;switch(b){case xr:{let A=w,T=A.bones,M=A.vertices,Y=T?M.length/3*2:M.length,X=e.readInt(!0),L=new ai(y,X,g,A),R=e.readFloat();for(let I=0,k=0;;I++){let _,se=e.readInt(!0);if(se==0)_=T?B.newFloatArray(Y):M;else{_=B.newFloatArray(Y);let oe=e.readInt(!0);if(se+=oe,n==1)for(let re=oe;re<se;re++)_[re]=e.readFloat();else for(let re=oe;re<se;re++)_[re]=e.readFloat()*n;if(!T)for(let re=0,de=_.length;re<de;re++)_[re]+=M[re]}if(L.setFrame(I,R,_),I==C)break;let te=e.readFloat();switch(e.readByte()){case Me:L.setStepped(I);break;case Re:le(e,L,k++,I,0,R,te,0,1,1)}R=te}s.push(L);break}case br:{let A=new $e(y,g,w);for(let T=0;T<y;T++){let M=e.readFloat(),Y=e.readInt32();A.setFrame(T,M,Ji[Y&15],Y>>4,e.readFloat())}s.push(A);break}}}}}let r=e.readInt(!0);if(r>0){let o=new Ge(r),c=i.slots.length;for(let u=0;u<r;u++){let f=e.readFloat(),m=e.readInt(!0),g=B.newArray(c,0);for(let w=c-1;w>=0;w--)g[w]=-1;let x=B.newArray(c-m,0),v=0,p=0;for(let w=0;w<m;w++){let b=e.readInt(!0);for(;v!=b;)x[p++]=v++;g[v+e.readInt(!0)]=v++}for(;v<c;)x[p++]=v++;for(let w=c-1;w>=0;w--)g[w]==-1&&(g[w]=x[--p]);o.setFrame(u,f,g)}s.push(o)}let h=e.readInt(!0);if(h>0){let o=new ht(h);for(let c=0;c<h;c++){let u=e.readFloat(),f=i.events[e.readInt(!0)],m=new Ci(u,f);m.intValue=e.readInt(!1),m.floatValue=e.readFloat(),m.stringValue=e.readBoolean()?e.readString():f.stringValue,m.data.audioPath&&(m.volume=e.readFloat(),m.balance=e.readFloat()),o.setFrame(c,m)}s.push(o)}let a=0;for(let o=0,c=s.length;o<c;o++)a=Math.max(a,s[o].getDuration());return new xt(t,s,a)}},ws=class{constructor(e,t=new Array,i=0,s=new DataView(e.buffer)){this.strings=t,this.index=i,this.buffer=s}readByte(){return this.buffer.getInt8(this.index++)}readUnsignedByte(){return this.buffer.getUint8(this.index++)}readShort(){let e=this.buffer.getInt16(this.index);return this.index+=2,e}readInt32(){let e=this.buffer.getInt32(this.index);return this.index+=4,e}readInt(e){let t=this.readByte(),i=t&127;return t&128&&(t=this.readByte(),i|=(t&127)<<7,t&128&&(t=this.readByte(),i|=(t&127)<<14,t&128&&(t=this.readByte(),i|=(t&127)<<21,t&128&&(t=this.readByte(),i|=(t&127)<<28)))),e?i:i>>>1^-(i&1)}readStringRef(){let e=this.readInt(!0);return e==0?null:this.strings[e-1]}readString(){let e=this.readInt(!0);switch(e){case 0:return null;case 1:return""}e--;let t="",i=0;for(let s=0;s<e;){let n=this.readUnsignedByte();switch(n>>4){case 12:case 13:t+=String.fromCharCode((n&31)<<6|this.readByte()&63),s+=2;break;case 14:t+=String.fromCharCode((n&15)<<12|(this.readByte()&63)<<6|this.readByte()&63),s+=3;break;default:t+=String.fromCharCode(n),s++}}return t}readFloat(){let e=this.buffer.getFloat32(this.index);return this.index+=4,e}readBoolean(){return this.readByte()!=0}},er=class{parent;skin;slotIndex;mesh;inheritTimeline;constructor(e,t,i,s,n){this.mesh=e,this.skin=t,this.slotIndex=i,this.parent=s,this.inheritTimeline=n}},tr=class{constructor(e=null,t=null){this.bones=e,this.vertices=t}},ze=(e=>(e[e.Region=0]="Region",e[e.BoundingBox=1]="BoundingBox",e[e.Mesh=2]="Mesh",e[e.LinkedMesh=3]="LinkedMesh",e[e.Path=4]="Path",e[e.Point=5]="Point",e[e.Clipping=6]="Clipping",e))(ze||{});function Pe(e,t,i){let s=e.readFloat(),n=e.readFloat()*i;for(let d=0,l=0,r=t.getFrameCount()-1;t.setFrame(d,s,n),d!=r;d++){let h=e.readFloat(),a=e.readFloat()*i;switch(e.readByte()){case Me:t.setStepped(d);break;case Re:le(e,t,l++,d,0,s,h,n,a,i)}s=h,n=a}return t}function Fi(e,t,i){let s=e.readFloat(),n=e.readFloat()*i,d=e.readFloat()*i;for(let l=0,r=0,h=t.getFrameCount()-1;t.setFrame(l,s,n,d),l!=h;l++){let a=e.readFloat(),o=e.readFloat()*i,c=e.readFloat()*i;switch(e.readByte()){case Me:t.setStepped(l);break;case Re:le(e,t,r++,l,0,s,a,n,o,i),le(e,t,r++,l,1,s,a,d,c,i)}s=a,n=o,d=c}return t}function le(e,t,i,s,n,d,l,r,h,a){t.setBezier(i,s,n,d,r,e.readFloat(),e.readFloat()*a,e.readFloat(),e.readFloat()*a,l,h)}var ir=0,sr=1,rr=2,nr=3,ar=4,lr=5,or=6,hr=7,dr=8,cr=9,ur=0,fr=1,mr=2,gr=3,pr=4,wr=5,xr=0,br=1,vr=0,yr=1,Ar=2,Me=1,Re=2,xs=class{minX=0;minY=0;maxX=0;maxY=0;boundingBoxes=new Array;polygons=new Array;polygonPool=new lt(()=>B.newFloatArray(16));update(e,t){if(!e)throw new Error("skeleton cannot be null.");let i=this.boundingBoxes,s=this.polygons,n=this.polygonPool,d=e.slots,l=d.length;i.length=0,n.freeAll(s),s.length=0;for(let r=0;r<l;r++){let h=d[r];if(!h.bone.active)continue;let a=h.getAttachment();if(a instanceof Ct){let o=a;i.push(o);let c=n.obtain();c.length!=o.worldVerticesLength&&(c=B.newFloatArray(o.worldVerticesLength)),s.push(c),o.computeWorldVertices(h,0,o.worldVerticesLength,c,0,2)}}t?this.aabbCompute():(this.minX=Number.POSITIVE_INFINITY,this.minY=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.maxY=Number.NEGATIVE_INFINITY)}aabbCompute(){let e=Number.POSITIVE_INFINITY,t=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,s=Number.NEGATIVE_INFINITY,n=this.polygons;for(let d=0,l=n.length;d<l;d++){let r=n[d],h=r;for(let a=0,o=r.length;a<o;a+=2){let c=h[a],u=h[a+1];e=Math.min(e,c),t=Math.min(t,u),i=Math.max(i,c),s=Math.max(s,u)}}this.minX=e,this.minY=t,this.maxX=i,this.maxY=s}aabbContainsPoint(e,t){return e>=this.minX&&e<=this.maxX&&t>=this.minY&&t<=this.maxY}aabbIntersectsSegment(e,t,i,s){let n=this.minX,d=this.minY,l=this.maxX,r=this.maxY;if(e<=n&&i<=n||t<=d&&s<=d||e>=l&&i>=l||t>=r&&s>=r)return!1;let h=(s-t)/(i-e),a=h*(n-e)+t;if(a>d&&a<r||(a=h*(l-e)+t,a>d&&a<r))return!0;let o=(d-t)/h+e;return o>n&&o<l||(o=(r-t)/h+e,o>n&&o<l)}aabbIntersectsSkeleton(e){return this.minX<e.maxX&&this.maxX>e.minX&&this.minY<e.maxY&&this.maxY>e.minY}containsPoint(e,t){let i=this.polygons;for(let s=0,n=i.length;s<n;s++)if(this.containsPointPolygon(i[s],e,t))return this.boundingBoxes[s];return null}containsPointPolygon(e,t,i){let s=e,n=e.length,d=n-2,l=!1;for(let r=0;r<n;r+=2){let h=s[r+1],a=s[d+1];if(h<i&&a>=i||a<i&&h>=i){let o=s[r];o+(i-h)/(a-h)*(s[d]-o)<t&&(l=!l)}d=r}return l}intersectsSegment(e,t,i,s){let n=this.polygons;for(let d=0,l=n.length;d<l;d++)if(this.intersectsSegmentPolygon(n[d],e,t,i,s))return this.boundingBoxes[d];return null}intersectsSegmentPolygon(e,t,i,s,n){let d=e,l=e.length,r=t-s,h=i-n,a=t*n-i*s,o=d[l-2],c=d[l-1];for(let u=0;u<l;u+=2){let f=d[u],m=d[u+1],g=o*m-c*f,x=o-f,v=c-m,p=r*v-h*x,w=(a*x-r*g)/p;if((w>=o&&w<=f||w>=f&&w<=o)&&(w>=t&&w<=s||w>=s&&w<=t)){let b=(a*v-h*g)/p;if((b>=c&&b<=m||b>=m&&b<=c)&&(b>=i&&b<=n||b>=n&&b<=i))return!0}o=f,c=m}return!1}getPolygon(e){if(!e)throw new Error("boundingBox cannot be null.");let t=this.boundingBoxes.indexOf(e);return t==-1?null:this.polygons[t]}getWidth(){return this.maxX-this.minX}getHeight(){return this.maxY-this.minY}},ye=class{convexPolygons=new Array;convexPolygonsIndices=new Array;indicesArray=new Array;isConcaveArray=new Array;triangles=new Array;polygonPool=new lt(()=>new Array);polygonIndicesPool=new lt(()=>new Array);triangulate(e){let t=e,i=e.length>>1,s=this.indicesArray;s.length=0;for(let l=0;l<i;l++)s[l]=l;let n=this.isConcaveArray;n.length=0;for(let l=0,r=i;l<r;++l)n[l]=ye.isConcave(l,i,t,s);let d=this.triangles;for(d.length=0;i>3;){let l=i-1,r=0,h=1;for(;;){e:if(!n[r]){let c=s[l]<<1,u=s[r]<<1,f=s[h]<<1,m=t[c],g=t[c+1],x=t[u],v=t[u+1],p=t[f],w=t[f+1];for(let b=(h+1)%i;b!=l;b=(b+1)%i){if(!n[b])continue;let y=s[b]<<1,C=t[y],A=t[y+1];if(ye.positiveArea(p,w,m,g,C,A)&&ye.positiveArea(m,g,x,v,C,A)&&ye.positiveArea(x,v,p,w,C,A))break e}break}if(h==0){do{if(!n[r])break;r--}while(r>0);break}l=r,r=h,h=(h+1)%i}d.push(s[(i+r-1)%i]),d.push(s[r]),d.push(s[(r+1)%i]),s.splice(r,1),n.splice(r,1),i--;let a=(i+r-1)%i,o=r==i?0:r;n[a]=ye.isConcave(a,i,t,s),n[o]=ye.isConcave(o,i,t,s)}return i==3&&(d.push(s[2]),d.push(s[0]),d.push(s[1])),d}decompose(e,t){let i=e,s=this.convexPolygons;this.polygonPool.freeAll(s),s.length=0;let n=this.convexPolygonsIndices;this.polygonIndicesPool.freeAll(n),n.length=0;let d=this.polygonIndicesPool.obtain();d.length=0;let l=this.polygonPool.obtain();l.length=0;let r=-1,h=0;for(let a=0,o=t.length;a<o;a+=3){let c=t[a]<<1,u=t[a+1]<<1,f=t[a+2]<<1,m=i[c],g=i[c+1],x=i[u],v=i[u+1],p=i[f],w=i[f+1],b=!1;if(r==c){let y=l.length-4,C=ye.winding(l[y],l[y+1],l[y+2],l[y+3],p,w),A=ye.winding(p,w,l[0],l[1],l[2],l[3]);C==h&&A==h&&(l.push(p),l.push(w),d.push(f),b=!0)}b||(l.length>0?(s.push(l),n.push(d)):(this.polygonPool.free(l),this.polygonIndicesPool.free(d)),l=this.polygonPool.obtain(),l.length=0,l.push(m),l.push(g),l.push(x),l.push(v),l.push(p),l.push(w),d=this.polygonIndicesPool.obtain(),d.length=0,d.push(c),d.push(u),d.push(f),h=ye.winding(m,g,x,v,p,w),r=c)}l.length>0&&(s.push(l),n.push(d));for(let a=0,o=s.length;a<o;a++){if(d=n[a],d.length==0)continue;let c=d[0],u=d[d.length-1];l=s[a];let f=l.length-4,m=l[f],g=l[f+1],x=l[f+2],v=l[f+3],p=l[0],w=l[1],b=l[2],y=l[3],C=ye.winding(m,g,x,v,p,w);for(let A=0;A<o;A++){if(A==a)continue;let T=n[A];if(T.length!=3)continue;let M=T[0],Y=T[1],X=T[2],L=s[A],R=L[L.length-2],I=L[L.length-1];if(M!=c||Y!=u)continue;let k=ye.winding(m,g,x,v,R,I),_=ye.winding(R,I,p,w,b,y);k==C&&_==C&&(L.length=0,T.length=0,l.push(R),l.push(I),d.push(X),m=x,g=v,x=R,v=I,A=0)}}for(let a=s.length-1;a>=0;a--)l=s[a],l.length==0&&(s.splice(a,1),this.polygonPool.free(l),d=n[a],n.splice(a,1),this.polygonIndicesPool.free(d));return s}static isConcave(e,t,i,s){let n=s[(t+e-1)%t]<<1,d=s[e]<<1,l=s[(e+1)%t]<<1;return!this.positiveArea(i[n],i[n+1],i[d],i[d+1],i[l],i[l+1])}static positiveArea(e,t,i,s,n,d){return e*(d-s)+i*(t-d)+n*(s-t)>=0}static winding(e,t,i,s,n,d){let l=i-e,r=s-t;return n*r-d*l+l*t-e*r>=0?1:-1}},Ft=class{triangulator=new ye;clippingPolygon=new Array;clipOutput=new Array;clippedVertices=new Array;clippedTriangles=new Array;scratch=new Array;clipAttachment=null;clippingPolygons=null;clipStart(e,t){if(this.clipAttachment)return 0;this.clipAttachment=t;let i=t.worldVerticesLength,s=B.setArraySize(this.clippingPolygon,i);t.computeWorldVertices(e,0,i,s,0,2);let n=this.clippingPolygon;Ft.makeClockwise(n);let d=this.clippingPolygons=this.triangulator.decompose(n,this.triangulator.triangulate(n));for(let l=0,r=d.length;l<r;l++){let h=d[l];Ft.makeClockwise(h),h.push(h[0]),h.push(h[1])}return d.length}clipEndWithSlot(e){this.clipAttachment&&this.clipAttachment.endSlot==e.data&&this.clipEnd()}clipEnd(){this.clipAttachment&&(this.clipAttachment=null,this.clippingPolygons=null,this.clippedVertices.length=0,this.clippedTriangles.length=0,this.clippingPolygon.length=0)}isClipping(){return this.clipAttachment!=null}clipTriangles(e,t,i,s,n,d,l,r){let h=this.clipOutput,a=this.clippedVertices,o=this.clippedTriangles,c=this.clippingPolygons,u=c.length,f=r?12:8,m=0;a.length=0,o.length=0;e:for(let g=0;g<s;g+=3){let x=i[g]<<1,v=e[x],p=e[x+1],w=n[x],b=n[x+1];x=i[g+1]<<1;let y=e[x],C=e[x+1],A=n[x],T=n[x+1];x=i[g+2]<<1;let M=e[x],Y=e[x+1],X=n[x],L=n[x+1];for(let R=0;R<u;R++){let I=a.length;if(this.clip(v,p,y,C,M,Y,c[R],h)){let k=h.length;if(k==0)continue;let _=C-Y,se=M-y,te=v-M,oe=Y-p,re=1/(_*te+se*(p-Y)),de=k>>1,ne=this.clipOutput,ie=B.setArraySize(a,I+de*f);for(let xe=0;xe<k;xe+=2){let Fe=ne[xe],we=ne[xe+1];ie[I]=Fe,ie[I+1]=we,ie[I+2]=d.r,ie[I+3]=d.g,ie[I+4]=d.b,ie[I+5]=d.a;let Ce=Fe-M,Se=we-Y,_e=(_*Ce+se*Se)*re,rt=(oe*Ce+te*Se)*re,mt=1-_e-rt;ie[I+6]=w*_e+A*rt+X*mt,ie[I+7]=b*_e+T*rt+L*mt,r&&(ie[I+8]=l.r,ie[I+9]=l.g,ie[I+10]=l.b,ie[I+11]=l.a),I+=f}I=o.length;let ue=B.setArraySize(o,I+3*(de-2));de--;for(let xe=1;xe<de;xe++)ue[I]=m,ue[I+1]=m+xe,ue[I+2]=m+xe+1,I+=3;m+=de+1}else{let k=B.setArraySize(a,I+3*f);k[I]=v,k[I+1]=p,k[I+2]=d.r,k[I+3]=d.g,k[I+4]=d.b,k[I+5]=d.a,r?(k[I+6]=w,k[I+7]=b,k[I+8]=l.r,k[I+9]=l.g,k[I+10]=l.b,k[I+11]=l.a,k[I+12]=y,k[I+13]=C,k[I+14]=d.r,k[I+15]=d.g,k[I+16]=d.b,k[I+17]=d.a,k[I+18]=A,k[I+19]=T,k[I+20]=l.r,k[I+21]=l.g,k[I+22]=l.b,k[I+23]=l.a,k[I+24]=M,k[I+25]=Y,k[I+26]=d.r,k[I+27]=d.g,k[I+28]=d.b,k[I+29]=d.a,k[I+30]=X,k[I+31]=L,k[I+32]=l.r,k[I+33]=l.g,k[I+34]=l.b,k[I+35]=l.a):(k[I+6]=w,k[I+7]=b,k[I+8]=y,k[I+9]=C,k[I+10]=d.r,k[I+11]=d.g,k[I+12]=d.b,k[I+13]=d.a,k[I+14]=A,k[I+15]=T,k[I+16]=M,k[I+17]=Y,k[I+18]=d.r,k[I+19]=d.g,k[I+20]=d.b,k[I+21]=d.a,k[I+22]=X,k[I+23]=L),I=o.length;let _=B.setArraySize(o,I+3);_[I]=m,_[I+1]=m+1,_[I+2]=m+2,m+=3;continue e}}}}clip(e,t,i,s,n,d,l,r){let h=r,a=!1,o;l.length%4>=2?(o=r,r=this.scratch):o=this.scratch,o.length=0,o.push(e),o.push(t),o.push(i),o.push(s),o.push(n),o.push(d),o.push(e),o.push(t),r.length=0;let c=l,u=l.length-4;for(let f=0;;f+=2){let m=c[f],g=c[f+1],x=c[f+2],v=c[f+3],p=m-x,w=g-v,b=o,y=o.length-2,C=r.length;for(let T=0;T<y;T+=2){let M=b[T],Y=b[T+1],X=b[T+2],L=b[T+3],R=p*(L-v)-w*(X-x)>0;if(p*(Y-v)-w*(M-x)>0){if(R){r.push(X),r.push(L);continue}let I=L-Y,k=X-M,_=I*(x-m)-k*(v-g);if(Math.abs(_)>1e-6){let se=(k*(g-Y)-I*(m-M))/_;r.push(m+(x-m)*se),r.push(g+(v-g)*se)}else r.push(m),r.push(g)}else if(R){let I=L-Y,k=X-M,_=I*(x-m)-k*(v-g);if(Math.abs(_)>1e-6){let se=(k*(g-Y)-I*(m-M))/_;r.push(m+(x-m)*se),r.push(g+(v-g)*se)}else r.push(m),r.push(g);r.push(X),r.push(L)}a=!0}if(C==r.length)return h.length=0,!0;if(r.push(r[0]),r.push(r[1]),f==u)break;let A=r;r=o,r.length=0,o=A}if(h!=r){h.length=0;for(let f=0,m=r.length-2;f<m;f++)h[f]=r[f]}else h.length=h.length-2;return a}static makeClockwise(e){let t=e,i=e.length,s=t[i-2]*t[1]-t[0]*t[i-1],n=0,d=0,l=0,r=0;for(let h=0,a=i-3;h<a;h+=2)n=t[h],d=t[h+1],l=t[h+2],r=t[h+3],s+=n*r-l*d;if(!(s<0))for(let h=0,a=i-2,o=i>>1;h<o;h+=2){let c=t[h],u=t[h+1],f=a-h;t[h]=t[f],t[h+1]=t[f+1],t[f]=c,t[f+1]=u}}},bs=class{attachmentLoader;scale=1;linkedMeshes=new Array;constructor(e){this.attachmentLoader=e}readSkeletonData(e){let t=this.scale,i=new Ii,s=typeof e=="string"?JSON.parse(e):e,n=s.skeleton;if(n&&(i.hash=n.hash,i.version=n.spine,i.x=n.x,i.y=n.y,i.width=n.width,i.height=n.height,i.fps=n.fps,i.imagesPath=n.images),s.bones)for(let d=0;d<s.bones.length;d++){let l=s.bones[d],r=null,h=E(l,"parent",null);h&&(r=i.findBone(h));let a=new vi(i.bones.length,l.name,r);a.length=E(l,"length",0)*t,a.x=E(l,"x",0)*t,a.y=E(l,"y",0)*t,a.rotation=E(l,"rotation",0),a.scaleX=E(l,"scaleX",1),a.scaleY=E(l,"scaleY",1),a.shearX=E(l,"shearX",0),a.shearY=E(l,"shearY",0),a.transformMode=B.enumValue(Tt,E(l,"transform","Normal")),a.skinRequired=E(l,"skin",!1);let o=E(l,"color",null);o&&a.color.setFromString(o),i.bones.push(a)}if(s.slots)for(let d=0;d<s.slots.length;d++){let l=s.slots[d],r=i.findBone(l.bone);if(!r)throw new Error(`Couldn't find bone ${l.bone} for slot ${l.name}`);let h=new Ri(i.slots.length,l.name,r),a=E(l,"color",null);a&&h.color.setFromString(a);let o=E(l,"dark",null);o&&(h.darkColor=D.fromString(o)),h.attachmentName=E(l,"attachment",null),h.blendMode=B.enumValue(Yt,E(l,"blend","normal")),i.slots.push(h)}if(s.ik)for(let d=0;d<s.ik.length;d++){let l=s.ik[d],r=new Ti(l.name);r.order=E(l,"order",0),r.skinRequired=E(l,"skin",!1);for(let a=0;a<l.bones.length;a++){let o=i.findBone(l.bones[a]);if(!o)throw new Error(`Couldn't find bone ${l.bones[a]} for IK constraint ${l.name}.`);r.bones.push(o)}let h=i.findBone(l.target);if(!h)throw new Error(`Couldn't find target bone ${l.target} for IK constraint ${l.name}.`);r.target=h,r.mix=E(l,"mix",1),r.softness=E(l,"softness",0)*t,r.bendDirection=E(l,"bendPositive",!0)?1:-1,r.compress=E(l,"compress",!1),r.stretch=E(l,"stretch",!1),r.uniform=E(l,"uniform",!1),i.ikConstraints.push(r)}if(s.transform)for(let d=0;d<s.transform.length;d++){let l=s.transform[d],r=new Yi(l.name);r.order=E(l,"order",0),r.skinRequired=E(l,"skin",!1);for(let o=0;o<l.bones.length;o++){let c=l.bones[o],u=i.findBone(c);if(!u)throw new Error(`Couldn't find bone ${c} for transform constraint ${l.name}.`);r.bones.push(u)}let h=l.target,a=i.findBone(h);if(!a)throw new Error(`Couldn't find target bone ${h} for transform constraint ${l.name}.`);r.target=a,r.local=E(l,"local",!1),r.relative=E(l,"relative",!1),r.offsetRotation=E(l,"rotation",0),r.offsetX=E(l,"x",0)*t,r.offsetY=E(l,"y",0)*t,r.offsetScaleX=E(l,"scaleX",0),r.offsetScaleY=E(l,"scaleY",0),r.offsetShearY=E(l,"shearY",0),r.mixRotate=E(l,"mixRotate",1),r.mixX=E(l,"mixX",1),r.mixY=E(l,"mixY",r.mixX),r.mixScaleX=E(l,"mixScaleX",1),r.mixScaleY=E(l,"mixScaleY",r.mixScaleX),r.mixShearY=E(l,"mixShearY",1),i.transformConstraints.push(r)}if(s.path)for(let d=0;d<s.path.length;d++){let l=s.path[d],r=new ki(l.name);r.order=E(l,"order",0),r.skinRequired=E(l,"skin",!1);for(let o=0;o<l.bones.length;o++){let c=l.bones[o],u=i.findBone(c);if(!u)throw new Error(`Couldn't find bone ${c} for path constraint ${l.name}.`);r.bones.push(u)}let h=l.target,a=i.findSlot(h);if(!a)throw new Error(`Couldn't find target slot ${h} for path constraint ${l.name}.`);r.target=a,r.positionMode=B.enumValue(Et,E(l,"positionMode","Percent")),r.spacingMode=B.enumValue(It,E(l,"spacingMode","Length")),r.rotateMode=B.enumValue(Mt,E(l,"rotateMode","Tangent")),r.offsetRotation=E(l,"rotation",0),r.position=E(l,"position",0),r.positionMode==0&&(r.position*=t),r.spacing=E(l,"spacing",0),(r.spacingMode==0||r.spacingMode==1)&&(r.spacing*=t),r.mixRotate=E(l,"mixRotate",1),r.mixX=E(l,"mixX",1),r.mixY=E(l,"mixY",r.mixX),i.pathConstraints.push(r)}if(s.skins)for(let d=0;d<s.skins.length;d++){let l=s.skins[d],r=new Rt(l.name);if(l.bones)for(let h=0;h<l.bones.length;h++){let a=l.bones[h],o=i.findBone(a);if(!o)throw new Error(`Couldn't find bone ${a} for skin ${l.name}.`);r.bones.push(o)}if(l.ik)for(let h=0;h<l.ik.length;h++){let a=l.ik[h],o=i.findIkConstraint(a);if(!o)throw new Error(`Couldn't find IK constraint ${a} for skin ${l.name}.`);r.constraints.push(o)}if(l.transform)for(let h=0;h<l.transform.length;h++){let a=l.transform[h],o=i.findTransformConstraint(a);if(!o)throw new Error(`Couldn't find transform constraint ${a} for skin ${l.name}.`);r.constraints.push(o)}if(l.path)for(let h=0;h<l.path.length;h++){let a=l.path[h],o=i.findPathConstraint(a);if(!o)throw new Error(`Couldn't find path constraint ${a} for skin ${l.name}.`);r.constraints.push(o)}for(let h in l.attachments){let a=i.findSlot(h);if(!a)throw new Error(`Couldn't find slot ${h} for skin ${l.name}.`);let o=l.attachments[h];for(let c in o){let u=this.readAttachment(o[c],r,a.index,c,i);u&&r.setAttachment(a.index,c,u)}}i.skins.push(r),r.name=="default"&&(i.defaultSkin=r)}for(let d=0,l=this.linkedMeshes.length;d<l;d++){let r=this.linkedMeshes[d],h=r.skin?i.findSkin(r.skin):i.defaultSkin;if(!h)throw new Error(`Skin not found: ${r.skin}`);let a=h.getAttachment(r.slotIndex,r.parent);if(!a)throw new Error(`Parent mesh not found: ${r.parent}`);r.mesh.timelineAttachment=r.inheritTimeline?a:r.mesh,r.mesh.setParentMesh(a),r.mesh.region!=null&&r.mesh.updateRegion()}if(this.linkedMeshes.length=0,s.events)for(let d in s.events){let l=s.events[d],r=new Si(d);r.intValue=E(l,"int",0),r.floatValue=E(l,"float",0),r.stringValue=E(l,"string",""),r.audioPath=E(l,"audio",null),r.audioPath&&(r.volume=E(l,"volume",1),r.balance=E(l,"balance",0)),i.events.push(r)}if(s.animations)for(let d in s.animations){let l=s.animations[d];this.readAnimation(l,d,i)}return i}readAttachment(e,t,i,s,n){let d=this.scale;switch(s=E(e,"name",s),E(e,"type","region")){case"region":{let l=E(e,"path",s),r=this.readSequence(E(e,"sequence",null)),h=this.attachmentLoader.newRegionAttachment(t,s,l,r);if(!h)return null;h.path=l,h.x=E(e,"x",0)*d,h.y=E(e,"y",0)*d,h.scaleX=E(e,"scaleX",1),h.scaleY=E(e,"scaleY",1),h.rotation=E(e,"rotation",0),h.width=e.width*d,h.height=e.height*d,h.sequence=r;let a=E(e,"color",null);return a&&h.color.setFromString(a),h.region!=null&&h.updateRegion(),h}case"boundingbox":{let l=this.attachmentLoader.newBoundingBoxAttachment(t,s);if(!l)return null;this.readVertices(e,l,e.vertexCount<<1);let r=E(e,"color",null);return r&&l.color.setFromString(r),l}case"mesh":case"linkedmesh":{let l=E(e,"path",s),r=this.readSequence(E(e,"sequence",null)),h=this.attachmentLoader.newMeshAttachment(t,s,l,r);if(!h)return null;h.path=l;let a=E(e,"color",null);a&&h.color.setFromString(a),h.width=E(e,"width",0)*d,h.height=E(e,"height",0)*d,h.sequence=r;let o=E(e,"parent",null);if(o)return this.linkedMeshes.push(new Cr(h,E(e,"skin",null),i,o,E(e,"timelines",!0))),h;let c=e.uvs;return this.readVertices(e,h,c.length),h.triangles=e.triangles,h.regionUVs=c,h.region!=null&&h.updateRegion(),h.edges=E(e,"edges",null),h.hullLength=E(e,"hull",0)*2,h}case"path":{let l=this.attachmentLoader.newPathAttachment(t,s);if(!l)return null;l.closed=E(e,"closed",!1),l.constantSpeed=E(e,"constantSpeed",!0);let r=e.vertexCount;this.readVertices(e,l,r<<1);let h=B.newArray(r/3,0);for(let o=0;o<e.lengths.length;o++)h[o]=e.lengths[o]*d;l.lengths=h;let a=E(e,"color",null);return a&&l.color.setFromString(a),l}case"point":{let l=this.attachmentLoader.newPointAttachment(t,s);if(!l)return null;l.x=E(e,"x",0)*d,l.y=E(e,"y",0)*d,l.rotation=E(e,"rotation",0);let r=E(e,"color",null);return r&&l.color.setFromString(r),l}case"clipping":{let l=this.attachmentLoader.newClippingAttachment(t,s);if(!l)return null;let r=E(e,"end",null);r&&(l.endSlot=n.findSlot(r));let h=e.vertexCount;this.readVertices(e,l,h<<1);let a=E(e,"color",null);return a&&l.color.setFromString(a),l}}return null}readSequence(e){if(e==null)return null;let t=new qt(E(e,"count",0));return t.start=E(e,"start",1),t.digits=E(e,"digits",0),t.setupIndex=E(e,"setup",0),t}readVertices(e,t,i){let s=this.scale;t.worldVerticesLength=i;let n=e.vertices;if(i==n.length){let r=B.toFloatArray(n);if(s!=1)for(let h=0,a=n.length;h<a;h++)r[h]*=s;t.vertices=r;return}let d=new Array,l=new Array;for(let r=0,h=n.length;r<h;){let a=n[r++];l.push(a);for(let o=r+a*4;r<o;r+=4)l.push(n[r]),d.push(n[r+1]*s),d.push(n[r+2]*s),d.push(n[r+3])}t.bones=l,t.vertices=B.toFloatArray(d)}readAnimation(e,t,i){let s=this.scale,n=new Array;if(e.slots)for(let l in e.slots){let r=e.slots[l],h=i.findSlot(l);if(!h)throw new Error("Slot not found: "+l);let a=h.index;for(let o in r){let c=r[o];if(!c)continue;let u=c.length;if(o=="attachment"){let f=new He(u,a);for(let m=0;m<u;m++){let g=c[m];f.setFrame(m,E(g,"time",0),E(g,"name",null))}n.push(f)}else if(o=="rgba"){let f=new ti(u,u<<2,a),m=c[0],g=E(m,"time",0),x=D.fromString(m.color);for(let v=0,p=0;;v++){f.setFrame(v,g,x.r,x.g,x.b,x.a);let w=c[v+1];if(!w){f.shrink(p);break}let b=E(w,"time",0),y=D.fromString(w.color),C=m.curve;C&&(p=he(C,f,p,v,0,g,b,x.r,y.r,1),p=he(C,f,p,v,1,g,b,x.g,y.g,1),p=he(C,f,p,v,2,g,b,x.b,y.b,1),p=he(C,f,p,v,3,g,b,x.a,y.a,1)),g=b,x=y,m=w}n.push(f)}else if(o=="rgb"){let f=new ii(u,u*3,a),m=c[0],g=E(m,"time",0),x=D.fromString(m.color);for(let v=0,p=0;;v++){f.setFrame(v,g,x.r,x.g,x.b);let w=c[v+1];if(!w){f.shrink(p);break}let b=E(w,"time",0),y=D.fromString(w.color),C=m.curve;C&&(p=he(C,f,p,v,0,g,b,x.r,y.r,1),p=he(C,f,p,v,1,g,b,x.g,y.g,1),p=he(C,f,p,v,2,g,b,x.b,y.b,1)),g=b,x=y,m=w}n.push(f)}else if(o=="alpha")n.push(Le(c,new si(u,u,a),0,1));else if(o=="rgba2"){let f=new ri(u,u*7,a),m=c[0],g=E(m,"time",0),x=D.fromString(m.light),v=D.fromString(m.dark);for(let p=0,w=0;;p++){f.setFrame(p,g,x.r,x.g,x.b,x.a,v.r,v.g,v.b);let b=c[p+1];if(!b){f.shrink(w);break}let y=E(b,"time",0),C=D.fromString(b.light),A=D.fromString(b.dark),T=m.curve;T&&(w=he(T,f,w,p,0,g,y,x.r,C.r,1),w=he(T,f,w,p,1,g,y,x.g,C.g,1),w=he(T,f,w,p,2,g,y,x.b,C.b,1),w=he(T,f,w,p,3,g,y,x.a,C.a,1),w=he(T,f,w,p,4,g,y,v.r,A.r,1),w=he(T,f,w,p,5,g,y,v.g,A.g,1),w=he(T,f,w,p,6,g,y,v.b,A.b,1)),g=y,x=C,v=A,m=b}n.push(f)}else if(o=="rgb2"){let f=new ni(u,u*6,a),m=c[0],g=E(m,"time",0),x=D.fromString(m.light),v=D.fromString(m.dark);for(let p=0,w=0;;p++){f.setFrame(p,g,x.r,x.g,x.b,v.r,v.g,v.b);let b=c[p+1];if(!b){f.shrink(w);break}let y=E(b,"time",0),C=D.fromString(b.light),A=D.fromString(b.dark),T=m.curve;T&&(w=he(T,f,w,p,0,g,y,x.r,C.r,1),w=he(T,f,w,p,1,g,y,x.g,C.g,1),w=he(T,f,w,p,2,g,y,x.b,C.b,1),w=he(T,f,w,p,3,g,y,v.r,A.r,1),w=he(T,f,w,p,4,g,y,v.g,A.g,1),w=he(T,f,w,p,5,g,y,v.b,A.b,1)),g=y,x=C,v=A,m=b}n.push(f)}}}if(e.bones)for(let l in e.bones){let r=e.bones[l],h=i.findBone(l);if(!h)throw new Error("Bone not found: "+l);let a=h.index;for(let o in r){let c=r[o],u=c.length;if(u!=0){if(o==="rotate")n.push(Le(c,new ot(u,u,a),0,1));else if(o==="translate"){let f=new Ht(u,u<<1,a);n.push(Li(c,f,"x","y",0,s))}else if(o==="translatex"){let f=new Gt(u,u,a);n.push(Le(c,f,0,s))}else if(o==="translatey"){let f=new jt(u,u,a);n.push(Le(c,f,0,s))}else if(o==="scale"){let f=new Zt(u,u<<1,a);n.push(Li(c,f,"x","y",1,1))}else if(o==="scalex"){let f=new Jt(u,u,a);n.push(Le(c,f,1,1))}else if(o==="scaley"){let f=new Kt(u,u,a);n.push(Le(c,f,1,1))}else if(o==="shear"){let f=new Qt(u,u<<1,a);n.push(Li(c,f,"x","y",0,1))}else if(o==="shearx"){let f=new $t(u,u,a);n.push(Le(c,f,0,1))}else if(o==="sheary"){let f=new ei(u,u,a);n.push(Le(c,f,0,1))}}}}if(e.ik)for(let l in e.ik){let r=e.ik[l],h=r[0];if(!h)continue;let a=i.findIkConstraint(l);if(!a)throw new Error("IK Constraint not found: "+l);let o=i.ikConstraints.indexOf(a),c=new li(r.length,r.length<<1,o),u=E(h,"time",0),f=E(h,"mix",1),m=E(h,"softness",0)*s;for(let g=0,x=0;;g++){c.setFrame(g,u,f,m,E(h,"bendPositive",!0)?1:-1,E(h,"compress",!1),E(h,"stretch",!1));let v=r[g+1];if(!v){c.shrink(x);break}let p=E(v,"time",0),w=E(v,"mix",1),b=E(v,"softness",0)*s,y=h.curve;y&&(x=he(y,c,x,g,0,u,p,f,w,1),x=he(y,c,x,g,1,u,p,m,b,s)),u=p,f=w,m=b,h=v}n.push(c)}if(e.transform)for(let l in e.transform){let r=e.transform[l],h=r[0];if(!h)continue;let a=i.findTransformConstraint(l);if(!a)throw new Error("Transform constraint not found: "+l);let o=i.transformConstraints.indexOf(a),c=new oi(r.length,r.length*6,o),u=E(h,"time",0),f=E(h,"mixRotate",1),m=E(h,"mixX",1),g=E(h,"mixY",m),x=E(h,"mixScaleX",1),v=E(h,"mixScaleY",x),p=E(h,"mixShearY",1);for(let w=0,b=0;;w++){c.setFrame(w,u,f,m,g,x,v,p);let y=r[w+1];if(!y){c.shrink(b);break}let C=E(y,"time",0),A=E(y,"mixRotate",1),T=E(y,"mixX",1),M=E(y,"mixY",T),Y=E(y,"mixScaleX",1),X=E(y,"mixScaleY",Y),L=E(y,"mixShearY",1),R=h.curve;R&&(b=he(R,c,b,w,0,u,C,f,A,1),b=he(R,c,b,w,1,u,C,m,T,1),b=he(R,c,b,w,2,u,C,g,M,1),b=he(R,c,b,w,3,u,C,x,Y,1),b=he(R,c,b,w,4,u,C,v,X,1),b=he(R,c,b,w,5,u,C,p,L,1)),u=C,f=A,m=T,g=M,x=Y,v=X,x=Y,h=y}n.push(c)}if(e.path)for(let l in e.path){let r=e.path[l],h=i.findPathConstraint(l);if(!h)throw new Error("Path constraint not found: "+l);let a=i.pathConstraints.indexOf(h);for(let o in r){let c=r[o],u=c[0];if(!u)continue;let f=c.length;if(o==="position"){let m=new hi(f,f,a);n.push(Le(c,m,0,h.positionMode==0?s:1))}else if(o==="spacing"){let m=new di(f,f,a);n.push(Le(c,m,0,h.spacingMode==0||h.spacingMode==1?s:1))}else if(o==="mix"){let m=new ci(f,f*3,a),g=E(u,"time",0),x=E(u,"mixRotate",1),v=E(u,"mixX",1),p=E(u,"mixY",v);for(let w=0,b=0;;w++){m.setFrame(w,g,x,v,p);let y=c[w+1];if(!y){m.shrink(b);break}let C=E(y,"time",0),A=E(y,"mixRotate",1),T=E(y,"mixX",1),M=E(y,"mixY",T),Y=u.curve;Y&&(b=he(Y,m,b,w,0,g,C,x,A,1),b=he(Y,m,b,w,1,g,C,v,T,1),b=he(Y,m,b,w,2,g,C,p,M,1)),g=C,x=A,v=T,p=M,u=y}n.push(m)}}}if(e.attachments)for(let l in e.attachments){let r=e.attachments[l],h=i.findSkin(l);if(!h)throw new Error("Skin not found: "+l);for(let a in r){let o=r[a],c=i.findSlot(a);if(!c)throw new Error("Slot not found: "+a);let u=c.index;for(let f in o){let m=o[f],g=h.getAttachment(u,f);for(let x in m){let v=m[x],p=v[0];if(p){if(x=="deform"){let w=g.bones,b=g.vertices,y=w?b.length/3*2:b.length,C=new ai(v.length,v.length,u,g),A=E(p,"time",0);for(let T=0,M=0;;T++){let Y,X=E(p,"vertices",null);if(!X)Y=w?B.newFloatArray(y):b;else{Y=B.newFloatArray(y);let k=E(p,"offset",0);if(B.arrayCopy(X,0,Y,k,X.length),s!=1)for(let _=k,se=_+X.length;_<se;_++)Y[_]*=s;if(!w)for(let _=0;_<y;_++)Y[_]+=b[_]}C.setFrame(T,A,Y);let L=v[T+1];if(!L){C.shrink(M);break}let R=E(L,"time",0),I=p.curve;I&&(M=he(I,C,M,T,0,A,R,0,1,1)),A=R,p=L}n.push(C)}else if(x=="sequence"){let w=new $e(v.length,u,g),b=0;for(let y=0;y<v.length;y++){let C=E(p,"delay",b),A=E(p,"time",0),T=Zi[E(p,"mode","hold")],M=E(p,"index",0);w.setFrame(y,A,T,M,C),b=C,p=v[y+1]}n.push(w)}}}}}}if(e.drawOrder){let l=new Ge(e.drawOrder.length),r=i.slots.length,h=0;for(let a=0;a<e.drawOrder.length;a++,h++){let o=e.drawOrder[a],c=null,u=E(o,"offsets",null);if(u){c=B.newArray(r,-1);let f=B.newArray(r-u.length,0),m=0,g=0;for(let x=0;x<u.length;x++){let v=u[x],p=i.findSlot(v.slot);if(!p)throw new Error("Slot not found: "+p);let w=p.index;for(;m!=w;)f[g++]=m++;c[m+v.offset]=m++}for(;m<r;)f[g++]=m++;for(let x=r-1;x>=0;x--)c[x]==-1&&(c[x]=f[--g])}l.setFrame(h,E(o,"time",0),c)}n.push(l)}if(e.events){let l=new ht(e.events.length),r=0;for(let h=0;h<e.events.length;h++,r++){let a=e.events[h],o=i.findEvent(a.name);if(!o)throw new Error("Event not found: "+a.name);let c=new Ci(B.toSinglePrecision(E(a,"time",0)),o);c.intValue=E(a,"int",o.intValue),c.floatValue=E(a,"float",o.floatValue),c.stringValue=E(a,"string",o.stringValue),c.data.audioPath&&(c.volume=E(a,"volume",1),c.balance=E(a,"balance",0)),l.setFrame(r,c)}n.push(l)}let d=0;for(let l=0,r=n.length;l<r;l++)d=Math.max(d,n[l].getDuration());i.animations.push(new xt(t,n,d))}},Cr=class{parent;skin;slotIndex;mesh;inheritTimeline;constructor(e,t,i,s,n){this.mesh=e,this.skin=t,this.slotIndex=i,this.parent=s,this.inheritTimeline=n}};function Le(e,t,i,s){let n=e[0],d=E(n,"time",0),l=E(n,"value",i)*s,r=0;for(let h=0;;h++){t.setFrame(h,d,l);let a=e[h+1];if(!a)return t.shrink(r),t;let o=E(a,"time",0),c=E(a,"value",i)*s;n.curve&&(r=he(n.curve,t,r,h,0,d,o,l,c,s)),d=o,l=c,n=a}}function Li(e,t,i,s,n,d){let l=e[0],r=E(l,"time",0),h=E(l,i,n)*d,a=E(l,s,n)*d,o=0;for(let c=0;;c++){t.setFrame(c,r,h,a);let u=e[c+1];if(!u)return t.shrink(o),t;let f=E(u,"time",0),m=E(u,i,n)*d,g=E(u,s,n)*d,x=l.curve;x&&(o=he(x,t,o,c,0,r,f,h,m,d),o=he(x,t,o,c,1,r,f,a,g,d)),r=f,h=m,a=g,l=u}}function he(e,t,i,s,n,d,l,r,h,a){if(e=="stepped")return t.setStepped(s),i;let o=n<<2,c=e[o],u=e[o+1]*a,f=e[o+2],m=e[o+3]*a;return t.setBezier(i,s,n,d,r,c,u,f,m,l,h),i+1}function E(e,t,i){return e[t]!==void 0?e[t]:i}typeof Math.fround>"u"&&(Math.fround=function(e){return function(t){return e[0]=t,e[0]}}(new Float32Array(1)));var ge=class{canvas;gl;restorables=new Array;constructor(e,t={alpha:"true"}){if(e instanceof WebGLRenderingContext||typeof WebGL2RenderingContext<"u"&&e instanceof WebGL2RenderingContext)this.gl=e,this.canvas=this.gl.canvas;else{let i=e;this.gl=i.getContext("webgl2",t)||i.getContext("webgl",t),this.canvas=i,i.addEventListener("webglcontextlost",s=>{let n=s;s&&s.preventDefault()}),i.addEventListener("webglcontextrestored",s=>{for(let n=0,d=this.restorables.length;n<d;n++)this.restorables[n].restore()})}}addRestorable(e){this.restorables.push(e)}removeRestorable(e){let t=this.restorables.indexOf(e);t>-1&&this.restorables.splice(t,1)}},Lt=class extends pi{context;texture=null;boundUnit=0;useMipMaps=!1;constructor(e,t,i=!1){super(t),this.context=e instanceof ge?e:new ge(e),this.useMipMaps=i,this.restore(),this.context.addRestorable(this)}setFilters(e,t){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,e),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,Lt.validateMagFilter(t)),this.useMipMaps=Lt.usesMipMaps(e),this.useMipMaps&&i.generateMipmap(i.TEXTURE_2D)}static validateMagFilter(e){switch(e){case 9987:case 9987:case 9985:case 9986:case 9984:return 9729;default:return e}}static usesMipMaps(e){switch(e){case 9987:case 9987:case 9985:case 9986:case 9984:return!0;default:return!1}}setWraps(e,t){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,e),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,t)}update(e){let t=this.context.gl;this.texture||(this.texture=this.context.gl.createTexture()),this.bind(),Lt.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL&&t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,this._image),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,e?t.LINEAR_MIPMAP_LINEAR:t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),e&&t.generateMipmap(t.TEXTURE_2D)}restore(){this.texture=null,this.update(this.useMipMaps)}bind(e=0){let t=this.context.gl;this.boundUnit=e,t.activeTexture(t.TEXTURE0+e),t.bindTexture(t.TEXTURE_2D,this.texture)}unbind(){let e=this.context.gl;e.activeTexture(e.TEXTURE0+this.boundUnit),e.bindTexture(e.TEXTURE_2D,null)}dispose(){this.context.removeRestorable(this),this.context.gl.deleteTexture(this.texture)}},ct=Lt;P(ct,"DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL",!1);var Xi=class extends cs{constructor(e,t="",i=new Ai){super(s=>new ct(e,s),t,i)}},Ae=class{x=0;y=0;z=0;constructor(e=0,t=0,i=0){this.x=e,this.y=t,this.z=i}setFrom(e){return this.x=e.x,this.y=e.y,this.z=e.z,this}set(e,t,i){return this.x=e,this.y=t,this.z=i,this}add(e){return this.x+=e.x,this.y+=e.y,this.z+=e.z,this}sub(e){return this.x-=e.x,this.y-=e.y,this.z-=e.z,this}scale(e){return this.x*=e,this.y*=e,this.z*=e,this}normalize(){let e=this.length();return e==0?this:(e=1/e,this.x*=e,this.y*=e,this.z*=e,this)}cross(e){return this.set(this.y*e.z-this.z*e.y,this.z*e.x-this.x*e.z,this.x*e.y-this.y*e.x)}multiply(e){let t=e.values;return this.set(this.x*t[V]+this.y*t[q]+this.z*t[H]+t[O],this.x*t[G]+this.y*t[N]+this.z*t[j]+t[U],this.x*t[Z]+this.y*t[J]+this.z*t[z]+t[W])}project(e){let t=e.values,i=1/(this.x*t[Q]+this.y*t[$]+this.z*t[ee]+t[K]);return this.set((this.x*t[V]+this.y*t[q]+this.z*t[H]+t[O])*i,(this.x*t[G]+this.y*t[N]+this.z*t[j]+t[U])*i,(this.x*t[Z]+this.y*t[J]+this.z*t[z]+t[W])*i)}dot(e){return this.x*e.x+this.y*e.y+this.z*e.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}distance(e){let t=e.x-this.x,i=e.y-this.y,s=e.z-this.z;return Math.sqrt(t*t+i*i+s*s)}},V=0,q=4,H=8,O=12,G=1,N=5,j=9,U=13,Z=2,J=6,z=10,W=14,Q=3,$=7,ee=11,K=15,Ye=class{temp=new Float32Array(16);values=new Float32Array(16);constructor(){let e=this.values;e[V]=1,e[N]=1,e[z]=1,e[K]=1}set(e){return this.values.set(e),this}transpose(){let e=this.temp,t=this.values;return e[V]=t[V],e[q]=t[G],e[H]=t[Z],e[O]=t[Q],e[G]=t[q],e[N]=t[N],e[j]=t[J],e[U]=t[$],e[Z]=t[H],e[J]=t[j],e[z]=t[z],e[W]=t[ee],e[Q]=t[O],e[$]=t[U],e[ee]=t[W],e[K]=t[K],this.set(e)}identity(){let e=this.values;return e[V]=1,e[q]=0,e[H]=0,e[O]=0,e[G]=0,e[N]=1,e[j]=0,e[U]=0,e[Z]=0,e[J]=0,e[z]=1,e[W]=0,e[Q]=0,e[$]=0,e[ee]=0,e[K]=1,this}invert(){let e=this.values,t=this.temp,i=e[Q]*e[J]*e[j]*e[O]-e[Z]*e[$]*e[j]*e[O]-e[Q]*e[N]*e[z]*e[O]+e[G]*e[$]*e[z]*e[O]+e[Z]*e[N]*e[ee]*e[O]-e[G]*e[J]*e[ee]*e[O]-e[Q]*e[J]*e[H]*e[U]+e[Z]*e[$]*e[H]*e[U]+e[Q]*e[q]*e[z]*e[U]-e[V]*e[$]*e[z]*e[U]-e[Z]*e[q]*e[ee]*e[U]+e[V]*e[J]*e[ee]*e[U]+e[Q]*e[N]*e[H]*e[W]-e[G]*e[$]*e[H]*e[W]-e[Q]*e[q]*e[j]*e[W]+e[V]*e[$]*e[j]*e[W]+e[G]*e[q]*e[ee]*e[W]-e[V]*e[N]*e[ee]*e[W]-e[Z]*e[N]*e[H]*e[K]+e[G]*e[J]*e[H]*e[K]+e[Z]*e[q]*e[j]*e[K]-e[V]*e[J]*e[j]*e[K]-e[G]*e[q]*e[z]*e[K]+e[V]*e[N]*e[z]*e[K];if(i==0)throw new Error("non-invertible matrix");let s=1/i;return t[V]=e[j]*e[W]*e[$]-e[U]*e[z]*e[$]+e[U]*e[J]*e[ee]-e[N]*e[W]*e[ee]-e[j]*e[J]*e[K]+e[N]*e[z]*e[K],t[q]=e[O]*e[z]*e[$]-e[H]*e[W]*e[$]-e[O]*e[J]*e[ee]+e[q]*e[W]*e[ee]+e[H]*e[J]*e[K]-e[q]*e[z]*e[K],t[H]=e[H]*e[U]*e[$]-e[O]*e[j]*e[$]+e[O]*e[N]*e[ee]-e[q]*e[U]*e[ee]-e[H]*e[N]*e[K]+e[q]*e[j]*e[K],t[O]=e[O]*e[j]*e[J]-e[H]*e[U]*e[J]-e[O]*e[N]*e[z]+e[q]*e[U]*e[z]+e[H]*e[N]*e[W]-e[q]*e[j]*e[W],t[G]=e[U]*e[z]*e[Q]-e[j]*e[W]*e[Q]-e[U]*e[Z]*e[ee]+e[G]*e[W]*e[ee]+e[j]*e[Z]*e[K]-e[G]*e[z]*e[K],t[N]=e[H]*e[W]*e[Q]-e[O]*e[z]*e[Q]+e[O]*e[Z]*e[ee]-e[V]*e[W]*e[ee]-e[H]*e[Z]*e[K]+e[V]*e[z]*e[K],t[j]=e[O]*e[j]*e[Q]-e[H]*e[U]*e[Q]-e[O]*e[G]*e[ee]+e[V]*e[U]*e[ee]+e[H]*e[G]*e[K]-e[V]*e[j]*e[K],t[U]=e[H]*e[U]*e[Z]-e[O]*e[j]*e[Z]+e[O]*e[G]*e[z]-e[V]*e[U]*e[z]-e[H]*e[G]*e[W]+e[V]*e[j]*e[W],t[Z]=e[N]*e[W]*e[Q]-e[U]*e[J]*e[Q]+e[U]*e[Z]*e[$]-e[G]*e[W]*e[$]-e[N]*e[Z]*e[K]+e[G]*e[J]*e[K],t[J]=e[O]*e[J]*e[Q]-e[q]*e[W]*e[Q]-e[O]*e[Z]*e[$]+e[V]*e[W]*e[$]+e[q]*e[Z]*e[K]-e[V]*e[J]*e[K],t[z]=e[q]*e[U]*e[Q]-e[O]*e[N]*e[Q]+e[O]*e[G]*e[$]-e[V]*e[U]*e[$]-e[q]*e[G]*e[K]+e[V]*e[N]*e[K],t[W]=e[O]*e[N]*e[Z]-e[q]*e[U]*e[Z]-e[O]*e[G]*e[J]+e[V]*e[U]*e[J]+e[q]*e[G]*e[W]-e[V]*e[N]*e[W],t[Q]=e[j]*e[J]*e[Q]-e[N]*e[z]*e[Q]-e[j]*e[Z]*e[$]+e[G]*e[z]*e[$]+e[N]*e[Z]*e[ee]-e[G]*e[J]*e[ee],t[$]=e[q]*e[z]*e[Q]-e[H]*e[J]*e[Q]+e[H]*e[Z]*e[$]-e[V]*e[z]*e[$]-e[q]*e[Z]*e[ee]+e[V]*e[J]*e[ee],t[ee]=e[H]*e[N]*e[Q]-e[q]*e[j]*e[Q]-e[H]*e[G]*e[$]+e[V]*e[j]*e[$]+e[q]*e[G]*e[ee]-e[V]*e[N]*e[ee],t[K]=e[q]*e[j]*e[Z]-e[H]*e[N]*e[Z]+e[H]*e[G]*e[J]-e[V]*e[j]*e[J]-e[q]*e[G]*e[z]+e[V]*e[N]*e[z],e[V]=t[V]*s,e[q]=t[q]*s,e[H]=t[H]*s,e[O]=t[O]*s,e[G]=t[G]*s,e[N]=t[N]*s,e[j]=t[j]*s,e[U]=t[U]*s,e[Z]=t[Z]*s,e[J]=t[J]*s,e[z]=t[z]*s,e[W]=t[W]*s,e[Q]=t[Q]*s,e[$]=t[$]*s,e[ee]=t[ee]*s,e[K]=t[K]*s,this}determinant(){let e=this.values;return e[Q]*e[J]*e[j]*e[O]-e[Z]*e[$]*e[j]*e[O]-e[Q]*e[N]*e[z]*e[O]+e[G]*e[$]*e[z]*e[O]+e[Z]*e[N]*e[ee]*e[O]-e[G]*e[J]*e[ee]*e[O]-e[Q]*e[J]*e[H]*e[U]+e[Z]*e[$]*e[H]*e[U]+e[Q]*e[q]*e[z]*e[U]-e[V]*e[$]*e[z]*e[U]-e[Z]*e[q]*e[ee]*e[U]+e[V]*e[J]*e[ee]*e[U]+e[Q]*e[N]*e[H]*e[W]-e[G]*e[$]*e[H]*e[W]-e[Q]*e[q]*e[j]*e[W]+e[V]*e[$]*e[j]*e[W]+e[G]*e[q]*e[ee]*e[W]-e[V]*e[N]*e[ee]*e[W]-e[Z]*e[N]*e[H]*e[K]+e[G]*e[J]*e[H]*e[K]+e[Z]*e[q]*e[j]*e[K]-e[V]*e[J]*e[j]*e[K]-e[G]*e[q]*e[z]*e[K]+e[V]*e[N]*e[z]*e[K]}translate(e,t,i){let s=this.values;return s[O]+=e,s[U]+=t,s[W]+=i,this}copy(){return new Ye().set(this.values)}projection(e,t,i,s){this.identity();let n=1/Math.tan(i*(Math.PI/180)/2),d=(t+e)/(e-t),l=2*t*e/(e-t),r=this.values;return r[V]=n/s,r[G]=0,r[Z]=0,r[Q]=0,r[q]=0,r[N]=n,r[J]=0,r[$]=0,r[H]=0,r[j]=0,r[z]=d,r[ee]=-1,r[O]=0,r[U]=0,r[W]=l,r[K]=0,this}ortho2d(e,t,i,s){return this.ortho(e,e+i,t,t+s,0,1)}ortho(e,t,i,s,n,d){this.identity();let l=2/(t-e),r=2/(s-i),h=-2/(d-n),a=-(t+e)/(t-e),o=-(s+i)/(s-i),c=-(d+n)/(d-n),u=this.values;return u[V]=l,u[G]=0,u[Z]=0,u[Q]=0,u[q]=0,u[N]=r,u[J]=0,u[$]=0,u[H]=0,u[j]=0,u[z]=h,u[ee]=0,u[O]=a,u[U]=o,u[W]=c,u[K]=1,this}multiply(e){let t=this.temp,i=this.values,s=e.values;return t[V]=i[V]*s[V]+i[q]*s[G]+i[H]*s[Z]+i[O]*s[Q],t[q]=i[V]*s[q]+i[q]*s[N]+i[H]*s[J]+i[O]*s[$],t[H]=i[V]*s[H]+i[q]*s[j]+i[H]*s[z]+i[O]*s[ee],t[O]=i[V]*s[O]+i[q]*s[U]+i[H]*s[W]+i[O]*s[K],t[G]=i[G]*s[V]+i[N]*s[G]+i[j]*s[Z]+i[U]*s[Q],t[N]=i[G]*s[q]+i[N]*s[N]+i[j]*s[J]+i[U]*s[$],t[j]=i[G]*s[H]+i[N]*s[j]+i[j]*s[z]+i[U]*s[ee],t[U]=i[G]*s[O]+i[N]*s[U]+i[j]*s[W]+i[U]*s[K],t[Z]=i[Z]*s[V]+i[J]*s[G]+i[z]*s[Z]+i[W]*s[Q],t[J]=i[Z]*s[q]+i[J]*s[N]+i[z]*s[J]+i[W]*s[$],t[z]=i[Z]*s[H]+i[J]*s[j]+i[z]*s[z]+i[W]*s[ee],t[W]=i[Z]*s[O]+i[J]*s[U]+i[z]*s[W]+i[W]*s[K],t[Q]=i[Q]*s[V]+i[$]*s[G]+i[ee]*s[Z]+i[K]*s[Q],t[$]=i[Q]*s[q]+i[$]*s[N]+i[ee]*s[J]+i[K]*s[$],t[ee]=i[Q]*s[H]+i[$]*s[j]+i[ee]*s[z]+i[K]*s[ee],t[K]=i[Q]*s[O]+i[$]*s[U]+i[ee]*s[W]+i[K]*s[K],this.set(this.temp)}multiplyLeft(e){let t=this.temp,i=this.values,s=e.values;return t[V]=s[V]*i[V]+s[q]*i[G]+s[H]*i[Z]+s[O]*i[Q],t[q]=s[V]*i[q]+s[q]*i[N]+s[H]*i[J]+s[O]*i[$],t[H]=s[V]*i[H]+s[q]*i[j]+s[H]*i[z]+s[O]*i[ee],t[O]=s[V]*i[O]+s[q]*i[U]+s[H]*i[W]+s[O]*i[K],t[G]=s[G]*i[V]+s[N]*i[G]+s[j]*i[Z]+s[U]*i[Q],t[N]=s[G]*i[q]+s[N]*i[N]+s[j]*i[J]+s[U]*i[$],t[j]=s[G]*i[H]+s[N]*i[j]+s[j]*i[z]+s[U]*i[ee],t[U]=s[G]*i[O]+s[N]*i[U]+s[j]*i[W]+s[U]*i[K],t[Z]=s[Z]*i[V]+s[J]*i[G]+s[z]*i[Z]+s[W]*i[Q],t[J]=s[Z]*i[q]+s[J]*i[N]+s[z]*i[J]+s[W]*i[$],t[z]=s[Z]*i[H]+s[J]*i[j]+s[z]*i[z]+s[W]*i[ee],t[W]=s[Z]*i[O]+s[J]*i[U]+s[z]*i[W]+s[W]*i[K],t[Q]=s[Q]*i[V]+s[$]*i[G]+s[ee]*i[Z]+s[K]*i[Q],t[$]=s[Q]*i[q]+s[$]*i[N]+s[ee]*i[J]+s[K]*i[$],t[ee]=s[Q]*i[H]+s[$]*i[j]+s[ee]*i[z]+s[K]*i[ee],t[K]=s[Q]*i[O]+s[$]*i[U]+s[ee]*i[W]+s[K]*i[K],this.set(this.temp)}lookAt(e,t,i){let s=Ye.xAxis,n=Ye.yAxis,d=Ye.zAxis;d.setFrom(t).normalize(),s.setFrom(t).normalize(),s.cross(i).normalize(),n.setFrom(s).cross(d).normalize(),this.identity();let l=this.values;return l[V]=s.x,l[q]=s.y,l[H]=s.z,l[G]=n.x,l[N]=n.y,l[j]=n.z,l[Z]=-d.x,l[J]=-d.y,l[z]=-d.z,Ye.tmpMatrix.identity(),Ye.tmpMatrix.values[O]=-e.x,Ye.tmpMatrix.values[U]=-e.y,Ye.tmpMatrix.values[W]=-e.z,this.multiply(Ye.tmpMatrix),this}},De=Ye;P(De,"xAxis",new Ae),P(De,"yAxis",new Ae),P(De,"zAxis",new Ae),P(De,"tmpMatrix",new Ye);var vs=class{position=new Ae(0,0,0);direction=new Ae(0,0,-1);up=new Ae(0,1,0);near=0;far=100;zoom=1;viewportWidth=0;viewportHeight=0;projectionView=new De;inverseProjectionView=new De;projection=new De;view=new De;constructor(e,t){this.viewportWidth=e,this.viewportHeight=t,this.update()}update(){let e=this.projection,t=this.view,i=this.projectionView,s=this.inverseProjectionView,n=this.zoom,d=this.viewportWidth,l=this.viewportHeight;e.ortho(n*(-d/2),n*(d/2),n*(-l/2),n*(l/2),this.near,this.far),t.lookAt(this.position,this.direction,this.up),i.set(e.values),i.multiply(t),s.set(i.values).invert()}screenToWorld(e,t,i){let s=e.x,n=i-e.y-1;return e.x=2*s/t-1,e.y=2*n/i-1,e.z=2*e.z-1,e.project(this.inverseProjectionView),e}worldToScreen(e,t,i){return e.project(this.projectionView),e.x=t*(e.x+1)/2,e.y=i*(e.y+1)/2,e.z=(e.z+1)/2,e}setViewport(e,t){this.viewportWidth=e,this.viewportHeight=t}},ut=class{element;mouseX=0;mouseY=0;buttonDown=!1;touch0=null;touch1=null;initialPinchDistance=0;listeners=new Array;eventListeners=[];constructor(e){this.element=e,this.setupCallbacks(e)}setupCallbacks(e){let t=l=>{if(l instanceof MouseEvent){let r=e.getBoundingClientRect();this.mouseX=l.clientX-r.left,this.mouseY=l.clientY-r.top,this.buttonDown=!0,this.listeners.map(h=>{h.down&&h.down(this.mouseX,this.mouseY)}),document.addEventListener("mousemove",i),document.addEventListener("mouseup",s)}},i=l=>{if(l instanceof MouseEvent){let r=e.getBoundingClientRect();this.mouseX=l.clientX-r.left,this.mouseY=l.clientY-r.top,this.listeners.map(h=>{this.buttonDown?h.dragged&&h.dragged(this.mouseX,this.mouseY):h.moved&&h.moved(this.mouseX,this.mouseY)})}},s=l=>{if(l instanceof MouseEvent){let r=e.getBoundingClientRect();this.mouseX=l.clientX-r.left,this.mouseY=l.clientY-r.top,this.buttonDown=!1,this.listeners.map(h=>{h.up&&h.up(this.mouseX,this.mouseY)}),document.removeEventListener("mousemove",i),document.removeEventListener("mouseup",s)}},n=l=>{l.preventDefault();let r=l.deltaY;l.deltaMode==WheelEvent.DOM_DELTA_LINE&&(r*=8),l.deltaMode==WheelEvent.DOM_DELTA_PAGE&&(r*=24),this.listeners.map(h=>{h.wheel&&h.wheel(l.deltaY)})};e.addEventListener("mousedown",t,!0),e.addEventListener("mousemove",i,!0),e.addEventListener("mouseup",s,!0),e.addEventListener("wheel",n,!0),e.addEventListener("touchstart",l=>{if(!this.touch0||!this.touch1){var r=l.changedTouches;let h=r.item(0);if(!h)return;let a=e.getBoundingClientRect(),o=h.clientX-a.left,c=h.clientY-a.top,u=new ys(h.identifier,o,c);if(this.mouseX=o,this.mouseY=c,this.buttonDown=!0,!this.touch0)this.touch0=u,this.listeners.map(f=>{f.down&&f.down(u.x,u.y)});else if(!this.touch1){this.touch1=u;let f=this.touch1.x-this.touch0.x,m=this.touch1.x-this.touch0.x;this.initialPinchDistance=Math.sqrt(f*f+m*m),this.listeners.map(g=>{g.zoom&&g.zoom(this.initialPinchDistance,this.initialPinchDistance)})}}l.preventDefault()},!1),e.addEventListener("touchmove",l=>{if(this.touch0){var r=l.changedTouches;let o=e.getBoundingClientRect();for(var h=0;h<r.length;h++){var a=r[h];let c=a.clientX-o.left,u=a.clientY-o.top;this.touch0.identifier===a.identifier&&(this.touch0.x=this.mouseX=c,this.touch0.y=this.mouseY=u,this.listeners.map(f=>{f.dragged&&f.dragged(c,u)})),this.touch1&&this.touch1.identifier===a.identifier&&(this.touch1.x=this.mouseX=c,this.touch1.y=this.mouseY=u)}if(this.touch0&&this.touch1){let c=this.touch1.x-this.touch0.x,u=this.touch1.x-this.touch0.x,f=Math.sqrt(c*c+u*u);this.listeners.map(m=>{m.zoom&&m.zoom(this.initialPinchDistance,f)})}}l.preventDefault()},!1);let d=l=>{if(this.touch0){var r=l.changedTouches;let o=e.getBoundingClientRect();for(var h=0;h<r.length;h++){var a=r[h];let c=a.clientX-o.left,u=a.clientY-o.top;if(this.touch0.identifier===a.identifier)if(this.touch0=null,this.mouseX=c,this.mouseY=u,this.listeners.map(f=>{f.up&&f.up(c,u)}),this.touch1)this.touch0=this.touch1,this.touch1=null,this.mouseX=this.touch0.x,this.mouseX=this.touch0.x,this.buttonDown=!0,this.listeners.map(f=>{f.down&&f.down(this.touch0.x,this.touch0.y)});else{this.buttonDown=!1;break}this.touch1&&this.touch1.identifier&&(this.touch1=null)}}l.preventDefault()};e.addEventListener("touchend",d,!1),e.addEventListener("touchcancel",d)}addListener(e){this.listeners.push(e)}removeListener(e){let t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}},ys=class{constructor(e,t,i){this.identifier=e,this.x=t,this.y=i}},Sr=class{constructor(e,t){this.canvas=e,this.camera=t;let i=0,s=0,n=0,d=0,l=0,r=0,h=0,a=0;new ut(e).addListener({down:(o,c)=>{i=t.position.x,s=t.position.y,d=r=o,l=h=c,a=t.zoom},dragged:(o,c)=>{let u=o-d,f=c-l,m=t.screenToWorld(new Ae(0,0),e.clientWidth,e.clientHeight),g=t.screenToWorld(new Ae(u,f),e.clientWidth,e.clientHeight).sub(m);t.position.set(i-g.x,s-g.y,0),t.update(),r=o,h=c},wheel:o=>{let c=o/200*t.zoom,u=t.zoom+c;if(u>0){let f=0,m=0;if(o<0)f=r,m=h;else{let v=new Ae(e.clientWidth/2+15,e.clientHeight/2),p=r-v.x,w=e.clientHeight-1-h-v.y;f=v.x-p,m=e.clientHeight-1-v.y+w}let g=t.screenToWorld(new Ae(f,m),e.clientWidth,e.clientHeight);t.zoom=u,t.update();let x=t.screenToWorld(new Ae(f,m),e.clientWidth,e.clientHeight);t.position.add(g.sub(x)),t.update()}},zoom:(o,c)=>{let u=o/c;t.zoom=a*u},up:(o,c)=>{r=o,h=c},moved:(o,c)=>{r=o,h=c}})}},fe=class{constructor(e,t,i){this.vertexShader=t,this.fragmentShader=i,this.vsSource=t,this.fsSource=i,this.context=e instanceof ge?e:new ge(e),this.context.addRestorable(this),this.compile()}context;vs=null;vsSource;fs=null;fsSource;program=null;tmp2x2=new Float32Array(2*2);tmp3x3=new Float32Array(3*3);tmp4x4=new Float32Array(4*4);getProgram(){return this.program}getVertexShader(){return this.vertexShader}getFragmentShader(){return this.fragmentShader}getVertexShaderSource(){return this.vsSource}getFragmentSource(){return this.fsSource}compile(){let e=this.context.gl;try{if(this.vs=this.compileShader(e.VERTEX_SHADER,this.vertexShader),!this.vs)throw new Error("Couldn't compile vertex shader.");if(this.fs=this.compileShader(e.FRAGMENT_SHADER,this.fragmentShader),!this.fs)throw new Error("Couldn#t compile fragment shader.");this.program=this.compileProgram(this.vs,this.fs)}catch(t){throw this.dispose(),t}}compileShader(e,t){let i=this.context.gl,s=i.createShader(e);if(!s)throw new Error("Couldn't create shader.");if(i.shaderSource(s,t),i.compileShader(s),!i.getShaderParameter(s,i.COMPILE_STATUS)){let n="Couldn't compile shader: "+i.getShaderInfoLog(s);if(i.deleteShader(s),!i.isContextLost())throw new Error(n)}return s}compileProgram(e,t){let i=this.context.gl,s=i.createProgram();if(!s)throw new Error("Couldn't compile program.");if(i.attachShader(s,e),i.attachShader(s,t),i.linkProgram(s),!i.getProgramParameter(s,i.LINK_STATUS)){let n="Couldn't compile shader program: "+i.getProgramInfoLog(s);if(i.deleteProgram(s),!i.isContextLost())throw new Error(n)}return s}restore(){this.compile()}bind(){this.context.gl.useProgram(this.program)}unbind(){this.context.gl.useProgram(null)}setUniformi(e,t){this.context.gl.uniform1i(this.getUniformLocation(e),t)}setUniformf(e,t){this.context.gl.uniform1f(this.getUniformLocation(e),t)}setUniform2f(e,t,i){this.context.gl.uniform2f(this.getUniformLocation(e),t,i)}setUniform3f(e,t,i,s){this.context.gl.uniform3f(this.getUniformLocation(e),t,i,s)}setUniform4f(e,t,i,s,n){this.context.gl.uniform4f(this.getUniformLocation(e),t,i,s,n)}setUniform2x2f(e,t){let i=this.context.gl;this.tmp2x2.set(t),i.uniformMatrix2fv(this.getUniformLocation(e),!1,this.tmp2x2)}setUniform3x3f(e,t){let i=this.context.gl;this.tmp3x3.set(t),i.uniformMatrix3fv(this.getUniformLocation(e),!1,this.tmp3x3)}setUniform4x4f(e,t){let i=this.context.gl;this.tmp4x4.set(t),i.uniformMatrix4fv(this.getUniformLocation(e),!1,this.tmp4x4)}getUniformLocation(e){let t=this.context.gl;if(!this.program)throw new Error("Shader not compiled.");let i=t.getUniformLocation(this.program,e);if(!i&&!t.isContextLost())throw new Error(`Couldn't find location for uniform ${e}`);return i}getAttributeLocation(e){let t=this.context.gl;if(!this.program)throw new Error("Shader not compiled.");let i=t.getAttribLocation(this.program,e);if(i==-1&&!t.isContextLost())throw new Error(`Couldn't find location for attribute ${e}`);return i}dispose(){this.context.removeRestorable(this);let e=this.context.gl;this.vs&&(e.deleteShader(this.vs),this.vs=null),this.fs&&(e.deleteShader(this.fs),this.fs=null),this.program&&(e.deleteProgram(this.program),this.program=null)}static newColoredTextured(e){let t=`
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
attribute vec2 ${fe.TEXCOORDS};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_color;
varying vec2 v_texCoords;

void main () {
	v_color = ${fe.COLOR};
	v_texCoords = ${fe.TEXCOORDS};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,i=`
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_color;
varying vec2 v_texCoords;
uniform sampler2D u_texture;

void main () {
	gl_FragColor = v_color * texture2D(u_texture, v_texCoords);
}
`;return new fe(e,t,i)}static newTwoColoredTextured(e){let t=`
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
attribute vec4 ${fe.COLOR2};
attribute vec2 ${fe.TEXCOORDS};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_light;
varying vec4 v_dark;
varying vec2 v_texCoords;

void main () {
	v_light = ${fe.COLOR};
	v_dark = ${fe.COLOR2};
	v_texCoords = ${fe.TEXCOORDS};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,i=`
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_light;
varying LOWP vec4 v_dark;
varying vec2 v_texCoords;
uniform sampler2D u_texture;

void main () {
	vec4 texColor = texture2D(u_texture, v_texCoords);
	gl_FragColor.a = texColor.a * v_light.a;
	gl_FragColor.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;
}
`;return new fe(e,t,i)}static newColored(e){let t=`
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_color;

void main () {
	v_color = ${fe.COLOR};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,i=`
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_color;

void main () {
	gl_FragColor = v_color;
}
`;return new fe(e,t,i)}},pe=fe;P(pe,"MVP_MATRIX","u_projTrans"),P(pe,"POSITION","a_position"),P(pe,"COLOR","a_color"),P(pe,"COLOR2","a_color2"),P(pe,"TEXCOORDS","a_texCoords"),P(pe,"SAMPLER","u_texture");var Bi=class{constructor(e,t,i,s){this.attributes=t,this.context=e instanceof ge?e:new ge(e),this.elementsPerVertex=0;for(let n=0;n<t.length;n++)this.elementsPerVertex+=t[n].numElements;this.vertices=new Float32Array(i*this.elementsPerVertex),this.indices=new Uint16Array(s),this.context.addRestorable(this)}context;vertices;verticesBuffer=null;verticesLength=0;dirtyVertices=!1;indices;indicesBuffer=null;indicesLength=0;dirtyIndices=!1;elementsPerVertex=0;getAttributes(){return this.attributes}maxVertices(){return this.vertices.length/this.elementsPerVertex}numVertices(){return this.verticesLength/this.elementsPerVertex}setVerticesLength(e){this.dirtyVertices=!0,this.verticesLength=e}getVertices(){return this.vertices}maxIndices(){return this.indices.length}numIndices(){return this.indicesLength}setIndicesLength(e){this.dirtyIndices=!0,this.indicesLength=e}getIndices(){return this.indices}getVertexSizeInFloats(){let e=0;for(var t=0;t<this.attributes.length;t++){let i=this.attributes[t];e+=i.numElements}return e}setVertices(e){if(this.dirtyVertices=!0,e.length>this.vertices.length)throw Error("Mesh can't store more than "+this.maxVertices()+" vertices");this.vertices.set(e,0),this.verticesLength=e.length}setIndices(e){if(this.dirtyIndices=!0,e.length>this.indices.length)throw Error("Mesh can't store more than "+this.maxIndices()+" indices");this.indices.set(e,0),this.indicesLength=e.length}draw(e,t){this.drawWithOffset(e,t,0,this.indicesLength>0?this.indicesLength:this.verticesLength/this.elementsPerVertex)}drawWithOffset(e,t,i,s){let n=this.context.gl;(this.dirtyVertices||this.dirtyIndices)&&this.update(),this.bind(e),this.indicesLength>0?n.drawElements(t,s,n.UNSIGNED_SHORT,i*2):n.drawArrays(t,i,s),this.unbind(e)}bind(e){let t=this.context.gl;t.bindBuffer(t.ARRAY_BUFFER,this.verticesBuffer);let i=0;for(let s=0;s<this.attributes.length;s++){let n=this.attributes[s],d=e.getAttributeLocation(n.name);t.enableVertexAttribArray(d),t.vertexAttribPointer(d,n.numElements,t.FLOAT,!1,this.elementsPerVertex*4,i*4),i+=n.numElements}this.indicesLength>0&&t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indicesBuffer)}unbind(e){let t=this.context.gl;for(let i=0;i<this.attributes.length;i++){let s=this.attributes[i],n=e.getAttributeLocation(s.name);t.disableVertexAttribArray(n)}t.bindBuffer(t.ARRAY_BUFFER,null),this.indicesLength>0&&t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,null)}update(){let e=this.context.gl;this.dirtyVertices&&(this.verticesBuffer||(this.verticesBuffer=e.createBuffer()),e.bindBuffer(e.ARRAY_BUFFER,this.verticesBuffer),e.bufferData(e.ARRAY_BUFFER,this.vertices.subarray(0,this.verticesLength),e.DYNAMIC_DRAW),this.dirtyVertices=!1),this.dirtyIndices&&(this.indicesBuffer||(this.indicesBuffer=e.createBuffer()),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indicesBuffer),e.bufferData(e.ELEMENT_ARRAY_BUFFER,this.indices.subarray(0,this.indicesLength),e.DYNAMIC_DRAW),this.dirtyIndices=!1)}restore(){this.verticesBuffer=null,this.indicesBuffer=null,this.update()}dispose(){this.context.removeRestorable(this);let e=this.context.gl;e.deleteBuffer(this.verticesBuffer),e.deleteBuffer(this.indicesBuffer)}},tt=class{constructor(e,t,i){this.name=e,this.type=t,this.numElements=i}},Xt=class extends tt{constructor(){super(pe.POSITION,Ze.Float,2)}},Tr=class extends tt{constructor(){super(pe.POSITION,Ze.Float,3)}},Pi=class extends tt{constructor(e=0){super(pe.TEXCOORDS+(e==0?"":e),Ze.Float,2)}},Bt=class extends tt{constructor(){super(pe.COLOR,Ze.Float,4)}},As=class extends tt{constructor(){super(pe.COLOR2,Ze.Float,4)}},Ze=(e=>(e[e.Float=0]="Float",e))(Ze||{}),Ve=1,kr=769,Cs=770,Ss=771,Ts=774,Je=class{context;drawCalls=0;isDrawing=!1;mesh;shader=null;lastTexture=null;verticesLength=0;indicesLength=0;srcColorBlend;srcAlphaBlend;dstBlend;cullWasEnabled=!1;constructor(e,t=!0,i=10920){if(i>10920)throw new Error("Can't have more than 10920 triangles per batch: "+i);this.context=e instanceof ge?e:new ge(e);let s=t?[new Xt,new Bt,new Pi,new As]:[new Xt,new Bt,new Pi];this.mesh=new Bi(e,s,i,i*3);let n=this.context.gl;this.srcColorBlend=n.SRC_ALPHA,this.srcAlphaBlend=n.ONE,this.dstBlend=n.ONE_MINUS_SRC_ALPHA}begin(e){if(this.isDrawing)throw new Error("PolygonBatch is already drawing. Call PolygonBatch.end() before calling PolygonBatch.begin()");this.drawCalls=0,this.shader=e,this.lastTexture=null,this.isDrawing=!0;let t=this.context.gl;t.enable(t.BLEND),t.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend),Je.disableCulling&&(this.cullWasEnabled=t.isEnabled(t.CULL_FACE),this.cullWasEnabled&&t.disable(t.CULL_FACE))}setBlendMode(e,t){const i=Je.blendModesGL[e],s=t?i.srcRgbPma:i.srcRgb,n=i.srcAlpha,d=i.dstRgb;if(this.srcColorBlend==s&&this.srcAlphaBlend==n&&this.dstBlend==d)return;this.srcColorBlend=s,this.srcAlphaBlend=n,this.dstBlend=d,this.isDrawing&&this.flush(),this.context.gl.blendFuncSeparate(s,d,n,d)}draw(e,t,i){e!=this.lastTexture?(this.flush(),this.lastTexture=e):(this.verticesLength+t.length>this.mesh.getVertices().length||this.indicesLength+i.length>this.mesh.getIndices().length)&&this.flush();let s=this.mesh.numVertices();this.mesh.getVertices().set(t,this.verticesLength),this.verticesLength+=t.length,this.mesh.setVerticesLength(this.verticesLength);let n=this.mesh.getIndices();for(let d=this.indicesLength,l=0;l<i.length;d++,l++)n[d]=i[l]+s;this.indicesLength+=i.length,this.mesh.setIndicesLength(this.indicesLength)}flush(){if(this.verticesLength!=0){if(!this.lastTexture)throw new Error("No texture set.");if(!this.shader)throw new Error("No shader set.");this.lastTexture.bind(),this.mesh.draw(this.shader,this.context.gl.TRIANGLES),this.verticesLength=0,this.indicesLength=0,this.mesh.setVerticesLength(0),this.mesh.setIndicesLength(0),this.drawCalls++,Je.globalDrawCalls++}}end(){if(!this.isDrawing)throw new Error("PolygonBatch is not drawing. Call PolygonBatch.begin() before calling PolygonBatch.end()");(this.verticesLength>0||this.indicesLength>0)&&this.flush(),this.shader=null,this.lastTexture=null,this.isDrawing=!1;let e=this.context.gl;e.disable(e.BLEND),Je.disableCulling&&this.cullWasEnabled&&e.enable(e.CULL_FACE)}getDrawCalls(){return this.drawCalls}static getAndResetGlobalDrawCalls(){let e=Je.globalDrawCalls;return Je.globalDrawCalls=0,e}dispose(){this.mesh.dispose()}},it=Je;P(it,"disableCulling",!1),P(it,"globalDrawCalls",0),P(it,"blendModesGL",[{srcRgb:Cs,srcRgbPma:Ve,dstRgb:Ss,srcAlpha:Ve},{srcRgb:Cs,srcRgbPma:Ve,dstRgb:Ve,srcAlpha:Ve},{srcRgb:Ts,srcRgbPma:Ts,dstRgb:Ss,srcAlpha:Ve},{srcRgb:Ve,srcRgbPma:Ve,dstRgb:kr,srcAlpha:Ve}]);var Di=class{context;isDrawing=!1;mesh;shapeType=be.Filled;color=new D(1,1,1,1);shader=null;vertexIndex=0;tmp=new Te;srcColorBlend;srcAlphaBlend;dstBlend;constructor(e,t=10920){if(t>10920)throw new Error("Can't have more than 10920 triangles per batch: "+t);this.context=e instanceof ge?e:new ge(e),this.mesh=new Bi(e,[new Xt,new Bt],t,0);let i=this.context.gl;this.srcColorBlend=i.SRC_ALPHA,this.srcAlphaBlend=i.ONE,this.dstBlend=i.ONE_MINUS_SRC_ALPHA}begin(e){if(this.isDrawing)throw new Error("ShapeRenderer.begin() has already been called");this.shader=e,this.vertexIndex=0,this.isDrawing=!0;let t=this.context.gl;t.enable(t.BLEND),t.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend)}setBlendMode(e,t,i){this.srcColorBlend=e,this.srcAlphaBlend=t,this.dstBlend=i,this.isDrawing&&(this.flush(),this.context.gl.blendFuncSeparate(e,i,t,i))}setColor(e){this.color.setFromColor(e)}setColorWith(e,t,i,s){this.color.set(e,t,i,s)}point(e,t,i){this.check(be.Point,1),i||(i=this.color),this.vertex(e,t,i)}line(e,t,i,s,n){this.check(be.Line,2);let d=this.mesh.getVertices(),l=this.vertexIndex;n||(n=this.color),this.vertex(e,t,n),this.vertex(i,s,n)}triangle(e,t,i,s,n,d,l,r,h,a){this.check(e?be.Filled:be.Line,3);let o=this.mesh.getVertices(),c=this.vertexIndex;r||(r=this.color),h||(h=this.color),a||(a=this.color),e?(this.vertex(t,i,r),this.vertex(s,n,h),this.vertex(d,l,a)):(this.vertex(t,i,r),this.vertex(s,n,h),this.vertex(s,n,r),this.vertex(d,l,h),this.vertex(d,l,r),this.vertex(t,i,h))}quad(e,t,i,s,n,d,l,r,h,a,o,c,u){this.check(e?be.Filled:be.Line,3);let f=this.mesh.getVertices(),m=this.vertexIndex;a||(a=this.color),o||(o=this.color),c||(c=this.color),u||(u=this.color),e?(this.vertex(t,i,a),this.vertex(s,n,o),this.vertex(d,l,c),this.vertex(d,l,c),this.vertex(r,h,u),this.vertex(t,i,a)):(this.vertex(t,i,a),this.vertex(s,n,o),this.vertex(s,n,o),this.vertex(d,l,c),this.vertex(d,l,c),this.vertex(r,h,u),this.vertex(r,h,u),this.vertex(t,i,a))}rect(e,t,i,s,n,d){this.quad(e,t,i,t+s,i,t+s,i+n,t,i+n,d,d,d,d)}rectLine(e,t,i,s,n,d,l){this.check(e?be.Filled:be.Line,8),l||(l=this.color);let r=this.tmp.set(n-i,t-s);r.normalize(),d*=.5;let h=r.x*d,a=r.y*d;e?(this.vertex(t+h,i+a,l),this.vertex(t-h,i-a,l),this.vertex(s+h,n+a,l),this.vertex(s-h,n-a,l),this.vertex(s+h,n+a,l),this.vertex(t-h,i-a,l)):(this.vertex(t+h,i+a,l),this.vertex(t-h,i-a,l),this.vertex(s+h,n+a,l),this.vertex(s-h,n-a,l),this.vertex(s+h,n+a,l),this.vertex(t+h,i+a,l),this.vertex(s-h,n-a,l),this.vertex(t-h,i-a,l))}x(e,t,i){this.line(e-i,t-i,e+i,t+i),this.line(e-i,t+i,e+i,t-i)}polygon(e,t,i,s){if(i<3)throw new Error("Polygon must contain at least 3 vertices");this.check(be.Line,i*2),s||(s=this.color);let n=this.mesh.getVertices(),d=this.vertexIndex;t<<=1,i<<=1;let l=e[t],r=e[t+1],h=t+i;for(let a=t,o=t+i-2;a<o;a+=2){let c=e[a],u=e[a+1],f=0,m=0;a+2>=h?(f=l,m=r):(f=e[a+2],m=e[a+3]),this.vertex(c,u,s),this.vertex(f,m,s)}}circle(e,t,i,s,n,d=0){if(d==0&&(d=Math.max(1,6*F.cbrt(s)|0)),d<=0)throw new Error("segments must be > 0.");n||(n=this.color);let l=2*F.PI/d,r=Math.cos(l),h=Math.sin(l),a=s,o=0;if(e){this.check(be.Filled,d*3+3),d--;for(let u=0;u<d;u++){this.vertex(t,i,n),this.vertex(t+a,i+o,n);let f=a;a=r*a-h*o,o=h*f+r*o,this.vertex(t+a,i+o,n)}this.vertex(t,i,n),this.vertex(t+a,i+o,n)}else{this.check(be.Line,d*2+2);for(let u=0;u<d;u++){this.vertex(t+a,i+o,n);let f=a;a=r*a-h*o,o=h*f+r*o,this.vertex(t+a,i+o,n)}this.vertex(t+a,i+o,n)}let c=a;a=s,o=0,this.vertex(t+a,i+o,n)}curve(e,t,i,s,n,d,l,r,h,a){this.check(be.Line,h*2+2),a||(a=this.color);let o=1/h,c=o*o,u=o*o*o,f=3*o,m=3*c,g=6*c,x=6*u,v=e-i*2+n,p=t-s*2+d,w=(i-n)*3-e+l,b=(s-d)*3-t+r,y=e,C=t,A=(i-e)*f+v*m+w*u,T=(s-t)*f+p*m+b*u,M=v*g+w*x,Y=p*g+b*x,X=w*x,L=b*x;for(;h-- >0;)this.vertex(y,C,a),y+=A,C+=T,A+=M,T+=Y,M+=X,Y+=L,this.vertex(y,C,a);this.vertex(y,C,a),this.vertex(l,r,a)}vertex(e,t,i){let s=this.vertexIndex,n=this.mesh.getVertices();n[s++]=e,n[s++]=t,n[s++]=i.r,n[s++]=i.g,n[s++]=i.b,n[s++]=i.a,this.vertexIndex=s}end(){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");this.flush();let e=this.context.gl;e.disable(e.BLEND),this.isDrawing=!1}flush(){if(this.vertexIndex!=0){if(!this.shader)throw new Error("No shader set.");this.mesh.setVerticesLength(this.vertexIndex),this.mesh.draw(this.shader,this.shapeType),this.vertexIndex=0}}check(e,t){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");if(this.shapeType==e)if(this.mesh.maxVertices()-this.mesh.numVertices()<t)this.flush();else return;else this.flush(),this.shapeType=e}dispose(){this.mesh.dispose()}},be=(e=>(e[e.Point=0]="Point",e[e.Line=1]="Line",e[e.Filled=4]="Filled",e))(be||{}),Vi=class{boneLineColor=new D(1,0,0,1);boneOriginColor=new D(0,1,0,1);attachmentLineColor=new D(0,0,1,.5);triangleLineColor=new D(1,.64,0,.5);pathColor=new D().setFromString("FF7F00");clipColor=new D(.8,0,0,2);aabbColor=new D(0,1,0,.5);drawBones=!0;drawRegionAttachments=!0;drawBoundingBoxes=!0;drawMeshHull=!0;drawMeshTriangles=!0;drawPaths=!0;drawSkeletonXY=!1;drawClipping=!0;premultipliedAlpha=!1;scale=1;boneWidth=2;context;bounds=new xs;temp=new Array;vertices=B.newFloatArray(2*1024);constructor(e){this.context=e instanceof ge?e:new ge(e)}draw(e,t,i){let s=t.x,n=t.y,d=this.context.gl,l=this.premultipliedAlpha?d.ONE:d.SRC_ALPHA;e.setBlendMode(l,d.ONE,d.ONE_MINUS_SRC_ALPHA);let r=t.bones;if(this.drawBones){e.setColor(this.boneLineColor);for(let h=0,a=r.length;h<a;h++){let o=r[h];if(i&&i.indexOf(o.data.name)>-1||!o.parent)continue;let c=o.data.length*o.a+o.worldX,u=o.data.length*o.c+o.worldY;e.rectLine(!0,o.worldX,o.worldY,c,u,this.boneWidth*this.scale)}this.drawSkeletonXY&&e.x(s,n,4*this.scale)}if(this.drawRegionAttachments){e.setColor(this.attachmentLineColor);let h=t.slots;for(let a=0,o=h.length;a<o;a++){let c=h[a],u=c.getAttachment();if(u instanceof ae){let f=u,m=this.vertices;f.computeWorldVertices(c,m,0,2),e.line(m[0],m[1],m[2],m[3]),e.line(m[2],m[3],m[4],m[5]),e.line(m[4],m[5],m[6],m[7]),e.line(m[6],m[7],m[0],m[1])}}}if(this.drawMeshHull||this.drawMeshTriangles){let h=t.slots;for(let a=0,o=h.length;a<o;a++){let c=h[a];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof Ue))continue;let f=u,m=this.vertices;f.computeWorldVertices(c,0,f.worldVerticesLength,m,0,2);let g=f.triangles,x=f.hullLength;if(this.drawMeshTriangles){e.setColor(this.triangleLineColor);for(let v=0,p=g.length;v<p;v+=3){let w=g[v]*2,b=g[v+1]*2,y=g[v+2]*2;e.triangle(!1,m[w],m[w+1],m[b],m[b+1],m[y],m[y+1])}}if(this.drawMeshHull&&x>0){e.setColor(this.attachmentLineColor),x=(x>>1)*2;let v=m[x-2],p=m[x-1];for(let w=0,b=x;w<b;w+=2){let y=m[w],C=m[w+1];e.line(y,C,v,p),v=y,p=C}}}}if(this.drawBoundingBoxes){let h=this.bounds;h.update(t,!0),e.setColor(this.aabbColor),e.rect(!1,h.minX,h.minY,h.getWidth(),h.getHeight());let a=h.polygons,o=h.boundingBoxes;for(let c=0,u=a.length;c<u;c++){let f=a[c];e.setColor(o[c].color),e.polygon(f,0,f.length)}}if(this.drawPaths){let h=t.slots;for(let a=0,o=h.length;a<o;a++){let c=h[a];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof je))continue;let f=u,m=f.worldVerticesLength,g=this.temp=B.setArraySize(this.temp,m,0);f.computeWorldVertices(c,0,m,g,0,2);let x=this.pathColor,v=g[2],p=g[3],w=0,b=0;if(f.closed){e.setColor(x);let y=g[0],C=g[1],A=g[m-2],T=g[m-1];w=g[m-4],b=g[m-3],e.curve(v,p,y,C,A,T,w,b,32),e.setColor(Vi.LIGHT_GRAY),e.line(v,p,y,C),e.line(w,b,A,T)}m-=4;for(let y=4;y<m;y+=6){let C=g[y],A=g[y+1],T=g[y+2],M=g[y+3];w=g[y+4],b=g[y+5],e.setColor(x),e.curve(v,p,C,A,T,M,w,b,32),e.setColor(Vi.LIGHT_GRAY),e.line(v,p,C,A),e.line(w,b,T,M),v=w,p=b}}}if(this.drawBones){e.setColor(this.boneOriginColor);for(let h=0,a=r.length;h<a;h++){let o=r[h];i&&i.indexOf(o.data.name)>-1||e.circle(!0,o.worldX,o.worldY,3*this.scale,this.boneOriginColor,8)}}if(this.drawClipping){let h=t.slots;e.setColor(this.clipColor);for(let a=0,o=h.length;a<o;a++){let c=h[a];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof dt))continue;let f=u,m=f.worldVerticesLength,g=this.temp=B.setArraySize(this.temp,m,0);f.computeWorldVertices(c,0,m,g,0,2);for(let x=0,v=g.length;x<v;x+=2){let p=g[x],w=g[x+1],b=g[(x+2)%g.length],y=g[(x+3)%g.length];e.line(p,w,b,y)}}}}dispose(){}},Pt=Vi;P(Pt,"LIGHT_GRAY",new D(.7529411764705882,.7529411764705882,.7529411764705882,1)),P(Pt,"GREEN",new D(0,1,0,1));var Er=class{constructor(e,t,i){this.vertices=e,this.numVertices=t,this.numFloats=i}},ks=class{premultipliedAlpha=!1;tempColor=new D;tempColor2=new D;vertices;vertexSize=2+2+4;twoColorTint=!1;renderable=new Er([],0,0);clipper=new Ft;temp=new Te;temp2=new Te;temp3=new D;temp4=new D;constructor(e,t=!0){this.twoColorTint=t,t&&(this.vertexSize+=4),this.vertices=B.newFloatArray(this.vertexSize*1024)}draw(e,t,i=-1,s=-1,n=null){let d=this.clipper,l=this.premultipliedAlpha,r=this.twoColorTint,h=null,a=this.renderable,o,c,u=t.drawOrder,f,m=t.color,g=r?12:8,x=!1;i==-1&&(x=!0);for(let v=0,p=u.length;v<p;v++){let w=d.isClipping()?2:g,b=u[v];if(!b.bone.active){d.clipEndWithSlot(b);continue}if(i>=0&&i==b.data.index&&(x=!0),!x){d.clipEndWithSlot(b);continue}s>=0&&s==b.data.index&&(x=!1);let y=b.getAttachment(),C;if(y instanceof ae){let A=y;a.vertices=this.vertices,a.numVertices=4,a.numFloats=w<<2,A.computeWorldVertices(b,a.vertices,0,w),c=ks.QUAD_TRIANGLES,o=A.uvs,C=A.region.texture,f=A.color}else if(y instanceof Ue){let A=y;a.vertices=this.vertices,a.numVertices=A.worldVerticesLength>>1,a.numFloats=a.numVertices*w,a.numFloats>a.vertices.length&&(a.vertices=this.vertices=B.newFloatArray(a.numFloats)),A.computeWorldVertices(b,0,A.worldVerticesLength,a.vertices,0,w),c=A.triangles,C=A.region.texture,o=A.uvs,f=A.color}else if(y instanceof dt){let A=y;d.clipStart(b,A);continue}else{d.clipEndWithSlot(b);continue}if(C){let A=b.color,T=this.tempColor;T.r=m.r*A.r*f.r,T.g=m.g*A.g*f.g,T.b=m.b*A.b*f.b,T.a=m.a*A.a*f.a,l&&(T.r*=T.a,T.g*=T.a,T.b*=T.a);let M=this.tempColor2;b.darkColor?(l?(M.r=b.darkColor.r*T.a,M.g=b.darkColor.g*T.a,M.b=b.darkColor.b*T.a):M.setFromColor(b.darkColor),M.a=l?1:0):M.set(0,0,0,1);let Y=b.data.blendMode;if(Y!=h&&(h=Y,e.setBlendMode(h,l)),d.isClipping()){d.clipTriangles(a.vertices,a.numFloats,c,c.length,o,T,M,r);let X=new Float32Array(d.clippedVertices),L=d.clippedTriangles;n&&n(X,X.length,g),e.draw(C,X,L)}else{let X=a.vertices;if(r)for(let R=2,I=0,k=a.numFloats;R<k;R+=g,I+=2)X[R]=T.r,X[R+1]=T.g,X[R+2]=T.b,X[R+3]=T.a,X[R+4]=o[I],X[R+5]=o[I+1],X[R+6]=M.r,X[R+7]=M.g,X[R+8]=M.b,X[R+9]=M.a;else for(let R=2,I=0,k=a.numFloats;R<k;R+=g,I+=2)X[R]=T.r,X[R+1]=T.g,X[R+2]=T.b,X[R+3]=T.a,X[R+4]=o[I],X[R+5]=o[I+1];let L=a.vertices.subarray(0,a.numFloats);n&&n(a.vertices,a.numFloats,g),e.draw(C,L,c)}}d.clipEndWithSlot(b)}d.clipEnd()}},Oi=ks;P(Oi,"QUAD_TRIANGLES",[0,1,2,2,3,0]);var S=[0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0],Dt=[0,1,2,2,3,0],Vt=new D(1,1,1,1),Ni=class{context;canvas;camera;batcher;twoColorTint=!1;batcherShader;shapes;shapesShader;activeRenderer=null;skeletonRenderer;skeletonDebugRenderer;constructor(e,t,i=!0){this.canvas=e,this.context=t instanceof ge?t:new ge(t),this.twoColorTint=i,this.camera=new vs(e.width,e.height),this.batcherShader=i?pe.newTwoColoredTextured(this.context):pe.newColoredTextured(this.context),this.batcher=new it(this.context,i),this.shapesShader=pe.newColored(this.context),this.shapes=new Di(this.context),this.skeletonRenderer=new Oi(this.context,i),this.skeletonDebugRenderer=new Pt(this.context)}dispose(){this.batcher.dispose(),this.batcherShader.dispose(),this.shapes.dispose(),this.shapesShader.dispose(),this.skeletonDebugRenderer.dispose()}begin(){this.camera.update(),this.enableRenderer(this.batcher)}drawSkeleton(e,t=!1,i=-1,s=-1,n=null){this.enableRenderer(this.batcher),this.skeletonRenderer.premultipliedAlpha=t,this.skeletonRenderer.draw(this.batcher,e,i,s,n)}drawSkeletonDebug(e,t=!1,i){this.enableRenderer(this.shapes),this.skeletonDebugRenderer.premultipliedAlpha=t,this.skeletonDebugRenderer.draw(this.shapes,e,i)}drawTexture(e,t,i,s,n,d){this.enableRenderer(this.batcher),d||(d=Vt);var l=0;S[l++]=t,S[l++]=i,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=0,S[l++]=1,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t+s,S[l++]=i,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=1,S[l++]=1,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t+s,S[l++]=i+n,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=1,S[l++]=0,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t,S[l++]=i+n,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=0,S[l++]=0,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l]=0),this.batcher.draw(e,S,Dt)}drawTextureUV(e,t,i,s,n,d,l,r,h,a){this.enableRenderer(this.batcher),a||(a=Vt);var o=0;S[o++]=t,S[o++]=i,S[o++]=a.r,S[o++]=a.g,S[o++]=a.b,S[o++]=a.a,S[o++]=d,S[o++]=l,this.twoColorTint&&(S[o++]=0,S[o++]=0,S[o++]=0,S[o++]=0),S[o++]=t+s,S[o++]=i,S[o++]=a.r,S[o++]=a.g,S[o++]=a.b,S[o++]=a.a,S[o++]=r,S[o++]=l,this.twoColorTint&&(S[o++]=0,S[o++]=0,S[o++]=0,S[o++]=0),S[o++]=t+s,S[o++]=i+n,S[o++]=a.r,S[o++]=a.g,S[o++]=a.b,S[o++]=a.a,S[o++]=r,S[o++]=h,this.twoColorTint&&(S[o++]=0,S[o++]=0,S[o++]=0,S[o++]=0),S[o++]=t,S[o++]=i+n,S[o++]=a.r,S[o++]=a.g,S[o++]=a.b,S[o++]=a.a,S[o++]=d,S[o++]=h,this.twoColorTint&&(S[o++]=0,S[o++]=0,S[o++]=0,S[o]=0),this.batcher.draw(e,S,Dt)}drawTextureRotated(e,t,i,s,n,d,l,r,h){this.enableRenderer(this.batcher),h||(h=Vt);let a=t+d,o=i+l,c=-d,u=-l,f=s-d,m=n-l,g=c,x=u,v=c,p=m,w=f,b=m,y=f,C=u,A=0,T=0,M=0,Y=0,X=0,L=0,R=0,I=0;if(r!=0){let _=F.cosDeg(r),se=F.sinDeg(r);A=_*g-se*x,T=se*g+_*x,R=_*v-se*p,I=se*v+_*p,X=_*w-se*b,L=se*w+_*b,M=X+(A-R),Y=L+(T-I)}else A=g,T=x,R=v,I=p,X=w,L=b,M=y,Y=C;A+=a,T+=o,M+=a,Y+=o,X+=a,L+=o,R+=a,I+=o;var k=0;S[k++]=A,S[k++]=T,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=0,S[k++]=1,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=M,S[k++]=Y,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=1,S[k++]=1,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=X,S[k++]=L,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=1,S[k++]=0,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=R,S[k++]=I,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=0,S[k++]=0,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k]=0),this.batcher.draw(e,S,Dt)}drawRegion(e,t,i,s,n,d){this.enableRenderer(this.batcher),d||(d=Vt);var l=0;S[l++]=t,S[l++]=i,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=e.u,S[l++]=e.v2,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t+s,S[l++]=i,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=e.u2,S[l++]=e.v2,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t+s,S[l++]=i+n,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=e.u2,S[l++]=e.v,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=t,S[l++]=i+n,S[l++]=d.r,S[l++]=d.g,S[l++]=d.b,S[l++]=d.a,S[l++]=e.u,S[l++]=e.v,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l]=0),this.batcher.draw(e.page.texture,S,Dt)}line(e,t,i,s,n,d){this.enableRenderer(this.shapes),this.shapes.line(e,t,i,s,n)}triangle(e,t,i,s,n,d,l,r,h,a){this.enableRenderer(this.shapes),this.shapes.triangle(e,t,i,s,n,d,l,r,h,a)}quad(e,t,i,s,n,d,l,r,h,a,o,c,u){this.enableRenderer(this.shapes),this.shapes.quad(e,t,i,s,n,d,l,r,h,a,o,c,u)}rect(e,t,i,s,n,d){this.enableRenderer(this.shapes),this.shapes.rect(e,t,i,s,n,d)}rectLine(e,t,i,s,n,d,l){this.enableRenderer(this.shapes),this.shapes.rectLine(e,t,i,s,n,d,l)}polygon(e,t,i,s){this.enableRenderer(this.shapes),this.shapes.polygon(e,t,i,s)}circle(e,t,i,s,n,d=0){this.enableRenderer(this.shapes),this.shapes.circle(e,t,i,s,n,d)}curve(e,t,i,s,n,d,l,r,h,a){this.enableRenderer(this.shapes),this.shapes.curve(e,t,i,s,n,d,l,r,h,a)}end(){this.activeRenderer===this.batcher?this.batcher.end():this.activeRenderer===this.shapes&&this.shapes.end(),this.activeRenderer=null}resize(e){let t=this.canvas;var i=window.devicePixelRatio||1,s=Math.round(t.clientWidth*i),n=Math.round(t.clientHeight*i);if((t.width!=s||t.height!=n)&&(t.width=s,t.height=n),this.context.gl.viewport(0,0,t.width,t.height),e===Ot.Expand)this.camera.setViewport(s,n);else if(e===Ot.Fit){let d=t.width,l=t.height,r=this.camera.viewportWidth,h=this.camera.viewportHeight,a=h/r,o=l/d,c=a<o?r/d:h/l;this.camera.setViewport(d*c,l*c)}this.camera.update()}enableRenderer(e){this.activeRenderer!==e&&(this.end(),e instanceof it?(this.batcherShader.bind(),this.batcherShader.setUniform4x4f(pe.MVP_MATRIX,this.camera.projectionView.values),this.batcherShader.setUniformi("u_texture",0),this.batcher.begin(this.batcherShader),this.activeRenderer=this.batcher):e instanceof Di?(this.shapesShader.bind(),this.shapesShader.setUniform4x4f(pe.MVP_MATRIX,this.camera.projectionView.values),this.shapes.begin(this.shapesShader),this.activeRenderer=this.shapes):this.activeRenderer=this.skeletonDebugRenderer)}},Ot=(e=>(e[e.Stretch=0]="Stretch",e[e.Expand=1]="Expand",e[e.Fit=2]="Fit",e))(Ot||{}),ft,st,Es=0,Ir=1,Ui=1,Is=165,Ms=108,Ke=163,Rs=class{renderer;logo=null;spinner=null;angle=0;fadeOut=0;fadeIn=0;timeKeeper=new pt;backgroundColor=new D(.135,.135,.135,1);tempColor=new D;constructor(e){if(this.renderer=e,this.timeKeeper.maxDelta=9,!st){let t=navigator.userAgent.indexOf("Safari")>-1,i=()=>Es++;st=new Image,st.src=Rr,t||(st.crossOrigin="anonymous"),st.onload=i,ft=new Image,ft.src=Mr,t||(ft.crossOrigin="anonymous"),ft.onload=i}}dispose(){this.logo?.dispose(),this.spinner?.dispose()}draw(e=!1){if(Es<2||e&&this.fadeOut>Ui)return;this.timeKeeper.update();let t=Math.abs(Math.sin(this.timeKeeper.totalTime+.25));this.angle-=this.timeKeeper.delta*200*(1+1.5*Math.pow(t,5));let i=this.tempColor,s=this.renderer,n=s.canvas,d=s.context.gl;if(s.resize(1),s.camera.position.set(n.width/2,n.height/2,0),s.batcher.setBlendMode(0,!0),e){if(this.fadeOut+=this.timeKeeper.delta*(this.timeKeeper.totalTime<1?2:1),this.fadeOut>Ui)return;i.setFromColor(this.backgroundColor),t=1-this.fadeOut/Ui,t=1-(t-1)*(t-1),i.a*=t,i.a>0&&(s.camera.zoom=1,s.begin(),s.quad(!0,0,0,n.width,0,n.width,n.height,0,n.height,i,i,i,i),s.end())}else this.fadeIn+=this.timeKeeper.delta,this.backgroundColor.a>0&&(d.clearColor(this.backgroundColor.r,this.backgroundColor.g,this.backgroundColor.b,this.backgroundColor.a),d.clear(d.COLOR_BUFFER_BIT)),t=1;t*=Math.min(this.fadeIn/Ir,1),i.set(t,t,t,t),this.logo||(this.logo=new ct(s.context,st),this.spinner=new ct(s.context,ft)),s.camera.zoom=Math.max(1,Ke/n.height),s.begin(),s.drawTexture(this.logo,(n.width-Is)/2,(n.height-Ms)/2,Is,Ms,i),this.spinner&&s.drawTextureRotated(this.spinner,(n.width-Ke)/2,(n.height-Ke)/2,Ke,Ke,Ke/2,Ke/2,this.angle,i),s.end()}},Mr="data:image/png;base64,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",Rr="data:image/png;base64,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",Yr=class{constructor(e,t){this.config=t,t.pathPrefix||(t.pathPrefix=""),t.app||(t.app={loadAssets:()=>{},initialize:()=>{},update:()=>{},render:()=>{},error:()=>{},dispose:()=>{}}),t.webglConfig&&(t.webglConfig={alpha:!0}),this.htmlCanvas=e,this.context=new ge(e,t.webglConfig),this.renderer=new Ni(e,this.context),this.gl=this.context.gl,this.assetManager=new Xi(this.context,t.pathPrefix),this.input=new ut(e),t.app.loadAssets&&t.app.loadAssets(this);let i=()=>{this.disposed||(requestAnimationFrame(i),this.time.update(),t.app.update&&t.app.update(this,this.time.delta),t.app.render&&t.app.render(this))},s=()=>{if(!this.disposed){if(this.assetManager.isLoadingComplete()){this.assetManager.hasErrors()?t.app.error&&t.app.error(this,this.assetManager.getErrors()):(t.app.initialize&&t.app.initialize(this),i());return}requestAnimationFrame(s)}};requestAnimationFrame(s)}context;time=new pt;htmlCanvas;gl;renderer;assetManager;input;disposed=!1;clear(e,t,i,s){this.gl.clearColor(e,t,i,s),this.gl.clear(this.gl.COLOR_BUFFER_BIT)}dispose(){this.config.app.dispose&&this.config.app.dispose(this),this.disposed=!0}},Fr=class{constructor(e,t){this.config=t;let i=typeof e=="string"?document.getElementById(e):e;if(i==null)throw new Error("SpinePlayer parent not found: "+e);this.parent=i,t.showControls===void 0&&(t.showControls=!0);let s=t.showControls?`
<div class="spine-player-controls spine-player-popup-parent spine-player-controls-hidden">
<div class="spine-player-timeline"></div>
<div class="spine-player-buttons">
<button class="spine-player-button spine-player-button-icon-pause"></button>
<div class="spine-player-button-spacer"></div>
<button class="spine-player-button spine-player-button-icon-speed"></button>
<button class="spine-player-button spine-player-button-icon-animations"></button>
<button class="spine-player-button spine-player-button-icon-skins"></button>
<button class="spine-player-button spine-player-button-icon-settings"></button>
<button class="spine-player-button spine-player-button-icon-fullscreen"></button>
<img class="spine-player-button-icon-spine-logo" src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20104%2031.16%22%3E%3Cpath%20d%3D%22M104%2012.68a1.31%201.31%200%200%201-.37%201%201.28%201.28%200%200%201-.85.31H91.57a10.51%2010.51%200%200%200%20.29%202.55%204.92%204.92%200%200%200%201%202%204.27%204.27%200%200%200%201.64%201.26%206.89%206.89%200%200%200%202.6.44%2010.66%2010.66%200%200%200%202.17-.2%2012.81%2012.81%200%200%200%201.64-.44q.69-.25%201.14-.44a1.87%201.87%200%200%201%20.68-.2.44.44%200%200%201%20.27.04.43.43%200%200%201%20.16.2%201.38%201.38%200%200%201%20.09.37%204.89%204.89%200%200%201%200%20.58%204.14%204.14%200%200%201%200%20.43v.32a.83.83%200%200%201-.09.26%201.1%201.1%200%200%201-.17.22%202.77%202.77%200%200%201-.61.34%208.94%208.94%200%200%201-1.32.46%2018.54%2018.54%200%200%201-1.88.41%2013.78%2013.78%200%200%201-2.28.18%2010.55%2010.55%200%200%201-3.68-.59%206.82%206.82%200%200%201-2.66-1.74%207.44%207.44%200%200%201-1.63-2.89%2013.48%2013.48%200%200%201-.55-4%2012.76%2012.76%200%200%201%20.57-3.94%208.35%208.35%200%200%201%201.64-3%207.15%207.15%200%200%201%202.58-1.87%208.47%208.47%200%200%201%203.39-.65%208.19%208.19%200%200%201%203.41.64%206.46%206.46%200%200%201%202.32%201.73%207%207%200%200%201%201.3%202.54%2011.17%2011.17%200%200%201%20.43%203.13zm-3.14-.93a5.69%205.69%200%200%200-1.09-3.86%204.17%204.17%200%200%200-3.42-1.4%204.52%204.52%200%200%200-2%20.44%204.41%204.41%200%200%200-1.47%201.15A5.29%205.29%200%200%200%2092%209.75a7%207%200%200%200-.36%202zM80.68%2021.94a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.31%206.31%200%200%201-.76%200%206.5%206.5%200%200%201-.78%200%201.74%201.74%200%200%201-.47-.11.59.59%200%200%201-.25-.18.42.42%200%200%201-.08-.26V12a9.8%209.8%200%200%200-.23-2.35%204.86%204.86%200%200%200-.66-1.53%202.88%202.88%200%200%200-1.13-1%203.57%203.57%200%200%200-1.6-.34%204%204%200%200%200-2.35.83A12.71%2012.71%200%200%200%2069.11%2010v11.9a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.51%206.51%200%200%201-.78%200%206.31%206.31%200%200%201-.76%200%201.88%201.88%200%200%201-.48-.11.52.52%200%200%201-.25-.18.46.46%200%200%201-.07-.26v-17a.53.53%200%200%201%20.03-.21.5.5%200%200%201%20.23-.19%201.28%201.28%200%200%201%20.44-.11%208.53%208.53%200%200%201%201.39%200%201.12%201.12%200%200%201%20.43.11.6.6%200%200%201%20.22.19.47.47%200%200%201%20.07.26V7.2a10.46%2010.46%200%200%201%202.87-2.36%206.17%206.17%200%200%201%202.88-.75%206.41%206.41%200%200%201%202.87.58%205.16%205.16%200%200%201%201.88%201.54%206.15%206.15%200%200%201%201%202.26%2013.46%2013.46%200%200%201%20.31%203.11z%22%20fill%3D%22%23fff%22%2F%3E%3Cpath%20d%3D%22M43.35%202.86c.09%202.6%201.89%204%205.48%204.61%203%20.48%205.79.24%206.69-2.37%201.75-5.09-2.4-3.82-6-4.39s-6.31-2.03-6.17%202.15zm1.08%2010.69c.33%201.94%202.14%203.06%204.91%203s4.84-1.16%205.13-3.25c.53-3.88-2.53-2.38-5.3-2.3s-5.4-1.26-4.74%202.55zM48%2022.44c.55%201.45%202.06%202.06%204.1%201.63s3.45-1.11%203.33-2.76c-.21-3.06-2.22-2.1-4.26-1.66S47%2019.6%2048%2022.44zm1.78%206.78c.16%201.22%201.22%202%202.88%201.93s2.92-.67%203.13-2c.4-2.43-1.46-1.53-3.12-1.51s-3.17-.82-2.89%201.58z%22%20fill%3D%22%23ff4000%22%2F%3E%3Cpath%20d%3D%22M35.28%2013.16a15.33%2015.33%200%200%201-.48%204%208.75%208.75%200%200%201-1.42%203%206.35%206.35%200%200%201-2.32%201.91%207.14%207.14%200%200%201-3.16.67%206.1%206.1%200%200%201-1.4-.15%205.34%205.34%200%200%201-1.26-.47%207.29%207.29%200%200%201-1.24-.81q-.61-.49-1.29-1.15v8.51a.47.47%200%200%201-.08.26.56.56%200%200%201-.25.19%201.74%201.74%200%200%201-.47.11%206.47%206.47%200%200%201-.78%200%206.26%206.26%200%200%201-.76%200%201.89%201.89%200%200%201-.48-.11.49.49%200%200%201-.25-.19.51.51%200%200%201-.07-.26V4.91a.57.57%200%200%201%20.06-.27.46.46%200%200%201%20.23-.18%201.47%201.47%200%200%201%20.44-.1%207.41%207.41%200%200%201%201.3%200%201.45%201.45%200%200%201%20.43.1.52.52%200%200%201%20.24.18.51.51%200%200%201%20.07.27V7.2a18.06%2018.06%200%200%201%201.49-1.38%209%209%200%200%201%201.45-1%206.82%206.82%200%200%201%201.49-.59%207.09%207.09%200%200%201%204.78.52%206%206%200%200%201%202.13%202%208.79%208.79%200%200%201%201.2%202.9%2015.72%2015.72%200%200%201%20.4%203.51zm-3.28.36a15.64%2015.64%200%200%200-.2-2.53%207.32%207.32%200%200%200-.69-2.17%204.06%204.06%200%200%200-1.3-1.51%203.49%203.49%200%200%200-2-.57%204.1%204.1%200%200%200-1.2.18%204.92%204.92%200%200%200-1.2.57%208.54%208.54%200%200%200-1.28%201A15.77%2015.77%200%200%200%2022.76%2010v6.77a13.53%2013.53%200%200%200%202.46%202.4%204.12%204.12%200%200%200%202.44.83%203.56%203.56%200%200%200%202-.57A4.28%204.28%200%200%200%2031%2018a7.58%207.58%200%200%200%20.77-2.12%2011.43%2011.43%200%200%200%20.23-2.36zM12%2017.3a5.39%205.39%200%200%201-.48%202.33%204.73%204.73%200%200%201-1.37%201.72%206.19%206.19%200%200%201-2.12%201.06%209.62%209.62%200%200%201-2.71.36%2010.38%2010.38%200%200%201-3.21-.5A7.63%207.63%200%200%201%201%2021.82a3.25%203.25%200%200%201-.66-.43%201.09%201.09%200%200%201-.3-.53%203.59%203.59%200%200%201-.04-.93%204.06%204.06%200%200%201%200-.61%202%202%200%200%201%20.09-.4.42.42%200%200%201%20.16-.22.43.43%200%200%201%20.24-.07%201.35%201.35%200%200%201%20.61.26q.41.26%201%20.56a9.22%209.22%200%200%200%201.41.55%206.25%206.25%200%200%200%201.87.26%205.62%205.62%200%200%200%201.44-.17%203.48%203.48%200%200%200%201.12-.5%202.23%202.23%200%200%200%20.73-.84%202.68%202.68%200%200%200%20.26-1.21%202%202%200%200%200-.37-1.21%203.55%203.55%200%200%200-1-.87%208.09%208.09%200%200%200-1.36-.66l-1.56-.61a16%2016%200%200%201-1.57-.73%206%206%200%200%201-1.37-1%204.52%204.52%200%200%201-1-1.4%204.69%204.69%200%200%201-.37-2%204.88%204.88%200%200%201%20.39-1.87%204.46%204.46%200%200%201%201.16-1.61%205.83%205.83%200%200%201%201.94-1.11A8.06%208.06%200%200%201%206.53%204a8.28%208.28%200%200%201%201.36.11%209.36%209.36%200%200%201%201.23.28%205.92%205.92%200%200%201%20.94.37%204.09%204.09%200%200%201%20.59.35%201%201%200%200%201%20.26.26.83.83%200%200%201%20.09.26%201.32%201.32%200%200%200%20.06.35%203.87%203.87%200%200%201%200%20.51%204.76%204.76%200%200%201%200%20.56%201.39%201.39%200%200%201-.09.39.5.5%200%200%201-.16.22.35.35%200%200%201-.21.07%201%201%200%200%201-.49-.21%207%207%200%200%200-.83-.44%209.26%209.26%200%200%200-1.2-.44%205.49%205.49%200%200%200-1.58-.16%204.93%204.93%200%200%200-1.4.18%202.69%202.69%200%200%200-1%20.51%202.16%202.16%200%200%200-.59.83%202.43%202.43%200%200%200-.2%201%202%202%200%200%200%20.38%201.24%203.6%203.6%200%200%200%201%20.88%208.25%208.25%200%200%200%201.38.68l1.58.62q.8.32%201.59.72a6%206%200%200%201%201.39%201%204.37%204.37%200%200%201%201%201.36%204.46%204.46%200%200%201%20.37%201.8z%22%20fill%3D%22%23fff%22%2F%3E%3C%2Fsvg%3E">
</div></div>`:"";this.parent.appendChild(this.dom=We(`<div class="spine-player" style="position:relative;height:100%"><canvas class="spine-player-canvas" style="display:block;width:100%;height:100%"></canvas>${s}</div>`));try{this.validateConfig(t)}catch(n){this.showError(n.message,n)}this.initialize(),this.addEventListener(window,"resize",()=>this.drawFrame(!1)),requestAnimationFrame(()=>this.drawFrame())}parent;dom;canvas=null;context=null;sceneRenderer=null;loadingScreen=null;assetManager=null;bg=new D;bgFullscreen=new D;playerControls=null;timelineSlider=null;playButton=null;skinButton=null;animationButton=null;playTime=0;selectedBones=[];cancelId=0;popup=null;error=!1;skeleton=null;animationState=null;paused=!0;speed=1;time=new pt;stopRequestAnimationFrame=!1;disposed=!1;viewport={};currentViewport={};previousViewport={};viewportTransitionStart=0;eventListeners=[];dispose(){this.sceneRenderer?.dispose(),this.loadingScreen?.dispose(),this.assetManager?.dispose();for(var e=0;e<this.eventListeners.length;e++){var t=this.eventListeners[e];t.target.removeEventListener(t.event,t.func)}this.parent.removeChild(this.dom),this.disposed=!0}addEventListener(e,t,i){this.eventListeners.push({target:e,event:t,func:i}),e.addEventListener(t,i)}validateConfig(e){if(!e)throw new Error("A configuration object must be passed to to new SpinePlayer().");if(e.skelUrl&&(e.binaryUrl=e.skelUrl),!e.jsonUrl&&!e.binaryUrl)throw new Error("A URL must be specified for the skeleton JSON or binary file.");if(!e.atlasUrl)throw new Error("A URL must be specified for the atlas file.");if(e.backgroundColor||(e.backgroundColor=e.alpha?"00000000":"000000"),e.fullScreenBackgroundColor||(e.fullScreenBackgroundColor=e.backgroundColor),e.backgroundImage&&!e.backgroundImage.url&&(e.backgroundImage=void 0),e.premultipliedAlpha===void 0&&(e.premultipliedAlpha=!0),e.preserveDrawingBuffer===void 0&&(e.preserveDrawingBuffer=!1),e.mipmaps===void 0&&(e.mipmaps=!0),e.debug||(e.debug={bones:!1,clipping:!1,bounds:!1,hulls:!1,meshes:!1,paths:!1,points:!1,regions:!1}),e.animations&&e.animation&&e.animations.indexOf(e.animation)<0)throw new Error("Animation '"+e.animation+"' is not in the config animation list: "+zi(e.animations));if(e.skins&&e.skin&&e.skins.indexOf(e.skin)<0)throw new Error("Default skin '"+e.skin+"' is not in the config skins list: "+zi(e.skins));e.viewport||(e.viewport={}),e.viewport.animations||(e.viewport.animations={}),e.viewport.debugRender===void 0&&(e.viewport.debugRender=!1),e.viewport.transitionTime===void 0&&(e.viewport.transitionTime=.25),e.controlBones||(e.controlBones=[]),e.showLoading===void 0&&(e.showLoading=!0),e.defaultMix===void 0&&(e.defaultMix=.25)}initialize(){let e=this.config,t=this.dom;if(!e.alpha){let i=e.backgroundColor;this.dom.style.backgroundColor=(i.charAt(0)=="#"?i:"#"+i).substr(0,7)}try{this.canvas=Oe(t,"spine-player-canvas"),this.context=new ge(this.canvas,{alpha:e.alpha,preserveDrawingBuffer:e.preserveDrawingBuffer}),this.sceneRenderer=new Ni(this.canvas,this.context,!0),e.showLoading&&(this.loadingScreen=new Rs(this.sceneRenderer))}catch(i){return this.showError(`Sorry, your browser does not support 
Please use the latest version of Firefox, Chrome, Edge, or Safari.`,i),null}if(this.assetManager=new Xi(this.context,"",e.downloader),e.rawDataURIs)for(let i in e.rawDataURIs)this.assetManager.setRawDataURI(i,e.rawDataURIs[i]);if(e.jsonUrl?this.assetManager.loadJson(e.jsonUrl):this.assetManager.loadBinary(e.binaryUrl),this.assetManager.loadTextureAtlas(e.atlasUrl),e.backgroundImage&&this.assetManager.loadTexture(e.backgroundImage.url),this.bg.setFromString(e.backgroundColor),this.bgFullscreen.setFromString(e.fullScreenBackgroundColor),e.showControls){this.playerControls=t.children[1];let i=this.playerControls.children,s=i[0],n=i[1].children;this.playButton=n[0];let d=n[2];this.animationButton=n[3],this.skinButton=n[4];let l=n[5],r=n[6],h=n[7];this.timelineSlider=new Ys,s.appendChild(this.timelineSlider.create()),this.timelineSlider.change=m=>{this.pause();let x=this.animationState.getCurrent(0).animation.duration*m;this.animationState.update(x-this.playTime),this.animationState.apply(this.skeleton),this.skeleton.updateWorldTransform(),this.playTime=x},this.playButton.onclick=()=>this.paused?this.play():this.pause(),d.onclick=()=>this.showSpeedDialog(d),this.animationButton.onclick=()=>this.showAnimationsDialog(this.animationButton),this.skinButton.onclick=()=>this.showSkinsDialog(this.skinButton),l.onclick=()=>this.showSettingsDialog(l);let a=this.canvas.clientWidth,o=this.canvas.clientHeight,c=this.canvas.style.width,u=this.canvas.style.height,f=!1;r.onclick=()=>{let m=()=>{f=!f,f||(this.canvas.style.width=a+"px",this.canvas.style.height=o+"px",this.drawFrame(!1),requestAnimationFrame(()=>{this.canvas.style.width=c,this.canvas.style.height=u}))},g=t;g.onfullscreenchange=m,g.onwebkitfullscreenchange=m;let x=document;x.fullscreenElement||x.webkitFullscreenElement||x.mozFullScreenElement||x.msFullscreenElement?x.exitFullscreen?x.exitFullscreen():x.mozCancelFullScreen?x.mozCancelFullScreen():x.webkitExitFullscreen?x.webkitExitFullscreen():x.msExitFullscreen&&x.msExitFullscreen():(a=this.canvas.clientWidth,o=this.canvas.clientHeight,c=this.canvas.style.width,u=this.canvas.style.height,g.requestFullscreen?g.requestFullscreen():g.webkitRequestFullScreen?g.webkitRequestFullScreen():g.mozRequestFullScreen?g.mozRequestFullScreen():g.msRequestFullscreen&&g.msRequestFullscreen())},h.onclick=()=>window.open("http://esotericsoftware.com")}return t}loadSkeleton(){if(this.error)return;this.assetManager.hasErrors()&&this.showError(`Error: Assets could not be loaded.
`+zi(this.assetManager.getErrors()));let e=this.config,t=this.assetManager.require(e.atlasUrl),i=this.context.gl,s=i.getExtension("EXT_texture_filter_anisotropic"),n=i.getParameter(i.VERSION).indexOf("WebGL 1.0")!=-1;for(let o of t.pages){let c=o.minFilter;var d=e.mipmaps,l=F.isPowerOfTwo(o.width)&&F.isPowerOfTwo(o.height);n&&!l&&(d=!1),d&&(s?(i.texParameterf(i.TEXTURE_2D,s.TEXTURE_MAX_ANISOTROPY_EXT,8),c=9987):c=9729,o.texture.setFilters(c,9728)),c!=9728&&c!=9729&&o.texture.update(!0)}let r;if(e.jsonUrl)try{let o=this.assetManager.remove(e.jsonUrl);if(!o)throw new Error("Empty JSON data.");if(e.jsonField&&(o=o[e.jsonField],!o))throw new Error("JSON field does not exist: "+e.jsonField);r=new bs(new bi(t)).readSkeletonData(o)}catch(o){this.showError(`Error: Could not load skeleton JSON.
${o.message}`,o);return}else{let o=this.assetManager.remove(e.binaryUrl),c=new ps(new bi(t));try{r=c.readSkeletonData(o)}catch(u){this.showError(`Error: Could not load skeleton binary.
${u.message}`,u);return}}this.skeleton=new Ei(r);let h=new ns(r);h.defaultMix=e.defaultMix,this.animationState=new ui(h),e.controlBones.forEach(o=>{r.findBone(o)||this.showError(`Error: Control bone does not exist in skeleton: ${o}`)}),!e.skin&&r.skins.length&&(e.skin=r.skins[0].name),e.skins&&e.skin.length&&e.skins.forEach(o=>{this.skeleton.data.findSkin(o)||this.showError(`Error: Skin in config list does not exist in skeleton: ${o}`)}),e.skin&&(this.skeleton.data.findSkin(e.skin)||this.showError(`Error: Skin does not exist in skeleton: ${e.skin}`),this.skeleton.setSkinByName(e.skin),this.skeleton.setSlotsToSetupPose()),Object.getOwnPropertyNames(e.viewport.animations).forEach(o=>{r.findAnimation(o)||this.showError(`Error: Animation for which a viewport was specified does not exist in skeleton: ${o}`)}),e.animations&&e.animations.length&&(e.animations.forEach(o=>{this.skeleton.data.findAnimation(o)||this.showError(`Error: Animation in config list does not exist in skeleton: ${o}`)}),e.animation||(e.animation=e.animations[0])),e.animation&&!r.findAnimation(e.animation)&&this.showError(`Error: Animation does not exist in skeleton: ${e.animation}`),this.setupInput(),e.showControls&&((r.skins.length==1||e.skins&&e.skins.length==1)&&this.skinButton.classList.add("spine-player-hidden"),(r.animations.length==1||e.animations&&e.animations.length==1)&&this.animationButton.classList.add("spine-player-hidden")),e.success&&e.success(this);let a=this.animationState.getCurrent(0);a?this.currentViewport||(this.setViewport(a.animation),this.play()):e.animation?(a=this.setAnimation(e.animation),this.play()):(a=this.animationState.setEmptyAnimation(0),a.trackEnd=1e8,this.setViewport(a.animation),this.pause())}setupInput(){let e=this.config,t=e.controlBones;if(!t.length&&!e.showControls)return;let i=this.selectedBones=new Array(t.length),s=this.canvas,n=null,d=new Te,l=new Ae,r=new Ae,h=new Te,a=this.skeleton,o=this.sceneRenderer,c=function(u,f){r.set(u,s.clientHeight-f,0),d.x=d.y=0;let m=24,g=0,x=null;for(let v=0;v<t.length;v++){i[v]=null;let p=a.findBone(t[v]);if(!p)continue;let w=o.camera.worldToScreen(l.set(p.worldX,p.worldY,0),s.clientWidth,s.clientHeight).distance(r);w<m&&(m=w,x=p,g=v,d.x=l.x-r.x,d.y=l.y-r.y)}return x&&(i[g]=x),x};if(new ut(s).addListener({down:(u,f)=>{n=c(u,f)},up:()=>{n?n=null:e.showControls&&(this.paused?this.play():this.pause())},dragged:(u,f)=>{n&&(u=F.clamp(u+d.x,0,s.clientWidth),f=F.clamp(f-d.y,0,s.clientHeight),o.camera.screenToWorld(l.set(u,f,0),s.clientWidth,s.clientHeight),n.parent?(n.parent.worldToLocal(h.set(l.x-a.x,l.y-a.y)),n.x=h.x,n.y=h.y):(n.x=l.x-a.x,n.y=l.y-a.y))},moved:(u,f)=>c(u,f)}),e.showControls){this.addEventListener(document,"mousemove",x=>{x instanceof MouseEvent&&g(x.clientX,x.clientY)}),this.addEventListener(document,"touchmove",x=>{if(x instanceof TouchEvent){let v=x.changedTouches;if(v.length){let p=v[0];g(p.clientX,p.clientY)}}});let u=(x,v,p)=>{let w=x-p.left,b=v-p.top;return w>=0&&w<=p.width&&b>=0&&b<=p.height},f=!0,m=!1,g=(x,v)=>{let p=Oe(this.dom,"spine-player-popup");f=u(x,v,this.playerControls.getBoundingClientRect()),m=u(x,v,s.getBoundingClientRect()),clearTimeout(this.cancelId),!p&&!f&&!m&&!this.paused?this.playerControls.classList.add("spine-player-controls-hidden"):this.playerControls.classList.remove("spine-player-controls-hidden"),!f&&!p&&!this.paused&&(this.cancelId=setTimeout(()=>{this.paused||this.playerControls.classList.add("spine-player-controls-hidden")},1e3))}}}play(){this.paused=!1;let e=this.config;e.showControls&&(this.cancelId=setTimeout(()=>{this.paused||this.playerControls.classList.add("spine-player-controls-hidden")},1e3),this.playButton.classList.remove("spine-player-button-icon-play"),this.playButton.classList.add("spine-player-button-icon-pause"),e.animation||(e.animations&&e.animations.length?e.animation=e.animations[0]:this.skeleton.data.animations.length&&(e.animation=this.skeleton.data.animations[0].name),e.animation&&this.setAnimation(e.animation)))}pause(){this.paused=!0,this.config.showControls&&(this.playerControls.classList.remove("spine-player-controls-hidden"),clearTimeout(this.cancelId),this.playButton.classList.remove("spine-player-button-icon-pause"),this.playButton.classList.add("spine-player-button-icon-play"))}setAnimation(e,t=!0){return e=this.setViewport(e),this.animationState.setAnimationWith(0,e,t)}addAnimation(e,t=!0,i=0){return e=this.setViewport(e),this.animationState.addAnimationWith(0,e,t,i)}setViewport(e){if(typeof e=="string"){let n=this.skeleton.data.findAnimation(e);if(!n)throw new Error("Animation not found: "+e);e=n}this.previousViewport=this.currentViewport;let t=this.config.viewport,i=this.currentViewport={padLeft:t.padLeft!==void 0?t.padLeft:"10%",padRight:t.padRight!==void 0?t.padRight:"10%",padTop:t.padTop!==void 0?t.padTop:"10%",padBottom:t.padBottom!==void 0?t.padBottom:"10%"};t.x!==void 0&&t.y!==void 0&&t.width&&t.height?(i.x=t.x,i.y=t.y,i.width=t.width,i.height=t.height):this.calculateAnimationViewport(e,i);let s=this.config.viewport.animations[e.name];return s&&(s.x!==void 0&&s.y!==void 0&&s.width&&s.height&&(i.x=s.x,i.y=s.y,i.width=s.width,i.height=s.height),s.padLeft!==void 0&&(i.padLeft=s.padLeft),s.padRight!==void 0&&(i.padRight=s.padRight),s.padTop!==void 0&&(i.padTop=s.padTop),s.padBottom!==void 0&&(i.padBottom=s.padBottom)),i.padLeft=this.percentageToWorldUnit(i.width,i.padLeft),i.padRight=this.percentageToWorldUnit(i.width,i.padRight),i.padBottom=this.percentageToWorldUnit(i.height,i.padBottom),i.padTop=this.percentageToWorldUnit(i.height,i.padTop),this.viewportTransitionStart=performance.now(),e}percentageToWorldUnit(e,t){return typeof t=="string"?e*parseFloat(t.substr(0,t.length-1))/100:t}calculateAnimationViewport(e,t){this.skeleton.setToSetupPose();let i=100,s=e.duration?e.duration/i:0,n=0,d=1e8,l=-1e8,r=1e8,h=-1e8,a=new Te,o=new Te;for(let c=0;c<i;c++,n+=s)e.apply(this.skeleton,n,n,!1,[],1,0,0),this.skeleton.updateWorldTransform(),this.skeleton.getBounds(a,o),!isNaN(a.x)&&!isNaN(a.y)&&!isNaN(o.x)&&!isNaN(o.y)?(d=Math.min(a.x,d),l=Math.max(a.x+o.x,l),r=Math.min(a.y,r),h=Math.max(a.y+o.y,h)):this.showError("Animation bounds are invalid: "+e.name);t.x=d,t.y=r,t.width=l-d,t.height=h-r}drawFrame(e=!0){try{if(this.error||this.disposed)return;e&&!this.stopRequestAnimationFrame&&requestAnimationFrame(()=>this.drawFrame());let t=document,s=t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement?this.bgFullscreen:this.bg;this.time.update();let n=this.time.delta,d=this.assetManager.isLoadingComplete();!this.skeleton&&d&&this.loadSkeleton();let l=this.skeleton,r=this.config;if(l){let h=this.sceneRenderer;h.resize(1);let a=this.paused?0:n*this.speed;if(r.frame&&r.frame(this,a),!this.paused&&(this.animationState.update(a),this.animationState.apply(l),l.updateWorldTransform(),r.showControls)){this.playTime+=a;let m=this.animationState.getCurrent(0);if(m){let g=m.animation.duration;for(;this.playTime>=g&&g!=0;)this.playTime-=g;this.playTime=Math.max(0,Math.min(this.playTime,g)),this.timelineSlider.setValue(this.playTime/g)}}let o=this.viewport;if(o.x=this.currentViewport.x-this.currentViewport.padLeft,o.y=this.currentViewport.y-this.currentViewport.padBottom,o.width=this.currentViewport.width+this.currentViewport.padLeft+this.currentViewport.padRight,o.height=this.currentViewport.height+this.currentViewport.padBottom+this.currentViewport.padTop,this.previousViewport){let m=(performance.now()-this.viewportTransitionStart)/1e3/r.viewport.transitionTime;if(m<1){let g=this.previousViewport.x-this.previousViewport.padLeft,x=this.previousViewport.y-this.previousViewport.padBottom,v=this.previousViewport.width+this.previousViewport.padLeft+this.previousViewport.padRight,p=this.previousViewport.height+this.previousViewport.padBottom+this.previousViewport.padTop;o.x=g+(o.x-g)*m,o.y=x+(o.y-x)*m,o.width=v+(o.width-v)*m,o.height=p+(o.height-p)*m}}h.camera.zoom=this.canvas.height/this.canvas.width>o.height/o.width?o.width/this.canvas.width:o.height/this.canvas.height,h.camera.position.x=o.x+o.width/2,h.camera.position.y=o.y+o.height/2;let c=this.context.gl;c.clearColor(s.r,s.g,s.b,s.a),c.clear(c.COLOR_BUFFER_BIT),r.update&&r.update(this,a),h.begin();let u=r.backgroundImage;if(u){let m=this.assetManager.require(u.url);u.x!==void 0&&u.y!==void 0&&u.width&&u.height?h.drawTexture(m,u.x,u.y,u.width,u.height):h.drawTexture(m,o.x,o.y,o.width,o.height)}h.drawSkeleton(l,r.premultipliedAlpha),((h.skeletonDebugRenderer.drawBones=r.debug.bones)||(h.skeletonDebugRenderer.drawBoundingBoxes=r.debug.bounds)||(h.skeletonDebugRenderer.drawClipping=r.debug.clipping)||(h.skeletonDebugRenderer.drawMeshHull=r.debug.hulls)||(h.skeletonDebugRenderer.drawPaths=r.debug.paths)||(h.skeletonDebugRenderer.drawRegionAttachments=r.debug.regions)||(h.skeletonDebugRenderer.drawMeshTriangles=r.debug.meshes))&&h.drawSkeletonDebug(l,r.premultipliedAlpha);let f=r.controlBones;if(f.length){let m=this.selectedBones;c.lineWidth(2);for(let g=0;g<f.length;g++){let x=l.findBone(f[g]);if(!x)continue;let v=m[g]?Xr:Pr,p=m[g]?Br:Dr;h.circle(!0,l.x+x.worldX,l.y+x.worldY,20,v),h.circle(!1,l.x+x.worldX,l.y+x.worldY,20,p)}}r.viewport.debugRender&&(c.lineWidth(1),h.rect(!1,this.currentViewport.x,this.currentViewport.y,this.currentViewport.width,this.currentViewport.height,D.GREEN),h.rect(!1,o.x,o.y,o.width,o.height,D.RED)),h.end(),r.draw&&r.draw(this,a)}r.showLoading&&(this.loadingScreen.backgroundColor.setFromColor(s),this.loadingScreen.draw(d)),d&&r.loading&&r.loading(this,n)}catch(t){this.showError(`Error: Unable to render skeleton.
${t.message}`,t)}}stopRendering(){this.stopRequestAnimationFrame=!0}hidePopup(e){return this.popup!=null&&this.popup.hide(e)}showSpeedDialog(e){let t="speed";if(this.hidePopup(t))return;let i=new Nt(t,e,this,this.playerControls,`
<div class="spine-player-popup-title">Speed</div>
<hr>
<div class="spine-player-row" style="align-items:center;padding:8px">
<div class="spine-player-column">
	<div class="spine-player-speed-slider" style="margin-bottom:4px"></div>
	<div class="spine-player-row" style="justify-content:space-between"><div>0.1x</div><div>1x</div><div>2x</div></div>
</div>
</div>`),s=new Ys(2,.1,!0);Oe(i.dom,"spine-player-speed-slider").appendChild(s.create()),s.setValue(this.speed/2),s.change=n=>this.speed=n*2,i.show()}showAnimationsDialog(e){let t="animations";if(this.hidePopup(t)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new Nt(t,e,this,this.playerControls,'<div class="spine-player-popup-title">Animations</div><hr><ul class="spine-player-list"></ul>'),s=Oe(i.dom,"spine-player-list");this.skeleton.data.animations.forEach(n=>{if(this.config.animations&&this.config.animations.indexOf(n.name)<0)return;let d=We('<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>');n.name==this.config.animation&&d.classList.add("selected"),Oe(d,"selectable-text").innerText=n.name,s.appendChild(d),d.onclick=()=>{Fs(s.children,"selected"),d.classList.add("selected"),this.config.animation=n.name,this.playTime=0,this.setAnimation(n.name),this.play()}}),i.show()}showSkinsDialog(e){let t="skins";if(this.hidePopup(t)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new Nt(t,e,this,this.playerControls,'<div class="spine-player-popup-title">Skins</div><hr><ul class="spine-player-list"></ul>'),s=Oe(i.dom,"spine-player-list");this.skeleton.data.skins.forEach(n=>{if(this.config.skins&&this.config.skins.indexOf(n.name)<0)return;let d=We('<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>');n.name==this.config.skin&&d.classList.add("selected"),Oe(d,"selectable-text").innerText=n.name,s.appendChild(d),d.onclick=()=>{Fs(s.children,"selected"),d.classList.add("selected"),this.config.skin=n.name,this.skeleton.setSkinByName(this.config.skin),this.skeleton.setSlotsToSetupPose()}}),i.show()}showSettingsDialog(e){let t="settings";if(this.hidePopup(t)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new Nt(t,e,this,this.playerControls,'<div class="spine-player-popup-title">Debug</div><hr><ul class="spine-player-list"></li>'),s=Oe(i.dom,"spine-player-list"),n=(d,l)=>{let r=We('<li class="spine-player-list-item"></li>'),h=new Lr(d);r.appendChild(h.create());let a=this.config.debug;h.setEnabled(a[l]),h.change=o=>a[l]=o,s.appendChild(r)};n("Bones","bones"),n("Regions","regions"),n("Meshes","meshes"),n("Bounds","bounds"),n("Paths","paths"),n("Clipping","clipping"),n("Points","points"),n("Hulls","hulls"),i.show()}showError(e,t){if(this.error){if(t)throw t}else throw this.error=!0,this.dom.appendChild(We('<div class="spine-player-error" style="background:#000;color:#fff;position:absolute;top:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;overflow:auto;z-index:999">'+e.replace(`
`,"<br><br>")+"</div>")),this.config.error&&this.config.error(this,e),t||new Error(e)}},Nt=class{constructor(e,t,i,s,n){this.id=e,this.button=t,this.player=i,this.dom=We('<div class="spine-player-popup spine-player-hidden"></div>'),this.dom.innerHTML=n,s.appendChild(this.dom),this.className="spine-player-button-icon-"+e+"-selected"}dom;className;windowClickListener;dispose(){}hide(e){return this.dom.remove(),this.button.classList.remove(this.className),this.id==e?(this.player.popup=null,!0):!1}show(){this.player.popup=this,this.button.classList.add(this.className),this.dom.classList.remove("spine-player-hidden");let e=!1,t=()=>{e||requestAnimationFrame(t);let n=this.player.dom,d=Math.abs(n.getBoundingClientRect().bottom-n.getBoundingClientRect().bottom),l=Math.abs(n.getBoundingClientRect().right-n.getBoundingClientRect().right);this.dom.style.maxHeight=n.clientHeight-d-l+"px"};requestAnimationFrame(t);let i=!0,s=n=>{if(i||this.player.popup!=this){i=!1;return}this.dom.contains(n.target)||(this.dom.remove(),window.removeEventListener("click",s),this.button.classList.remove(this.className),this.player.popup=null,e=!0)};this.player.addEventListener(window,"click",s)}},Lr=class{constructor(e){this.text=e}switch=null;enabled=!1;change=()=>{};create(){return this.switch=We(`
<div class="spine-player-switch">
	<span class="spine-player-switch-text">${this.text}</span>
	<div class="spine-player-switch-knob-area">
		<div class="spine-player-switch-knob"></div>
	</div>
</div>`),this.switch.addEventListener("click",()=>{this.setEnabled(!this.enabled),this.change&&this.change(this.enabled)}),this.switch}setEnabled(e){e?this.switch?.classList.add("active"):this.switch?.classList.remove("active"),this.enabled=e}isEnabled(){return this.enabled}},Ys=class{constructor(e=0,t=.1,i=!1){this.snaps=e,this.snapPercentage=t,this.big=i}slider=null;value=null;knob=null;change=()=>{};create(){this.slider=We(`
<div class="spine-player-slider ${this.big?"big":""}">
	<div class="spine-player-slider-value"></div>
	<!--<div class="spine-player-slider-knob"></div>-->
</div>`),this.value=Oe(this.slider,"spine-player-slider-value"),this.setValue(0);let e=!1;return new ut(this.slider).addListener({down:(t,i)=>{e=!0,this.value?.classList.add("hovering")},up:(t,i)=>{e=!1,this.change&&this.change(this.setValue(t/this.slider.clientWidth)),this.value?.classList.remove("hovering")},moved:(t,i)=>{e&&this.change&&this.change(this.setValue(t/this.slider.clientWidth))},dragged:(t,i)=>{this.change&&this.change(this.setValue(t/this.slider.clientWidth))}}),this.slider}setValue(e){if(e=Math.max(0,Math.min(1,e)),this.snaps){let t=1/this.snaps,i=e%t;i<t*this.snapPercentage?e=e-i:i>t-t*this.snapPercentage&&(e=e-i+t),e=Math.max(0,Math.min(1,e))}return this.value.style.width=""+e*100+"%",e}};function Oe(e,t){return e.getElementsByClassName(t)[0]}function We(e){let t=document.createElement("div");return t.innerHTML=e,t.children[0]}function Fs(e,t){for(let i=0;i<e.length;i++)e[i].classList.remove(t)}function zi(e){return JSON.stringify(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&#34;").replace(/'/g,"&#39;")}var Xr=new D(.478,0,0,.25),Br=new D(1,1,1,1),Pr=new D(.478,0,0,.5),Dr=new D(1,0,0,.8);function Vr(e){return new Promise((t,i)=>{const s=document.createElement("script");s.src=e,s.onload=()=>t(),s.onerror=()=>i(new Error(`Script load error for ${e}`)),document.head.appendChild(s)})}function Or(e){return new Promise((t,i)=>{const s=document.createElement("link");s.href=e,s.rel="stylesheet",s.onload=()=>t(),s.onerror=()=>i(new Error(`CSS load error for ${e}`)),document.head.appendChild(s)})}var Ls=class{constructor(e){this.parent=e,this.load()}prefix=`<html>
<head>
<style>
body { margin: 0px; }
</style>
</head>
<body>`.trim();postfix="</body>";code;player;async load(){await Promise.all([Vr("https://www.unpkg.com/codemirror@5.51.0/lib/codemirror.js"),Or("https://www.unpkg.com/codemirror@5.51.0/lib/codemirror.css")]),this.render(this.parent)}render(e){let t=`
				<div style="display: flex; flex-direction: column; width: 100%; height: 100%;">
					<div style="width: 100%; height: 50%;"></div>
					<iframe style="width: 100%; height: 50%; outline: none; border: none;"></iframe>
				</div>
			`;e.innerHTML=t;let i=e.children[0].children[0];this.player=e.children[0].children[1],requestAnimationFrame(()=>{this.code=CodeMirror(i,{lineNumbers:!0,tabSize:3,indentUnit:3,indentWithTabs:!0,scrollBarStyle:"native",mode:"htmlmixed",theme:"monokai"}),this.code.on("change",()=>{this.startPlayer()}),this.setCode(Ls.DEFAULT_CODE)})}setPreAndPostfix(e,t){this.prefix=e,this.postfix=t,this.startPlayer()}setCode(e){this.code.setValue(e),this.startPlayer()}timerId=0;startPlayer(){clearTimeout(this.timerId),this.timerId=setTimeout(()=>{let e=this.code.getDoc().getValue();e=this.prefix+e+this.postfix,e=window.btoa(e),this.player.src="",this.player.src="data:text/html;base64,"+e},500)}},Xs=Ls;return P(Xs,"DEFAULT_CODE",`
<script src="https://esotericsoftware.com/files/spine-player/4.1/spine-player.js"><\/script>
<link rel="stylesheet" href="https://esotericsoftware.com/files/spine-player/4.1/spine-player.css">

<div id="player-container" style="width: 100vw; height: 100vh;"></div>

<script>
new spine.SpinePlayer("player-container", {
	jsonUrl: "https://esotericsoftware.com/files/examples/4.1/spineboy/export/spineboy-pro.json",
	atlasUrl: "https://esotericsoftware.com/files/examples/4.1/spineboy/export/spineboy-pma.atlas"
});
<\/script>
		`.trim()),Hs(qi)})();
