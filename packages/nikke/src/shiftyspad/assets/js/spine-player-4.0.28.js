var spine=(()=>{var Ui=Object.defineProperty,Xs=t=>Ui(t,"__esModule",{value:!0}),Bs=(t,e)=>{Xs(t);for(var i in e)Ui(t,i,{get:e[i],enumerable:!0})},zi={};Bs(zi,{AlphaTimeline:()=>ti,Animation:()=>ye,AnimationState:()=>Se,AnimationStateAdapter:()=>Os,AnimationStateData:()=>$i,AssetManager:()=>Yi,AssetManagerBase:()=>rs,AtlasAttachmentLoader:()=>pi,Attachment:()=>ze,AttachmentTimeline:()=>Kt,BinaryInput:()=>cs,BlendMode:()=>yt,Bone:()=>wi,BoneData:()=>xi,BoundingBoxAttachment:()=>ke,CURRENT:()=>Qi,CameraController:()=>fr,ClippingAttachment:()=>ge,Color:()=>O,Color2Attribute:()=>bs,ColorAttribute:()=>Le,ConstraintData:()=>Me,CurveTimeline:()=>Rt,CurveTimeline1:()=>Ft,CurveTimeline2:()=>Ae,DebugUtils:()=>Vs,DeformTimeline:()=>si,Downloader:()=>as,DrawOrderTimeline:()=>Qt,Event:()=>bi,EventData:()=>vi,EventQueue:()=>Ji,EventTimeline:()=>me,EventType:()=>Ct,FIRST:()=>hi,FakeTexture:()=>Ns,GLTexture:()=>pe,HOLD_FIRST:()=>Te,HOLD_MIX:()=>Ki,HOLD_SUBSEQUENT:()=>di,IkConstraint:()=>ns,IkConstraintData:()=>yi,IkConstraintTimeline:()=>ri,Input:()=>xe,IntSet:()=>Ps,Interpolation:()=>Wi,JitterEffect:()=>dr,LoadingScreen:()=>Cs,M00:()=>N,M01:()=>j,M02:()=>H,M03:()=>U,M10:()=>G,M11:()=>z,M12:()=>Z,M13:()=>W,M20:()=>J,M21:()=>K,M22:()=>_,M23:()=>q,M30:()=>$,M31:()=>tt,M32:()=>et,M33:()=>Q,ManagedWebGLRenderingContext:()=>wt,MathUtils:()=>F,Matrix4:()=>_t,Mesh:()=>Ri,MeshAttachment:()=>jt,MixBlend:()=>ft,MixDirection:()=>Vt,OrthoCamera:()=>xs,PathAttachment:()=>te,PathConstraint:()=>ae,PathConstraintData:()=>Ai,PathConstraintMixTimeline:()=>oi,PathConstraintPositionTimeline:()=>ni,PathConstraintSpacingTimeline:()=>li,PointAttachment:()=>gi,PolygonBatcher:()=>Li,Pool:()=>ue,Position2Attribute:()=>Fe,Position3Attribute:()=>mr,PositionMode:()=>Dt,Pow:()=>_i,PowOut:()=>qi,RGB2Timeline:()=>ii,RGBA2Timeline:()=>ei,RGBATimeline:()=>Qe,RGBTimeline:()=>$e,RegionAttachment:()=>at,ResizeMode:()=>ie,RotateMode:()=>ee,RotateTimeline:()=>fe,SETUP:()=>ci,SUBSEQUENT:()=>Ce,ScaleTimeline:()=>je,ScaleXTimeline:()=>He,ScaleYTimeline:()=>Ge,SceneRenderer:()=>Pi,Shader:()=>bt,ShapeRenderer:()=>Xi,ShapeType:()=>At,ShearTimeline:()=>Ze,ShearXTimeline:()=>Je,ShearYTimeline:()=>Ke,Skeleton:()=>hs,SkeletonBinary:()=>ds,SkeletonBounds:()=>us,SkeletonClipping:()=>Ie,SkeletonData:()=>Si,SkeletonDebugRenderer:()=>Be,SkeletonJson:()=>fs,SkeletonRenderer:()=>Bi,Skin:()=>Ee,SkinEntry:()=>Ci,Slot:()=>ls,SlotData:()=>Ti,SpacingMode:()=>gt,SpineCanvas:()=>br,SpinePlayer:()=>vr,SpinePlayerEditor:()=>Es,StringSet:()=>Ue,SwirlEffect:()=>gs,TexCoordAttribute:()=>Fi,Texture:()=>fi,TextureAtlas:()=>es,TextureAtlasPage:()=>is,TextureAtlasRegion:()=>mi,TextureFilter:()=>pt,TextureRegion:()=>ts,TextureWrap:()=>$t,TimeKeeper:()=>ve,Timeline:()=>mt,Touch:()=>ws,TrackEntry:()=>Zi,TransformConstraint:()=>os,TransformConstraintData:()=>ki,TransformConstraintTimeline:()=>ai,TransformMode:()=>Tt,TranslateTimeline:()=>We,TranslateXTimeline:()=>_e,TranslateYTimeline:()=>qe,Triangulator:()=>kt,Utils:()=>V,Vector2:()=>Yt,Vector3:()=>Mt,VertexAttachment:()=>Pt,VertexAttribute:()=>le,VertexAttributeType:()=>Ht,WebGLBlendModeConverter:()=>Re,WindowedMean:()=>Ds});var Ps=class{constructor(){this.array=new Array}add(t){let e=this.contains(t);return this.array[t|0]=t|0,!e}contains(t){return this.array[t|0]!=null}remove(t){this.array[t|0]=void 0}clear(){this.array.length=0}},Ue=class{constructor(){this.entries={},this.size=0}add(t){let e=this.entries[t];return this.entries[t]=!0,e?!1:(this.size++,!0)}addAll(t){let e=this.size;for(var i=0,s=t.length;i<s;i++)this.add(t[i]);return e!=this.size}contains(t){return this.entries[t]}clear(){this.entries={},this.size=0}},Jt=class{constructor(t=0,e=0,i=0,s=0){this.r=t,this.g=e,this.b=i,this.a=s}set(t,e,i,s){return this.r=t,this.g=e,this.b=i,this.a=s,this.clamp()}setFromColor(t){return this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this}setFromString(t){return t=t.charAt(0)=="#"?t.substr(1):t,this.r=parseInt(t.substr(0,2),16)/255,this.g=parseInt(t.substr(2,2),16)/255,this.b=parseInt(t.substr(4,2),16)/255,this.a=t.length!=8?1:parseInt(t.substr(6,2),16)/255,this}add(t,e,i,s){return this.r+=t,this.g+=e,this.b+=i,this.a+=s,this.clamp()}clamp(){return this.r<0?this.r=0:this.r>1&&(this.r=1),this.g<0?this.g=0:this.g>1&&(this.g=1),this.b<0?this.b=0:this.b>1&&(this.b=1),this.a<0?this.a=0:this.a>1&&(this.a=1),this}static rgba8888ToColor(t,e){t.r=((e&4278190080)>>>24)/255,t.g=((e&16711680)>>>16)/255,t.b=((e&65280)>>>8)/255,t.a=(e&255)/255}static rgb888ToColor(t,e){t.r=((e&16711680)>>>16)/255,t.g=((e&65280)>>>8)/255,t.b=(e&255)/255}static fromString(t){return new Jt().setFromString(t)}},O=Jt;O.WHITE=new Jt(1,1,1,1),O.RED=new Jt(1,0,0,1),O.GREEN=new Jt(0,1,0,1),O.BLUE=new Jt(0,0,1,1),O.MAGENTA=new Jt(1,0,1,1);var Nt=class{static clamp(t,e,i){return t<e?e:t>i?i:t}static cosDeg(t){return Math.cos(t*Nt.degRad)}static sinDeg(t){return Math.sin(t*Nt.degRad)}static signum(t){return t>0?1:t<0?-1:0}static toInt(t){return t>0?Math.floor(t):Math.ceil(t)}static cbrt(t){let e=Math.pow(Math.abs(t),1/3);return t<0?-e:e}static randomTriangular(t,e){return Nt.randomTriangularWith(t,e,(t+e)*.5)}static randomTriangularWith(t,e,i){let s=Math.random(),a=e-t;return s<=(i-t)/a?t+Math.sqrt(s*a*(i-t)):e-Math.sqrt((1-s)*a*(e-i))}static isPowerOfTwo(t){return t&&(t&t-1)==0}},F=Nt;F.PI=3.1415927,F.PI2=Nt.PI*2,F.radiansToDegrees=180/Nt.PI,F.radDeg=Nt.radiansToDegrees,F.degreesToRadians=Nt.PI/180,F.degRad=Nt.degreesToRadians;var Wi=class{apply(t,e,i){return t+(e-t)*this.applyInternal(i)}},_i=class extends Wi{constructor(t){super();this.power=2,this.power=t}applyInternal(t){return t<=.5?Math.pow(t*2,this.power)/2:Math.pow((t-1)*2,this.power)/(this.power%2==0?-2:2)+1}},qi=class extends _i{constructor(t){super(t)}applyInternal(t){return Math.pow(t-1,this.power)*(this.power%2==0?-1:1)+1}},re=class{static arrayCopy(t,e,i,s,a){for(let d=e,n=s;d<e+a;d++,n++)i[n]=t[d]}static arrayFill(t,e,i,s){for(let a=e;a<i;a++)t[a]=s}static setArraySize(t,e,i=0){let s=t.length;if(s==e)return t;if(t.length=e,s<e)for(let a=s;a<e;a++)t[a]=i;return t}static ensureArrayCapacity(t,e,i=0){return t.length>=e?t:re.setArraySize(t,e,i)}static newArray(t,e){let i=new Array(t);for(let s=0;s<t;s++)i[s]=e;return i}static newFloatArray(t){if(re.SUPPORTS_TYPED_ARRAYS)return new Float32Array(t);{let e=new Array(t);for(let i=0;i<e.length;i++)e[i]=0;return e}}static newShortArray(t){if(re.SUPPORTS_TYPED_ARRAYS)return new Int16Array(t);{let e=new Array(t);for(let i=0;i<e.length;i++)e[i]=0;return e}}static toFloatArray(t){return re.SUPPORTS_TYPED_ARRAYS?new Float32Array(t):t}static toSinglePrecision(t){return re.SUPPORTS_TYPED_ARRAYS?Math.fround(t):t}static webkit602BugfixHelper(t,e){}static contains(t,e,i=!0){for(var s=0;s<t.length;s++)if(t[s]==e)return!0;return!1}static enumValue(t,e){return t[e[0].toUpperCase()+e.slice(1)]}},V=re;V.SUPPORTS_TYPED_ARRAYS=typeof Float32Array!="undefined";var Vs=class{static logBones(t){for(let e=0;e<t.bones.length;e++){let i=t.bones[e];console.log(i.data.name+", "+i.a+", "+i.b+", "+i.c+", "+i.d+", "+i.worldX+", "+i.worldY)}}},ue=class{constructor(t){this.items=new Array,this.instantiator=t}obtain(){return this.items.length>0?this.items.pop():this.instantiator()}free(t){t.reset&&t.reset(),this.items.push(t)}freeAll(t){for(let e=0;e<t.length;e++)this.free(t[e])}clear(){this.items.length=0}},Yt=class{constructor(t=0,e=0){this.x=t,this.y=e}set(t,e){return this.x=t,this.y=e,this}length(){let t=this.x,e=this.y;return Math.sqrt(t*t+e*e)}normalize(){let t=this.length();return t!=0&&(this.x/=t,this.y/=t),this}},ve=class{constructor(){this.maxDelta=.064,this.framesPerSecond=0,this.delta=0,this.totalTime=0,this.lastTime=Date.now()/1e3,this.frameCount=0,this.frameTime=0}update(){let t=Date.now()/1e3;this.delta=t-this.lastTime,this.frameTime+=this.delta,this.totalTime+=this.delta,this.delta>this.maxDelta&&(this.delta=this.maxDelta),this.lastTime=t,this.frameCount++,this.frameTime>1&&(this.framesPerSecond=this.frameCount/this.frameTime,this.frameTime=0,this.frameCount=0)}},Ds=class{constructor(t=32){this.addedValues=0,this.lastValue=0,this.mean=0,this.dirty=!0,this.values=new Array(t)}hasEnoughData(){return this.addedValues>=this.values.length}addValue(t){this.addedValues<this.values.length&&this.addedValues++,this.values[this.lastValue++]=t,this.lastValue>this.values.length-1&&(this.lastValue=0),this.dirty=!0}getMean(){if(this.hasEnoughData()){if(this.dirty){let t=0;for(let e=0;e<this.values.length;e++)t+=this.values[e];this.mean=t/this.values.length,this.dirty=!1}return this.mean}return 0}},ze=class{constructor(t){if(!t)throw new Error("name cannot be null.");this.name=t}},ji=class extends ze{constructor(t){super(t);this.id=ji.nextID++,this.bones=null,this.vertices=null,this.worldVerticesLength=0,this.deformAttachment=this}computeWorldVertices(t,e,i,s,a,d){i=a+(i>>1)*d;let n=t.bone.skeleton,r=t.deform,h=this.vertices,o=this.bones;if(!o){r.length>0&&(h=r);let m=t.bone,f=m.worldX,g=m.worldY,x=m.a,v=m.b,p=m.c,b=m.d;for(let w=e,y=a;y<i;w+=2,y+=d){let T=h[w],A=h[w+1];s[y]=T*x+A*v+f,s[y+1]=T*p+A*b+g}return}let l=0,c=0;for(let m=0;m<e;m+=2){let f=o[l];l+=f+1,c+=f}let u=n.bones;if(r.length==0)for(let m=a,f=c*3;m<i;m+=d){let g=0,x=0,v=o[l++];for(v+=l;l<v;l++,f+=3){let p=u[o[l]],b=h[f],w=h[f+1],y=h[f+2];g+=(b*p.a+w*p.b+p.worldX)*y,x+=(b*p.c+w*p.d+p.worldY)*y}s[m]=g,s[m+1]=x}else{let m=r;for(let f=a,g=c*3,x=c<<1;f<i;f+=d){let v=0,p=0,b=o[l++];for(b+=l;l<b;l++,g+=3,x+=2){let w=u[o[l]],y=h[g]+m[x],T=h[g+1]+m[x+1],A=h[g+2];v+=(y*w.a+T*w.b+w.worldX)*A,p+=(y*w.c+T*w.d+w.worldY)*A}s[f]=v,s[f+1]=p}}}copyTo(t){this.bones?(t.bones=new Array(this.bones.length),V.arrayCopy(this.bones,0,t.bones,0,this.bones.length)):t.bones=null,this.vertices?(t.vertices=V.newFloatArray(this.vertices.length),V.arrayCopy(this.vertices,0,t.vertices,0,this.vertices.length)):t.vertices=null,t.worldVerticesLength=this.worldVerticesLength,t.deformAttachment=this.deformAttachment}},Pt=ji;Pt.nextID=0;var ye=class{constructor(t,e,i){if(this.timelines=null,this.timelineIds=null,!t)throw new Error("name cannot be null.");this.name=t,this.setTimelines(e),this.duration=i}setTimelines(t){if(!t)throw new Error("timelines cannot be null.");this.timelines=t,this.timelineIds=new Ue;for(var e=0;e<t.length;e++)this.timelineIds.addAll(t[e].getPropertyIds())}hasTimeline(t){for(let e=0;e<t.length;e++)if(this.timelineIds.contains(t[e]))return!0;return!1}apply(t,e,i,s,a,d,n,r){if(!t)throw new Error("skeleton cannot be null.");s&&this.duration!=0&&(i%=this.duration,e>0&&(e%=this.duration));let h=this.timelines;for(let o=0,l=h.length;o<l;o++)h[o].apply(t,e,i,a,d,n,r)}},ft;(function(t){t[t.setup=0]="setup",t[t.first=1]="first",t[t.replace=2]="replace",t[t.add=3]="add"})(ft||(ft={}));var Vt;(function(t){t[t.mixIn=0]="mixIn",t[t.mixOut=1]="mixOut"})(Vt||(Vt={}));var ot={rotate:0,x:1,y:2,scaleX:3,scaleY:4,shearX:5,shearY:6,rgb:7,alpha:8,rgb2:9,attachment:10,deform:11,event:12,drawOrder:13,ikConstraint:14,transformConstraint:15,pathConstraintPosition:16,pathConstraintSpacing:17,pathConstraintMix:18},mt=class{constructor(t,e){this.propertyIds=null,this.frames=null,this.propertyIds=e,this.frames=V.newFloatArray(t*this.getFrameEntries())}getPropertyIds(){return this.propertyIds}getFrameEntries(){return 1}getFrameCount(){return this.frames.length/this.getFrameEntries()}getDuration(){return this.frames[this.frames.length-this.getFrameEntries()]}static search1(t,e){let i=t.length;for(let s=1;s<i;s++)if(t[s]>e)return s-1;return i-1}static search(t,e,i){let s=t.length;for(let a=i;a<s;a+=i)if(t[a]>e)return a-i;return s-i}},Rt=class extends mt{constructor(t,e,i){super(t,i);this.curves=null,this.curves=V.newFloatArray(t+e*18),this.curves[t-1]=1}setLinear(t){this.curves[t]=0}setStepped(t){this.curves[t]=1}shrink(t){let e=this.getFrameCount()+t*18;if(this.curves.length>e){let i=V.newFloatArray(e);V.arrayCopy(this.curves,0,i,0,e),this.curves=i}}setBezier(t,e,i,s,a,d,n,r,h,o,l){let c=this.curves,u=this.getFrameCount()+t*18;i==0&&(c[e]=2+u);let m=(s-d*2+r)*.03,f=(a-n*2+h)*.03,g=((d-r)*3-s+o)*.006,x=((n-h)*3-a+l)*.006,v=m*2+g,p=f*2+x,b=(d-s)*.3+m+g*.16666667,w=(n-a)*.3+f+x*.16666667,y=s+b,T=a+w;for(let A=u+18;u<A;u+=2)c[u]=y,c[u+1]=T,b+=v,w+=p,v+=g,p+=x,y+=b,T+=w}getBezierValue(t,e,i,s){let a=this.curves;if(a[s]>t){let h=this.frames[e],o=this.frames[e+i];return o+(t-h)/(a[s]-h)*(a[s+1]-o)}let d=s+18;for(s+=2;s<d;s+=2)if(a[s]>=t){let h=a[s-2],o=a[s-1];return o+(t-h)/(a[s]-h)*(a[s+1]-o)}e+=this.getFrameEntries();let n=a[d-2],r=a[d-1];return r+(t-n)/(this.frames[e]-n)*(this.frames[e+i]-r)}},Ft=class extends Rt{constructor(t,e,i){super(t,e,[i])}getFrameEntries(){return 2}setFrame(t,e,i){t<<=1,this.frames[t]=e,this.frames[t+1]=i}getCurveValue(t){let e=this.frames,i=e.length-2;for(let a=2;a<=i;a+=2)if(e[a]>t){i=a-2;break}let s=this.curves[i>>1];switch(s){case 0:let a=e[i],d=e[i+1];return d+(t-a)/(e[i+2]-a)*(e[i+2+1]-d);case 1:return e[i+1]}return this.getBezierValue(t,i,1,s-2)}},Ae=class extends Rt{constructor(t,e,i,s){super(t,e,[i,s])}getFrameEntries(){return 3}setFrame(t,e,i,s){t*=3,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s}},fe=class extends Ft{constructor(t,e,i){super(t,e,ot.rotate+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.rotation=r.data.rotation;return;case 1:r.rotation+=(r.data.rotation-r.rotation)*a}return}let o=this.getCurveValue(i);switch(d){case 0:r.rotation=r.data.rotation+o*a;break;case 1:case 2:o+=r.data.rotation-r.rotation;case 3:r.rotation+=o*a}}},We=class extends Ae{constructor(t,e,i){super(t,e,ot.x+"|"+i,ot.y+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.x=r.data.x,r.y=r.data.y;return;case 1:r.x+=(r.data.x-r.x)*a,r.y+=(r.data.y-r.y)*a}return}let o=0,l=0,c=mt.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let m=h[c];o=h[c+1],l=h[c+2];let f=(i-m)/(h[c+3]-m);o+=(h[c+3+1]-o)*f,l+=(h[c+3+2]-l)*f;break;case 1:o=h[c+1],l=h[c+2];break;default:o=this.getBezierValue(i,c,1,u-2),l=this.getBezierValue(i,c,2,u+18-2)}switch(d){case 0:r.x=r.data.x+o*a,r.y=r.data.y+l*a;break;case 1:case 2:r.x+=(r.data.x+o-r.x)*a,r.y+=(r.data.y+l-r.y)*a;break;case 3:r.x+=o*a,r.y+=l*a}}},_e=class extends Ft{constructor(t,e,i){super(t,e,ot.x+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.x=r.data.x;return;case 1:r.x+=(r.data.x-r.x)*a}return}let o=this.getCurveValue(i);switch(d){case 0:r.x=r.data.x+o*a;break;case 1:case 2:r.x+=(r.data.x+o-r.x)*a;break;case 3:r.x+=o*a}}},qe=class extends Ft{constructor(t,e,i){super(t,e,ot.y+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.y=r.data.y;return;case 1:r.y+=(r.data.y-r.y)*a}return}let o=this.getCurveValue(i);switch(d){case 0:r.y=r.data.y+o*a;break;case 1:case 2:r.y+=(r.data.y+o-r.y)*a;break;case 3:r.y+=o*a}}},je=class extends Ae{constructor(t,e,i){super(t,e,ot.scaleX+"|"+i,ot.scaleY+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleX=r.data.scaleX,r.scaleY=r.data.scaleY;return;case 1:r.scaleX+=(r.data.scaleX-r.scaleX)*a,r.scaleY+=(r.data.scaleY-r.scaleY)*a}return}let o,l,c=mt.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let m=h[c];o=h[c+1],l=h[c+2];let f=(i-m)/(h[c+3]-m);o+=(h[c+3+1]-o)*f,l+=(h[c+3+2]-l)*f;break;case 1:o=h[c+1],l=h[c+2];break;default:o=this.getBezierValue(i,c,1,u-2),l=this.getBezierValue(i,c,2,u+18-2)}if(o*=r.data.scaleX,l*=r.data.scaleY,a==1)d==3?(r.scaleX+=o-r.data.scaleX,r.scaleY+=l-r.data.scaleY):(r.scaleX=o,r.scaleY=l);else{let m=0,f=0;if(n==1)switch(d){case 0:m=r.data.scaleX,f=r.data.scaleY,r.scaleX=m+(Math.abs(o)*F.signum(m)-m)*a,r.scaleY=f+(Math.abs(l)*F.signum(f)-f)*a;break;case 1:case 2:m=r.scaleX,f=r.scaleY,r.scaleX=m+(Math.abs(o)*F.signum(m)-m)*a,r.scaleY=f+(Math.abs(l)*F.signum(f)-f)*a;break;case 3:r.scaleX+=(o-r.data.scaleX)*a,r.scaleY+=(l-r.data.scaleY)*a}else switch(d){case 0:m=Math.abs(r.data.scaleX)*F.signum(o),f=Math.abs(r.data.scaleY)*F.signum(l),r.scaleX=m+(o-m)*a,r.scaleY=f+(l-f)*a;break;case 1:case 2:m=Math.abs(r.scaleX)*F.signum(o),f=Math.abs(r.scaleY)*F.signum(l),r.scaleX=m+(o-m)*a,r.scaleY=f+(l-f)*a;break;case 3:r.scaleX+=(o-r.data.scaleX)*a,r.scaleY+=(l-r.data.scaleY)*a}}}},He=class extends Ft{constructor(t,e,i){super(t,e,ot.scaleX+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleX=r.data.scaleX;return;case 1:r.scaleX+=(r.data.scaleX-r.scaleX)*a}return}let o=this.getCurveValue(i)*r.data.scaleX;if(a==1)d==3?r.scaleX+=o-r.data.scaleX:r.scaleX=o;else{let l=0;if(n==1)switch(d){case 0:l=r.data.scaleX,r.scaleX=l+(Math.abs(o)*F.signum(l)-l)*a;break;case 1:case 2:l=r.scaleX,r.scaleX=l+(Math.abs(o)*F.signum(l)-l)*a;break;case 3:r.scaleX+=(o-r.data.scaleX)*a}else switch(d){case 0:l=Math.abs(r.data.scaleX)*F.signum(o),r.scaleX=l+(o-l)*a;break;case 1:case 2:l=Math.abs(r.scaleX)*F.signum(o),r.scaleX=l+(o-l)*a;break;case 3:r.scaleX+=(o-r.data.scaleX)*a}}}},Ge=class extends Ft{constructor(t,e,i){super(t,e,ot.scaleY+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.scaleY=r.data.scaleY;return;case 1:r.scaleY+=(r.data.scaleY-r.scaleY)*a}return}let o=this.getCurveValue(i)*r.data.scaleY;if(a==1)d==3?r.scaleY+=o-r.data.scaleY:r.scaleY=o;else{let l=0;if(n==1)switch(d){case 0:l=r.data.scaleY,r.scaleY=l+(Math.abs(o)*F.signum(l)-l)*a;break;case 1:case 2:l=r.scaleY,r.scaleY=l+(Math.abs(o)*F.signum(l)-l)*a;break;case 3:r.scaleY+=(o-r.data.scaleY)*a}else switch(d){case 0:l=Math.abs(r.data.scaleY)*F.signum(o),r.scaleY=l+(o-l)*a;break;case 1:case 2:l=Math.abs(r.scaleY)*F.signum(o),r.scaleY=l+(o-l)*a;break;case 3:r.scaleY+=(o-r.data.scaleY)*a}}}},Ze=class extends Ae{constructor(t,e,i){super(t,e,ot.shearX+"|"+i,ot.shearY+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearX=r.data.shearX,r.shearY=r.data.shearY;return;case 1:r.shearX+=(r.data.shearX-r.shearX)*a,r.shearY+=(r.data.shearY-r.shearY)*a}return}let o=0,l=0,c=mt.search(h,i,3),u=this.curves[c/3];switch(u){case 0:let m=h[c];o=h[c+1],l=h[c+2];let f=(i-m)/(h[c+3]-m);o+=(h[c+3+1]-o)*f,l+=(h[c+3+2]-l)*f;break;case 1:o=h[c+1],l=h[c+2];break;default:o=this.getBezierValue(i,c,1,u-2),l=this.getBezierValue(i,c,2,u+18-2)}switch(d){case 0:r.shearX=r.data.shearX+o*a,r.shearY=r.data.shearY+l*a;break;case 1:case 2:r.shearX+=(r.data.shearX+o-r.shearX)*a,r.shearY+=(r.data.shearY+l-r.shearY)*a;break;case 3:r.shearX+=o*a,r.shearY+=l*a}}},Je=class extends Ft{constructor(t,e,i){super(t,e,ot.shearX+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearX=r.data.shearX;return;case 1:r.shearX+=(r.data.shearX-r.shearX)*a}return}let o=this.getCurveValue(i);switch(d){case 0:r.shearX=r.data.shearX+o*a;break;case 1:case 2:r.shearX+=(r.data.shearX+o-r.shearX)*a;break;case 3:r.shearX+=o*a}}},Ke=class extends Ft{constructor(t,e,i){super(t,e,ot.shearY+"|"+i);this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,a,d,n){let r=t.bones[this.boneIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.shearY=r.data.shearY;return;case 1:r.shearY+=(r.data.shearY-r.shearY)*a}return}let o=this.getCurveValue(i);switch(d){case 0:r.shearY=r.data.shearY+o*a;break;case 1:case 2:r.shearY+=(r.data.shearY+o-r.shearY)*a;break;case 3:r.shearY+=o*a}}},Qe=class extends Rt{constructor(t,e,i){super(t,e,[ot.rgb+"|"+i,ot.alpha+"|"+i]);this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 5}setFrame(t,e,i,s,a,d){t*=5,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=a,this.frames[t+4]=d}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,o=r.color;if(i<h[0]){let x=r.data.color;switch(d){case 0:o.setFromColor(x);return;case 1:o.add((x.r-o.r)*a,(x.g-o.g)*a,(x.b-o.b)*a,(x.a-o.a)*a)}return}let l=0,c=0,u=0,m=0,f=mt.search(h,i,5),g=this.curves[f/5];switch(g){case 0:let x=h[f];l=h[f+1],c=h[f+2],u=h[f+3],m=h[f+4];let v=(i-x)/(h[f+5]-x);l+=(h[f+5+1]-l)*v,c+=(h[f+5+2]-c)*v,u+=(h[f+5+3]-u)*v,m+=(h[f+5+4]-m)*v;break;case 1:l=h[f+1],c=h[f+2],u=h[f+3],m=h[f+4];break;default:l=this.getBezierValue(i,f,1,g-2),c=this.getBezierValue(i,f,2,g+18-2),u=this.getBezierValue(i,f,3,g+18*2-2),m=this.getBezierValue(i,f,4,g+18*3-2)}a==1?o.set(l,c,u,m):(d==0&&o.setFromColor(r.data.color),o.add((l-o.r)*a,(c-o.g)*a,(u-o.b)*a,(m-o.a)*a))}},$e=class extends Rt{constructor(t,e,i){super(t,e,[ot.rgb+"|"+i]);this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 4}setFrame(t,e,i,s,a){t<<=2,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=a}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,o=r.color;if(i<h[0]){let g=r.data.color;switch(d){case 0:o.r=g.r,o.g=g.g,o.b=g.b;return;case 1:o.r+=(g.r-o.r)*a,o.g+=(g.g-o.g)*a,o.b+=(g.b-o.b)*a}return}let l=0,c=0,u=0,m=mt.search(h,i,4),f=this.curves[m>>2];switch(f){case 0:let g=h[m];l=h[m+1],c=h[m+2],u=h[m+3];let x=(i-g)/(h[m+4]-g);l+=(h[m+4+1]-l)*x,c+=(h[m+4+2]-c)*x,u+=(h[m+4+3]-u)*x;break;case 1:l=h[m+1],c=h[m+2],u=h[m+3];break;default:l=this.getBezierValue(i,m,1,f-2),c=this.getBezierValue(i,m,2,f+18-2),u=this.getBezierValue(i,m,3,f+18*2-2)}if(a==1)o.r=l,o.g=c,o.b=u;else{if(d==0){let g=r.data.color;o.r=g.r,o.g=g.g,o.b=g.b}o.r+=(l-o.r)*a,o.g+=(c-o.g)*a,o.b+=(u-o.b)*a}}},ti=class extends Ft{constructor(t,e,i){super(t,e,ot.alpha+"|"+i);this.slotIndex=0,this.slotIndex=i}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=r.color;if(i<this.frames[0]){let l=r.data.color;switch(d){case 0:h.a=l.a;return;case 1:h.a+=(l.a-h.a)*a}return}let o=this.getCurveValue(i);a==1?h.a=o:(d==0&&(h.a=r.data.color.a),h.a+=(o-h.a)*a)}},ei=class extends Rt{constructor(t,e,i){super(t,e,[ot.rgb+"|"+i,ot.alpha+"|"+i,ot.rgb2+"|"+i]);this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 8}setFrame(t,e,i,s,a,d,n,r,h){t<<=3,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=a,this.frames[t+4]=d,this.frames[t+5]=n,this.frames[t+6]=r,this.frames[t+7]=h}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,o=r.color,l=r.darkColor;if(i<h[0]){let w=r.data.color,y=r.data.darkColor;switch(d){case 0:o.setFromColor(w),l.r=y.r,l.g=y.g,l.b=y.b;return;case 1:o.add((w.r-o.r)*a,(w.g-o.g)*a,(w.b-o.b)*a,(w.a-o.a)*a),l.r+=(y.r-l.r)*a,l.g+=(y.g-l.g)*a,l.b+=(y.b-l.b)*a}return}let c=0,u=0,m=0,f=0,g=0,x=0,v=0,p=mt.search(h,i,8),b=this.curves[p>>3];switch(b){case 0:let w=h[p];c=h[p+1],u=h[p+2],m=h[p+3],f=h[p+4],g=h[p+5],x=h[p+6],v=h[p+7];let y=(i-w)/(h[p+8]-w);c+=(h[p+8+1]-c)*y,u+=(h[p+8+2]-u)*y,m+=(h[p+8+3]-m)*y,f+=(h[p+8+4]-f)*y,g+=(h[p+8+5]-g)*y,x+=(h[p+8+6]-x)*y,v+=(h[p+8+7]-v)*y;break;case 1:c=h[p+1],u=h[p+2],m=h[p+3],f=h[p+4],g=h[p+5],x=h[p+6],v=h[p+7];break;default:c=this.getBezierValue(i,p,1,b-2),u=this.getBezierValue(i,p,2,b+18-2),m=this.getBezierValue(i,p,3,b+18*2-2),f=this.getBezierValue(i,p,4,b+18*3-2),g=this.getBezierValue(i,p,5,b+18*4-2),x=this.getBezierValue(i,p,6,b+18*5-2),v=this.getBezierValue(i,p,7,b+18*6-2)}if(a==1)o.set(c,u,m,f),l.r=g,l.g=x,l.b=v;else{if(d==0){o.setFromColor(r.data.color);let w=r.data.darkColor;l.r=w.r,l.g=w.g,l.b=w.b}o.add((c-o.r)*a,(u-o.g)*a,(m-o.b)*a,(f-o.a)*a),l.r+=(g-l.r)*a,l.g+=(x-l.g)*a,l.b+=(v-l.b)*a}}},ii=class extends Rt{constructor(t,e,i){super(t,e,[ot.rgb+"|"+i,ot.rgb2+"|"+i]);this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 7}setFrame(t,e,i,s,a,d,n,r){t*=7,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=a,this.frames[t+4]=d,this.frames[t+5]=n,this.frames[t+6]=r}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=this.frames,o=r.color,l=r.darkColor;if(i<h[0]){let w=r.data.color,y=r.data.darkColor;switch(d){case 0:o.r=w.r,o.g=w.g,o.b=w.b,l.r=y.r,l.g=y.g,l.b=y.b;return;case 1:o.r+=(w.r-o.r)*a,o.g+=(w.g-o.g)*a,o.b+=(w.b-o.b)*a,l.r+=(y.r-l.r)*a,l.g+=(y.g-l.g)*a,l.b+=(y.b-l.b)*a}return}let c=0,u=0,m=0,f=0,g=0,x=0,v=0,p=mt.search(h,i,7),b=this.curves[p/7];switch(b){case 0:let w=h[p];c=h[p+1],u=h[p+2],m=h[p+3],g=h[p+4],x=h[p+5],v=h[p+6];let y=(i-w)/(h[p+7]-w);c+=(h[p+7+1]-c)*y,u+=(h[p+7+2]-u)*y,m+=(h[p+7+3]-m)*y,g+=(h[p+7+4]-g)*y,x+=(h[p+7+5]-x)*y,v+=(h[p+7+6]-v)*y;break;case 1:c=h[p+1],u=h[p+2],m=h[p+3],g=h[p+4],x=h[p+5],v=h[p+6];break;default:c=this.getBezierValue(i,p,1,b-2),u=this.getBezierValue(i,p,2,b+18-2),m=this.getBezierValue(i,p,3,b+18*2-2),g=this.getBezierValue(i,p,4,b+18*3-2),x=this.getBezierValue(i,p,5,b+18*4-2),v=this.getBezierValue(i,p,6,b+18*5-2)}if(a==1)o.r=c,o.g=u,o.b=m,l.r=g,l.g=x,l.b=v;else{if(d==0){let w=r.data.color,y=r.data.darkColor;o.r=w.r,o.g=w.g,o.b=w.b,l.r=y.r,l.g=y.g,l.b=y.b}o.r+=(c-o.r)*a,o.g+=(u-o.g)*a,o.b+=(m-o.b)*a,l.r+=(g-l.r)*a,l.g+=(x-l.g)*a,l.b+=(v-l.b)*a}}},Kt=class extends mt{constructor(t,e){super(t,[ot.attachment+"|"+e]);this.slotIndex=0,this.slotIndex=e,this.attachmentNames=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.attachmentNames[t]=i}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!!r.bone.active){if(n==1){d==0&&this.setAttachment(t,r,r.data.attachmentName);return}if(i<this.frames[0]){(d==0||d==1)&&this.setAttachment(t,r,r.data.attachmentName);return}this.setAttachment(t,r,this.attachmentNames[mt.search1(this.frames,i)])}}setAttachment(t,e,i){e.setAttachment(i?t.getAttachment(this.slotIndex,i):null)}},si=class extends Rt{constructor(t,e,i,s){super(t,e,[ot.deform+"|"+i+"|"+s.id]);this.slotIndex=0,this.attachment=null,this.vertices=null,this.slotIndex=i,this.attachment=s,this.vertices=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.vertices[t]=i}setBezier(t,e,i,s,a,d,n,r,h,o,l){let c=this.curves,u=this.getFrameCount()+t*18;i==0&&(c[e]=2+u);let m=(s-d*2+r)*.03,f=h*.03-n*.06,g=((d-r)*3-s+o)*.006,x=(n-h+.33333333)*.018,v=m*2+g,p=f*2+x,b=(d-s)*.3+m+g*.16666667,w=n*.3+f+x*.16666667,y=s+b,T=w;for(let A=u+18;u<A;u+=2)c[u]=y,c[u+1]=T,b+=v,w+=p,v+=g,p+=x,y+=b,T+=w}getCurvePercent(t,e){let i=this.curves,s=i[e];switch(s){case 0:let r=this.frames[e];return(t-r)/(this.frames[e+this.getFrameEntries()]-r);case 1:return 0}if(s-=2,i[s]>t){let r=this.frames[e];return i[s+1]*(t-r)/(i[s]-r)}let a=s+18;for(s+=2;s<a;s+=2)if(i[s]>=t){let r=i[s-2],h=i[s-1];return h+(t-r)/(i[s]-r)*(i[s+1]-h)}let d=i[a-2],n=i[a-1];return n+(1-n)*(t-d)/(this.frames[e+this.getFrameEntries()]-d)}apply(t,e,i,s,a,d,n){let r=t.slots[this.slotIndex];if(!r.bone.active)return;let h=r.getAttachment();if(!(h instanceof Pt)||h.deformAttachment!=this.attachment)return;let o=r.deform;o.length==0&&(d=0);let l=this.vertices,c=l[0].length,u=this.frames;if(i<u[0]){let p=h;switch(d){case 0:o.length=0;return;case 1:if(a==1){o.length=0;return}if(o.length=c,p.bones){a=1-a;for(var m=0;m<c;m++)o[m]*=a}else{let b=p.vertices;for(var m=0;m<c;m++)o[m]+=(b[m]-o[m])*a}}return}if(o.length=c,i>=u[u.length-1]){let p=l[u.length-1];if(a==1)if(d==3){let b=h;if(b.bones)for(let w=0;w<c;w++)o[w]+=p[w];else{let w=b.vertices;for(let y=0;y<c;y++)o[y]+=p[y]-w[y]}}else V.arrayCopy(p,0,o,0,c);else switch(d){case 0:{let w=h;if(w.bones)for(let y=0;y<c;y++)o[y]=p[y]*a;else{let y=w.vertices;for(let T=0;T<c;T++){let A=y[T];o[T]=A+(p[T]-A)*a}}break}case 1:case 2:for(let w=0;w<c;w++)o[w]+=(p[w]-o[w])*a;break;case 3:let b=h;if(b.bones)for(let w=0;w<c;w++)o[w]+=p[w]*a;else{let w=b.vertices;for(let y=0;y<c;y++)o[y]+=(p[y]-w[y])*a}}return}let f=mt.search1(u,i),g=this.getCurvePercent(i,f),x=l[f],v=l[f+1];if(a==1)if(d==3){let p=h;if(p.bones)for(let b=0;b<c;b++){let w=x[b];o[b]+=w+(v[b]-w)*g}else{let b=p.vertices;for(let w=0;w<c;w++){let y=x[w];o[w]+=y+(v[w]-y)*g-b[w]}}}else for(let p=0;p<c;p++){let b=x[p];o[p]=b+(v[p]-b)*g}else switch(d){case 0:{let b=h;if(b.bones)for(let w=0;w<c;w++){let y=x[w];o[w]=(y+(v[w]-y)*g)*a}else{let w=b.vertices;for(let y=0;y<c;y++){let T=x[y],A=w[y];o[y]=A+(T+(v[y]-T)*g-A)*a}}break}case 1:case 2:for(let b=0;b<c;b++){let w=x[b];o[b]+=(w+(v[b]-w)*g-o[b])*a}break;case 3:let p=h;if(p.bones)for(let b=0;b<c;b++){let w=x[b];o[b]+=(w+(v[b]-w)*g)*a}else{let b=p.vertices;for(let w=0;w<c;w++){let y=x[w];o[w]+=(y+(v[w]-y)*g-b[w])*a}}}}},Hi=class extends mt{constructor(t){super(t,Hi.propertyIds);this.events=null,this.events=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e){this.frames[t]=e.time,this.events[t]=e}apply(t,e,i,s,a,d,n){if(!s)return;let r=this.frames,h=this.frames.length;if(e>i)this.apply(t,e,Number.MAX_VALUE,s,a,d,n),e=-1;else if(e>=r[h-1])return;if(i<r[0])return;let o=0;if(e<r[0])o=0;else{o=mt.search1(r,e)+1;let l=r[o];for(;o>0&&r[o-1]==l;)o--}for(;o<h&&i>=r[o];o++)s.push(this.events[o])}},me=Hi;me.propertyIds=[""+ot.event];var Gi=class extends mt{constructor(t){super(t,Gi.propertyIds);this.drawOrders=null,this.drawOrders=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.drawOrders[t]=i}apply(t,e,i,s,a,d,n){if(n==1){d==0&&V.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length);return}if(i<this.frames[0]){(d==0||d==1)&&V.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length);return}let r=this.drawOrders[mt.search1(this.frames,i)];if(!r)V.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length);else{let h=t.drawOrder,o=t.slots;for(let l=0,c=r.length;l<c;l++)h[l]=o[r[l]]}}},Qt=Gi;Qt.propertyIds=[""+ot.drawOrder];var ri=class extends Rt{constructor(t,e,i){super(t,e,[ot.ikConstraint+"|"+i]);this.ikConstraintIndex=0,this.ikConstraintIndex=i}getFrameEntries(){return 6}setFrame(t,e,i,s,a,d,n){t*=6,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=a,this.frames[t+4]=d?1:0,this.frames[t+5]=n?1:0}apply(t,e,i,s,a,d,n){let r=t.ikConstraints[this.ikConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.mix=r.data.mix,r.softness=r.data.softness,r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch;return;case 1:r.mix+=(r.data.mix-r.mix)*a,r.softness+=(r.data.softness-r.softness)*a,r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch}return}let o=0,l=0,c=mt.search(h,i,6),u=this.curves[c/6];switch(u){case 0:let m=h[c];o=h[c+1],l=h[c+2];let f=(i-m)/(h[c+6]-m);o+=(h[c+6+1]-o)*f,l+=(h[c+6+2]-l)*f;break;case 1:o=h[c+1],l=h[c+2];break;default:o=this.getBezierValue(i,c,1,u-2),l=this.getBezierValue(i,c,2,u+18-2)}d==0?(r.mix=r.data.mix+(o-r.data.mix)*a,r.softness=r.data.softness+(l-r.data.softness)*a,n==1?(r.bendDirection=r.data.bendDirection,r.compress=r.data.compress,r.stretch=r.data.stretch):(r.bendDirection=h[c+3],r.compress=h[c+4]!=0,r.stretch=h[c+5]!=0)):(r.mix+=(o-r.mix)*a,r.softness+=(l-r.softness)*a,n==0&&(r.bendDirection=h[c+3],r.compress=h[c+4]!=0,r.stretch=h[c+5]!=0))}},ai=class extends Rt{constructor(t,e,i){super(t,e,[ot.transformConstraint+"|"+i]);this.transformConstraintIndex=0,this.transformConstraintIndex=i}getFrameEntries(){return 7}setFrame(t,e,i,s,a,d,n,r){let h=this.frames;t*=7,h[t]=e,h[t+1]=i,h[t+2]=s,h[t+3]=a,h[t+4]=d,h[t+5]=n,h[t+6]=r}apply(t,e,i,s,a,d,n){let r=t.transformConstraints[this.transformConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){let v=r.data;switch(d){case 0:r.mixRotate=v.mixRotate,r.mixX=v.mixX,r.mixY=v.mixY,r.mixScaleX=v.mixScaleX,r.mixScaleY=v.mixScaleY,r.mixShearY=v.mixShearY;return;case 1:r.mixRotate+=(v.mixRotate-r.mixRotate)*a,r.mixX+=(v.mixX-r.mixX)*a,r.mixY+=(v.mixY-r.mixY)*a,r.mixScaleX+=(v.mixScaleX-r.mixScaleX)*a,r.mixScaleY+=(v.mixScaleY-r.mixScaleY)*a,r.mixShearY+=(v.mixShearY-r.mixShearY)*a}return}let o,l,c,u,m,f,g=mt.search(h,i,7),x=this.curves[g/7];switch(x){case 0:let v=h[g];o=h[g+1],l=h[g+2],c=h[g+3],u=h[g+4],m=h[g+5],f=h[g+6];let p=(i-v)/(h[g+7]-v);o+=(h[g+7+1]-o)*p,l+=(h[g+7+2]-l)*p,c+=(h[g+7+3]-c)*p,u+=(h[g+7+4]-u)*p,m+=(h[g+7+5]-m)*p,f+=(h[g+7+6]-f)*p;break;case 1:o=h[g+1],l=h[g+2],c=h[g+3],u=h[g+4],m=h[g+5],f=h[g+6];break;default:o=this.getBezierValue(i,g,1,x-2),l=this.getBezierValue(i,g,2,x+18-2),c=this.getBezierValue(i,g,3,x+18*2-2),u=this.getBezierValue(i,g,4,x+18*3-2),m=this.getBezierValue(i,g,5,x+18*4-2),f=this.getBezierValue(i,g,6,x+18*5-2)}if(d==0){let v=r.data;r.mixRotate=v.mixRotate+(o-v.mixRotate)*a,r.mixX=v.mixX+(l-v.mixX)*a,r.mixY=v.mixY+(c-v.mixY)*a,r.mixScaleX=v.mixScaleX+(u-v.mixScaleX)*a,r.mixScaleY=v.mixScaleY+(m-v.mixScaleY)*a,r.mixShearY=v.mixShearY+(f-v.mixShearY)*a}else r.mixRotate+=(o-r.mixRotate)*a,r.mixX+=(l-r.mixX)*a,r.mixY+=(c-r.mixY)*a,r.mixScaleX+=(u-r.mixScaleX)*a,r.mixScaleY+=(m-r.mixScaleY)*a,r.mixShearY+=(f-r.mixShearY)*a}},ni=class extends Ft{constructor(t,e,i){super(t,e,ot.pathConstraintPosition+"|"+i);this.pathConstraintIndex=0,this.pathConstraintIndex=i}apply(t,e,i,s,a,d,n){let r=t.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.position=r.data.position;return;case 1:r.position+=(r.data.position-r.position)*a}return}let o=this.getCurveValue(i);d==0?r.position=r.data.position+(o-r.data.position)*a:r.position+=(o-r.position)*a}},li=class extends Ft{constructor(t,e,i){super(t,e,ot.pathConstraintSpacing+"|"+i);this.pathConstraintIndex=0,this.pathConstraintIndex=i}apply(t,e,i,s,a,d,n){let r=t.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.spacing=r.data.spacing;return;case 1:r.spacing+=(r.data.spacing-r.spacing)*a}return}let o=this.getCurveValue(i);d==0?r.spacing=r.data.spacing+(o-r.data.spacing)*a:r.spacing+=(o-r.spacing)*a}},oi=class extends Rt{constructor(t,e,i){super(t,e,[ot.pathConstraintMix+"|"+i]);this.pathConstraintIndex=0,this.pathConstraintIndex=i}getFrameEntries(){return 4}setFrame(t,e,i,s,a){let d=this.frames;t<<=2,d[t]=e,d[t+1]=i,d[t+2]=s,d[t+3]=a}apply(t,e,i,s,a,d,n){let r=t.pathConstraints[this.pathConstraintIndex];if(!r.active)return;let h=this.frames;if(i<h[0]){switch(d){case 0:r.mixRotate=r.data.mixRotate,r.mixX=r.data.mixX,r.mixY=r.data.mixY;return;case 1:r.mixRotate+=(r.data.mixRotate-r.mixRotate)*a,r.mixX+=(r.data.mixX-r.mixX)*a,r.mixY+=(r.data.mixY-r.mixY)*a}return}let o,l,c,u=mt.search(h,i,4),m=this.curves[u>>2];switch(m){case 0:let f=h[u];o=h[u+1],l=h[u+2],c=h[u+3];let g=(i-f)/(h[u+4]-f);o+=(h[u+4+1]-o)*g,l+=(h[u+4+2]-l)*g,c+=(h[u+4+3]-c)*g;break;case 1:o=h[u+1],l=h[u+2],c=h[u+3];break;default:o=this.getBezierValue(i,u,1,m-2),l=this.getBezierValue(i,u,2,m+18-2),c=this.getBezierValue(i,u,3,m+18*2-2)}if(d==0){let f=r.data;r.mixRotate=f.mixRotate+(o-f.mixRotate)*a,r.mixX=f.mixX+(l-f.mixX)*a,r.mixY=f.mixY+(c-f.mixY)*a}else r.mixRotate+=(o-r.mixRotate)*a,r.mixX+=(l-r.mixX)*a,r.mixY+=(c-r.mixY)*a}},Se=class{constructor(t){this.data=null,this.tracks=new Array,this.timeScale=1,this.unkeyedState=0,this.events=new Array,this.listeners=new Array,this.queue=new Ji(this),this.propertyIDs=new Ue,this.animationsChanged=!1,this.trackEntryPool=new ue(()=>new Zi),this.data=t}static emptyAnimation(){return ui||(ui=new ye("<empty>",[],0)),ui}update(t){t*=this.timeScale;let e=this.tracks;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(!a)continue;a.animationLast=a.nextAnimationLast,a.trackLast=a.nextTrackLast;let d=t*a.timeScale;if(a.delay>0){if(a.delay-=d,a.delay>0)continue;d=-a.delay,a.delay=0}let n=a.next;if(n){let r=a.trackLast-n.delay;if(r>=0){for(n.delay=0,n.trackTime+=a.timeScale==0?0:(r/a.timeScale+t)*n.timeScale,a.trackTime+=d,this.setCurrent(i,n,!0);n.mixingFrom;)n.mixTime+=t,n=n.mixingFrom;continue}}else if(a.trackLast>=a.trackEnd&&!a.mixingFrom){e[i]=null,this.queue.end(a),this.clearNext(a);continue}if(a.mixingFrom&&this.updateMixingFrom(a,t)){let r=a.mixingFrom;for(a.mixingFrom=null,r&&(r.mixingTo=null);r;)this.queue.end(r),r=r.mixingFrom}a.trackTime+=d}this.queue.drain()}updateMixingFrom(t,e){let i=t.mixingFrom;if(!i)return!0;let s=this.updateMixingFrom(i,e);return i.animationLast=i.nextAnimationLast,i.trackLast=i.nextTrackLast,t.mixTime>0&&t.mixTime>=t.mixDuration?((i.totalAlpha==0||t.mixDuration==0)&&(t.mixingFrom=i.mixingFrom,i.mixingFrom&&(i.mixingFrom.mixingTo=t),t.interruptAlpha=i.interruptAlpha,this.queue.end(i)),s):(i.trackTime+=e*i.timeScale,t.mixTime+=e,!1)}apply(t){if(!t)throw new Error("skeleton cannot be null.");this.animationsChanged&&this._animationsChanged();let e=this.events,i=this.tracks,s=!1;for(let c=0,u=i.length;c<u;c++){let m=i[c];if(!m||m.delay>0)continue;s=!0;let f=c==0?ft.first:m.mixBlend,g=m.alpha;m.mixingFrom?g*=this.applyMixingFrom(m,t,f):m.trackTime>=m.trackEnd&&!m.next&&(g=0);let x=m.animationLast,v=m.getAnimationTime(),p=v,b=e;m.reverse&&(p=m.animation.duration-p,b=null);let w=m.animation.timelines,y=w.length;if(c==0&&g==1||f==ft.add)for(let T=0;T<y;T++){V.webkit602BugfixHelper(g,f);var a=w[T];a instanceof Kt?this.applyAttachmentTimeline(a,t,p,f,!0):a.apply(t,x,p,b,g,f,Vt.mixIn)}else{let T=m.timelineMode,A=m.timelinesRotation.length!=y<<1;A&&(m.timelinesRotation.length=y<<1);for(let E=0;E<y;E++){let I=w[E],Y=T[E]==Ce?f:ft.setup;I instanceof fe?this.applyRotateTimeline(I,t,p,g,Y,m.timelinesRotation,E<<1,A):I instanceof Kt?this.applyAttachmentTimeline(I,t,p,f,!0):(V.webkit602BugfixHelper(g,f),I.apply(t,x,p,b,g,Y,Vt.mixIn))}}this.queueEvents(m,v),e.length=0,m.nextAnimationLast=v,m.nextTrackLast=m.trackTime}for(var d=this.unkeyedState+ci,n=t.slots,r=0,h=t.slots.length;r<h;r++){var o=n[r];if(o.attachmentState==d){var l=o.data.attachmentName;o.setAttachment(l?t.getAttachment(o.data.index,l):null)}}return this.unkeyedState+=2,this.queue.drain(),s}applyMixingFrom(t,e,i){let s=t.mixingFrom;s.mixingFrom&&this.applyMixingFrom(s,e,i);let a=0;t.mixDuration==0?(a=1,i==ft.first&&(i=ft.setup)):(a=t.mixTime/t.mixDuration,a>1&&(a=1),i!=ft.first&&(i=s.mixBlend));let d=a<s.attachmentThreshold,n=a<s.drawOrderThreshold,r=s.animation.timelines,h=r.length,o=s.alpha*t.interruptAlpha,l=o*(1-a),c=s.animationLast,u=s.getAnimationTime(),m=u,f=null;if(s.reverse?m=s.animation.duration-m:a<s.eventThreshold&&(f=this.events),i==ft.add)for(let g=0;g<h;g++)r[g].apply(e,c,m,f,l,i,Vt.mixOut);else{let g=s.timelineMode,x=s.timelineHoldMix,v=s.timelinesRotation.length!=h<<1;v&&(s.timelinesRotation.length=h<<1),s.totalAlpha=0;for(let p=0;p<h;p++){let b=r[p],w=Vt.mixOut,y,T=0;switch(g[p]){case Ce:if(!n&&b instanceof Qt)continue;y=i,T=l;break;case hi:y=ft.setup,T=l;break;case di:y=i,T=o;break;case Te:y=ft.setup,T=o;break;default:y=ft.setup;let A=x[p];T=o*Math.max(0,1-A.mixTime/A.mixDuration);break}s.totalAlpha+=T,b instanceof fe?this.applyRotateTimeline(b,e,m,T,y,s.timelinesRotation,p<<1,v):b instanceof Kt?this.applyAttachmentTimeline(b,e,m,y,d):(V.webkit602BugfixHelper(T,i),n&&b instanceof Qt&&y==ft.setup&&(w=Vt.mixIn),b.apply(e,c,m,f,T,y,w))}}return t.mixDuration>0&&this.queueEvents(s,u),this.events.length=0,s.nextAnimationLast=u,s.nextTrackLast=s.trackTime,a}applyAttachmentTimeline(t,e,i,s,a){var d=e.slots[t.slotIndex];!d.bone.active||(i<t.frames[0]?(s==ft.setup||s==ft.first)&&this.setAttachment(e,d,d.data.attachmentName,a):this.setAttachment(e,d,t.attachmentNames[mt.search1(t.frames,i)],a),d.attachmentState<=this.unkeyedState&&(d.attachmentState=this.unkeyedState+ci))}setAttachment(t,e,i,s){e.setAttachment(i?t.getAttachment(e.data.index,i):null),s&&(e.attachmentState=this.unkeyedState+Qi)}applyRotateTimeline(t,e,i,s,a,d,n,r){if(r&&(d[n]=0),s==1){t.apply(e,0,i,null,1,a,Vt.mixIn);return}let h=e.bones[t.boneIndex];if(!h.active)return;let o=t.frames,l=0,c=0;if(i<o[0])switch(a){case ft.setup:h.rotation=h.data.rotation;default:return;case ft.first:l=h.rotation,c=h.data.rotation}else l=a==ft.setup?h.data.rotation:h.rotation,c=h.data.rotation+t.getCurveValue(i);let u=0,m=c-l;if(m-=(16384-(16384.499999999996-m/360|0))*360,m==0)u=d[n];else{let f=0,g=0;r?(f=0,g=m):(f=d[n],g=d[n+1]);let x=m>0,v=f>=0;F.signum(g)!=F.signum(m)&&Math.abs(g)<=90&&(Math.abs(f)>180&&(f+=360*F.signum(f)),v=x),u=m+f-f%360,v!=x&&(u+=360*F.signum(f)),d[n]=u}d[n+1]=m,h.rotation=l+u*s}queueEvents(t,e){let i=t.animationStart,s=t.animationEnd,a=s-i,d=t.trackLast%a,n=this.events,r=0,h=n.length;for(;r<h;r++){let l=n[r];if(l.time<d)break;l.time>s||this.queue.event(t,l)}let o=!1;for(t.loop?o=a==0||d>t.trackTime%a:o=e>=s&&t.animationLast<s,o&&this.queue.complete(t);r<h;r++){let l=n[r];l.time<i||this.queue.event(t,l)}}clearTracks(){let t=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let e=0,i=this.tracks.length;e<i;e++)this.clearTrack(e);this.tracks.length=0,this.queue.drainDisabled=t,this.queue.drain()}clearTrack(t){if(t>=this.tracks.length)return;let e=this.tracks[t];if(!e)return;this.queue.end(e),this.clearNext(e);let i=e;for(;;){let s=i.mixingFrom;if(!s)break;this.queue.end(s),i.mixingFrom=null,i.mixingTo=null,i=s}this.tracks[e.trackIndex]=null,this.queue.drain()}setCurrent(t,e,i){let s=this.expandToIndex(t);this.tracks[t]=e,e.previous=null,s&&(i&&this.queue.interrupt(s),e.mixingFrom=s,s.mixingTo=e,e.mixTime=0,s.mixingFrom&&s.mixDuration>0&&(e.interruptAlpha*=Math.min(1,s.mixTime/s.mixDuration)),s.timelinesRotation.length=0),this.queue.start(e)}setAnimation(t,e,i=!1){let s=this.data.skeletonData.findAnimation(e);if(!s)throw new Error("Animation not found: "+e);return this.setAnimationWith(t,s,i)}setAnimationWith(t,e,i=!1){if(!e)throw new Error("animation cannot be null.");let s=!0,a=this.expandToIndex(t);a&&(a.nextTrackLast==-1?(this.tracks[t]=a.mixingFrom,this.queue.interrupt(a),this.queue.end(a),this.clearNext(a),a=a.mixingFrom,s=!1):this.clearNext(a));let d=this.trackEntry(t,e,i,a);return this.setCurrent(t,d,s),this.queue.drain(),d}addAnimation(t,e,i=!1,s=0){let a=this.data.skeletonData.findAnimation(e);if(!a)throw new Error("Animation not found: "+e);return this.addAnimationWith(t,a,i,s)}addAnimationWith(t,e,i=!1,s=0){if(!e)throw new Error("animation cannot be null.");let a=this.expandToIndex(t);if(a)for(;a.next;)a=a.next;let d=this.trackEntry(t,e,i,a);return a?(a.next=d,d.previous=a,s<=0&&(s+=a.getTrackComplete()-d.mixDuration)):(this.setCurrent(t,d,!0),this.queue.drain()),d.delay=s,d}setEmptyAnimation(t,e=0){let i=this.setAnimationWith(t,Se.emptyAnimation(),!1);return i.mixDuration=e,i.trackEnd=e,i}addEmptyAnimation(t,e=0,i=0){let s=this.addAnimationWith(t,Se.emptyAnimation(),!1,i);return i<=0&&(s.delay+=s.mixDuration-e),s.mixDuration=e,s.trackEnd=e,s}setEmptyAnimations(t=0){let e=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let i=0,s=this.tracks.length;i<s;i++){let a=this.tracks[i];a&&this.setEmptyAnimation(a.trackIndex,t)}this.queue.drainDisabled=e,this.queue.drain()}expandToIndex(t){return t<this.tracks.length?this.tracks[t]:(V.ensureArrayCapacity(this.tracks,t+1,null),this.tracks.length=t+1,null)}trackEntry(t,e,i,s){let a=this.trackEntryPool.obtain();return a.reset(),a.trackIndex=t,a.animation=e,a.loop=i,a.holdPrevious=!1,a.eventThreshold=0,a.attachmentThreshold=0,a.drawOrderThreshold=0,a.animationStart=0,a.animationEnd=e.duration,a.animationLast=-1,a.nextAnimationLast=-1,a.delay=0,a.trackTime=0,a.trackLast=-1,a.nextTrackLast=-1,a.trackEnd=Number.MAX_VALUE,a.timeScale=1,a.alpha=1,a.interruptAlpha=1,a.mixTime=0,a.mixDuration=s?this.data.getMix(s.animation,e):0,a.mixBlend=ft.replace,a}clearNext(t){let e=t.next;for(;e;)this.queue.dispose(e),e=e.next;t.next=null}_animationsChanged(){this.animationsChanged=!1,this.propertyIDs.clear();let t=this.tracks;for(let e=0,i=t.length;e<i;e++){let s=t[e];if(!!s){for(;s.mixingFrom;)s=s.mixingFrom;do(!s.mixingTo||s.mixBlend!=ft.add)&&this.computeHold(s),s=s.mixingTo;while(s)}}}computeHold(t){let e=t.mixingTo,i=t.animation.timelines,s=t.animation.timelines.length,a=t.timelineMode;a.length=s;let d=t.timelineHoldMix;d.length=0;let n=this.propertyIDs;if(e&&e.holdPrevious){for(let r=0;r<s;r++)a[r]=n.addAll(i[r].getPropertyIds())?Te:di;return}t:for(let r=0;r<s;r++){let h=i[r],o=h.getPropertyIds();if(!n.addAll(o))a[r]=Ce;else if(!e||h instanceof Kt||h instanceof Qt||h instanceof me||!e.animation.hasTimeline(o))a[r]=hi;else{for(let l=e.mixingTo;l;l=l.mixingTo)if(!l.animation.hasTimeline(o)){if(t.mixDuration>0){a[r]=Ki,d[r]=l;continue t}break}a[r]=Te}}}getCurrent(t){return t>=this.tracks.length?null:this.tracks[t]}addListener(t){if(!t)throw new Error("listener cannot be null.");this.listeners.push(t)}removeListener(t){let e=this.listeners.indexOf(t);e>=0&&this.listeners.splice(e,1)}clearListeners(){this.listeners.length=0}clearListenerNotifications(){this.queue.clear()}},Zi=class{constructor(){this.animation=null,this.previous=null,this.next=null,this.mixingFrom=null,this.mixingTo=null,this.listener=null,this.trackIndex=0,this.loop=!1,this.holdPrevious=!1,this.reverse=!1,this.eventThreshold=0,this.attachmentThreshold=0,this.drawOrderThreshold=0,this.animationStart=0,this.animationEnd=0,this.animationLast=0,this.nextAnimationLast=0,this.delay=0,this.trackTime=0,this.trackLast=0,this.nextTrackLast=0,this.trackEnd=0,this.timeScale=0,this.alpha=0,this.mixTime=0,this.mixDuration=0,this.interruptAlpha=0,this.totalAlpha=0,this.mixBlend=ft.replace,this.timelineMode=new Array,this.timelineHoldMix=new Array,this.timelinesRotation=new Array}reset(){this.next=null,this.previous=null,this.mixingFrom=null,this.mixingTo=null,this.animation=null,this.listener=null,this.timelineMode.length=0,this.timelineHoldMix.length=0,this.timelinesRotation.length=0}getAnimationTime(){if(this.loop){let t=this.animationEnd-this.animationStart;return t==0?this.animationStart:this.trackTime%t+this.animationStart}return Math.min(this.trackTime+this.animationStart,this.animationEnd)}setAnimationLast(t){this.animationLast=t,this.nextAnimationLast=t}isComplete(){return this.trackTime>=this.animationEnd-this.animationStart}resetRotationDirections(){this.timelinesRotation.length=0}getTrackComplete(){let t=this.animationEnd-this.animationStart;if(t!=0){if(this.loop)return t*(1+(this.trackTime/t|0));if(this.trackTime<t)return t}return this.trackTime}},Ji=class{constructor(t){this.objects=[],this.drainDisabled=!1,this.animState=null,this.animState=t}start(t){this.objects.push(Ct.start),this.objects.push(t),this.animState.animationsChanged=!0}interrupt(t){this.objects.push(Ct.interrupt),this.objects.push(t)}end(t){this.objects.push(Ct.end),this.objects.push(t),this.animState.animationsChanged=!0}dispose(t){this.objects.push(Ct.dispose),this.objects.push(t)}complete(t){this.objects.push(Ct.complete),this.objects.push(t)}event(t,e){this.objects.push(Ct.event),this.objects.push(t),this.objects.push(e)}drain(){if(this.drainDisabled)return;this.drainDisabled=!0;let t=this.objects,e=this.animState.listeners;for(let i=0;i<t.length;i+=2){let s=t[i],a=t[i+1];switch(s){case Ct.start:a.listener&&a.listener.start&&a.listener.start(a);for(let n=0;n<e.length;n++)e[n].start&&e[n].start(a);break;case Ct.interrupt:a.listener&&a.listener.interrupt&&a.listener.interrupt(a);for(let n=0;n<e.length;n++)e[n].interrupt&&e[n].interrupt(a);break;case Ct.end:a.listener&&a.listener.end&&a.listener.end(a);for(let n=0;n<e.length;n++)e[n].end&&e[n].end(a);case Ct.dispose:a.listener&&a.listener.dispose&&a.listener.dispose(a);for(let n=0;n<e.length;n++)e[n].dispose&&e[n].dispose(a);this.animState.trackEntryPool.free(a);break;case Ct.complete:a.listener&&a.listener.complete&&a.listener.complete(a);for(let n=0;n<e.length;n++)e[n].complete&&e[n].complete(a);break;case Ct.event:let d=t[i+++2];a.listener&&a.listener.event&&a.listener.event(a,d);for(let n=0;n<e.length;n++)e[n].event&&e[n].event(a,d);break}}this.clear(),this.drainDisabled=!1}clear(){this.objects.length=0}},Ct;(function(t){t[t.start=0]="start",t[t.interrupt=1]="interrupt",t[t.end=2]="end",t[t.dispose=3]="dispose",t[t.complete=4]="complete",t[t.event=5]="event"})(Ct||(Ct={}));var Os=class{start(t){}interrupt(t){}end(t){}dispose(t){}complete(t){}event(t,e){}},Ce=0,hi=1,di=2,Te=3,Ki=4,ci=1,Qi=2,ui=null,$i=class{constructor(t){if(this.skeletonData=null,this.animationToMixTime={},this.defaultMix=0,!t)throw new Error("skeletonData cannot be null.");this.skeletonData=t}setMix(t,e,i){let s=this.skeletonData.findAnimation(t);if(!s)throw new Error("Animation not found: "+t);let a=this.skeletonData.findAnimation(e);if(!a)throw new Error("Animation not found: "+e);this.setMixWith(s,a,i)}setMixWith(t,e,i){if(!t)throw new Error("from cannot be null.");if(!e)throw new Error("to cannot be null.");let s=t.name+"."+e.name;this.animationToMixTime[s]=i}getMix(t,e){let i=t.name+"."+e.name,s=this.animationToMixTime[i];return s===void 0?this.defaultMix:s}},ke=class extends Pt{constructor(t){super(t);this.color=new O(1,1,1,1)}copy(){let t=new ke(this.name);return this.copyTo(t),t.color.setFromColor(this.color),t}},ge=class extends Pt{constructor(t){super(t);this.endSlot=null,this.color=new O(.2275,.2275,.8078,1)}copy(){let t=new ge(this.name);return this.copyTo(t),t.endSlot=this.endSlot,t.color.setFromColor(this.color),t}},fi=class{constructor(t){this._image=t}getImage(){return this._image}},pt;(function(t){t[t.Nearest=9728]="Nearest",t[t.Linear=9729]="Linear",t[t.MipMap=9987]="MipMap",t[t.MipMapNearestNearest=9984]="MipMapNearestNearest",t[t.MipMapLinearNearest=9985]="MipMapLinearNearest",t[t.MipMapNearestLinear=9986]="MipMapNearestLinear",t[t.MipMapLinearLinear=9987]="MipMapLinearLinear"})(pt||(pt={}));var $t;(function(t){t[t.MirroredRepeat=33648]="MirroredRepeat",t[t.ClampToEdge=33071]="ClampToEdge",t[t.Repeat=10497]="Repeat"})($t||($t={}));var ts=class{constructor(){this.u=0,this.v=0,this.u2=0,this.v2=0,this.width=0,this.height=0,this.degrees=0,this.offsetX=0,this.offsetY=0,this.originalWidth=0,this.originalHeight=0}},Ns=class extends fi{setFilters(t,e){}setWraps(t,e){}dispose(){}},es=class{constructor(t){this.pages=new Array,this.regions=new Array;let e=new Us(t),i=new Array(4),s=null,a=null,d={};d.size=()=>{s.width=parseInt(i[1]),s.height=parseInt(i[2])},d.format=()=>{},d.filter=()=>{s.minFilter=V.enumValue(pt,i[1]),s.magFilter=V.enumValue(pt,i[2])},d.repeat=()=>{i[1].indexOf("x")!=-1&&(s.uWrap=$t.Repeat),i[1].indexOf("y")!=-1&&(s.vWrap=$t.Repeat)},d.pma=()=>{s.pma=i[1]=="true"};var n={};n.xy=()=>{a.x=parseInt(i[1]),a.y=parseInt(i[2])},n.size=()=>{a.width=parseInt(i[1]),a.height=parseInt(i[2])},n.bounds=()=>{a.x=parseInt(i[1]),a.y=parseInt(i[2]),a.width=parseInt(i[3]),a.height=parseInt(i[4])},n.offset=()=>{a.offsetX=parseInt(i[1]),a.offsetY=parseInt(i[2])},n.orig=()=>{a.originalWidth=parseInt(i[1]),a.originalHeight=parseInt(i[2])},n.offsets=()=>{a.offsetX=parseInt(i[1]),a.offsetY=parseInt(i[2]),a.originalWidth=parseInt(i[3]),a.originalHeight=parseInt(i[4])},n.rotate=()=>{let l=i[1];l=="true"?a.degrees=90:l!="false"&&(a.degrees=parseInt(l))},n.index=()=>{a.index=parseInt(i[1])};let r=e.readLine();for(;r&&r.trim().length==0;)r=e.readLine();for(;!(!r||r.trim().length==0||e.readEntry(i,r)==0);)r=e.readLine();let h=null,o=null;for(;r!==null;)if(r.trim().length==0)s=null,r=e.readLine();else if(s){for(a=new mi,a.page=s,a.name=r;;){let l=e.readEntry(i,r=e.readLine());if(l==0)break;let c=n[i[0]];if(c)c();else{h||(h=[],o=[]),h.push(i[0]);let u=[];for(let m=0;m<l;m++)u.push(parseInt(i[m+1]));o.push(u)}}a.originalWidth==0&&a.originalHeight==0&&(a.originalWidth=a.width,a.originalHeight=a.height),h&&h.length>0&&(a.names=h,a.values=o,h=null,o=null),a.u=a.x/s.width,a.v=a.y/s.height,a.degrees==90?(a.u2=(a.x+a.height)/s.width,a.v2=(a.y+a.width)/s.height):(a.u2=(a.x+a.width)/s.width,a.v2=(a.y+a.height)/s.height),this.regions.push(a)}else{for(s=new is,s.name=r.trim();e.readEntry(i,r=e.readLine())!=0;){let l=d[i[0]];l&&l()}this.pages.push(s)}}findRegion(t){for(let e=0;e<this.regions.length;e++)if(this.regions[e].name==t)return this.regions[e];return null}setTextures(t,e=""){for(let i of this.pages)i.setTexture(t.get(e+i.name))}dispose(){for(let t=0;t<this.pages.length;t++)this.pages[t].texture.dispose()}},Us=class{constructor(t){this.lines=null,this.index=0,this.lines=t.split(/\r\n|\r|\n/)}readLine(){return this.index>=this.lines.length?null:this.lines[this.index++]}readEntry(t,e){if(!e||(e=e.trim(),e.length==0))return 0;let i=e.indexOf(":");if(i==-1)return 0;t[0]=e.substr(0,i).trim();for(let s=1,a=i+1;;s++){let d=e.indexOf(",",a);if(d==-1)return t[s]=e.substr(a).trim(),s;if(t[s]=e.substr(a,d-a).trim(),a=d+1,s==4)return 4}}},is=class{constructor(){this.name=null,this.minFilter=pt.Nearest,this.magFilter=pt.Nearest,this.uWrap=$t.ClampToEdge,this.vWrap=$t.ClampToEdge,this.texture=null,this.width=0,this.height=0,this.pma=!1}setTexture(t){this.texture=t,t.setFilters(this.minFilter,this.magFilter),t.setWraps(this.uWrap,this.vWrap)}},mi=class extends ts{constructor(){super(...arguments);this.page=null,this.name=null,this.x=0,this.y=0,this.offsetX=0,this.offsetY=0,this.originalWidth=0,this.originalHeight=0,this.index=0,this.degrees=0,this.names=null,this.values=null}},jt=class extends Pt{constructor(t){super(t);this.region=null,this.path=null,this.regionUVs=null,this.uvs=null,this.triangles=null,this.color=new O(1,1,1,1),this.width=0,this.height=0,this.hullLength=0,this.edges=null,this.parentMesh=null,this.tempColor=new O(0,0,0,0)}updateUVs(){let t=this.regionUVs;(!this.uvs||this.uvs.length!=t.length)&&(this.uvs=V.newFloatArray(t.length));let e=this.uvs,i=this.uvs.length,s=this.region.u,a=this.region.v,d=0,n=0;if(this.region instanceof mi){let r=this.region,h=r.page.texture.getImage(),o=h.width,l=h.height;switch(r.degrees){case 90:s-=(r.originalHeight-r.offsetY-r.height)/o,a-=(r.originalWidth-r.offsetX-r.width)/l,d=r.originalHeight/o,n=r.originalWidth/l;for(let c=0;c<i;c+=2)e[c]=s+t[c+1]*d,e[c+1]=a+(1-t[c])*n;return;case 180:s-=(r.originalWidth-r.offsetX-r.width)/o,a-=r.offsetY/l,d=r.originalWidth/o,n=r.originalHeight/l;for(let c=0;c<i;c+=2)e[c]=s+(1-t[c])*d,e[c+1]=a+(1-t[c+1])*n;return;case 270:s-=r.offsetY/o,a-=r.offsetX/l,d=r.originalHeight/o,n=r.originalWidth/l;for(let c=0;c<i;c+=2)e[c]=s+(1-t[c+1])*d,e[c+1]=a+t[c]*n;return}s-=r.offsetX/o,a-=(r.originalHeight-r.offsetY-r.height)/l,d=r.originalWidth/o,n=r.originalHeight/l}else this.region?(d=this.region.u2-s,n=this.region.v2-a):(s=a=0,d=n=1);for(let r=0;r<i;r+=2)e[r]=s+t[r]*d,e[r+1]=a+t[r+1]*n}getParentMesh(){return this.parentMesh}setParentMesh(t){this.parentMesh=t,t&&(this.bones=t.bones,this.vertices=t.vertices,this.worldVerticesLength=t.worldVerticesLength,this.regionUVs=t.regionUVs,this.triangles=t.triangles,this.hullLength=t.hullLength,this.worldVerticesLength=t.worldVerticesLength)}copy(){if(this.parentMesh)return this.newLinkedMesh();let t=new jt(this.name);return t.region=this.region,t.path=this.path,t.color.setFromColor(this.color),this.copyTo(t),t.regionUVs=new Array(this.regionUVs.length),V.arrayCopy(this.regionUVs,0,t.regionUVs,0,this.regionUVs.length),t.uvs=new Array(this.uvs.length),V.arrayCopy(this.uvs,0,t.uvs,0,this.uvs.length),t.triangles=new Array(this.triangles.length),V.arrayCopy(this.triangles,0,t.triangles,0,this.triangles.length),t.hullLength=this.hullLength,this.edges&&(t.edges=new Array(this.edges.length),V.arrayCopy(this.edges,0,t.edges,0,this.edges.length)),t.width=this.width,t.height=this.height,t}newLinkedMesh(){let t=new jt(this.name);return t.region=this.region,t.path=this.path,t.color.setFromColor(this.color),t.deformAttachment=this.deformAttachment,t.setParentMesh(this.parentMesh?this.parentMesh:this),t.updateUVs(),t}},te=class extends Pt{constructor(t){super(t);this.lengths=null,this.closed=!1,this.constantSpeed=!1,this.color=new O(1,1,1,1)}copy(){let t=new te(this.name);return this.copyTo(t),t.lengths=new Array(this.lengths.length),V.arrayCopy(this.lengths,0,t.lengths,0,this.lengths.length),t.closed=closed,t.constantSpeed=this.constantSpeed,t.color.setFromColor(this.color),t}},gi=class extends Pt{constructor(t){super(t);this.x=0,this.y=0,this.rotation=0,this.color=new O(.38,.94,0,1)}computeWorldPosition(t,e){return e.x=this.x*t.a+this.y*t.b+t.worldX,e.y=this.x*t.c+this.y*t.d+t.worldY,e}computeWorldRotation(t){let e=F.cosDeg(this.rotation),i=F.sinDeg(this.rotation),s=e*t.a+i*t.b,a=e*t.c+i*t.d;return Math.atan2(a,s)*F.radDeg}copy(){let t=new gi(this.name);return t.x=this.x,t.y=this.y,t.rotation=this.rotation,t.color.setFromColor(this.color),t}},ss=class extends ze{constructor(t){super(t);this.x=0,this.y=0,this.scaleX=1,this.scaleY=1,this.rotation=0,this.width=0,this.height=0,this.color=new O(1,1,1,1),this.path=null,this.rendererObject=null,this.region=null,this.offset=V.newFloatArray(8),this.uvs=V.newFloatArray(8),this.tempColor=new O(1,1,1,1)}updateOffset(){let t=this.region,e=this.width/this.region.originalWidth*this.scaleX,i=this.height/this.region.originalHeight*this.scaleY,s=-this.width/2*this.scaleX+this.region.offsetX*e,a=-this.height/2*this.scaleY+this.region.offsetY*i,d=s+this.region.width*e,n=a+this.region.height*i,r=this.rotation*Math.PI/180,h=Math.cos(r),o=Math.sin(r),l=this.x,c=this.y,u=s*h+l,m=s*o,f=a*h+c,g=a*o,x=d*h+l,v=d*o,p=n*h+c,b=n*o,w=this.offset;w[0]=u-g,w[1]=f+m,w[2]=u-b,w[3]=p+m,w[4]=x-b,w[5]=p+v,w[6]=x-g,w[7]=f+v}setRegion(t){this.region=t;let e=this.uvs;t.degrees==90?(e[2]=t.u,e[3]=t.v2,e[4]=t.u,e[5]=t.v,e[6]=t.u2,e[7]=t.v,e[0]=t.u2,e[1]=t.v2):(e[0]=t.u,e[1]=t.v2,e[2]=t.u,e[3]=t.v,e[4]=t.u2,e[5]=t.v,e[6]=t.u2,e[7]=t.v2)}computeWorldVertices(t,e,i,s){let a=this.offset,d=t.worldX,n=t.worldY,r=t.a,h=t.b,o=t.c,l=t.d,c=0,u=0;c=a[0],u=a[1],e[i]=c*r+u*h+d,e[i+1]=c*o+u*l+n,i+=s,c=a[2],u=a[3],e[i]=c*r+u*h+d,e[i+1]=c*o+u*l+n,i+=s,c=a[4],u=a[5],e[i]=c*r+u*h+d,e[i+1]=c*o+u*l+n,i+=s,c=a[6],u=a[7],e[i]=c*r+u*h+d,e[i+1]=c*o+u*l+n}copy(){let t=new ss(this.name);return t.region=this.region,t.rendererObject=this.rendererObject,t.path=this.path,t.x=this.x,t.y=this.y,t.scaleX=this.scaleX,t.scaleY=this.scaleY,t.rotation=this.rotation,t.width=this.width,t.height=this.height,V.arrayCopy(this.uvs,0,t.uvs,0,8),V.arrayCopy(this.offset,0,t.offset,0,8),t.color.setFromColor(this.color),t}},at=ss;at.X1=0,at.Y1=1,at.C1R=2,at.C1G=3,at.C1B=4,at.C1A=5,at.U1=6,at.V1=7,at.X2=8,at.Y2=9,at.C2R=10,at.C2G=11,at.C2B=12,at.C2A=13,at.U2=14,at.V2=15,at.X3=16,at.Y3=17,at.C3R=18,at.C3G=19,at.C3B=20,at.C3A=21,at.U3=22,at.V3=23,at.X4=24,at.Y4=25,at.C4R=26,at.C4G=27,at.C4B=28,at.C4A=29,at.U4=30,at.V4=31;var pi=class{constructor(t){this.atlas=null,this.atlas=t}newRegionAttachment(t,e,i){let s=this.atlas.findRegion(i);if(!s)throw new Error("Region not found in atlas: "+i+" (region attachment: "+e+")");s.renderObject=s;let a=new at(e);return a.setRegion(s),a}newMeshAttachment(t,e,i){let s=this.atlas.findRegion(i);if(!s)throw new Error("Region not found in atlas: "+i+" (mesh attachment: "+e+")");s.renderObject=s;let a=new jt(e);return a.region=s,a}newBoundingBoxAttachment(t,e){return new ke(e)}newPathAttachment(t,e){return new te(e)}newPointAttachment(t,e){return new gi(e)}newClippingAttachment(t,e){return new ge(e)}},xi=class{constructor(t,e,i){if(this.index=0,this.name=null,this.parent=null,this.length=0,this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1,this.shearX=0,this.shearY=0,this.transformMode=Tt.Normal,this.skinRequired=!1,this.color=new O,t<0)throw new Error("index must be >= 0.");if(!e)throw new Error("name cannot be null.");this.index=t,this.name=e,this.parent=i}},Tt;(function(t){t[t.Normal=0]="Normal",t[t.OnlyTranslation=1]="OnlyTranslation",t[t.NoRotationOrReflection=2]="NoRotationOrReflection",t[t.NoScale=3]="NoScale",t[t.NoScaleOrReflection=4]="NoScaleOrReflection"})(Tt||(Tt={}));var wi=class{constructor(t,e,i){if(this.data=null,this.skeleton=null,this.parent=null,this.children=new Array,this.x=0,this.y=0,this.rotation=0,this.scaleX=0,this.scaleY=0,this.shearX=0,this.shearY=0,this.ax=0,this.ay=0,this.arotation=0,this.ascaleX=0,this.ascaleY=0,this.ashearX=0,this.ashearY=0,this.a=0,this.b=0,this.c=0,this.d=0,this.worldY=0,this.worldX=0,this.sorted=!1,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.skeleton=e,this.parent=i,this.setToSetupPose()}isActive(){return this.active}update(){this.updateWorldTransformWith(this.ax,this.ay,this.arotation,this.ascaleX,this.ascaleY,this.ashearX,this.ashearY)}updateWorldTransform(){this.updateWorldTransformWith(this.x,this.y,this.rotation,this.scaleX,this.scaleY,this.shearX,this.shearY)}updateWorldTransformWith(t,e,i,s,a,d,n){this.ax=t,this.ay=e,this.arotation=i,this.ascaleX=s,this.ascaleY=a,this.ashearX=d,this.ashearY=n;let r=this.parent;if(!r){let u=this.skeleton,m=i+90+n,f=u.scaleX,g=u.scaleY;this.a=F.cosDeg(i+d)*s*f,this.b=F.cosDeg(m)*a*f,this.c=F.sinDeg(i+d)*s*g,this.d=F.sinDeg(m)*a*g,this.worldX=t*f+u.x,this.worldY=e*g+u.y;return}let h=r.a,o=r.b,l=r.c,c=r.d;switch(this.worldX=h*t+o*e+r.worldX,this.worldY=l*t+c*e+r.worldY,this.data.transformMode){case Tt.Normal:{let u=i+90+n,m=F.cosDeg(i+d)*s,f=F.cosDeg(u)*a,g=F.sinDeg(i+d)*s,x=F.sinDeg(u)*a;this.a=h*m+o*g,this.b=h*f+o*x,this.c=l*m+c*g,this.d=l*f+c*x;return}case Tt.OnlyTranslation:{let u=i+90+n;this.a=F.cosDeg(i+d)*s,this.b=F.cosDeg(u)*a,this.c=F.sinDeg(i+d)*s,this.d=F.sinDeg(u)*a;break}case Tt.NoRotationOrReflection:{let u=h*h+l*l,m=0;u>1e-4?(u=Math.abs(h*c-o*l)/u,h/=this.skeleton.scaleX,l/=this.skeleton.scaleY,o=l*u,c=h*u,m=Math.atan2(l,h)*F.radDeg):(h=0,l=0,m=90-Math.atan2(c,o)*F.radDeg);let f=i+d-m,g=i+n-m+90,x=F.cosDeg(f)*s,v=F.cosDeg(g)*a,p=F.sinDeg(f)*s,b=F.sinDeg(g)*a;this.a=h*x-o*p,this.b=h*v-o*b,this.c=l*x+c*p,this.d=l*v+c*b;break}case Tt.NoScale:case Tt.NoScaleOrReflection:{let u=F.cosDeg(i),m=F.sinDeg(i),f=(h*u+o*m)/this.skeleton.scaleX,g=(l*u+c*m)/this.skeleton.scaleY,x=Math.sqrt(f*f+g*g);x>1e-5&&(x=1/x),f*=x,g*=x,x=Math.sqrt(f*f+g*g),this.data.transformMode==Tt.NoScale&&h*c-o*l<0!=(this.skeleton.scaleX<0!=this.skeleton.scaleY<0)&&(x=-x);let v=Math.PI/2+Math.atan2(g,f),p=Math.cos(v)*x,b=Math.sin(v)*x,w=F.cosDeg(d)*s,y=F.cosDeg(90+n)*a,T=F.sinDeg(d)*s,A=F.sinDeg(90+n)*a;this.a=f*w+p*T,this.b=f*y+p*A,this.c=g*w+b*T,this.d=g*y+b*A;break}}this.a*=this.skeleton.scaleX,this.b*=this.skeleton.scaleX,this.c*=this.skeleton.scaleY,this.d*=this.skeleton.scaleY}setToSetupPose(){let t=this.data;this.x=t.x,this.y=t.y,this.rotation=t.rotation,this.scaleX=t.scaleX,this.scaleY=t.scaleY,this.shearX=t.shearX,this.shearY=t.shearY}getWorldRotationX(){return Math.atan2(this.c,this.a)*F.radDeg}getWorldRotationY(){return Math.atan2(this.d,this.b)*F.radDeg}getWorldScaleX(){return Math.sqrt(this.a*this.a+this.c*this.c)}getWorldScaleY(){return Math.sqrt(this.b*this.b+this.d*this.d)}updateAppliedTransform(){let t=this.parent;if(!t){this.ax=this.worldX-this.skeleton.x,this.ay=this.worldY-this.skeleton.y,this.arotation=Math.atan2(this.c,this.a)*F.radDeg,this.ascaleX=Math.sqrt(this.a*this.a+this.c*this.c),this.ascaleY=Math.sqrt(this.b*this.b+this.d*this.d),this.ashearX=0,this.ashearY=Math.atan2(this.a*this.b+this.c*this.d,this.a*this.d-this.b*this.c)*F.radDeg;return}let e=t.a,i=t.b,s=t.c,a=t.d,d=1/(e*a-i*s),n=this.worldX-t.worldX,r=this.worldY-t.worldY;this.ax=n*a*d-r*i*d,this.ay=r*e*d-n*s*d;let h=d*a,o=d*e,l=d*i,c=d*s,u=h*this.a-l*this.c,m=h*this.b-l*this.d,f=o*this.c-c*this.a,g=o*this.d-c*this.b;if(this.ashearX=0,this.ascaleX=Math.sqrt(u*u+f*f),this.ascaleX>1e-4){let x=u*g-m*f;this.ascaleY=x/this.ascaleX,this.ashearY=Math.atan2(u*m+f*g,x)*F.radDeg,this.arotation=Math.atan2(f,u)*F.radDeg}else this.ascaleX=0,this.ascaleY=Math.sqrt(m*m+g*g),this.ashearY=0,this.arotation=90-Math.atan2(g,m)*F.radDeg}worldToLocal(t){let e=1/(this.a*this.d-this.b*this.c),i=t.x-this.worldX,s=t.y-this.worldY;return t.x=i*this.d*e-s*this.b*e,t.y=s*this.a*e-i*this.c*e,t}localToWorld(t){let e=t.x,i=t.y;return t.x=e*this.a+i*this.b+this.worldX,t.y=e*this.c+i*this.d+this.worldY,t}worldToLocalRotation(t){let e=F.sinDeg(t),i=F.cosDeg(t);return Math.atan2(this.a*e-this.c*i,this.d*i-this.b*e)*F.radDeg+this.rotation-this.shearX}localToWorldRotation(t){t-=this.rotation-this.shearX;let e=F.sinDeg(t),i=F.cosDeg(t);return Math.atan2(i*this.c+e*this.d,i*this.a+e*this.b)*F.radDeg}rotateWorld(t){let e=this.a,i=this.b,s=this.c,a=this.d,d=F.cosDeg(t),n=F.sinDeg(t);this.a=d*e-n*s,this.b=d*i-n*a,this.c=n*e+d*s,this.d=n*i+d*a}},Me=class{constructor(t,e,i){this.name=t,this.order=e,this.skinRequired=i}},rs=class{constructor(t,e="",i=null){this.pathPrefix=null,this.assets={},this.errors={},this.toLoad=0,this.loaded=0,this.textureLoader=t,this.pathPrefix=e,this.downloader=i||new as}start(t){return this.toLoad++,this.pathPrefix+t}success(t,e,i){this.toLoad--,this.loaded++,this.assets[e]=i,t&&t(e,i)}error(t,e,i){this.toLoad--,this.loaded++,this.errors[e]=i,t&&t(e,i)}loadAll(){return new Promise((e,i)=>{let s=()=>{if(this.isLoadingComplete()){this.hasErrors()?i(this.errors):e(this);return}requestAnimationFrame(s)};requestAnimationFrame(s)})}setRawDataURI(t,e){this.downloader.rawDataUris[this.pathPrefix+t]=e}loadBinary(t,e=null,i=null){t=this.start(t),this.downloader.downloadBinary(t,s=>{this.success(e,t,s)},(s,a)=>{this.error(i,t,`Couldn't load binary ${t}: status ${s}, ${a}`)})}loadText(t,e=null,i=null){t=this.start(t),this.downloader.downloadText(t,s=>{this.success(e,t,s)},(s,a)=>{this.error(i,t,`Couldn't load text ${t}: status ${s}, ${a}`)})}loadJson(t,e=null,i=null){t=this.start(t),this.downloader.downloadJson(t,s=>{this.success(e,t,s)},(s,a)=>{this.error(i,t,`Couldn't load JSON ${t}: status ${s}, ${a}`)})}loadTexture(t,e=null,i=null){if(t=this.start(t),!!!(typeof window!="undefined"&&typeof navigator!="undefined"&&window.document))fetch(t,{mode:"cors"}).then(d=>d.ok?d.blob():(this.error(i,t,`Couldn't load image: ${t}`),null)).then(d=>d?createImageBitmap(d,{premultiplyAlpha:"none",colorSpaceConversion:"none"}):null).then(d=>{d&&this.success(e,t,this.textureLoader(d))});else{let d=new Image;d.crossOrigin="anonymous",d.onload=()=>{this.success(e,t,this.textureLoader(d))},d.onerror=()=>{this.error(i,t,`Couldn't load image: ${t}`)},this.downloader.rawDataUris[t]&&(t=this.downloader.rawDataUris[t]),d.src=t}}loadTextureAtlas(t,e=null,i=null,s=null){let a=t.lastIndexOf("/"),d=a>=0?t.substring(0,a+1):"";t=this.start(t),this.downloader.downloadText(t,n=>{try{let r=new es(n),h=r.pages.length,o=!1;for(let l of r.pages)this.loadTexture(s==null?d+l.name:s[l.name],(c,u)=>{o||(l.setTexture(u),--h==0&&this.success(e,t,r))},(c,u)=>{o||this.error(i,t,`Couldn't load texture atlas ${t} page image: ${c}`),o=!0})}catch(r){this.error(i,t,`Couldn't parse texture atlas ${t}: ${r.message}`)}},(n,r)=>{this.error(i,t,`Couldn't load texture atlas ${t}: status ${n}, ${r}`)})}get(t){return this.assets[this.pathPrefix+t]}require(t){t=this.pathPrefix+t;let e=this.assets[t];if(e)return e;let i=this.errors[t];throw Error("Asset not found: "+t+(i?`
`+i:""))}remove(t){t=this.pathPrefix+t;let e=this.assets[t];return e.dispose&&e.dispose(),delete this.assets[t],e}removeAll(){for(let t in this.assets){let e=this.assets[t];e.dispose&&e.dispose()}this.assets={}}isLoadingComplete(){return this.toLoad==0}getToLoad(){return this.toLoad}getLoaded(){return this.loaded}dispose(){this.removeAll()}hasErrors(){return Object.keys(this.errors).length>0}getErrors(){return this.errors}},as=class{constructor(){this.callbacks={},this.rawDataUris={}}dataUriToString(t){if(!t.startsWith("data:"))throw new Error("Not a data URI.");let e=t.indexOf("base64,");return e!=-1?(e+="base64,".length,atob(t.substr(e))):t.substr(t.indexOf(",")+1)}base64ToUint8Array(t){for(var e=window.atob(t),i=e.length,s=new Uint8Array(i),a=0;a<i;a++)s[a]=e.charCodeAt(a);return s}dataUriToUint8Array(t){if(!t.startsWith("data:"))throw new Error("Not a data URI.");let e=t.indexOf("base64,");if(e==-1)throw new Error("Not a binary data URI.");return e+="base64,".length,this.base64ToUint8Array(t.substr(e))}downloadText(t,e,i){if(this.start(t,e,i))return;if(this.rawDataUris[t]){try{let d=this.rawDataUris[t];this.finish(t,200,this.dataUriToString(d))}catch(d){this.finish(t,400,JSON.stringify(d))}return}let s=new XMLHttpRequest;s.overrideMimeType("text/html"),s.open("GET",t,!0);let a=()=>{this.finish(t,s.status,s.responseText)};s.onload=a,s.onerror=a,s.send()}downloadJson(t,e,i){this.downloadText(t,s=>{e(JSON.parse(s))},i)}downloadBinary(t,e,i){if(this.start(t,e,i))return;if(this.rawDataUris[t]){try{let d=this.rawDataUris[t];this.finish(t,200,this.dataUriToUint8Array(d))}catch(d){this.finish(t,400,JSON.stringify(d))}return}let s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType="arraybuffer";let a=()=>{this.finish(t,s.status,s.response)};s.onload=()=>{s.status==200||s.status==0?this.finish(t,200,new Uint8Array(s.response)):a()},s.onerror=a,s.send()}start(t,e,i){let s=this.callbacks[t];try{if(s)return!0;this.callbacks[t]=s=[]}finally{s.push(e,i)}}finish(t,e,i){let s=this.callbacks[t];delete this.callbacks[t];let a=e==200||e==0?[i]:[e,i];for(let d=a.length-1,n=s.length;d<n;d+=2)s[d].apply(null,a)}},bi=class{constructor(t,e){if(this.data=null,this.intValue=0,this.floatValue=0,this.stringValue=null,this.time=0,this.volume=0,this.balance=0,!e)throw new Error("data cannot be null.");this.time=t,this.data=e}},vi=class{constructor(t){this.name=null,this.intValue=0,this.floatValue=0,this.stringValue=null,this.audioPath=null,this.volume=0,this.balance=0,this.name=t}},ns=class{constructor(t,e){if(this.data=null,this.bones=null,this.target=null,this.bendDirection=0,this.compress=!1,this.stretch=!1,this.mix=1,this.softness=0,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.mix=t.mix,this.softness=t.softness,this.bendDirection=t.bendDirection,this.compress=t.compress,this.stretch=t.stretch,this.bones=new Array;for(let i=0;i<t.bones.length;i++)this.bones.push(e.findBone(t.bones[i].name));this.target=e.findBone(t.target.name)}isActive(){return this.active}update(){if(this.mix==0)return;let t=this.target,e=this.bones;switch(e.length){case 1:this.apply1(e[0],t.worldX,t.worldY,this.compress,this.stretch,this.data.uniform,this.mix);break;case 2:this.apply2(e[0],e[1],t.worldX,t.worldY,this.bendDirection,this.stretch,this.data.uniform,this.softness,this.mix);break}}apply1(t,e,i,s,a,d,n){let r=t.parent,h=r.a,o=r.b,l=r.c,c=r.d,u=-t.ashearX-t.arotation,m=0,f=0;switch(t.data.transformMode){case Tt.OnlyTranslation:m=e-t.worldX,f=i-t.worldY;break;case Tt.NoRotationOrReflection:let v=Math.abs(h*c-o*l)/(h*h+l*l),p=h/t.skeleton.scaleX,b=l/t.skeleton.scaleY;o=-b*v*t.skeleton.scaleX,c=p*v*t.skeleton.scaleY,u+=Math.atan2(b,p)*F.radDeg;default:let w=e-r.worldX,y=i-r.worldY,T=h*c-o*l;m=(w*c-y*o)/T-t.ax,f=(y*h-w*l)/T-t.ay}u+=Math.atan2(f,m)*F.radDeg,t.ascaleX<0&&(u+=180),u>180?u-=360:u<-180&&(u+=360);let g=t.ascaleX,x=t.ascaleY;if(s||a){switch(t.data.transformMode){case Tt.NoScale:case Tt.NoScaleOrReflection:m=e-t.worldX,f=i-t.worldY}let v=t.data.length*g,p=Math.sqrt(m*m+f*f);if(s&&p<v||a&&p>v&&v>1e-4){let b=(p/v-1)*n+1;g*=b,d&&(x*=b)}}t.updateWorldTransformWith(t.ax,t.ay,t.arotation+u*n,g,x,t.ashearX,t.ashearY)}apply2(t,e,i,s,a,d,n,r,h){let o=t.ax,l=t.ay,c=t.ascaleX,u=t.ascaleY,m=c,f=u,g=e.ascaleX,x=0,v=0,p=0;c<0?(c=-c,x=180,p=-1):(x=0,p=1),u<0&&(u=-u,p=-p),g<0?(g=-g,v=180):v=0;let b=e.ax,w=0,y=0,T=0,A=t.a,E=t.b,I=t.c,Y=t.d,P=Math.abs(c-u)<=1e-4;!P||d?(w=0,y=A*b+t.worldX,T=I*b+t.worldY):(w=e.ay,y=A*b+E*w+t.worldX,T=I*b+Y*w+t.worldY);let X=t.parent;A=X.a,E=X.b,I=X.c,Y=X.d;let B=1/(A*Y-E*I),C=y-X.worldX,k=T-X.worldY,D=(C*Y-k*E)*B-o,R=(k*A-C*I)*B-l,L=Math.sqrt(D*D+R*R),st=e.data.length*g,ct,ht;if(L<1e-4){this.apply1(t,i,s,!1,d,!1,h),e.updateWorldTransformWith(b,w,0,e.ascaleX,e.ascaleY,e.ashearX,e.ashearY);return}C=i-X.worldX,k=s-X.worldY;let rt=(C*Y-k*E)*B-o,it=(k*A-C*I)*B-l,dt=rt*rt+it*it;if(r!=0){r*=c*(g+1)*.5;let vt=Math.sqrt(dt),Et=vt-L-st*c+r;if(Et>0){let It=Math.min(1,Et/(r*2))-1;It=(Et-r*(1-It*It))/vt,rt-=It*rt,it-=It*it,dt=rt*rt+it*it}}t:if(P){st*=c;let vt=(dt-L*L-st*st)/(2*L*st);vt<-1?(vt=-1,ht=Math.PI*a):vt>1?(vt=1,ht=0,d&&(A=(Math.sqrt(dt)/(L+st)-1)*h+1,m*=A,n&&(f*=A))):ht=Math.acos(vt)*a,A=L+st*vt,E=st*Math.sin(ht),ct=Math.atan2(it*A-rt*E,rt*A+it*E)}else{A=c*st,E=u*st;let vt=A*A,Et=E*E,It=Math.atan2(it,rt);I=Et*L*L+vt*dt-vt*Et;let Zt=-2*Et*L,he=Et-vt;if(Y=Zt*Zt-4*he*I,Y>=0){let de=Math.sqrt(Y);Zt<0&&(de=-de),de=-(Zt+de)*.5;let Fs=de/he,Ls=I/de,ce=Math.abs(Fs)<Math.abs(Ls)?Fs:Ls;if(ce*ce<=dt){k=Math.sqrt(dt-ce*ce)*a,ct=It-Math.atan2(k,ce),ht=Math.atan2(k/u,(ce-L)/c);break t}}let be=F.PI,Oe=L-A,Oi=Oe*Oe,Is=0,Ys=0,Ne=L+A,Ni=Ne*Ne,Rs=0;I=-A*L/(vt-Et),I>=-1&&I<=1&&(I=Math.acos(I),C=A*Math.cos(I)+L,k=E*Math.sin(I),Y=C*C+k*k,Y<Oi&&(be=I,Oi=Y,Oe=C,Is=k),Y>Ni&&(Ys=I,Ni=Y,Ne=C,Rs=k)),dt<=(Oi+Ni)*.5?(ct=It-Math.atan2(Is*a,Oe),ht=be*a):(ct=It-Math.atan2(Rs*a,Ne),ht=Ys*a)}let St=Math.atan2(w,b)*p,Bt=t.arotation;ct=(ct-St)*F.radDeg+x-Bt,ct>180?ct-=360:ct<-180&&(ct+=360),t.updateWorldTransformWith(o,l,Bt+ct*h,m,f,0,0),Bt=e.arotation,ht=((ht+St)*F.radDeg-e.ashearX)*p+v-Bt,ht>180?ht-=360:ht<-180&&(ht+=360),e.updateWorldTransformWith(b,w,Bt+ht*h,e.ascaleX,e.ascaleY,e.ashearX,e.ashearY)}},yi=class extends Me{constructor(t){super(t,0,!1);this.bones=new Array,this.target=null,this.bendDirection=1,this.compress=!1,this.stretch=!1,this.uniform=!1,this.mix=1,this.softness=0}},Ai=class extends Me{constructor(t){super(t,0,!1);this.bones=new Array,this.target=null,this.positionMode=null,this.spacingMode=null,this.rotateMode=null,this.offsetRotation=0,this.position=0,this.spacing=0,this.mixRotate=0,this.mixX=0,this.mixY=0}},Dt;(function(t){t[t.Fixed=0]="Fixed",t[t.Percent=1]="Percent"})(Dt||(Dt={}));var gt;(function(t){t[t.Length=0]="Length",t[t.Fixed=1]="Fixed",t[t.Percent=2]="Percent",t[t.Proportional=3]="Proportional"})(gt||(gt={}));var ee;(function(t){t[t.Tangent=0]="Tangent",t[t.Chain=1]="Chain",t[t.ChainScale=2]="ChainScale"})(ee||(ee={}));var Ut=class{constructor(t,e){if(this.data=null,this.bones=null,this.target=null,this.position=0,this.spacing=0,this.mixRotate=0,this.mixX=0,this.mixY=0,this.spaces=new Array,this.positions=new Array,this.world=new Array,this.curves=new Array,this.lengths=new Array,this.segments=new Array,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.bones=new Array;for(let i=0,s=t.bones.length;i<s;i++)this.bones.push(e.findBone(t.bones[i].name));this.target=e.findSlot(t.target.name),this.position=t.position,this.spacing=t.spacing,this.mixRotate=t.mixRotate,this.mixX=t.mixX,this.mixY=t.mixY}isActive(){return this.active}update(){let t=this.target.getAttachment();if(!(t instanceof te))return;let e=this.mixRotate,i=this.mixX,s=this.mixY;if(e==0&&i==0&&s==0)return;let a=this.data,d=a.rotateMode==ee.Tangent,n=a.rotateMode==ee.ChainScale,r=this.bones,h=r.length,o=d?h:h+1,l=V.setArraySize(this.spaces,o),c=n?this.lengths=V.setArraySize(this.lengths,h):null,u=this.spacing;switch(a.spacingMode){case gt.Percent:if(n)for(let w=0,y=o-1;w<y;w++){let T=r[w],A=T.data.length;if(A<Ut.epsilon)c[w]=0;else{let E=A*T.a,I=A*T.c;c[w]=Math.sqrt(E*E+I*I)}}V.arrayFill(l,1,o,u);break;case gt.Proportional:let p=0;for(let w=0,y=o-1;w<y;){let T=r[w],A=T.data.length;if(A<Ut.epsilon)n&&(c[w]=0),l[++w]=u;else{let E=A*T.a,I=A*T.c,Y=Math.sqrt(E*E+I*I);n&&(c[w]=Y),l[++w]=Y,p+=Y}}if(p>0){p=o/p*u;for(let w=1;w<o;w++)l[w]*=p}break;default:let b=a.spacingMode==gt.Length;for(let w=0,y=o-1;w<y;){let T=r[w],A=T.data.length;if(A<Ut.epsilon)n&&(c[w]=0),l[++w]=u;else{let E=A*T.a,I=A*T.c,Y=Math.sqrt(E*E+I*I);n&&(c[w]=Y),l[++w]=(b?A+u:u)*Y/A}}}let m=this.computeWorldPositions(t,o,d),f=m[0],g=m[1],x=a.offsetRotation,v=!1;if(x==0)v=a.rotateMode==ee.Chain;else{v=!1;let p=this.target.bone;x*=p.a*p.d-p.b*p.c>0?F.degRad:-F.degRad}for(let p=0,b=3;p<h;p++,b+=3){let w=r[p];w.worldX+=(f-w.worldX)*i,w.worldY+=(g-w.worldY)*s;let y=m[b],T=m[b+1],A=y-f,E=T-g;if(n){let I=c[p];if(I!=0){let Y=(Math.sqrt(A*A+E*E)/I-1)*e+1;w.a*=Y,w.c*=Y}}if(f=y,g=T,e>0){let I=w.a,Y=w.b,P=w.c,X=w.d,B=0,C=0,k=0;if(d?B=m[b-1]:l[p+1]==0?B=m[b+2]:B=Math.atan2(E,A),B-=Math.atan2(P,I),v){C=Math.cos(B),k=Math.sin(B);let D=w.data.length;f+=(D*(C*I-k*P)-A)*e,g+=(D*(k*I+C*P)-E)*e}else B+=x;B>F.PI?B-=F.PI2:B<-F.PI&&(B+=F.PI2),B*=e,C=Math.cos(B),k=Math.sin(B),w.a=C*I-k*P,w.b=C*Y-k*X,w.c=k*I+C*P,w.d=k*Y+C*X}w.updateAppliedTransform()}}computeWorldPositions(t,e,i){let s=this.target,a=this.position,d=this.spaces,n=V.setArraySize(this.positions,e*3+2),r=null,h=t.closed,o=t.worldVerticesLength,l=o/6,c=Ut.NONE;if(!t.constantSpeed){let R=t.lengths;l-=h?1:2;let L=R[l];this.data.positionMode==Dt.Percent&&(a*=L);let st;switch(this.data.spacingMode){case gt.Percent:st=L;break;case gt.Proportional:st=L/e;break;default:st=1}r=V.setArraySize(this.world,8);for(let ct=0,ht=0,rt=0;ct<e;ct++,ht+=3){let it=d[ct]*st;a+=it;let dt=a;if(h)dt%=L,dt<0&&(dt+=L),rt=0;else if(dt<0){c!=Ut.BEFORE&&(c=Ut.BEFORE,t.computeWorldVertices(s,2,4,r,0,2)),this.addBeforePosition(dt,r,0,n,ht);continue}else if(dt>L){c!=Ut.AFTER&&(c=Ut.AFTER,t.computeWorldVertices(s,o-6,4,r,0,2)),this.addAfterPosition(dt-L,r,0,n,ht);continue}for(;;rt++){let St=R[rt];if(!(dt>St)){if(rt==0)dt/=St;else{let Bt=R[rt-1];dt=(dt-Bt)/(St-Bt)}break}}rt!=c&&(c=rt,h&&rt==l?(t.computeWorldVertices(s,o-4,4,r,0,2),t.computeWorldVertices(s,0,4,r,4,2)):t.computeWorldVertices(s,rt*6+2,8,r,0,2)),this.addCurvePosition(dt,r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],n,ht,i||ct>0&&it==0)}return n}h?(o+=2,r=V.setArraySize(this.world,o),t.computeWorldVertices(s,2,o-4,r,0,2),t.computeWorldVertices(s,0,2,r,o-4,2),r[o-2]=r[0],r[o-1]=r[1]):(l--,o-=4,r=V.setArraySize(this.world,o),t.computeWorldVertices(s,2,o,r,0,2));let u=V.setArraySize(this.curves,l),m=0,f=r[0],g=r[1],x=0,v=0,p=0,b=0,w=0,y=0,T=0,A=0,E=0,I=0,Y=0,P=0,X=0,B=0;for(let R=0,L=2;R<l;R++,L+=6)x=r[L],v=r[L+1],p=r[L+2],b=r[L+3],w=r[L+4],y=r[L+5],T=(f-x*2+p)*.1875,A=(g-v*2+b)*.1875,E=((x-p)*3-f+w)*.09375,I=((v-b)*3-g+y)*.09375,Y=T*2+E,P=A*2+I,X=(x-f)*.75+T+E*.16666667,B=(v-g)*.75+A+I*.16666667,m+=Math.sqrt(X*X+B*B),X+=Y,B+=P,Y+=E,P+=I,m+=Math.sqrt(X*X+B*B),X+=Y,B+=P,m+=Math.sqrt(X*X+B*B),X+=Y+E,B+=P+I,m+=Math.sqrt(X*X+B*B),u[R]=m,f=w,g=y;this.data.positionMode==Dt.Percent&&(a*=m);let C;switch(this.data.spacingMode){case gt.Percent:C=m;break;case gt.Proportional:C=m/e;break;default:C=1}let k=this.segments,D=0;for(let R=0,L=0,st=0,ct=0;R<e;R++,L+=3){let ht=d[R]*C;a+=ht;let rt=a;if(h)rt%=m,rt<0&&(rt+=m),st=0;else if(rt<0){this.addBeforePosition(rt,r,0,n,L);continue}else if(rt>m){this.addAfterPosition(rt-m,r,o-4,n,L);continue}for(;;st++){let it=u[st];if(!(rt>it)){if(st==0)rt/=it;else{let dt=u[st-1];rt=(rt-dt)/(it-dt)}break}}if(st!=c){c=st;let it=st*6;for(f=r[it],g=r[it+1],x=r[it+2],v=r[it+3],p=r[it+4],b=r[it+5],w=r[it+6],y=r[it+7],T=(f-x*2+p)*.03,A=(g-v*2+b)*.03,E=((x-p)*3-f+w)*.006,I=((v-b)*3-g+y)*.006,Y=T*2+E,P=A*2+I,X=(x-f)*.3+T+E*.16666667,B=(v-g)*.3+A+I*.16666667,D=Math.sqrt(X*X+B*B),k[0]=D,it=1;it<8;it++)X+=Y,B+=P,Y+=E,P+=I,D+=Math.sqrt(X*X+B*B),k[it]=D;X+=Y,B+=P,D+=Math.sqrt(X*X+B*B),k[8]=D,X+=Y+E,B+=P+I,D+=Math.sqrt(X*X+B*B),k[9]=D,ct=0}for(rt*=D;;ct++){let it=k[ct];if(!(rt>it)){if(ct==0)rt/=it;else{let dt=k[ct-1];rt=ct+(rt-dt)/(it-dt)}break}}this.addCurvePosition(rt*.1,f,g,x,v,p,b,w,y,n,L,i||R>0&&ht==0)}return n}addBeforePosition(t,e,i,s,a){let d=e[i],n=e[i+1],r=e[i+2]-d,h=e[i+3]-n,o=Math.atan2(h,r);s[a]=d+t*Math.cos(o),s[a+1]=n+t*Math.sin(o),s[a+2]=o}addAfterPosition(t,e,i,s,a){let d=e[i+2],n=e[i+3],r=d-e[i],h=n-e[i+1],o=Math.atan2(h,r);s[a]=d+t*Math.cos(o),s[a+1]=n+t*Math.sin(o),s[a+2]=o}addCurvePosition(t,e,i,s,a,d,n,r,h,o,l,c){if(t==0||isNaN(t)){o[l]=e,o[l+1]=i,o[l+2]=Math.atan2(a-i,s-e);return}let u=t*t,m=u*t,f=1-t,g=f*f,x=g*f,v=f*t,p=v*3,b=f*p,w=p*t,y=e*x+s*b+d*w+r*m,T=i*x+a*b+n*w+h*m;o[l]=y,o[l+1]=T,c&&(t<.001?o[l+2]=Math.atan2(a-i,s-e):o[l+2]=Math.atan2(T-(i*g+a*v*2+n*u),y-(e*g+s*v*2+d*u)))}},ae=Ut;ae.NONE=-1,ae.BEFORE=-2,ae.AFTER=-3,ae.epsilon=1e-5;var ls=class{constructor(t,e){if(this.data=null,this.bone=null,this.color=null,this.darkColor=null,this.attachment=null,this.attachmentTime=0,this.attachmentState=0,this.deform=new Array,!t)throw new Error("data cannot be null.");if(!e)throw new Error("bone cannot be null.");this.data=t,this.bone=e,this.color=new O,this.darkColor=t.darkColor?new O:null,this.setToSetupPose()}getSkeleton(){return this.bone.skeleton}getAttachment(){return this.attachment}setAttachment(t){this.attachment!=t&&((!(t instanceof Pt)||!(this.attachment instanceof Pt)||t.deformAttachment!=this.attachment.deformAttachment)&&(this.deform.length=0),this.attachment=t,this.attachmentTime=this.bone.skeleton.time)}setAttachmentTime(t){this.attachmentTime=this.bone.skeleton.time-t}getAttachmentTime(){return this.bone.skeleton.time-this.attachmentTime}setToSetupPose(){this.color.setFromColor(this.data.color),this.darkColor&&this.darkColor.setFromColor(this.data.darkColor),this.data.attachmentName?(this.attachment=null,this.setAttachment(this.bone.skeleton.getAttachment(this.data.index,this.data.attachmentName))):this.attachment=null}},os=class{constructor(t,e){if(this.data=null,this.bones=null,this.target=null,this.mixRotate=0,this.mixX=0,this.mixY=0,this.mixScaleX=0,this.mixScaleY=0,this.mixShearY=0,this.temp=new Yt,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.mixRotate=t.mixRotate,this.mixX=t.mixX,this.mixY=t.mixY,this.mixScaleX=t.mixScaleX,this.mixScaleY=t.mixScaleY,this.mixShearY=t.mixShearY,this.bones=new Array;for(let i=0;i<t.bones.length;i++)this.bones.push(e.findBone(t.bones[i].name));this.target=e.findBone(t.target.name)}isActive(){return this.active}update(){this.mixRotate==0&&this.mixX==0&&this.mixY==0&&this.mixScaleX==0&&this.mixScaleX==0&&this.mixShearY==0||(this.data.local?this.data.relative?this.applyRelativeLocal():this.applyAbsoluteLocal():this.data.relative?this.applyRelativeWorld():this.applyAbsoluteWorld())}applyAbsoluteWorld(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,a=this.mixScaleY,d=this.mixShearY,n=e!=0||i!=0,r=this.target,h=r.a,o=r.b,l=r.c,c=r.d,u=h*c-o*l>0?F.degRad:-F.degRad,m=this.data.offsetRotation*u,f=this.data.offsetShearY*u,g=this.bones;for(let x=0,v=g.length;x<v;x++){let p=g[x];if(t!=0){let b=p.a,w=p.b,y=p.c,T=p.d,A=Math.atan2(l,h)-Math.atan2(y,b)+m;A>F.PI?A-=F.PI2:A<-F.PI&&(A+=F.PI2),A*=t;let E=Math.cos(A),I=Math.sin(A);p.a=E*b-I*y,p.b=E*w-I*T,p.c=I*b+E*y,p.d=I*w+E*T}if(n){let b=this.temp;r.localToWorld(b.set(this.data.offsetX,this.data.offsetY)),p.worldX+=(b.x-p.worldX)*e,p.worldY+=(b.y-p.worldY)*i}if(s!=0){let b=Math.sqrt(p.a*p.a+p.c*p.c);b!=0&&(b=(b+(Math.sqrt(h*h+l*l)-b+this.data.offsetScaleX)*s)/b),p.a*=b,p.c*=b}if(a!=0){let b=Math.sqrt(p.b*p.b+p.d*p.d);b!=0&&(b=(b+(Math.sqrt(o*o+c*c)-b+this.data.offsetScaleY)*a)/b),p.b*=b,p.d*=b}if(d>0){let b=p.b,w=p.d,y=Math.atan2(w,b),T=Math.atan2(c,o)-Math.atan2(l,h)-(y-Math.atan2(p.c,p.a));T>F.PI?T-=F.PI2:T<-F.PI&&(T+=F.PI2),T=y+(T+f)*d;let A=Math.sqrt(b*b+w*w);p.b=Math.cos(T)*A,p.d=Math.sin(T)*A}p.updateAppliedTransform()}}applyRelativeWorld(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,a=this.mixScaleY,d=this.mixShearY,n=e!=0||i!=0,r=this.target,h=r.a,o=r.b,l=r.c,c=r.d,u=h*c-o*l>0?F.degRad:-F.degRad,m=this.data.offsetRotation*u,f=this.data.offsetShearY*u,g=this.bones;for(let x=0,v=g.length;x<v;x++){let p=g[x];if(t!=0){let b=p.a,w=p.b,y=p.c,T=p.d,A=Math.atan2(l,h)+m;A>F.PI?A-=F.PI2:A<-F.PI&&(A+=F.PI2),A*=t;let E=Math.cos(A),I=Math.sin(A);p.a=E*b-I*y,p.b=E*w-I*T,p.c=I*b+E*y,p.d=I*w+E*T}if(n){let b=this.temp;r.localToWorld(b.set(this.data.offsetX,this.data.offsetY)),p.worldX+=b.x*e,p.worldY+=b.y*i}if(s!=0){let b=(Math.sqrt(h*h+l*l)-1+this.data.offsetScaleX)*s+1;p.a*=b,p.c*=b}if(a!=0){let b=(Math.sqrt(o*o+c*c)-1+this.data.offsetScaleY)*a+1;p.b*=b,p.d*=b}if(d>0){let b=Math.atan2(c,o)-Math.atan2(l,h);b>F.PI?b-=F.PI2:b<-F.PI&&(b+=F.PI2);let w=p.b,y=p.d;b=Math.atan2(y,w)+(b-F.PI/2+f)*d;let T=Math.sqrt(w*w+y*y);p.b=Math.cos(b)*T,p.d=Math.sin(b)*T}p.updateAppliedTransform()}}applyAbsoluteLocal(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,a=this.mixScaleY,d=this.mixShearY,n=this.target,r=this.bones;for(let h=0,o=r.length;h<o;h++){let l=r[h],c=l.arotation;if(t!=0){let v=n.arotation-c+this.data.offsetRotation;v-=(16384-(16384.499999999996-v/360|0))*360,c+=v*t}let u=l.ax,m=l.ay;u+=(n.ax-u+this.data.offsetX)*e,m+=(n.ay-m+this.data.offsetY)*i;let f=l.ascaleX,g=l.ascaleY;s!=0&&f!=0&&(f=(f+(n.ascaleX-f+this.data.offsetScaleX)*s)/f),a!=0&&g!=0&&(g=(g+(n.ascaleY-g+this.data.offsetScaleY)*a)/g);let x=l.ashearY;if(d!=0){let v=n.ashearY-x+this.data.offsetShearY;v-=(16384-(16384.499999999996-v/360|0))*360,x+=v*d}l.updateWorldTransformWith(u,m,c,f,g,l.ashearX,x)}}applyRelativeLocal(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,a=this.mixScaleY,d=this.mixShearY,n=this.target,r=this.bones;for(let h=0,o=r.length;h<o;h++){let l=r[h],c=l.arotation+(n.arotation+this.data.offsetRotation)*t,u=l.ax+(n.ax+this.data.offsetX)*e,m=l.ay+(n.ay+this.data.offsetY)*i,f=l.ascaleX*((n.ascaleX-1+this.data.offsetScaleX)*s+1),g=l.ascaleY*((n.ascaleY-1+this.data.offsetScaleY)*a+1),x=l.ashearY+(n.ashearY+this.data.offsetShearY)*d;l.updateWorldTransformWith(u,m,c,f,g,l.ashearX,x)}}},hs=class{constructor(t){if(this.data=null,this.bones=null,this.slots=null,this.drawOrder=null,this.ikConstraints=null,this.transformConstraints=null,this.pathConstraints=null,this._updateCache=new Array,this.skin=null,this.color=null,this.time=0,this.scaleX=1,this.scaleY=1,this.x=0,this.y=0,!t)throw new Error("data cannot be null.");this.data=t,this.bones=new Array;for(let e=0;e<t.bones.length;e++){let i=t.bones[e],s;if(!i.parent)s=new wi(i,this,null);else{let a=this.bones[i.parent.index];s=new wi(i,this,a),a.children.push(s)}this.bones.push(s)}this.slots=new Array,this.drawOrder=new Array;for(let e=0;e<t.slots.length;e++){let i=t.slots[e],s=this.bones[i.boneData.index],a=new ls(i,s);this.slots.push(a),this.drawOrder.push(a)}this.ikConstraints=new Array;for(let e=0;e<t.ikConstraints.length;e++){let i=t.ikConstraints[e];this.ikConstraints.push(new ns(i,this))}this.transformConstraints=new Array;for(let e=0;e<t.transformConstraints.length;e++){let i=t.transformConstraints[e];this.transformConstraints.push(new os(i,this))}this.pathConstraints=new Array;for(let e=0;e<t.pathConstraints.length;e++){let i=t.pathConstraints[e];this.pathConstraints.push(new ae(i,this))}this.color=new O(1,1,1,1),this.updateCache()}updateCache(){let t=this._updateCache;t.length=0;let e=this.bones;for(let o=0,l=e.length;o<l;o++){let c=e[o];c.sorted=c.data.skinRequired,c.active=!c.sorted}if(this.skin){let o=this.skin.bones;for(let l=0,c=this.skin.bones.length;l<c;l++){let u=this.bones[o[l].index];do u.sorted=!1,u.active=!0,u=u.parent;while(u)}}let i=this.ikConstraints,s=this.transformConstraints,a=this.pathConstraints,d=i.length,n=s.length,r=a.length,h=d+n+r;t:for(let o=0;o<h;o++){for(let l=0;l<d;l++){let c=i[l];if(c.data.order==o){this.sortIkConstraint(c);continue t}}for(let l=0;l<n;l++){let c=s[l];if(c.data.order==o){this.sortTransformConstraint(c);continue t}}for(let l=0;l<r;l++){let c=a[l];if(c.data.order==o){this.sortPathConstraint(c);continue t}}}for(let o=0,l=e.length;o<l;o++)this.sortBone(e[o])}sortIkConstraint(t){if(t.active=t.target.isActive()&&(!t.data.skinRequired||this.skin&&V.contains(this.skin.constraints,t.data,!0)),!t.active)return;let e=t.target;this.sortBone(e);let i=t.bones,s=i[0];if(this.sortBone(s),i.length==1)this._updateCache.push(t),this.sortReset(s.children);else{let a=i[i.length-1];this.sortBone(a),this._updateCache.push(t),this.sortReset(s.children),a.sorted=!0}}sortPathConstraint(t){if(t.active=t.target.bone.isActive()&&(!t.data.skinRequired||this.skin&&V.contains(this.skin.constraints,t.data,!0)),!t.active)return;let e=t.target,i=e.data.index,s=e.bone;this.skin&&this.sortPathConstraintAttachment(this.skin,i,s),this.data.defaultSkin&&this.data.defaultSkin!=this.skin&&this.sortPathConstraintAttachment(this.data.defaultSkin,i,s);for(let r=0,h=this.data.skins.length;r<h;r++)this.sortPathConstraintAttachment(this.data.skins[r],i,s);let a=e.getAttachment();a instanceof te&&this.sortPathConstraintAttachmentWith(a,s);let d=t.bones,n=d.length;for(let r=0;r<n;r++)this.sortBone(d[r]);this._updateCache.push(t);for(let r=0;r<n;r++)this.sortReset(d[r].children);for(let r=0;r<n;r++)d[r].sorted=!0}sortTransformConstraint(t){if(t.active=t.target.isActive()&&(!t.data.skinRequired||this.skin&&V.contains(this.skin.constraints,t.data,!0)),!t.active)return;this.sortBone(t.target);let e=t.bones,i=e.length;if(t.data.local)for(let s=0;s<i;s++){let a=e[s];this.sortBone(a.parent),this.sortBone(a)}else for(let s=0;s<i;s++)this.sortBone(e[s]);this._updateCache.push(t);for(let s=0;s<i;s++)this.sortReset(e[s].children);for(let s=0;s<i;s++)e[s].sorted=!0}sortPathConstraintAttachment(t,e,i){let s=t.attachments[e];if(!!s)for(let a in s)this.sortPathConstraintAttachmentWith(s[a],i)}sortPathConstraintAttachmentWith(t,e){if(!(t instanceof te))return;let i=t.bones;if(!i)this.sortBone(e);else{let s=this.bones;for(let a=0,d=i.length;a<d;){let n=i[a++];for(n+=a;a<n;)this.sortBone(s[i[a++]])}}}sortBone(t){if(t.sorted)return;let e=t.parent;e&&this.sortBone(e),t.sorted=!0,this._updateCache.push(t)}sortReset(t){for(let e=0,i=t.length;e<i;e++){let s=t[e];!s.active||(s.sorted&&this.sortReset(s.children),s.sorted=!1)}}updateWorldTransform(){let t=this.bones;for(let i=0,s=t.length;i<s;i++){let a=t[i];a.ax=a.x,a.ay=a.y,a.arotation=a.rotation,a.ascaleX=a.scaleX,a.ascaleY=a.scaleY,a.ashearX=a.shearX,a.ashearY=a.shearY}let e=this._updateCache;for(let i=0,s=e.length;i<s;i++)e[i].update()}updateWorldTransformWith(t){let e=this.getRootBone(),i=t.a,s=t.b,a=t.c,d=t.d;e.worldX=i*this.x+s*this.y+t.worldX,e.worldY=a*this.x+d*this.y+t.worldY;let n=e.rotation+90+e.shearY,r=F.cosDeg(e.rotation+e.shearX)*e.scaleX,h=F.cosDeg(n)*e.scaleY,o=F.sinDeg(e.rotation+e.shearX)*e.scaleX,l=F.sinDeg(n)*e.scaleY;e.a=(i*r+s*o)*this.scaleX,e.b=(i*h+s*l)*this.scaleX,e.c=(a*r+d*o)*this.scaleY,e.d=(a*h+d*l)*this.scaleY;let c=this._updateCache;for(let u=0,m=c.length;u<m;u++){let f=c[u];f!=e&&f.update()}}setToSetupPose(){this.setBonesToSetupPose(),this.setSlotsToSetupPose()}setBonesToSetupPose(){let t=this.bones;for(let a=0,d=t.length;a<d;a++)t[a].setToSetupPose();let e=this.ikConstraints;for(let a=0,d=e.length;a<d;a++){let n=e[a];n.mix=n.data.mix,n.softness=n.data.softness,n.bendDirection=n.data.bendDirection,n.compress=n.data.compress,n.stretch=n.data.stretch}let i=this.transformConstraints;for(let a=0,d=i.length;a<d;a++){let n=i[a],r=n.data;n.mixRotate=r.mixRotate,n.mixX=r.mixX,n.mixY=r.mixY,n.mixScaleX=r.mixScaleX,n.mixScaleY=r.mixScaleY,n.mixShearY=r.mixShearY}let s=this.pathConstraints;for(let a=0,d=s.length;a<d;a++){let n=s[a],r=n.data;n.position=r.position,n.spacing=r.spacing,n.mixRotate=r.mixRotate,n.mixX=r.mixX,n.mixY=r.mixY}}setSlotsToSetupPose(){let t=this.slots;V.arrayCopy(t,0,this.drawOrder,0,t.length);for(let e=0,i=t.length;e<i;e++)t[e].setToSetupPose()}getRootBone(){return this.bones.length==0?null:this.bones[0]}findBone(t){if(!t)throw new Error("boneName cannot be null.");let e=this.bones;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.data.name==t)return a}return null}findSlot(t){if(!t)throw new Error("slotName cannot be null.");let e=this.slots;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.data.name==t)return a}return null}setSkinByName(t){let e=this.data.findSkin(t);if(!e)throw new Error("Skin not found: "+t);this.setSkin(e)}setSkin(t){if(t!=this.skin){if(t)if(this.skin)t.attachAll(this,this.skin);else{let e=this.slots;for(let i=0,s=e.length;i<s;i++){let a=e[i],d=a.data.attachmentName;if(d){let n=t.getAttachment(i,d);n&&a.setAttachment(n)}}}this.skin=t,this.updateCache()}}getAttachmentByName(t,e){return this.getAttachment(this.data.findSlot(t).index,e)}getAttachment(t,e){if(!e)throw new Error("attachmentName cannot be null.");if(this.skin){let i=this.skin.getAttachment(t,e);if(i)return i}return this.data.defaultSkin?this.data.defaultSkin.getAttachment(t,e):null}setAttachment(t,e){if(!t)throw new Error("slotName cannot be null.");let i=this.slots;for(let s=0,a=i.length;s<a;s++){let d=i[s];if(d.data.name==t){let n=null;if(e&&(n=this.getAttachment(s,e),!n))throw new Error("Attachment not found: "+e+", for slot: "+t);d.setAttachment(n);return}}throw new Error("Slot not found: "+t)}findIkConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.ikConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.data.name==t)return a}return null}findTransformConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.transformConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.data.name==t)return a}return null}findPathConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.pathConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.data.name==t)return a}return null}getBoundsRect(){let t=new Yt,e=new Yt;return this.getBounds(t,e),{x:t.x,y:t.y,width:e.x,height:e.y}}getBounds(t,e,i=new Array(2)){if(!t)throw new Error("offset cannot be null.");if(!e)throw new Error("size cannot be null.");let s=this.drawOrder,a=Number.POSITIVE_INFINITY,d=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY,r=Number.NEGATIVE_INFINITY;for(let h=0,o=s.length;h<o;h++){let l=s[h];if(!l.bone.active)continue;let c=0,u=null,m=l.getAttachment();if(m instanceof at)c=8,u=V.setArraySize(i,c,0),m.computeWorldVertices(l.bone,u,0,2);else if(m instanceof jt){let f=m;c=f.worldVerticesLength,u=V.setArraySize(i,c,0),f.computeWorldVertices(l,0,c,u,0,2)}if(u)for(let f=0,g=u.length;f<g;f+=2){let x=u[f],v=u[f+1];a=Math.min(a,x),d=Math.min(d,v),n=Math.max(n,x),r=Math.max(r,v)}}t.set(a,d),e.set(n-a,r-d)}update(t){this.time+=t}},Si=class{constructor(){this.name=null,this.bones=new Array,this.slots=new Array,this.skins=new Array,this.defaultSkin=null,this.events=new Array,this.animations=new Array,this.ikConstraints=new Array,this.transformConstraints=new Array,this.pathConstraints=new Array,this.x=0,this.y=0,this.width=0,this.height=0,this.version=null,this.hash=null,this.fps=0,this.imagesPath=null,this.audioPath=null}findBone(t){if(!t)throw new Error("boneName cannot be null.");let e=this.bones;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findSlot(t){if(!t)throw new Error("slotName cannot be null.");let e=this.slots;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findSkin(t){if(!t)throw new Error("skinName cannot be null.");let e=this.skins;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findEvent(t){if(!t)throw new Error("eventDataName cannot be null.");let e=this.events;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findAnimation(t){if(!t)throw new Error("animationName cannot be null.");let e=this.animations;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findIkConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.ikConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findTransformConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.transformConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}findPathConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.pathConstraints;for(let i=0,s=e.length;i<s;i++){let a=e[i];if(a.name==t)return a}return null}},Ci=class{constructor(t=0,e=null,i=null){this.slotIndex=t,this.name=e,this.attachment=i}},Ee=class{constructor(t){if(this.name=null,this.attachments=new Array,this.bones=Array(),this.constraints=new Array,!t)throw new Error("name cannot be null.");this.name=t}setAttachment(t,e,i){if(!i)throw new Error("attachment cannot be null.");let s=this.attachments;t>=s.length&&(s.length=t+1),s[t]||(s[t]={}),s[t][e]=i}addSkin(t){for(let s=0;s<t.bones.length;s++){let a=t.bones[s],d=!1;for(let n=0;n<this.bones.length;n++)if(this.bones[n]==a){d=!0;break}d||this.bones.push(a)}for(let s=0;s<t.constraints.length;s++){let a=t.constraints[s],d=!1;for(let n=0;n<this.constraints.length;n++)if(this.constraints[n]==a){d=!0;break}d||this.constraints.push(a)}let e=t.getAttachments();for(let s=0;s<e.length;s++){var i=e[s];this.setAttachment(i.slotIndex,i.name,i.attachment)}}copySkin(t){for(let s=0;s<t.bones.length;s++){let a=t.bones[s],d=!1;for(let n=0;n<this.bones.length;n++)if(this.bones[n]==a){d=!0;break}d||this.bones.push(a)}for(let s=0;s<t.constraints.length;s++){let a=t.constraints[s],d=!1;for(let n=0;n<this.constraints.length;n++)if(this.constraints[n]==a){d=!0;break}d||this.constraints.push(a)}let e=t.getAttachments();for(let s=0;s<e.length;s++){var i=e[s];!i.attachment||(i.attachment instanceof jt?(i.attachment=i.attachment.newLinkedMesh(),this.setAttachment(i.slotIndex,i.name,i.attachment)):(i.attachment=i.attachment.copy(),this.setAttachment(i.slotIndex,i.name,i.attachment)))}}getAttachment(t,e){let i=this.attachments[t];return i?i[e]:null}removeAttachment(t,e){let i=this.attachments[t];i&&(i[e]=null)}getAttachments(){let t=new Array;for(var e=0;e<this.attachments.length;e++){let i=this.attachments[e];if(i)for(let s in i){let a=i[s];a&&t.push(new Ci(e,s,a))}}return t}getAttachmentsForSlot(t,e){let i=this.attachments[t];if(i)for(let s in i){let a=i[s];a&&e.push(new Ci(t,s,a))}}clear(){this.attachments.length=0,this.bones.length=0,this.constraints.length=0}attachAll(t,e){let i=0;for(let s=0;s<t.slots.length;s++){let a=t.slots[s],d=a.getAttachment();if(d&&i<e.attachments.length){let n=e.attachments[i];for(let r in n){let h=n[r];if(d==h){let o=this.getAttachment(i,r);o&&a.setAttachment(o);break}}}i++}}},Ti=class{constructor(t,e,i){if(this.index=0,this.name=null,this.boneData=null,this.color=new O(1,1,1,1),this.darkColor=null,this.attachmentName=null,this.blendMode=null,t<0)throw new Error("index must be >= 0.");if(!e)throw new Error("name cannot be null.");if(!i)throw new Error("boneData cannot be null.");this.index=t,this.name=e,this.boneData=i}},yt;(function(t){t[t.Normal=0]="Normal",t[t.Additive=1]="Additive",t[t.Multiply=2]="Multiply",t[t.Screen=3]="Screen"})(yt||(yt={}));var ki=class extends Me{constructor(t){super(t,0,!1);this.bones=new Array,this.target=null,this.mixRotate=0,this.mixX=0,this.mixY=0,this.mixScaleX=0,this.mixScaleY=0,this.mixShearY=0,this.offsetRotation=0,this.offsetX=0,this.offsetY=0,this.offsetScaleX=0,this.offsetScaleY=0,this.offsetShearY=0,this.relative=!1,this.local=!1}},ds=class{constructor(t){this.scale=1,this.attachmentLoader=null,this.linkedMeshes=new Array,this.attachmentLoader=t}readSkeletonData(t){let e=this.scale,i=new Si;i.name="";let s=new cs(t),a=s.readInt32(),d=s.readInt32();i.hash=d==0&&a==0?null:d.toString(16)+a.toString(16),i.version=s.readString(),i.x=s.readFloat(),i.y=s.readFloat(),i.width=s.readFloat(),i.height=s.readFloat();let n=s.readBoolean();n&&(i.fps=s.readFloat(),i.imagesPath=s.readString(),i.audioPath=s.readString());let r=0;r=s.readInt(!0);for(let o=0;o<r;o++)s.strings.push(s.readString());r=s.readInt(!0);for(let o=0;o<r;o++){let l=s.readString(),c=o==0?null:i.bones[s.readInt(!0)],u=new xi(o,l,c);u.rotation=s.readFloat(),u.x=s.readFloat()*e,u.y=s.readFloat()*e,u.scaleX=s.readFloat(),u.scaleY=s.readFloat(),u.shearX=s.readFloat(),u.shearY=s.readFloat(),u.length=s.readFloat()*e,u.transformMode=s.readInt(!0),u.skinRequired=s.readBoolean(),n&&O.rgba8888ToColor(u.color,s.readInt32()),i.bones.push(u)}r=s.readInt(!0);for(let o=0;o<r;o++){let l=s.readString(),c=i.bones[s.readInt(!0)],u=new Ti(o,l,c);O.rgba8888ToColor(u.color,s.readInt32());let m=s.readInt32();m!=-1&&O.rgb888ToColor(u.darkColor=new O,m),u.attachmentName=s.readStringRef(),u.blendMode=s.readInt(!0),i.slots.push(u)}r=s.readInt(!0);for(let o=0,l;o<r;o++){let c=new yi(s.readString());c.order=s.readInt(!0),c.skinRequired=s.readBoolean(),l=s.readInt(!0);for(let u=0;u<l;u++)c.bones.push(i.bones[s.readInt(!0)]);c.target=i.bones[s.readInt(!0)],c.mix=s.readFloat(),c.softness=s.readFloat()*e,c.bendDirection=s.readByte(),c.compress=s.readBoolean(),c.stretch=s.readBoolean(),c.uniform=s.readBoolean(),i.ikConstraints.push(c)}r=s.readInt(!0);for(let o=0,l;o<r;o++){let c=new ki(s.readString());c.order=s.readInt(!0),c.skinRequired=s.readBoolean(),l=s.readInt(!0);for(let u=0;u<l;u++)c.bones.push(i.bones[s.readInt(!0)]);c.target=i.bones[s.readInt(!0)],c.local=s.readBoolean(),c.relative=s.readBoolean(),c.offsetRotation=s.readFloat(),c.offsetX=s.readFloat()*e,c.offsetY=s.readFloat()*e,c.offsetScaleX=s.readFloat(),c.offsetScaleY=s.readFloat(),c.offsetShearY=s.readFloat(),c.mixRotate=s.readFloat(),c.mixX=s.readFloat(),c.mixY=s.readFloat(),c.mixScaleX=s.readFloat(),c.mixScaleY=s.readFloat(),c.mixShearY=s.readFloat(),i.transformConstraints.push(c)}r=s.readInt(!0);for(let o=0,l;o<r;o++){let c=new Ai(s.readString());c.order=s.readInt(!0),c.skinRequired=s.readBoolean(),l=s.readInt(!0);for(let u=0;u<l;u++)c.bones.push(i.bones[s.readInt(!0)]);c.target=i.slots[s.readInt(!0)],c.positionMode=s.readInt(!0),c.spacingMode=s.readInt(!0),c.rotateMode=s.readInt(!0),c.offsetRotation=s.readFloat(),c.position=s.readFloat(),c.positionMode==Dt.Fixed&&(c.position*=e),c.spacing=s.readFloat(),(c.spacingMode==gt.Length||c.spacingMode==gt.Fixed)&&(c.spacing*=e),c.mixRotate=s.readFloat(),c.mixX=s.readFloat(),c.mixY=s.readFloat(),i.pathConstraints.push(c)}let h=this.readSkin(s,i,!0,n);h&&(i.defaultSkin=h,i.skins.push(h));{let o=i.skins.length;for(V.setArraySize(i.skins,r=o+s.readInt(!0));o<r;o++)i.skins[o]=this.readSkin(s,i,!1,n)}r=this.linkedMeshes.length;for(let o=0;o<r;o++){let l=this.linkedMeshes[o],u=(l.skin?i.findSkin(l.skin):i.defaultSkin).getAttachment(l.slotIndex,l.parent);l.mesh.deformAttachment=l.inheritDeform?u:l.mesh,l.mesh.setParentMesh(u),l.mesh.updateUVs()}this.linkedMeshes.length=0,r=s.readInt(!0);for(let o=0;o<r;o++){let l=new vi(s.readStringRef());l.intValue=s.readInt(!1),l.floatValue=s.readFloat(),l.stringValue=s.readString(),l.audioPath=s.readString(),l.audioPath&&(l.volume=s.readFloat(),l.balance=s.readFloat()),i.events.push(l)}r=s.readInt(!0);for(let o=0;o<r;o++)i.animations.push(this.readAnimation(s,s.readString(),i));return i}readSkin(t,e,i,s){let a=null,d=0;if(i){if(d=t.readInt(!0),d==0)return null;a=new Ee("default")}else{a=new Ee(t.readStringRef()),a.bones.length=t.readInt(!0);for(let n=0,r=a.bones.length;n<r;n++)a.bones[n]=e.bones[t.readInt(!0)];for(let n=0,r=t.readInt(!0);n<r;n++)a.constraints.push(e.ikConstraints[t.readInt(!0)]);for(let n=0,r=t.readInt(!0);n<r;n++)a.constraints.push(e.transformConstraints[t.readInt(!0)]);for(let n=0,r=t.readInt(!0);n<r;n++)a.constraints.push(e.pathConstraints[t.readInt(!0)]);d=t.readInt(!0)}for(let n=0;n<d;n++){let r=t.readInt(!0);for(let h=0,o=t.readInt(!0);h<o;h++){let l=t.readStringRef(),c=this.readAttachment(t,e,a,r,l,s);c&&a.setAttachment(r,l,c)}}return a}readAttachment(t,e,i,s,a,d){let n=this.scale,r=t.readStringRef();switch(r||(r=a),t.readByte()){case zt.Region:{let h=t.readStringRef(),o=t.readFloat(),l=t.readFloat(),c=t.readFloat(),u=t.readFloat(),m=t.readFloat(),f=t.readFloat(),g=t.readFloat(),x=t.readInt32();h||(h=r);let v=this.attachmentLoader.newRegionAttachment(i,r,h);return v?(v.path=h,v.x=l*n,v.y=c*n,v.scaleX=u,v.scaleY=m,v.rotation=o,v.width=f*n,v.height=g*n,O.rgba8888ToColor(v.color,x),v.updateOffset(),v):null}case zt.BoundingBox:{let h=t.readInt(!0),o=this.readVertices(t,h),l=d?t.readInt32():0,c=this.attachmentLoader.newBoundingBoxAttachment(i,r);return c?(c.worldVerticesLength=h<<1,c.vertices=o.vertices,c.bones=o.bones,d&&O.rgba8888ToColor(c.color,l),c):null}case zt.Mesh:{let h=t.readStringRef(),o=t.readInt32(),l=t.readInt(!0),c=this.readFloatArray(t,l<<1,1),u=this.readShortArray(t),m=this.readVertices(t,l),f=t.readInt(!0),g=null,x=0,v=0;d&&(g=this.readShortArray(t),x=t.readFloat(),v=t.readFloat()),h||(h=r);let p=this.attachmentLoader.newMeshAttachment(i,r,h);return p?(p.path=h,O.rgba8888ToColor(p.color,o),p.bones=m.bones,p.vertices=m.vertices,p.worldVerticesLength=l<<1,p.triangles=u,p.regionUVs=c,p.updateUVs(),p.hullLength=f<<1,d&&(p.edges=g,p.width=x*n,p.height=v*n),p):null}case zt.LinkedMesh:{let h=t.readStringRef(),o=t.readInt32(),l=t.readStringRef(),c=t.readStringRef(),u=t.readBoolean(),m=0,f=0;d&&(m=t.readFloat(),f=t.readFloat()),h||(h=r);let g=this.attachmentLoader.newMeshAttachment(i,r,h);return g?(g.path=h,O.rgba8888ToColor(g.color,o),d&&(g.width=m*n,g.height=f*n),this.linkedMeshes.push(new zs(g,l,s,c,u)),g):null}case zt.Path:{let h=t.readBoolean(),o=t.readBoolean(),l=t.readInt(!0),c=this.readVertices(t,l),u=V.newArray(l/3,0);for(let g=0,x=u.length;g<x;g++)u[g]=t.readFloat()*n;let m=d?t.readInt32():0,f=this.attachmentLoader.newPathAttachment(i,r);return f?(f.closed=h,f.constantSpeed=o,f.worldVerticesLength=l<<1,f.vertices=c.vertices,f.bones=c.bones,f.lengths=u,d&&O.rgba8888ToColor(f.color,m),f):null}case zt.Point:{let h=t.readFloat(),o=t.readFloat(),l=t.readFloat(),c=d?t.readInt32():0,u=this.attachmentLoader.newPointAttachment(i,r);return u?(u.x=o*n,u.y=l*n,u.rotation=h,d&&O.rgba8888ToColor(u.color,c),u):null}case zt.Clipping:{let h=t.readInt(!0),o=t.readInt(!0),l=this.readVertices(t,o),c=d?t.readInt32():0,u=this.attachmentLoader.newClippingAttachment(i,r);return u?(u.endSlot=e.slots[h],u.worldVerticesLength=o<<1,u.vertices=l.vertices,u.bones=l.bones,d&&O.rgba8888ToColor(u.color,c),u):null}}return null}readVertices(t,e){let i=this.scale,s=e<<1,a=new Ws;if(!t.readBoolean())return a.vertices=this.readFloatArray(t,s,i),a;let d=new Array,n=new Array;for(let r=0;r<e;r++){let h=t.readInt(!0);n.push(h);for(let o=0;o<h;o++)n.push(t.readInt(!0)),d.push(t.readFloat()*i),d.push(t.readFloat()*i),d.push(t.readFloat())}return a.vertices=V.toFloatArray(d),a.bones=n,a}readFloatArray(t,e,i){let s=new Array(e);if(i==1)for(let a=0;a<e;a++)s[a]=t.readFloat();else for(let a=0;a<e;a++)s[a]=t.readFloat()*i;return s}readShortArray(t){let e=t.readInt(!0),i=new Array(e);for(let s=0;s<e;s++)i[s]=t.readShort();return i}readAnimation(t,e,i){t.readInt(!0);let s=new Array,a=this.scale,d=new O,n=new O;for(let l=0,c=t.readInt(!0);l<c;l++){let u=t.readInt(!0);for(let m=0,f=t.readInt(!0);m<f;m++){let g=t.readByte(),x=t.readInt(!0),v=x-1;switch(g){case tr:{let p=new Kt(x,u);for(let b=0;b<x;b++)p.setFrame(b,t.readFloat(),t.readStringRef());s.push(p);break}case er:{let p=t.readInt(!0),b=new Qe(x,p,u),w=t.readFloat(),y=t.readUnsignedByte()/255,T=t.readUnsignedByte()/255,A=t.readUnsignedByte()/255,E=t.readUnsignedByte()/255;for(let I=0,Y=0;b.setFrame(I,w,y,T,A,E),I!=v;I++){let P=t.readFloat(),X=t.readUnsignedByte()/255,B=t.readUnsignedByte()/255,C=t.readUnsignedByte()/255,k=t.readUnsignedByte()/255;switch(t.readByte()){case Lt:b.setStepped(I);break;case Xt:nt(t,b,Y++,I,0,w,P,y,X,1),nt(t,b,Y++,I,1,w,P,T,B,1),nt(t,b,Y++,I,2,w,P,A,C,1),nt(t,b,Y++,I,3,w,P,E,k,1)}w=P,y=X,T=B,A=C,E=k}s.push(b);break}case ir:{let p=t.readInt(!0),b=new $e(x,p,u),w=t.readFloat(),y=t.readUnsignedByte()/255,T=t.readUnsignedByte()/255,A=t.readUnsignedByte()/255;for(let E=0,I=0;b.setFrame(E,w,y,T,A),E!=v;E++){let Y=t.readFloat(),P=t.readUnsignedByte()/255,X=t.readUnsignedByte()/255,B=t.readUnsignedByte()/255;switch(t.readByte()){case Lt:b.setStepped(E);break;case Xt:nt(t,b,I++,E,0,w,Y,y,P,1),nt(t,b,I++,E,1,w,Y,T,X,1),nt(t,b,I++,E,2,w,Y,A,B,1)}w=Y,y=P,T=X,A=B}s.push(b);break}case sr:{let p=t.readInt(!0),b=new ei(x,p,u),w=t.readFloat(),y=t.readUnsignedByte()/255,T=t.readUnsignedByte()/255,A=t.readUnsignedByte()/255,E=t.readUnsignedByte()/255,I=t.readUnsignedByte()/255,Y=t.readUnsignedByte()/255,P=t.readUnsignedByte()/255;for(let X=0,B=0;b.setFrame(X,w,y,T,A,E,I,Y,P),X!=v;X++){let C=t.readFloat(),k=t.readUnsignedByte()/255,D=t.readUnsignedByte()/255,R=t.readUnsignedByte()/255,L=t.readUnsignedByte()/255,st=t.readUnsignedByte()/255,ct=t.readUnsignedByte()/255,ht=t.readUnsignedByte()/255;switch(t.readByte()){case Lt:b.setStepped(X);break;case Xt:nt(t,b,B++,X,0,w,C,y,k,1),nt(t,b,B++,X,1,w,C,T,D,1),nt(t,b,B++,X,2,w,C,A,R,1),nt(t,b,B++,X,3,w,C,E,L,1),nt(t,b,B++,X,4,w,C,I,st,1),nt(t,b,B++,X,5,w,C,Y,ct,1),nt(t,b,B++,X,6,w,C,P,ht,1)}w=C,y=k,T=D,A=R,E=L,I=st,Y=ct,P=ht}s.push(b);break}case rr:{let p=t.readInt(!0),b=new ii(x,p,u),w=t.readFloat(),y=t.readUnsignedByte()/255,T=t.readUnsignedByte()/255,A=t.readUnsignedByte()/255,E=t.readUnsignedByte()/255,I=t.readUnsignedByte()/255,Y=t.readUnsignedByte()/255;for(let P=0,X=0;b.setFrame(P,w,y,T,A,E,I,Y),P!=v;P++){let B=t.readFloat(),C=t.readUnsignedByte()/255,k=t.readUnsignedByte()/255,D=t.readUnsignedByte()/255,R=t.readUnsignedByte()/255,L=t.readUnsignedByte()/255,st=t.readUnsignedByte()/255;switch(t.readByte()){case Lt:b.setStepped(P);break;case Xt:nt(t,b,X++,P,0,w,B,y,C,1),nt(t,b,X++,P,1,w,B,T,k,1),nt(t,b,X++,P,2,w,B,A,D,1),nt(t,b,X++,P,3,w,B,E,R,1),nt(t,b,X++,P,4,w,B,I,L,1),nt(t,b,X++,P,5,w,B,Y,st,1)}w=B,y=C,T=k,A=D,E=R,I=L,Y=st}s.push(b);break}case ar:{let p=new ti(x,t.readInt(!0),u),b=t.readFloat(),w=t.readUnsignedByte()/255;for(let y=0,T=0;p.setFrame(y,b,w),y!=v;y++){let A=t.readFloat(),E=t.readUnsignedByte()/255;switch(t.readByte()){case Lt:p.setStepped(y);break;case Xt:nt(t,p,T++,y,0,b,A,w,E,1)}b=A,w=E}s.push(p);break}}}}for(let l=0,c=t.readInt(!0);l<c;l++){let u=t.readInt(!0);for(let m=0,f=t.readInt(!0);m<f;m++){let g=t.readByte(),x=t.readInt(!0),v=t.readInt(!0);switch(g){case _s:s.push(Wt(t,new fe(x,v,u),1));break;case qs:s.push(Mi(t,new We(x,v,u),a));break;case js:s.push(Wt(t,new _e(x,v,u),a));break;case Hs:s.push(Wt(t,new qe(x,v,u),a));break;case Gs:s.push(Mi(t,new je(x,v,u),1));break;case Zs:s.push(Wt(t,new He(x,v,u),1));break;case Js:s.push(Wt(t,new Ge(x,v,u),1));break;case Ks:s.push(Mi(t,new Ze(x,v,u),1));break;case Qs:s.push(Wt(t,new Je(x,v,u),1));break;case $s:s.push(Wt(t,new Ke(x,v,u),1))}}}for(let l=0,c=t.readInt(!0);l<c;l++){let u=t.readInt(!0),m=t.readInt(!0),f=m-1,g=new ri(m,t.readInt(!0),u),x=t.readFloat(),v=t.readFloat(),p=t.readFloat()*a;for(let b=0,w=0;g.setFrame(b,x,v,p,t.readByte(),t.readBoolean(),t.readBoolean()),b!=f;b++){let y=t.readFloat(),T=t.readFloat(),A=t.readFloat()*a;switch(t.readByte()){case Lt:g.setStepped(b);break;case Xt:nt(t,g,w++,b,0,x,y,v,T,1),nt(t,g,w++,b,1,x,y,p,A,a)}x=y,v=T,p=A}s.push(g)}for(let l=0,c=t.readInt(!0);l<c;l++){let u=t.readInt(!0),m=t.readInt(!0),f=m-1,g=new ai(m,t.readInt(!0),u),x=t.readFloat(),v=t.readFloat(),p=t.readFloat(),b=t.readFloat(),w=t.readFloat(),y=t.readFloat(),T=t.readFloat();for(let A=0,E=0;g.setFrame(A,x,v,p,b,w,y,T),A!=f;A++){let I=t.readFloat(),Y=t.readFloat(),P=t.readFloat(),X=t.readFloat(),B=t.readFloat(),C=t.readFloat(),k=t.readFloat();switch(t.readByte()){case Lt:g.setStepped(A);break;case Xt:nt(t,g,E++,A,0,x,I,v,Y,1),nt(t,g,E++,A,1,x,I,p,P,1),nt(t,g,E++,A,2,x,I,b,X,1),nt(t,g,E++,A,3,x,I,w,B,1),nt(t,g,E++,A,4,x,I,y,C,1),nt(t,g,E++,A,5,x,I,T,k,1)}x=I,v=Y,p=P,b=X,w=B,y=C,T=k}s.push(g)}for(let l=0,c=t.readInt(!0);l<c;l++){let u=t.readInt(!0),m=i.pathConstraints[u];for(let f=0,g=t.readInt(!0);f<g;f++)switch(t.readByte()){case nr:s.push(Wt(t,new ni(t.readInt(!0),t.readInt(!0),u),m.positionMode==Dt.Fixed?a:1));break;case lr:s.push(Wt(t,new li(t.readInt(!0),t.readInt(!0),u),m.spacingMode==gt.Length||m.spacingMode==gt.Fixed?a:1));break;case or:let x=new oi(t.readInt(!0),t.readInt(!0),u),v=t.readFloat(),p=t.readFloat(),b=t.readFloat(),w=t.readFloat();for(let y=0,T=0,A=x.getFrameCount()-1;x.setFrame(y,v,p,b,w),y!=A;y++){let E=t.readFloat(),I=t.readFloat(),Y=t.readFloat(),P=t.readFloat();switch(t.readByte()){case Lt:x.setStepped(y);break;case Xt:nt(t,x,T++,y,0,v,E,p,I,1),nt(t,x,T++,y,1,v,E,b,Y,1),nt(t,x,T++,y,2,v,E,w,P,1)}v=E,p=I,b=Y,w=P}s.push(x)}}for(let l=0,c=t.readInt(!0);l<c;l++){let u=i.skins[t.readInt(!0)];for(let m=0,f=t.readInt(!0);m<f;m++){let g=t.readInt(!0);for(let x=0,v=t.readInt(!0);x<v;x++){let p=t.readStringRef(),b=u.getAttachment(g,p),w=b.bones,y=b.vertices,T=w?y.length/3*2:y.length,A=t.readInt(!0),E=A-1,I=t.readInt(!0),Y=new si(A,I,g,b),P=t.readFloat();for(let X=0,B=0;;X++){let C,k=t.readInt(!0);if(k==0)C=w?V.newFloatArray(T):y;else{C=V.newFloatArray(T);let R=t.readInt(!0);if(k+=R,a==1)for(let L=R;L<k;L++)C[L]=t.readFloat();else for(let L=R;L<k;L++)C[L]=t.readFloat()*a;if(!w)for(let L=0,st=C.length;L<st;L++)C[L]+=y[L]}if(Y.setFrame(X,P,C),X==E)break;let D=t.readFloat();switch(t.readByte()){case Lt:Y.setStepped(X);break;case Xt:nt(t,Y,B++,X,0,P,D,0,1,1)}P=D}s.push(Y)}}}let r=t.readInt(!0);if(r>0){let l=new Qt(r),c=i.slots.length;for(let u=0;u<r;u++){let m=t.readFloat(),f=t.readInt(!0),g=V.newArray(c,0);for(let b=c-1;b>=0;b--)g[b]=-1;let x=V.newArray(c-f,0),v=0,p=0;for(let b=0;b<f;b++){let w=t.readInt(!0);for(;v!=w;)x[p++]=v++;g[v+t.readInt(!0)]=v++}for(;v<c;)x[p++]=v++;for(let b=c-1;b>=0;b--)g[b]==-1&&(g[b]=x[--p]);l.setFrame(u,m,g)}s.push(l)}let h=t.readInt(!0);if(h>0){let l=new me(h);for(let c=0;c<h;c++){let u=t.readFloat(),m=i.events[t.readInt(!0)],f=new bi(u,m);f.intValue=t.readInt(!1),f.floatValue=t.readFloat(),f.stringValue=t.readBoolean()?t.readString():m.stringValue,f.data.audioPath&&(f.volume=t.readFloat(),f.balance=t.readFloat()),l.setFrame(c,f)}s.push(l)}let o=0;for(let l=0,c=s.length;l<c;l++)o=Math.max(o,s[l].getDuration());return new ye(e,s,o)}},cs=class{constructor(t,e=new Array,i=0,s=new DataView(t.buffer)){this.strings=e,this.index=i,this.buffer=s}readByte(){return this.buffer.getInt8(this.index++)}readUnsignedByte(){return this.buffer.getUint8(this.index++)}readShort(){let t=this.buffer.getInt16(this.index);return this.index+=2,t}readInt32(){let t=this.buffer.getInt32(this.index);return this.index+=4,t}readInt(t){let e=this.readByte(),i=e&127;return(e&128)!=0&&(e=this.readByte(),i|=(e&127)<<7,(e&128)!=0&&(e=this.readByte(),i|=(e&127)<<14,(e&128)!=0&&(e=this.readByte(),i|=(e&127)<<21,(e&128)!=0&&(e=this.readByte(),i|=(e&127)<<28)))),t?i:i>>>1^-(i&1)}readStringRef(){let t=this.readInt(!0);return t==0?null:this.strings[t-1]}readString(){let t=this.readInt(!0);switch(t){case 0:return null;case 1:return""}t--;let e="",i=0;for(let s=0;s<t;){let a=this.readUnsignedByte();switch(a>>4){case 12:case 13:e+=String.fromCharCode((a&31)<<6|this.readByte()&63),s+=2;break;case 14:e+=String.fromCharCode((a&15)<<12|(this.readByte()&63)<<6|this.readByte()&63),s+=3;break;default:e+=String.fromCharCode(a),s++}}return e}readFloat(){let t=this.buffer.getFloat32(this.index);return this.index+=4,t}readBoolean(){return this.readByte()!=0}},zs=class{constructor(t,e,i,s,a){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=s,this.inheritDeform=a}},Ws=class{constructor(t=null,e=null){this.bones=t,this.vertices=e}},zt;(function(t){t[t.Region=0]="Region",t[t.BoundingBox=1]="BoundingBox",t[t.Mesh=2]="Mesh",t[t.LinkedMesh=3]="LinkedMesh",t[t.Path=4]="Path",t[t.Point=5]="Point",t[t.Clipping=6]="Clipping"})(zt||(zt={}));function Wt(t,e,i){let s=t.readFloat(),a=t.readFloat()*i;for(let d=0,n=0,r=e.getFrameCount()-1;e.setFrame(d,s,a),d!=r;d++){let h=t.readFloat(),o=t.readFloat()*i;switch(t.readByte()){case Lt:e.setStepped(d);break;case Xt:nt(t,e,n++,d,0,s,h,a,o,i)}s=h,a=o}return e}function Mi(t,e,i){let s=t.readFloat(),a=t.readFloat()*i,d=t.readFloat()*i;for(let n=0,r=0,h=e.getFrameCount()-1;e.setFrame(n,s,a,d),n!=h;n++){let o=t.readFloat(),l=t.readFloat()*i,c=t.readFloat()*i;switch(t.readByte()){case Lt:e.setStepped(n);break;case Xt:nt(t,e,r++,n,0,s,o,a,l,i),nt(t,e,r++,n,1,s,o,d,c,i)}s=o,a=l,d=c}return e}function nt(t,e,i,s,a,d,n,r,h,o){e.setBezier(i,s,a,d,r,t.readFloat(),t.readFloat()*o,t.readFloat(),t.readFloat()*o,n,h)}var _s=0,qs=1,js=2,Hs=3,Gs=4,Zs=5,Js=6,Ks=7,Qs=8,$s=9,tr=0,er=1,ir=2,sr=3,rr=4,ar=5,nr=0,lr=1,or=2,Lt=1,Xt=2,us=class{constructor(){this.minX=0,this.minY=0,this.maxX=0,this.maxY=0,this.boundingBoxes=new Array,this.polygons=new Array,this.polygonPool=new ue(()=>V.newFloatArray(16))}update(t,e){if(!t)throw new Error("skeleton cannot be null.");let i=this.boundingBoxes,s=this.polygons,a=this.polygonPool,d=t.slots,n=d.length;i.length=0,a.freeAll(s),s.length=0;for(let r=0;r<n;r++){let h=d[r];if(!h.bone.active)continue;let o=h.getAttachment();if(o instanceof ke){let l=o;i.push(l);let c=a.obtain();c.length!=l.worldVerticesLength&&(c=V.newFloatArray(l.worldVerticesLength)),s.push(c),l.computeWorldVertices(h,0,l.worldVerticesLength,c,0,2)}}e?this.aabbCompute():(this.minX=Number.POSITIVE_INFINITY,this.minY=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.maxY=Number.NEGATIVE_INFINITY)}aabbCompute(){let t=Number.POSITIVE_INFINITY,e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,s=Number.NEGATIVE_INFINITY,a=this.polygons;for(let d=0,n=a.length;d<n;d++){let r=a[d],h=r;for(let o=0,l=r.length;o<l;o+=2){let c=h[o],u=h[o+1];t=Math.min(t,c),e=Math.min(e,u),i=Math.max(i,c),s=Math.max(s,u)}}this.minX=t,this.minY=e,this.maxX=i,this.maxY=s}aabbContainsPoint(t,e){return t>=this.minX&&t<=this.maxX&&e>=this.minY&&e<=this.maxY}aabbIntersectsSegment(t,e,i,s){let a=this.minX,d=this.minY,n=this.maxX,r=this.maxY;if(t<=a&&i<=a||e<=d&&s<=d||t>=n&&i>=n||e>=r&&s>=r)return!1;let h=(s-e)/(i-t),o=h*(a-t)+e;if(o>d&&o<r||(o=h*(n-t)+e,o>d&&o<r))return!0;let l=(d-e)/h+t;return!!(l>a&&l<n||(l=(r-e)/h+t,l>a&&l<n))}aabbIntersectsSkeleton(t){return this.minX<t.maxX&&this.maxX>t.minX&&this.minY<t.maxY&&this.maxY>t.minY}containsPoint(t,e){let i=this.polygons;for(let s=0,a=i.length;s<a;s++)if(this.containsPointPolygon(i[s],t,e))return this.boundingBoxes[s];return null}containsPointPolygon(t,e,i){let s=t,a=t.length,d=a-2,n=!1;for(let r=0;r<a;r+=2){let h=s[r+1],o=s[d+1];if(h<i&&o>=i||o<i&&h>=i){let l=s[r];l+(i-h)/(o-h)*(s[d]-l)<e&&(n=!n)}d=r}return n}intersectsSegment(t,e,i,s){let a=this.polygons;for(let d=0,n=a.length;d<n;d++)if(this.intersectsSegmentPolygon(a[d],t,e,i,s))return this.boundingBoxes[d];return null}intersectsSegmentPolygon(t,e,i,s,a){let d=t,n=t.length,r=e-s,h=i-a,o=e*a-i*s,l=d[n-2],c=d[n-1];for(let u=0;u<n;u+=2){let m=d[u],f=d[u+1],g=l*f-c*m,x=l-m,v=c-f,p=r*v-h*x,b=(o*x-r*g)/p;if((b>=l&&b<=m||b>=m&&b<=l)&&(b>=e&&b<=s||b>=s&&b<=e)){let w=(o*v-h*g)/p;if((w>=c&&w<=f||w>=f&&w<=c)&&(w>=i&&w<=a||w>=a&&w<=i))return!0}l=m,c=f}return!1}getPolygon(t){if(!t)throw new Error("boundingBox cannot be null.");let e=this.boundingBoxes.indexOf(t);return e==-1?null:this.polygons[e]}getWidth(){return this.maxX-this.minX}getHeight(){return this.maxY-this.minY}},kt=class{constructor(){this.convexPolygons=new Array,this.convexPolygonsIndices=new Array,this.indicesArray=new Array,this.isConcaveArray=new Array,this.triangles=new Array,this.polygonPool=new ue(()=>new Array),this.polygonIndicesPool=new ue(()=>new Array)}triangulate(t){let e=t,i=t.length>>1,s=this.indicesArray;s.length=0;for(let n=0;n<i;n++)s[n]=n;let a=this.isConcaveArray;a.length=0;for(let n=0,r=i;n<r;++n)a[n]=kt.isConcave(n,i,e,s);let d=this.triangles;for(d.length=0;i>3;){let n=i-1,r=0,h=1;for(;;){t:if(!a[r]){let c=s[n]<<1,u=s[r]<<1,m=s[h]<<1,f=e[c],g=e[c+1],x=e[u],v=e[u+1],p=e[m],b=e[m+1];for(let w=(h+1)%i;w!=n;w=(w+1)%i){if(!a[w])continue;let y=s[w]<<1,T=e[y],A=e[y+1];if(kt.positiveArea(p,b,f,g,T,A)&&kt.positiveArea(f,g,x,v,T,A)&&kt.positiveArea(x,v,p,b,T,A))break t}break}if(h==0){do{if(!a[r])break;r--}while(r>0);break}n=r,r=h,h=(h+1)%i}d.push(s[(i+r-1)%i]),d.push(s[r]),d.push(s[(r+1)%i]),s.splice(r,1),a.splice(r,1),i--;let o=(i+r-1)%i,l=r==i?0:r;a[o]=kt.isConcave(o,i,e,s),a[l]=kt.isConcave(l,i,e,s)}return i==3&&(d.push(s[2]),d.push(s[0]),d.push(s[1])),d}decompose(t,e){let i=t,s=this.convexPolygons;this.polygonPool.freeAll(s),s.length=0;let a=this.convexPolygonsIndices;this.polygonIndicesPool.freeAll(a),a.length=0;let d=this.polygonIndicesPool.obtain();d.length=0;let n=this.polygonPool.obtain();n.length=0;let r=-1,h=0;for(let o=0,l=e.length;o<l;o+=3){let c=e[o]<<1,u=e[o+1]<<1,m=e[o+2]<<1,f=i[c],g=i[c+1],x=i[u],v=i[u+1],p=i[m],b=i[m+1],w=!1;if(r==c){let y=n.length-4,T=kt.winding(n[y],n[y+1],n[y+2],n[y+3],p,b),A=kt.winding(p,b,n[0],n[1],n[2],n[3]);T==h&&A==h&&(n.push(p),n.push(b),d.push(m),w=!0)}w||(n.length>0?(s.push(n),a.push(d)):(this.polygonPool.free(n),this.polygonIndicesPool.free(d)),n=this.polygonPool.obtain(),n.length=0,n.push(f),n.push(g),n.push(x),n.push(v),n.push(p),n.push(b),d=this.polygonIndicesPool.obtain(),d.length=0,d.push(c),d.push(u),d.push(m),h=kt.winding(f,g,x,v,p,b),r=c)}n.length>0&&(s.push(n),a.push(d));for(let o=0,l=s.length;o<l;o++){if(d=a[o],d.length==0)continue;let c=d[0],u=d[d.length-1];n=s[o];let m=n.length-4,f=n[m],g=n[m+1],x=n[m+2],v=n[m+3],p=n[0],b=n[1],w=n[2],y=n[3],T=kt.winding(f,g,x,v,p,b);for(let A=0;A<l;A++){if(A==o)continue;let E=a[A];if(E.length!=3)continue;let I=E[0],Y=E[1],P=E[2],X=s[A],B=X[X.length-2],C=X[X.length-1];if(I!=c||Y!=u)continue;let k=kt.winding(f,g,x,v,B,C),D=kt.winding(B,C,p,b,w,y);k==T&&D==T&&(X.length=0,E.length=0,n.push(B),n.push(C),d.push(P),f=x,g=v,x=B,v=C,A=0)}}for(let o=s.length-1;o>=0;o--)n=s[o],n.length==0&&(s.splice(o,1),this.polygonPool.free(n),d=a[o],a.splice(o,1),this.polygonIndicesPool.free(d));return s}static isConcave(t,e,i,s){let a=s[(e+t-1)%e]<<1,d=s[t]<<1,n=s[(t+1)%e]<<1;return!this.positiveArea(i[a],i[a+1],i[d],i[d+1],i[n],i[n+1])}static positiveArea(t,e,i,s,a,d){return t*(d-s)+i*(e-d)+a*(s-e)>=0}static winding(t,e,i,s,a,d){let n=i-t,r=s-e;return a*r-d*n+n*e-t*r>=0?1:-1}},Ie=class{constructor(){this.triangulator=new kt,this.clippingPolygon=new Array,this.clipOutput=new Array,this.clippedVertices=new Array,this.clippedTriangles=new Array,this.scratch=new Array}clipStart(t,e){if(this.clipAttachment)return 0;this.clipAttachment=e;let i=e.worldVerticesLength,s=V.setArraySize(this.clippingPolygon,i);e.computeWorldVertices(t,0,i,s,0,2);let a=this.clippingPolygon;Ie.makeClockwise(a);let d=this.clippingPolygons=this.triangulator.decompose(a,this.triangulator.triangulate(a));for(let n=0,r=d.length;n<r;n++){let h=d[n];Ie.makeClockwise(h),h.push(h[0]),h.push(h[1])}return d.length}clipEndWithSlot(t){this.clipAttachment&&this.clipAttachment.endSlot==t.data&&this.clipEnd()}clipEnd(){!this.clipAttachment||(this.clipAttachment=null,this.clippingPolygons=null,this.clippedVertices.length=0,this.clippedTriangles.length=0,this.clippingPolygon.length=0)}isClipping(){return this.clipAttachment!=null}clipTriangles(t,e,i,s,a,d,n,r){let h=this.clipOutput,o=this.clippedVertices,l=this.clippedTriangles,c=this.clippingPolygons,u=this.clippingPolygons.length,m=r?12:8,f=0;o.length=0,l.length=0;t:for(let g=0;g<s;g+=3){let x=i[g]<<1,v=t[x],p=t[x+1],b=a[x],w=a[x+1];x=i[g+1]<<1;let y=t[x],T=t[x+1],A=a[x],E=a[x+1];x=i[g+2]<<1;let I=t[x],Y=t[x+1],P=a[x],X=a[x+1];for(let B=0;B<u;B++){let C=o.length;if(this.clip(v,p,y,T,I,Y,c[B],h)){let k=h.length;if(k==0)continue;let D=T-Y,R=I-y,L=v-I,st=Y-p,ct=1/(D*L+R*(p-Y)),ht=k>>1,rt=this.clipOutput,it=V.setArraySize(o,C+ht*m);for(let St=0;St<k;St+=2){let Bt=rt[St],vt=rt[St+1];it[C]=Bt,it[C+1]=vt,it[C+2]=d.r,it[C+3]=d.g,it[C+4]=d.b,it[C+5]=d.a;let Et=Bt-I,It=vt-Y,Zt=(D*Et+R*It)*ct,he=(st*Et+L*It)*ct,be=1-Zt-he;it[C+6]=b*Zt+A*he+P*be,it[C+7]=w*Zt+E*he+X*be,r&&(it[C+8]=n.r,it[C+9]=n.g,it[C+10]=n.b,it[C+11]=n.a),C+=m}C=l.length;let dt=V.setArraySize(l,C+3*(ht-2));ht--;for(let St=1;St<ht;St++)dt[C]=f,dt[C+1]=f+St,dt[C+2]=f+St+1,C+=3;f+=ht+1}else{let k=V.setArraySize(o,C+3*m);k[C]=v,k[C+1]=p,k[C+2]=d.r,k[C+3]=d.g,k[C+4]=d.b,k[C+5]=d.a,r?(k[C+6]=b,k[C+7]=w,k[C+8]=n.r,k[C+9]=n.g,k[C+10]=n.b,k[C+11]=n.a,k[C+12]=y,k[C+13]=T,k[C+14]=d.r,k[C+15]=d.g,k[C+16]=d.b,k[C+17]=d.a,k[C+18]=A,k[C+19]=E,k[C+20]=n.r,k[C+21]=n.g,k[C+22]=n.b,k[C+23]=n.a,k[C+24]=I,k[C+25]=Y,k[C+26]=d.r,k[C+27]=d.g,k[C+28]=d.b,k[C+29]=d.a,k[C+30]=P,k[C+31]=X,k[C+32]=n.r,k[C+33]=n.g,k[C+34]=n.b,k[C+35]=n.a):(k[C+6]=b,k[C+7]=w,k[C+8]=y,k[C+9]=T,k[C+10]=d.r,k[C+11]=d.g,k[C+12]=d.b,k[C+13]=d.a,k[C+14]=A,k[C+15]=E,k[C+16]=I,k[C+17]=Y,k[C+18]=d.r,k[C+19]=d.g,k[C+20]=d.b,k[C+21]=d.a,k[C+22]=P,k[C+23]=X),C=l.length;let D=V.setArraySize(l,C+3);D[C]=f,D[C+1]=f+1,D[C+2]=f+2,f+=3;continue t}}}}clip(t,e,i,s,a,d,n,r){let h=r,o=!1,l=null;n.length%4>=2?(l=r,r=this.scratch):l=this.scratch,l.length=0,l.push(t),l.push(e),l.push(i),l.push(s),l.push(a),l.push(d),l.push(t),l.push(e),r.length=0;let c=n,u=n.length-4;for(let m=0;;m+=2){let f=c[m],g=c[m+1],x=c[m+2],v=c[m+3],p=f-x,b=g-v,w=l,y=l.length-2,T=r.length;for(let E=0;E<y;E+=2){let I=w[E],Y=w[E+1],P=w[E+2],X=w[E+3],B=p*(X-v)-b*(P-x)>0;if(p*(Y-v)-b*(I-x)>0){if(B){r.push(P),r.push(X);continue}let C=X-Y,k=P-I,D=C*(x-f)-k*(v-g);if(Math.abs(D)>1e-6){let R=(k*(g-Y)-C*(f-I))/D;r.push(f+(x-f)*R),r.push(g+(v-g)*R)}else r.push(f),r.push(g)}else if(B){let C=X-Y,k=P-I,D=C*(x-f)-k*(v-g);if(Math.abs(D)>1e-6){let R=(k*(g-Y)-C*(f-I))/D;r.push(f+(x-f)*R),r.push(g+(v-g)*R)}else r.push(f),r.push(g);r.push(P),r.push(X)}o=!0}if(T==r.length)return h.length=0,!0;if(r.push(r[0]),r.push(r[1]),m==u)break;let A=r;r=l,r.length=0,l=A}if(h!=r){h.length=0;for(let m=0,f=r.length-2;m<f;m++)h[m]=r[m]}else h.length=h.length-2;return o}static makeClockwise(t){let e=t,i=t.length,s=e[i-2]*e[1]-e[0]*e[i-1],a=0,d=0,n=0,r=0;for(let h=0,o=i-3;h<o;h+=2)a=e[h],d=e[h+1],n=e[h+2],r=e[h+3],s+=a*r-n*d;if(!(s<0))for(let h=0,o=i-2,l=i>>1;h<l;h+=2){let c=e[h],u=e[h+1],m=o-h;e[h]=e[m],e[h+1]=e[m+1],e[m]=c,e[m+1]=u}}},fs=class{constructor(t){this.attachmentLoader=null,this.scale=1,this.linkedMeshes=new Array,this.attachmentLoader=t}readSkeletonData(t){let e=this.scale,i=new Si,s=typeof t=="string"?JSON.parse(t):t,a=s.skeleton;if(a&&(i.hash=a.hash,i.version=a.spine,i.x=a.x,i.y=a.y,i.width=a.width,i.height=a.height,i.fps=a.fps,i.imagesPath=a.images),s.bones)for(let d=0;d<s.bones.length;d++){let n=s.bones[d],r=null,h=M(n,"parent",null);h&&(r=i.findBone(h));let o=new xi(i.bones.length,n.name,r);o.length=M(n,"length",0)*e,o.x=M(n,"x",0)*e,o.y=M(n,"y",0)*e,o.rotation=M(n,"rotation",0),o.scaleX=M(n,"scaleX",1),o.scaleY=M(n,"scaleY",1),o.shearX=M(n,"shearX",0),o.shearY=M(n,"shearY",0),o.transformMode=V.enumValue(Tt,M(n,"transform","Normal")),o.skinRequired=M(n,"skin",!1);let l=M(n,"color",null);l&&o.color.setFromString(l),i.bones.push(o)}if(s.slots)for(let d=0;d<s.slots.length;d++){let n=s.slots[d],r=i.findBone(n.bone),h=new Ti(i.slots.length,n.name,r),o=M(n,"color",null);o&&h.color.setFromString(o);let l=M(n,"dark",null);l&&(h.darkColor=O.fromString(l)),h.attachmentName=M(n,"attachment",null),h.blendMode=V.enumValue(yt,M(n,"blend","normal")),i.slots.push(h)}if(s.ik)for(let d=0;d<s.ik.length;d++){let n=s.ik[d],r=new yi(n.name);r.order=M(n,"order",0),r.skinRequired=M(n,"skin",!1);for(let h=0;h<n.bones.length;h++)r.bones.push(i.findBone(n.bones[h]));r.target=i.findBone(n.target),r.mix=M(n,"mix",1),r.softness=M(n,"softness",0)*e,r.bendDirection=M(n,"bendPositive",!0)?1:-1,r.compress=M(n,"compress",!1),r.stretch=M(n,"stretch",!1),r.uniform=M(n,"uniform",!1),i.ikConstraints.push(r)}if(s.transform)for(let d=0;d<s.transform.length;d++){let n=s.transform[d],r=new ki(n.name);r.order=M(n,"order",0),r.skinRequired=M(n,"skin",!1);for(let o=0;o<n.bones.length;o++)r.bones.push(i.findBone(n.bones[o]));let h=n.target;r.target=i.findBone(h),r.local=M(n,"local",!1),r.relative=M(n,"relative",!1),r.offsetRotation=M(n,"rotation",0),r.offsetX=M(n,"x",0)*e,r.offsetY=M(n,"y",0)*e,r.offsetScaleX=M(n,"scaleX",0),r.offsetScaleY=M(n,"scaleY",0),r.offsetShearY=M(n,"shearY",0),r.mixRotate=M(n,"mixRotate",1),r.mixX=M(n,"mixX",1),r.mixY=M(n,"mixY",r.mixX),r.mixScaleX=M(n,"mixScaleX",1),r.mixScaleY=M(n,"mixScaleY",r.mixScaleX),r.mixShearY=M(n,"mixShearY",1),i.transformConstraints.push(r)}if(s.path)for(let d=0;d<s.path.length;d++){let n=s.path[d],r=new Ai(n.name);r.order=M(n,"order",0),r.skinRequired=M(n,"skin",!1);for(let o=0;o<n.bones.length;o++)r.bones.push(i.findBone(n.bones[o]));let h=n.target;r.target=i.findSlot(h),r.positionMode=V.enumValue(Dt,M(n,"positionMode","Percent")),r.spacingMode=V.enumValue(gt,M(n,"spacingMode","Length")),r.rotateMode=V.enumValue(ee,M(n,"rotateMode","Tangent")),r.offsetRotation=M(n,"rotation",0),r.position=M(n,"position",0),r.positionMode==Dt.Fixed&&(r.position*=e),r.spacing=M(n,"spacing",0),(r.spacingMode==gt.Length||r.spacingMode==gt.Fixed)&&(r.spacing*=e),r.mixRotate=M(n,"mixRotate",1),r.mixX=M(n,"mixX",1),r.mixY=M(n,"mixY",r.mixX),i.pathConstraints.push(r)}if(s.skins)for(let d=0;d<s.skins.length;d++){let n=s.skins[d],r=new Ee(n.name);if(n.bones)for(let h=0;h<n.bones.length;h++)r.bones.push(i.findBone(n.bones[h]));if(n.ik)for(let h=0;h<n.ik.length;h++)r.constraints.push(i.findIkConstraint(n.ik[h]));if(n.transform)for(let h=0;h<n.transform.length;h++)r.constraints.push(i.findTransformConstraint(n.transform[h]));if(n.path)for(let h=0;h<n.path.length;h++)r.constraints.push(i.findPathConstraint(n.path[h]));for(let h in n.attachments){let o=i.findSlot(h),l=n.attachments[h];for(let c in l){let u=this.readAttachment(l[c],r,o.index,c,i);u&&r.setAttachment(o.index,c,u)}}i.skins.push(r),r.name=="default"&&(i.defaultSkin=r)}for(let d=0,n=this.linkedMeshes.length;d<n;d++){let r=this.linkedMeshes[d],o=(r.skin?i.findSkin(r.skin):i.defaultSkin).getAttachment(r.slotIndex,r.parent);r.mesh.deformAttachment=r.inheritDeform?o:r.mesh,r.mesh.setParentMesh(o),r.mesh.updateUVs()}if(this.linkedMeshes.length=0,s.events)for(let d in s.events){let n=s.events[d],r=new vi(d);r.intValue=M(n,"int",0),r.floatValue=M(n,"float",0),r.stringValue=M(n,"string",""),r.audioPath=M(n,"audio",null),r.audioPath&&(r.volume=M(n,"volume",1),r.balance=M(n,"balance",0)),i.events.push(r)}if(s.animations)for(let d in s.animations){let n=s.animations[d];this.readAnimation(n,d,i)}return i}readAttachment(t,e,i,s,a){let d=this.scale;switch(s=M(t,"name",s),M(t,"type","region")){case"region":{let n=M(t,"path",s),r=this.attachmentLoader.newRegionAttachment(e,s,n);if(!r)return null;r.path=n,r.x=M(t,"x",0)*d,r.y=M(t,"y",0)*d,r.scaleX=M(t,"scaleX",1),r.scaleY=M(t,"scaleY",1),r.rotation=M(t,"rotation",0),r.width=t.width*d,r.height=t.height*d;let h=M(t,"color",null);return h&&r.color.setFromString(h),r.updateOffset(),r}case"boundingbox":{let n=this.attachmentLoader.newBoundingBoxAttachment(e,s);if(!n)return null;this.readVertices(t,n,t.vertexCount<<1);let r=M(t,"color",null);return r&&n.color.setFromString(r),n}case"mesh":case"linkedmesh":{let n=M(t,"path",s),r=this.attachmentLoader.newMeshAttachment(e,s,n);if(!r)return null;r.path=n;let h=M(t,"color",null);h&&r.color.setFromString(h),r.width=M(t,"width",0)*d,r.height=M(t,"height",0)*d;let o=M(t,"parent",null);if(o)return this.linkedMeshes.push(new hr(r,M(t,"skin",null),i,o,M(t,"deform",!0))),r;let l=t.uvs;return this.readVertices(t,r,l.length),r.triangles=t.triangles,r.regionUVs=l,r.updateUVs(),r.edges=M(t,"edges",null),r.hullLength=M(t,"hull",0)*2,r}case"path":{let n=this.attachmentLoader.newPathAttachment(e,s);if(!n)return null;n.closed=M(t,"closed",!1),n.constantSpeed=M(t,"constantSpeed",!0);let r=t.vertexCount;this.readVertices(t,n,r<<1);let h=V.newArray(r/3,0);for(let l=0;l<t.lengths.length;l++)h[l]=t.lengths[l]*d;n.lengths=h;let o=M(t,"color",null);return o&&n.color.setFromString(o),n}case"point":{let n=this.attachmentLoader.newPointAttachment(e,s);if(!n)return null;n.x=M(t,"x",0)*d,n.y=M(t,"y",0)*d,n.rotation=M(t,"rotation",0);let r=M(t,"color",null);return r&&n.color.setFromString(r),n}case"clipping":{let n=this.attachmentLoader.newClippingAttachment(e,s);if(!n)return null;let r=M(t,"end",null);r&&(n.endSlot=a.findSlot(r));let h=t.vertexCount;this.readVertices(t,n,h<<1);let o=M(t,"color",null);return o&&n.color.setFromString(o),n}}return null}readVertices(t,e,i){let s=this.scale;e.worldVerticesLength=i;let a=t.vertices;if(i==a.length){let r=V.toFloatArray(a);if(s!=1)for(let h=0,o=a.length;h<o;h++)r[h]*=s;e.vertices=r;return}let d=new Array,n=new Array;for(let r=0,h=a.length;r<h;){let o=a[r++];n.push(o);for(let l=r+o*4;r<l;r+=4)n.push(a[r]),d.push(a[r+1]*s),d.push(a[r+2]*s),d.push(a[r+3])}e.bones=n,e.vertices=V.toFloatArray(d)}readAnimation(t,e,i){let s=this.scale,a=new Array;if(t.slots)for(let n in t.slots){let r=t.slots[n],h=i.findSlot(n).index;for(let o in r){let l=r[o];if(!l)continue;let c=l.length;if(o=="attachment"){let u=new Kt(c,h);for(let m=0;m<c;m++){let f=l[m];u.setFrame(m,M(f,"time",0),f.name)}a.push(u)}else if(o=="rgba"){let u=new Qe(c,c<<2,h),m=l[0],f=M(m,"time",0),g=O.fromString(m.color);for(let x=0,v=0;;x++){u.setFrame(x,f,g.r,g.g,g.b,g.a);let p=l[x+1];if(!p){u.shrink(v);break}let b=M(p,"time",0),w=O.fromString(p.color),y=m.curve;y&&(v=lt(y,u,v,x,0,f,b,g.r,w.r,1),v=lt(y,u,v,x,1,f,b,g.g,w.g,1),v=lt(y,u,v,x,2,f,b,g.b,w.b,1),v=lt(y,u,v,x,3,f,b,g.a,w.a,1)),f=b,g=w,m=p}a.push(u)}else if(o=="rgb"){let u=new $e(c,c*3,h),m=l[0],f=M(m,"time",0),g=O.fromString(m.color);for(let x=0,v=0;;x++){u.setFrame(x,f,g.r,g.g,g.b);let p=l[x+1];if(!p){u.shrink(v);break}let b=M(p,"time",0),w=O.fromString(p.color),y=m.curve;y&&(v=lt(y,u,v,x,0,f,b,g.r,w.r,1),v=lt(y,u,v,x,1,f,b,g.g,w.g,1),v=lt(y,u,v,x,2,f,b,g.b,w.b,1)),f=b,g=w,m=p}a.push(u)}else if(o=="alpha")a.push(Ot(l,new ti(c,c,h),0,1));else if(o=="rgba2"){let u=new ei(c,c*7,h),m=l[0],f=M(m,"time",0),g=O.fromString(m.light),x=O.fromString(m.dark);for(let v=0,p=0;;v++){u.setFrame(v,f,g.r,g.g,g.b,g.a,x.r,x.g,x.b);let b=l[v+1];if(!b){u.shrink(p);break}let w=M(b,"time",0),y=O.fromString(b.light),T=O.fromString(b.dark),A=m.curve;A&&(p=lt(A,u,p,v,0,f,w,g.r,y.r,1),p=lt(A,u,p,v,1,f,w,g.g,y.g,1),p=lt(A,u,p,v,2,f,w,g.b,y.b,1),p=lt(A,u,p,v,3,f,w,g.a,y.a,1),p=lt(A,u,p,v,4,f,w,x.r,T.r,1),p=lt(A,u,p,v,5,f,w,x.g,T.g,1),p=lt(A,u,p,v,6,f,w,x.b,T.b,1)),f=w,g=y,x=T,m=b}a.push(u)}else if(o=="rgb2"){let u=new ii(c,c*6,h),m=l[0],f=M(m,"time",0),g=O.fromString(m.light),x=O.fromString(m.dark);for(let v=0,p=0;;v++){u.setFrame(v,f,g.r,g.g,g.b,x.r,x.g,x.b);let b=l[v+1];if(!b){u.shrink(p);break}let w=M(b,"time",0),y=O.fromString(b.light),T=O.fromString(b.dark),A=m.curve;A&&(p=lt(A,u,p,v,0,f,w,g.r,y.r,1),p=lt(A,u,p,v,1,f,w,g.g,y.g,1),p=lt(A,u,p,v,2,f,w,g.b,y.b,1),p=lt(A,u,p,v,3,f,w,x.r,T.r,1),p=lt(A,u,p,v,4,f,w,x.g,T.g,1),p=lt(A,u,p,v,5,f,w,x.b,T.b,1)),f=w,g=y,x=T,m=b}a.push(u)}}}if(t.bones)for(let n in t.bones){let r=t.bones[n],h=i.findBone(n).index;for(let o in r){let l=r[o],c=l.length;if(c!=0){if(o==="rotate")a.push(Ot(l,new fe(c,c,h),0,1));else if(o==="translate"){let u=new We(c,c<<1,h);a.push(Ei(l,u,"x","y",0,s))}else if(o==="translatex"){let u=new _e(c,c,h);a.push(Ot(l,u,0,s))}else if(o==="translatey"){let u=new qe(c,c,h);a.push(Ot(l,u,0,s))}else if(o==="scale"){let u=new je(c,c<<1,h);a.push(Ei(l,u,"x","y",1,1))}else if(o==="scalex"){let u=new He(c,c,h);a.push(Ot(l,u,1,1))}else if(o==="scaley"){let u=new Ge(c,c,h);a.push(Ot(l,u,1,1))}else if(o==="shear"){let u=new Ze(c,c<<1,h);a.push(Ei(l,u,"x","y",0,1))}else if(o==="shearx"){let u=new Je(c,c,h);a.push(Ot(l,u,0,1))}else if(o==="sheary"){let u=new Ke(c,c,h);a.push(Ot(l,u,0,1))}}}}if(t.ik)for(let n in t.ik){let r=t.ik[n],h=r[0];if(!h)continue;let o=i.findIkConstraint(n),l=i.ikConstraints.indexOf(o),c=new ri(r.length,r.length<<1,l),u=M(h,"time",0),m=M(h,"mix",1),f=M(h,"softness",0)*s;for(let g=0,x=0;;g++){c.setFrame(g,u,m,f,M(h,"bendPositive",!0)?1:-1,M(h,"compress",!1),M(h,"stretch",!1));let v=r[g+1];if(!v){c.shrink(x);break}let p=M(v,"time",0),b=M(v,"mix",1),w=M(v,"softness",0)*s,y=h.curve;y&&(x=lt(y,c,x,g,0,u,p,m,b,1),x=lt(y,c,x,g,1,u,p,f,w,s)),u=p,m=b,f=w,h=v}a.push(c)}if(t.transform)for(let n in t.transform){let r=t.transform[n],h=r[0];if(!h)continue;let o=i.findTransformConstraint(n),l=i.transformConstraints.indexOf(o),c=new ai(r.length,r.length*6,l),u=M(h,"time",0),m=M(h,"mixRotate",1),f=M(h,"mixX",1),g=M(h,"mixY",f),x=M(h,"mixScaleX",1),v=M(h,"mixScaleY",x),p=M(h,"mixShearY",1);for(let b=0,w=0;;b++){c.setFrame(b,u,m,f,g,x,v,p);let y=r[b+1];if(!y){c.shrink(w);break}let T=M(y,"time",0),A=M(y,"mixRotate",1),E=M(y,"mixX",1),I=M(y,"mixY",E),Y=M(y,"mixScaleX",1),P=M(y,"mixScaleY",Y),X=M(y,"mixShearY",1),B=h.curve;B&&(w=lt(B,c,w,b,0,u,T,m,A,1),w=lt(B,c,w,b,1,u,T,f,E,1),w=lt(B,c,w,b,2,u,T,g,I,1),w=lt(B,c,w,b,3,u,T,x,Y,1),w=lt(B,c,w,b,4,u,T,v,P,1),w=lt(B,c,w,b,5,u,T,p,X,1)),u=T,m=A,f=E,g=I,x=Y,v=P,x=Y,h=y}a.push(c)}if(t.path)for(let n in t.path){let r=t.path[n],h=i.findPathConstraint(n),o=i.pathConstraints.indexOf(h);for(let l in r){let c=r[l],u=c[0];if(!u)continue;let m=c.length;if(l==="position"){let f=new ni(m,m,o);a.push(Ot(c,f,0,h.positionMode==Dt.Fixed?s:1))}else if(l==="spacing"){let f=new li(m,m,o);a.push(Ot(c,f,0,h.spacingMode==gt.Length||h.spacingMode==gt.Fixed?s:1))}else if(l==="mix"){let f=new oi(m,m*3,o),g=M(u,"time",0),x=M(u,"mixRotate",1),v=M(u,"mixX",1),p=M(u,"mixY",v);for(let b=0,w=0;;b++){f.setFrame(b,g,x,v,p);let y=c[b+1];if(!y){f.shrink(w);break}let T=M(y,"time",0),A=M(y,"mixRotate",1),E=M(y,"mixX",1),I=M(y,"mixY",E),Y=u.curve;Y&&(w=lt(Y,f,w,b,0,g,T,x,A,1),w=lt(Y,f,w,b,1,g,T,v,E,1),w=lt(Y,f,w,b,2,g,T,p,I,1)),g=T,x=A,v=E,p=I,u=y}a.push(f)}}}if(t.deform)for(let n in t.deform){let r=t.deform[n],h=i.findSkin(n);for(let o in r){let l=r[o],c=i.findSlot(o).index;for(let u in l){let m=l[u],f=m[0];if(!f)continue;let g=h.getAttachment(c,u),x=g.bones,v=g.vertices,p=x?v.length/3*2:v.length,b=new si(m.length,m.length,c,g),w=M(f,"time",0);for(let y=0,T=0;;y++){let A,E=M(f,"vertices",null);if(!E)A=x?V.newFloatArray(p):v;else{A=V.newFloatArray(p);let X=M(f,"offset",0);if(V.arrayCopy(E,0,A,X,E.length),s!=1)for(let B=X,C=B+E.length;B<C;B++)A[B]*=s;if(!x)for(let B=0;B<p;B++)A[B]+=v[B]}b.setFrame(y,w,A);let I=m[y+1];if(!I){b.shrink(T);break}let Y=M(I,"time",0),P=f.curve;P&&(T=lt(P,b,T,y,0,w,Y,0,1,1)),w=Y,f=I}a.push(b)}}}if(t.drawOrder){let n=new Qt(t.drawOrder.length),r=i.slots.length,h=0;for(let o=0;o<t.drawOrder.length;o++,h++){let l=t.drawOrder[o],c=null,u=M(l,"offsets",null);if(u){c=V.newArray(r,-1);let m=V.newArray(r-u.length,0),f=0,g=0;for(let x=0;x<u.length;x++){let v=u[x],p=i.findSlot(v.slot).index;for(;f!=p;)m[g++]=f++;c[f+v.offset]=f++}for(;f<r;)m[g++]=f++;for(let x=r-1;x>=0;x--)c[x]==-1&&(c[x]=m[--g])}n.setFrame(h,M(l,"time",0),c)}a.push(n)}if(t.events){let n=new me(t.events.length),r=0;for(let h=0;h<t.events.length;h++,r++){let o=t.events[h],l=i.findEvent(o.name),c=new bi(V.toSinglePrecision(M(o,"time",0)),l);c.intValue=M(o,"int",l.intValue),c.floatValue=M(o,"float",l.floatValue),c.stringValue=M(o,"string",l.stringValue),c.data.audioPath&&(c.volume=M(o,"volume",1),c.balance=M(o,"balance",0)),n.setFrame(r,c)}a.push(n)}let d=0;for(let n=0,r=a.length;n<r;n++)d=Math.max(d,a[n].getDuration());i.animations.push(new ye(e,a,d))}},hr=class{constructor(t,e,i,s,a){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=s,this.inheritDeform=a}};function Ot(t,e,i,s){let a=t[0],d=M(a,"time",0),n=M(a,"value",i)*s,r=0;for(let h=0;;h++){e.setFrame(h,d,n);let o=t[h+1];if(!o)return e.shrink(r),e;let l=M(o,"time",0),c=M(o,"value",i)*s;a.curve&&(r=lt(a.curve,e,r,h,0,d,l,n,c,s)),d=l,n=c,a=o}}function Ei(t,e,i,s,a,d){let n=t[0],r=M(n,"time",0),h=M(n,i,a)*d,o=M(n,s,a)*d,l=0;for(let c=0;;c++){e.setFrame(c,r,h,o);let u=t[c+1];if(!u)return e.shrink(l),e;let m=M(u,"time",0),f=M(u,i,a)*d,g=M(u,s,a)*d,x=n.curve;x&&(l=lt(x,e,l,c,0,r,m,h,f,d),l=lt(x,e,l,c,1,r,m,o,g,d)),r=m,h=f,o=g,n=u}}function lt(t,e,i,s,a,d,n,r,h,o){if(t=="stepped")return e.setStepped(s),i;let l=a<<2,c=t[l],u=t[l+1]*o,m=t[l+2],f=t[l+3]*o;return e.setBezier(i,s,a,d,r,c,u,m,f,n,h),i+1}function M(t,e,i){return t[e]!==void 0?t[e]:i}(()=>{typeof Math.fround=="undefined"&&(Math.fround=function(t){return function(e){return t[0]=e,t[0]}}(new Float32Array(1)))})();var dr=class{constructor(t,e){this.jitterX=0,this.jitterY=0,this.jitterX=t,this.jitterY=e}begin(t){}transform(t,e,i,s){t.x+=F.randomTriangular(-this.jitterX,this.jitterY),t.y+=F.randomTriangular(-this.jitterX,this.jitterY)}end(){}},ms=class{constructor(t){this.centerX=0,this.centerY=0,this.radius=0,this.angle=0,this.worldX=0,this.worldY=0,this.radius=t}begin(t){this.worldX=t.x+this.centerX,this.worldY=t.y+this.centerY}transform(t,e,i,s){let a=this.angle*F.degreesToRadians,d=t.x-this.worldX,n=t.y-this.worldY,r=Math.sqrt(d*d+n*n);if(r<this.radius){let h=ms.interpolation.apply(0,a,(this.radius-r)/this.radius),o=Math.cos(h),l=Math.sin(h);t.x=o*d-l*n+this.worldX,t.y=l*d+o*n+this.worldY}}end(){}},gs=ms;gs.interpolation=new qi(2);var wt=class{constructor(t,e={alpha:"true"}){this.restorables=new Array,t instanceof WebGLRenderingContext||typeof WebGL2RenderingContext!="undefined"&&t instanceof WebGL2RenderingContext?(this.gl=t,this.canvas=this.gl.canvas):this.setupCanvas(t,e)}setupCanvas(t,e){this.gl=t.getContext("webgl2",e)||t.getContext("webgl",e),this.canvas=t,t.addEventListener("webglcontextlost",i=>{let s=i;i&&i.preventDefault()}),t.addEventListener("webglcontextrestored",i=>{for(let s=0,a=this.restorables.length;s<a;s++)this.restorables[s].restore()})}addRestorable(t){this.restorables.push(t)}removeRestorable(t){let e=this.restorables.indexOf(t);e>-1&&this.restorables.splice(e,1)}},ne=1,cr=769,ps=770,Ye=771,ur=774,Re=class{static getDestGLBlendMode(t){switch(t){case yt.Normal:return Ye;case yt.Additive:return ne;case yt.Multiply:return Ye;case yt.Screen:return Ye;default:throw new Error("Unknown blend mode: "+t)}}static getSourceColorGLBlendMode(t,e=!1){switch(t){case yt.Normal:return e?ne:ps;case yt.Additive:return e?ne:ps;case yt.Multiply:return ur;case yt.Screen:return ne;default:throw new Error("Unknown blend mode: "+t)}}static getSourceAlphaGLBlendMode(t){switch(t){case yt.Normal:return ne;case yt.Additive:return ne;case yt.Multiply:return Ye;case yt.Screen:return cr;default:throw new Error("Unknown blend mode: "+t)}}},Ii=class extends fi{constructor(t,e,i=!1){super(e);this.texture=null,this.boundUnit=0,this.useMipMaps=!1,this.context=t instanceof wt?t:new wt(t),this.useMipMaps=i,this.restore(),this.context.addRestorable(this)}setFilters(t,e){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,Ii.validateMagFilter(e))}static validateMagFilter(t){switch(t){case pt.MipMap:case pt.MipMapLinearLinear:case pt.MipMapLinearNearest:case pt.MipMapNearestLinear:case pt.MipMapNearestNearest:return pt.Linear;default:return t}}setWraps(t,e){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,e)}update(t){let e=this.context.gl;this.texture||(this.texture=this.context.gl.createTexture()),this.bind(),Ii.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL&&e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,this._image),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,t?e.LINEAR_MIPMAP_LINEAR:e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t&&e.generateMipmap(e.TEXTURE_2D)}restore(){this.texture=null,this.update(this.useMipMaps)}bind(t=0){let e=this.context.gl;this.boundUnit=t,e.activeTexture(e.TEXTURE0+t),e.bindTexture(e.TEXTURE_2D,this.texture)}unbind(){let t=this.context.gl;t.activeTexture(t.TEXTURE0+this.boundUnit),t.bindTexture(t.TEXTURE_2D,null)}dispose(){this.context.removeRestorable(this),this.context.gl.deleteTexture(this.texture)}},pe=Ii;pe.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL=!1;var Yi=class extends rs{constructor(t,e="",i=null){super(s=>new pe(t,s),e,i)}},Mt=class{constructor(t=0,e=0,i=0){this.x=0,this.y=0,this.z=0,this.x=t,this.y=e,this.z=i}setFrom(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}set(t,e,i){return this.x=t,this.y=e,this.z=i,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}scale(t){return this.x*=t,this.y*=t,this.z*=t,this}normalize(){let t=this.length();return t==0?this:(t=1/t,this.x*=t,this.y*=t,this.z*=t,this)}cross(t){return this.set(this.y*t.z-this.z*t.y,this.z*t.x-this.x*t.z,this.x*t.y-this.y*t.x)}multiply(t){let e=t.values;return this.set(this.x*e[N]+this.y*e[j]+this.z*e[H]+e[U],this.x*e[G]+this.y*e[z]+this.z*e[Z]+e[W],this.x*e[J]+this.y*e[K]+this.z*e[_]+e[q])}project(t){let e=t.values,i=1/(this.x*e[$]+this.y*e[tt]+this.z*e[et]+e[Q]);return this.set((this.x*e[N]+this.y*e[j]+this.z*e[H]+e[U])*i,(this.x*e[G]+this.y*e[z]+this.z*e[Z]+e[W])*i,(this.x*e[J]+this.y*e[K]+this.z*e[_]+e[q])*i)}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}distance(t){let e=t.x-this.x,i=t.y-this.y,s=t.z-this.z;return Math.sqrt(e*e+i*i+s*s)}},N=0,j=4,H=8,U=12,G=1,z=5,Z=9,W=13,J=2,K=6,_=10,q=14,$=3,tt=7,et=11,Q=15,xt=class{constructor(){this.temp=new Float32Array(16),this.values=new Float32Array(16);let t=this.values;t[N]=1,t[z]=1,t[_]=1,t[Q]=1}set(t){return this.values.set(t),this}transpose(){let t=this.temp,e=this.values;return t[N]=e[N],t[j]=e[G],t[H]=e[J],t[U]=e[$],t[G]=e[j],t[z]=e[z],t[Z]=e[K],t[W]=e[tt],t[J]=e[H],t[K]=e[Z],t[_]=e[_],t[q]=e[et],t[$]=e[U],t[tt]=e[W],t[et]=e[q],t[Q]=e[Q],this.set(t)}identity(){let t=this.values;return t[N]=1,t[j]=0,t[H]=0,t[U]=0,t[G]=0,t[z]=1,t[Z]=0,t[W]=0,t[J]=0,t[K]=0,t[_]=1,t[q]=0,t[$]=0,t[tt]=0,t[et]=0,t[Q]=1,this}invert(){let t=this.values,e=this.temp,i=t[$]*t[K]*t[Z]*t[U]-t[J]*t[tt]*t[Z]*t[U]-t[$]*t[z]*t[_]*t[U]+t[G]*t[tt]*t[_]*t[U]+t[J]*t[z]*t[et]*t[U]-t[G]*t[K]*t[et]*t[U]-t[$]*t[K]*t[H]*t[W]+t[J]*t[tt]*t[H]*t[W]+t[$]*t[j]*t[_]*t[W]-t[N]*t[tt]*t[_]*t[W]-t[J]*t[j]*t[et]*t[W]+t[N]*t[K]*t[et]*t[W]+t[$]*t[z]*t[H]*t[q]-t[G]*t[tt]*t[H]*t[q]-t[$]*t[j]*t[Z]*t[q]+t[N]*t[tt]*t[Z]*t[q]+t[G]*t[j]*t[et]*t[q]-t[N]*t[z]*t[et]*t[q]-t[J]*t[z]*t[H]*t[Q]+t[G]*t[K]*t[H]*t[Q]+t[J]*t[j]*t[Z]*t[Q]-t[N]*t[K]*t[Z]*t[Q]-t[G]*t[j]*t[_]*t[Q]+t[N]*t[z]*t[_]*t[Q];if(i==0)throw new Error("non-invertible matrix");let s=1/i;return e[N]=t[Z]*t[q]*t[tt]-t[W]*t[_]*t[tt]+t[W]*t[K]*t[et]-t[z]*t[q]*t[et]-t[Z]*t[K]*t[Q]+t[z]*t[_]*t[Q],e[j]=t[U]*t[_]*t[tt]-t[H]*t[q]*t[tt]-t[U]*t[K]*t[et]+t[j]*t[q]*t[et]+t[H]*t[K]*t[Q]-t[j]*t[_]*t[Q],e[H]=t[H]*t[W]*t[tt]-t[U]*t[Z]*t[tt]+t[U]*t[z]*t[et]-t[j]*t[W]*t[et]-t[H]*t[z]*t[Q]+t[j]*t[Z]*t[Q],e[U]=t[U]*t[Z]*t[K]-t[H]*t[W]*t[K]-t[U]*t[z]*t[_]+t[j]*t[W]*t[_]+t[H]*t[z]*t[q]-t[j]*t[Z]*t[q],e[G]=t[W]*t[_]*t[$]-t[Z]*t[q]*t[$]-t[W]*t[J]*t[et]+t[G]*t[q]*t[et]+t[Z]*t[J]*t[Q]-t[G]*t[_]*t[Q],e[z]=t[H]*t[q]*t[$]-t[U]*t[_]*t[$]+t[U]*t[J]*t[et]-t[N]*t[q]*t[et]-t[H]*t[J]*t[Q]+t[N]*t[_]*t[Q],e[Z]=t[U]*t[Z]*t[$]-t[H]*t[W]*t[$]-t[U]*t[G]*t[et]+t[N]*t[W]*t[et]+t[H]*t[G]*t[Q]-t[N]*t[Z]*t[Q],e[W]=t[H]*t[W]*t[J]-t[U]*t[Z]*t[J]+t[U]*t[G]*t[_]-t[N]*t[W]*t[_]-t[H]*t[G]*t[q]+t[N]*t[Z]*t[q],e[J]=t[z]*t[q]*t[$]-t[W]*t[K]*t[$]+t[W]*t[J]*t[tt]-t[G]*t[q]*t[tt]-t[z]*t[J]*t[Q]+t[G]*t[K]*t[Q],e[K]=t[U]*t[K]*t[$]-t[j]*t[q]*t[$]-t[U]*t[J]*t[tt]+t[N]*t[q]*t[tt]+t[j]*t[J]*t[Q]-t[N]*t[K]*t[Q],e[_]=t[j]*t[W]*t[$]-t[U]*t[z]*t[$]+t[U]*t[G]*t[tt]-t[N]*t[W]*t[tt]-t[j]*t[G]*t[Q]+t[N]*t[z]*t[Q],e[q]=t[U]*t[z]*t[J]-t[j]*t[W]*t[J]-t[U]*t[G]*t[K]+t[N]*t[W]*t[K]+t[j]*t[G]*t[q]-t[N]*t[z]*t[q],e[$]=t[Z]*t[K]*t[$]-t[z]*t[_]*t[$]-t[Z]*t[J]*t[tt]+t[G]*t[_]*t[tt]+t[z]*t[J]*t[et]-t[G]*t[K]*t[et],e[tt]=t[j]*t[_]*t[$]-t[H]*t[K]*t[$]+t[H]*t[J]*t[tt]-t[N]*t[_]*t[tt]-t[j]*t[J]*t[et]+t[N]*t[K]*t[et],e[et]=t[H]*t[z]*t[$]-t[j]*t[Z]*t[$]-t[H]*t[G]*t[tt]+t[N]*t[Z]*t[tt]+t[j]*t[G]*t[et]-t[N]*t[z]*t[et],e[Q]=t[j]*t[Z]*t[J]-t[H]*t[z]*t[J]+t[H]*t[G]*t[K]-t[N]*t[Z]*t[K]-t[j]*t[G]*t[_]+t[N]*t[z]*t[_],t[N]=e[N]*s,t[j]=e[j]*s,t[H]=e[H]*s,t[U]=e[U]*s,t[G]=e[G]*s,t[z]=e[z]*s,t[Z]=e[Z]*s,t[W]=e[W]*s,t[J]=e[J]*s,t[K]=e[K]*s,t[_]=e[_]*s,t[q]=e[q]*s,t[$]=e[$]*s,t[tt]=e[tt]*s,t[et]=e[et]*s,t[Q]=e[Q]*s,this}determinant(){let t=this.values;return t[$]*t[K]*t[Z]*t[U]-t[J]*t[tt]*t[Z]*t[U]-t[$]*t[z]*t[_]*t[U]+t[G]*t[tt]*t[_]*t[U]+t[J]*t[z]*t[et]*t[U]-t[G]*t[K]*t[et]*t[U]-t[$]*t[K]*t[H]*t[W]+t[J]*t[tt]*t[H]*t[W]+t[$]*t[j]*t[_]*t[W]-t[N]*t[tt]*t[_]*t[W]-t[J]*t[j]*t[et]*t[W]+t[N]*t[K]*t[et]*t[W]+t[$]*t[z]*t[H]*t[q]-t[G]*t[tt]*t[H]*t[q]-t[$]*t[j]*t[Z]*t[q]+t[N]*t[tt]*t[Z]*t[q]+t[G]*t[j]*t[et]*t[q]-t[N]*t[z]*t[et]*t[q]-t[J]*t[z]*t[H]*t[Q]+t[G]*t[K]*t[H]*t[Q]+t[J]*t[j]*t[Z]*t[Q]-t[N]*t[K]*t[Z]*t[Q]-t[G]*t[j]*t[_]*t[Q]+t[N]*t[z]*t[_]*t[Q]}translate(t,e,i){let s=this.values;return s[U]+=t,s[W]+=e,s[q]+=i,this}copy(){return new xt().set(this.values)}projection(t,e,i,s){this.identity();let a=1/Math.tan(i*(Math.PI/180)/2),d=(e+t)/(t-e),n=2*e*t/(t-e),r=this.values;return r[N]=a/s,r[G]=0,r[J]=0,r[$]=0,r[j]=0,r[z]=a,r[K]=0,r[tt]=0,r[H]=0,r[Z]=0,r[_]=d,r[et]=-1,r[U]=0,r[W]=0,r[q]=n,r[Q]=0,this}ortho2d(t,e,i,s){return this.ortho(t,t+i,e,e+s,0,1)}ortho(t,e,i,s,a,d){this.identity();let n=2/(e-t),r=2/(s-i),h=-2/(d-a),o=-(e+t)/(e-t),l=-(s+i)/(s-i),c=-(d+a)/(d-a),u=this.values;return u[N]=n,u[G]=0,u[J]=0,u[$]=0,u[j]=0,u[z]=r,u[K]=0,u[tt]=0,u[H]=0,u[Z]=0,u[_]=h,u[et]=0,u[U]=o,u[W]=l,u[q]=c,u[Q]=1,this}multiply(t){let e=this.temp,i=this.values,s=t.values;return e[N]=i[N]*s[N]+i[j]*s[G]+i[H]*s[J]+i[U]*s[$],e[j]=i[N]*s[j]+i[j]*s[z]+i[H]*s[K]+i[U]*s[tt],e[H]=i[N]*s[H]+i[j]*s[Z]+i[H]*s[_]+i[U]*s[et],e[U]=i[N]*s[U]+i[j]*s[W]+i[H]*s[q]+i[U]*s[Q],e[G]=i[G]*s[N]+i[z]*s[G]+i[Z]*s[J]+i[W]*s[$],e[z]=i[G]*s[j]+i[z]*s[z]+i[Z]*s[K]+i[W]*s[tt],e[Z]=i[G]*s[H]+i[z]*s[Z]+i[Z]*s[_]+i[W]*s[et],e[W]=i[G]*s[U]+i[z]*s[W]+i[Z]*s[q]+i[W]*s[Q],e[J]=i[J]*s[N]+i[K]*s[G]+i[_]*s[J]+i[q]*s[$],e[K]=i[J]*s[j]+i[K]*s[z]+i[_]*s[K]+i[q]*s[tt],e[_]=i[J]*s[H]+i[K]*s[Z]+i[_]*s[_]+i[q]*s[et],e[q]=i[J]*s[U]+i[K]*s[W]+i[_]*s[q]+i[q]*s[Q],e[$]=i[$]*s[N]+i[tt]*s[G]+i[et]*s[J]+i[Q]*s[$],e[tt]=i[$]*s[j]+i[tt]*s[z]+i[et]*s[K]+i[Q]*s[tt],e[et]=i[$]*s[H]+i[tt]*s[Z]+i[et]*s[_]+i[Q]*s[et],e[Q]=i[$]*s[U]+i[tt]*s[W]+i[et]*s[q]+i[Q]*s[Q],this.set(this.temp)}multiplyLeft(t){let e=this.temp,i=this.values,s=t.values;return e[N]=s[N]*i[N]+s[j]*i[G]+s[H]*i[J]+s[U]*i[$],e[j]=s[N]*i[j]+s[j]*i[z]+s[H]*i[K]+s[U]*i[tt],e[H]=s[N]*i[H]+s[j]*i[Z]+s[H]*i[_]+s[U]*i[et],e[U]=s[N]*i[U]+s[j]*i[W]+s[H]*i[q]+s[U]*i[Q],e[G]=s[G]*i[N]+s[z]*i[G]+s[Z]*i[J]+s[W]*i[$],e[z]=s[G]*i[j]+s[z]*i[z]+s[Z]*i[K]+s[W]*i[tt],e[Z]=s[G]*i[H]+s[z]*i[Z]+s[Z]*i[_]+s[W]*i[et],e[W]=s[G]*i[U]+s[z]*i[W]+s[Z]*i[q]+s[W]*i[Q],e[J]=s[J]*i[N]+s[K]*i[G]+s[_]*i[J]+s[q]*i[$],e[K]=s[J]*i[j]+s[K]*i[z]+s[_]*i[K]+s[q]*i[tt],e[_]=s[J]*i[H]+s[K]*i[Z]+s[_]*i[_]+s[q]*i[et],e[q]=s[J]*i[U]+s[K]*i[W]+s[_]*i[q]+s[q]*i[Q],e[$]=s[$]*i[N]+s[tt]*i[G]+s[et]*i[J]+s[Q]*i[$],e[tt]=s[$]*i[j]+s[tt]*i[z]+s[et]*i[K]+s[Q]*i[tt],e[et]=s[$]*i[H]+s[tt]*i[Z]+s[et]*i[_]+s[Q]*i[et],e[Q]=s[$]*i[U]+s[tt]*i[W]+s[et]*i[q]+s[Q]*i[Q],this.set(this.temp)}lookAt(t,e,i){xt.initTemps();let s=xt.xAxis,a=xt.yAxis,d=xt.zAxis;d.setFrom(e).normalize(),s.setFrom(e).normalize(),s.cross(i).normalize(),a.setFrom(s).cross(d).normalize(),this.identity();let n=this.values;return n[N]=s.x,n[j]=s.y,n[H]=s.z,n[G]=a.x,n[z]=a.y,n[Z]=a.z,n[J]=-d.x,n[K]=-d.y,n[_]=-d.z,xt.tmpMatrix.identity(),xt.tmpMatrix.values[U]=-t.x,xt.tmpMatrix.values[W]=-t.y,xt.tmpMatrix.values[q]=-t.z,this.multiply(xt.tmpMatrix),this}static initTemps(){xt.xAxis===null&&(xt.xAxis=new Mt),xt.yAxis===null&&(xt.yAxis=new Mt),xt.zAxis===null&&(xt.zAxis=new Mt)}},_t=xt;_t.xAxis=null,_t.yAxis=null,_t.zAxis=null,_t.tmpMatrix=new xt;var xs=class{constructor(t,e){this.position=new Mt(0,0,0),this.direction=new Mt(0,0,-1),this.up=new Mt(0,1,0),this.near=0,this.far=100,this.zoom=1,this.viewportWidth=0,this.viewportHeight=0,this.projectionView=new _t,this.inverseProjectionView=new _t,this.projection=new _t,this.view=new _t,this.viewportWidth=t,this.viewportHeight=e,this.update()}update(){let t=this.projection,e=this.view,i=this.projectionView,s=this.inverseProjectionView,a=this.zoom,d=this.viewportWidth,n=this.viewportHeight;t.ortho(a*(-d/2),a*(d/2),a*(-n/2),a*(n/2),this.near,this.far),e.lookAt(this.position,this.direction,this.up),i.set(t.values),i.multiply(e),s.set(i.values).invert()}screenToWorld(t,e,i){let s=t.x,a=i-t.y-1;return t.x=2*s/e-1,t.y=2*a/i-1,t.z=2*t.z-1,t.project(this.inverseProjectionView),t}worldToScreen(t,e,i){return t.project(this.projectionView),t.x=e*(t.x+1)/2,t.y=i*(t.y+1)/2,t.z=(t.z+1)/2,t}setViewport(t,e){this.viewportWidth=t,this.viewportHeight=e}},xe=class{constructor(t){this.mouseX=0,this.mouseY=0,this.buttonDown=!1,this.touch0=null,this.touch1=null,this.initialPinchDistance=0,this.listeners=new Array,this.eventListeners=[],this.element=t,this.setupCallbacks(t)}setupCallbacks(t){let e=n=>{if(n instanceof MouseEvent){let r=t.getBoundingClientRect();this.mouseX=n.clientX-r.left,this.mouseY=n.clientY-r.top,this.buttonDown=!0,this.listeners.map(h=>{h.down&&h.down(this.mouseX,this.mouseY)}),document.addEventListener("mousemove",i),document.addEventListener("mouseup",s)}},i=n=>{if(n instanceof MouseEvent){let r=t.getBoundingClientRect();this.mouseX=n.clientX-r.left,this.mouseY=n.clientY-r.top,this.listeners.map(h=>{this.buttonDown?h.dragged&&h.dragged(this.mouseX,this.mouseY):h.moved&&h.moved(this.mouseX,this.mouseY)})}},s=n=>{if(n instanceof MouseEvent){let r=t.getBoundingClientRect();this.mouseX=n.clientX-r.left,this.mouseY=n.clientY-r.top,this.buttonDown=!1,this.listeners.map(h=>{h.up&&h.up(this.mouseX,this.mouseY)}),document.removeEventListener("mousemove",i),document.removeEventListener("mouseup",s)}},a=n=>{n.preventDefault();let r=n.deltaY;n.deltaMode==WheelEvent.DOM_DELTA_LINE&&(r*=8),n.deltaMode==WheelEvent.DOM_DELTA_PAGE&&(r*=24),this.listeners.map(h=>{h.wheel&&h.wheel(n.deltaY)})};t.addEventListener("mousedown",e,!0),t.addEventListener("mousemove",i,!0),t.addEventListener("mouseup",s,!0),t.addEventListener("wheel",a,!0),t.addEventListener("touchstart",n=>{if(!this.touch0||!this.touch1){var r=n.changedTouches;let h=r.item(0),o=t.getBoundingClientRect(),l=h.clientX-o.left,c=h.clientY-o.top,u=new ws(h.identifier,l,c);if(this.mouseX=l,this.mouseY=c,this.buttonDown=!0,!this.touch0)this.touch0=u,this.listeners.map(m=>{m.down&&m.down(u.x,u.y)});else if(!this.touch1){this.touch1=u;let m=this.touch1.x-this.touch0.x,f=this.touch1.x-this.touch0.x;this.initialPinchDistance=Math.sqrt(m*m+f*f),this.listeners.map(g=>{g.zoom&&g.zoom(this.initialPinchDistance,this.initialPinchDistance)})}}n.preventDefault()},!1),t.addEventListener("touchmove",n=>{if(this.touch0){var r=n.changedTouches;let l=t.getBoundingClientRect();for(var h=0;h<r.length;h++){var o=r[h];let c=o.clientX-l.left,u=o.clientY-l.top;this.touch0.identifier===o.identifier&&(this.touch0.x=this.mouseX=c,this.touch0.y=this.mouseY=u,this.listeners.map(m=>{m.dragged&&m.dragged(c,u)})),this.touch1&&this.touch1.identifier===o.identifier&&(this.touch1.x=this.mouseX=c,this.touch1.y=this.mouseY=u)}if(this.touch0&&this.touch1){let c=this.touch1.x-this.touch0.x,u=this.touch1.x-this.touch0.x,m=Math.sqrt(c*c+u*u);this.listeners.map(f=>{f.zoom&&f.zoom(this.initialPinchDistance,m)})}}n.preventDefault()},!1);let d=n=>{if(this.touch0){var r=n.changedTouches;let l=t.getBoundingClientRect();for(var h=0;h<r.length;h++){var o=r[h];let c=o.clientX-l.left,u=o.clientY-l.top;if(this.touch0.identifier===o.identifier)if(this.touch0=null,this.mouseX=c,this.mouseY=u,this.listeners.map(m=>{m.up&&m.up(c,u)}),this.touch1)this.touch0=this.touch1,this.touch1=null,this.mouseX=this.touch0.x,this.mouseX=this.touch0.x,this.buttonDown=!0,this.listeners.map(m=>{m.down&&m.down(this.touch0.x,this.touch0.y)});else{this.buttonDown=!1;break}this.touch1&&this.touch1.identifier&&(this.touch1=null)}}n.preventDefault()};t.addEventListener("touchend",d,!1),t.addEventListener("touchcancel",d)}addListener(t){this.listeners.push(t)}removeListener(t){let e=this.listeners.indexOf(t);e>-1&&this.listeners.splice(e,1)}},ws=class{constructor(t,e,i){this.identifier=t,this.x=e,this.y=i}},fr=class{constructor(t,e){this.canvas=t,this.camera=e;let i=0,s=0,a=0,d=0,n=0,r=0,h=0,o=0;new xe(t).addListener({down:(l,c)=>{i=e.position.x,s=e.position.y,d=r=l,n=h=c,o=e.zoom},dragged:(l,c)=>{let u=l-d,m=c-n,f=e.screenToWorld(new Mt(0,0),t.clientWidth,t.clientHeight),g=e.screenToWorld(new Mt(u,m),t.clientWidth,t.clientHeight).sub(f);e.position.set(i-g.x,s-g.y,0),e.update(),r=l,h=c},wheel:l=>{let c=l/200*e.zoom,u=e.zoom+c;if(u>0){let m=0,f=0;if(l<0)m=r,f=h;else{let v=new Mt(t.clientWidth/2+15,t.clientHeight/2),p=r-v.x,b=t.clientHeight-1-h-v.y;m=v.x-p,f=t.clientHeight-1-v.y+b}let g=e.screenToWorld(new Mt(m,f),t.clientWidth,t.clientHeight);e.zoom=u,e.update();let x=e.screenToWorld(new Mt(m,f),t.clientWidth,t.clientHeight);e.position.add(g.sub(x)),e.update()}},zoom:(l,c)=>{let u=l/c;e.zoom=o*u},up:(l,c)=>{r=l,h=c},moved:(l,c)=>{r=l,h=c}})}},ut=class{constructor(t,e,i){this.vertexShader=e,this.fragmentShader=i,this.vs=null,this.fs=null,this.program=null,this.tmp2x2=new Float32Array(2*2),this.tmp3x3=new Float32Array(3*3),this.tmp4x4=new Float32Array(4*4),this.vsSource=e,this.fsSource=i,this.context=t instanceof wt?t:new wt(t),this.context.addRestorable(this),this.compile()}getProgram(){return this.program}getVertexShader(){return this.vertexShader}getFragmentShader(){return this.fragmentShader}getVertexShaderSource(){return this.vsSource}getFragmentSource(){return this.fsSource}compile(){let t=this.context.gl;try{this.vs=this.compileShader(t.VERTEX_SHADER,this.vertexShader),this.fs=this.compileShader(t.FRAGMENT_SHADER,this.fragmentShader),this.program=this.compileProgram(this.vs,this.fs)}catch(e){throw this.dispose(),e}}compileShader(t,e){let i=this.context.gl,s=i.createShader(t);if(i.shaderSource(s,e),i.compileShader(s),!i.getShaderParameter(s,i.COMPILE_STATUS)){let a="Couldn't compile shader: "+i.getShaderInfoLog(s);if(i.deleteShader(s),!i.isContextLost())throw new Error(a)}return s}compileProgram(t,e){let i=this.context.gl,s=i.createProgram();if(i.attachShader(s,t),i.attachShader(s,e),i.linkProgram(s),!i.getProgramParameter(s,i.LINK_STATUS)){let a="Couldn't compile shader program: "+i.getProgramInfoLog(s);if(i.deleteProgram(s),!i.isContextLost())throw new Error(a)}return s}restore(){this.compile()}bind(){this.context.gl.useProgram(this.program)}unbind(){this.context.gl.useProgram(null)}setUniformi(t,e){this.context.gl.uniform1i(this.getUniformLocation(t),e)}setUniformf(t,e){this.context.gl.uniform1f(this.getUniformLocation(t),e)}setUniform2f(t,e,i){this.context.gl.uniform2f(this.getUniformLocation(t),e,i)}setUniform3f(t,e,i,s){this.context.gl.uniform3f(this.getUniformLocation(t),e,i,s)}setUniform4f(t,e,i,s,a){this.context.gl.uniform4f(this.getUniformLocation(t),e,i,s,a)}setUniform2x2f(t,e){let i=this.context.gl;this.tmp2x2.set(e),i.uniformMatrix2fv(this.getUniformLocation(t),!1,this.tmp2x2)}setUniform3x3f(t,e){let i=this.context.gl;this.tmp3x3.set(e),i.uniformMatrix3fv(this.getUniformLocation(t),!1,this.tmp3x3)}setUniform4x4f(t,e){let i=this.context.gl;this.tmp4x4.set(e),i.uniformMatrix4fv(this.getUniformLocation(t),!1,this.tmp4x4)}getUniformLocation(t){let e=this.context.gl,i=e.getUniformLocation(this.program,t);if(!i&&!e.isContextLost())throw new Error(`Couldn't find location for uniform ${t}`);return i}getAttributeLocation(t){let e=this.context.gl,i=e.getAttribLocation(this.program,t);if(i==-1&&!e.isContextLost())throw new Error(`Couldn't find location for attribute ${t}`);return i}dispose(){this.context.removeRestorable(this);let t=this.context.gl;this.vs&&(t.deleteShader(this.vs),this.vs=null),this.fs&&(t.deleteShader(this.fs),this.fs=null),this.program&&(t.deleteProgram(this.program),this.program=null)}static newColoredTextured(t){let e=`
				attribute vec4 ${ut.POSITION};
				attribute vec4 ${ut.COLOR};
				attribute vec2 ${ut.TEXCOORDS};
				uniform mat4 ${ut.MVP_MATRIX};
				varying vec4 v_color;
				varying vec2 v_texCoords;

				void main () {
					v_color = ${ut.COLOR};
					v_texCoords = ${ut.TEXCOORDS};
					gl_Position = ${ut.MVP_MATRIX} * ${ut.POSITION};
				}
			`,i=`
				#ifdef GL_ES
					#define LOWP lowp
					precision mediump float;
				#else
					#define LOWP
				#endif
				varying LOWP vec4 v_color;
				varying vec2 v_texCoords;
				uniform sampler2D u_texture;

				void main () {
					gl_FragColor = v_color * texture2D(u_texture, v_texCoords);
				}
			`;return new ut(t,e,i)}static newTwoColoredTextured(t){let e=`
				attribute vec4 ${ut.POSITION};
				attribute vec4 ${ut.COLOR};
				attribute vec4 ${ut.COLOR2};
				attribute vec2 ${ut.TEXCOORDS};
				uniform mat4 ${ut.MVP_MATRIX};
				varying vec4 v_light;
				varying vec4 v_dark;
				varying vec2 v_texCoords;

				void main () {
					v_light = ${ut.COLOR};
					v_dark = ${ut.COLOR2};
					v_texCoords = ${ut.TEXCOORDS};
					gl_Position = ${ut.MVP_MATRIX} * ${ut.POSITION};
				}
			`,i=`
				#ifdef GL_ES
					#define LOWP lowp
					precision mediump float;
				#else
					#define LOWP
				#endif
				varying LOWP vec4 v_light;
				varying LOWP vec4 v_dark;
				varying vec2 v_texCoords;
				uniform sampler2D u_texture;

				void main () {
					vec4 texColor = texture2D(u_texture, v_texCoords);
					gl_FragColor.a = texColor.a * v_light.a;
					gl_FragColor.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;
				}
			`;return new ut(t,e,i)}static newColored(t){let e=`
				attribute vec4 ${ut.POSITION};
				attribute vec4 ${ut.COLOR};
				uniform mat4 ${ut.MVP_MATRIX};
				varying vec4 v_color;

				void main () {
					v_color = ${ut.COLOR};
					gl_Position = ${ut.MVP_MATRIX} * ${ut.POSITION};
				}
			`,i=`
				#ifdef GL_ES
					#define LOWP lowp
					precision mediump float;
				#else
					#define LOWP
				#endif
				varying LOWP vec4 v_color;

				void main () {
					gl_FragColor = v_color;
				}
			`;return new ut(t,e,i)}},bt=ut;bt.MVP_MATRIX="u_projTrans",bt.POSITION="a_position",bt.COLOR="a_color",bt.COLOR2="a_color2",bt.TEXCOORDS="a_texCoords",bt.SAMPLER="u_texture";var Ri=class{constructor(t,e,i,s){this.attributes=e,this.verticesLength=0,this.dirtyVertices=!1,this.indicesLength=0,this.dirtyIndices=!1,this.elementsPerVertex=0,this.context=t instanceof wt?t:new wt(t),this.elementsPerVertex=0;for(let a=0;a<e.length;a++)this.elementsPerVertex+=e[a].numElements;this.vertices=new Float32Array(i*this.elementsPerVertex),this.indices=new Uint16Array(s),this.context.addRestorable(this)}getAttributes(){return this.attributes}maxVertices(){return this.vertices.length/this.elementsPerVertex}numVertices(){return this.verticesLength/this.elementsPerVertex}setVerticesLength(t){this.dirtyVertices=!0,this.verticesLength=t}getVertices(){return this.vertices}maxIndices(){return this.indices.length}numIndices(){return this.indicesLength}setIndicesLength(t){this.dirtyIndices=!0,this.indicesLength=t}getIndices(){return this.indices}getVertexSizeInFloats(){let t=0;for(var e=0;e<this.attributes.length;e++)t+=this.attributes[e].numElements;return t}setVertices(t){if(this.dirtyVertices=!0,t.length>this.vertices.length)throw Error("Mesh can't store more than "+this.maxVertices()+" vertices");this.vertices.set(t,0),this.verticesLength=t.length}setIndices(t){if(this.dirtyIndices=!0,t.length>this.indices.length)throw Error("Mesh can't store more than "+this.maxIndices()+" indices");this.indices.set(t,0),this.indicesLength=t.length}draw(t,e){this.drawWithOffset(t,e,0,this.indicesLength>0?this.indicesLength:this.verticesLength/this.elementsPerVertex)}drawWithOffset(t,e,i,s){let a=this.context.gl;(this.dirtyVertices||this.dirtyIndices)&&this.update(),this.bind(t),this.indicesLength>0?a.drawElements(e,s,a.UNSIGNED_SHORT,i*2):a.drawArrays(e,i,s),this.unbind(t)}bind(t){let e=this.context.gl;e.bindBuffer(e.ARRAY_BUFFER,this.verticesBuffer);let i=0;for(let s=0;s<this.attributes.length;s++){let a=this.attributes[s],d=t.getAttributeLocation(a.name);e.enableVertexAttribArray(d),e.vertexAttribPointer(d,a.numElements,e.FLOAT,!1,this.elementsPerVertex*4,i*4),i+=a.numElements}this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indicesBuffer)}unbind(t){let e=this.context.gl;for(let i=0;i<this.attributes.length;i++){let s=this.attributes[i],a=t.getAttributeLocation(s.name);e.disableVertexAttribArray(a)}e.bindBuffer(e.ARRAY_BUFFER,null),this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null)}update(){let t=this.context.gl;this.dirtyVertices&&(this.verticesBuffer||(this.verticesBuffer=t.createBuffer()),t.bindBuffer(t.ARRAY_BUFFER,this.verticesBuffer),t.bufferData(t.ARRAY_BUFFER,this.vertices.subarray(0,this.verticesLength),t.DYNAMIC_DRAW),this.dirtyVertices=!1),this.dirtyIndices&&(this.indicesBuffer||(this.indicesBuffer=t.createBuffer()),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indicesBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,this.indices.subarray(0,this.indicesLength),t.DYNAMIC_DRAW),this.dirtyIndices=!1)}restore(){this.verticesBuffer=null,this.indicesBuffer=null,this.update()}dispose(){this.context.removeRestorable(this);let t=this.context.gl;t.deleteBuffer(this.verticesBuffer),t.deleteBuffer(this.indicesBuffer)}},le=class{constructor(t,e,i){this.name=t,this.type=e,this.numElements=i}},Fe=class extends le{constructor(){super(bt.POSITION,Ht.Float,2)}},mr=class extends le{constructor(){super(bt.POSITION,Ht.Float,3)}},Fi=class extends le{constructor(t=0){super(bt.TEXCOORDS+(t==0?"":t),Ht.Float,2)}},Le=class extends le{constructor(){super(bt.COLOR,Ht.Float,4)}},bs=class extends le{constructor(){super(bt.COLOR2,Ht.Float,4)}},Ht;(function(t){t[t.Float=0]="Float"})(Ht||(Ht={}));var Li=class{constructor(t,e=!0,i=10920){if(this.isDrawing=!1,this.shader=null,this.lastTexture=null,this.verticesLength=0,this.indicesLength=0,i>10920)throw new Error("Can't have more than 10920 triangles per batch: "+i);this.context=t instanceof wt?t:new wt(t);let s=e?[new Fe,new Le,new Fi,new bs]:[new Fe,new Le,new Fi];this.mesh=new Ri(t,s,i,i*3);let a=this.context.gl;this.srcColorBlend=a.SRC_ALPHA,this.srcAlphaBlend=a.ONE,this.dstBlend=a.ONE_MINUS_SRC_ALPHA}begin(t){if(this.isDrawing)throw new Error("PolygonBatch is already drawing. Call PolygonBatch.end() before calling PolygonBatch.begin()");this.drawCalls=0,this.shader=t,this.lastTexture=null,this.isDrawing=!0;let e=this.context.gl;e.enable(e.BLEND),e.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend)}setBlendMode(t,e,i){this.srcColorBlend==t&&this.srcAlphaBlend==e&&this.dstBlend==i||(this.srcColorBlend=t,this.srcAlphaBlend=e,this.dstBlend=i,this.isDrawing&&(this.flush(),this.context.gl.blendFuncSeparate(t,i,e,i)))}draw(t,e,i){t!=this.lastTexture?(this.flush(),this.lastTexture=t):(this.verticesLength+e.length>this.mesh.getVertices().length||this.indicesLength+i.length>this.mesh.getIndices().length)&&this.flush();let s=this.mesh.numVertices();this.mesh.getVertices().set(e,this.verticesLength),this.verticesLength+=e.length,this.mesh.setVerticesLength(this.verticesLength);let a=this.mesh.getIndices();for(let d=this.indicesLength,n=0;n<i.length;d++,n++)a[d]=i[n]+s;this.indicesLength+=i.length,this.mesh.setIndicesLength(this.indicesLength)}flush(){this.verticesLength!=0&&(this.lastTexture.bind(),this.mesh.draw(this.shader,this.context.gl.TRIANGLES),this.verticesLength=0,this.indicesLength=0,this.mesh.setVerticesLength(0),this.mesh.setIndicesLength(0),this.drawCalls++)}end(){if(!this.isDrawing)throw new Error("PolygonBatch is not drawing. Call PolygonBatch.begin() before calling PolygonBatch.end()");(this.verticesLength>0||this.indicesLength>0)&&this.flush(),this.shader=null,this.lastTexture=null,this.isDrawing=!1;let t=this.context.gl;t.disable(t.BLEND)}getDrawCalls(){return this.drawCalls}dispose(){this.mesh.dispose()}},Xi=class{constructor(t,e=10920){if(this.isDrawing=!1,this.shapeType=At.Filled,this.color=new O(1,1,1,1),this.vertexIndex=0,this.tmp=new Yt,e>10920)throw new Error("Can't have more than 10920 triangles per batch: "+e);this.context=t instanceof wt?t:new wt(t),this.mesh=new Ri(t,[new Fe,new Le],e,0);let i=this.context.gl;this.srcColorBlend=i.SRC_ALPHA,this.srcAlphaBlend=i.ONE,this.dstBlend=i.ONE_MINUS_SRC_ALPHA}begin(t){if(this.isDrawing)throw new Error("ShapeRenderer.begin() has already been called");this.shader=t,this.vertexIndex=0,this.isDrawing=!0;let e=this.context.gl;e.enable(e.BLEND),e.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend)}setBlendMode(t,e,i){this.srcColorBlend=t,this.srcAlphaBlend=e,this.dstBlend=i,this.isDrawing&&(this.flush(),this.context.gl.blendFuncSeparate(t,i,e,i))}setColor(t){this.color.setFromColor(t)}setColorWith(t,e,i,s){this.color.set(t,e,i,s)}point(t,e,i=null){this.check(At.Point,1),i===null&&(i=this.color),this.vertex(t,e,i)}line(t,e,i,s,a=null){this.check(At.Line,2);let d=this.mesh.getVertices(),n=this.vertexIndex;a===null&&(a=this.color),this.vertex(t,e,a),this.vertex(i,s,a)}triangle(t,e,i,s,a,d,n,r=null,h=null,o=null){this.check(t?At.Filled:At.Line,3);let l=this.mesh.getVertices(),c=this.vertexIndex;r===null&&(r=this.color),h===null&&(h=this.color),o===null&&(o=this.color),t?(this.vertex(e,i,r),this.vertex(s,a,h),this.vertex(d,n,o)):(this.vertex(e,i,r),this.vertex(s,a,h),this.vertex(s,a,r),this.vertex(d,n,h),this.vertex(d,n,r),this.vertex(e,i,h))}quad(t,e,i,s,a,d,n,r,h,o=null,l=null,c=null,u=null){this.check(t?At.Filled:At.Line,3);let m=this.mesh.getVertices(),f=this.vertexIndex;o===null&&(o=this.color),l===null&&(l=this.color),c===null&&(c=this.color),u===null&&(u=this.color),t?(this.vertex(e,i,o),this.vertex(s,a,l),this.vertex(d,n,c),this.vertex(d,n,c),this.vertex(r,h,u),this.vertex(e,i,o)):(this.vertex(e,i,o),this.vertex(s,a,l),this.vertex(s,a,l),this.vertex(d,n,c),this.vertex(d,n,c),this.vertex(r,h,u),this.vertex(r,h,u),this.vertex(e,i,o))}rect(t,e,i,s,a,d=null){this.quad(t,e,i,e+s,i,e+s,i+a,e,i+a,d,d,d,d)}rectLine(t,e,i,s,a,d,n=null){this.check(t?At.Filled:At.Line,8),n===null&&(n=this.color);let r=this.tmp.set(a-i,e-s);r.normalize(),d*=.5;let h=r.x*d,o=r.y*d;t?(this.vertex(e+h,i+o,n),this.vertex(e-h,i-o,n),this.vertex(s+h,a+o,n),this.vertex(s-h,a-o,n),this.vertex(s+h,a+o,n),this.vertex(e-h,i-o,n)):(this.vertex(e+h,i+o,n),this.vertex(e-h,i-o,n),this.vertex(s+h,a+o,n),this.vertex(s-h,a-o,n),this.vertex(s+h,a+o,n),this.vertex(e+h,i+o,n),this.vertex(s-h,a-o,n),this.vertex(e-h,i-o,n))}x(t,e,i){this.line(t-i,e-i,t+i,e+i),this.line(t-i,e+i,t+i,e-i)}polygon(t,e,i,s=null){if(i<3)throw new Error("Polygon must contain at least 3 vertices");this.check(At.Line,i*2),s===null&&(s=this.color);let a=this.mesh.getVertices(),d=this.vertexIndex;e<<=1,i<<=1;let n=t[e],r=t[e+1],h=e+i;for(let o=e,l=e+i-2;o<l;o+=2){let c=t[o],u=t[o+1],m=0,f=0;o+2>=h?(m=n,f=r):(m=t[o+2],f=t[o+3]),this.vertex(c,u,s),this.vertex(m,f,s)}}circle(t,e,i,s,a=null,d=0){if(d===0&&(d=Math.max(1,6*F.cbrt(s)|0)),d<=0)throw new Error("segments must be > 0.");a===null&&(a=this.color);let n=2*F.PI/d,r=Math.cos(n),h=Math.sin(n),o=s,l=0;if(t){this.check(At.Filled,d*3+3),d--;for(let u=0;u<d;u++){this.vertex(e,i,a),this.vertex(e+o,i+l,a);let m=o;o=r*o-h*l,l=h*m+r*l,this.vertex(e+o,i+l,a)}this.vertex(e,i,a),this.vertex(e+o,i+l,a)}else{this.check(At.Line,d*2+2);for(let u=0;u<d;u++){this.vertex(e+o,i+l,a);let m=o;o=r*o-h*l,l=h*m+r*l,this.vertex(e+o,i+l,a)}this.vertex(e+o,i+l,a)}let c=o;o=s,l=0,this.vertex(e+o,i+l,a)}curve(t,e,i,s,a,d,n,r,h,o=null){this.check(At.Line,h*2+2),o===null&&(o=this.color);let l=1/h,c=l*l,u=l*l*l,m=3*l,f=3*c,g=6*c,x=6*u,v=t-i*2+a,p=e-s*2+d,b=(i-a)*3-t+n,w=(s-d)*3-e+r,y=t,T=e,A=(i-t)*m+v*f+b*u,E=(s-e)*m+p*f+w*u,I=v*g+b*x,Y=p*g+w*x,P=b*x,X=w*x;for(;h-- >0;)this.vertex(y,T,o),y+=A,T+=E,A+=I,E+=Y,I+=P,Y+=X,this.vertex(y,T,o);this.vertex(y,T,o),this.vertex(n,r,o)}vertex(t,e,i){let s=this.vertexIndex,a=this.mesh.getVertices();a[s++]=t,a[s++]=e,a[s++]=i.r,a[s++]=i.g,a[s++]=i.b,a[s++]=i.a,this.vertexIndex=s}end(){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");this.flush();let t=this.context.gl;t.disable(t.BLEND),this.isDrawing=!1}flush(){this.vertexIndex!=0&&(this.mesh.setVerticesLength(this.vertexIndex),this.mesh.draw(this.shader,this.shapeType),this.vertexIndex=0)}check(t,e){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");if(this.shapeType==t)if(this.mesh.maxVertices()-this.mesh.numVertices()<e)this.flush();else return;else this.flush(),this.shapeType=t}dispose(){this.mesh.dispose()}},At;(function(t){t[t.Point=0]="Point",t[t.Line=1]="Line",t[t.Filled=4]="Filled"})(At||(At={}));var Xe=class{constructor(t){this.boneLineColor=new O(1,0,0,1),this.boneOriginColor=new O(0,1,0,1),this.attachmentLineColor=new O(0,0,1,.5),this.triangleLineColor=new O(1,.64,0,.5),this.pathColor=new O().setFromString("FF7F00"),this.clipColor=new O(.8,0,0,2),this.aabbColor=new O(0,1,0,.5),this.drawBones=!0,this.drawRegionAttachments=!0,this.drawBoundingBoxes=!0,this.drawMeshHull=!0,this.drawMeshTriangles=!0,this.drawPaths=!0,this.drawSkeletonXY=!1,this.drawClipping=!0,this.premultipliedAlpha=!1,this.scale=1,this.boneWidth=2,this.bounds=new us,this.temp=new Array,this.vertices=V.newFloatArray(2*1024),this.context=t instanceof wt?t:new wt(t)}draw(t,e,i=null){let s=e.x,a=e.y,d=this.context.gl,n=this.premultipliedAlpha?d.ONE:d.SRC_ALPHA;t.setBlendMode(n,d.ONE,d.ONE_MINUS_SRC_ALPHA);let r=e.bones;if(this.drawBones){t.setColor(this.boneLineColor);for(let h=0,o=r.length;h<o;h++){let l=r[h];if(i&&i.indexOf(l.data.name)>-1||!l.parent)continue;let c=s+l.data.length*l.a+l.worldX,u=a+l.data.length*l.c+l.worldY;t.rectLine(!0,s+l.worldX,a+l.worldY,c,u,this.boneWidth*this.scale)}this.drawSkeletonXY&&t.x(s,a,4*this.scale)}if(this.drawRegionAttachments){t.setColor(this.attachmentLineColor);let h=e.slots;for(let o=0,l=h.length;o<l;o++){let c=h[o],u=c.getAttachment();if(u instanceof at){let m=u,f=this.vertices;m.computeWorldVertices(c.bone,f,0,2),t.line(f[0],f[1],f[2],f[3]),t.line(f[2],f[3],f[4],f[5]),t.line(f[4],f[5],f[6],f[7]),t.line(f[6],f[7],f[0],f[1])}}}if(this.drawMeshHull||this.drawMeshTriangles){let h=e.slots;for(let o=0,l=h.length;o<l;o++){let c=h[o];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof jt))continue;let m=u,f=this.vertices;m.computeWorldVertices(c,0,m.worldVerticesLength,f,0,2);let g=m.triangles,x=m.hullLength;if(this.drawMeshTriangles){t.setColor(this.triangleLineColor);for(let v=0,p=g.length;v<p;v+=3){let b=g[v]*2,w=g[v+1]*2,y=g[v+2]*2;t.triangle(!1,f[b],f[b+1],f[w],f[w+1],f[y],f[y+1])}}if(this.drawMeshHull&&x>0){t.setColor(this.attachmentLineColor),x=(x>>1)*2;let v=f[x-2],p=f[x-1];for(let b=0,w=x;b<w;b+=2){let y=f[b],T=f[b+1];t.line(y,T,v,p),v=y,p=T}}}}if(this.drawBoundingBoxes){let h=this.bounds;h.update(e,!0),t.setColor(this.aabbColor),t.rect(!1,h.minX,h.minY,h.getWidth(),h.getHeight());let o=h.polygons,l=h.boundingBoxes;for(let c=0,u=o.length;c<u;c++){let m=o[c];t.setColor(l[c].color),t.polygon(m,0,m.length)}}if(this.drawPaths){let h=e.slots;for(let o=0,l=h.length;o<l;o++){let c=h[o];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof te))continue;let m=u,f=m.worldVerticesLength,g=this.temp=V.setArraySize(this.temp,f,0);m.computeWorldVertices(c,0,f,g,0,2);let x=this.pathColor,v=g[2],p=g[3],b=0,w=0;if(m.closed){t.setColor(x);let y=g[0],T=g[1],A=g[f-2],E=g[f-1];b=g[f-4],w=g[f-3],t.curve(v,p,y,T,A,E,b,w,32),t.setColor(Xe.LIGHT_GRAY),t.line(v,p,y,T),t.line(b,w,A,E)}f-=4;for(let y=4;y<f;y+=6){let T=g[y],A=g[y+1],E=g[y+2],I=g[y+3];b=g[y+4],w=g[y+5],t.setColor(x),t.curve(v,p,T,A,E,I,b,w,32),t.setColor(Xe.LIGHT_GRAY),t.line(v,p,T,A),t.line(b,w,E,I),v=b,p=w}}}if(this.drawBones){t.setColor(this.boneOriginColor);for(let h=0,o=r.length;h<o;h++){let l=r[h];i&&i.indexOf(l.data.name)>-1||t.circle(!0,s+l.worldX,a+l.worldY,3*this.scale,Xe.GREEN,8)}}if(this.drawClipping){let h=e.slots;t.setColor(this.clipColor);for(let o=0,l=h.length;o<l;o++){let c=h[o];if(!c.bone.active)continue;let u=c.getAttachment();if(!(u instanceof ge))continue;let m=u,f=m.worldVerticesLength,g=this.temp=V.setArraySize(this.temp,f,0);m.computeWorldVertices(c,0,f,g,0,2);for(let x=0,v=g.length;x<v;x+=2){let p=g[x],b=g[x+1],w=g[(x+2)%g.length],y=g[(x+3)%g.length];t.line(p,b,w,y)}}}}dispose(){}},Be=Xe;Be.LIGHT_GRAY=new O(192/255,192/255,192/255,1),Be.GREEN=new O(0,1,0,1);var gr=class{constructor(t,e,i){this.vertices=t,this.numVertices=e,this.numFloats=i}},vs=class{constructor(t,e=!0){this.premultipliedAlpha=!1,this.vertexEffect=null,this.tempColor=new O,this.tempColor2=new O,this.vertexSize=2+2+4,this.twoColorTint=!1,this.renderable=new gr(null,0,0),this.clipper=new Ie,this.temp=new Yt,this.temp2=new Yt,this.temp3=new O,this.temp4=new O,this.twoColorTint=e,e&&(this.vertexSize+=4),this.vertices=V.newFloatArray(this.vertexSize*1024)}draw(t,e,i=-1,s=-1){let a=this.clipper,d=this.premultipliedAlpha,n=this.twoColorTint,r=null,h=this.temp,o=this.temp2,l=this.temp3,c=this.temp4,u=this.renderable,m=null,f=null,g=e.drawOrder,x=null,v=e.color,p=n?12:8,b=!1;i==-1&&(b=!0);for(let w=0,y=g.length;w<y;w++){let T=a.isClipping()?2:p,A=g[w];if(!A.bone.active){a.clipEndWithSlot(A);continue}if(i>=0&&i==A.data.index&&(b=!0),!b){a.clipEndWithSlot(A);continue}s>=0&&s==A.data.index&&(b=!1);let E=A.getAttachment(),I=null;if(E instanceof at){let Y=E;u.vertices=this.vertices,u.numVertices=4,u.numFloats=T<<2,Y.computeWorldVertices(A.bone,u.vertices,0,T),f=vs.QUAD_TRIANGLES,m=Y.uvs,I=Y.region.renderObject.page.texture,x=Y.color}else if(E instanceof jt){let Y=E;u.vertices=this.vertices,u.numVertices=Y.worldVerticesLength>>1,u.numFloats=u.numVertices*T,u.numFloats>u.vertices.length&&(u.vertices=this.vertices=V.newFloatArray(u.numFloats)),Y.computeWorldVertices(A,0,Y.worldVerticesLength,u.vertices,0,T),f=Y.triangles,I=Y.region.renderObject.page.texture,m=Y.uvs,x=Y.color}else if(E instanceof ge){let Y=E;a.clipStart(A,Y);continue}else{a.clipEndWithSlot(A);continue}if(I){let Y=A.color,P=this.tempColor;P.r=v.r*Y.r*x.r,P.g=v.g*Y.g*x.g,P.b=v.b*Y.b*x.b,P.a=v.a*Y.a*x.a,d&&(P.r*=P.a,P.g*=P.a,P.b*=P.a);let X=this.tempColor2;A.darkColor?(d?(X.r=A.darkColor.r*P.a,X.g=A.darkColor.g*P.a,X.b=A.darkColor.b*P.a):X.setFromColor(A.darkColor),X.a=d?1:0):X.set(0,0,0,1);let B=A.data.blendMode;if(B!=r&&(r=B,t.setBlendMode(Re.getSourceColorGLBlendMode(r,d),Re.getSourceAlphaGLBlendMode(r),Re.getDestGLBlendMode(r))),a.isClipping()){a.clipTriangles(u.vertices,u.numFloats,f,f.length,m,P,X,n);let C=new Float32Array(a.clippedVertices),k=a.clippedTriangles;if(this.vertexEffect){let D=this.vertexEffect,R=C;if(n)for(let L=0,st=C.length;L<st;L+=p)h.x=R[L],h.y=R[L+1],l.set(R[L+2],R[L+3],R[L+4],R[L+5]),o.x=R[L+6],o.y=R[L+7],c.set(R[L+8],R[L+9],R[L+10],R[L+11]),D.transform(h,o,l,c),R[L]=h.x,R[L+1]=h.y,R[L+2]=l.r,R[L+3]=l.g,R[L+4]=l.b,R[L+5]=l.a,R[L+6]=o.x,R[L+7]=o.y,R[L+8]=c.r,R[L+9]=c.g,R[L+10]=c.b,R[L+11]=c.a;else for(let L=0,st=C.length;L<st;L+=p)h.x=R[L],h.y=R[L+1],l.set(R[L+2],R[L+3],R[L+4],R[L+5]),o.x=R[L+6],o.y=R[L+7],c.set(0,0,0,0),D.transform(h,o,l,c),R[L]=h.x,R[L+1]=h.y,R[L+2]=l.r,R[L+3]=l.g,R[L+4]=l.b,R[L+5]=l.a,R[L+6]=o.x,R[L+7]=o.y}t.draw(I,C,k)}else{let C=u.vertices;if(this.vertexEffect){let D=this.vertexEffect;if(n)for(let R=0,L=0,st=u.numFloats;R<st;R+=p,L+=2)h.x=C[R],h.y=C[R+1],o.x=m[L],o.y=m[L+1],l.setFromColor(P),c.setFromColor(X),D.transform(h,o,l,c),C[R]=h.x,C[R+1]=h.y,C[R+2]=l.r,C[R+3]=l.g,C[R+4]=l.b,C[R+5]=l.a,C[R+6]=o.x,C[R+7]=o.y,C[R+8]=c.r,C[R+9]=c.g,C[R+10]=c.b,C[R+11]=c.a;else for(let R=0,L=0,st=u.numFloats;R<st;R+=p,L+=2)h.x=C[R],h.y=C[R+1],o.x=m[L],o.y=m[L+1],l.setFromColor(P),c.set(0,0,0,0),D.transform(h,o,l,c),C[R]=h.x,C[R+1]=h.y,C[R+2]=l.r,C[R+3]=l.g,C[R+4]=l.b,C[R+5]=l.a,C[R+6]=o.x,C[R+7]=o.y}else if(n)for(let D=2,R=0,L=u.numFloats;D<L;D+=p,R+=2)C[D]=P.r,C[D+1]=P.g,C[D+2]=P.b,C[D+3]=P.a,C[D+4]=m[R],C[D+5]=m[R+1],C[D+6]=X.r,C[D+7]=X.g,C[D+8]=X.b,C[D+9]=X.a;else for(let D=2,R=0,L=u.numFloats;D<L;D+=p,R+=2)C[D]=P.r,C[D+1]=P.g,C[D+2]=P.b,C[D+3]=P.a,C[D+4]=m[R],C[D+5]=m[R+1];let k=u.vertices.subarray(0,u.numFloats);t.draw(I,k,f)}}a.clipEndWithSlot(A)}a.clipEnd()}},Bi=vs;Bi.QUAD_TRIANGLES=[0,1,2,2,3,0];var S=[0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0],Pe=[0,1,2,2,3,0],Ve=new O(1,1,1,1),Pi=class{constructor(t,e,i=!0){this.twoColorTint=!1,this.canvas=t,this.context=e instanceof wt?e:new wt(e),this.twoColorTint=i,this.camera=new xs(t.width,t.height),this.batcherShader=i?bt.newTwoColoredTextured(this.context):bt.newColoredTextured(this.context),this.batcher=new Li(this.context,i),this.shapesShader=bt.newColored(this.context),this.shapes=new Xi(this.context),this.skeletonRenderer=new Bi(this.context,i),this.skeletonDebugRenderer=new Be(this.context)}dispose(){this.batcher.dispose(),this.batcherShader.dispose(),this.shapes.dispose(),this.shapesShader.dispose(),this.skeletonDebugRenderer.dispose()}begin(){this.camera.update(),this.enableRenderer(this.batcher)}drawSkeleton(t,e=!1,i=-1,s=-1){this.enableRenderer(this.batcher),this.skeletonRenderer.premultipliedAlpha=e,this.skeletonRenderer.draw(this.batcher,t,i,s)}drawSkeletonDebug(t,e=!1,i=null){this.enableRenderer(this.shapes),this.skeletonDebugRenderer.premultipliedAlpha=e,this.skeletonDebugRenderer.draw(this.shapes,t,i)}drawTexture(t,e,i,s,a,d=null){this.enableRenderer(this.batcher),d===null&&(d=Ve);var n=0;S[n++]=e,S[n++]=i,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=0,S[n++]=1,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e+s,S[n++]=i,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=1,S[n++]=1,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e+s,S[n++]=i+a,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=1,S[n++]=0,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e,S[n++]=i+a,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=0,S[n++]=0,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n]=0),this.batcher.draw(t,S,Pe)}drawTextureUV(t,e,i,s,a,d,n,r,h,o=null){this.enableRenderer(this.batcher),o===null&&(o=Ve);var l=0;S[l++]=e,S[l++]=i,S[l++]=o.r,S[l++]=o.g,S[l++]=o.b,S[l++]=o.a,S[l++]=d,S[l++]=n,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=e+s,S[l++]=i,S[l++]=o.r,S[l++]=o.g,S[l++]=o.b,S[l++]=o.a,S[l++]=r,S[l++]=n,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=e+s,S[l++]=i+a,S[l++]=o.r,S[l++]=o.g,S[l++]=o.b,S[l++]=o.a,S[l++]=r,S[l++]=h,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l++]=0),S[l++]=e,S[l++]=i+a,S[l++]=o.r,S[l++]=o.g,S[l++]=o.b,S[l++]=o.a,S[l++]=d,S[l++]=h,this.twoColorTint&&(S[l++]=0,S[l++]=0,S[l++]=0,S[l]=0),this.batcher.draw(t,S,Pe)}drawTextureRotated(t,e,i,s,a,d,n,r,h=null){this.enableRenderer(this.batcher),h===null&&(h=Ve);let o=e+d,l=i+n,c=-d,u=-n,m=s-d,f=a-n,g=c,x=u,v=c,p=f,b=m,w=f,y=m,T=u,A=0,E=0,I=0,Y=0,P=0,X=0,B=0,C=0;if(r!=0){let D=F.cosDeg(r),R=F.sinDeg(r);A=D*g-R*x,E=R*g+D*x,B=D*v-R*p,C=R*v+D*p,P=D*b-R*w,X=R*b+D*w,I=P+(A-B),Y=X+(E-C)}else A=g,E=x,B=v,C=p,P=b,X=w,I=y,Y=T;A+=o,E+=l,I+=o,Y+=l,P+=o,X+=l,B+=o,C+=l;var k=0;S[k++]=A,S[k++]=E,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=0,S[k++]=1,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=I,S[k++]=Y,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=1,S[k++]=1,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=P,S[k++]=X,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=1,S[k++]=0,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k++]=0),S[k++]=B,S[k++]=C,S[k++]=h.r,S[k++]=h.g,S[k++]=h.b,S[k++]=h.a,S[k++]=0,S[k++]=0,this.twoColorTint&&(S[k++]=0,S[k++]=0,S[k++]=0,S[k]=0),this.batcher.draw(t,S,Pe)}drawRegion(t,e,i,s,a,d=null){this.enableRenderer(this.batcher),d===null&&(d=Ve);var n=0;S[n++]=e,S[n++]=i,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=t.u,S[n++]=t.v2,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e+s,S[n++]=i,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=t.u2,S[n++]=t.v2,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e+s,S[n++]=i+a,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=t.u2,S[n++]=t.v,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n++]=0),S[n++]=e,S[n++]=i+a,S[n++]=d.r,S[n++]=d.g,S[n++]=d.b,S[n++]=d.a,S[n++]=t.u,S[n++]=t.v,this.twoColorTint&&(S[n++]=0,S[n++]=0,S[n++]=0,S[n]=0),this.batcher.draw(t.page.texture,S,Pe)}line(t,e,i,s,a=null,d=null){this.enableRenderer(this.shapes),this.shapes.line(t,e,i,s,a)}triangle(t,e,i,s,a,d,n,r=null,h=null,o=null){this.enableRenderer(this.shapes),this.shapes.triangle(t,e,i,s,a,d,n,r,h,o)}quad(t,e,i,s,a,d,n,r,h,o=null,l=null,c=null,u=null){this.enableRenderer(this.shapes),this.shapes.quad(t,e,i,s,a,d,n,r,h,o,l,c,u)}rect(t,e,i,s,a,d=null){this.enableRenderer(this.shapes),this.shapes.rect(t,e,i,s,a,d)}rectLine(t,e,i,s,a,d,n=null){this.enableRenderer(this.shapes),this.shapes.rectLine(t,e,i,s,a,d,n)}polygon(t,e,i,s=null){this.enableRenderer(this.shapes),this.shapes.polygon(t,e,i,s)}circle(t,e,i,s,a=null,d=0){this.enableRenderer(this.shapes),this.shapes.circle(t,e,i,s,a,d)}curve(t,e,i,s,a,d,n,r,h,o=null){this.enableRenderer(this.shapes),this.shapes.curve(t,e,i,s,a,d,n,r,h,o)}end(){this.activeRenderer===this.batcher?this.batcher.end():this.activeRenderer===this.shapes&&this.shapes.end(),this.activeRenderer=null}resize(t){let e=this.canvas;var i=window.devicePixelRatio||1,s=Math.round(e.clientWidth*i),a=Math.round(e.clientHeight*i);if((e.width!=s||e.height!=a)&&(e.width=s,e.height=a),this.context.gl.viewport(0,0,e.width,e.height),t===ie.Expand)this.camera.setViewport(s,a);else if(t===ie.Fit){let d=e.width,n=e.height,r=this.camera.viewportWidth,h=this.camera.viewportHeight,o=h/r,l=n/d,c=o<l?r/d:h/n;this.camera.setViewport(d*c,n*c)}this.camera.update()}enableRenderer(t){this.activeRenderer!==t&&(this.end(),t instanceof Li?(this.batcherShader.bind(),this.batcherShader.setUniform4x4f(bt.MVP_MATRIX,this.camera.projectionView.values),this.batcherShader.setUniformi("u_texture",0),this.batcher.begin(this.batcherShader),this.activeRenderer=this.batcher):t instanceof Xi?(this.shapesShader.bind(),this.shapesShader.setUniform4x4f(bt.MVP_MATRIX,this.camera.projectionView.values),this.shapes.begin(this.shapesShader),this.activeRenderer=this.shapes):this.activeRenderer=this.skeletonDebugRenderer)}},ie;(function(t){t[t.Stretch=0]="Stretch",t[t.Expand=1]="Expand",t[t.Fit=2]="Fit"})(ie||(ie={}));var we,oe,ys=0,pr=1,Vi=1,As=165,Ss=108,se=163,Cs=class{constructor(t){if(this.logo=null,this.spinner=null,this.angle=0,this.fadeOut=0,this.fadeIn=0,this.timeKeeper=new ve,this.backgroundColor=new O(.135,.135,.135,1),this.tempColor=new O,this.renderer=t,this.timeKeeper.maxDelta=9,!oe){let e=navigator.userAgent.indexOf("Safari")>-1,i=()=>ys++;oe=new Image,oe.src=wr,e||(oe.crossOrigin="anonymous"),oe.onload=i,we=new Image,we.src=xr,e||(we.crossOrigin="anonymous"),we.onload=i}}dispose(){this.logo.dispose(),this.spinner.dispose()}draw(t=!1){if(ys<2||t&&this.fadeOut>Vi)return;this.timeKeeper.update();let e=Math.abs(Math.sin(this.timeKeeper.totalTime+.25));this.angle-=this.timeKeeper.delta*200*(1+1.5*Math.pow(e,5));let i=this.tempColor,s=this.renderer,a=s.canvas,d=s.context.gl;if(s.resize(ie.Expand),s.camera.position.set(a.width/2,a.height/2,0),s.batcher.setBlendMode(d.ONE,d.ONE,d.ONE_MINUS_SRC_ALPHA),t){if(this.fadeOut+=this.timeKeeper.delta*(this.timeKeeper.totalTime<1?2:1),this.fadeOut>Vi)return;i.setFromColor(this.backgroundColor),e=1-this.fadeOut/Vi,e=1-(e-1)*(e-1),i.a*=e,i.a>0&&(s.camera.zoom=1,s.begin(),s.quad(!0,0,0,a.width,0,a.width,a.height,0,a.height,i,i,i,i),s.end())}else this.fadeIn+=this.timeKeeper.delta,this.backgroundColor.a>0&&(d.clearColor(this.backgroundColor.r,this.backgroundColor.g,this.backgroundColor.b,this.backgroundColor.a),d.clear(d.COLOR_BUFFER_BIT)),e=1;e*=Math.min(this.fadeIn/pr,1),i.set(e,e,e,e),this.logo||(this.logo=new pe(s.context,oe),this.spinner=new pe(s.context,we)),s.camera.zoom=Math.max(1,se/a.height),s.begin(),s.drawTexture(this.logo,(a.width-As)/2,(a.height-Ss)/2,As,Ss,i),s.drawTextureRotated(this.spinner,(a.width-se)/2,(a.height-se)/2,se,se,se/2,se/2,this.angle,i),s.end()}},xr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKMAAACjCAYAAADmbK6AAAALKElEQVR42u2de2iW5R/GPzuqcwfnnKfNmafl5tTNHWzqNi3DEMQykcAoJSsySkspjSIk0iD/07Kf4R+FnVBDUTshZGpWUEJaaiWFgZlUFmXmIe3HNXthyebeZ77P9H13ffBG8Y8H7ut7vff93N/7fu4vGGPiFZiez/Qtw9lytJajfzfw9z/j+efPOv7cV8W+lUNY2a8T/ayTCRsWFLJA5rtUO1LLkV5p9LJeJizQiHeqnlOtmVFtdTGrrZkJCxYXsTgaI6r9MY4/UpNItW4mFDaXsTlaM6qVZlBq3UwofFrJp0HMWJ9DvXUzobCznJ1BzFjWlTLrZkJh/TDWBzFjTgo51s2EgnKI0Rrx+FiOWzNzVaym91Syx5qZsGBWb2ZFa0ZN6dbMhAWTcpkUrRmXD2K5NTNhgVbH0Zpxbl/mWjMTFvRIo0e0ZpzcncnWzISKtvmiMWNRJ4qslwmVXRXsas2Ix8ZwzFqZsGFREYtaM+Oaa1ljrUzYkJ9G/ok6TlzKjJWZVFor0y7c1Zu7WjLiqiGsskamXdHopyT4vALmzS9k/t19uHtKHlOSIMn6xAtARjIZ1sFcUSZ0Y4La+G6M18hS2IlCn4a+WoC0JNL0d/dUupdnUj40g6EJ2VEdMnhrOG/p5f/jUXz8SgmvaGU6KpNRNsLVQV0OdXf24s63h/P2gWoOrBjMCr2GJFQnnxnIM3q5P1PPmaYv+4ev4/C6UtbpV2gzXCkgL5W8Bwt48OIc6ul6Tp+s4+SyASxLiI4+PYCn1bHzDZxvaQW6vZzto7MYnQIpNkf7kp5EuozYUroqEjcNKHHd0Tl9mBPN1pk+hFeieGBnBtog7UXjsj9pWg+m6duecw2cay1OC/uxMC47KmP9OIYfoz1YoC20J/rzRG4quTZK2EAyJGs20qwUbYw0aNRmUxtvfUW/uEtNzc1NB1/X8LVyd15hh82F43AvD+VlXcsSJEZa1CQ3ejleAO7oxR3RDP0XN91X4+NXYb8nkv7UNTwV7e0YTdu7I3g33t7tuaEbNwSZpps2fSyvs4M2Tjhot+jb0Xzbltj8r5j/xVt/6Z1Ob93U1ZYO691EhhzchcHeXosVjcNZysyezLw4xRZt05R+fTeuj8vOj+zKyG0j2aZcVVs6v+QalnjrMFZASQYl2nBoSyz06e3j/Xk8rgWYmMvEICu2pm1HOTuc7okV8FgRj0XukwzanhvCc/F+72TjoQjdObN1OFuDLmh0xP+WHtxiI10ukJlCprb4guiv1fP+avZrS1C7NAkliHZjDtZwMMgqbukAltpMlwuMy2FcEBPqvfLLar5Uqi0hBdEwryy+Mv5n6zkbjTBa+dlMlwvUZFETZKGiFM7tvbhdJ3gSVRO0wzIjnxmvl/J6a6JsGMYGrahtpssFeqbR841S3mhN80OjOaSDEdqd6SjaMKgzgzRK7q1ib3PT9sYyNo7JZoyNFNvRcVMZmy7WOvIuryv/Zvdmdt90+nY0bRp3AvROohFwdwW7dTG7RFlbwlqdrbOBYg005NAQmZU0HWt1rXMBH1Xw0dQ8pmqzoaPmdhun7bHZjNVe9qP9eFQfO1VkUmHjhAVUZ1GtnKFSbjrkrPfy4i4UW5t/6ZxM54J0CqxFe81KpGsQyE4h23oYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjLna+bdOy+IiFquIpGq16Pb79cNYv3IIK/X/ugx+Ui6TVKvYVU9Nc8gX8od8Ir/IN/KPfCQ/yVfyl/6/pfJvLChkQdD6wyqntquCXYuKWJSfRr6D0dEAxV3xlw/khyD+kd/ku/88cHo+09tS3LBpO1HHCVUqcIA6CqB4K+6X6x35L/JM2loXurlWmUmlA5XogOIcK8/If5HncrSWo7F6cKIWPjT/RXGOlWfkv8hzaWsN4uaaysE6WIkOKM6x8oz8F3kusXqo2vxC5jtYiQ4ozrH0TeS5qIZcrB7qkrwdA8U5Vp6R/yLPZV8V+2L14Cl5THGwEh1QnGPlGfkv8lyUlIzFQ1cNYVVHrcjZ0VCcFe9Y+Eb+izy3ceclUl43aFN52DXXssYpnY6a4qFS8ZcP2uIf+e7inRh6pdFrdTGrm8uiHx/L8T2V7NGWzvJBLJ/bl7mTuzO5qBNFDoiJID/IF/KHfCK/yDfyT3O7d/KbfNfS80hNIrU0g9L6HOq1x5iTQo6FNpeLfCQ/yVfyl3xmXYwxxhhjjDHGGGOMMcYYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjDHNk9z4JwJ0SqKTdQkbyEwhU393T6V7zzR6pieR3tE1ITeVXImhe6BXDGZFdRbVeank2TBhcaEMr0rwbixj49IBLL2/L/ffmMuNHfqO9tFZjJYBd1ewO3Lx+IcVfKhqna5nHZYR6XFPH+5R3eeI5t9fx/fvjeC9Jdew5OKZKqFR/RDVKL6vL/f9PJafmyvHsL+a/ff24V6NmjZQbGchVbY6UM2BluqHv1rCqzVZ1KQlkZboepCdQvacPsz5bjTfXao+yMEaDt7Wk9tSIMVGig3TejCtJSM2bSpkPjWPqd1S6Zao+lORSYWmgkOjORRNwZqd5ezMSiHLRooNr5XwWjS6/1XHX9vL2T67N7M1iyXa9JCrYjVrS1gbpJyw6hBfsmiNCYT0P9/A+Wj1/6qGr5YNYFlJBiWJogEzezLz/ZG8/9s4fgtSyuvNYbyp1IONFBtu7sHNv4/j9yAxUHWrdaWsG9+N8XHd+YxkMpSy+aySz841cC5oXbmHCnnI74yxAgZ3YbDeB4PEQCOpBpFNZWwa2ZWRcdnxLsl00crtRB0n2lLg8JNRfDKoM4NsolgBSmw/UMADba1+qpmqfyf6x1u/0a/og3I+aEunP6/i86osqmygcGarF4p54dex/Bo0LqfqOfVwIQ/HW5/RSkwV1oN2WLlHTc82TljAwM4M1O5LWwYKZTjibYXNS0N5KcjKTe10PadfLObFuJwK4ozp+UzXDBTUjL+M5ZcBnRkQV53dMIwNQTu6bSTbVEzbi5awuVByd2E/FgaN0Tc1fKOzBHHV2aAdVSdv6s5NNkp7cSH/++xAng2yyHx+CM/H21YhfdPp+0U1X0TbSZnXx8faG9Aop0MS0cToh1p+iLcpOkLj9t/JOk5eqoPHxnDsyf486an5yqCDK7XZ1O4oZ4dWyy3FSXHUAYq47uyYbMZoGmhpG3DlEFb6uNiVBhpyaHhnBO8oJmfqOROJjzIiP43hJ8UxITqqX56S2Hur2KsOnq3nrE6PPNKPRwrSKbAZrjTQNZmuE7oxYXMZmxWbw9dxWFu4W4ezVedOE6qzI7oyYkY+M7TPeWsPbk2UX1qioSN+E3OZqOR2cReKE+qQRFN0Pi7y73g/UawU1KzezJpXwLz5hczX1ueUPKYkNb6GJQZ+j7/aAfRZREsv+quGsMoamXZBW2Gt5eU0alorEzYsKmJRa/m4NdeyxlqZsCGa84DKnVorEzboC7podis69DfIJmwufHMc7famvvmxZiYsKOtKWbRm1OcW1syEBboSJFozLh/EcmtmwgIluaM14/phrLdmJixYXMTiaM24p5I91syEBTphFOR7Y2tmwgJNvUFOr+tov3UzoaAv44KYUatv62ZCoemdhtG0+hzqrZsJBR08DWLG0gxKrZu50qvpxos3U5NItW4mFPp1ot+lPlpq2lYXs9qamVBZUMiC1ox4pJYjvlfStAu6GmTLcLboMtPIV4/6im5fFfuUi9QIap2MiWP+D96R1vPmsD/fAAAAAElFTkSuQmCC",wr="data:image/png;base64,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",br=class{constructor(t,e){this.time=new ve,e.pathPrefix===void 0&&(e.pathPrefix=""),e.app===void 0&&(e.app={loadAssets:()=>{},initialize:()=>{},update:()=>{},render:()=>{},error:()=>{}}),e.webglConfig===void 0&&(e.webglConfig={alpha:!0}),this.htmlCanvas=t,this.context=new wt(t,e.webglConfig),this.renderer=new Pi(t,this.context),this.gl=this.context.gl,this.assetManager=new Yi(this.context,e.pathPrefix),this.input=new xe(t),e.app.loadAssets(this);let i=()=>{requestAnimationFrame(i),this.time.update(),e.app.update(this,this.time.delta),e.app.render(this)},s=()=>{if(this.assetManager.isLoadingComplete()){this.assetManager.hasErrors()?e.app.error(this,this.assetManager.getErrors()):(e.app.initialize(this),i());return}requestAnimationFrame(s)};requestAnimationFrame(s)}clear(t,e,i,s){this.gl.clearColor(t,e,i,s),this.gl.clear(this.gl.COLOR_BUFFER_BIT)}},vr=class{constructor(t,e){if(this.config=e,this.bg=new O,this.bgFullscreen=new O,this.playTime=0,this.cancelId=0,this.paused=!0,this.speed=1,this.time=new ve,this.stopRequestAnimationFrame=!1,this.disposed=!1,this.viewport={},this.viewportTransitionStart=0,this.eventListeners=[],this.parent=typeof t=="string"?document.getElementById(t):t,!this.parent)throw new Error("SpinePlayer parent not found: "+t);e.showControls===void 0&&(e.showControls=!0);let i=e.showControls?`
<div class="spine-player-controls spine-player-popup-parent spine-player-controls-hidden">
<div class="spine-player-timeline"></div>
<div class="spine-player-buttons">
<button class="spine-player-button spine-player-button-icon-pause"></button>
<div class="spine-player-button-spacer"></div>
<button class="spine-player-button spine-player-button-icon-speed"></button>
<button class="spine-player-button spine-player-button-icon-animations"></button>
<button class="spine-player-button spine-player-button-icon-skins"></button>
<button class="spine-player-button spine-player-button-icon-settings"></button>
<button class="spine-player-button spine-player-button-icon-fullscreen"></button>
<img class="spine-player-button-icon-spine-logo" src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20104%2031.16%22%3E%3Cpath%20d%3D%22M104%2012.68a1.31%201.31%200%200%201-.37%201%201.28%201.28%200%200%201-.85.31H91.57a10.51%2010.51%200%200%200%20.29%202.55%204.92%204.92%200%200%200%201%202%204.27%204.27%200%200%200%201.64%201.26%206.89%206.89%200%200%200%202.6.44%2010.66%2010.66%200%200%200%202.17-.2%2012.81%2012.81%200%200%200%201.64-.44q.69-.25%201.14-.44a1.87%201.87%200%200%201%20.68-.2.44.44%200%200%201%20.27.04.43.43%200%200%201%20.16.2%201.38%201.38%200%200%201%20.09.37%204.89%204.89%200%200%201%200%20.58%204.14%204.14%200%200%201%200%20.43v.32a.83.83%200%200%201-.09.26%201.1%201.1%200%200%201-.17.22%202.77%202.77%200%200%201-.61.34%208.94%208.94%200%200%201-1.32.46%2018.54%2018.54%200%200%201-1.88.41%2013.78%2013.78%200%200%201-2.28.18%2010.55%2010.55%200%200%201-3.68-.59%206.82%206.82%200%200%201-2.66-1.74%207.44%207.44%200%200%201-1.63-2.89%2013.48%2013.48%200%200%201-.55-4%2012.76%2012.76%200%200%201%20.57-3.94%208.35%208.35%200%200%201%201.64-3%207.15%207.15%200%200%201%202.58-1.87%208.47%208.47%200%200%201%203.39-.65%208.19%208.19%200%200%201%203.41.64%206.46%206.46%200%200%201%202.32%201.73%207%207%200%200%201%201.3%202.54%2011.17%2011.17%200%200%201%20.43%203.13zm-3.14-.93a5.69%205.69%200%200%200-1.09-3.86%204.17%204.17%200%200%200-3.42-1.4%204.52%204.52%200%200%200-2%20.44%204.41%204.41%200%200%200-1.47%201.15A5.29%205.29%200%200%200%2092%209.75a7%207%200%200%200-.36%202zM80.68%2021.94a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.31%206.31%200%200%201-.76%200%206.5%206.5%200%200%201-.78%200%201.74%201.74%200%200%201-.47-.11.59.59%200%200%201-.25-.18.42.42%200%200%201-.08-.26V12a9.8%209.8%200%200%200-.23-2.35%204.86%204.86%200%200%200-.66-1.53%202.88%202.88%200%200%200-1.13-1%203.57%203.57%200%200%200-1.6-.34%204%204%200%200%200-2.35.83A12.71%2012.71%200%200%200%2069.11%2010v11.9a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.51%206.51%200%200%201-.78%200%206.31%206.31%200%200%201-.76%200%201.88%201.88%200%200%201-.48-.11.52.52%200%200%201-.25-.18.46.46%200%200%201-.07-.26v-17a.53.53%200%200%201%20.03-.21.5.5%200%200%201%20.23-.19%201.28%201.28%200%200%201%20.44-.11%208.53%208.53%200%200%201%201.39%200%201.12%201.12%200%200%201%20.43.11.6.6%200%200%201%20.22.19.47.47%200%200%201%20.07.26V7.2a10.46%2010.46%200%200%201%202.87-2.36%206.17%206.17%200%200%201%202.88-.75%206.41%206.41%200%200%201%202.87.58%205.16%205.16%200%200%201%201.88%201.54%206.15%206.15%200%200%201%201%202.26%2013.46%2013.46%200%200%201%20.31%203.11z%22%20fill%3D%22%23fff%22%2F%3E%3Cpath%20d%3D%22M43.35%202.86c.09%202.6%201.89%204%205.48%204.61%203%20.48%205.79.24%206.69-2.37%201.75-5.09-2.4-3.82-6-4.39s-6.31-2.03-6.17%202.15zm1.08%2010.69c.33%201.94%202.14%203.06%204.91%203s4.84-1.16%205.13-3.25c.53-3.88-2.53-2.38-5.3-2.3s-5.4-1.26-4.74%202.55zM48%2022.44c.55%201.45%202.06%202.06%204.1%201.63s3.45-1.11%203.33-2.76c-.21-3.06-2.22-2.1-4.26-1.66S47%2019.6%2048%2022.44zm1.78%206.78c.16%201.22%201.22%202%202.88%201.93s2.92-.67%203.13-2c.4-2.43-1.46-1.53-3.12-1.51s-3.17-.82-2.89%201.58z%22%20fill%3D%22%23ff4000%22%2F%3E%3Cpath%20d%3D%22M35.28%2013.16a15.33%2015.33%200%200%201-.48%204%208.75%208.75%200%200%201-1.42%203%206.35%206.35%200%200%201-2.32%201.91%207.14%207.14%200%200%201-3.16.67%206.1%206.1%200%200%201-1.4-.15%205.34%205.34%200%200%201-1.26-.47%207.29%207.29%200%200%201-1.24-.81q-.61-.49-1.29-1.15v8.51a.47.47%200%200%201-.08.26.56.56%200%200%201-.25.19%201.74%201.74%200%200%201-.47.11%206.47%206.47%200%200%201-.78%200%206.26%206.26%200%200%201-.76%200%201.89%201.89%200%200%201-.48-.11.49.49%200%200%201-.25-.19.51.51%200%200%201-.07-.26V4.91a.57.57%200%200%201%20.06-.27.46.46%200%200%201%20.23-.18%201.47%201.47%200%200%201%20.44-.1%207.41%207.41%200%200%201%201.3%200%201.45%201.45%200%200%201%20.43.1.52.52%200%200%201%20.24.18.51.51%200%200%201%20.07.27V7.2a18.06%2018.06%200%200%201%201.49-1.38%209%209%200%200%201%201.45-1%206.82%206.82%200%200%201%201.49-.59%207.09%207.09%200%200%201%204.78.52%206%206%200%200%201%202.13%202%208.79%208.79%200%200%201%201.2%202.9%2015.72%2015.72%200%200%201%20.4%203.51zm-3.28.36a15.64%2015.64%200%200%200-.2-2.53%207.32%207.32%200%200%200-.69-2.17%204.06%204.06%200%200%200-1.3-1.51%203.49%203.49%200%200%200-2-.57%204.1%204.1%200%200%200-1.2.18%204.92%204.92%200%200%200-1.2.57%208.54%208.54%200%200%200-1.28%201A15.77%2015.77%200%200%200%2022.76%2010v6.77a13.53%2013.53%200%200%200%202.46%202.4%204.12%204.12%200%200%200%202.44.83%203.56%203.56%200%200%200%202-.57A4.28%204.28%200%200%200%2031%2018a7.58%207.58%200%200%200%20.77-2.12%2011.43%2011.43%200%200%200%20.23-2.36zM12%2017.3a5.39%205.39%200%200%201-.48%202.33%204.73%204.73%200%200%201-1.37%201.72%206.19%206.19%200%200%201-2.12%201.06%209.62%209.62%200%200%201-2.71.36%2010.38%2010.38%200%200%201-3.21-.5A7.63%207.63%200%200%201%201%2021.82a3.25%203.25%200%200%201-.66-.43%201.09%201.09%200%200%201-.3-.53%203.59%203.59%200%200%201-.04-.93%204.06%204.06%200%200%201%200-.61%202%202%200%200%201%20.09-.4.42.42%200%200%201%20.16-.22.43.43%200%200%201%20.24-.07%201.35%201.35%200%200%201%20.61.26q.41.26%201%20.56a9.22%209.22%200%200%200%201.41.55%206.25%206.25%200%200%200%201.87.26%205.62%205.62%200%200%200%201.44-.17%203.48%203.48%200%200%200%201.12-.5%202.23%202.23%200%200%200%20.73-.84%202.68%202.68%200%200%200%20.26-1.21%202%202%200%200%200-.37-1.21%203.55%203.55%200%200%200-1-.87%208.09%208.09%200%200%200-1.36-.66l-1.56-.61a16%2016%200%200%201-1.57-.73%206%206%200%200%201-1.37-1%204.52%204.52%200%200%201-1-1.4%204.69%204.69%200%200%201-.37-2%204.88%204.88%200%200%201%20.39-1.87%204.46%204.46%200%200%201%201.16-1.61%205.83%205.83%200%200%201%201.94-1.11A8.06%208.06%200%200%201%206.53%204a8.28%208.28%200%200%201%201.36.11%209.36%209.36%200%200%201%201.23.28%205.92%205.92%200%200%201%20.94.37%204.09%204.09%200%200%201%20.59.35%201%201%200%200%201%20.26.26.83.83%200%200%201%20.09.26%201.32%201.32%200%200%200%20.06.35%203.87%203.87%200%200%201%200%20.51%204.76%204.76%200%200%201%200%20.56%201.39%201.39%200%200%201-.09.39.5.5%200%200%201-.16.22.35.35%200%200%201-.21.07%201%201%200%200%201-.49-.21%207%207%200%200%200-.83-.44%209.26%209.26%200%200%200-1.2-.44%205.49%205.49%200%200%200-1.58-.16%204.93%204.93%200%200%200-1.4.18%202.69%202.69%200%200%200-1%20.51%202.16%202.16%200%200%200-.59.83%202.43%202.43%200%200%200-.2%201%202%202%200%200%200%20.38%201.24%203.6%203.6%200%200%200%201%20.88%208.25%208.25%200%200%200%201.38.68l1.58.62q.8.32%201.59.72a6%206%200%200%201%201.39%201%204.37%204.37%200%200%201%201%201.36%204.46%204.46%200%200%201%20.37%201.8z%22%20fill%3D%22%23fff%22%2F%3E%3C%2Fsvg%3E">
</div></div>`:"";this.parent.appendChild(this.dom=Gt(`<div class="spine-player" style="position:relative;height:100%"><canvas class="spine-player-canvas" style="display:block;width:100%;height:100%"></canvas>${i}</div>`));try{this.validateConfig(e)}catch(s){this.showError(s.message,s)}this.initialize(),this.addEventListener(window,"resize",()=>this.drawFrame(!1)),requestAnimationFrame(()=>this.drawFrame())}dispose(){this.sceneRenderer.dispose(),this.loadingScreen&&this.loadingScreen.dispose(),this.assetManager.dispose();for(var t=0;t<this.eventListeners.length;t++){var e=this.eventListeners[t];e.target.removeEventListener(e.event,e.func)}this.parent.removeChild(this.dom),this.disposed=!0}addEventListener(t,e,i){this.eventListeners.push({target:t,event:e,func:i}),t.addEventListener(e,i)}validateConfig(t){if(!t)throw new Error("A configuration object must be passed to to new SpinePlayer().");if(t.skelUrl&&(t.binaryUrl=t.skelUrl),!t.jsonUrl&&!t.binaryUrl)throw new Error("A URL must be specified for the skeleton JSON or binary file.");if(!t.atlasUrl)throw new Error("A URL must be specified for the atlas file.");if(t.backgroundColor||(t.backgroundColor=t.alpha?"00000000":"000000"),t.fullScreenBackgroundColor||(t.fullScreenBackgroundColor=t.backgroundColor),t.backgroundImage&&!t.backgroundImage.url&&(t.backgroundImage=null),t.premultipliedAlpha===void 0&&(t.premultipliedAlpha=!0),t.preserveDrawingBuffer===void 0&&(t.preserveDrawingBuffer=!1),t.mipmaps===void 0&&(t.mipmaps=!0),t.debug||(t.debug={}),t.animations&&t.animation&&t.animations.indexOf(t.animation)<0)throw new Error("Animation '"+t.animation+"' is not in the config animation list: "+Di(t.animations));if(t.skins&&t.skin&&t.skins.indexOf(t.skin)<0)throw new Error("Default skin '"+t.skin+"' is not in the config skins list: "+Di(t.skins));t.viewport||(t.viewport={}),t.viewport.animations||(t.viewport.animations={}),t.viewport.debugRender===void 0&&(t.viewport.debugRender=!1),t.viewport.transitionTime===void 0&&(t.viewport.transitionTime=.25),t.controlBones||(t.controlBones=[]),t.showLoading===void 0&&(t.showLoading=!0),t.defaultMix===void 0&&(t.defaultMix=.25)}initialize(){let t=this.config,e=this.dom;if(!t.alpha){let i=t.backgroundColor;this.dom.style.backgroundColor=(i.charAt(0)=="#"?i:"#"+i).substr(0,7)}try{this.canvas=qt(e,"spine-player-canvas"),this.context=new wt(this.canvas,{alpha:t.alpha,preserveDrawingBuffer:t.preserveDrawingBuffer}),this.sceneRenderer=new Pi(this.canvas,this.context,!0),t.showLoading&&(this.loadingScreen=new Cs(this.sceneRenderer))}catch(i){this.showError(`Sorry, your browser does not support 
Please use the latest version of Firefox, Chrome, Edge, or Safari.`,i)}if(this.assetManager=new Yi(this.context,"",t.downloader),t.rawDataURIs)for(let i in t.rawDataURIs)this.assetManager.setRawDataURI(i,t.rawDataURIs[i]);if(t.jsonUrl?this.assetManager.loadJson(t.jsonUrl):this.assetManager.loadBinary(t.binaryUrl),this.assetManager.loadTextureAtlas(t.atlasUrl),t.backgroundImage&&this.assetManager.loadTexture(t.backgroundImage.url),this.bg.setFromString(t.backgroundColor),this.bgFullscreen.setFromString(t.fullScreenBackgroundColor),t.showControls){this.playerControls=e.children[1];let i=this.playerControls.children,s=i[0],a=i[1].children;this.playButton=a[0];let d=a[2];this.animationButton=a[3],this.skinButton=a[4];let n=a[5],r=a[6],h=a[7];this.timelineSlider=new Ts,s.appendChild(this.timelineSlider.create()),this.timelineSlider.change=f=>{this.pause();let x=this.animationState.getCurrent(0).animation.duration*f;this.animationState.update(x-this.playTime),this.animationState.apply(this.skeleton),this.skeleton.updateWorldTransform(),this.playTime=x},this.playButton.onclick=()=>this.paused?this.play():this.pause(),d.onclick=()=>this.showSpeedDialog(d),this.animationButton.onclick=()=>this.showAnimationsDialog(this.animationButton),this.skinButton.onclick=()=>this.showSkinsDialog(this.skinButton),n.onclick=()=>this.showSettingsDialog(n);let o=this.canvas.clientWidth,l=this.canvas.clientHeight,c=this.canvas.style.width,u=this.canvas.style.height,m=!1;r.onclick=()=>{let f=()=>{m=!m,m||(this.canvas.style.width=o+"px",this.canvas.style.height=l+"px",this.drawFrame(!1),requestAnimationFrame(()=>{this.canvas.style.width=c,this.canvas.style.height=u}))},g=e;g.onfullscreenchange=f,g.onwebkitfullscreenchange=f;let x=document;x.fullscreenElement||x.webkitFullscreenElement||x.mozFullScreenElement||x.msFullscreenElement?x.exitFullscreen?x.exitFullscreen():x.mozCancelFullScreen?x.mozCancelFullScreen():x.webkitExitFullscreen?x.webkitExitFullscreen():x.msExitFullscreen&&x.msExitFullscreen():(o=this.canvas.clientWidth,l=this.canvas.clientHeight,c=this.canvas.style.width,u=this.canvas.style.height,g.requestFullscreen?g.requestFullscreen():g.webkitRequestFullScreen?g.webkitRequestFullScreen():g.mozRequestFullScreen?g.mozRequestFullScreen():g.msRequestFullscreen&&g.msRequestFullscreen())},h.onclick=()=>window.open("http://esotericsoftware.com")}return e}loadSkeleton(){if(this.error)return;this.assetManager.hasErrors()&&this.showError(`Error: Assets could not be loaded.
`+Di(this.assetManager.getErrors()));let t=this.config,e=this.assetManager.require(t.atlasUrl),i=this.context.gl,s=i.getExtension("EXT_texture_filter_anisotropic"),a=i.getParameter(i.VERSION).indexOf("WebGL 1.0")!=-1;for(let l of e.pages){let c=l.minFilter;var d=t.mipmaps,n=F.isPowerOfTwo(l.width)&&F.isPowerOfTwo(l.height);a&&!n&&(d=!1),d&&(s?(i.texParameterf(i.TEXTURE_2D,s.TEXTURE_MAX_ANISOTROPY_EXT,8),c=pt.MipMapLinearLinear):c=pt.Linear,l.texture.setFilters(c,pt.Nearest)),c!=pt.Nearest&&c!=pt.Linear&&l.texture.update(!0)}let r;if(t.jsonUrl)try{let l=this.assetManager.remove(t.jsonUrl);if(!l)throw new Error("Empty JSON data.");if(t.jsonField&&(l=l[t.jsonField],!l))throw new Error("JSON field does not exist: "+t.jsonField);r=new fs(new pi(e)).readSkeletonData(l)}catch(l){this.showError(`Error: Could not load skeleton JSON.
${l.message}`,l)}else{let l=this.assetManager.remove(t.binaryUrl),c=new ds(new pi(e));try{r=c.readSkeletonData(l)}catch(u){this.showError(`Error: Could not load skeleton binary.
${u.message}`,u)}}this.skeleton=new hs(r);let h=new $i(r);h.defaultMix=t.defaultMix,this.animationState=new Se(h),t.controlBones.forEach(l=>{r.findBone(l)||this.showError(`Error: Control bone does not exist in skeleton: ${l}`)}),!t.skin&&r.skins.length&&(t.skin=r.skins[0].name),t.skins&&t.skin.length&&t.skins.forEach(l=>{this.skeleton.data.findSkin(l)||this.showError(`Error: Skin in config list does not exist in skeleton: ${l}`)}),t.skin&&(this.skeleton.data.findSkin(t.skin)||this.showError(`Error: Skin does not exist in skeleton: ${t.skin}`),this.skeleton.setSkinByName(t.skin),this.skeleton.setSlotsToSetupPose()),Object.getOwnPropertyNames(t.viewport.animations).forEach(l=>{r.findAnimation(l)||this.showError(`Error: Animation for which a viewport was specified does not exist in skeleton: ${l}`)}),t.animations&&t.animations.length&&(t.animations.forEach(l=>{this.skeleton.data.findAnimation(l)||this.showError(`Error: Animation in config list does not exist in skeleton: ${l}`)}),t.animation||(t.animation=t.animations[0])),t.animation&&!r.findAnimation(t.animation)&&this.showError(`Error: Animation does not exist in skeleton: ${t.animation}`),this.setupInput(),t.showControls&&((r.skins.length==1||t.skins&&t.skins.length==1)&&this.skinButton.classList.add("spine-player-hidden"),(r.animations.length==1||t.animations&&t.animations.length==1)&&this.animationButton.classList.add("spine-player-hidden")),t.success&&t.success(this);let o=this.animationState.getCurrent(0);o?this.currentViewport||(this.setViewport(o.animation),this.play()):t.animation?(o=this.setAnimation(t.animation),this.play()):(o=this.animationState.setEmptyAnimation(0),o.trackEnd=1e8,this.setViewport(o.animation),this.pause())}setupInput(){let t=this.config,e=t.controlBones;if(!e.length&&!t.showControls)return;let i=this.selectedBones=new Array(e.length),s=this.canvas,a=null,d=new Yt,n=new Mt,r=new Mt,h=new Yt,o=this.skeleton,l=this.sceneRenderer,c=function(u,m){r.set(u,s.clientHeight-m,0),d.x=d.y=0;let f=24,g=0,x;for(let v=0;v<e.length;v++){i[v]=null;let p=o.findBone(e[v]),b=l.camera.worldToScreen(n.set(p.worldX,p.worldY,0),s.clientWidth,s.clientHeight).distance(r);b<f&&(f=b,x=p,g=v,d.x=n.x-r.x,d.y=n.y-r.y)}return x&&(i[g]=x),x};if(new xe(s).addListener({down:(u,m)=>{a=c(u,m)},up:()=>{a?a=null:t.showControls&&(this.paused?this.play():this.pause())},dragged:(u,m)=>{a&&(u=F.clamp(u+d.x,0,s.clientWidth),m=F.clamp(m-d.y,0,s.clientHeight),l.camera.screenToWorld(n.set(u,m,0),s.clientWidth,s.clientHeight),a.parent?(a.parent.worldToLocal(h.set(n.x-o.x,n.y-o.y)),a.x=h.x,a.y=h.y):(a.x=n.x-o.x,a.y=n.y-o.y))},moved:(u,m)=>c(u,m)}),t.showControls){this.addEventListener(document,"mousemove",x=>{x instanceof MouseEvent&&g(x.clientX,x.clientY)}),this.addEventListener(document,"touchmove",x=>{if(x instanceof TouchEvent){let v=x.changedTouches;if(v.length){let p=v[0];g(p.clientX,p.clientY)}}});let u=(x,v,p)=>{let b=x-p.left,w=v-p.top;return b>=0&&b<=p.width&&w>=0&&w<=p.height},m=!0,f=!1,g=(x,v)=>{let p=qt(this.dom,"spine-player-popup");m=u(x,v,this.playerControls.getBoundingClientRect()),f=u(x,v,s.getBoundingClientRect()),clearTimeout(this.cancelId),!p&&!m&&!f&&!this.paused?this.playerControls.classList.add("spine-player-controls-hidden"):this.playerControls.classList.remove("spine-player-controls-hidden"),!m&&!p&&!this.paused&&(this.cancelId=setTimeout(()=>{this.paused||this.playerControls.classList.add("spine-player-controls-hidden")},1e3))}}}play(){this.paused=!1;let t=this.config;t.showControls&&(this.cancelId=setTimeout(()=>{this.paused||this.playerControls.classList.add("spine-player-controls-hidden")},1e3),this.playButton.classList.remove("spine-player-button-icon-play"),this.playButton.classList.add("spine-player-button-icon-pause"),t.animation||(t.animations&&t.animations.length?t.animation=t.animations[0]:this.skeleton.data.animations.length&&(t.animation=this.skeleton.data.animations[0].name),t.animation&&this.setAnimation(t.animation)))}pause(){this.paused=!0,this.config.showControls&&(this.playerControls.classList.remove("spine-player-controls-hidden"),clearTimeout(this.cancelId),this.playButton.classList.remove("spine-player-button-icon-pause"),this.playButton.classList.add("spine-player-button-icon-play"))}setAnimation(t,e=!0){return t=this.setViewport(t),this.animationState.setAnimationWith(0,t,e)}addAnimation(t,e=!0,i=0){return t=this.setViewport(t),this.animationState.addAnimationWith(0,t,e,i)}setViewport(t){typeof t=="string"&&(t=this.skeleton.data.findAnimation(t)),this.previousViewport=this.currentViewport;let e=this.config.viewport,i=this.currentViewport={padLeft:e.padLeft!==void 0?e.padLeft:"10%",padRight:e.padRight!==void 0?e.padRight:"10%",padTop:e.padTop!==void 0?e.padTop:"10%",padBottom:e.padBottom!==void 0?e.padBottom:"10%"};e.x!==void 0&&e.y!==void 0&&e.width&&e.height?(i.x=e.x,i.y=e.y,i.width=e.width,i.height=e.height):this.calculateAnimationViewport(t,i);let s=this.config.viewport.animations[t.name];return s&&(s.x!==void 0&&s.y!==void 0&&s.width&&s.height&&(i.x=s.x,i.y=s.y,i.width=s.width,i.height=s.height),s.padLeft!==void 0&&(i.padLeft=s.padLeft),s.padRight!==void 0&&(i.padRight=s.padRight),s.padTop!==void 0&&(i.padTop=s.padTop),s.padBottom!==void 0&&(i.padBottom=s.padBottom)),i.padLeft=this.percentageToWorldUnit(i.width,i.padLeft),i.padRight=this.percentageToWorldUnit(i.width,i.padRight),i.padBottom=this.percentageToWorldUnit(i.height,i.padBottom),i.padTop=this.percentageToWorldUnit(i.height,i.padTop),this.viewportTransitionStart=performance.now(),t}percentageToWorldUnit(t,e){return typeof e=="string"?t*parseFloat(e.substr(0,e.length-1))/100:e}calculateAnimationViewport(t,e){this.skeleton.setToSetupPose();let i=100,s=t.duration?t.duration/i:0,a=0,d=1e8,n=-1e8,r=1e8,h=-1e8,o=new Yt,l=new Yt;for(let c=0;c<i;c++,a+=s)t.apply(this.skeleton,a,a,!1,null,1,ft.setup,Vt.mixIn),this.skeleton.updateWorldTransform(),this.skeleton.getBounds(o,l),!isNaN(o.x)&&!isNaN(o.y)&&!isNaN(l.x)&&!isNaN(l.y)?(d=Math.min(o.x,d),n=Math.max(o.x+l.x,n),r=Math.min(o.y,r),h=Math.max(o.y+l.y,h)):this.showError("Animation bounds are invalid: "+t.name);e.x=d,e.y=r,e.width=n-d,e.height=h-r}drawFrame(t=!0){try{if(this.error||this.disposed)return;t&&!this.stopRequestAnimationFrame&&requestAnimationFrame(()=>this.drawFrame());let e=document,s=e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement?this.bgFullscreen:this.bg;this.time.update();let a=this.time.delta,d=this.assetManager.isLoadingComplete();!this.skeleton&&d&&this.loadSkeleton();let n=this.skeleton,r=this.config;if(n){let h=this.sceneRenderer;h.resize(ie.Expand);let o=this.paused?0:a*this.speed;if(r.frame&&r.frame(this,o),!this.paused&&(this.animationState.update(o),this.animationState.apply(n),n.updateWorldTransform(),r.showControls)){this.playTime+=o;let f=this.animationState.getCurrent(0);if(f){let g=f.animation.duration;for(;this.playTime>=g&&g!=0;)this.playTime-=g;this.playTime=Math.max(0,Math.min(this.playTime,g)),this.timelineSlider.setValue(this.playTime/g)}}let l=this.viewport;if(l.x=this.currentViewport.x-this.currentViewport.padLeft,l.y=this.currentViewport.y-this.currentViewport.padBottom,l.width=this.currentViewport.width+this.currentViewport.padLeft+this.currentViewport.padRight,l.height=this.currentViewport.height+this.currentViewport.padBottom+this.currentViewport.padTop,this.previousViewport){let f=(performance.now()-this.viewportTransitionStart)/1e3/r.viewport.transitionTime;if(f<1){let g=this.previousViewport.x-this.previousViewport.padLeft,x=this.previousViewport.y-this.previousViewport.padBottom,v=this.previousViewport.width+this.previousViewport.padLeft+this.previousViewport.padRight,p=this.previousViewport.height+this.previousViewport.padBottom+this.previousViewport.padTop;l.x=g+(l.x-g)*f,l.y=x+(l.y-x)*f,l.width=v+(l.width-v)*f,l.height=p+(l.height-p)*f}}h.camera.zoom=this.canvas.height/this.canvas.width>l.height/l.width?l.width/this.canvas.width:l.height/this.canvas.height,h.camera.position.x=l.x+l.width/2,h.camera.position.y=l.y+l.height/2;let c=this.context.gl;c.clearColor(s.r,s.g,s.b,s.a),c.clear(c.COLOR_BUFFER_BIT),r.update&&r.update(this,o),h.begin();let u=r.backgroundImage;if(u){let f=this.assetManager.require(u.url);u.x!==void 0&&u.y!==void 0&&u.width&&u.height?h.drawTexture(f,u.x,u.y,u.width,u.height):h.drawTexture(f,l.x,l.y,l.width,l.height)}h.drawSkeleton(n,r.premultipliedAlpha),((h.skeletonDebugRenderer.drawBones=r.debug.bones)||(h.skeletonDebugRenderer.drawBoundingBoxes=r.debug.bounds)||(h.skeletonDebugRenderer.drawClipping=r.debug.clipping)||(h.skeletonDebugRenderer.drawMeshHull=r.debug.hulls)||(h.skeletonDebugRenderer.drawPaths=r.debug.paths)||(h.skeletonDebugRenderer.drawRegionAttachments=r.debug.regions)||(h.skeletonDebugRenderer.drawMeshTriangles=r.debug.meshes))&&h.drawSkeletonDebug(n,r.premultipliedAlpha);let m=r.controlBones;if(m.length){let f=this.selectedBones;c.lineWidth(2);for(let g=0;g<m.length;g++){let x=n.findBone(m[g]);if(!x)continue;let v=f[g]?Ar:Cr,p=f[g]?Sr:Tr;h.circle(!0,n.x+x.worldX,n.y+x.worldY,20,v),h.circle(!1,n.x+x.worldX,n.y+x.worldY,20,p)}}r.viewport.debugRender&&(c.lineWidth(1),h.rect(!1,this.currentViewport.x,this.currentViewport.y,this.currentViewport.width,this.currentViewport.height,O.GREEN),h.rect(!1,l.x,l.y,l.width,l.height,O.RED)),h.end(),r.draw&&r.draw(this,o)}r.showLoading&&(this.loadingScreen.backgroundColor.setFromColor(s),this.loadingScreen.draw(d)),d&&r.loading&&r.loading(this,a)}catch(e){this.showError(`Error: Unable to render skeleton.
${e.message}`,e)}}stopRendering(){this.stopRequestAnimationFrame=!0}hidePopup(t){return this.popup&&this.popup.hide(t)}showSpeedDialog(t){let e="speed";if(this.hidePopup(e))return;let i=new De(e,t,this,this.playerControls,`
<div class="spine-player-popup-title">Speed</div>
<hr>
<div class="spine-player-row" style="align-items:center;padding:8px">
<div class="spine-player-column">
	<div class="spine-player-speed-slider" style="margin-bottom:4px"></div>
	<div class="spine-player-row" style="justify-content:space-between"><div>0.1x</div><div>1x</div><div>2x</div></div>
</div>
</div>`),s=new Ts(2,.1,!0);qt(i.dom,"spine-player-speed-slider").appendChild(s.create()),s.setValue(this.speed/2),s.change=a=>this.speed=a*2,i.show()}showAnimationsDialog(t){let e="animations";if(this.hidePopup(e)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new De(e,t,this,this.playerControls,'<div class="spine-player-popup-title">Animations</div><hr><ul class="spine-player-list"></ul>'),s=qt(i.dom,"spine-player-list");this.skeleton.data.animations.forEach(a=>{if(this.config.animations&&this.config.animations.indexOf(a.name)<0)return;let d=Gt('<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>');a.name==this.config.animation&&d.classList.add("selected"),qt(d,"selectable-text").innerText=a.name,s.appendChild(d),d.onclick=()=>{ks(s.children,"selected"),d.classList.add("selected"),this.config.animation=a.name,this.playTime=0,this.setAnimation(a.name),this.play()}}),i.show()}showSkinsDialog(t){let e="skins";if(this.hidePopup(e)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new De(e,t,this,this.playerControls,'<div class="spine-player-popup-title">Skins</div><hr><ul class="spine-player-list"></ul>'),s=qt(i.dom,"spine-player-list");this.skeleton.data.skins.forEach(a=>{if(this.config.skins&&this.config.skins.indexOf(a.name)<0)return;let d=Gt('<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>');a.name==this.config.skin&&d.classList.add("selected"),qt(d,"selectable-text").innerText=a.name,s.appendChild(d),d.onclick=()=>{ks(s.children,"selected"),d.classList.add("selected"),this.config.skin=a.name,this.skeleton.setSkinByName(this.config.skin),this.skeleton.setSlotsToSetupPose()}}),i.show()}showSettingsDialog(t){let e="settings";if(this.hidePopup(e)||!this.skeleton||!this.skeleton.data.animations.length)return;let i=new De(e,t,this,this.playerControls,'<div class="spine-player-popup-title">Debug</div><hr><ul class="spine-player-list"></li>'),s=qt(i.dom,"spine-player-list"),a=(d,n)=>{let r=Gt('<li class="spine-player-list-item"></li>'),h=new yr(d);r.appendChild(h.create());let o=this.config.debug;h.setEnabled(o[n]),h.change=l=>o[n]=l,s.appendChild(r)};a("Bones","bones"),a("Regions","regions"),a("Meshes","meshes"),a("Bounds","bounds"),a("Paths","paths"),a("Clipping","clipping"),a("Points","points"),a("Hulls","hulls"),i.show()}showError(t,e=null){if(this.error){if(e)throw e}else throw this.error=!0,this.dom.appendChild(Gt('<div class="spine-player-error" style="background:#000;color:#fff;position:absolute;top:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;overflow:auto;z-index:999">'+t.replace(`
`,"<br><br>")+"</div>")),this.config.error&&this.config.error(this,t),e||new Error(t)}},De=class{constructor(t,e,i,s,a){this.id=t,this.button=e,this.player=i,this.dom=Gt('<div class="spine-player-popup spine-player-hidden"></div>'),this.dom.innerHTML=a,s.appendChild(this.dom),this.className="spine-player-button-icon-"+t+"-selected"}dispose(){}hide(t){if(this.dom.remove(),this.button.classList.remove(this.className),this.id==t)return this.player.popup=null,!0}show(){this.player.popup=this,this.button.classList.add(this.className),this.dom.classList.remove("spine-player-hidden");let t=!1,e=()=>{t||requestAnimationFrame(e);let a=this.player.dom,d=Math.abs(a.getBoundingClientRect().bottom-a.getBoundingClientRect().bottom),n=Math.abs(a.getBoundingClientRect().right-a.getBoundingClientRect().right);this.dom.style.maxHeight=a.clientHeight-d-n+"px"};requestAnimationFrame(e);let i=!0,s=a=>{if(i||this.player.popup!=this){i=!1;return}this.dom.contains(a.target)||(this.dom.remove(),window.removeEventListener("click",s),this.button.classList.remove(this.className),this.player.popup=null,t=!0)};this.player.addEventListener(window,"click",s)}},yr=class{constructor(t){this.text=t,this.enabled=!1}create(){return this.switch=Gt(`
<div class="spine-player-switch">
	<span class="spine-player-switch-text">${this.text}</span>
	<div class="spine-player-switch-knob-area">
		<div class="spine-player-switch-knob"></div>
	</div>
</div>`),this.switch.addEventListener("click",()=>{this.setEnabled(!this.enabled),this.change&&this.change(this.enabled)}),this.switch}setEnabled(t){t?this.switch.classList.add("active"):this.switch.classList.remove("active"),this.enabled=t}isEnabled(){return this.enabled}},Ts=class{constructor(t=0,e=.1,i=!1){this.snaps=t,this.snapPercentage=e,this.big=i}create(){this.slider=Gt(`
<div class="spine-player-slider ${this.big?"big":""}">
	<div class="spine-player-slider-value"></div>
	<!--<div class="spine-player-slider-knob"></div>-->
</div>`),this.value=qt(this.slider,"spine-player-slider-value"),this.setValue(0);let t=!1;return new xe(this.slider).addListener({down:(e,i)=>{t=!0,this.value.classList.add("hovering")},up:(e,i)=>{t=!1,this.change&&this.change(this.setValue(e/this.slider.clientWidth)),this.value.classList.remove("hovering")},moved:(e,i)=>{t&&this.change&&this.change(this.setValue(e/this.slider.clientWidth))},dragged:(e,i)=>{this.change&&this.change(this.setValue(e/this.slider.clientWidth))}}),this.slider}setValue(t){if(t=Math.max(0,Math.min(1,t)),this.snaps){let e=1/this.snaps,i=t%e;i<e*this.snapPercentage?t=t-i:i>e-e*this.snapPercentage&&(t=t-i+e),t=Math.max(0,Math.min(1,t))}return this.value.style.width=""+t*100+"%",t}};function qt(t,e){return t.getElementsByClassName(e)[0]}function Gt(t){let e=document.createElement("div");return e.innerHTML=t,e.children[0]}function ks(t,e){for(let i=0;i<t.length;i++)t[i].classList.remove(e)}function Di(t){return JSON.stringify(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&#34;").replace(/'/g,"&#39;")}var Ar=new O(.478,0,0,.25),Sr=new O(1,1,1,1),Cr=new O(.478,0,0,.5),Tr=new O(1,0,0,.8),Ms=class{constructor(t){this.prefix=`<html>
<head>
<style>
body { margin: 0px; }
</style>
</head>
<body>`.trim(),this.postfix="</body>",this.timerId=0,this.render(t)}render(t){let e=`
				<div class="spine-player-editor-container">
					<div class="spine-player-editor-code"></div>
					<iframe class="spine-player-editor-player"></iframe>
				</div>
			`;t.innerHTML=e;let i=t.getElementsByClassName("spine-player-editor-code")[0];this.player=t.getElementsByClassName("spine-player-editor-player")[0],requestAnimationFrame(()=>{this.code=CodeMirror(i,{lineNumbers:!0,tabSize:3,indentUnit:3,indentWithTabs:!0,scrollBarStyle:"native",mode:"htmlmixed",theme:"monokai"}),this.code.on("change",()=>{this.startPlayer()}),this.setCode(Ms.DEFAULT_CODE)})}setPreAndPostfix(t,e){this.prefix=t,this.postfix=e,this.startPlayer()}setCode(t){this.code.setValue(t),this.startPlayer()}startPlayer(){clearTimeout(this.timerId),this.timerId=setTimeout(()=>{let t=this.code.getDoc().getValue();t=this.prefix+t+this.postfix,t=window.btoa(t),this.player.src="",this.player.src="data:text/html;base64,"+t},500)}},Es=Ms;return Es.DEFAULT_CODE=`
<script src="https://esotericsoftware.com/files/spine-player/4.0/spine-player.js"><\/script>
<link rel="stylesheet" href="https://esotericsoftware.com/files/spine-player/4.0/spine-player.css">

<div id="player-container" style="width: 100%; height: 100vh;"></div>

<script>
new spine.SpinePlayer("player-container", {
	jsonUrl: "https://esotericsoftware.com/files/examples/4.0/spineboy/export/spineboy-pro.json",
	atlasUrl: "https://esotericsoftware.com/files/examples/4.0/spineboy/export/spineboy-pma.atlas"
});
<\/script>
		`.trim(),zi})();
