@use "sass:string";
@use "sass:list";

@function px2rem($v) {
  @if unitless($v) {
    @return calc($v / 16) * 1rem;
  } @else {
    @return calc($v / 16px) * 1rem;
  }
}

@function px2vh($v) {
  @if unitless($v) {
    @return calc($v / 1080) * 100vh;
  } @else {
    @return calc($v / 1080px) * 100vh;
  }
}

@mixin shadow0 {
  box-shadow: 0px px2rem(2px) px2rem(4px) 0px rgba(0, 0, 0, 0.2);
}

@mixin bgimg($url) {
  background-image: url("#{$url}");
  $file: string.split($url, ".");
  $filename: list.nth($file, 1);

  @if $filename {
    .webp & {
      background-image: url("#{$filename}.webp");
    }

    .avif & {
      background-image: url("#{$filename}.avif");
    }
  }
}

@mixin social($scale) {
  .social {
    width: px2rem(484px * $scale);
    height: px2rem(40px * $scale);
    margin-top: px2rem(45px);
    position: relative;
    display: flex;

    a {
      display: block;
      width: px2rem(42px * $scale);
      height: px2rem(40px * $scale);
      flex: none;
      background: url(@/shiftyspad/assets/images/social.png) no-repeat;
      background-size: px2rem(434px * $scale);

      + a {
        margin-left: px2rem(36px * $scale);
      }
    }

    .social-ac {
      background-image: url(@/shiftyspad/assets/images/social-ac.png) !important;
      background-repeat: no-repeat !important;
      background-position: center top !important;
      background-size: auto 100% !important;

      &:hover {
        background-image: url(@/shiftyspad/assets/images/social-ac-hover.png) !important;
      }
    }

    .social-twitter {
      background-position: px2rem(-1px * $scale) 0;

      &:hover {
        background-position: px2rem(-1px * $scale) px2rem(-61px * $scale);
      }
    }

    .social-discord {
      background-position: px2rem(-78px * $scale) 0;

      &:hover {
        background-position: px2rem(-78px * $scale) px2rem(-61px * $scale);
      }
    }

    .social-facebook {
      background-position: px2rem(-157px * $scale) 0;

      &:hover {
        background-position: px2rem(-157px * $scale) px2rem(-61px * $scale);
      }
    }

    .social-youtube {
      background-position: px2rem(-239px * $scale) 0;

      &:hover {
        background-position: px2rem(-239px * $scale) px2rem(-61px * $scale);
      }
    }

    .social-reddit {
      background-position: px2rem(-318px * $scale) 0;

      &:hover {
        background-position: px2rem(-318px * $scale) px2rem(-61px * $scale);
      }
    }

    .social-ins {
      background-position: px2rem(-396px * $scale) 0;

      &:hover {
        background-position: px2rem(-396px * $scale) px2rem(-61px * $scale);
      }
    }
  }
}

@mixin socialvh($scale) {
  .social {
    width: px2vh(484px * $scale);
    height: px2vh(40px * $scale);
    margin-top: px2vh(45px);
    position: relative;
    display: flex;

    a {
      display: block;
      width: px2vh(42px * $scale);
      height: px2vh(40px * $scale);
      flex: none;
      background: url(@/shiftyspad/assets/images/social.png) no-repeat;
      background-size: px2vh(434px * $scale);

      + a {
        margin-left: px2vh(36px * $scale);
      }
    }

    .social-twitter {
      background-position: px2vh(-1px * $scale) 0;

      &:hover {
        background-position: px2vh(-1px * $scale) px2vh(-61px * $scale);
      }
    }

    .social-discord {
      background-position: px2vh(-78px * $scale) 0;

      &:hover {
        background-position: px2vh(-78px * $scale) px2vh(-61px * $scale);
      }
    }

    .social-facebook {
      background-position: px2vh(-157px * $scale) 0;

      &:hover {
        background-position: px2vh(-157px * $scale) px2vh(-61px * $scale);
      }
    }

    .social-youtube {
      background-position: px2vh(-239px * $scale) 0;

      &:hover {
        background-position: px2vh(-239px * $scale) px2vh(-61px * $scale);
      }
    }

    .social-reddit {
      background-position: px2vh(-318px * $scale) 0;

      &:hover {
        background-position: px2vh(-318px * $scale) px2vh(-61px * $scale);
      }
    }

    .social-ins {
      background-position: px2vh(-396px * $scale) 0;

      &:hover {
        background-position: px2vh(-396px * $scale) px2vh(-61px * $scale);
      }
    }
  }
}
