// 图鉴，通用样式
.chapter-section-item {
  background: url(@/shiftyspad/assets/images/storyline/storyline-detaill-sectionbg.png) no-repeat
    center top;
  background-size: px2rem(690) 100%;
  background-color: #2d2d2f;
  border-radius: px2rem(8);
  width: px2rem(690);
  min-height: px2rem(69);
  height: auto;
  margin-top: px2rem(13);
  color: #fff;
  font-size: px2rem(22);
  padding: px2rem(6) px2rem(22);
  box-sizing: border-box;
}

.chapter-section-icon-etc {
  background: url(@/shiftyspad/assets/images/icon-etc.png) no-repeat center top / 100%;
  width: px2rem(32);
  height: px2rem(22);
  margin-right: px2rem(10);
}

.chapter-section-icon-divider {
  background: url(@/shiftyspad/assets/images/icon-divider.png) no-repeat center top / 100%;
  width: px2rem(10);
  height: px2rem(22);
  margin-right: px2rem(10);
}

.chapter-section-icon-star {
  background: url(@/shiftyspad/assets/images/icon-star.png) no-repeat center top / 100%;
  width: px2rem(36);
  height: px2rem(33);
}

.is-pc {
  .chapter-section-item {
    width: px2rem(1028px);
  }
}
