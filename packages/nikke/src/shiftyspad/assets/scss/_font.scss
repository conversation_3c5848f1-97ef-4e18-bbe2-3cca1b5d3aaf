@font-face {
  font-family: "industry-demi";
  src: url(@/shiftyspad/assets/fonts/Industry-Demi.ttf);
}
.ff-industry {
  font-family: "industry-demi" !important;
}

// number font
@font-face {
  font-family: "Deco_Indt_demi";
  src: url(@/shiftyspad/assets/fonts/Deco_Indt_demi.ttf);
}
.ff-num {
  font-family: "Deco_Indt_demi" !important;
  * {
    font-family: "Deco_Indt_demi" !important;
  }
}
@font-face {
  font-family: "Deco_abltn_60";
  src: url(@/shiftyspad/assets/fonts/Deco_abltn_60_spacing.ttf);
}
.ff-num-bold {
  font-family: "Deco_abltn_60" !important;
  * {
    font-family: "Deco_abltn_60" !important;
  }
}
.is-pc {
  .ff-num-bold {
    font-weight: 600 !important;
    * {
      font-weight: 600 !important;
    }
  }
}

// English
@font-face {
  font-family: "Pretendard-Regular";
  src: url(@/shiftyspad/assets/fonts/Pretendard-Regular.ttf);
}
@font-face {
  font-family: "Pretendard-Bold";
  src: url(@/shiftyspad/assets/fonts/Pretendard-Bold.ttf);
}
@font-face {
  font-family: "Pretendard-ExtraBold";
  src: url(@/shiftyspad/assets/fonts/Pretendard-ExtraBold.ttf);
}
html[lang="en"] .shiftypad {
  * {
    font-family: "Pretendard-Regular";
  }
  .ff-tt-bold {
    font-family: "Pretendard-Bold";
  }
  .ff-tt-extra-bold {
    font-family: "Pretendard-ExtraBold";
  }
}

// Korean
@font-face {
  font-family: "KR_common_regular";
  src: url(@/shiftyspad/assets/fonts/KR_common_regular.ttf);
}
@font-face {
  font-family: "KR_common_bold";
  src: url(@/shiftyspad/assets/fonts/KR_common_bold.ttf);
}
@font-face {
  font-family: "KR_common_extra_bold";
  src: url(@/shiftyspad/assets/fonts/KR_common_extra_bold.ttf);
}
html[lang="ko"] .shiftypad {
  * {
    font-family: "KR_common_regular";
  }
  .ff-tt-bold {
    font-family: "KR_common_bold";
  }
  .ff-tt-extra-bold {
    font-family: "KR_common_extra_bold";
  }
}

// Japanese
@font-face {
  font-family: "JP_common_regular";
  src: url(@/shiftyspad/assets/fonts/JP_common_regular.ttf);
}
@font-face {
  font-family: "JP_common_bold";
  src: url(@/shiftyspad/assets/fonts/JP_common_bold.ttf);
}
@font-face {
  font-family: "JP_common_extra_bold";
  src: url(@/shiftyspad/assets/fonts/JP_common_extra_bold.ttf);
}
html[lang="ja"] .shiftypad {
  * {
    font-family: "JP_common_regular";
  }
  .ff-tt-bold {
    font-family: "JP_common_bold";
  }
  .ff-tt-extra-bold {
    font-family: "JP_common_extra_bold";
  }
}

// Simple Chinese
@font-face {
  font-family: "SC_common_regular";
  src: url(@/shiftyspad/assets/fonts/SC_common_regular.otf);
}
@font-face {
  font-family: "SC_common_bold";
  src: url(@/shiftyspad/assets/fonts/SC_common_bold.otf);
}
@font-face {
  font-family: "SC_common_extra_bold";
  src: url(@/shiftyspad/assets/fonts/SC_common_extra_bold.otf);
}
html[lang="cn"] .shiftypad {
  * {
    font-family: "SC_common_regular";
  }
  .ff-tt-bold {
    font-family: "SC_common_bold";
  }
  .ff-tt-extra-bold {
    font-family: "SC_common_extra_bold";
  }
}
// Tradition Chinese
@font-face {
  font-family: "TC_common_regular";
  src: url(@/shiftyspad/assets/fonts/TC_common_regular.otf);
}
@font-face {
  font-family: "TC_common_bold";
  src: url(@/shiftyspad/assets/fonts/TC_common_bold.otf);
}
@font-face {
  font-family: "TC_common_extra_bold";
  src: url(@/shiftyspad/assets/fonts/TC_common_extra_bold.otf);
}
// !!!与nikke字体冲突，暂时注释掉--------------
// html[lang="zh-TW"],
// .lang-zh {
//   *,
//   .ff-tt {
//     font-family: "TC_common_regular";
//   }
//   &.is-pc {
//     *,
//     .ff-tt {
//       font-weight: 600;
//     }
//   }
//   .ff-tt-bold {
//     font-family: "TC_common_bold";
//     font-weight: normal;
//   }
//   .ff-tt-extra-bold {
//     font-family: "TC_common_extra_bold";
//     font-weight: normal;
//   }
// }
// !!!与nikke字体冲突，暂时注释掉--------------

// JP_common // Gothic MB101 Pr6
// KR_common // SUIT
// SC_common// Noto Sans SC
// TC_common// Noto Sans TC

// TODO: JP,SC,TC，字体太大，改成Google Fonts
// JP -> Gothic MB101 Pr6 是Adobe Font
