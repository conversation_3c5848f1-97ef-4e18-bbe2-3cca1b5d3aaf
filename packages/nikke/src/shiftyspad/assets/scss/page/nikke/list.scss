.nikkes {
  padding-top: px2rem(88 + 42);
  height: 100%;
  min-height: calc(100vh - px2rem(88 + 42));
  background: #eef1f4 url(@/shiftyspad/assets/images/storyline/storyline-bg.jpg) no-repeat center
    top / 100%;
  padding-bottom: px2rem(50);
}

.nikkes-tab {
  height: px2rem(63);

  a {
    width: px2rem(262);
    height: 100%;
    background: #2b2b2e;
    color: #fff;
    font-size: px2rem(22);
    font-weight: bold;
    line-height: 1.5;

    &.on {
      background: linear-gradient(
        to bottom,
        #16abf5 0%,
        #16abf5 px2rem(5px),
        #eff2f6 px2rem(5px),
        #eff2f6 100%
      );
      color: #303235;
    }
  }
}

.h-28 {
  height: px2rem(28);
}

.h-40 {
  height: px2rem(40);
}

.filters {
  a {
    display: block;
    width: px2rem(83);
    height: px2rem(59);
    line-height: px2rem(54);
    border-radius: px2rem(10);
    background: #3e3f43;
    @include shadow0;
    text-align: center;
    box-sizing: border-box;

    img {
      vertical-align: middle;
    }

    &.on {
      background: #16b0f4;
    }
  }

  a + a {
    margin-left: px2rem(15);
  }

  .filter-btn-refresh {
    width: px2rem(65);
    height: px2rem(56);
    margin-top: px2rem(2);

    img {
      height: px2rem(25);
    }
  }

  .filter-btn-panel {
    width: auto;
    min-width: px2rem(155);
    height: px2rem(52);
    padding: 0 px2rem(20);
    box-sizing: border-box;
    border-radius: px2rem(6);
  }
}

.filter-panel {
  width: px2rem(526);
  // height: px2rem(704);
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: px2rem(150);
  right: px2rem(30);
  border-radius: px2rem(15);
  padding: px2rem(15);
  box-sizing: border-box;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  z-index: 220;

  a.on {
    background: #16b0f4;
  }

  &-btn-reset {
    color: var(--brand-1);
    font-size: px2rem(23);
    text-decoration: underline;
    position: absolute;
    right: px2rem(15);
    top: px2rem(15);
  }

  &-title {
    color: #292929;
    font-weight: bold;
    font-size: px2rem(23);
    padding-bottom: px2rem(6);
    border-bottom: rgba(0, 0, 0, 0.3) solid px2rem(1px);
  }

  &-row {
    &-tit {
      color: #292929;
      font-weight: bold;
      font-size: px2rem(22);
      text-align: center;
    }

    &-list {
      a {
        display: block;
        width: px2rem(118);
        height: px2rem(56);
        margin: 0 px2rem(4);
        background: #323232;
        border-radius: px2rem(10);
        line-height: px2rem(56);
        margin-top: px2rem(10);
        flex: none;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        position: relative;
      }

      a:nth-child(4n + 1) {
        margin-left: 0;
      }

      a:nth-child(4n + 4) {
        margin-right: 0;
      }
    }
  }

  &-sort-arrow {
    border: transparent solid px2rem(6);
    border-bottom-color: #fff;
    width: 0;
    height: 0;
    position: absolute;
    right: px2rem(6);
    bottom: px2rem(6);

    &--down {
      border-bottom-color: transparent;
      border-top-color: #fff;
      bottom: px2rem(2);
    }
  }
}

.nodata-empty {
  background: #eff2f6 url(@/shiftyspad/assets/images/empty-bgdeco.png) no-repeat center px2rem(600);
  background-size: px2rem(701) px2rem(349);
  height: px2rem(1000);
  display: flex;
  align-items: center;
  justify-content: center;

  p {
    color: #222222;
    font-size: px2rem(24);
    text-align: center;
  }
}

// Moile

.is-mobile {
  .filters {
    position: relative;
    padding-top: px2rem(80);

    .filter-input {
      display: flex;
      width: 100%;
      padding-left: px2rem(30);
      padding-right: px2rem(30);
      box-sizing: border-box;
      position: absolute;
      left: 0;
      top: 0;

      > a {
        display: block;
        width: px2rem(80);
        height: px2rem(60);
        background: #3e3f43 url(@/shiftyspad/assets/images/icon-search.png) no-repeat center;
        background-size: px2rem(38);
        border-radius: px2rem(10);
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        @include shadow0;
      }
    }

    .filter-input-name {
      display: block;
      width: px2rem(612);
      height: px2rem(60);
      background: #fff;
      color: #000;
      font-size: px2rem(22);
      padding: px2rem(4) px2rem(18);
      box-sizing: border-box;
      border: none;
      border-radius: px2rem(10px);
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      @include shadow0;
      margin-right: 0;
    }
  }
}

// PC

.is-pc {
  .nikkes {
    padding-top: px2rem(82);
    min-height: calc(100vh - px2rem(88));
    background-size: 100% px2rem(136);
    padding-bottom: 0;
  }

  .nikkes-bd {
    width: px2rem(1080);
    margin-left: auto;
    margin-right: auto;

    .nikkes-all-item,
    .nikkes-player-item {
      margin: px2rem(7) px2rem(6);
    }
  }

  .nikkes-tab {
    height: px2rem(54);

    a {
      width: px2rem(209);
      font-size: px2rem(18);
    }
  }

  .filter-panel {
    top: px2rem(60);
    right: px2rem(30);

    &-btn-reset {
      font-size: px2rem(18);
    }

    &-title {
      font-size: px2rem(18);
    }

    &-row {
      &-tit {
        font-size: px2rem(16);
      }

      &-list {
        a {
          width: px2rem(118);
          height: px2rem(44);
          line-height: px2rem(44);
        }
      }
    }
  }

  .h-28 {
    height: px2rem(22);
  }

  .h-40 {
    height: px2rem(28);
  }

  .filters {
    a {
      width: px2rem(60);
      height: px2rem(46);
      line-height: px2rem(44);
    }

    .filter-btn-refresh {
      width: px2rem(48);
      height: px2rem(40);
      line-height: px2rem(38);

      img {
        height: px2rem(18);
      }
    }

    .filter-btn-panel {
      width: auto;
      min-width: px2rem(120);
      height: px2rem(40);
      line-height: px2rem(40);
      font-size: px2rem(18);
    }
  }

  .filter-input {
    margin-right: px2rem(10);
    position: relative;

    > a {
      display: none !important;
    }
  }

  .filter-input-name {
    width: px2rem(244);
    height: px2rem(40);
    background: #fff;
    color: #000;
    font-size: px2rem(18);
    padding: px2rem(4) px2rem(18);
    box-sizing: border-box;
    border: none;
    border-radius: px2rem(8px);
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .nodata-empty {
    background: #eff2f6 url(@/shiftyspad/assets/images/empty-bgdeco.png) no-repeat center
      px2rem(500);
    background-size: px2rem(701) px2rem(349);
    height: px2rem(800);
    display: flex;
    align-items: center;
    justify-content: center;

    p {
      color: #fff;
      background: #8d8d8b;
      font-size: px2rem(20);
      padding: 0 px2rem(10);
      text-align: center;
      transform: translate(0, px2rem(-100));
    }
  }
}

.arrow {
  display: none;
  position: absolute;
  right: px2rem(12);
  top: px2rem(20);
  margin: px2rem(10) auto;
  box-sizing: border-box;
  width: px2rem(15);
  height: px2rem(15);
  border: solid #000;
  border-width: 0 px2rem(15) px2rem(15) 0;
  clip-path: polygon(50% 0, 100% 50%, 100% 50%, 0 50%);

  &::before {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    border: solid #fff;
    border-width: 0 px2rem(15) px2rem(15) 0;
    clip-path: polygon(50% 0, 100% 50%, 100% 50%, 0 50%);
    transform: translateX(px2rem(-2px));
  }
}

.on .arrow {
  display: block;
}

.arrow.up {
  transform: rotate(0deg);
}

.arrow.down {
  transform: rotate(180deg);
  top: px2rem(15);

  &::before {
    transform: translateX(px2rem(2px));
  }
}

.filter-clear-btn {
  position: absolute;
  right: px2rem(15);
  top: px2rem(10);
  background-color: #16b0f4;
  color: #fff;
  cursor: pointer;
  border-radius: px2rem(10);
  display: block;
  width: px2rem(80);
  height: px2rem(40);
  line-height: px2rem(40);
  font-weight: bold;
  text-align: center;
  font-size: px2rem(23);
}

.head-nav {
  z-index: 99;
}

.is-pc {
  .nikkes {
    .back-btn {
      display: none !important;
    }
  }
}

html[lang="ja"],
.lang-ja {
  &.is-pc {
    .filter-input-name {
      width: px2rem(284);
    }
  }
}
