.home {
  background-color: #f6f6f6;
  @include bgimg("@/shiftyspad/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
  padding-top: px2rem(88);
  padding-bottom: px2rem(35);
}

.home-btns {
  > a {
    display: block;
    background: url(@/shiftyspad/assets/images/home/<USER>/ 100%;
    width: px2rem(234);
    height: px2rem(194);
    text-align: center;
    i {
      display: block;
      width: px2rem(101);
      height: px2rem(101);
      margin: px2rem(20) auto px2rem(15);
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 100% auto;
      &.home-btn-icon1 {
        background-image: url(@/shiftyspad/assets/images/home/<USER>
      }
      &.home-btn-icon2 {
        background-image: url(@/shiftyspad/assets/images/home/<USER>
      }
      &.home-btn-icon3 {
        background-image: url(@/shiftyspad/assets/images/home/<USER>
      }
      &.home-btn-icon4 {
        background-image: url(@/shiftyspad/assets/images/home/<USER>
      }
    }
    span {
      color: #ffffff;
      font-size: px2rem(24);
    }
    &.disabled {
      background-image: url(@/shiftyspad/assets/images/home/<USER>
      pointer-events: none;
      i {
        opacity: 0.5;
      }
      span {
        opacity: 0.5;
      }
    }
  }
}
.home-banners {
  width: px2rem(690);
  height: px2rem(388);
  // border: #918e8f solid px2rem(2);
  // border-radius: px2rem(12px);
  .home-banner {
    display: block;
    // &::after {
    //   display: block;
    //   content: '';
    //   background: url(@/shiftyspad/assets/images/home/<USER>/ 100%;
    //   width: px2rem(704);
    //   height: px2rem(284);
    //   position: absolute;
    //   left: px2rem(-2);
    //   bottom: px2rem(-6);
    // }
    img {
      border-radius: px2rem(12px);
    }
  }
}

.is-pc {
  .home {
    padding-top: px2rem(56);
    @include bgimg("@/shiftyspad/assets/images/home/<USER>/home-bg.jpg");
    min-height: px2rem(720);
    &--logined {
      padding-bottom: 0;
    }
  }
  .home-btns {
    margin-top: px2rem(0);
    > a {
      display: block;
      background: url(@/shiftyspad/assets/images/home/<USER>/home-btn-itembg.png) no-repeat center top /
        100%;
      width: px2rem(259);
      height: px2rem(134);
      text-align: center;
      margin: 0 px2rem(2);
      i {
        width: px2rem(60);
        height: px2rem(60);
        margin-bottom: 0;
      }
      span {
        font-size: px2rem(18);
      }
      &.disabled {
        background-image: url(@/shiftyspad/assets/images/home/<USER>/home-btn-itembg-gray.png);
      }
    }
  }
  .home-banners {
    width: px2rem(1060);
    height: px2rem(198);
    box-shadow: none;
    .home-banner {
      width: px2rem(332);
      height: px2rem(188);
      margin: 0 px2rem(10);
      flex: none;
      &::after {
        display: none;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: px2rem(10);
        box-shadow:
          0 1px 2px rgba(0, 0, 0, 0.1),
          0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
}
