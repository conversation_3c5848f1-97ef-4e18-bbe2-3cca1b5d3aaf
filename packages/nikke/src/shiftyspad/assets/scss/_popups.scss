.popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 49;
}

.pop-close {
  position: absolute;
  right: px2rem(20px);
  top: px2rem(25px);
  width: px2rem(30px);
  height: px2rem(30px);

  &:before,
  &:after {
    position: absolute;
    top: px2rem(15px);
    content: " ";
    width: px2rem(32px);
    height: px2rem(2px);
    background-color: #7e7156;
  }

  &:before {
    transform: rotate(45deg);
  }

  &:after {
    transform: rotate(-45deg);
  }
}

.pop-close--light {
  width: px2rem(40px);
  height: px2rem(40px);
  right: px2rem(-40px);
  top: px2rem(-40px);

  &:before,
  &:after {
    top: pxrem(36px);
    background-color: #f1c683;
    width: px2rem(44px);
  }
}

.pop-bd {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: px2rem(646px);
  background: #ffffff;
  border-radius: px2rem(20);
}

.pop-btn-close {
  background: url(@/shiftyspad/assets/images/icon-close.png) no-repeat center top / 100%;
  width: px2rem(32);
  height: px2rem(32);
  position: absolute;
  right: px2rem(15);
  top: px2rem(20);
}

.pop-btn-ok {
  display: block;
  background: #0eb1fe;
  width: px2rem(220);
  height: px2rem(60);
  text-align: center;
  line-height: px2rem(60);
  padding-top: px2rem(3);
  border-radius: px2rem(10);
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: px2rem(28);
  font-weight: bold;
}

.is-mobile {
  .pop-close--light {
    right: 0;
    top: px2rem(-55px);
  }
}

.is-pc {
  .pop-btn-close {
    width: px2rem(26);
    height: px2rem(26);
  }

  .pop-btn-ok {
    width: px2rem(160);
    height: px2rem(48);
    padding-top: px2rem(3);
    line-height: px2rem(48);
    border-radius: px2rem(6);
    font-size: px2rem(20);
  }
}
