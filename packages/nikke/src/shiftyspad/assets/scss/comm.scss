@import "@/shiftyspad/assets/css/reset.css";
// @import 'primevue/resources/themes/saga-blue/theme.css';
// 只是把css @layer 删掉了
@import "./primevue_resources_themes_saga-blue_theme.css";

// override primevue dropdown styles
.p-dropdown {
  position: relative;
}

.p-dropdown .p-dropdown-trigger {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
}

* {
  box-sizing: border-box;
}

.hid {
  display: block;
  height: 0;
  overflow: hidden;
}

.tn {
  text-indent: -9999em;
  overflow: hidden;
  display: block;
}

div:focus,
span:focus {
  outline: none;
}

.block {
  display: block;
}

.none {
  display: none;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.grow {
  flex-grow: 1;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.order-2 {
  order: 2;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.mt-5 {
  margin-top: px2rem(5px);
}

.mt-10 {
  margin-top: px2rem(10px);
}

.mt-15 {
  margin-top: px2rem(15px);
}

.mt-20 {
  margin-top: px2rem(20px);
}

.mt-30 {
  margin-top: px2rem(30px);
}

.mt-40 {
  margin-top: px2rem(40px);
}

.mt-50 {
  margin-top: px2rem(50px);
}

.mr-5 {
  margin-right: px2rem(5px);
}

.mr-10 {
  margin-right: px2rem(10px);
}

.mr-15 {
  margin-right: px2rem(15px);
}

.mr-20 {
  margin-right: px2rem(20px);
}

.mr-30 {
  margin-right: px2rem(30px);
}

.mr-40 {
  margin-right: px2rem(40px);
}

.mr-50 {
  margin-right: px2rem(50px);
}

.ml-5 {
  margin-left: px2rem(5px);
}

.ml-10 {
  margin-left: px2rem(10px);
}

.ml-15 {
  margin-left: px2rem(15px);
}

.ml-20 {
  margin-left: px2rem(20px);
}

.ml-30 {
  margin-left: px2rem(30px);
}

.ml-40 {
  margin-left: px2rem(40px);
}

.ml-50 {
  margin-left: px2rem(50px);
}

.mb-5 {
  margin-bottom: px2rem(5px);
}

.mb-10 {
  margin-bottom: px2rem(10px);
}

.mb-15 {
  margin-bottom: px2rem(15px);
}

.mb-20 {
  margin-bottom: px2rem(20px);
}

.mb-30 {
  margin-bottom: px2rem(30px);
}

.mb-40 {
  margin-bottom: px2rem(40px);
}

.p-5 {
  padding: px2rem(5px);
}

.p-10 {
  padding: px2rem(10px);
}

.p-15 {
  padding: px2rem(15px);
}

.p-20 {
  padding: px2rem(20px);
}

.px-5 {
  padding-left: px2rem(5px);
  padding-right: px2rem(5px);
}

.pt-0 {
  padding-top: 0;
}

.pt-3 {
  padding-top: px2rem(3px);
}

.pt-5 {
  padding-top: px2rem(5px);
}

.pt-10 {
  padding-top: px2rem(10px);
}

.pt-15 {
  padding-top: px2rem(15px);
}

.pt-20 {
  padding-top: px2rem(20px);
}

.pt-30 {
  padding-top: px2rem(30px);
}

.pb-0 {
  padding-bottom: 0;
}

.pb-5 {
  padding-bottom: px2rem(5px);
}

.pb-10 {
  padding-bottom: px2rem(10px);
}

.pb-15 {
  padding-bottom: px2rem(15px);
}

.pb-20 {
  padding-bottom: px2rem(20px);
}

.pb-30 {
  padding-bottom: px2rem(30px);
}

.pb-40 {
  padding-bottom: px2rem(40px);
}

.pb-50 {
  padding-bottom: px2rem(50px);
}

.pb-60 {
  padding-bottom: px2rem(60px);
}

.pr-0 {
  padding-right: 0;
}

.pr-5 {
  padding-right: px2rem(5px);
}

.pr-10 {
  padding-right: px2rem(10px);
}

.pr-15 {
  padding-right: px2rem(15px);
}

.pr-20 {
  padding-right: px2rem(20px);
}

.pr-30 {
  padding-right: px2rem(30px);
}

.pr-40 {
  padding-right: px2rem(40px);
}

.pl-0 {
  padding-left: 0;
}

.pl-5 {
  padding-left: px2rem(5px);
}

.pl-10 {
  padding-left: px2rem(10px);
}

.pl-15 {
  padding-left: px2rem(15px);
}

.pl-20 {
  padding-left: px2rem(20px);
}

.pl-30 {
  padding-left: px2rem(30px);
}

.pl-40 {
  padding-left: px2rem(40px);
}

.pl-50 {
  padding-left: px2rem(50px);
}

.pl-80 {
  padding-left: px2rem(80px);
}

.text-12 {
  font-size: px2rem(12);
}

.text-14 {
  font-size: px2rem(14);
}

.text-16 {
  font-size: px2rem(16);
}

.text-17 {
  font-size: px2rem(17);
}

.text-18 {
  font-size: px2rem(18);
}

.text-19 {
  font-size: px2rem(19);
}

.text-20 {
  font-size: px2rem(20);
}

.text-22 {
  font-size: px2rem(22);
}

.text-24 {
  font-size: px2rem(24);
}

.text-26 {
  font-size: px2rem(26);
}

.text-28 {
  font-size: px2rem(28);
}

.text-32 {
  font-size: px2rem(32);
}

.text-36 {
  font-size: px2rem(36);
}

.text-46 {
  font-size: px2rem(46);
}

.text-50 {
  font-size: px2rem(50);
}

.text-60 {
  font-size: px2rem(60);
}

.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.leading-snug {
  line-height: 1.375;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-loose {
  line-height: 2;
}

.text-black {
  color: #000;
}

.text-black-80 {
  color: rgba(0, 0, 0, 0.8);
}

.text-black-70 {
  color: rgba(0, 0, 0, 0.7);
}

.text-black-60 {
  color: rgba(0, 0, 0, 0.6);
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5);
}

.text-ink {
  color: #262626;
}

.text-ink-80 {
  color: rgba(38, 38, 38, 0.8);
}

.text-ink-60 {
  color: rgba(38, 38, 38, 0.6);
}

.text-ink-50 {
  color: rgba(38, 38, 38, 0.5);
}

.text-white {
  color: #fff;
}

.text-white-70 {
  color: rgba(255, 255, 255, 0.7);
}

.text-white-60 {
  color: rgba(255, 255, 255, 0.6);
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5);
}

.text-highlight-blue {
  color: #12a8fe;
}

.text-highlight-orange {
  color: #ff7700;
}

.text-dark {
  color: #45484c;
}

.text-red {
  color: #ff001a;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-bold {
  font-weight: 700;
}

.font-bolder {
  font-weight: 800;
}

.m-auto {
  margin: auto;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.w-full {
  width: 100%;
}

.w-80 {
  width: 80%;
}

.w-70 {
  width: 70%;
}

.w-66 {
  width: 66.66%;
}

.w-60 {
  width: 60%;
}

.w-55 {
  width: 55%;
}

.w-half,
.w-50 {
  width: 50%;
}

.w-45 {
  width: 45%;
}

.w-40 {
  width: 40%;
}

.w-33 {
  width: 33.33%;
}

.w-25 {
  width: 25%;
}

.w-20 {
  width: 20%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-80 {
  height: 80%;
}

.h-70 {
  height: 70%;
}

.h-60 {
  height: 60%;
}

.h-half,
.h-50 {
  height: 50%;
}

.h-40 {
  height: 40%;
}

.h-33 {
  height: 33.33%;
}

.h-25 {
  height: 25%;
}

.h-20 {
  height: 20%;
}

.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-fill {
  object-fit: fill;
}

.cursor-pointer {
  cursor: pointer;
}

.bg-gray {
  background-color: #f0f0f0;
}

.bg-gray2 {
  background-color: #cdcdcd;
}

.bg-gray-10 {
  background-color: #d2d2d2;
}

.bg-gray-20 {
  background-color: #e4e4e9;
}

.bg-gray-30 {
  background-color: #b5b5bd;
}

.bg-black {
  background-color: #000;
}

.bg-black-20 {
  background-color: rgba(0, 0, 0, 0.2);
}

.bg-dark {
  background-color: #45484c;
}

.bg-white {
  background-color: #fff;
}

.bg-white-50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-blue {
  background-color: #12a8fe;
}

.border-radius-5 {
  border-radius: px2rem(5);
}

.border-radius-10 {
  border-radius: px2rem(10);
}

.border-left-radius-10 {
  border-top-left-radius: px2rem(10);
  border-bottom-left-radius: px2rem(10);
}

.border-right-radius-10 {
  border-top-right-radius: px2rem(10);
  border-bottom-right-radius: px2rem(10);
}

.border-radius-20 {
  border-radius: px2rem(20);
}

.border-top-gray {
  border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
}

.border-right-gray {
  border-right: rgba(0, 0, 0, 0.2) solid px2rem(1);
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.align-baseline {
  vertical-align: baseline;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.shadow0 {
  box-shadow: 0px px2rem(2px) px2rem(4px) 0px rgba(0, 0, 0, 0.2);
}

.shadow1 {
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.2);
}

.marquee .marquee-inner {
  display: inline-block;
  width: max-content;
  // padding-left: 100%;
  will-change: transform;
  animation-name: marquee, marquee-left;
  animation-duration: 15s, 15s;
  animation-timing-function: linear, linear;
  animation-iteration-count: infinite, 1;
  animation-fill-mode: both, both;
}

@keyframes marquee-left {
  0% {
    padding-left: 10%;
  }

  100% {
    padding-left: 100%;
  }
}

.is-pc .marquee .marquee-inner:hover {
  animation-play-state: paused;
}

@keyframes marquee {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(-100%, 0);
  }
}

.hex-border-white {
  background: url(@/shiftyspad/assets/images/hex-bg-white.png) no-repeat center center / 100%;
  width: px2rem(63);
  height: px2rem(72);
}

.hex-border-dark {
  background: url(@/shiftyspad/assets/images/hex-bg-dark.png) no-repeat center center / 100%;
  width: px2rem(63);
  height: px2rem(72);
}

.filter-black {
  filter: brightness(0);
}

.c-tab {
  height: px2rem(63);

  a {
    width: px2rem(262);
    height: 100%;
    background: #2b2b2e;
    color: #fff;
    font-size: px2rem(22);
    font-weight: bold;
    line-height: 1.5;

    &.on {
      background: linear-gradient(
        to bottom,
        #16abf5 0%,
        #16abf5 px2rem(5px),
        #eff2f6 px2rem(5px),
        #eff2f6 100%
      );
      color: #303235;
    }
  }
}

// .hex {
//   background: red;
//   -webkit-clip-path: polygon(25% 5%, 75% 5%, 100% 50%, 75% 95%, 25% 95%, 0% 50%);
//   clip-path: polygon(25% 5%, 75% 5%, 100% 50%, 75% 95%, 25% 95%, 0% 50%);
// }
// .hex-border {
//   background: red;
//   -webkit-clip-path: polygon(25% 5%, 75% 5%, 100% 50%, 75% 95%, 25% 95%, 0% 50%);
//   clip-path: polygon(25% 5%, 75% 5%, 100% 50%, 75% 95%, 25% 95%, 0% 50%);
// }

@import "./font";

body {
  // background: #000;
}

.is-pc body {
  font-size: 16px;
  // min-width: 1400px;
}

.is-pc {
  #orientLayer {
    display: none !important;
  }
}

.swiper-next,
.swiper-prev {
  background: url(@/shiftyspad/assets/images/swiper-prev.png) no-repeat center top / 100%;
  width: px2rem(84);
  height: px2rem(84);
  position: absolute;
  top: 50%;
  margin-top: px2rem(-42);
  z-index: 2;
}

.swiper-prev {
  left: px2rem(10);
}

.swiper-next {
  background-image: url(@/shiftyspad/assets/images/swiper-next.png);
  right: px2rem(10);
}

.socials {
  background: url(@/shiftyspad/assets/images/socials.png) no-repeat center top / 100%;
  width: px2rem(263);
  height: px2rem(41);
}

.expandable-box {
  width: px2rem(710);
  background: #fff;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: px2rem(15);
  padding-top: px2rem(15);
  padding-bottom: px2rem(80);
  box-sizing: border-box;

  &--nobtn {
    padding-bottom: px2rem(15);
  }

  .btn-toggle {
    width: 100%;
    transform: translate(-50%, 0);
    margin-left: 0;
  }

  @at-root {
    .is-pc {
      .expandable-box {
        width: px2rem(1200);
      }
    }

    // @media screen and (max-width: 1500px) {
    //   .is-pc {
    //     .expandable-box {
    //       width: px2rem(1000);
    //     }
    //   }
    // }
    // @media screen and (min-width: 1800px) {
    //   .is-pc {
    //     .expandable-box {
    //       width: px2rem(1400);
    //     }
    //   }
    // }
  }
}

.btn-toggle {
  background: linear-gradient(to bottom, #fff 0%, #fff 90%, #12a8fe 90%, #12a8fe 100%);
  box-shadow:
    0 -1px 1px rgba(0, 0, 0, 0.1),
    0 -1px 5px rgba(0, 0, 0, 0.15);
  border-bottom-left-radius: px2rem(15);
  border-bottom-right-radius: px2rem(15);
  width: px2rem(710);
  height: px2rem(64);
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: px2rem(0);
  left: 50%;
  margin-left: px2rem(-355);

  i {
    background: url(@/shiftyspad/assets/images/icon-toggle.png) no-repeat center center / 100%;
    width: px2rem(38);
    height: px2rem(24);
    transform: scale(1, -1);
  }

  span {
    color: #00b5ff;
    font-size: px2rem(24);
    text-align: center;
    margin-left: px2rem(10);
  }

  &.expanded {
    i {
      transform: scale(1, 1);
    }
  }
}

.is-pc {
  .btn-toggle {
    i {
      width: px2rem(30);
      height: px2rem(24);
    }

    span {
      font-size: px2rem(20);
    }
  }
}

.black-bottom-notice {
  width: 100%;
  background: #000;
  height: px2rem(50);
  line-height: px2rem(50);
  position: sticky;
  bottom: 0;
  left: 0;
  margin-top: px2rem(50);
  z-index: 180;
  overflow: hidden;

  > p {
    width: px2rem(1300);
    margin-left: auto;
    margin-right: auto;
    color: #fff;
    font-size: px2rem(18);
    white-space: nowrap;
  }

  &--absolute {
    position: absolute;
  }
}

.level-up,
.level-down {
  // @include bgi('@/shiftyspad/assets/images/lv-up.png')
  width: px2rem(26);
  height: px2rem(20);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.level-up {
  @include bgimg("@/shiftyspad/assets/images/lv-up.png");
  animation: ani-levelup 1s ease-out both;
}

@keyframes ani-levelup {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }

  40% {
    opacity: 1;
  }

  100% {
    transform: translate(0, -120%);
    opacity: 0;
  }
}

.level-down {
  @include bgimg("@/shiftyspad/assets/images/lv-down.png");
  animation: ani-leveldown 1s ease-out both;
}

@keyframes ani-leveldown {
  0% {
    transform: translate(0, -120%);
    opacity: 1;
  }

  40% {
    opacity: 1;
  }

  100% {
    transform: translate(0, 0);
    opacity: 0;
  }
}

.listfade-enter-active,
.listfade-leave-active {
  transition: opacity 0.5s ease !important;
}

.listfade-enter-from,
.listfade-leave-to {
  opacity: 0 !important;
}

.listfade-leave-active {
  position: absolute !important;
}

@import "./storyline";
@import "./onetrust";
@import "./error";
