interface IBanOptions {
  /**
   * debugger when entering devtools
   */
  debug?: boolean;

  /**
   * debugger interval time
   */
  debug_interval?: number;

  /**
   * redirect after entering debugger loop
   */
  redirect?: string;

  /**
   * enter debugger loop callback
   */
  callback?: () => void;
}

abstract class Singleton {
  private static instances: Map<string, Singleton> = new Map();
  public static getInstance<T extends Singleton>(
    // 补充上这行, 子类调用getInstance 时会提示没有 class_name~
    this: (new (...args: any[]) => T) & { class_name: string },
    ...args: any[]
  ): T {
    const class_name = this.class_name || this.name;

    if (!Singleton.instances.has(class_name)) {
      const instance = new this(...args);
      Singleton.instances.set(class_name, instance);
    }
    return Singleton.instances.get(class_name) as T;
  }

  protected constructor() {
    const class_name = (this.constructor as any).class_name || this.constructor.name;
    if (Singleton.instances.has(class_name)) {
      throw new Error("Singleton classes cannot be instantiated more than once.");
    }
    Singleton.instances.set(class_name, this);
  }
}
// const backdoor =
//   sha256(getQuery(DefaultQueryKey.CmsDisableDebugKey) ?? "") ===
//   DefaultParams.SignForEnableDevtoolsInProd;

export default class DevtoolsDefender extends Singleton {
  static class_name = "DevtoolsDefender";

  #callback?: () => void;
  #interval?: number;
  #redirect?: string;
  #debug_time: number = 100;

  /**
   * singleton should hide constructor
   */
  public constructor(options?: IBanOptions) {
    super();
    this.setConfig(options);
  }

  public setConfig(custom_options?: IBanOptions) {
    const { callback, redirect } = Object.assign({}, custom_options);
    this.#callback = callback;
    this.#redirect = redirect ?? "/";
  }

  public start() {
    const debugger_func = new Function("debugger");

    // clear prev
    this.clear();

    this.#interval = window.setInterval(() => {
      const prev = new Date().getTime();
      debugger_func();
      const after = new Date().getTime();
      if (after - prev >= this.#debug_time) {
        this.#callback?.();
        // redirect
        if (this.#redirect) {
          location.replace(this.#redirect);
        }
      }
    }, this.#debug_time);
  }

  public disable() {
    this.clear();
  }

  private clear() {
    clearInterval(this.#interval);
  }
}
