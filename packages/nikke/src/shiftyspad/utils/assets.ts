export const getAssetsImage = (url: string) => {
  const u = import.meta.url;
  if (!import.meta.env.DEV) {
    return getPublicResouce(`shiftysassets/images/${url}`);
  }
  return new URL(`../assets/images/${url}`, u).href;
};

export const getNikkeStaticGameResouce = (url: string) => {
  return `${import.meta.env.VITE_APP_TOOLS_CDN}${url}`;
};

export const getPublicResouce = (url: string) => {
  return `${import.meta.env.VITE_APP_CDN_BASE_URL.replace(/\/$/g, "")}/${url.replace(/^\//g, "")}`;
};
