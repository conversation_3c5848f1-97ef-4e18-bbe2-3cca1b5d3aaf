import { Skill } from "packages/types/shiftyspad";

export function f(str: any, temp = "-"): string {
  if (!str) {
    return temp;
  }
  return typeof str === "string" ? str : f(str.toString(), temp);
}

export function formatNum(num: number | string) {
  const thousand = 1000;
  const million = 1000 * thousand;
  const sign = Number(num) < 0 ? "-" : "";
  const map = {
    M: million,
    K: thousand,
  } as const;
  const keys = Object.keys(map) as (keyof typeof map)[];
  const res = Math.abs(Number(num));
  for (let i = 0; i < keys.length; i++) {
    if (res > 10 * map[keys[i]]) {
      return `${sign}${Math.round(res / map[keys[i]])}${keys[i]}`;
    }
  }
  return `${sign}${num}`;
}

export const patch = (val: number, count: number) => {
  let result = String(val);
  while (result.length < count) {
    result = `0${result}`;
  }
  return result;
};

const convertToHTMLSpan = (text: string) => {
  const regex = /<color=(#[0-9a-fA-F]{6})>|<\/color>/g;

  // @ts-ignore
  const result = text.replace(regex, (_match: string, color1: string, color2: string) => {
    if (color1) {
      return `<span style="color: ${color1};">`;
    }
    if (color2) {
      return `</span>`;
    }
  });

  return result;
};

export const parseNewLine = (str: string) => {
  str = str.replace(new RegExp(`\\n`, "g"), "<br />");
  str = str.replace(new RegExp(`\\\\n`, "g"), "<br />");
  return str;
};

export function parseGameSkillDesc(skill: Skill, level: number) {
  const values: Record<string, string> = {};
  skill.description_value_list.forEach((v, index) => {
    if (Array.isArray(v.description_value)) {
      const [default_val] = v.description_value;
      values[`description_value_${patch(index + 1, 2)}`] =
        v.description_value[level] || default_val;
    }
  });
  return parseGameDesc(skill.description_localkey, values);
}

export const parseGameDesc = (desc: string, values: any) => {
  let result = desc;
  Object.keys(values).forEach((key) => {
    result = parseNewLine(result);
    if (typeof values[key] === "string") {
      result = result.replace(new RegExp(`{${key}}`, "g"), values[key]);
    }
    if (typeof values[key] === "number") {
      result = result.replace(new RegExp(`{${key}}`, "g"), String(Number(values[key]) / 100));
    }
  });
  result = convertToHTMLSpan(result);
  return result;
};
