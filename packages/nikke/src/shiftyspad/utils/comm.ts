const pxToRem = (v: number) => {
  return `${v / 16}rem`;
};

export { pxToRem };

export function throttle<T extends (...args: any) => any>(func: T, limit: number = 500) {
  let inThrottle: boolean;
  let lastResult: ReturnType<T>;

  return function (this: any): ReturnType<T> {
    const args: any = arguments;
    const context = this;

    if (!inThrottle) {
      inThrottle = true;

      setTimeout(() => (inThrottle = false), limit);

      lastResult = func.apply(context, args);
    }

    return lastResult;
  };
}

export function promiseWrap<T extends (...args: any) => ReturnType<T>>(func: T) {
  let _promise_: null | ReturnType<T> = null;
  /**
   * NOTE: 调用时参数会变化/每次调用结果会变化，这种场景promise复用会导致悲剧
   */
  return (...args: Parameters<T>) => {
    if (!_promise_) {
      _promise_ = func(...args);
      // bluebird ?
      if (_promise_ instanceof Promise) {
        _promise_.finally(() => {
          _promise_ = null;
        });
      }
    }
    return _promise_;
  };
}
