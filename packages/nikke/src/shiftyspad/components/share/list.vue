<template>
  <container :title="t('nav_nikke_list')">
    <div
      class="w-full max-w-[var(--max-pc-w)] flex flex-wrap pl-20 bg-[var(--fill-3)] pt-2 nikkes-list overflow-hidden"
    >
      <PlayerItem
        v-for="nikke in nikke_list"
        :key="nikke.id"
        :nikke="nikke"
        :screenshot="true"
      ></PlayerItem>
      <div
        v-for="(_, index) in empty_block_count"
        :key="index"
        class="relative nikkes-player-item"
      ></div>
    </div>
  </container>
</template>

<script setup lang="ts">
import { computed } from "vue";
import PlayerItem from "@/shiftyspad/components/nikke-list/player-item.vue";
import container from "./container.vue";
import { useSortNikkeList } from "@/shiftyspad/composable/nikke-filter";
import { t } from "@/locales";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/shiftyspad/stores/user";

// 仅主态
const { user_nikkelist_info } = storeToRefs(useUserStore());
const { filter_player_nikke_list } = useSortNikkeList({ disable_ss: true, user_nikkelist_info });
const total_count = 16;
const nikke_list = computed(() => filter_player_nikke_list.value.slice(0, total_count));
const empty_block_count = computed(() => total_count - nikke_list.value.length);

defineExpose({
  isready: () => Boolean(filter_player_nikke_list.value.length),
});
</script>
