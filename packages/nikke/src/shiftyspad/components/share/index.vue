<template>
  <container :title="t('shiftys_spad')" :usebg="true">
    <div class="f-width">
      <div v-if="logined">
        <div class="relative pb-[24px]">
          <TypeData
            :type="1"
            :user_basic_info="user_basic_info"
            :user_battle_info="user_battle_info"
          ></TypeData>

          <div class="mt-[12px]">
            <Outpost :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
          </div>

          <div class="mt-[12px]">
            <!-- 我的资源 -->
            <Material :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
          </div>
        </div>
        <div class="mb-[20px]">
          <div class="flex items-start justify-between mx-[12px]">
            <div class="font-bold text-[18px] leading-[22px] text-[color:var(--text-1)]">
              {{ t("nikke_list_tab_player") }}
            </div>
            <div class="flex items-center cursor-pointer mt-[5px]">
              <span class="flex items-center justify-center w-[12px] h-[12px] ml-[4px]">
                <SvgIcon
                  name="icon-arrow-top"
                  color="var(--text-1)"
                  class="w-[7px] h-[4px] rotate-[90deg]"
                ></SvgIcon>
              </span>
            </div>
          </div>
          <NikkeRoleList
            v-if="self_user_id"
            ref="role_list"
            class="mt-[8px] mx-[12px]"
            :uid="''"
            :is_client="false"
            :shiftys_user="shiftys_user"
          ></NikkeRoleList>
        </div>
      </div>
    </div>
  </container>
</template>

<script setup lang="ts">
import "swiper/css";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import { ref, toRefs } from "vue";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useI18n } from "vue-i18n";
import container from "./container.vue";
import TypeData from "@/components/common/type-data/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import NikkeRoleList from "@/components/common/nikke-role-list/index.vue";
import Outpost from "@/shiftyspad/components/main/sub/outpost.vue";
import Material from "@/shiftyspad/components/main/sub/material.vue";

const user = useUserStore();
const { shiftys_user, self_user_id } = toRefs(user);
const { logined, user_basic_info, user_battle_info } = storeToRefs(user);

const { t } = useI18n();
const role_list = ref();
defineExpose({
  isready: () => Boolean(role_list.value.isready?.()),
});
</script>
