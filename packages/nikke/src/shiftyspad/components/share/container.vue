<template>
  <div
    class="w-full max-w-[var(--max-pc-w)] flex flex-col relative bg-[url(@/assets/imgs/common/topic-default.png)] bg-no-repeat bg-[length:100%_auto] bg-[center_top] bg-[var(--fill-3)] pt-[32px] box-border"
  >
    <i
      class="absolute top-0 left-0 w-full h-[211px] !bg-[#000] opacity-[0.7] pointer-events-none"
    ></i>
    <div
      v-if="title"
      class="absolute w-full top-[15px] text-[color:var(--color-white)] text-[length:16px] font-bold leading-[19px] text-center"
    >
      {{ title }}
    </div>
    <UserBaseInfo :showswitch="false" class="relative"></UserBaseInfo>
    <div
      class="relative flex-1 pt-[25px] mt-[8px]"
      :class="
        usebg
          ? `bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-no-repeat bg-[length:100%_auto] bg-[center_top] pt-[25px] mt-[8px]`
          : ''
      "
    >
      <slot></slot>
    </div>
    <div
      class="w-full h-[97px] bg-[color:#000] flex justify-around items-center py-[18px] bg-[url(@/shiftyspad/assets/images/share-footer-bg.png)] bg-no-repeat bg-[length:100%_100%]"
    >
      <div class="flex">
        <div
          class="w-[64px] h-[65px] mr-[12px] bg-[url(@/shiftyspad/assets/images/share-footer-code-bg.png)] bg-cover bg-no-repeat flex justify-center items-center"
        >
          <div
            class="w-[64px] h-[61px] bg-[url(@/shiftyspad/assets/images/share-footer-code.png)] bg-cover bg-no-repeat"
          ></div>
        </div>
        <div class="flex flex-col justify-center">
          <div class="text-[24px] leading-[20px] font-semibold bg-clip-text text-[color:#B3D8FB]">
            {{ t("blablalink") }}
          </div>
          <div
            class="text-[16px] leading-[20px] font-semibold bg-clip-text text-[color:var(--color-white)]"
          >
            {{ t("query_data_ad") }}
          </div>
        </div>
      </div>
      <div class="flex flex-col justify-center items-center">
        <div
          class="w-[26px] h-[30px] bg-[url(@/shiftyspad/assets/images/share-footer-logo.png)] bg-no-repeat bg-cover"
        ></div>
        <div class="text-[16px] leading-[20px] text-[color:var(--color-white)]">blablalink</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserBaseInfo from "@/shiftyspad/components/main/UserBaseInfo.vue";
import { t } from "@/locales";

defineProps<{
  title?: string;
  usebg?: boolean;
}>();
</script>
