<script setup lang="ts">
import { ref, toRaw, toRefs } from "vue";
import { MI_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { RarityColorMap } from "@/shiftyspad/types/assets_field_map";
import { computed } from "vue";
import { NikkeListData } from "packages/types/shiftyspad";
import { useImgLoadedStore } from "@/shiftyspad/stores/imgLoaded";
import SvgIcon from "@/components/common/svg-icon.vue";

const props = defineProps<{
  nikke: NikkeListData;
  lock_style?: boolean;
}>();
const { nikke } = toRaw(props);
const { lock_style } = toRefs(props);
const { resource_id, original_rare, corporation } = nikke;
const name = nikke.name_localkey?.name ?? "unknown";
const avatar = ref(MI_CHARACTER_URL({ resource_id: resource_id as number }));
const rarityColor = computed(() => {
  return RarityColorMap[original_rare];
});
const copImage = computed(() => {
  return getAssetsImage(`icon-manufacturer-${corporation.toLowerCase()}--white.png`);
});
const imgLoaded = useImgLoadedStore();
const { setLoad, isLoaded } = imgLoaded;
const lazyAvatar = computed(() => {
  return !isLoaded(avatar.value);
});
</script>

<template>
  <div
    class="cursor-pointer relative nikkes-all-item h-[180px] max-h-[180px] w-[102px] max-w-[22%] my-[4.5px] mx-[5px]"
  >
    <template v-if="lazyAvatar">
      <img
        :key="avatar"
        v-lazy="avatar"
        class="nikkes-all-item-img"
        alt=""
        @loaded="setLoad(avatar)"
      />
    </template>
    <template v-else>
      <img :src="avatar" class="nikkes-all-item-img" alt="" />
    </template>
    <span class="absolute hex-border-dark nikkes-all-item-icon">
      <template v-if="lazyAvatar">
        <img :key="copImage" v-lazy="copImage" alt="" class="inline-block" />
      </template>
      <template v-else>
        <img :src="copImage" alt="" class="inline-block" />
      </template>
    </span>
    <div
      class="nikkes-all-item-btm text-white flex align-center justify-center text-20 pl-10 pr-10 box-border leading-tight text-center"
      :class="[`nikkes-all-item-btm--${rarityColor}`]"
    >
      <span v-if="!lock_style" v-autofontsizeheight>{{ name }}</span>
    </div>
    <div
      v-if="lock_style"
      class="absolute z-[5] top-0 left-0 bg-black/50 w-full h-full cursor-default flex flex-col justify-end items-center pb-[6px]"
    >
      <div class="w-[18px] h-[18px]">
        <SvgIcon name="icon-lock" color="white"></SvgIcon>
      </div>
      <div
        class="mt-[1px] text-stroke1 font-[InterBold] text-[9px] text-white font-bold leading-[11px]"
      >
        {{ name }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.nikkes-all-item {
  border-radius: px2rem(8);
  overflow: hidden;
  box-shadow: 0 0 1px #f5a919;
  &-img {
    display: block;
    width: 100%;
    height: 100%;
    object-position: center top;
    object-fit: cover;
  }
  &-icon {
    width: px2rem(40);
    height: px2rem(46);
    left: px2rem(4);
    top: px2rem(4);
    text-align: center;
    img {
      width: px2rem(26);
      height: px2rem(26);
      margin-top: px2rem(9);
      object-position: center;
      object-fit: contain;
    }
  }
  &-btm {
    width: 100%;
    height: px2rem(59);
    bottom: 0;
    position: absolute;
    background-repeat: repeat-x;
    background-size: auto px2rem(59);
    &--yellow {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-yellowbg.png");
    }
    &--blue {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-bluebg.png");
    }
    &--purple {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-purplebg.png");
    }
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .nikkes-all-item {
    width: px2rem(160 * 0.85);
    border-radius: px2rem(8);
    margin: px2rem(7 * 0.85) px2rem(8 * 0.85);
    &-img {
      height: px2rem(222 * 0.85);
      object-position: center top;
      object-fit: cover;
    }
    &-icon {
      width: px2rem(40 * 0.85);
      height: px2rem(46 * 0.85);
      left: px2rem(4 * 0.85);
      top: px2rem(4 * 0.85);
      img {
        width: px2rem(26 * 0.85);
        height: px2rem(26 * 0.85);
        margin-top: px2rem(9 * 0.85);
      }
    }
    &-btm {
      bottom: 0;
      position: absolute;
      height: px2rem(59 * 0.85);
      background-size: auto px2rem(59 * 0.85);
      font-size: px2rem(20 * 0.85);
    }
  }
}
</style>
