<script setup lang="ts">
import { ref, computed, toRaw } from "vue";
import { BurstType, Nikke, NikkeListData } from "packages/types/shiftyspad";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { useRoleImage } from "@/shiftyspad/composable/role/role-avatar";
import { useImgLoadedStore } from "@/shiftyspad/stores/imgLoaded";
import { BurstMap, RarityColorMap, ClassMap } from "@/shiftyspad/types/assets_field_map";

import WeaponSlotItem from "@/shiftyspad/components/character/WeaponSlotItem.vue";
import NikkeStar from "@/shiftyspad/components/main/NikkeStar.vue";

// import aa from '@/shiftyspad/assets/images/icon-code-blue.png';

const props = defineProps<{
  nikke: Nikke & NikkeListData;
  screenshot: boolean;
}>();
const { nikke, screenshot } = toRaw(props);
const { mi_image } = useRoleImage(nikke.resource_id);
const { name, weapon, code, burst, rarity, limit_break, classType } = (() => {
  return {
    name: nikke.name_localkey?.name ?? "unknown",
    weapon: nikke.shot_id?.element.weapon_type,
    code: nikke.element_id?.element.element,
    classType: nikke.class,
    limit_break: nikke.limit_break,
    burst: nikke.use_burst_skill,
    rarity: nikke.original_rare,
  };
})();
const showWeapon = ref(weapon);
const showBrust = ref(BurstMap[burst] as BurstType);

const level = computed(() => (nikke.level > 0 ? nikke.level : 1));
const rarityColor = computed(() => {
  return RarityColorMap[rarity];
});
const job = computed(() => {
  return ClassMap[classType];
});
const codeImg = computed(() => {
  return getAssetsImage(`icon-code-${code?.toLowerCase()}.png`);
});
const burstImage = computed(() => {
  return getAssetsImage(`icon-burst-${showBrust.value}.png`);
});
const jobImage = computed(() => {
  return getAssetsImage(`nikkes/nikke-job-${job.value}--${rarityColor.value}.png`);
});
const imgLoaded = useImgLoadedStore();
const { setLoad, isLoaded } = imgLoaded;
const lazyAvatar = computed(() => {
  return !isLoaded(mi_image.value);
});
const bg_class = computed(() => {
  if (props.screenshot) return "!shadow-none border-[1px] border-[var(--line-1)]";
  return {};
});
</script>

<template>
  <div
    :class="bg_class"
    class="cursor-pointer relative nikkes-player-item h-[180px] max-h-[180px] w-[102px] max-w-[22%] my-[4.5px] mx-[5px] bg-[var(--fill-3)]"
  >
    <slot></slot>
    <template v-if="lazyAvatar && !screenshot">
      <img
        :key="mi_image"
        v-lazy="mi_image"
        class="nikkes-player-item-img"
        alt=""
        @loaded="setLoad(mi_image)"
      />
    </template>
    <template v-else>
      <img :src="mi_image" class="nikkes-player-item-img" alt="" />
    </template>
    <img v-if="codeImg" class="icon-code" :src="codeImg" alt="character element" />
    <!-- 武器 -->
    <WeaponSlotItem :weapon="showWeapon"></WeaponSlotItem>
    <!-- 一阶/二阶/.. -->
    <p class="hex-border-dark text-center icon-burst" :class="[showBrust]">
      <img class="h-50 object-contain" :src="burstImage" alt="" />
    </p>
    <!-- <img class="icon-love" :src="aa" alt="character element" /> -->
    <div
      class="absolute nikkes-player-item-btm h-[60px]"
      :class="[`nikkes-player-item-btm--${rarityColor}`]"
    >
      <div class="level text-16 text-white absolute ff-num text-center leading-none">
        <em>LV.</em>
        <span v-if="!screenshot" class="ff-num-bold text-32" :data-txt="level">{{ level }}</span>
        <div v-if="screenshot" class="ff-num-bold text-32">{{ level }}</div>
      </div>
      <img :src="jobImage" class="job" alt="" />
      <NikkeStar
        :class-name="'flex flex-end rarity-list absolute'"
        :rarity="rarity"
        :limit_break="limit_break"
      ></NikkeStar>
      <p class="name text-19 text-white absolute">
        <span v-automarquee>{{ name }}</span>
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.nikkes-player-item {
  overflow: hidden;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  &-img {
    display: block;
    width: 100%;
    max-height: 100%;
    object-position: center top;
    object-fit: cover;
  }
  &-btm {
    background-repeat: no-repeat;
    background-position: center bottom;
    background-size: 100%;
    &--yellow {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-item-btmbg.png");
    }
    &--blue {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-item-btmbg--blue.png");
    }
    &--purple {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-item-btmbg--purple.png");
    }
    width: 100%;
    left: 0;
    bottom: -2px;
    .level {
      left: px2rem(5);
      bottom: px2rem(8);
      em {
        font-style: normal;
        text-shadow:
          0 0 1px #464646,
          0 0 2px #464646;
      }
      span {
        display: block;
        position: relative;
        -webkit-text-stroke: 4px #464646;
        &::before {
          content: attr(data-txt);
          position: absolute;
          top: 0;
          left: 0;
          -webkit-text-stroke: 0;
          color: #fff;
        }
      }
    }
    .job {
      width: px2rem(86);
      height: px2rem(86);
      position: absolute;
      right: px2rem(0);
      bottom: px2rem(10);
    }
    .name {
      width: px2rem(100);
      white-space: nowrap;
      overflow: hidden;
      text-shadow:
        0 0 1px #464646,
        0 0 2px #464646;
      text-align: right;
      right: px2rem(5);
      bottom: px2rem(10);
    }
  }
}

.icon-burst {
  background-repeat: no-repeat;
  background-size: 100%;
  width: px2rem(30);
  height: px2rem(34);
  position: absolute;
  top: px2rem(75);
  left: px2rem(5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.weapon-slot-item--hex {
  top: px2rem(40);
}

.icon-code {
  width: px2rem(29);
  height: px2rem(34);
  position: absolute;
  top: px2rem(5);
  left: px2rem(5);
}
.icon-love {
  width: px2rem(29);
  height: px2rem(34);
  position: absolute;
  top: px2rem(110);
  left: px2rem(5);
}
.rarity-list {
  position: absolute;
  right: px2rem(6);
  top: px2rem(36);
  &.nikke-star {
    // :deep(img) {
    //   width: px2rem(22);
    //   height: px2rem(22);
    // }
    // :deep(.evolve) {
    //   width: px2rem(22);
    //   height: px2rem(22);
    //   line-height: px2rem(22);
    //   font-size: px2rem(8);
    // }
    top: px2rem(28);
    transform-origin: 100% 0%;
    transform: scale(0.85);
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .nikkes-player-item {
    margin: px2rem(7 * 0.85) px2rem(8 * 0.85);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    &-btm {
      width: 100%;
      left: 0;
      bottom: -2px;
      .level {
        font-size: px2rem(16 * 0.85);
        left: px2rem(5 * 0.85);
        bottom: px2rem(8 * 0.85);
        span {
          font-size: px2rem(32 * 0.85);
        }
      }
      .job {
        width: px2rem(86 * 0.85);
        height: px2rem(86 * 0.85);
        right: px2rem(0 * 0.85);
        bottom: px2rem(10 * 0.85);
      }
      .name {
        font-size: px2rem(19 * 0.85);
        width: px2rem(100 * 0.85);
        right: px2rem(5 * 0.85);
        bottom: px2rem(10 * 0.85);
      }
    }
  }

  .icon-burst {
    width: px2rem(30 * 0.85);
    height: px2rem(34 * 0.85);
    top: px2rem(75 * 0.85);
    left: px2rem(5 * 0.85);
  }

  .icon-code {
    width: px2rem(29 * 0.85);
    height: px2rem(34 * 0.85);
    top: px2rem(5 * 0.85);
    left: px2rem(5 * 0.85);
  }
  .rarity-list {
    right: px2rem(6 * 0.85);
    top: px2rem(36 * 0.85);
    &.nikke-star {
      // :deep(img) {
      //   width: px2rem(22 * 0.85);
      //   height: px2rem(22 * 0.85);
      // }
      // :deep(.evolve) {
      //   width: px2rem(22 * 0.85);
      //   height: px2rem(22 * 0.85);
      //   line-height: px2rem(22 * 0.85);
      //   font-size: px2rem(8 * 0.85);
      // }
      top: px2rem(28 * 0.85);
      transform-origin: 100% 0%;
      transform: scale(0.75);
    }
  }
  .weapon-slot-item--hex {
    width: px2rem(30 * 0.85);
    height: px2rem(33 * 0.85);
    top: px2rem(40 * 0.85);
    left: px2rem(5 * 0.85);
    .icon-weapon {
      z-index: 100;
      width: px2rem(22 * 0.85);
      height: px2rem(22 * 0.85);
      margin-top: px2rem(-11 * 0.85);
    }
  }
}
</style>
