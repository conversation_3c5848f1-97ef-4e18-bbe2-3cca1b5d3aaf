<template>
    <div class="mt-[12px]">
        <div v-for="(item, index) in list" :key="index" class="nikke-numerical-item flex  justify-between items-start bg-[#fff] py-[8px]  px-[12px] mb-[6px]">
            <div class="nikke-numerical-item-left flex w-[60%] h-[100%]" >
                <div class="relative w-[68px] h-[68px]  mr-[8px]" :class="item.bg">
                    <img :src="imgNumerical" class="w-[68p] h-[68px]" alt="">
                    <!-- 爱心 -->
                    <div 
                        class="love absolute flex justify-center items-center top-[0px] left-[4px]  w-[16px] h-[16px]  bg-[length:100%_100%] " 
                        :class="item.bg==='bg1'?'love-purple':item.bg==='bg2'?'love-organge':'love-blue'"
                        >
                        <div 
                            class="love-inner  bg-[length:100%_100%] w-[12px] h-[8px]"
                            :class="item.bg==='bg1'?'purple-inner':item.bg==='bg2'?'organge-inner':'blue-inner'"
                        ></div>
                    </div>
                    <!-- 等级 -->
                    <div class="lv w-[30px] h-[10px] absolute right-[4px] top-[4px] flex ">
                        <img  class="w-[10px] h-[10px]" :src="starOnImg" alt="">
                        <img  class="w-[10px] h-[10px]" :src="starImg" alt="">
                        <img  class="w-[10px] h-[10px]" :src="starImg" alt="">
                    </div>
                    <!-- 名字 -->
                     <div class="role-name absolute bottom-[4px] left-[0px] right-0  overflow-hidden whitespace-nowrap text-ellipsis truncate text-center">Modernia </div>
                </div>
                <div class="role-info flex-1 bg-[#F5F5F5] px-[6px] pt-[8px] rounded-md">
                    <div class="aa flex font-[DINNextLTPro]  items-center w-full">
                        <div class="flex-1 text-center">
                            <div class=" text-14  text-[#5A5A5B]">LV.</div>
                            <div class="text-18 font-bold text-[#141416]">140</div>
                        </div>
                        <div class="w-[1px] h-[20px] bg-[#DBDBDB]  mr-[6px] ml-[6px]"></div>
                        <div class="flex-1 text-center">
                            <div class=" text-14  text-[#5A5A5B]">POWER</div>
                            <div class="color-linear text-18 font-bold">21563</div>
                        </div>
                    </div>
                    <div class="flex justify-center items-center border-t-[1px] border-[#DBDBDB] pt-[3px]">
                        <img class="w-[30px] h-[28px] mr-[6px]" :src="combatPower1" alt="">
                        <img class="w-[30px] h-[28px] mr-[6px]" :src="combatPower2" alt="">
                        <img class="w-[30px] h-[28px]" :src="combatPower3" alt="">
                    </div>
                </div>
            </div>
            <!-- 右侧信息 -->
            <div v-if="item.combatPower.length>0" class="w-[40%]  h-full ml-[6px]" >
                <div v-for="(it, idx) in item.combatPower"  :key="idx">
                    <div v-if="it.isHave"  class="bg-[#F5F5F5] h-[20px] px-[6px] flex justify-between items-center rounded-md mb-[4px] text-[#141416]">
                        <div class="w-[60%] overflow-hidden whitespace-nowrap truncate text-16">{{it.text}}</div>
                        <div class="text-20 font-bold  font-[DINNextLTProBold] ">{{ it.value }}</div>
                    </div>
                  
                    <div v-else class="bg-[#F5F5F5] h-[20px] px-[6px] flex justify-between items-center rounded-md mb-[4px]">
                        <div class="text-16 text-center w-full text-[#585859]">no data</div>
                    </div>
                </div>
            </div>
            <!-- 右侧信息无数据 -->
            <div v-else  class="w-[40%]  ml-[6px] bg-[#F5F5F5] h-[68px] flex justify-center items-center rounded-md ">
                <div class="text-[#585859] text-16">暂无综合词条</div>

            </div>
        </div>
        <!-- 未解锁 -->
        <div class="nikke-numerical-item bg-[#fff] py-[8px]  px-[12px] mb-[6px] " >
            <div class="lock flex  justify-between items-start relative">
                <div class="absolute w-[30px] h-[30px]  bg-[url(@/shiftyspad/assets/images/numerical/lock.png)] bg-[length:100%_100%] bg-no-repeat top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 z-20"></div>

                <div class="nikke-numerical-item-left flex w-[60%] h-[100%]">
                    <div class="relative w-[68px] h-[68px]  mr-[8px]" >
                        <img :src="imgNumerical" class="w-[68p] h-[68px]" alt="">
                    </div>
                    <div class="role-info flex-1 bg-[#F5F5F5] px-[6px] pt-[8px] rounded-md">
                    </div>
                </div>
                <div class="w-[40%]  ml-[6px] bg-[#F5F5F5] h-[68px] flex justify-center items-center rounded-md ">
                    <div class="text-[#585859] text-16"></div>
    
                </div>

            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import starImg from "@/shiftyspad/assets/images/icon-nikke-star.png";
import starOnImg from "@/shiftyspad/assets/images/icon-nikke-star-gold.png";
import  imgNumerical from '@/shiftyspad/assets/images/numerical/numerical-img.png';
import  combatPower1 from '@/shiftyspad/assets/images/numerical/combat-power1.png';
import  combatPower2 from '@/shiftyspad/assets/images/numerical/combat-power2.png';
import  combatPower3 from '@/shiftyspad/assets/images/numerical/combat-power3.png';




const list=[
    {
        img:'xxxx0',
        bg:'bg1',
        name:'Modernia',
        lv:'140',
        power:'21563',
        combatPower:[
            {   
                isHave:true,
                text:'123123132',
                value:'59.66%'
            },
            {   
                isHave:true,
                text:'123123132',
                value:'59.66%'
            },
            {   
                isHave:false,
                text:'123123132',
                value:'59.66%'
            },
        ]
    },
    {
        img:'xxxx0',
        bg:'bg2',
        name:'Modernia',
        lv:'140',
        power:'21563',
        combatPower:[]
    },
    {
        img:'xxxx0',
        bg:'bg3',
        name:'Modernia',
        lv:'140',
        power:'21563',
        combatPower:[]
    },
]

</script>

<style  lang="scss" scoped>



.love-inner{
    
}
.bg1{
    &::before{
        content: '';
        height: 20px;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: linear-gradient(180deg, rgba(189, 85, 255, 0.00) 14.29%, rgba(189, 85, 255, 0.39) 52.89%, rgba(189, 85, 255, 0.82) 80.31%, #BD55FF 100%);
    }
}
.bg2::before{
    content: '';
    height: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(253, 180, 25, 0.00) 14.29%, rgba(253, 180, 25, 0.39) 52.89%, rgba(253, 180, 25, 0.82) 80.31%, #FDB419 100%);
}
.bg3::before{
    content: '';
    height: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 192, 255, 0.00) 14.29%, rgba(0, 192, 255, 0.39) 52.89%, rgba(0, 192, 255, 0.82) 80.31%, #00C0FF 100%);
}

.love{
    &-purple{
        background-image: url('@/shiftyspad/assets/images/numerical/love-bg-1.png');
    }
    &-organge{
        background-image: url('@/shiftyspad/assets/images/numerical/love-bg-2.png');
    }
    &-blue{
        background-image: url('@/shiftyspad/assets/images/numerical/love-bg-3.png');
    }
}
.purple-inner{
    background-image: url('@/shiftyspad/assets/images/numerical/love-inner-1.png');
}
.organge-inner{
    background-image: url('@/shiftyspad/assets/images/numerical/love-inner-2.png');
}
.blue-inner{
    background-image: url('@/shiftyspad/assets/images/numerical/love-inner-3.png'); 
}
.role{
    &-name{
        color: #FFF;
        text-align: center;
        font-family: "DINNextLTPro";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 12px; 
        color: #fff; /* 文字颜色  #716856*/
        text-shadow: 
            -1px -1px 0 #716856,  
            1px -1px 0 #716856,
            -1px  1px 0 #716856,
            1px  1px 0 #716856; /* 描边颜色 */

    }
}
.color-linear{
    background: linear-gradient(90deg, #FFBA49 0%, #FC6A37 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.lock::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
    z-index:10;
}

</style>