<template>
  <div
    :class="{
      'storyline-section-item--inner cursor-pointer':
        ['choice'].includes(scene.speech_window?.toLowerCase()) && !scene.selected,
    }"
    class="attr-voice-item p-20 bg-black mt-20 flex justify-between align-center storyline-section-item"
  >
    <div class="section-item-left">
      <img
        v-if="character_img"
        :src="character_img"
        class="section-avatar"
        width="100%"
        @error="character_img = ''"
      />
      <p class="text-white-60" :class="{ 'text-24': isMobile, 'text-16': !isMobile }">
        {{ get_name() }}
      </p>
      <p
        v-safe-html="getContent()"
        class="text-white"
        :class="{ 'text-28': isMobile, 'text-20': !isMobile, on: false }"
      ></p>
    </div>
    <Spinner v-if="loading" class="section-spinner"></Spinner>
  </div>
</template>

<script lang="ts" setup>
import { Speech } from "@/shiftyspad/types/character";
import { isMobileDevice } from "packages/utils/tools";
import { parseNewLine } from "@/shiftyspad/utils/str";
import { SM_CHARACTER_URL } from "@/shiftyspad/const/urls";

import { useI18n } from "vue-i18n";
import { ref, toRaw } from "vue";

import { useVoice } from "@/shiftyspad/composable/voice";

// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";

const props = defineProps<{
  scene: Speech;
  selected?: boolean;
  commander_name: string;
}>();
const isMobile = isMobileDevice();
const { t } = useI18n();
const { scene, commander_name } = toRaw(props);
const { loading } = useVoice(scene.play_sound);

const character_img = ref(
  scene.speaker_detail ? SM_CHARACTER_URL({ resource_id: scene.speaker_detail?.resource_id }) : "",
);

const getContent = () => {
  const res = parseNewLine(scene.scenario_localkey);
  return res.replace("@UserName", commander_name);
};

const is_self = () => {
  const speaker = scene.speaker?.toLowerCase();
  const speech_window = scene.speech_window?.toLowerCase();
  return speech_window === "self" || speaker === "self";
};

const get_name = () => {
  const speech_window = scene.speech_window?.toLowerCase();
  if (["narration", "monologue", "subtitles"].includes(speech_window)) {
    return "";
  }
  if (speech_window === "choice") {
    return t("choice");
  }
  if (is_self()) {
    return commander_name;
  }
  // speech window -> speech
  return scene.speaker_detail?.name_localkey || scene.speaker;
};
</script>
