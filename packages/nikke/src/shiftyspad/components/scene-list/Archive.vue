<!-- 剧情-档案馆 -->
<script setup lang="ts">
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useArchive } from "@/shiftyspad/composable/archive";
import { report } from "packages/utils/tlog";

const router = useRouter();
const { showArchiveList } = useArchive();

export interface ArchiveItem {
  id: number;
  img: string;
  align: string;
  // rewardTxt: string
  // rewardActive: boolean | number
  // rewardCur: number
  // rewardTotal: number
}

const goDetail = (item: ArchiveItem) => {
  // 剧情图鉴--档案馆--档案按钮点击埋点
  report.small_tool_plot_archives_archives_btn.cm_click({
    archives_id: item.id,
  });
  router.push({ name: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL, params: { id: item.id } });
};
</script>
<template>
  <div class="relative storyline-archive">
    <i class="bg-deco-line bg-deco-line--left"></i><i class="bg-deco-line bg-deco-line--right"></i>
    <ul class="archive-list">
      <li
        v-for="item in showArchiveList"
        :key="item.id"
        :class="`cursor-pointer archive-item archive-item--${item.align}`"
      >
        <div class="relative archive-task">
          <i class="absolute archive-task-redpoint"></i>
          <p class="absolute archive-task-deco"></p>
          <!-- <div class="absolute flex align-center justify-center archive-task-name">
            <i class="archive-task-icon-reward"></i>{{ item.rewardTxt
            }}<i class="archive-task-arrow"></i>
            <span>{{ item.rewardCur }}/{{ item.rewardTotal }}</span>
          </div> -->
          <i class="absolute archive-task-redpoint"></i>
          <img
            :key="item.img"
            v-lazy="item.img"
            class="archive-task-img"
            alt=""
            @click="goDetail(item)"
          />
        </div>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.storyline-archive {
  background-color: #eef1f4;
  @include bgimg("@/shiftyspad/assets/images/storyline/storyline-achieves-bgdeco.png");
  background-repeat: no-repeat;
  background-position: right bottom;
  background-size: px2rem(430);
  padding: px2rem(60) 0;
  .bg-deco-line {
    @include bgimg("@/shiftyspad/assets/images/storyline/storyline-achieves-bgline.png");
    background-repeat: repeat-y;
    background-size: px2rem(28);
    width: px2rem(28);
    height: 100%;
    position: absolute;
    top: 0;
    &--left {
      left: px2rem(20);
    }
    &--right {
      right: px2rem(20);
    }
  }
}
.archive-item {
  &--left {
    margin-left: px2rem(20);
    .archive-task-redpoint {
      left: px2rem(64);
    }
    .archive-task-deco {
      left: px2rem(-2);
      transform: scale(-1, 1);
    }
    .archive-task-name {
      left: px2rem(72);
    }
    .archive-task-img {
      margin-left: px2rem(0);
    }
  }
  &--right {
    margin-left: auto;
    margin-right: px2rem(20);
    .archive-task-redpoint {
      right: px2rem(64);
    }
    .archive-task-deco {
      right: px2rem(-2);
    }
    .archive-task-name {
      right: px2rem(72);
    }
    .archive-task-img {
      margin-right: px2rem(0);
      margin-left: auto;
    }
  }
}
.archive-task {
  &-redpoint {
    @include bgimg("@/shiftyspad/assets/images/icon-redpoint.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(26);
    height: px2rem(26);
    top: px2rem(64);
    z-index: 3;
    display: none;
  }
  &-name + .archive-task-redpoint {
    top: px2rem(46);
  }
  &-deco {
    @include bgimg("@/shiftyspad/assets/images/storyline/archive-task-deco.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(75);
    height: px2rem(32);
    top: px2rem(100);
    &.lock {
      @include bgimg("@/shiftyspad/assets/images/storyline/archive-task-lock.png");
    }
  }
  &-name {
    @include bgimg("@/shiftyspad/assets/images/storyline/archive-task-namebg.png");
    background-repeat: repeat-x;
    background-position: center top;
    background-size: 100%;
    min-width: px2rem(185);
    padding: 0 px2rem(20);
    height: px2rem(45);
    top: px2rem(58);
    color: #aca9a9;
    font-size: px2rem(22);
  }
  &-img {
    display: block;
    width: px2rem(800 * 0.7);
    height: px2rem(512 * 0.7);
    object-fit: contain;
  }
  &-icon-reward {
    @include bgimg("@/shiftyspad/assets/images/icon-reward.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(20);
    height: px2rem(21);
    margin-right: px2rem(10);
  }
  &-arrow {
    display: block;
    width: 0;
    height: 0;
    border: transparent solid px2rem(6);
    border-left-color: #aaa7a7;
    margin: 0 px2rem(4);
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .storyline-archive {
    width: px2rem(768px);
    margin-left: auto;
    margin-right: auto;
    background-position: px2rem(220) bottom;
    background-size: px2rem(400);
    .archive-list {
    }
    .archive-item + .archive-item {
      margin-top: px2rem(20);
    }
    .archive-task {
      &-img {
        // width: px2rem(800 * 0.7);
        // height: px2rem(512 * 0.7);
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .archive-detail {
    .c-tips {
      width: px2rem(690);
      margin-left: auto;
      margin-right: auto;
      padding-left: 0;
    }
  }
}
</style>
