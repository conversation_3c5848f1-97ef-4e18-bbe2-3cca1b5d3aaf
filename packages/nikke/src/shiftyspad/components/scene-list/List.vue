<!-- 主线剧情和突发剧情公用的列表 -->
<!-- 主线剧情 -->
<script setup lang="ts">
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";
import { StroylineMainItem } from "packages/types/game-scene";
import { toRefs } from "vue";
import { useI18n } from "vue-i18n";
import SvgIcon from "@/components/common/svg-icon.vue";
import Loading from "@/components/common/loading.vue";
import { report } from "packages/utils/tlog";

const { t } = useI18n();

const emit = defineEmits(["toDetail"]);

const props = defineProps<{
  data: StroylineMainItem[];
  showCur: boolean;
  loading?: boolean;
}>();

const { data, showCur, loading } = toRefs(props);

const goDetail = (item: StroylineMainItem) => {
  // 剧情图鉴--主线--章节点击埋点
  report.small_tool_plot_main_line_chapter_btn.cm_click({
    chapter_id: item.id,
  });
  if (item.locked) {
    NoticePopup.info({ title: "notice", message: t("scene_unlock_tip"), confirm: t("confirm") });
  } else {
    emit("toDetail", { id: item.id });
  }
};
</script>
<template>
  <Loading v-if="loading" class="h-[50vh]"></Loading>
  <div v-else class="flex flex-wrap storyline-list">
    <div
      v-for="item in data"
      :key="item.id"
      class="relative cursor-pointer storyline-mitem"
      :class="{ locked: item.locked }"
      @click="goDetail(item)"
    >
      <div class="relative">
        <img :key="item.img" v-lazy="item.img" class="chapter-img" alt="" />
        <span
          v-show="item.locked"
          class="absolute w-full h-full top-0 left-0 bg-[color:rgba(0,0,0,0.8)]"
        >
          <SvgIcon
            name="icon-union"
            color="var(--text-4)"
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[16px] h-[19px]"
          ></SvgIcon>
        </span>
      </div>
      <p class="chapter-index">
        <span>{{ item.order }}</span>
      </p>
      <div class="flex justify-between">
        <p class="chapter-name">
          <span v-automarquee class="ff-tt-bold">{{ item.name }}</span>
        </p>
        <span class="chapter-prog"
          ><em v-if="showCur">{{ item.sectionCur }}</em> <span v-if="showCur">/</span
          >{{ item.sectionTotal }}</span
        >
      </div>
      <i class="absolute item-deco-line"></i>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.storyline-list {
  margin: 0 auto;
  padding-left: px2rem(25);
  box-sizing: border-box;
  padding-bottom: px2rem(80);
}
</style>
<style lang="scss">
.storyline-mitem {
  width: px2rem(166);
  height: px2rem(237);
  background: #fff url(@/shiftyspad/assets/images/storyline/storyline-deco-icon.png) no-repeat right
    bottom;
  background-size: px2rem(106) px2rem(87);
  margin: px2rem(30) px2rem(4);
  padding: px2rem(5);
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  .chapter-img {
    display: block;
    width: px2rem(155);
    height: px2rem(155);
    margin: 0 auto;
    object-fit: cover;
  }
  .chapter-name {
    color: #333333;
    font-size: px2rem(22);
    width: px2rem(90);
    overflow: hidden;
    span {
      white-space: nowrap;
    }
  }
  .chapter-prog {
    font-size: px2rem(22);
    color: #84817d;
    white-space: nowrap;
    em {
      color: #333333;
      font-size: px2rem(24);
      font-style: normal;
    }
  }
  .item-deco-line {
    @include bgimg("@/shiftyspad/assets/images/storyline/storyline-deco-line.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(16);
    height: px2rem(43);
    bottom: px2rem(-40);
    left: 0;
    pointer-events: none;
    z-index: 2;
  }
  &::after {
    display: block;
    content: "";
    width: px2rem(166);
    height: px2rem(2);
    position: absolute;
    left: 0;
    bottom: px2rem(-32);
    background: #d2d0ce;
  }
  .item-lock {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    @include bgimg("@/shiftyspad/assets/images/icon-lock.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: px2rem(42);
    display: none;
  }
  &.locked .item-lock {
    display: block;
  }
}
</style>
