<!-- 主线剧情 -->
<script setup lang="ts">
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";

import { useScene } from "@/shiftyspad/composable/scene/scene";

import StorylineList from "@/shiftyspad/components/scene-list/List.vue";

const router = useRouter();
const { showMainList, loading, init } = useScene();

init();

function toDetailHandler({ id }: { id: string }) {
  router.push({ name: RoutesName.SHIFTYSPAD_SCENE_MAIN_DETAIL, params: { id } });
}
</script>
<template>
  <StorylineList
    :data="showMainList"
    :loading="loading"
    :show-cur="true"
    @to-detail="toDetailHandler"
  ></StorylineList>
</template>
