<!-- 突发剧情 -->
<script setup lang="ts">
import StorylineList from "@/shiftyspad/components/scene-list/List.vue";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useScene } from "@/shiftyspad/composable/scene/scene";
const router = useRouter();
const { showSuddenList } = useScene();
function toDetailHandler({ id }: { id: string }) {
  router.push({ name: RoutesName.SHIFTYSPAD_SCENE_SUDDEN_DETAIL, params: { id } });
}
</script>
<template>
  <StorylineList
    :data="showSuddenList"
    :show-cur="false"
    @to-detail="toDetailHandler"
  ></StorylineList>
</template>
