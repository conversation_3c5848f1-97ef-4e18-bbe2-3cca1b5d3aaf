<template>
  <SubHeading :text="t('daily_mission')" />
  <div
    v-if="is_daily_mission_hidden"
    class="h-[180px] mx-[12px] mt-[8px] justify-center flex items-center text-[color:var(--text-3)] bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
  >
    <Nodata :text="t('shiftyspad_user_set_module_private', [t('daily_mission')])"></Nodata>
  </div>
  <div
    v-else
    class="bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)] mx-[12px] mt-[6px] p-[12px] pb-[16px]"
  >
    <div
      class="relative flex items-center gap-x-[7px] pb-[8px] pl-[6px] pr-[12px] border-b-[0.5px] border-solid border-[#DBDBDBDB]"
    >
      <img
        src="@/shiftyspad/assets/images/home/<USER>"
        class="w-[34px] h-[33px] object-contain"
        alt=""
      />
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <span class="font-medium text-[13px] leading-[19px] text-[color:var(--text-1)]">
            OUTPOST DEFENSE
          </span>
          <span
            class="!font-[DINNextLTProBold] text-[20px] leading-[24px] text-[color:var(--text-1)]"
          >
            {{ outpost_defense }}%
          </span>
        </div>
        <div class="w-full h-[4px] bg-[#D9D9D9] mt-[3px]">
          <div class="h-full bg-[var(--brand-1)]" :style="{ width: outpost_defense + '%' }"></div>
        </div>
      </div>
      <div v-if="!unlockOutpost" class="hide-progress w-[50px] flex items-center justify-center">
        <img
          src="data:image/png;base64,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"
          class="userinfo-icon-outpost-defense w-[50px]"
          alt=""
        />
      </div>
    </div>
    <div class="relative mx-[13px] mt-[12px]">
      <ul class="relative z-[1] w-full flex justify-between">
        <li>
          <img
            src="@/assets/imgs/shiftyspad/home/<USER>"
            class="h-[20px] w-[20px] object-contain block mx-auto"
            alt=""
          />
        </li>
        <li
          v-for="(point, index) in activityPoint"
          :key="index"
          :class="[
            (player_activity ?? 0 >= point.value) ? 'text-[#00aeff]' : 'text-[color:var(--text-3)]',
          ]"
        >
          <img
            src="@/shiftyspad/assets/images/home/<USER>"
            class="h-[20px] w-[20px] object-contain block mx-auto"
            alt=""
          />
          <span class="text-[11px] leading-[13px] mt-[4px]">
            {{ point.label }}
          </span>
        </li>
      </ul>
      <div
        class="absolute w-[calc(100%-20px)] h-[4px] top-[8px] left-1/2 -translate-x-1/2 bg-[#D9D9D9]"
      >
        <div
          class="h-full bg-[#12a8fe]"
          :style="{ width: ((player_activity ?? 0 > 100) ? 100 : player_activity) + '%' }"
        ></div>
      </div>
    </div>
    <div class="flex items-center px-[2px] py-[9px] bg-[var(--fill-5)]">
      <div class="flex items-center justify-between flex-1 px-[8px] gap-x-[3px]">
        <div>
          <span class="block font-medium text-[11px] leading-[13px] text-[color:var(--text-1)]">
            {{ t("main_remain_defense") }}
          </span>
        </div>
        <div class="!font-[DINNextLTProBold] text-[13px] leading-[16px] text-[color:var(--text-1)]">
          {{ remain_interception }}
        </div>
      </div>
      <i class="w-[0.5px] h-[30px] bg-[var(--fill-5)]"></i>
      <div class="flex items-center justify-between flex-1 px-[8px] gap-x-[3px]">
        <div>
          <span class="block font-medium text-[11px] leading-[13px] text-[color:var(--text-1)]">
            {{ t("main_rookie_battle") }}
          </span>
          <span class="block text-[9px] leading-[11px] text-[color:var(--text-3)] mt-[2px]">
            {{ t("date_attemp_time") }}
          </span>
        </div>
        <div class="!font-[DINNextLTProBold] text-[13px] leading-[16px] text-[color:var(--text-1)]">
          {{ remain_rookie_arena }}
        </div>
      </div>
      <i class="w-[0.5px] h-[30px] bg-[var(--fill-5)]"></i>
      <div class="flex items-center justify-between flex-1 px-[8px] gap-x-[3px]">
        <div>
          <span class="block font-medium text-[11px] leading-[13px] text-[color:var(--text-1)]">
            {{ t("main_special_battle") }}
          </span>
          <span class="block text-[9px] leading-[11px] text-[color:var(--text-3)] mt-[2px]">
            {{ t("date_attemp_time") }}
          </span>
        </div>
        <div class="!font-[DINNextLTProBold] text-[13px] leading-[16px] text-[color:var(--text-1)]">
          {{ remain_special_arena }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePlayerBasicInfo } from "@/shiftyspad/composable/basic-info";
import { UserBasicInfo, UserBattleInfo } from "packages/types/shiftyspad";

import { toRefs } from "vue";
import { useI18n } from "vue-i18n";

import Nodata from "@/components/common/nodata.vue";
import SubHeading from "@/shiftyspad/components/main/sub/sub-heading.vue";

const props = defineProps<{
  user_battle_info: UserBattleInfo | null;
  user_basic_info: UserBasicInfo | null;
}>();
const { user_battle_info, user_basic_info } = toRefs(props);
const { t } = useI18n();
const activityPoint = [
  { label: "20P", value: 20 },
  { label: "40P", value: 40 },
  { label: "60P", value: 60 },
  { label: "80P", value: 80 },
  { label: "100P", value: 100 },
];
const {
  unlockOutpost,
  outpost_defense,
  player_activity,
  remain_special_arena,
  remain_rookie_arena,
  remain_interception,

  is_daily_mission_hidden,
} = usePlayerBasicInfo({ user_basic_info, user_battle_info });
</script>

<style lang="scss">
.hide-progress {
  position: absolute;
  top: -0.2rem;
  left: -0.2rem;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  text-align: center;
  border-radius: 0.3rem;
  height: 100%;
  padding: 0.2rem;
}
</style>
