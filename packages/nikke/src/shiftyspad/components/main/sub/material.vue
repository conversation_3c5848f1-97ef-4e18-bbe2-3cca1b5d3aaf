<template>
  <SubHeading :text="t('main_material')" />

  <div
    v-if="is_material_info_hidden"
    class="h-[65px] mt-[8px] mx-[12px] text-[color:var(--text-3)] flex items-center justify-center bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
  >
    {{ t("shiftyspad_user_set_module_private", [t("main_material")]) }}
  </div>
  <div
    v-else
    class="flex flex-wrap mt-[8px] mx-[12px] bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
  >
    <div v-for="(material, idx) in material_list" :key="idx" class="w-1/4 group">
      <div
        class="flex items-center box-border border-b-[0.5px] border-r-[0.5px] border-solid border-[var(--line-1)] group-[&:nth-of-type(4n)]:border-r-[color:transparent] group-[&:nth-of-type(n+5)]:border-b-[color:transparent]"
      >
        <div class="flex items-center justify-center w-[32px] h-[32px] mx-[4px]">
          <div class="relative w-[28px] h-[28px]">
            <img
              class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
              :src="getAssetsImage(`icon-res-pic${idx + 1}.png`)"
              alt=""
            />
          </div>
        </div>
        <p class="!font-[DINNextLTProBold] text-[11px] leading-[13px] text-[color:var(--text-1)]">
          {{ material.num }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePlayerBasicInfo } from "@/shiftyspad/composable/basic-info";
import { UserBasicInfo, UserBattleInfo } from "packages/types/shiftyspad";
import { getAssetsImage } from "@/shiftyspad/utils/assets";

import { useI18n } from "vue-i18n";
import { toRefs } from "vue";

import SubHeading from "@/shiftyspad/components/main/sub/sub-heading.vue";

const props = defineProps<{
  user_battle_info: UserBattleInfo | null;
  user_basic_info: UserBasicInfo | null;
}>();
const { t } = useI18n();
const { user_battle_info, user_basic_info } = toRefs(props);
const { material_list, is_material_info_hidden } = usePlayerBasicInfo({
  user_basic_info,
  user_battle_info,
});
</script>
