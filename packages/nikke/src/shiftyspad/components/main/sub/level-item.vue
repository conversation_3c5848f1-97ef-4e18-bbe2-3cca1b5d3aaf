<template>
  <div
    class="w-1/2 flex items-center justify-between pl-[12px] pr-[21px] pt-[5px] pb-[2px] box-border border-b-[0.5px] border-solid border-[var(--line-1)]"
  >
    <p class="text-[10px] leading-[13px] text-[color:var(--text-1)] w-[75px]">
      {{ label }}
    </p>
    <p class="text-[11px] leading-[13px] text-[color:var(--text-3)]">
      <span class="!font-[DINNextLTProBold] text-[16px] leading-[16px] text-[color:var(--text-1)]">
        {{ value }}
      </span>
      Lv
    </p>
  </div>
</template>

<script setup lang="ts">
import { toRefs, computed } from "vue";

const props = defineProps<{
  level_item: {
    label: string | number;
    value: string | number;
  };
}>();

const { level_item } = toRefs(props);
const label = computed(() => level_item.value.label);
const value = computed(() => level_item.value.value);
</script>
