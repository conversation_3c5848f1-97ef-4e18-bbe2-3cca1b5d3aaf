<template>
  <div
    class="zh-patch flex items-center justify-center w-[194px] h-[29px] bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-[length:100%_100%] bg-no-repeat font-bold text-[18px] leading-[22px] text-[#3D5885] mx-auto"
  >
    <span> {{ text }} </span>
  </div>
</template>

<script setup lang="ts">
defineProps(["text"]);
</script>

<style lang="scss" scoped>
html[lang="zh"] .zh-patch {
  span {
    margin-top: 3px;
  }
}
</style>
