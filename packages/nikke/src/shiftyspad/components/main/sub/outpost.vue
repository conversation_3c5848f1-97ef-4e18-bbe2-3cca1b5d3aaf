<template>
  <SubHeading :text="t('main_outpost')" />
  <div
    v-if="is_outpost_info_hidden"
    class="flex items-center text-[color:var(--text-3)] h-[134px] justify-center mx-[12px] mt-[8px] bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
  >
    {{ t("shiftyspad_user_set_module_private", [t("main_outpost")]) }}
  </div>
  <div
    v-else
    class="flex flex-wrap mx-[12px] mt-[8px] bg-[var(--op-fill-white)] shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
  >
    <LevelItem :level_item="outpost_detail.elysion_level" />
    <LevelItem :level_item="outpost_detail.attacker_level" />
    <LevelItem :level_item="outpost_detail.tetra_level" />
    <LevelItem :level_item="outpost_detail.defender_level" />
    <LevelItem :level_item="outpost_detail.pilgrim_level" />
    <LevelItem :level_item="outpost_detail.supporter_level" />
    <LevelItem :level_item="outpost_detail.missilis_level" />
    <LevelItem :level_item="outpost_detail.sychro_level" />
    <LevelItem :level_item="outpost_detail.abnormal_level" />
    <LevelItem :level_item="outpost_detail.recyle_level" />
  </div>
</template>

<script setup lang="ts">
import { usePlayerBasicInfo } from "@/shiftyspad/composable/basic-info";
import { UserBasicInfo, UserBattleInfo } from "packages/types/shiftyspad";

import { toRefs } from "vue";
import { useI18n } from "vue-i18n";

import SubHeading from "@/shiftyspad/components/main/sub/sub-heading.vue";
import LevelItem from "@/shiftyspad/components/main/sub/level-item.vue";

const props = defineProps<{
  user_battle_info: UserBattleInfo | null;
  user_basic_info: UserBasicInfo | null;
}>();
const { t } = useI18n();
const { user_battle_info, user_basic_info } = toRefs(props);
const { outpost_detail, is_outpost_info_hidden } = usePlayerBasicInfo({
  user_basic_info,
  user_battle_info,
});
</script>
