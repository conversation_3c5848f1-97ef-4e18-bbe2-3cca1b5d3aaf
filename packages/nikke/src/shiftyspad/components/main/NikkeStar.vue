<script setup lang="ts">
import { RarityType } from "packages/types/shiftyspad";
import { toRefs } from "vue";
import { useStar } from "@/shiftyspad/composable/role/star";
import starImg from "@/shiftyspad/assets/images/icon-nikke-star.png";
import starOnImg from "@/shiftyspad/assets/images/icon-nikke-star-gold.png";

const props = defineProps<{
  rarity: RarityType;
  limit_break: number;
  className: string;
}>();

const { rarity, limit_break, className } = toRefs(props);
const { break_num, active_star, max_stars } = useStar({
  rarity: rarity.value,
  limit_break: limit_break.value,
});
</script>

<template>
  <div class="nikke-star" :class="className">
    <!-- R 没星 SR 2颗 SSR 3颗 -->
    <!-- <img
          v-if="rarity == RarityType.SSR"
          src="@/shiftyspad/assets/images/icon-nikke-star-gold.png"
          alt=""
        /> -->
    <img v-for="i in active_star" :key="i" :src="starOnImg" alt="" />
    <img v-for="i in max_stars - active_star" :key="i" :src="starImg" alt="" />

    <!-- 破度 = limit_break - 3星 剩余的数字 -->
    <p v-if="break_num" class="evolve ff-num" :class="{ 'evolve--max': break_num === 'MAX' }">
      <span>{{ break_num }}</span>
    </p>
  </div>
</template>

<style lang="scss">
.nikke-star {
  align-items: center;
}
.nikke-star .evolve {
  @include bgimg("@/shiftyspad/assets/images/icon-evolve.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(30);
  height: px2rem(30);
  line-height: px2rem(30);
  font-size: px2rem(15);
  text-align: center;
  color: #fff;
  padding-top: px2rem(1);
  box-sizing: border-box;
  position: relative;
  &--max {
    span {
      display: block;
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0) scale(0.75);
    }
  }
}
.nikke-star img {
  width: px2rem(25);
  height: px2rem(25);
  line-height: px2rem(30);
}
</style>
