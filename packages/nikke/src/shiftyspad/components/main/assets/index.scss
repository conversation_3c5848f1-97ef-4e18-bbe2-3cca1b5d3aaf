.login {
  @include bgimg("@/shiftyspad/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(730);
  height: px2rem(178);
  padding: 0 px2rem(55) px2rem(20);
  box-sizing: border-box;
  position: relative;
  z-index: 5;
  .login-btn {
    color: #fff;
    font-size: px2rem(28);
    font-weight: bold;
    min-width: px2rem(200);
    width: auto;
    padding: 0 px2rem(10);
    box-sizing: border-box;
    height: px2rem(60);
    background: #3e3f43;
    line-height: px2rem(60);
    text-align: center;
    position: relative;
    .firstlogin-tips {
      white-space: nowrap;
      color: #fff;
      font-size: px2rem(16);
      font-weight: normal;
      text-align: center;
      padding: 0 px2rem(20);
      display: flex;
      align-items: center;
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      top: px2rem(-58);
      right: px2rem(-50);
      border-radius: px2rem(10);
      line-height: 1;
      &::after {
        display: block;
        content: "";
        border: transparent solid px2rem(8);
        border-top-color: rgba(0, 0, 0, 0.6);
        width: 0;
        height: 0;
        left: 50%;
        bottom: px2rem(-15);
        position: absolute;
      }
      img {
        display: block;
        width: px2rem(45);
      }
    }
  }
  .user-avatar {
    display: block;
    width: px2rem(88);
    height: px2rem(88);
    border: #fff solid px2rem(2);
    border-radius: 50%;
    background: #157ad8;
    text-align: center;
    padding-top: px2rem(3);
    box-sizing: border-box;
    img {
      width: px2rem(82);
      height: px2rem(82);
      object-fit: cover;
    }
  }
}
.userinfo-top {
  @include bgimg("@/shiftyspad/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(730);
  height: px2rem(178);
  padding-left: px2rem(66);
  box-sizing: border-box;
  position: relative;
  z-index: 5;
  margin-top: px2rem(-10);
}
.userinfo-avatar {
  text-align: center;
  position: relative;
  margin-right: px2rem(20);
  img {
    display: block;
    border-radius: 50%;
    border: #fff solid px2rem(5);
    width: px2rem(80);
    height: px2rem(80);
    background: #000;
  }
  p {
    position: absolute;
    width: auto;
    left: 50%;
    transform: translate(-50%, 0) scale(0.5);
    bottom: px2rem(-16);
    min-width: px2rem(92);
    padding: 0 px2rem(4);
    height: px2rem(32);
    background: #5841a7;
    border-radius: px2rem(20);
    border: #fff solid px2rem(4);
    color: #fff;
    font-size: px2rem(24);
    text-align: center;
    line-height: 1;
  }
}
.userinfo-nick {
  color: #fff;
  font-size: px2rem(28);
  font-weight: bold;
  p {
    font-size: px2rem(18);
    color: #fff;
    margin-left: px2rem(20);
    font-weight: normal;
  }
}
.userinfo-icon-tip {
  width: px2rem(30);
  height: px2rem(30);
  display: block;
}
.userinfo-uid {
  font-size: px2rem(22);
  color: rgba(255, 255, 255, 0.7);
}
.nickinfo-area {
  color: #fff;
  opacity: 0.6;
  font-size: px2rem(18);
}
.userinfo-box {
  width: px2rem(712);
  overflow: hidden;
  position: relative;
  padding: px2rem(25) 0;
  padding-top: px2rem(50);
  padding-bottom: px2rem(100);
  margin-top: px2rem(-50);
  &::before {
    display: block;
    content: "";
    @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: px2rem(180);
  }
  &::after {
    display: block;
    content: "";
    @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: px2rem(240);
  }
  &-inner {
    @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    background-repeat: repeat-y;
    background-position: center top;
    background-size: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
    padding: 0 px2rem(26);
    box-sizing: border-box;
  }
}
.userinfo-title-h3 {
  width: 100%;
  height: px2rem(45);
  line-height: px2rem(45);
  color: #262626;
  font-size: px2rem(22);
  font-weight: normal;
  padding-left: px2rem(44);
  box-sizing: border-box;
  border-bottom: #e8e8e8 solid 1px;
  position: relative;
  i {
    @include bgimg("@/shiftyspad/assets/images/icon-liststyle.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    width: px2rem(17);
    height: px2rem(20);
    position: absolute;
    left: px2rem(15);
    top: 50%;
    margin-top: px2rem(-10);
  }
  &:after {
    display: block;
    content: "";
    @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(122);
    height: px2rem(6);
    position: absolute;
    right: px2rem(15);
    bottom: px2rem(10);
  }
}
.userinfo-border-box {
  border: #d7d7d7 solid px2rem(1);
  border-radius: px2rem(6);
  background: #fff;
  @include shadow0;
}
.userinfo-border-box--notop {
  border-top-width: 0;
}
.userinfo-icon-outpost-defense {
  display: block;
  width: px2rem(57);
  height: px2rem(56);
}
.userinfo-pbar {
  width: 100%;
  height: px2rem(8);
  background: #b5b5b5;
  p {
    width: 0%;
    height: 100%;
    background: #12a8fe;
  }
}
.userinfo-divider {
  position: relative;
  &:after {
    display: block;
    content: "";
    height: 80%;
    position: absolute;
    width: px2rem(1);
    right: 0;
    top: 50%;
    transform: translate(0, -50%);
    background: rgba(0, 0, 0, 0.1);
  }
}
.border-top-gray {
  border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
}
.border-right-gray {
  border-right: rgba(0, 0, 0, 0.2) solid px2rem(1);
}
.border-top-white {
  border-top: #fff solid px2rem(1);
}
.border-right-white {
  border-right: #fff solid px2rem(1);
}
.userinfo-defense-list {
  z-index: 2;
  .icon {
    display: block;
    width: px2rem(30);
    height: px2rem(42);
    margin-left: auto;
    margin-right: auto;
    @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center bottom;
    background-size: 100% auto;
  }
  li:first-child {
    .icon {
      width: px2rem(30);
      height: px2rem(42);
      @include bgimg("@/shiftyspad/assets/images/home/<USER>");
      margin-top: px2rem(3);
    }
  }
  p {
    color: #b5b5b5;
    font-size: px2rem(18);
    line-height: 1;
    margin-top: px2rem(5);
  }
  li.on {
    p {
      color: #00aeff;
    }
  }
  li.opened {
    .icon {
      width: px2rem(32);
      height: px2rem(42);
      @include bgimg("@/shiftyspad/assets/images/home/<USER>");
    }
    p {
      color: #6b6b6b;
    }
  }
}
.userinfo-defense-list + .userinfo-pbar {
  top: px2rem(24);
  left: px2rem(15);
  width: calc(100% - px2rem(30));
}
.res-item {
  height: px2rem(55);
  border-right: #e8e8e8 solid px2rem(1);
  border-bottom: #e8e8e8 solid px2rem(1);
  padding-left: px2rem(15);
}
.res-item:nth-child(4n + 1) {
  padding-left: 0;
}
.res-item:nth-child(n + 5) {
  border-bottom-width: 0;
}
.res-item:nth-child(4n) {
  border-right: none;
}
.res-item:nth-child(n + 5) {
  border-top: #fff solid px2rem(1);
}
.icon-res {
  width: px2rem(58);
  height: px2rem(48);
  object-fit: contain;
  margin-right: px2rem(10);
}
.btn-toggle {
  width: px2rem(690);
  margin-left: px2rem(-345);
  bottom: px2rem(-80);
  background: none;
  box-shadow: none;
}

//   MOBILE

.is-mobile {
  .userinfo-stable {
    display: flex;
    box-sizing: border-box;
    > div {
      width: 50%;
      border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
      box-sizing: border-box;
      padding-top: px2rem(5);
      padding-bottom: px2rem(5);
      &:nth-child(2n) {
        border-left: rgba(0, 0, 0, 0.2) solid px2rem(1);
        padding-left: px2rem(15);
      }
      &:nth-child(1),
      &:nth-child(2) {
        border-top-width: 0;
      }
    }
  }
}

// PC

html[lang="zh"] .font-center-patch {
  margin-top: 2px;
}
.is-pc {
  .login {
    @include bgimg("@/shiftyspad/assets/images/home/<USER>/home-login-bg.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(1060);
    height: px2rem(158);
    padding-bottom: px2rem(10);
    margin-top: px2rem(-10);
    .user-avatar {
      width: px2rem(91);
      height: px2rem(91);
      img {
        width: px2rem(82);
        height: px2rem(82);
      }
    }
    .login-btn {
      min-width: px2rem(160);
      height: px2rem(54);
      line-height: px2rem(54);
      border-radius: px2rem(10);
      font-size: px2rem(20);
      margin-top: px2rem(10);
    }
  }
  .userinfo-top {
    @include bgimg("@/shiftyspad/assets/images/home/<USER>/home-login-bg.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(1060);
    height: px2rem(158);
    padding-bottom: px2rem(10);
    margin-top: px2rem(-10);
  }
  .userinfo-box {
    width: px2rem(1050);
    &::before {
      @include bgimg("@/shiftyspad/assets/images/home/<USER>/userinfo-box-top.png");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      height: px2rem(160);
    }
    &::after {
      @include bgimg("@/shiftyspad/assets/images/home/<USER>/userinfo-box-btm.png");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      height: px2rem(240);
    }
    &-inner {
      @include bgimg("@/shiftyspad/assets/images/home/<USER>/userinfo-box-rbg.png");
      background-repeat: repeat-y;
      background-position: center top;
      background-size: 100%;
      padding: 0 px2rem(26);
    }
  }
  .userinfo-nick {
    p {
      font-size: px2rem(16);
    }
  }
  .userinfo-title-h3 {
    font-size: px2rem(18);
  }
  .userinfo-defense-list {
    p {
      font-size: px2rem(16);
    }
  }

  .row-half {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    > .userinfo-border-box {
      width: calc(50% - px2rem(5));
    }
  }
  .userinfo-stable {
    display: flex;
    box-sizing: border-box;
    position: relative;
    padding-right: 16.66%;
    margin-top: px2rem(10);
    margin-bottom: px2rem(10);
    > div {
      width: 20%;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      margin: 0;
      flex: none;
      box-sizing: border-box;
      padding-top: px2rem(5);
      padding-bottom: px2rem(5);
      border-left: rgba(0, 0, 0, 0.2) solid px2rem(1);
      &:nth-child(10) {
        order: 8;
        border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
      }
      &:nth-child(2) {
        order: 7;
        border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
      }
      &:nth-child(4) {
        order: 6;
        border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
      }
      &:nth-child(6) {
        order: 5;
        border-left: none;
        border-top: rgba(0, 0, 0, 0.2) solid px2rem(1);
      }
      &:first-child {
        border-left: none;
      }
      &:last-child {
        width: 40%;
      }
      &:nth-child(8) {
        width: 16.66%;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }
}
.hide-progress {
  position: absolute;
  top: -0.2rem;
  left: -0.2rem;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  text-align: center;
  border-radius: 0.3rem;
  height: 100%;
  padding: 0.2rem;
}
.marginmiddle {
  margin: 0 auto;
}
.expand-btn {
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 4px), calc(100% - 4px) 100%, 0 100%);
}
