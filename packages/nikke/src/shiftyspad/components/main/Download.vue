<!-- 下载页 -->
<script setup lang="ts">
import { IOSDownload, AndroidDownload } from "@/shiftyspad/const/links";
import { getLang } from "@/shiftyspad/const";
</script>

<template>
  <div class="download">
    <a href="javascript:;" class="download-close" @click="$emit('close')"></a>
    <!-- TODO: 多语言 -->
    <div class="mx-auto download-title"></div>
    <div class="flex justify-center mt-40 download-stores">
      <a :href="IOSDownload">
        <img
          v-if="getLang() === 'ja'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--ja.png"
          alt="Apple Store"
        />
        <img
          v-else-if="getLang() === 'ko'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--ko.png"
          alt="Apple Store"
        />
        <img
          v-else-if="getLang() === 'zh-TW'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--zh.png"
          alt="Apple Store"
        />
        <img
          v-else
          src="@/shiftyspad/assets/images/download/icon-store-appstore.png"
          alt="Apple Store"
        />
      </a>
      <a :href="AndroidDownload">
        <img
          v-if="getLang() === 'ja'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--ja.png"
          alt="Google Play"
        />
        <img
          v-else-if="getLang() === 'ko'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--ko.png"
          alt="Google Play"
        />
        <img
          v-else-if="getLang() === 'zh-TW'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--zh.png"
          alt="Google Play"
        />
        <img
          v-else
          src="@/shiftyspad/assets/images/download/icon-store-gplay.png"
          alt="Google Play"
        />
      </a>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.download {
  background-color: #000;
  @include bgimg("@/shiftyspad/assets/images/download/download-bg.jpg");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 300;
  padding-top: px2rem(1065px);
  box-sizing: border-box;
}
.download-close {
  display: block;
  @include bgimg("@/shiftyspad/assets/images/download/icon-close.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(60);
  height: px2rem(60);
  position: absolute;
  top: px2rem(120);
  right: px2rem(20);
  z-index: 2;
}
.download-title {
  @include bgimg("@/shiftyspad/assets/images/download/download-title.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: auto 100%;
  width: px2rem(750px);
  height: px2rem(120px);
}
.download-stores {
  a {
    display: block;
    height: px2rem(82px);
    margin: 0 px2rem(13px);
    img {
      width: auto;
      height: 100%;
    }
  }
}
html[lang="ko"] {
  .download-title {
    @include bgimg("@/shiftyspad/assets/images/download/download-title-ko.png");
    background-size: auto 100%;
    height: px2rem(127);
  }
}
html[lang="ja"] {
  .download-title {
    @include bgimg("@/shiftyspad/assets/images/download/download-title-ja.png");
    background-size: auto 100%;
    height: px2rem(157);
  }
}
html[lang="zh-TW"] {
  .download-title {
    @include bgimg("@/shiftyspad/assets/images/download/download-title-zh-tw.png");
    background-size: auto 100%;
    height: px2rem(181);
  }
}
</style>

<!-- pc style -->
<style lang="scss" scoped>
.is-pc {
  .download {
    @include bgimg("@/shiftyspad/assets/images/download/pc/download-bg.jpg");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    padding-top: 80vh;
  }
  .download-close {
    top: px2rem(80);
  }
  .download-title {
    display: none;
  }
}
html[lang="ko"] {
  &.is-pc {
    .download {
      @include bgimg("@/shiftyspad/assets/images/download/pc/download-bg-ko.jpg");
    }
  }
}
html[lang="ja"] {
  &.is-pc {
    .download {
      @include bgimg("@/shiftyspad/assets/images/download/pc/download-bg-ja.jpg");
    }
  }
}
html[lang="zh-TW"] {
  &.is-pc {
    .download {
      @include bgimg("@/shiftyspad/assets/images/download/pc/download-bg-zh-tw.jpg");
    }
  }
}
</style>
