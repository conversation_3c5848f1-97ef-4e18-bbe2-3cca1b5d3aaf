<!-- 用户信息 -->
<script setup lang="ts">
import { useUserStore } from "@/shiftyspad/stores/user";
import { RoutesName } from "@/router/routes";

import { computed, ref, toRefs } from "vue";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

import TypeData from "@/components/common/type-data/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import NikkeRoleList from "@/components/common/nikke-role-list/index.vue";
import Outpost from "@/shiftyspad/components/main/sub/outpost.vue";
import DailyNews from "@/shiftyspad/components/main/sub/daily-news.vue";
import Material from "@/shiftyspad/components/main/sub/material.vue";
import UserUnionInfo from "@/shiftyspad/components/main/UserUnionInfo.vue";
const router = useRouter();
const shiftyStore = useUserStore();
const { shiftys_user, self_user_id } = toRefs(shiftyStore);
const { uid, logined, is_client, user_basic_info, user_nikkelist_info, user_battle_info } =
  storeToRefs(shiftyStore);
const expanded = ref(false);

const { t } = useI18n();

const area_id = computed(() => shiftys_user.value.user_role_info.value?.area_id);
</script>

<template>
  <div v-if="logined">
    <div ref="expandBoxRef" class="relative pb-[24px]">
      <TypeData
        :type="1"
        :user_basic_info="user_basic_info"
        :user_battle_info="user_battle_info"
      ></TypeData>

      <DailyNews :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
      <!-- 基础信息 -->
      <template v-if="expanded">
        <div class="mt-[12px]">
          <Outpost :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
        </div>
        <div class="mt-[12px]">
          <Material :user_basic_info="user_basic_info" :user_battle_info="user_battle_info" />
        </div>
      </template>
      <div
        ref="expandBtnRef"
        class="expand-btn absolute bottom-0 left-1/2 -translate-x-1/2 bg-[#141416] w-[43px] h-[16px] flex items-center justify-center cursor-pointer"
        @click="expanded = !expanded"
      >
        <SvgIcon
          name="icon-arrow-top"
          color="var(--color-white)"
          class="w-[13px] h-[8px]"
          :class="[expanded ? '' : 'rotate-[180deg]']"
        ></SvgIcon>
      </div>
    </div>
    <!-- 公会信息 -->
    <UserUnionInfo
      :target_nikke_area_id="area_id"
      :target_intl_open_id="uid"
      :is_client="is_client"
    />
    <div class="mt-[12px] mb-[20px]">
      <div class="flex items-center justify-between mx-[12px]">
        <div class="flex items-center">
          <div class="font-bold text-[18px] text-[color:var(--text-1)] font-center-patch">
            {{ t("nikke_list_tab_player") }}
          </div>
          <SvgIcon
            v-if="!is_client && user_nikkelist_info.length"
            name="icon-setting"
            color="var(--fill-6)"
            class="w-[20px] h-[20px] ml-[8px] cursor-pointer"
            @click="
              router.push({
                name: RoutesName.SHIFTYSPAD_EDIT_NIKKE_LIST,
              })
            "
          ></SvgIcon>
        </div>
        <div class="flex items-center cursor-pointer mt-[5px]">
          <span
            class="text-[11px] text-[color:var(--text-1)] font-center-patch"
            @click="
              router.push({
                name: RoutesName.SHIFTYSPAD_NIKKE_LIST,
              })
            "
          >
            {{ t("view_all") }}
          </span>
          <span class="flex items-center justify-center w-[12px] h-full ml-[4px]">
            <SvgIcon
              name="icon-arrow-top"
              color="var(--text-1)"
              class="w-[7px] h-[4px] rotate-[90deg]"
            ></SvgIcon>
          </span>
        </div>
      </div>
      <!-- 主客态均有 -->
      <NikkeRoleList
        v-if="self_user_id"
        class="mt-[8px] mx-[12px]"
        :uid="uid"
        :is_client="is_client"
        :shiftys_user="shiftys_user"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("./assets/index.scss");
</style>
