<template>
  <div class="mt-[12px] mb-[20px] mx-[12px]">
    <div class="flex items-center justify-between mb-[8px]">
      <div class="flex items-center">
        <div
          class="font-bold text-[18px] text-[color:var(--text-1)] font-center-patch leading-[22px]"
        >
          {{ t("union") }}
        </div>
      </div>
      <div class="flex items-center cursor-pointer">
        <span
          class="text-[11px] text-[color:var(--text-1)] font-center-patch"
          @click="router.push({ name: RoutesName.SQUARE })"
        >
          {{ t("union_square") }}
        </span>
        <span class="flex items-center justify-center w-[12px] h-full ml-[4px]">
          <SvgIcon
            name="icon-arrow-top"
            color="var(--text-1)"
            class="w-[7px] h-[4px] rotate-[90deg]"
          ></SvgIcon>
        </span>
      </div>
    </div>
    <TeamInfo
      v-if="guild_info?.card"
      v-click-interceptor.need_login.mute.sign_privacy.stop="() => cardClick(guild_info?.card!)"
      :item="guild_info?.card"
    >
      <div
        v-if="!is_client"
        class="w-[16px] h-[16px] relative rounded-full bg-[var(--op-fill-white-20)] flex items-center justify-center cursor-pointer"
        @click="union_store.shareToPost()"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-post2" color="var(--color-white)" class="w-[10px] h-[10px]"></SvgIcon>
      </div>
    </TeamInfo>
    <Nodata
      v-else-if="user_hide_union"
      class="mt-[12px] mx-[12px]"
      :text="t('shiftyspad_user_set_module_private', [t('union')])"
    ></Nodata>
    <Nodata v-else-if="no_joined_union" class="mt-[12px] mx-[12px]" />
    <Nodata v-else class="mt-[12px] mx-[12px]" />
  </div>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useUnionStore } from "@/store/union";
import { UnionCard, useUserGuildInfo } from "@/api/union";
import { computed } from "vue";
import { showUnionDetail } from "@/components/announcement-square/team-pop";
import { CODE_ALL_CONFIGS } from "packages/configs/code";
import SvgIcon from "@/components/common/svg-icon.vue";
import Nodata from "@/components/common/nodata.vue";
import TeamInfo from "@/components/announcement-square/team-info/index.vue";
import { useBindRole } from "@/shiftyspad/composable/game-role";

const union_store = useUnionStore();
const router = useRouter();

const props = defineProps<{
  target_nikke_area_id: number | undefined;
  target_intl_open_id: string;
  is_client: boolean;
}>();

const { data: guild_info, error: guild_info_error } = useUserGuildInfo(
  computed(() => ({
    target_nikke_area_id: props.target_nikke_area_id!,
    target_intl_open_id: props.target_intl_open_id.split("-").reverse()[0],
  })),
  { enabled: computed(() => !!props.target_nikke_area_id) },
);

const no_joined_union = computed(() => {
  return guild_info.value && !guild_info.value.card;
});

const user_hide_union = computed(() => {
  return guild_info_error.value?.code === CODE_ALL_CONFIGS.UserNotAllowShowGuildInShiftypad;
});

const { bindRole } = useBindRole();

const cardClick = (item: UnionCard) => {
  showUnionDetail({
    item,
    from: "shiftyspad",
    from_post_item: undefined,
    onBindRole: bindRole,
  });
};
</script>
