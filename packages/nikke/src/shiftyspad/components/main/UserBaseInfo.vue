<script setup lang="ts">
import { computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useBindRole } from "@/shiftyspad/composable/game-role";
import { useGameRegion } from "@/composables/role/use-server-info";
import { useCopy } from "@/shiftyspad/composable/interact/copy";

import avatarPic from "@/shiftyspad/assets/images/appicon.png";
import Avatar from "@/components/common/avatar/index.vue";
import Btns from "@/components/common/btns/index.vue";
import FloatPop from "@/components/common/float-pop/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";

import { t } from "@/locales";
import { report } from "packages/utils/tlog";
import { useAddFriendIngame } from "@/shiftyspad/composable/role/add-friend";

withDefaults(
  defineProps<{
    showswitch?: boolean;
  }>(),
  {
    showswitch: true,
  },
);
const { copy, support_copy } = useCopy();
const { bindRole, show_bind_role } = useBindRole();
const userStore = useUserStore();
const { logined, is_client, user_basic_info, shiftys_user, user_role_info, shown_role_id } =
  storeToRefs(userStore);

const { show_add_friend, onAddFriend } = useAddFriendIngame(shiftys_user.value);

const { getServerName } = useGameRegion({
  all: true,
});
const server_name = computed(() => getServerName(String(user_role_info.value?.area_id)));

onMounted(() => {
  userStore.initSelfRole();
});
</script>

<template>
  <div class="pt-[16px]">
    <div v-if="!logined" class="flex items-center px-[16px]">
      <div class="w-[56px] h-[56px]">
        <Avatar class="box-border" src="" :auth_type="undefined"></Avatar>
      </div>
      <div
        class="font-medium text-[13px] leading-[16px] text-[#fff] ml-[12px]"
        @click="report.small_tool_login_btn.cm_click({})"
      >
        {{ t("please_login") }}<br />{{ t("view_game_data") }}
      </div>
      <div class="relative ml-auto mr-0">
        <Btns type="primary" size="m" :text="t('login')" @click="userStore.liPassLogin"></Btns>
        <FloatPop class="top-[calc(100%+12px)] right-0 z-10">
          <div class="flex items-center flex-wrap gap-[4px]">
            <!-- <span class="font-normal text-[12px] leading-[14px] text-[#141416]">
              {{ t("bind_role_bonus_hint") }}
            </span> -->
            <img
              src="@/shiftyspad/assets/images/icon-res-pic7.png"
              class="w-[16px] h-[14px] object-contain"
            />
            <span class="font-normal text-[11px] leading-[13px] text-[#3EAFFF]">{{
              t("main_firstbind_tip1")
            }}</span>
          </div>
        </FloatPop>
      </div>
    </div>
    <div v-else class="flex items-center px-[16px]">
      <!-- 角色信息 -->
      <div class="w-[56px] h-[56px] mr-[12px]">
        <RoleAvatar
          :scene="'shiftyspad'"
          :default_avatar="avatarPic"
          :avatar_id="user_role_info?.icon ?? 0"
          class="box-border"
        ></RoleAvatar>
      </div>
      <div>
        <div class="flex items-center gap-x-[4px]">
          <span class="font-medium text-[18px] leading-[22px] text-[#fff]">
            {{ user_role_info?.role_name ?? "-" }}
          </span>
          <span
            class="flex items-center justify-center !font-[DINNextLTProBold] text-[12px] leading-[12px] text-[#fff] border-[1px] border-[#fff] bg-transparent px-[3px] pt-[3px] pb-[1px]"
          >
            Lv.{{ user_basic_info?.player_level ?? "-" }}
          </span>
        </div>
        <div
          class="flex items-center gap-x-[8px] font-normal text-[11px] leading-[13px] text-[#959596] mt-[2px]"
        >
          <span>{{ server_name }}</span>
          <span v-if="showswitch">UID: {{ shown_role_id ?? "-" }}</span>
          <span
            v-if="showswitch && !is_client && support_copy"
            class="relative cursor-pointer"
            @click="copy(shown_role_id)"
          >
            <i class="absolute-center"></i>
            <SvgIcon name="icon-copy" color="#fff" class="inline-block w-[9px] h-[9px]"></SvgIcon>
          </span>
        </div>
      </div>
      <div
        v-if="show_bind_role && showswitch && !is_client"
        class="relative flex items-center justify-center w-[20px] h-[20px] bg-[color:rgba(255,255,255,0.1)] rounded-[50%] ml-auto mr-0 cursor-pointer"
        @click="() => bindRole()"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-switch2" color="#fff" class="inline-block w-[12px] h-[10px]"></SvgIcon>
      </div>
      <div
        v-if="show_add_friend"
        class="relative flex items-center justify-center w-[28px] h-[28px] bg-[color:rgba(255,255,255,0.1)] rounded-[50%] ml-auto mr-0 cursor-pointer"
        @click="onAddFriend"
      >
        <i class="absolute-center"></i>
        <SvgIcon name="icon-follow" color="#fff" class="inline-block w-[15px] h-[15px]"></SvgIcon>
      </div>
    </div>
  </div>
</template>
