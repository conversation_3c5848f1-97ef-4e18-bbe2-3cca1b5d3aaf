<script setup lang="ts">
import { toRefs } from "vue";
import { t } from "@/locales";

export type IconItem = {
  succ: "succ";
  error: "error";
};
const props = defineProps<{
  title?: string;
  message?: string;
  confirm?: string;
  textAlign?: string;
}>();
const { title, message, confirm } = toRefs(props);

const emit = defineEmits(["close", "ok"]);
</script>

<template>
  <div class="popup">
    <div class="pop-bd pop-notice">
      <a href="javascript:;" class="absolute pop-btn-close" @click="emit('close')"></a>
      <div class="flex justify-center align-center pop-notice-title">{{ title }}</div>
      <div class="flex flex-col pop-notice-bd">
        <p
          class="flex flex-col justify-center pop-notice-txt"
          :class="{ 'text-left': textAlign == 'left' }"
        >
          {{ message }}
        </p>
        <a href="javascript:;" class="mx-auto mt-20 pop-btn-ok" @click="emit('ok')">{{
          confirm || t("confirm")
        }}</a>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pop-notice {
  background: #ffffff;
  border-radius: px2rem(10);
  width: px2rem(648);
  padding-bottom: px2rem(40);
  &-title {
    height: px2rem(85);
    font-size: px2rem(28);
    color: #ffffff;
    background: #1279e1;
    font-weight: bold;
    border-top-left-radius: px2rem(10);
    border-top-right-radius: px2rem(10);
  }
  &-txt {
    color: #000;
    font-size: px2rem(24);
    text-align: center;
    line-height: 1.35;
    padding: px2rem(20);
    height: auto;
    min-height: px2rem(180px);
    white-space: pre-wrap;
    &.text-left {
      text-align: left;
    }
  }
}
</style>

<style lang="scss" scoped>
.is-pc {
  .pop-notice {
    border-radius: px2rem(6);
    padding-bottom: px2rem(20);
    &-title {
      height: px2rem(64);
      font-size: px2rem(20);
      border-top-left-radius: px2rem(6);
      border-top-right-radius: px2rem(6);
    }
    &-txt {
      font-size: px2rem(16);
      min-height: px2rem(120px);
    }
  }
}
</style>
