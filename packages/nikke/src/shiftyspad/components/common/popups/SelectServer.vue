<!-- 登录弹窗 -->
<script setup lang="ts">
import { useGamesPlus } from "@/composables/use-games-plus";
import { Role } from "packages/types/games";
import { t } from "@/locales";
import { ref, computed, toRefs } from "vue";
import { RoleAvatar } from "@/components/common/avatar/role-avatar.tsx";
import { SwitchTips } from "@/shiftyspad/components/common/popups/switch-tips.tsx";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import "@/shiftyspad/assets/scss/_popups.scss";

const props = withDefaults(
  defineProps<{
    hint: string;
  }>(),
  {
    hint: t("select_server_desc"),
  },
);

const emits = defineEmits<{
  close: [];
  "select-role": [data: Role];
}>();

const { hint } = toRefs(props);
const step = ref<1 | 2>(1);
const visible = ref(false);
const {
  pending,
  server_list,
  selected_role_info,
  selected_server,
  role_list,
  setCurrentRole,
  setCurrentRegion,
  getRoles,
} = useGamesPlus();

const close = () => {
  reset();
  visible.value = false;
  reject_ref.value?.("cancel select");
  emits("close");
};
const butt_text = computed(() => {
  if (pending.value) {
    return t("loading");
  }
  if (role_list.value.length) {
    return t("select_role_confirm");
  }
  return t("back");
});
const reset = () => {
  step.value = 1;
};
const bind = async () => {
  if (pending.value) return;
  if (!role_list.value.length) {
    step.value = 1;
  }
  if (!selected_role_info.value) return;
  resolve_ref.value?.(selected_role_info.value);
  close();
};

const resolve_ref = ref<(r: Role) => void>();
const reject_ref = ref();
const invoke = async (): Promise<Role> => {
  visible.value = true;
  return new Promise((resolve, reject) => {
    resolve_ref.value = (val: Role) => {
      clearTimeout(timer);
      resolve(val);
    };
    reject_ref.value = (msg?: string) => {
      clearTimeout(timer);
      reject(msg ?? "unknown reason");
    };
    const timer = setTimeout(() => {
      reject_ref.value?.("time out");
    }, 60 * 1000);
  });
};

defineExpose({ invoke, reset });
</script>
<template>
  <div v-show="visible" class="popup">
    <div v-show="step === 1" class="pop-bd text-center pop-serverselector">
      <a href="javascript:;" class="absolute pop-btn-close" @click="close"></a>
      <div class="flex justify-center text-32 items-center pop-serverselector-title">
        {{ t("select_server_title") }}
      </div>
      <p class="text-center text-ink-60 text-20 mt-[20px] text-black">
        {{ t(hint) }}
      </p>
      <SwitchTips></SwitchTips>
      <ul class="pop-serverselector-box">
        <li
          v-for="(server, index) in server_list"
          :key="index"
          class="cursor-pointer"
          :class="{ on: selected_server === server }"
          @click="
            () => {
              setCurrentRegion(server);
              step = 2;
              getRoles();
            }
          "
        >
          {{ server.area_name }}
        </li>
      </ul>
    </div>
    <div v-show="step === 2" class="pop-bd text-center pop-roleselector">
      <a href="javascript:;" class="absolute pop-btn-close" @click="close"></a>
      <div class="flex justify-center items-center text-32 pop-roleselector-title">
        {{ t("select_role_title") }}
      </div>
      <p class="text-center text-ink-60 text-20 mt-[20px] mb-[20px] text-black">
        {{ t("select_role_desc") }}
      </p>
      <div class="pop-roleselector-box">
        <p class="flex items-center text-20 text-white ff-tt-bold area-tit">
          {{ selected_server?.area_name }}
        </p>
        <div v-if="role_list.length">
          <div
            v-for="role_info in role_list"
            :key="role_info.role_id + role_info.icon"
            class="pt-[15px] pb-[15px] flex justify-around text-center"
            @click="setCurrentRole(role_info)"
          >
            <div
              class="flex flex-col items-center role-item"
              :class="{ on: selected_role_info === role_info }"
            >
              <p class="role-avatar">
                <RoleAvatar
                  v-if="role_info"
                  :key="role_info.icon"
                  :default_avatar="getAssetsImage('appicon.png')"
                  :avatar_id="role_info?.icon ?? 0"
                />
              </p>
              <!-- <span class="text-14 text-white role-lv">LV{{ role_info?.level }}</span> -->
              <p class="text-20 ff-tt-bold mt-[5px] role-name">{{ role_info?.role_name }}</p>
            </div>
          </div>
        </div>
        <div v-if="!role_list.length && !pending">
          <p
            class="p-[15px] flex justify-around text-center text-20 ff-tt-bold mt-[5px] role-name text-black"
          >
            {{ t("no_roles") }}
          </p>
        </div>
      </div>
      <a
        href="javascript:;"
        class="mx-auto mt-20 mb-5 text-22 pop-btn-ok"
        :class="[pending ? '!bg-[#bbb]' : 'bg-[#0eb1fe]']"
        @click="bind"
        >{{ butt_text }}</a
      >
      <a
        v-if="role_list.length"
        href="javascript:;"
        class="text-center text-ink-60 text-16"
        @click="step = 1"
        >{{ t("back") }}</a
      >
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pop-serverselector {
  background: #ffffff;
  border-radius: px2rem(10);
  width: px2rem(648);
  padding-bottom: px2rem(40);
  &-title {
    height: px2rem(85);
    color: #ffffff;
    background: #1279e1;
    font-weight: bold;
    border-top-left-radius: px2rem(10);
    border-top-right-radius: px2rem(10);
  }
  &-box {
    width: calc(100% - px2rem(100));
    margin: 0 auto;
    li {
      height: px2rem(48);
      padding: 0 px2rem(20);
      box-sizing: border-box;
      background: #f8f8f8;
      color: #222222;
      border-radius: px2rem(6);
      margin-top: px2rem(6);
      display: flex;
      justify-content: space-between;
      align-items: center;
      &.on {
        background: #0eb1fe;
        color: #fff;
      }
      > span {
        i {
          @include bgimg("@/shiftyspad/assets/images/icon-person.png");
          background-repeat: no-repeat;
          background-position: center top;
          background-size: 100%;
          display: block;
          width: px2rem(18);
          height: px2rem(19);
          margin: 0 auto;
        }
      }
    }
  }
  .pop-btn-ok {
    width: calc(100% - px2rem(100));
  }
  .pop-btn-ok.disabled {
    background: #bbb;
  }
  .pop-btn-ok.loading {
    background: #86cbeb;
  }
  .colorRed {
    color: red;
  }
}
.pop-roleselector {
  background: #ffffff;
  border-radius: px2rem(10);
  width: px2rem(648);
  padding-bottom: px2rem(40);
  &-title {
    height: px2rem(85);
    color: #ffffff;
    background: #1279e1;
    font-weight: bold;
    border-top-left-radius: px2rem(10);
    border-top-right-radius: px2rem(10);
  }
  &-box {
    width: calc(100% - px2rem(100));
    margin: 0 auto;
    border-radius: px2rem(10);
    box-shadow:
      0 1px 1px rgba(0, 0, 0, 0.1),
      0 1px 2px rgba(0, 0, 0, 0.2);
    .area-tit {
      background: #0eb1fe;
      height: px2rem(60);
      padding: 0 px2rem(20);
      box-sizing: border-box;
      color: #fff;
      border-radius: px2rem(10);
    }
  }
  .pop-btn-ok {
    width: calc(100% - px2rem(100));
  }
}
.role-item {
  .role-avatar {
    width: px2rem(90);
    height: px2rem(90);
    border-radius: 50%;
    background: #3f4044;
    border: px2rem(4) solid transparent;
    background-clip: padding-box;
    box-shadow: 0 0 0 px2rem(1) #646464;
    img {
      display: block;
      width: px2rem(80);
      height: px2rem(80);
      border-radius: 50%;
      object-fit: contain;
      margin: 0 auto;
    }
  }
  .role-lv {
    background: #393a44;
    padding: px2rem(2) px2rem(10);
    margin-top: px2rem(-15);
  }
  .role-name {
    color: #222;
  }
  &.on {
    .role-avatar {
      box-shadow: 0 0 0 px2rem(1) #0084ff;
    }
    .role-name {
      color: #4ca9ff;
    }
  }
}
</style>
