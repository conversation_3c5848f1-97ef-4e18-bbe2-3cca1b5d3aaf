import { useConfigs } from "@/composables/use-configs.ts";
import { t } from "@/locales";
import { defineComponent, onMounted } from "vue";
import { useDialog } from "@/components/ui/dialog/index.ts";
import { PopCallbackValue } from "packages/types/common";
import { useLogout } from "@/composables/use-logout.ts";

const { show: showDialog } = useDialog();
const { getAnotherLoginAreaConfig } = useConfigs();

const getRandom = () => Math.ceil(Math.random() * 100000000);

export const SwitchTips = defineComponent({
  setup() {
    const { logout } = useLogout();

    const id = getRandom();

    const onConfirm = () => {
      showDialog({
        title: t("warning"),
        content: t("are_you_sure_to_log_out"),
        confirm_text: t("confirm"),
        cancel_text: t("cancel"),
        z_index: 1000,
        async callback(options: { value: PopCallbackValue; close: () => void }) {
          const { value, close } = options;
          if (value === PopCallbackValue.confirm) {
            logout();
          }
          close();
        },
      });
    };

    onMounted(() => {
      const el = document.getElementById(`${id}`);
      if (el) el.onclick = onConfirm;
    });

    const server_name = `[${getAnotherLoginAreaConfig()?.label}]` || "";
    const span1 = `<span class="text-[var(--brand-1)] cursor-pointer" id="${id}">
        ${t("log_out_switch_region")}
      </span>`;
    const text = t("switch_server_to_login", [server_name, span1]);

    return () => {
      return (
        <div
          class="text-center text-ink-60 text-20 mt-[10px] mx-auto mb-[20px] text-black w-[calc(100%-6.25rem)]"
          v-html={text}
        ></div>
      );
    };
  },
});
