import NoticePopupComponent from "./NoticePopup.vue";
import { h, render } from "vue";

enum NoticePopupResultType {
  SUCCESS = "success",
  INFO = "info",
  ERROR = "error",
}
export function NoticePopup(props?: any) {
  const element = document.createElement("div");
  const destroy = () => {
    render(null, element);
    document.body.removeChild(element);
  };
  const onClose = () => {
    if (props && props.onClose) {
      props.onClose();
    }
    destroy();
  };
  const onOk = () => {
    if (props && props.onOk) {
      props.onOk();
    }
    destroy();
  };
  const defaultProps = { onClose, onOk };
  const opts = props ? Object.assign({}, props, defaultProps) : defaultProps;
  const vNode = h(NoticePopupComponent, opts);
  document.body.append(element);
  render(vNode, element);
  if (props?.delay > 0) {
    setTimeout(() => {
      destroy();
    }, props.delay);
  }
  return { destroy };
}

NoticePopup.info = (props?: any) => {
  return NoticePopup({ ...props, iconType: NoticePopupResultType.INFO, delay: 0 });
};

NoticePopup.error = (props?: any) => {
  return NoticePopup({ ...props, iconType: NoticePopupResultType.ERROR, delay: 1500 });
};

NoticePopup.success = (props?: any) => {
  return NoticePopup({ ...props, iconType: NoticePopupResultType.SUCCESS, delay: 1500 });
};
