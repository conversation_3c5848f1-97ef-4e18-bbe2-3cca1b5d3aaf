<script setup lang="ts">
import { toRefs } from "vue";
import { useRouter } from "vue-router";
import { isMobileDevice } from "packages/utils/tools";
import { t } from "@/locales";

const isMobile = isMobileDevice();
const router = useRouter();

const props = defineProps<{
  mode?: string;
}>();
const { mode } = toRefs(props);
</script>
<template>
  <a
    href="javascript:;"
    class="flex justify-center align-center back-btn"
    :class="[mode, { 'left-2/5': !isMobile, 'left-2': isMobile }]"
    @click="router.back()"
    ><i></i><span>{{ t("back") }}</span></a
  >
</template>

<style lang="scss" scoped>
.back-btn {
  position: fixed;
  bottom: px2rem(80);
  // left: px2rem(10);
  @include bgimg("@/shiftyspad/assets/images/back-btn.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(189);
  height: px2rem(74);
  padding-bottom: px2rem(10);
  box-sizing: border-box;
  z-index: 40;
  i {
    display: block;
    width: px2rem(34);
    height: px2rem(30);
    @include bgimg("@/shiftyspad/assets/images/icon-back.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
  }
  span {
    color: #fff;
    font-size: px2rem(24);
    font-weight: 600;
    margin-left: px2rem(10);
    text-shadow: 0 1px 1px #014897;
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .back-btn {
    @include bgimg("@/shiftyspad/assets/images/pc/back-btn.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(204);
    height: px2rem(56);
    left: 50%;
    margin-left: px2rem(-800);
    bottom: px2rem(120);
    i {
      width: px2rem(26);
      height: px2rem(22);
    }
    span {
      font-size: px2rem(20);
      margin-left: px2rem(20);
    }
  }
}
@media screen and (max-width: 1680px) {
  .is-pc {
    .back-btn {
      left: px2rem(10) !important;
      margin-left: 0 !important;
      bottom: px2rem(120) !important;
    }
  }
}

.is-pc {
  .nikkes-detail {
    .back-btn {
      margin-left: px2rem(-900);
    }
  }
}
</style>
