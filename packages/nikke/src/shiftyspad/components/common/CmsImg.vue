<script setup lang="ts">
import { toRefs } from "vue";
import { isWebp } from "@/shiftyspad/const/index";
import { ref } from "vue";
import { cms_img_utils } from "@tencent/pa-cms-utils";

const props = defineProps<{
  src: string;
}>();
const { src } = toRefs(props);

const real_src = ref(
  (() => {
    if (isWebp && cms_img_utils.isCmsOptImage(src.value)) {
      return cms_img_utils.toWebp(src.value);
    }
    return src.value;
  })(),
);

const on_err = () => {
  if (real_src.value !== src.value) {
    real_src.value = src.value;
  }
};
</script>
<template>
  <img :src="real_src" @error="on_err" />
</template>
