<script setup lang="ts">
import { IOSDownload, AndroidDownload } from "@/shiftyspad/const/links";
import { getContactUs, getLicense } from "@/shiftyspad/static/contact";
import { getLang } from "@/shiftyspad/const";
import { t } from "@/locales";
import { report } from "packages/utils/tlog";
import { isInGame } from "packages/utils/tools";
</script>

<template>
  <div class="footer">
    <div v-if="!isInGame()" class="flex justify-center mt-20 footer-download">
      <a :href="IOSDownload" target="_blank" @click="report.small_tool_Apple_Sore_btn.cm_click({})">
        <img
          v-if="getLang() === 'ja'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--ja.png"
          alt="Apple Store"
        />
        <img
          v-else-if="getLang() === 'ko'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--ko.png"
          alt="Apple Store"
        />
        <img
          v-else-if="getLang() === 'zh-TW'"
          src="@/shiftyspad/assets/images/download/icon-store-appstore--zh.png"
          alt="Apple Store"
        />
        <img
          v-else
          src="@/shiftyspad/assets/images/download/icon-store-appstore.png"
          alt="Apple Store"
        />
      </a>
      <a
        :href="AndroidDownload"
        target="_blank"
        @click="report.small_tool_Google_Play_btn.cm_click({})"
      >
        <img
          v-if="getLang() === 'ja'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--ja.png"
          alt="Google Play"
        />
        <img
          v-else-if="getLang() === 'ko'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--ko.png"
          alt="Google Play"
        />
        <img
          v-else-if="getLang() === 'zh-TW'"
          src="@/shiftyspad/assets/images/download/icon-store-gplay--zh.png"
          alt="Google Play"
        />
        <img
          v-else
          src="@/shiftyspad/assets/images/download/icon-store-gplay.png"
          alt="Google Play"
        />
      </a>
    </div>
    <div class="mt-40 flex align-center justify-center footer-app">
      <img src="@/shiftyspad/assets/images/appicon.png" alt="" />
      <div class="footer-app-desc">
        <p>{{ t("game_name") }}</p>
        <p>{{ t("game_type") }}</p>
        <p>{{ t("game_os") }}</p>
      </div>
      <p class="footer-logo"></p>
    </div>
    <div class="footer-txt">
      <div class="text-center mt-30 footer-links">
        <a :href="t('privacyLink')" target="_blank" class="privacy">{{ t("privacy") }}</a
        >|<a :href="t('cookiesLink')" target="_blank" class="cookies">{{ t("cookies") }}</a
        >|<a :href="getContactUs()" target="_blank">{{ t("contact_us") }}</a
        >|<a :href="getLicense()" target="_blank">{{ t("license_agreement") }}</a>
      </div>
      <div class="text-center mt-10 footer-copyrights">&copy;2025 Hotcool Technology Co. Ltd.</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.footer {
  height: px2rem(528px);
  background: #000;
}
.footer-download {
  a {
    display: block;
    height: px2rem(69px);
    margin: 0 px2rem(13px);
    img {
      width: auto;
      height: 100%;
    }
  }
}
.footer-app {
  > img {
    width: px2rem(213px);
    height: px2rem(213px);
  }
  &-desc {
    color: #fefefe;
    font-size: px2rem(25px);
    line-height: px2rem(40px);
    margin-left: px2rem(72px);
  }
}
.footer-logo {
  display: none;
}
.footer-txt {
  color: #808080;
  font-size: px2rem(20px);
  line-height: px2rem(30px);
  a {
    color: #808080;
    margin: 0 px2rem(4);
    &:hover {
      color: #ffffff;
    }
  }
  .footer-copyrights {
    font-size: px2rem(16px);
    line-height: px2rem(20px);
  }
}
</style>

<!-- pc style -->
<style lang="scss" scoped>
.is-pc {
  .footer {
    height: px2rem(266);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
  .footer-download {
    order: 2;
  }
  .footer-app {
    order: 1;
    margin-right: px2rem(40);
    margin-top: px2rem(15);
    > img {
      width: px2rem(133px);
      height: px2rem(133px);
    }
    &-desc {
      font-size: px2rem(15);
      line-height: px2rem(35);
      border-right: #fff solid px2rem(1);
      padding-right: px2rem(70);
    }
  }
  .footer-logo {
    display: block;
    @include bgimg("@/shiftyspad/assets/images/logo.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(181);
    height: px2rem(91);
    margin-left: px2rem(46);
  }
  .footer-txt {
    order: 3;
    display: flex;
    justify-content: center;
    width: 100%;
    font-size: px2rem(15);
    line-height: 1.5;
    a {
      margin: 0 px2rem(20);
    }
  }
  .footer-copyrights {
    margin-left: px2rem(400);
  }
}
</style>
