<script setup lang="ts">
import { useLoadingStore } from "@/shiftyspad/stores/loading";
import { toRefs } from "vue";
const loading_store = useLoadingStore();

const props = defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
});
const { loading } = toRefs(props);
</script>
<template>
  <div v-if="loading || loading_store.loading" class="loading loading--white">
    <p class="loading-spin"></p>
    <p class="loading-msg">{{ loading_store.content }}</p>
  </div>
</template>
<style lang="scss" scoped>
.is-pc {
  .loading {
    // topbar 56, footer: 266
    top: px2rem(56);
    height: calc(100% - px2rem(56));
  }
}
.loading {
  position: absolute;
  height: 100vh;
  width: 100%;
  top: px2rem(88);
  height: calc(100% - px2rem(88));
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  @include bgimg("@/shiftyspad/assets/images/loading-bg.jpg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  &-spin {
    @include bgimg("@/shiftyspad/assets/images/loading-spin.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(90);
    height: px2rem(90);
    position: absolute;
    left: 50%;
    top: 50%;
    margin: px2rem(-45) 0 0 px2rem(-45);
    animation: ani-loading-spin 2s linear infinite;
  }
  &-msg {
    position: absolute;
    left: 50%;
    top: 50%;
    width: px2rem(400);
    margin-left: px2rem(-200);
    box-sizing: border-box;
    color: #fff;
    text-align: center;
    padding-top: px2rem(55);
    font-size: px2rem(20);
  }
  @keyframes ani-loading-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  &--white {
    @include bgimg("@/shiftyspad/assets/images/loading-bg--white.jpg");
    .loading-spin {
      @include bgimg("@/shiftyspad/assets/images/loading-spin--white.png");
    }
    .loading-msg {
      color: #000;
    }
  }
}
</style>
