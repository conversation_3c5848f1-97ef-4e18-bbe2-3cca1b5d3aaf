<template>
  <div class="relative flex items-center ml-[16px]">
    <div class="flex items-center cursor-pointer" @click.stop="is_show = true">
      <SvgIcon
        :color="icon_color"
        name="icon-voice"
        class="w-[24px] h-[24px] cursor-pointer"
      ></SvgIcon>
      <div class="text-[14px] leading-[16px] ml-[4px] mr-[4px]">
        {{ t(cv_store.cv_lang.label) }}
      </div>
      <SvgIcon
        :color="icon_color"
        name="icon-polygon"
        class="w-[14px] h-[6px] cursor-pointer"
      ></SvgIcon>
    </div>
    <ul v-if="is_show" class="absolute w-full top-[34px] left-0 bg-[color:var(--op-fill-white)]">
      <li
        v-for="item in all_voice_list"
        :key="item.lang"
        class="flex items-center justify-center mb-[12px] cursor-pointer last-of-type:mb-[8px] first-of-type:pt-[4px]"
        :class="
          cv_store.cv_lang.lang === item.lang ? 'text-[color:var(--brand-1)]' : 'text-[color:#000]'
        "
        @click="
          () => {
            cv_store.switch(item);
            is_show = false;
          }
        "
      >
        <span class="text-[length:12px] leading-[14px] px-[8px]" :data-lang-val="item.lang">
          {{ t(item.label) }}
        </span>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { CVLang } from "packages/types/shiftyspad";
import { useCV } from "@/shiftyspad/stores/role/cv";
import { useI18n } from "vue-i18n";
import { ref, onMounted, onUnmounted } from "vue";
import SvgIcon from "@/components/common/svg-icon.vue";

withDefaults(
  defineProps<{
    icon_color?: string;
  }>(),
  {
    icon_color: "text-[color:var(--color-white)]",
  },
);

const cv_store = useCV();
const { t } = useI18n();
const is_show = ref(false);
const all_voice_list = [
  {
    lang: CVLang.JA,
    label: "cv_key_ja",
  },
  {
    lang: CVLang.KO,
    label: "cv_key_ko",
  },
  {
    lang: CVLang.EN,
    label: "cv_key_en",
  },
];

const close = () => {
  is_show.value = false;
};
onMounted(() => {
  document.addEventListener("click", close);
});
onUnmounted(() => {
  document.removeEventListener("click", close);
});
</script>
