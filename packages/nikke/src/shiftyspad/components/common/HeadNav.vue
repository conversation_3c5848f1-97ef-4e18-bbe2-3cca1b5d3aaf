<template>
  <div :class="{ 'h-[44px]': !transparent }">
    <Head
      class="z-[40]"
      :class="[
        `!border-none`,
        transparent ? ' text-[color:var(--color-white)]' : 'text-[color:var(--color-black)]',
      ]"
      :bg="transparent ? 'bg-transparent' : 'bg-[var(--color-white)]'"
      :title="title"
      :color="icon_color"
      :go-home="show_go_home"
      @goback="router.back"
    >
      <template #icon>
        <Cv v-if="has_voice" :icon_color="icon_color" />
        <SvgIcon
          v-if="show_privacy_setting"
          :color="icon_color"
          name="icon-privacy"
          class="cursor-pointer w-[24px] h-[24px] ml-[12px]"
          @click="
            () =>
              router.push({
                name: RoutesName.SETTING_SHIFTYSPAD_PRIVATE,
              })
          "
        ></SvgIcon>
        <SvgIcon
          v-if="showhint"
          :color="icon_color"
          name="icon-question"
          class="cursor-pointer w-[24px] h-[24px] ml-[12px]"
          @click="invokeHint"
        ></SvgIcon>
        <SvgIcon
          v-if="!is_client"
          :color="icon_color"
          name="icon-share"
          class="w-[24px] h-[24px] cursor-pointer ml-[12px]"
          @click="share"
        ></SvgIcon>
      </template>
    </Head>
  </div>
</template>

<script setup lang="ts">
import Cv from "@/shiftyspad/components/common/cv.vue";
import Head from "@/components/common/head/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";
import { computed, toRefs } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { useShiftysShare } from "@/shiftyspad/composable/share";
import { RoutesName } from "@/router/routes";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/shiftyspad/stores/user";
import router from "@/router";
import { useCV } from "@/shiftyspad/stores/role/cv";

const props = defineProps<{
  transparent?: boolean;
  showhint?: boolean;
}>();

const route = useRoute();
const { t } = useI18n();
const { share } = useShiftysShare();
const { is_client, has_role } = storeToRefs(useUserStore());
const { transparent } = toRefs(props);
const { has_voice } = storeToRefs(useCV());
const icon_color = computed(() =>
  transparent.value ? "text-[color:var(--color-white)]" : "text-[color:var(--color-black)]",
);

const show_go_home = computed(() => {
  return route.name === RoutesName.SHIFTYSPAD || route.name === RoutesName.SHIFTYSPAD_ROOT;
});

const is_root = computed(() =>
  [RoutesName.SHIFTYSPAD, RoutesName.SHIFTYSPAD_ROOT].includes(route.name as RoutesName),
);
const show_privacy_setting = computed(() => is_root.value && !is_client.value && has_role.value);
const title = computed(() => {
  switch (route.name) {
    case RoutesName.SHIFTYSPAD:
    case RoutesName.SHIFTYSPAD_ROOT: {
      return t("shiftys_spad");
    }
    case RoutesName.SHIFTYSPAD_NIKKE_LIST_PLAYER:
    case RoutesName.SHIFTYSPAD_NIKKE_LIST_ALL:
    case RoutesName.SHIFTYSPAD_NIKKE_LIST: {
      return t("nav_nikke_list");
    }
    case RoutesName.SHIFTYSPAD_SCENE:
    case RoutesName.SHIFTYSPAD_SCENE_MAIN:
    case RoutesName.SHIFTYSPAD_SCENE_SUDDEN:
    case RoutesName.SHIFTYSPAD_SCENE_ARCHIVE: {
      return t("nav_scene_list");
    }
    default: {
      return "";
    }
  }
});

const invokeHint = () => {
  NoticePopup.info({
    title: t("notice"),
    textAlign: "left",
    confirm: t("confirm"),
    message: t("data_sync_tip"),
  });
};
</script>

<style></style>
