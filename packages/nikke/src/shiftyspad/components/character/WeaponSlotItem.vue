<!-- 武器 -->
<script setup lang="ts">
import { toRefs } from "vue";
import { WeaponType, WeaponSlotItemSize, WeaponSlotItemMode } from "@/shiftyspad/types/character";
import { getWeaponAssetIcon } from "@/shiftyspad/composable/icon";

const props = withDefaults(
  defineProps<{
    weapon: WeaponType;
    mode?: WeaponSlotItemMode;
    size?: WeaponSlotItemSize;
  }>(),
  {
    mode: WeaponSlotItemMode.HEX,
    size: WeaponSlotItemSize.NORMAL,
  },
);
const { weapon, size, mode } = toRefs(props);
</script>

<template>
  <p
    :class="{
      small: size == WeaponSlotItemSize.SMALL,
      large: size == WeaponSlotItemSize.LARGE,
      'weapon-slot-item--circle': mode == WeaponSlotItemMode.CIRCLE,
      'weapon-slot-item--hex': mode == WeaponSlotItemMode.HEX,
    }"
  >
    <img class="icon-weapon" :src="getWeaponAssetIcon(weapon)" />
  </p>
</template>

<style lang="scss" scoped>
.weapon-slot-item--hex {
  @include bgimg("@/shiftyspad/assets/images/hex-bg-dark.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(30);
  height: px2rem(33);
  position: absolute;
  top: px2rem(50);
  left: px2rem(5);
  text-align: center;
  z-index: 0;
  .icon-weapon {
    z-index: 100;
    display: block;
    width: px2rem(22);
    height: px2rem(22);
    object-fit: contain;
    // filter: brightness(0);
    position: relative;
    top: 50%;
    margin: 0 auto;
    margin-top: px2rem(-11);
  }
}

.weapon-slot-item--hex.large {
  width: px2rem(63);
  height: px2rem(72);
  position: relative;
  top: auto;
  left: auto;
  overflow: hidden;
  .icon-weapon {
    z-index: 100;
    width: px2rem(44);
    height: px2rem(44);
    margin-top: px2rem(-22);
  }
}

.weapon-slot-item--hex.small {
  width: px2rem(26);
  height: px2rem(29);
  position: absolute;
  left: px2rem(4);
  top: 0;
  .icon-weapon {
    width: px2rem(18);
    height: px2rem(18);
    margin-top: px2rem(-9);
  }
}

.weapon-slot-item--circle {
  width: px2rem(100);
  height: px2rem(100);
  border-radius: 50%;
  background: #3f4044;
  margin: 0 auto;
  text-align: center;
  padding-top: px2rem(7);
  box-sizing: border-box;
  border: px2rem(4) solid transparent;
  background-clip: padding-box;
  box-shadow: 0 0 0 px2rem(1) #3f404466;
  .icon-weapon {
    display: block;
    width: px2rem(80);
    height: px2rem(80);
    object-fit: contain;
    margin: 0 auto;
  }
}
</style>
