<script setup lang="ts">
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { CharacterData } from "@/shiftyspad/types/character";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { useUserStore } from "@/shiftyspad/stores/user";
import { usePlayerRoleData } from "@/shiftyspad/composable/player";
import { parseGameSkillDesc } from "@/shiftyspad/utils/str";
import { t } from "@/locales";

import { computed, ref, toRaw, toRefs } from "vue";
import { isMobileDevice } from "packages/utils/tools";
import SvgIcon from "@/components/common/svg-icon.vue";
import Dialog from "@/shiftyspad/components/common/Dialog.vue";
import SkillIcon from "@/shiftyspad/components/character/sub-comp/skill-icon.vue";
import { getStandardizedLang } from "packages/utils/standard";

const props = defineProps<{
  character_data: CharacterData;
}>();

const isMobile = isMobileDevice();
const { shiftys_user } = toRefs(useUserStore());
const { character_data } = toRaw(props);

const {
  favorite_data,
  favorite_item,
  user_character,
  collection_has_player_skill,
  favorite_player_skills,
  favorite_item_skills,
  init_player_equip_data,
} = usePlayerRoleData({
  character: character_data,
  shiftys_user,
});

const show_skill = ref(false);
const favorite_item_level = computed(() => {
  return user_character.value?.item_level ?? -99;
});

const favorite_item_grade = computed(() => {
  return favorite_item.value?.grade?.at(favorite_item_level.value) ?? 0;
});

init_player_equip_data();

const attr_list = computed(() => {
  return [
    {
      icon: getAssetsImage("icon-upgrade-attack.png"),
      desc: t("atk_attr"),
      val: favorite_data.value?.atk,
    },
    {
      icon: getAssetsImage("icon-upgrade-defense.png"),
      desc: t("def_attr"),
      val: favorite_data.value?.def,
    },
    {
      icon: getAssetsImage("icon-upgrade-energy.png"),
      desc: t("hp_attr"),
      val: favorite_data.value?.hp,
    },
  ];
});
</script>
<template>
  <div class="mt-[12px] py-[10px] px-[6px] collect-box">
    <div class="flex justify-between items-center">
      <div
        class="mr-[8px] h-[16px] flex-1 text-[color:var(--text-1)] text-[11px] font-bold font-[DINNextLTPro] leading-[16px]"
        :class="{ 'text-[length:12px] h-[20px] leading-[20px]': !isMobile }"
      >
        {{ favorite_item?.name_localkey ?? "-" }}
      </div>
      <div
        class="w-auto rounded-[20px] h-[16px] bg-[#F88128] px-[12px] py-[2px] flex items-center justify-between leading-1"
        :class="{ 'text-[length:12px] h-[20px] leading-[20px]': !isMobile }"
      >
        <SvgIcon
          v-for="i in favorite_item_grade"
          :key="i"
          name="icon-start"
          class="w-[10px] h-[10px] px-[1px] pb-[0.5px] mr-[2px]"
        ></SvgIcon>
        <div
          v-for="i in 3 - favorite_item_grade"
          :key="i"
          class="dot w-[6px] px-[2px] h-[6px] flex-shrink-0 mr-[2px] bg-[#FEFEFE] opacity-46 rounded-[50%]"
        ></div>
        <div
          class="text-[color:var(--color-6)] text-[length:10px] ml-[2px] leading-[10px]"
          :class="{
            'mt-[2px]': getStandardizedLang() === 'zh-TW',
            'leading-[12px] text-[12px]': !isMobile,
          }"
        >
          {{ t("item_level", [favorite_item_level]) }}
        </div>
      </div>
    </div>
    <div class="mt-[8px] flex justify-between items-stretch">
      <div class="w-[68px] flex-shrink-0 mr-[6px]">
        <div
          class="bg-[#454545] rounded-sm text-[color:#fff] text-center"
          :class="{
            'h-[18px] leading-[18px] text-[length:11px] ': isMobile,
            'h-[24px] leading-[24px] text-[length:14px]': !isMobile,
          }"
        >
          {{ favorite_item?.favorite_rare ?? "-" }}
        </div>
        <div
          class="w-[68px] h-[68px] mt-[2px] border border-[var( --fill-5)] rounded-s relative"
          :class="{
            'bg-yellow': favorite_item?.favorite_rare === 'SSR',
            'bg-purple': favorite_item?.favorite_rare === 'SR',
            'bg-blue-r': favorite_item?.favorite_rare === 'R',
          }"
        >
          <img
            :src="ICONS_URL({ path: 'favoriteitem', name: favorite_item?.icon_resource_id || '' })"
            class="w-[66px] h-[66px] object-cover absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]"
          />
          <div
            class="absolute left-[2px] top-[2px] w-[16px] h-[16px] bg-[length:100%_100%] bg-no-repeat"
          ></div>
        </div>
      </div>
      <div class="flex-1 mr-[6px]">
        <div
          class="bg-[var(--color-5)] rounded-sm text-[color:var(--text-1)] text-center font-[Inter]"
          :class="{
            'h-[18px] leading-[18px] text-[length:11px] ': isMobile,
            'h-[24px] leading-[24px] text-[length:14px]': !isMobile,
          }"
        >
          {{ t("equipment_buff_data") }}
        </div>
        <div class="mt-[2px] py-[10px] px-[12px] bg-[var(--fill-3)]">
          <div
            v-for="(item, index) in attr_list"
            :key="index"
            class="flex justify-between text-[length:8px] leading-[10px] items-center mb-[8px] last:mb-0"
          >
            <div class="text-[color:#141416]">
              <img :src="item.icon" class="w-[8px] h-[8px] mr-[4px] inline-block" alt="" />
              {{ item.desc }}
            </div>
            <div class="text-[color:#000] font-bold">{{ item.val }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1">
        <div
          class="bg-[var(--color-5)] rounded-sm text-[color:var(--text-1)] text-center font-[Inter]"
          :class="{
            'h-[18px] leading-[18px] text-[length:11px] ': isMobile,
            'h-[24px] leading-[24px] text-[length:14px]': !isMobile,
          }"
        >
          {{ t("skill") }}
        </div>
        <!-- TODO: refactor, little icon -->
        <div class="skill-box bg-[var(--fill-3)] mt-[2px] min-h-[68px] px-[18px]">
          <div class="flex flex-col">
            <div class="flex flex-1 justify-center">
              <!-- 魔方技能 -->
              <SkillIcon
                v-for="(skill, index) in favorite_item_skills"
                :key="skill?.id ?? index"
                class="gap-item border-transparent-gap1"
                :exist="Boolean(skill)"
                :badge="skill?.skill_level"
                :is_unlock="skill?.is_unlock ?? false"
                :icon="ICONS_URL({ path: 'skill/char_skill', name: skill?.icon ?? '' })"
                @click="
                  () => {
                    if (skill && skill.is_unlock) {
                      show_skill = true;
                    }
                  }
                "
              ></SkillIcon>
              <SkillIcon
                v-for="i in 2 - favorite_item_skills.length"
                :key="i"
                icon=""
                :is_unlock="false"
                :exist="false"
                class="gap-item border-transparent-gap1"
              />
            </div>
            <!-- 玩家技能 -->
            <div v-if="collection_has_player_skill" class="flex flex-1 justify-center">
              <SkillIcon
                v-for="(skill, index) in favorite_player_skills"
                :key="skill?.favorite_skill_id ?? index"
                class="gap-item border-transparent-gap1"
                :exist="Boolean(skill)"
                :is_unlock="skill?.is_unlock ?? false"
                :badge="skill?.skill_level"
                :icon="ICONS_URL({ path: 'skill/char_skill', name: skill?.info.icon ?? '' })"
              ></SkillIcon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <teleport to="body">
      <Dialog v-if="show_skill" :title="t('skill')" @close="show_skill = false">
        <div
          v-for="(skill, index) in favorite_item_skills?.filter(Boolean)"
          :key="skill?.id"
          class="mb-[5px]"
        >
          <div class="px-[6px] py-[6px] skill-detail flex">
            <div class="w-[54px] mr-[8px]">
              <div
                class="w-full rounded-[20px] h-[16px] bg-[var(--color-5)] text-[length:12px] leading-[17px] text-[var(--color-7)]"
              >
                buff
              </div>
              <div class="mt-[12px]">
                <SkillIcon
                  :key="skill?.id ?? index"
                  class="gap-item border-transparent-gap"
                  :exist="Boolean(skill)"
                  :is_unlock="skill?.is_unlock ?? false"
                  :badge="skill?.skill_level"
                  :icon="ICONS_URL({ path: 'skill/char_skill', name: skill?.icon ?? '' })"
                ></SkillIcon>
              </div>
            </div>
            <div class="flex-1 text-left">
              <div class="leading-[16px] text-[length:14px] text-[color:#000] font-[DINNextLTPro]">
                {{ skill.name_localkey }}
              </div>
              <div
                class="bg-[var(--color-5)] h-[140px] m-h-[140px] overflow-y-auto px-[6px] py-[6px] text-[length:10px] mt-[4px]"
              >
                <div v-safe-html="parseGameSkillDesc(skill as any, skill.skill_level - 1)"></div>
              </div>
            </div>
          </div>
          <!-- tips -->
          <div
            v-if="!skill.is_unlock"
            class="mt-[4px] h-[32px] w-[100%] bg-[url('@/shiftyspad/assets/images/tips.png')] bg-[length:100%_100%] bg-no-repeat px-[6px] py-[8px] flex items-center"
          >
            <SvgIcon
              name="icon-lock"
              color="var(--text-1)"
              class="w-[14px] h-[14px] flex-shrik-0 mr-[4px]"
            ></SvgIcon>
            <span class="text-14 leading-12 text-[color:#141416] font-[DINNextLTPro] mr-[8px]">{{
              t("unlock_condition")
            }}</span>
            <span
              v-if="skill.unlock_level"
              class="text-14 leading-12 text-[color:#585859] font-[DINNextLTPro]"
            >
              <span class="text-[color:#3EAFFF]">LEVEL {{ skill.unlock_level }}</span>
            </span>
          </div>
        </div>
      </Dialog>
    </teleport>
  </div>
</template>

<style lang="scss" scoped>
.collect-box {
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
.skill-box {
  height: calc(100% - 16px - 4px);
  display: flex;
  align-items: center;
  justify-content: center;
}
.bg-purple::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(189, 85, 255, 0) 14.29%,
    rgba(189, 85, 255, 0.39) 52.89%,
    rgba(189, 85, 255, 0.82) 80.31%,
    #bd55ff 100%
  );
}
.bg-yellow::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(253, 180, 25, 0) 14.29%,
    rgba(253, 180, 25, 0.39) 52.89%,
    rgba(253, 180, 25, 0.82) 80.31%,
    #fdb419 100%
  );
}

.bg-blue-r::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 192, 255, 0) 14.29%,
    rgba(0, 192, 255, 0.39) 52.89%,
    rgba(0, 192, 255, 0.82) 80.31%,
    #00c0ff 100%
  );
}
.icon-upgrade {
  width: px2rem(24);
  height: px2rem(24);
  vertical-align: middle;
}
</style>
