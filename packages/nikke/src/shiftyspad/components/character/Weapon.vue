<!-- 装备 -->
<script setup lang="ts">
import { computed, toRaw } from "vue";
import { WeaponSlotItemMode } from "@/shiftyspad/types/character";
import { CharacterData } from "@/shiftyspad/types/character";
import WeaponSlotItem from "./WeaponSlotItem.vue";
import { parseGameDesc } from "@/shiftyspad/utils/str";
import { isMobileDevice } from "packages/utils/tools";
import { t } from "@/locales";

const isMobile = isMobileDevice();

const props = defineProps<{
  character_data: CharacterData;
}>();
const { character_data } = toRaw(props);
const { shot_detail } = character_data;

const nikke_op_type = computed(() => {
  const key = `input_type_${shot_detail.input_type.toLowerCase()}`;
  if (key.includes("charge")) return t("input_type_up");
  return t(key);
});
</script>

<template>
  <!--  -->
  <div id="nikkes-weapon" class="nikkes-detail-item nikkes-detail-weapon !w-auto !shadow-none">
    <div class="expandable-box expandable-box--nobtn !w-auto !shadow-none">
      <!-- nikkes-detail-box -->
      <div class="mx-auto mt-15 p-10 box-border !w-auto">
        <div class="flex align-center pb-10 nikkes-weapon-row">
          <p class="w-20 text-18 text-center">
            <span class="bg-gray-10 border-radius-20 pl-30 pr-30 pt-5 pb-5 leading-none">{{
              shot_detail.weapon_type
            }}</span>
          </p>
          <p
            class="ff-tt-bold w-80 text-black"
            :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
          >
            {{ shot_detail.name_localkey }}
          </p>
        </div>
        <div class="flex align-center nikkes-weapon-row">
          <div class="w-20">
            <WeaponSlotItem
              :weapon="shot_detail.weapon_type"
              :mode="WeaponSlotItemMode.CIRCLE"
            ></WeaponSlotItem>
          </div>
          <div class="w-80 border-top-gray pt-10">
            <p class="text-black-60" :class="{ 'text-18': isMobile, 'text-16': !isMobile }">
              {{ t(`${shot_detail.weapon_type.toLowerCase()}_desc`) }}
            </p>
            <div class="flex">
              <div class="w-half pr-5" :class="{ 'text-18': isMobile, 'text-16': !isMobile }">
                <p class="flex text-center mt-10 text-black">
                  <span class="w-60 bg-gray-20 border-left-radius-10 pt-5 pb-5">{{
                    t("max_ammo")
                  }}</span>
                  <span class="w-40 bg-gray-30 border-right-radius-10 pt-5 pb-5">{{
                    shot_detail.max_ammo
                  }}</span>
                </p>
                <p class="flex text-center mt-10 text-black">
                  <span class="w-60 bg-gray-20 border-left-radius-10 pt-5 pb-5">{{
                    t("reload_time")
                  }}</span>
                  <span class="w-40 bg-gray-30 border-right-radius-10 pt-5 pb-5"
                    >{{ Number(shot_detail.reload_time / 100).toFixed(2) }}s</span
                  >
                </p>
                <p class="flex text-center mt-10 text-black">
                  <span class="w-60 bg-gray-20 border-left-radius-10 pt-5 pb-5">{{
                    t("op_type")
                  }}</span>
                  <span class="w-40 bg-gray-30 border-right-radius-10 pt-5 pb-5">{{
                    nikke_op_type
                  }}</span>
                </p>
              </div>
              <div class="w-half pl-5 mt-10">
                <div
                  v-safe-html="parseGameDesc(shot_detail.description_localkey, shot_detail)"
                  class="bg-gray border-radius-5 p-5 box-border height-120 overflow-auto leading-normal scroll-bar"
                  :class="{ 'text-18': isMobile, 'text-16': !isMobile }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.height-120 {
  height: px2rem(120);
}
.border-transparent-gap {
  width: px2rem(100);
  height: px2rem(100);
  border-radius: 50%;
  background: #3f4044;
  margin: 0 auto;
  text-align: center;
  padding-top: px2rem(7);
  box-sizing: border-box;
  border: px2rem(4) solid #fff;
  background-clip: padding-box;
  box-shadow: 0 0 0 px2rem(1) #3f404466;
  img {
    display: block;
    width: px2rem(80);
    height: px2rem(80);
    object-fit: contain;
    margin: 0 auto;
  }
  &.small {
    width: px2rem(44);
    height: px2rem(44);
    line-height: px2rem(30);
    right: px2rem(-12);
    bottom: px2rem(-4);
    border-width: px2rem(2);
  }
}
.scroll-bar::-webkit-scrollbar-thumb {
  cursor: pointer;
}
</style>
<style lang="scss">
.is-pc {
  .nikkes-weapon-row {
    .w-20 {
      width: 15%;
    }
    .w-80 {
      width: 85%;
    }
  }
}
</style>
