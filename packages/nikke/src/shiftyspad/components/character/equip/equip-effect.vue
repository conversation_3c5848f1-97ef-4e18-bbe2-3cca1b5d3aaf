<template>
  <div
    v-if="buff"
    :class="[
      `border-radius-5 text-[#141416] text-center mt-10 flex px-[6px] level-${level(buff)}`,
      use_color && level(buff) > 14 ? `bg-[#141416]` : `bg-gray-20`,
    ]"
  >
    <p
      :class="[
        `flex-1 relative overflow-hidden whitespace-nowrap`,
        { 'text-16': isMobile, 'text-14': !isMobile },
        use_color && level(buff) > 14 && `text-white`,
      ]"
    >
      <span
        v-automarquee
        style="position: absolute; top: 50%; left: 0; transform: translateY(-50%)"
      >
        {{ buff.description_localkey }}
      </span>
    </p>
    <span
      :class="[
        `min-w-[40px] flex-none ff-num ml-[10px] text-right font-bold`,
        use_color && level(buff) >= 12 && `text-[#00B5FF]`,
      ]"
    >
      {{ formatFunc(buff) }}
    </span>
  </div>
</template>

<script lang="ts" setup>
import { type PlayerEquipData } from "@/shiftyspad/composable/player";
import { isMobileDevice } from "packages/utils/tools";
import { EquipFunctions } from "packages/types/shiftyspad";
import { toRefs } from "vue";

const isMobile = isMobileDevice();
const props = defineProps<{
  use_color: boolean;
  buff?: PlayerEquipData["buffs"][number];
}>();

const { buff } = toRefs(props);
const level = (buff: EquipFunctions["equip_effects"][number]) => {
  return buff.function_details?.at(0)?.level ?? 0;
};
const formatFunc = (buff: EquipFunctions["equip_effects"][number]) => {
  const [func] = buff.function_details ?? [];
  return `${Math.abs((func?.function_value ?? 0) / 100).toFixed(2)}%`;
};
</script>
