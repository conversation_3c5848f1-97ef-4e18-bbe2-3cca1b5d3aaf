<!-- 女神部队 -->
<script setup lang="ts">
import { ref, computed, toRaw } from "vue";
import { CharacterData } from "@/shiftyspad/types/character";
import { usePlayerRoleData } from "@/shiftyspad/composable/player";
import { useExpandBox } from "@/shiftyspad/composable/interact/expandbox";
import { watch } from "vue";
import { nextTick } from "vue";
import { t } from "@/locales";
import { useUserStore } from "@/shiftyspad/stores/user";
import EquipRow from "./EquipRow.vue";
import EquipEffect from "./equip-effect.vue";

const { expandBoxRef, expandBtnRef, initExpandBox, onItemTransitionEnter, onItemTransitionLeave } =
  useExpandBox();

const props = defineProps<{
  character_data: CharacterData;
}>();
const { character_data } = toRaw(props);
const { shiftys_user } = toRaw(useUserStore());
const expanded = ref(true);
const { user_character, equips, init_player_equip_data } = usePlayerRoleData({
  character: character_data,
  shiftys_user: shiftys_user as any,
});
const shown_equips = computed(() => (expanded.value ? equips.value : equips.value.slice(0, 1)));

/**
 * 合并词条的buff并且进行排序
 *
 * 优越(IncElementDmg)
 * 攻击，弹夹，蓄力速度，蓄力伤害，命中率，暴击率("StatCritical")，暴击伤害，防御力
 */
const all_buffs = computed(() => {
  const result = equips.value.filter(
    (equip) => equip && equip?.buffs?.length && equip?.item_rare?.toLowerCase() === "t10",
  );
  const all_func = result.map((equip) => equip.buffs).flat();
  const buff_map = new Map<string, (typeof all_func)[number]>();
  all_func.forEach((func) => {
    const { function_details } = func;
    const buff = function_details.at(0);
    if (!buff) {
      return;
    }
    if (buff_map.has(buff.function_type)) {
      const grouped_func = buff_map.get(buff.function_type)!;
      grouped_func.function_details.at(0)!.function_value += buff.function_value;
    } else {
      buff_map.set(buff.function_type, JSON.parse(JSON.stringify(func)));
    }
  });
  return Array.from(buff_map.entries())
    .sort((pre, next) => {
      const [pre_type, pre_func] = pre;
      const [next_type, next_func] = next;
      // const pre_effect = pre_func.function_details.at(0)!;
      // const next_effect = next_func.function_details.at(0)!;
      // if (pre_effect.level !== next_effect.level) return pre_effect.level - next_effect.level;
      // 数字越小优先级越高
      const type_val_sort = {
        IncElementDmg: 0,
        StatAtk: 1,
        StatAmmoLoad: 2,
        StatAmmo: 2,
        StatChargeTime: 3,
        StatChargeDamage: 4,
        StatAccuracyCircle: 5,
        OnHitRatio: 5,
        StatCritical: 6,
        StatCriticalDamage: 7,
        StatDef: 8,
      };
      // @ts-ignore
      return (type_val_sort[pre_type] ?? 999) - (type_val_sort[next_type] ?? 999);
    })
    .map(([_type, buff]) => buff);
});
const expandBoxInited = ref(false);

watch(
  () => user_character.value,
  () => {
    if (user_character.value) {
      init_player_equip_data();
    }
  },
  { immediate: true },
);

watch(shown_equips, (v) => {
  if (v && !expandBoxInited.value) {
    nextTick(() => {
      expandBoxInited.value = true;
      initExpandBox();
    });
  }
});
</script>

<template>
  <div
    v-if="user_character && shown_equips.length"
    id="nikkes-equip"
    ref="expandBoxRef"
    class="expandable-box nikkes-detail-item nikkes-detail-equip !w-auto !shadow-none"
  >
    <div
      v-if="all_buffs.length > 0"
      class="flex-col items-center nikkes-detail-box p-[6px] !w-auto"
    >
      <div class="flex justify-center items-center">
        <div
          class="flex justify-center items-center font-[Inter] max-w-[50%] text-[length:9px] leading-[1] text-[color:#141416] font-bold bg-gray-20 border-radius-20 px-[10px] py-[3px]"
        >
          {{ t("equip_effect_overview") }}
        </div>
      </div>
      <div class="flex flex-wrap gap-x-[6px]">
        <EquipEffect
          v-for="buff in all_buffs ?? []"
          :key="buff.id"
          class="flex-[1_1_calc(50%_-_12px)]"
          :buff="buff"
          :use_color="false"
        />
        <div v-for="i in all_buffs.length % 2" :key="i" class="flex-[1_1_calc(50%_-_12px)]"></div>
      </div>
    </div>
    <TransitionGroup
      appear
      name="listfade"
      tag="div"
      class="nikkes-equip-list"
      @enter="onItemTransitionEnter"
      @leave="onItemTransitionLeave"
    >
      <div
        v-for="equip in shown_equips"
        :key="(equip as any).equip || equip.equip_id"
        class="mx-auto mt-10 p-10 box-border nikkes-detail-box nikkes-detail-equips !w-auto"
      >
        <EquipRow
          :equip="equip"
          :remake-empty="equip.equip_id <= 0"
          :empty="equip.equip_id <= 0"
        ></EquipRow>
      </div>
    </TransitionGroup>
    <a
      ref="expandBtnRef"
      href="javascript:;"
      class="btn-toggle"
      :class="{ expanded: expanded }"
      @click="expanded = !expanded"
    >
      <i></i>
      <span class="ff-tt-bold">{{ expanded ? t("main_collapse") : t("main_expand") }}</span>
    </a>
  </div>
</template>
<style lang="scss">
.is-pc {
  .nikkes-equip-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .nikkes-detail-equips {
      width: calc(50% - px2rem(15));
      &:nth-child(2n) {
        margin-left: 0;
      }
    }
  }
}
</style>
