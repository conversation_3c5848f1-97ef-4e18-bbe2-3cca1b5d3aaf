<!-- 武器的头像信息 -->
<script setup lang="ts">
import type { EquipData, NikkeEquip } from "packages/types/shiftyspad";

import { patch } from "@/shiftyspad/utils/str";
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { equip_tier_color } from "@/shiftyspad/const/setting";
import { getEquipCorp } from "@/shiftyspad/composable/equip";
import { getIconByClass, getCleanIconByCorp } from "@/shiftyspad/composable/icon";

import { toRefs, computed } from "vue";

const props = defineProps<{
  equip: NikkeEquip & EquipData;
}>();
const { equip } = toRefs(props);
const rarity = computed(() => equip.value?.item_rare);
const is_overload = computed(() => equip.value?.item_rare?.toLowerCase() === "t10");
const equip_bg = computed(
  () =>
    `linear-gradient(to bottom, transparent 80%, ${
      // @ts-ignore
      equip_tier_color[is_overload.value ? "10" : equip.value.item_rare?.charAt(1)]
    } 100%)`,
);

const getCorp = () => {
  if (is_overload.value) {
    return getAssetsImage("icon-overload.png");
  }
  const corp = getEquipCorp(equip.value);
  return corp ? getCleanIconByCorp(corp as any) : null;
};
</script>
<template>
  <div class="relative equipinfo">
    <div class="pic flex justify-center" :class="[`pic--${(rarity || '').toLowerCase()}`]">
      <!-- TODO: 等设计换资源 -->
      <div
        class="z-10 absolute rounded w-[93%] h-[97%] translate-x-[-0.3px] translate-y-[-0.5px]"
        :style="{
          background: equip_bg,
        }"
      ></div>
      <img
        class="absolute !w-full !h-full !mt-0 z-1"
        :src="getAssetsImage('nikkes/nikke-equip-bg.png')"
      />
      <img class="z-10" :src="ICONS_URL({ path: 'equip', name: equip.resource_id })" alt="" />
    </div>

    <div class="icon-slot z-20">
      <p v-if="getCorp()" class="small weapon-slot-item--hex">
        <img class="icon-weapon" :src="getCorp() ?? ''" :class="{ 'no-filter': is_overload }" />
      </p>
      <p class="small weapon-slot-item--hex">
        <img class="icon-weapon" :src="getIconByClass(equip.class)" />
      </p>
    </div>
    <span class="text-white ff-num z-20">{{ patch(equip.equip_level, 2) }}</span>
  </div>
</template>

<style lang="scss" scoped>
.icon-slot {
  position: absolute;
  top: 0;
  left: 0;
}

.weapon-slot-item--hex.small {
  width: px2rem(26);
  height: px2rem(29);
  left: px2rem(4);
  top: 0;

  .icon-weapon {
    width: px2rem(18);
    height: px2rem(18);
    margin-top: px2rem(0);
  }
}

.weapon-slot-item--hex {
  @include bgimg("@/shiftyspad/assets/images/hex-bg-white.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(30);
  height: px2rem(33);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-weapon {
    display: block;
    object-fit: contain;
    filter: brightness(0);
    position: relative;
  }
}

.no-filter {
  filter: none !important;
}

.equipinfo {
  padding: px2rem(5);

  .pic {
    width: px2rem(92);
    height: px2rem(92);
    border-radius: px2rem(6);
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    position: relative;
    text-align: center;

    &::after {
      display: block;
      content: "";
      width: 100%;
      height: 50%;
      position: absolute;
      left: 0;
      bottom: 0;
    }

    img {
      width: 80%;
      height: 80%;
      object-fit: cover;
      margin-top: 10%;
    }

    &--r::after,
    &--water::after {
      background: linear-gradient(to bottom, transparent 50%, #00b3ff 100%);
    }

    &--ssr::after {
      background: linear-gradient(to bottom, transparent 50%, #ffa910 100%);
    }

    &--sr::after {
      background: linear-gradient(to bottom, transparent 50%, #c717ff 100%);
    }
  }

  .ff-num {
    text-shadow:
      0 0 1px rgba(0, 0, 0, 1),
      0 0 1px rgba(0, 0, 0, 1),
      0 0 2px rgba(0, 0, 0, 1);
    position: absolute;
    left: px2rem(6);
    bottom: 0;
  }
}

.icon-burst {
  background-repeat: no-repeat;
  background-size: 100%;
  width: px2rem(24);
  height: px2rem(27);
  position: absolute;
  left: px2rem(4);
  top: px2rem(32);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
