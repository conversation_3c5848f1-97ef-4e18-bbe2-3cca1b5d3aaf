<!-- TODO: i18n -->
<template>
  <div class="flex justify-between border-radius-10 bg-white p-10 box-border mt-10 shadow1">
    <div class="w-25 bg-gray border-radius-10 flex-none">
      <p class="text-20 text-center text-red">RANK</p>
      <p class="icon-love-lg mx-auto text-center text-white ff-num">20</p>
    </div>
    <div class="flex-1 bg-gray border-radius-10 ml-10 mr-10 flex">
      <div class="w-50 text-center">
        <p><span class="text-white bg-dark border-radius-10 pl-5 pr-5 text-16">火力型</span></p>
        <p class="text-12 leading-tight">LEVEL</p>
        <p class="ff-num text-black text-30 leading-tight">27</p>
      </div>
      <div class="w-50 text-center">
        <p><span class="text-white bg-dark border-radius-10 pl-5 pr-5 text-16">朝圣者</span></p>
        <p class="text-12 leading-tight">LEVEL</p>
        <p class="ff-num text-black text-30 leading-tight">24</p>
      </div>
    </div>
    <a
      class="block flex-none w-25 bg-blue border-radius-10 text-white text-24 text-center leading-tight font-bold pt-3 border-box cursor-pointer"
      @click="showAdditionPopupFlag = true"
    >
      加成<br />详情
    </a>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const showAdditionPopupFlag = ref<boolean>(false);
</script>
