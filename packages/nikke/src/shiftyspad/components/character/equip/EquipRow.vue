<!-- 武器单行 -->
<script setup lang="ts">
import { toRefs, computed } from "vue";
import EquipInfo from "./EquipInfo.vue";
import { type PlayerEquipData } from "@/shiftyspad/composable/player";
import { getEquipAttr } from "@/shiftyspad/composable/equip";
import { inject } from "vue";
import { isMobileDevice } from "packages/utils/tools";
import { t } from "@/locales";
import EquipEffect from "./equip-effect.vue";

const isMobile = isMobileDevice();
const { get_character }: any = inject("character");
const props = defineProps<{
  empty?: boolean; // 是否有装备
  equip?: PlayerEquipData;
}>();
const { empty, equip } = toRefs(props);

const i18nMap = {
  Defence: "def_attr",
  Atk: "atk_attr",
  Hp: "hp_attr",
  None: "None",
};
const is_overload = equip.value?.item_rare?.toLowerCase() === "t10";
const stats = computed(() => {
  const res = equip.value?.stat?.filter((v: any) => v.stat_type !== "None") ?? [];
  return res.sort((pre: any, next: any) => next.stat_value - pre.stat_value);
});
</script>

<template>
  <div class="h-full flex align-center equip-row">
    <p
      v-if="empty"
      class="text-black ml-10 mr-20 equip-row-empty-hd"
      :class="{ 'text-22': isMobile, 'text-18': !isMobile }"
    >
      {{ t("no_equip") }}
    </p>
    <EquipInfo v-if="equip && !empty" :equip="equip" class="ml-10 mr-20"></EquipInfo>
    <div
      v-if="empty || !equip"
      class="h-full bg-gray border-radius-10 flex align-center justify-center text-black equip-row-empty-bd"
      :class="{ 'text-22': isMobile, 'text-18': !isMobile }"
    >
      {{ t("no_equip") }}
    </div>
    <div v-else class="flex-1 h-full bg-gray border-radius-10 p-10 pt-20 box-border flex flex-col">
      <div class="flex w-[100%] justify-around text-18 text-center">
        <p
          class="w-[32%] mx-[4%] justify-center bg-gray-10 border-radius-20 pl-20 pr-20 pt-5 pb-5 overflow-hidden"
        >
          <span v-automarquee class="leading-none whitespace-nowrap">{{
            t("equipment_buff_data")
          }}</span>
        </p>
        <p
          class="flex-1 w-[52%] mx-[4%] justify-center bg-gray-10 border-radius-20 pl-20 pr-20 pt-5 pb-5 overflow-hidden"
        >
          <span v-automarquee class="leading-none whitespace-nowrap">{{ t("equip_effect") }}</span>
        </p>
      </div>
      <div
        class="flex-1 flex flex-wrap mt-20"
        :class="{ 'text-20': isMobile, 'text-16': !isMobile }"
      >
        <div
          :class="[
            `w-40 pr-5 box-border equip-row-val-detail`,
            equip.buffs && is_overload && `pt-[14px]`,
          ]"
        >
          <p v-for="effect in stats" :key="effect.stat_value" class="flex text-center">
            <span class="w-45 bg-gray-20 border-left-radius-10 pt-5 pb-5">{{
              t(i18nMap[effect.stat_type as "Hp"])
            }}</span>
            <span class="w-55 bg-gray-30 border-right-radius-10 pt-5 pb-5">{{
              Math.round(
                getEquipAttr({
                  equip,
                  type: effect?.stat_type,
                  character: get_character(),
                }),
              )
            }}</span>
          </p>
        </div>
        <div
          v-if="equip && equip.buffs?.length > 0"
          class="w-60 pl-5 pt-20 relative box-border"
          :class="{ 'text-17': isMobile, 'text-16': !isMobile }"
        >
          <p
            class="text-black text-center w-full ml-5 mr-5 box-content absolute overflow-hidden whitespace-nowrap"
            style="position: absolute; top: 0; left: 0"
          >
            <span v-automarquee>{{ t("enter_battle_hint") }}</span>
          </p>
          <EquipEffect
            v-for="buff in equip.buffs"
            :key="buff.equipment_option_id"
            :buff="buff"
            :use_color="true"
          />
          <p
            v-for="i in 3 - equip.buffs.length"
            :key="i"
            class="flex justify-center border-radius-5 text-[#141416] text-center mt-10 flex px-[4px] bg-gray-20"
          >
            <span>
              {{ t("equip_effect_now") }}
            </span>
          </p>
        </div>
        <div v-else class="w-60 pl-5 box-border">
          <p
            class="w-full h-full flex align-center justify-center text-black bg-gray-20 border-radius-10"
            :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
          >
            {{ t("no_effect") }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.equip-row-empty-hd {
  width: px2rem(102);
}
.equip-row-empty-bd {
  width: px2rem(548);
  // height: px2rem(129);
}
.equip-row-val-detail {
  display: flex;
  flex-direction: column;
  > p {
    flex: 1;
    > span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  > p + p {
    margin-top: px2rem(10);
  }
}
</style>
