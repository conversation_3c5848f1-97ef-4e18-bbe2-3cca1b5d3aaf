<template>
  <div class="error">
    {{ errmsg }}
    <Btn
      class="w-[28px] h-[28px] border-[0.5px] border-solid border-[color:var(--line-2)] box-border"
      data-btn-id="5"
      data-btn-name="refresh"
      @click="emits('refresh')"
    >
      <template #icon>
        <SvgIcon name="icon-switch" color="var(--text-3)" class="w-[20px] h-[20px]"></SvgIcon>
      </template>
    </Btn>
  </div>
</template>

<script setup lang="ts">
import { toRaw } from "vue";
import { useI18n } from "vue-i18n";
import Btn from "@/components/common/btn/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";

const props = defineProps<{
  errmsg?: String;
}>();
const { t } = useI18n();
const emits = defineEmits(["refresh"]);
const { errmsg = t("networkError") } = toRaw(props);
</script>

<style scoped>
.error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
