<template>
  <div class="viewmode mt-20">
    <div v-show="!ingame && status !== 'voice'" class="viewmode-btns !z-[10]">
      <img
        href="javascript:;"
        class="viewmode-btn !h-auto overflow-visible cursor-pointer object-cover"
        src="@/shiftyspad/assets/images/nikkes/viewmode-btn.png"
        @click="showList"
      />
      <p class="viewmode-list" :class="{ expanded: status === 'display' }">
        <a
          v-for="item in ViewModePose"
          v-show="item !== ViewModePose.STATIC"
          :key="item"
          href="javascript:;"
          :class="{ on: pose == item }"
          @click="changePose(item)"
        >
          <img :src="pose_icon(item)" alt="" />
        </a>
      </p>
    </div>
    <div
      v-show="status === 'none'"
      class="viewmode-btns !top-[95px]"
      @click="displayStatus.changeStatus('voice')"
    >
      <img
        href="javascript:;"
        class="viewmode-btn !h-auto overflow-visible cursor-pointer object-cover"
        src="@/shiftyspad/assets/images/nikkes/viewmode-btn2.png"
      />
    </div>
    <Pic
      v-if="pose === ViewModePose.STATIC"
      :key="skin.costume_index"
      :type="'stand'"
      :resource_id="character_data.resource_id"
      :skin_index="skin.costume_index"
      class="viewmode-spine"
    ></Pic>
    <Spine
      v-if="pose === ViewModePose.NORMAL"
      :key="skin.costume_index"
      :type="'stand'"
      :resource_id="character_data.resource_id"
      :skin_index="skin.costume_index"
      :additional_skins="character_data.additional_skins"
      class="viewmode-spine"
      :class="{
        'viewmode-kv--small': character_data.resource_id == 470,
        'viewmode-kv': move_center,
      }"
    ></Spine>
    <Spine
      v-if="pose === ViewModePose.SHOOT"
      :key="skin.costume_index"
      :type="'combat'"
      :action="'aim'"
      :resource_id="character_data.resource_id"
      :skin_index="skin.costume_index"
      :additional_skins="character_data.additional_skins"
      class="viewmode-spine"
      :class="{ 'viewmode-kv': move_center }"
    ></Spine>
    <Spine
      v-if="pose === ViewModePose.HALF_FACE"
      :key="skin.costume_index"
      :type="'combat'"
      :action="'cover'"
      :resource_id="character_data.resource_id"
      :skin_index="skin.costume_index"
      :additional_skins="character_data.additional_skins"
      class="viewmode-spine"
      :class="{ 'viewmode-kv': move_center }"
    ></Spine>
    <!-- <SdComp
      v-if="pose === ViewModePose.SD"
      :key="skin.costume_index"
      :skin_index="skin.costume_index"
      :resource_id="character_data.resource_id"
      class="viewmode-spine"
      :class="{ 'viewmode-kv': !viewmodeCleanFlag }"
    ></SdComp> -->
  </div>
</template>

<script setup lang="ts">
import { useSkinStore } from "@/shiftyspad/stores/skin";
import { useRoleDisplayState } from "@/shiftyspad/stores/role/display";

import { ViewModePose, type CharacterData } from "@/shiftyspad/types/character";
import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { ViewIconMap } from "@/shiftyspad/const/assets-map";

import Spine from "./Spine.vue";
import { Pic } from "./async-comp";
// import { SdComp } from './async-comp'

import { toRaw, ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { getQuery, isMobile, ingame as ingameHelper } from "@/shiftyspad/const/index";

const ingame = getQuery("env") !== "ingame-test" && !isMobile() && ingameHelper.isInIntlBrowser;
const props = defineProps<{
  character_data: CharacterData;
}>();
const skinStore = useSkinStore();
const displayStatus = useRoleDisplayState();
const { status } = storeToRefs(displayStatus);
const { skin } = storeToRefs(skinStore);
const { character_data } = toRaw(props);

const move_center = computed(() => status.value !== "voice" && status.value !== "display");
const pose = ref<ViewModePose>(ingame ? ViewModePose.STATIC : ViewModePose.NORMAL);
const pose_icon = (item: ViewModePose) =>
  getAssetsImage(`nikkes/viewmode-icon-${ViewIconMap[item]}${pose.value == item ? "-on" : ""}.png`);

const showList = () => {
  if (status.value === "display") {
    displayStatus.changeStatus("none");
  } else {
    displayStatus.changeStatus("display");
  }
};

const changePose = (item: ViewModePose) => {
  pose.value = item;
  status.value = "display";
};

watch(status, () => {
  pose.value = ViewModePose.NORMAL;
});
</script>

<style lang="scss" scoped>
@import url("../scss/detail.scss");
</style>
