<template>
  <Spinner v-if="status === 'loading'"></Spinner>
  <Error v-if="status === 'error'"></Error>
  <div
    ref="container"
    :class="$attrs.class"
    class="sdanimate-container"
    style="height: 100%; width: 100%"
  ></div>
</template>

<script setup lang="ts">
import { AniParams } from "@/shiftyspad/types/character";
import { getSdCdn } from "@/shiftyspad/service/character";
import { onMounted, onUnmounted, toRaw, ref } from "vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import Error from "./Error.vue";

// @ts-ignore
import * as THREE from "three";
// @ts-ignore
import { GLTF, GLTFLoader } from "three/addons/loaders/GLTFLoader.js";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";
import WebGL from "three/addons/capabilities/WebGL.js";
import { patch } from "@/shiftyspad/utils/str";
import { aegis } from "@/shiftyspad/service/rum";
// @ts-ignore
import { sd_reverse_list } from "./config";

const props = defineProps<AniParams>();
const container = ref<HTMLElement>();
const canceller = ref<number>(0);
const { resource_id, skin_index = 0 } = toRaw(props);
const status = ref("");
// view point
const mark = `${patch(resource_id, 3)}_${patch(skin_index, 2)}`;
const getThreeComponents = () => {
  const loader = new GLTFLoader();
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera();

  // set renderer
  const renderer = new THREE.WebGLRenderer({
    alpha: true,
    antialias: true,
  });
  renderer.setClearColor(0x000000, 0);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.toneMappingExposure = 1.5;
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;

  // set light
  const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0x080820, 5.5);
  scene.add(hemisphereLight);
  const clock = new THREE.Clock();
  return {
    renderer,
    loader,
    scene,
    camera,
    clock,
    el: renderer.domElement,
  };
};

const resize_func = ref();
onUnmounted(() => {
  if (!WebGL.isWebGLAvailable()) {
    status.value = "error";
    return;
  }
  // @ts-ignore
  const cancelAnimationFrame = window.cancelAnimationFrame || window.mozCancelAnimationFrame;
  cancelAnimationFrame(canceller.value);
  window.removeEventListener("resize", resize_func.value);
});

onMounted(() => {
  status.value = "loading";
  const comp_el = container.value as HTMLElement;
  const cdn = getSdCdn({ resource_id, skin_index, action: "run" });
  const { loader, scene, clock, el, renderer, camera } = getThreeComponents();
  const resize = () => {
    const newWidth = comp_el.clientWidth;
    const newHeight = comp_el.clientHeight;
    camera.aspect = newWidth / newHeight - 0.05;
    camera.updateProjectionMatrix();
    renderer.setSize(newWidth, newHeight);
  };
  resize_func.value = resize;
  resize_func.value();
  comp_el.appendChild(el);
  window.addEventListener("resize", resize_func.value);

  loader.load(
    cdn,
    function (gltf: GLTF) {
      status.value = "loaded";
      const modelScene = gltf.scene;

      if (import.meta.env.DEV) {
        const axesHelper = new THREE.AxesHelper(5); // 参数为轴的长度','例如：5个单位长度
        scene.add(axesHelper);
      }

      const mixer = new THREE.AnimationMixer(gltf.scene);
      const action = mixer.clipAction(gltf.animations[0]);
      const model = gltf.scene.children?.[0];

      // @ts-ignore
      model.children = model.children.filter((v) => v.material?.name !== "ground");

      action.play();
      modelScene.position.set(0, -1, 0);
      model.position.set(0, 0, 0);
      model.rotation.set(0, 0, 0);
      camera.rotation.set(0, 0, 0);
      const cam_z = sd_reverse_list.includes(mark) ? 5.5 : -5.5;
      camera.position.set(-0.3, -1, cam_z);

      scene.add(modelScene);

      // camera
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.update();
      // 禁止缩放
      controls.enableZoom = false;
      // 限制纵向旋转范围

      if (resource_id === 382) {
        controls.minPolarAngle = Math.PI / 2.5;
        controls.maxPolarAngle = Math.PI / 2.5;
      } else {
        controls.minPolarAngle = Math.PI / 2;
        controls.maxPolarAngle = Math.PI / 2;
      }

      function animate() {
        canceller.value = requestAnimationFrame(animate);
        if (mixer) {
          mixer.update(clock.getDelta());
        }
        controls.update();
        renderer.render(scene, camera);
      }
      animate();
    },
    undefined,
    function (error) {
      if (error) {
        status.value = "error";
      }
      aegis.error("[load-sd-failed]:", mark, error);
    },
  );
});
</script>

<style lang="scss" scoped>
.sdanimate-container {
  height: calc(100% - px2rem(100));
  position: relative;
  &::before {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background: rgba(200, 200, 200, 0.2);
    pointer-events: none;
    position: absolute;
    left: 0;
    top: 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: px2rem(15);
  }
  :deep(canvas) {
    position: relative;
    z-index: 2;
  }
}
.is-mobile {
  .sdanimate-container {
    height: calc(100% - px2rem(60));
    &::before {
      width: px2rem(710);
      height: 100%;
      left: px2rem(20);
    }
  }
}
</style>
<style lang="scss" scoped>
@import url("../scss/detail.scss");
</style>
