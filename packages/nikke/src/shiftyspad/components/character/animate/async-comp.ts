import { defineAsyncComponent } from "vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import Error from "./Error.vue";

const SdComp = defineAsyncComponent({
  loader: () => import("./SdAnimate.vue"),
  loadingComponent: Spinner,
  errorComponent: Error,
  delay: 200,
});

const Pic = defineAsyncComponent({
  loader: () => import("./Pic.vue"),
  loadingComponent: Spinner,
  errorComponent: Error,
  delay: 200,
});

export { SdComp, Pic };
