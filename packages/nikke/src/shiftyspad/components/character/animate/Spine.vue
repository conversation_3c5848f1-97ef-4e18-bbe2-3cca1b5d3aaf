<template>
  <div
    v-if="status !== 'error'"
    v-show="status === 'loaded'"
    :id="spine_id"
    :class="$attrs.class"
    style="width: 100%; height: 100%"
  ></div>
  <Spinner v-if="status === 'loading'"></Spinner>
  <Error v-if="status === 'error'" @refresh="reloadPlayer"></Error>
</template>

<script setup lang="ts">
import { ref, onMounted, toRaw, nextTick } from "vue";
import { GET_SPINE_URL } from "@/shiftyspad/const/urls";
import { loadSpinePlayer, SpineVersion } from "@/shiftyspad/service/comm";
import { AniParams } from "@/shiftyspad/types/character";
import Error from "./Error.vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import { patch } from "@/shiftyspad/utils/str";
import { aegis } from "@/shiftyspad/service/rum";
import type { SpinePlayer } from "spine-player401";
import { logger } from "@/shiftyspad/const";

const props = defineProps<AniParams & { type: "stand" | "combat"; action?: "cover" | "aim" }>();
const { resource_id, type, skin_index = 0, action, additional_skins = [] } = toRaw(props);
const action_mark = type === "stand" ? "stand" : action;
const mark = `${patch(resource_id, 3)}_${patch(skin_index, 2)}_${action_mark}`;
const status = ref<"loading" | "error" | "loaded">("loading");
const spine_id = `spine-${mark}`;

const v401_spine = [
  "112_01_stand",
  "131_01_aim",
  "282_00_stand",
  "800_00_stand",
  "800_01_stand",
  "801_00_cover",
  "801_00_aim",
  "801_00_stand",
  "802_00_cover",
  "802_00_aim",
  "802_00_stand",
];
// const vnew_spine = ["821_01_aim"];
// atlas 中出现日韩文
const v4028 = ["392_00_cover"];
const spine_config = {
  stand: {
    x: -800,
    y: -300,
    width: 1600,
    height: 2400,
    padLeft: "10%",
    padRight: "10%",
    padTop: "10%",
    padBottom: "10%",
  },
  combat: {
    x: props.action === "cover" ? -800 : -500,
    y: -700,
    width: 1600,
    height: 2400,
    padLeft: "10%",
    padRight: "10%",
    padTop: "10%",
    padBottom: "10%",
  },
};

const timer = ref(0);
const player = ref<SpinePlayer>();
const changeAni = (params: { ani_name: string; loop?: boolean }) => {
  const { ani_name, loop } = params;
  const ani = player.value?.skeleton?.data.findAnimation(ani_name);
  if (!ani?.duration) {
    return;
  }
  // if (
  //   ['102_00_stand', '090_01_stand', '090_02_stand', '451_00_stand', '131_00_stand'].includes(mark)
  // ) {
  //   player.value?.animationState.clearTracks()
  // }

  // or?

  if (!["353_00_stand", "412_00_aim"].includes(mark)) {
    player.value?.animationState.clearTracks();
  }

  player.value?.animationState.setAnimation(0, ani_name, loop ?? false);
  player.value?.play();
  return ani;
};

const reloadPlayer = () => {
  status.value = "loading";
  nextTick(() => {
    createSpinePlayer();
  });
};

const createSpinePlayer = () => {
  const { skel, atlas } = GET_SPINE_URL({
    type: props.type,
    resource_id,
    skin_index,
    combat: props.action,
  });
  let player_type: SpineVersion;
  if (v401_spine.includes(mark)) {
    player_type = "4.0.1";
  } else if (v4028.includes(mark)) {
    player_type = "4.0.28";
  } else {
    player_type = "4.1.20";
  }
  loadSpinePlayer(player_type)
    .then((SpinePlayer) => {
      const default_ani = type === "stand" ? "idle" : `${action}_idle`;
      const default_action_cover = action === "aim" ? "aim_fire" : "to_ami";
      const default_action_ani = type === "stand" ? "action" : default_action_cover;
      const splayer = new SpinePlayer(spine_id, {
        animation: default_ani,
        binaryUrl: skel,
        atlasUrl: atlas,
        alpha: true,
        showControls: false,
        backgroundColor: "#00000000",
        fullScreenBackgroundColor: "#00000000",
        // @ts-ignore
        viewport: spine_config[type],
        showLoading: false,

        success: () => {
          if ((splayer.skeleton?.data?.skins?.length || 1) > 1) {
            const skin_name = splayer.skeleton?.data?.skins?.[1].name;
            if (additional_skins.includes(skin_name)) {
              splayer.skeleton?.setSkinByName(skin_name);
            }
          }
          status.value = "loaded";
          player.value?.skeleton.setSlotsToSetupPose();
        },
        // @ts-ignore
        error: (_player: SpinePlayer, msg: unknown) => {
          status.value = "error";
          aegis.error("[load-spine-failed]:", mark, msg);
        },
      }) as SpinePlayer;
      player.value = splayer;
      document.getElementById(spine_id)?.addEventListener("click", () => {
        if (timer.value) {
          return;
        }
        const ani_detail = changeAni({
          loop: false,
          ani_name: default_action_ani,
        });

        const time = (ani_detail?.duration ?? 0) * 1000;
        timer.value = window.setTimeout(() => {
          changeAni({
            loop: true,
            ani_name: default_ani,
          });
          timer.value = 0;
          player.value?.skeleton.setSlotsToSetupPose();
        }, time);
      });
    })
    .catch((error) => {
      status.value = "error";
      logger.error("spine error: ", error);
    });
};

onMounted(() => {
  createSpinePlayer();
});
</script>
<style lang="scss" scoped>
@import url("../scss/detail.scss");
</style>
