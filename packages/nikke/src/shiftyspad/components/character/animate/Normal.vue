<template>
  <div class="viewmode-kv">
    <img :src="FULL_CHARACTER_URL({ resource_id, skin_index })" width="100%" alt="" />
  </div>
</template>

<script setup lang="ts">
import { FULL_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { AniParams } from "@/shiftyspad/types/character";
import { toRaw } from "vue";

const props = defineProps<AniParams>();
const { resource_id, skin_index = 0 } = toRaw(props);
</script>
