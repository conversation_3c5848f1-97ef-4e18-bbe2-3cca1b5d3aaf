<template>
  <div class="viewmode-kv">
    <Spinner v-if="status === 'loading'"></Spinner>
    <Error v-if="status === 'error'"></Error>
    <img
      class="block"
      style="transform: translate(10%, 10%)"
      :src="FULL_CHARACTER_URL({ resource_id, skin_index })"
      :width="`${isMobile ? '100%' : '120%'}`"
      alt=""
      @error="() => (status = 'error')"
      @load="() => (status = 'loaded')"
    />
  </div>
</template>

<script setup lang="ts">
import { FULL_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { AniParams } from "@/shiftyspad/types/character";
import { toRaw } from "vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import Error from "./Error.vue";
import { ref } from "vue";
import { isMobileDevice } from "packages/utils/tools";

const isMobile = isMobileDevice();
const status = ref("loading");
const props = defineProps<AniParams>();
const { resource_id, skin_index = 0 } = toRaw(props);
</script>
