<script lang="ts" setup>
import { useExpandBox } from "@/shiftyspad/composable/interact/expandbox";
import { usePlayerRoleData } from "@/shiftyspad/composable/player";
import { CharacterData } from "@/shiftyspad/types/character";
import { useUserStore } from "@/shiftyspad/stores/user";

import { ref, computed, onMounted, toRaw, toRefs } from "vue";
import { t } from "@/locales";

import WeaponSkill from "./WeaponSkill.vue";

const props = defineProps<{
  character_data: CharacterData;
}>();

const { character_data } = toRaw(props);
const { shiftys_user } = toRefs(useUserStore());

const { favorite_player_skills, init_player_equip_data } = usePlayerRoleData({
  character: character_data,
  shiftys_user,
});

init_player_equip_data();

const expanded = ref(true);
const skill_list = computed(() => {
  const getEnhancedSkill = (i: number) =>
    favorite_player_skills.value?.find((v) => v?.skill_change_slot === i && v.is_unlock)?.info;
  const strong_skill_1 = getEnhancedSkill(1);
  const strong_skill_2 = getEnhancedSkill(2);
  const strong_skill_ulti = getEnhancedSkill(3);

  return [
    {
      key: "skill1_level",
      is_enhanced: Boolean(strong_skill_1),
      cost: character_data.skill1_cost_detail,
      detail: strong_skill_1 || character_data.skill1_detail,
    },
    {
      key: "skill2_level",
      is_enhanced: Boolean(strong_skill_2),
      cost: character_data.skill2_cost_detail,
      detail: strong_skill_2 || character_data.skill2_detail,
    },
    {
      key: "ulti_skill_level",
      is_enhanced: Boolean(strong_skill_ulti),
      cost: character_data.ulti_skill_cost_detail,
      detail: strong_skill_ulti || character_data.ulti_skill_detail,
    },
  ];
});

onMounted(() => {
  setTimeout(() => {
    updateExpandBox();
  }, 200);
});

const {
  expandBoxRef,
  expandBtnRef,
  updateExpandBox,
  onItemTransitionEnter,
  onItemTransitionLeave,
} = useExpandBox();

const list = computed(() => {
  if (expanded.value) {
    return skill_list.value;
  } else {
    return skill_list.value.slice(0, 1);
  }
});
</script>

<template>
  <div id="nikkes-weapon" class="nikkes-detail-item nikkes-detail-weapon !w-auto !shadow-none">
    <div ref="expandBoxRef" class="expandable-box !w-auto !shadow-none">
      <TransitionGroup
        appear
        name="listfade"
        tag="div"
        class="mx-auto relative nikkes-detail-box-width !w-auto"
        @enter="onItemTransitionEnter"
        @leave="onItemTransitionLeave"
      >
        <WeaponSkill
          v-for="skill in list"
          :key="skill.key + skill.is_enhanced"
          :level_key="skill.key"
          :cost="skill.cost"
          :skill="skill.detail"
          :enhanced="skill.is_enhanced"
          @change-level="updateExpandBox"
        ></WeaponSkill>
      </TransitionGroup>

      <a
        ref="expandBtnRef"
        href="javascript:;"
        class="btn-toggle"
        :class="{ expanded: expanded }"
        @click="expanded = !expanded"
      >
        <i></i>
        <span class="ff-tt-bold">{{ expanded ? t("main_collapse") : t("main_expand") }}</span>
      </a>
    </div>
  </div>
</template>
