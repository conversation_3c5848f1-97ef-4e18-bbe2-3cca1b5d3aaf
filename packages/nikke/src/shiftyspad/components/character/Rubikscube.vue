<script setup lang="ts">
import { computed, ref, toRaw, toRefs } from "vue";

import { getAssetsImage } from "@/shiftyspad/utils/assets";
import { CharacterData } from "@/shiftyspad/types/character";
import { usePlayerRoleData } from "@/shiftyspad/composable/player";
import { useUserStore } from "@/shiftyspad/stores/user";
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { parseGameSkillDesc } from "@/shiftyspad/utils/str";
import { t } from "@/locales";

import { RarityType } from "packages/types/shiftyspad";
import { isMobileDevice } from "packages/utils/tools";

import Dialog from "@/shiftyspad/components/common/Dialog.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import SkillIcon from "@/shiftyspad/components/character/sub-comp/skill-icon.vue";

const props = defineProps<{
  character_data: CharacterData;
}>();
const isMobile = isMobileDevice();
const show_skill = ref(false);
const { shiftys_user } = toRefs(useUserStore());
const { character_data } = toRaw(props);

const { cube_item, cube_data, cube_skills, user_character, init_player_equip_data } =
  usePlayerRoleData({
    character: character_data,
    shiftys_user,
  });

init_player_equip_data();

const attr_list = computed(() => {
  return [
    {
      icon: getAssetsImage("icon-upgrade-attack.png"),
      desc: t("atk_attr"),
      val: cube_data.value?.atk,
    },
    {
      icon: getAssetsImage("icon-upgrade-defense.png"),
      desc: t("def_attr"),
      val: cube_data.value?.def,
    },
    {
      icon: getAssetsImage("icon-upgrade-energy.png"),
      desc: t("hp_attr"),
      val: cube_data.value?.hp,
    },
  ];
});
</script>

<template>
  <div class="mt-[12px]">
    <div class="flex justify-between">
      <!-- 战力 -->
      <div class="flex-1 w-[50%] mr-[6px] flex-shrink-0 rubik-item">
        <div
          class="w-full flex justify-center items-center font-bold bg-[var(--color-5)] text-center text-[color:var(--text-1)]"
          :class="{
            'h-[16px] leading-[16px] text-[length:10px] ': isMobile,
            'h-[24px] leading-[24px] text-[length:14px]': !isMobile,
          }"
        >
          {{ t("cube_combat") }}
        </div>
        <div class="mt-[8px] px-[6px] pb-[9px]">
          <div
            class="rubik-img-box mx-auto w-[46px] h-[48px] relative bg-[url('@/shiftyspad/assets/images/rubik-bg.png')] bg-[length:100%_100%]"
            :class="{
              'bg-yellow': cube_item?.item_rare === 'SSR',
              'bg-purple': cube_item?.item_rare === 'SR',
              'bg-blue-r': cube_item?.item_rare === 'R',
            }"
          >
            <img
              :src="ICONS_URL({ path: 'equip', name: `ie_${cube_item?.resource_id}` })"
              class="w-[50px] h-[50px] absolute top-[50%] -translate-y-[50%] left-[50%] -translate-x-[50%]"
              alt=""
            />

            <span
              v-if="cube_item?.item_rare === RarityType.SSR"
              class="w-[15px] h-[6px] absolute right-[3px] top-[3px] bg-[url('@/shiftyspad/assets/images/icon-ssr.png')] bg-[length:100%_100%]"
            >
            </span>
            <span
              v-if="cube_item?.item_rare === RarityType.R"
              class="w-[6px] h-[6px] absolute right-[3px] top-[3px] bg-[url('@/shiftyspad/assets/images/icon-r.png')] bg-[length:100%_100%]"
            >
            </span>
            <span
              v-if="cube_item?.item_rare === RarityType.SR"
              class="w-[12px] h-[6px] absolute right-[3px] top-[3px] bg-[url('@/shiftyspad/assets/images/icon-sr.png')] bg-[length:100%_100%]"
            >
            </span>
            <div class="rubik-name text-[color:#fff] bottom-[3px] absolute left-[3px]">
              <div class="text-[length:10px] leading-[9px]">LV.</div>
              <div class="text-[length:10px] leading-[9px]">
                {{ user_character?.cube_level ?? "-" }}
              </div>
            </div>
          </div>
          <div
            class="text-[color:var(--text-1)] text-center mt-[8px] text-[length:10px] truncate w-full font-[DINNextLTPro]"
          >
            {{ cube_item?.name_localkey ?? "-" }}
          </div>
          <div class="bg-[color:#DBDBDB] w-full h-[1px] mt-[6px]"></div>
          <div class="mt-[6px] px-[25px]">
            <div
              v-for="(item, index) in attr_list"
              :key="index"
              class="flex justify-between text-[length:8px] leading-[10px] items-center mb-[8px]"
            >
              <div class="text-[color:var(--text-1)]">
                <img :src="item.icon" class="icon-upgrade mr-10 inline-block" alt="" />
                {{ item.desc }}
              </div>
              <div class="text-[color:#000] font-bold">{{ item.val }}</div>
            </div>
          </div>
          <div class="mt-[0] bg-[color:#F5F5F5] flex justify-between py-[4px]">
            <SkillIcon
              v-for="skill in cube_skills"
              :key="skill?.id"
              class="border-transparent-gap relative"
              :exist="Boolean(skill?.exist)"
              :is_unlock="skill?.is_unlock ?? false"
              :badge="skill?.skill_level"
              :icon="ICONS_URL({ path: 'skill/char_skill', name: skill?.icon ?? '' })"
              @click="
                () => {
                  if (skill?.is_unlock) {
                    show_skill = true;
                  }
                }
              "
            ></SkillIcon>
          </div>
        </div>
      </div>
      <!-- 竞技场 -->
      <div class="flex-1 w-[50%] flex-shrink-0 rubik-item">
        <div
          class="w-full flex justify-center items-center font-bold bg-[var(--color-5)] text-center text-[color:var(--text-1)]"
          :class="{
            'h-[16px] leading-[16px] text-[length:10px] ': isMobile,
            'h-[24px] leading-[24px] text-[length:14px]': !isMobile,
          }"
        >
          {{ t("cube_areana") }}
        </div>
        <div class="no-data mt-[8px]">{{ t("no_data") }}</div>
      </div>
    </div>
    <teleport to="body">
      <Dialog v-if="show_skill" :title="t('skill')" @close="show_skill = false">
        <div
          v-for="skill in cube_skills?.filter((v) => v?.exist)"
          :key="skill?.id"
          class="mb-[5px]"
        >
          <div class="px-[6px] py-[6px] skill-detail flex">
            <div class="w-[54px] mr-[8px]">
              <div
                class="w-full rounded-[20px] h-[16px] bg-[var(--color-5)] text-[length:12px] leading-[17px] text-[var(--color-7)]"
              >
                buff
              </div>
              <div class="mt-[12px]">
                <SkillIcon
                  :key="skill?.id"
                  class="border-transparent-gap relative"
                  :exist="Boolean(skill?.exist)"
                  :is_unlock="skill?.is_unlock ?? false"
                  :badge="skill?.is_unlock ? (skill?.skill_level ?? 1) : ''"
                  :icon="ICONS_URL({ path: 'skill/char_skill', name: skill?.icon ?? '' })"
                ></SkillIcon>
              </div>
            </div>
            <div class="flex-1 text-left">
              <div
                class="leading-[16px] text-[length:14px] text-[var(--color-black)] font-[DINNextLTPro]"
              >
                {{ skill!.name_localkey }}
              </div>
              <div
                class="bg-[var(--color-5)] h-[140px] m-h-[140px] overflow-y-auto px-[6px] py-[6px] text-[length:10px] mt-[4px]"
              >
                <div
                  v-if="skill?.exist"
                  v-safe-html="
                    parseGameSkillDesc(skill, skill.skill_level - 1 < 0 ? 0 : skill.skill_level - 1)
                  "
                ></div>
              </div>
            </div>
          </div>
          <!-- tips -->
          <div
            v-if="!skill?.is_unlock"
            class="mt-[4px] h-[32px] w-[100%] bg-[url('@/shiftyspad/assets/images/tips.png')] bg-[length:100%_100%] bg-no-repeat px-[6px] py-[8px] flex items-center"
          >
            <SvgIcon
              name="icon-lock"
              color="var(--text-1)"
              class="w-[14px] h-[14px] flex-shrik-0 mr-[4px]"
            ></SvgIcon>
            <span
              class="text-14 leading-12 text-[color:var(--text-1)] font-[DINNextLTPro] mr-[8px]"
              >{{ t("unlock_condition") }}</span
            >
            <span
              v-if="skill?.unlock_level"
              class="text-14 leading-12 text-[color:#585859] font-[DINNextLTPro]"
            >
              <span class="text-[color:#3EAFFF]">LEVEL {{ skill!.unlock_level }}</span>
            </span>
          </div>
        </div>
      </Dialog>
    </teleport>
  </div>
</template>

<style lang="scss" scoped>
.rubik-item {
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

.rubik-name {
  text-shadow:
    -1px -1px 0 #716856,
    1px -1px 0 #716856,
    -1px 1px 0 #716856,
    1px 1px 0 #716856; /* 描边颜色 */
}
.bg-purple::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(189, 85, 255, 0) 14.29%,
    rgba(189, 85, 255, 0.39) 52.89%,
    rgba(189, 85, 255, 0.82) 80.31%,
    #bd55ff 100%
  );
}
.bg-yellow::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(253, 180, 25, 0) 14.29%,
    rgba(253, 180, 25, 0.39) 52.89%,
    rgba(253, 180, 25, 0.82) 80.31%,
    #fdb419 100%
  );
}

.bg-blue-r::before {
  content: "";
  height: 20px;
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 192, 255, 0) 14.29%,
    rgba(0, 192, 255, 0.39) 52.89%,
    rgba(0, 192, 255, 0.82) 80.31%,
    #00c0ff 100%
  );
}
.icon-upgrade {
  width: px2rem(24);
  height: px2rem(24);
  vertical-align: middle;
}
.no-data {
  height: calc(100% - 26px);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style>
.skill-detail {
  background: #fff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
</style>
