<template>
  <div :class="{ 'is-none': !exist, 'is-lock': !is_unlock }" class="relative flex-shrink-0">
    <div
      v-if="!exist"
      class="icon-lock bg-[url('@/shiftyspad/assets/images/skill-forbidden.png')] bg-[length:100%_100%]"
    ></div>
    <template v-else>
      <img v-if="icon" :class="{ 'cursor-pointer': is_unlock }" :src="icon" />
      <div
        v-if="!is_unlock"
        class="icon-lock bg-[url('@/shiftyspad/assets/images/skill-lock.png')] bg-[length:100%_100%]"
      ></div>
      <p v-if="is_unlock && badge" class="border-transparent-gap1 small absolute">
        <span
          class="text-white ff-num text-highlight-blue absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]"
          >{{ badge }}</span
        >
      </p>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";

const props = defineProps<{
  is_unlock: boolean;
  icon: string;
  exist: boolean;
  badge?: number | string;
}>();

const { is_unlock, icon, badge } = toRefs(props);
</script>

<style scoped lang="scss">
.border-transparent-gap1 {
  //   width: px2rem(50);
  //   height: px2rem(50);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #3f4044;

  text-align: center;
  padding-top: px2rem(5);
  box-sizing: border-box;
  border: 2px solid #fff;
  background-clip: padding-box;

  box-shadow: 0 0 0 px2rem(1) #3f404466;
  &.gap-item {
    margin-right: 20px;
    margin-top: px2rem(8);
    &:last-child {
      margin-right: 0;
    }
  }

  &.is-lock::before {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: px2rem(2) solid #ccc;
    background: rgba(0, 0, 0, 0.56);
  }
  &.is-none::before {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 1px solid #ccc;
    background: #adadad;
  }
  .icon-lock {
    display: block;
    position: absolute;
    width: 15px;
    height: 15px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;
  }
  img {
    display: block;
    width: 15px;
    height: 15px;
    object-fit: contain;
    margin: 0 auto;
  }
  &.small {
    width: 15px;
    height: 15px;
    line-height: 1;
    font-size: 10px;
    padding-top: 0;
    right: px2rem(-10);
    bottom: px2rem(-10);
    border-width: px2rem(2);
  }
}

.border-transparent-gap {
  width: px2rem(50);
  height: px2rem(50);
  border-radius: 50%;
  background: #3f4044;
  margin: 0 auto;
  text-align: center;
  padding-top: px2rem(5);
  box-sizing: border-box;
  border: px2rem(4) solid #fff;
  background-clip: padding-box;
  box-shadow: 0 0 0 px2rem(1) #3f404466;

  &.is-lock::before {
    content: "";
    position: absolute;
    top: px2rem(-4);
    left: px2rem(-4);
    width: px2rem(52);
    height: px2rem(52);
    border-radius: 50%;
    border: px2rem(2) solid #ccc;
    background: rgba(0, 0, 0, 0.56);
  }
  &.is-none::before {
    content: "";
    position: absolute;
    top: px2rem(-4);
    left: px2rem(-4);
    width: px2rem(52);
    height: px2rem(52);
    border-radius: 50%;
    border: px2rem(2) solid #ccc;
    background: #adadad;
  }
  .icon-lock {
    display: block;
    position: absolute;
    width: px2rem(30);
    height: px2rem(30);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;
  }
  img {
    display: block;
    width: px2rem(30);
    height: px2rem(30);
    object-fit: contain;
    margin: 0 auto;
  }
  &.small {
    width: px2rem(30);
    height: px2rem(30);
    line-height: px2rem(20);
    right: px2rem(-10);
    bottom: px2rem(-10);
    border-width: px2rem(2);
  }
}
</style>
