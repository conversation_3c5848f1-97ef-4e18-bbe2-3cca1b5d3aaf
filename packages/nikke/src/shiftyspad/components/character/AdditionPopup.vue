<script setup lang="ts">
import { useRecycle } from "@/shiftyspad/composable/recycledata";
import { useCharacterIcon } from "@/shiftyspad/composable/icon";
import { CharacterData } from "@/shiftyspad/types/character";
import { toRaw, toRefs } from "vue";
import { t } from "@/locales";
import { useUserStore } from "@/shiftyspad/stores/user";

const emit = defineEmits(["close", "ok"]);
const props = defineProps<{
  character_data: CharacterData;
  attractive_level?: number;
}>();
const { character_data, attractive_level } = toRaw(props);
const { user_battle_info, user_nikkelist_info } = toRefs(useUserStore());
const {
  enterprise_level_key,
  enterprise_level,
  class_level,
  class_level_key,
  enterprise_buff,
  attractive_buff,
  class_buff,
} = useRecycle({
  character_data,
  user_battle_info,
  user_nikkelist_info,
});

const { clean_corp_icon, class_icon } = useCharacterIcon(character_data);
</script>

<template>
  <div class="popup">
    <div class="pop-bd pop-addition">
      <a href="javascript:;" class="absolute pop-btn-close" @click="emit('close')"></a>
      <div class="flex justify-center align-center pop-addition-title">
        {{ t("buff_detail") }}
      </div>
      <div
        v-if="attractive_level && character_data.original_rare.toLowerCase() !== 'r'"
        class="flex flex-col justify-center pop-addition-row mx-auto mt-10"
      >
        <div
          class="flex justify-center align-center pop-addition-love pop-addition-love--red mx-auto mt-10"
        >
          <img src="@/shiftyspad/assets/images/icon-love.png" class="icon-love flex-none" alt="" />
          <span class="text-white text-20">{{ t("nikke_attract_level") }}</span>
          <p class="text-white text-26 ff-num">RANK {{ props.attractive_level }}</p>
        </div>
        <div class="flex justity-center pop-addition-tt mt-10">
          <p class="w-33 text-center text-20">
            {{ t("hp_attr") }} <span class="ff-num">{{ attractive_buff.hp || "0" }}</span>
          </p>
          <p class="w-33 text-center text-20">
            {{ t("def_attr") }} <span class="ff-num">{{ attractive_buff.def || "0" }}</span>
          </p>
          <p class="w-33 text-center text-20">
            {{ t("atk_attr") }} <span class="ff-num">{{ attractive_buff.atk || "0" }}</span>
          </p>
        </div>
      </div>
      <div
        v-for="(level, i) in [
          { key: class_level_key, icon: class_icon, val: class_level, buff: class_buff },
          {
            key: enterprise_level_key,
            icon: clean_corp_icon,
            val: enterprise_level,
            buff: enterprise_buff,
          },
        ]"
        v-show="level.val >= 0"
        :key="i"
        class="flex flex-col justify-center pop-addition-row mx-auto mt-10"
      >
        <div class="flex justify-center align-center pop-addition-love mx-auto mt-10">
          <img :src="level.icon" class="icon-love flex-none" alt="" />
          <span class="text-white text-20">{{ t(level.key) }}</span>
          <p class="text-white text-26 ff-num">RANK {{ level.val }}</p>
        </div>
        <div class="flex justity-center pop-addition-tt mt-10">
          <p class="w-33 text-center text-20">
            {{ t("hp_attr") }} <span class="ff-num">{{ level.buff.hp || "0" }}</span>
          </p>
          <p class="w-33 text-center text-20">
            {{ t("def_attr") }} <span class="ff-num">{{ level.buff.def || "0" }}</span>
          </p>
          <p class="w-33 text-center text-20">
            {{ t("atk_attr") }} <span class="ff-num">{{ level.buff.atk || "0" }}</span>
          </p>
        </div>
      </div>
      <a href="javascript:;" class="mx-auto mt-30 pop-btn-ok" @click="emit('close')">{{
        t("close")
      }}</a>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pop-addition {
  background: #ffffff;
  border-radius: px2rem(10);
  width: px2rem(648);
  padding-bottom: px2rem(40);
  &-title {
    font-size: px2rem(24);
    height: px2rem(85);
    color: #ffffff;
    background: #1279e1;
    font-weight: bold;
    border-top-left-radius: px2rem(10);
    border-top-right-radius: px2rem(10);
  }
  &-row {
    width: px2rem(600);
    height: px2rem(120);
    background: #fff;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.2);
    border-radius: px2rem(10);
  }
  &-love {
    width: px2rem(373);
    height: px2rem(45);
    background: #1279e1;
    border-radius: px2rem(10);
    > span {
      margin: 0 px2rem(40);
    }
    &--red {
      background: #db4857;
    }
  }
  &-tt {
    color: #656565;
    > p > span {
      margin-left: px2rem(30);
    }
  }
}
.icon-love {
  width: px2rem(30);
  height: px2rem(30);
}
</style>
<style lang="scss" scoped>
.is-pc {
  .pop-addition {
    transform: translate(-50%, -50%) scale(0.8);
  }
}
</style>
