<!-- 升级信息 -->
<script setup lang="ts">
import { ref, toRaw, computed, watch, onMounted, toRefs } from "vue";
import { useUserRoleLevelData } from "@/shiftyspad/composable/character";
import { CharacterData } from "@/shiftyspad/types/character";
import { useMockDataStore } from "@/shiftyspad/stores/character";
import { ICONS_URL, SM_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { formatNum } from "@/shiftyspad/utils/str";
// @ts-ignore 层级问题
import { useProgresBar } from "@/shiftyspad/composable/interact/progressbar";
import { throttle } from "@/shiftyspad/utils/comm";
import { t } from "@/locales";
import { isMobileDevice } from "packages/utils/tools";
import { useUserStore } from "@/shiftyspad/stores/user";
import { logger } from "@/shiftyspad/const";
import { getAssetsImage } from "@/shiftyspad/utils/assets";

const isMobile = isMobileDevice();
const props = defineProps<{
  character_data: CharacterData;
}>();
const shiftyUser = useUserStore();
const { shiftys_user } = toRefs(shiftyUser);
const { character_data } = toRaw(props);
const mockStore = useMockDataStore();
const icon_map: Record<string, string> = {
  gold: "im_1002",
  character_exp: "im_1005",
  character_exp_2: "im_1006",
};

const {
  progressBar,
  progressDot,

  onchange,
} = useProgresBar();

onchange((percent: number) => {
  change_level(Math.ceil(percent * max_level.value));
});

// 模拟数据
const {
  change_level,
  change_limit_break,

  level,
  grade_info,

  max_limit_break,
  max_level,
  max_stars,
  real_basic_attribute_atk,
  real_basic_attribute_def,
  real_basic_attribute_hp,
  real_character_fight_attribute,

  basic_attribute_atk,
  basic_attribute_def,
  basic_attribute_hp,
  character_fight_attribute,

  upgrade_material,

  init_player_equip_data,
} = useUserRoleLevelData({
  character_data,
  shiftys_user: shiftys_user,
});

const formatColor = (pre: number, next: number) => {
  const gap = Number((next - pre).toFixed(1));
  return gap >= 0;
};
const format = (pre: number, next: number) => {
  const gap = Number((next - pre).toFixed(1));
  if (gap >= 0) {
    return `+${Math.abs(gap)}`;
  } else {
    return `-${Math.abs(gap)}`;
  }
};

const upgradeArrowRef = ref<HTMLElement>();
const arrowStatus = ref<"" | "up" | "down">("");
function showArrowAnim() {
  (upgradeArrowRef.value as HTMLElement).innerHTML = "";
  const span = document.createElement("span");
  span.className = `upgrade-arrow-${arrowStatus.value}`;
  span.style.position = "absolute";
  (upgradeArrowRef.value as HTMLElement).appendChild(span);
  setTimeout(() => {
    try {
      if ((upgradeArrowRef.value as HTMLElement)?.contains(span)) {
        (upgradeArrowRef.value as HTMLElement)?.removeChild(span);
      }
    } catch (err) {
      logger.error("capture error in removeChild");
    }
  }, 1200);
}
const arrowAnim = throttle(function () {
  showArrowAnim();
}, 100);
watch(
  () => mockStore.level,
  (nv, ov) => {
    arrowStatus.value = nv > ov ? "up" : nv < ov ? "down" : "";
    arrowAnim();
  },
);

const evolveVal = computed(() => {
  return mockStore.limit_break === max_limit_break.value ? "max" : mockStore.core;
});
const evolveAnimRef = ref<HTMLElement>();
onMounted(() => {
  if (evolveAnimRef.value) {
    (evolveAnimRef.value as HTMLElement).addEventListener("animationend", () => {
      (evolveAnimRef.value as HTMLElement).setAttribute("data-anim", "0");
    });
  }
});
const showEvolveAnim = (isUp: boolean) => {
  const clz = `ani-icon-evolve--${isUp ? "up" : "down"}`;
  const evolveAnimEle = evolveAnimRef.value as HTMLElement;
  if (!evolveAnimEle) return;
  if (
    evolveAnimEle.className.indexOf(clz) >= 0 &&
    evolveAnimEle.getAttribute("data-anim") === "1"
  ) {
    return;
  }
  evolveAnimEle.className = "";
  setTimeout(() => {
    evolveAnimEle.setAttribute("data-anim", "1");
    evolveAnimEle.className = clz;
  }, 10);
};

// TODO: FIXME, see packages/nikke/src/shiftyspad/components/character/BasicDetail.vue
// 确认已登录
watch(
  () => [props.character_data],
  () => {
    if (props.character_data) shiftys_user.value.initAllData().then(() => init_player_equip_data());
  },
  {
    immediate: true,
  },
);

const attr_list = computed(() => {
  return [
    {
      icon: getAssetsImage("icon-upgrade-fight.png"),
      desc: t("fight_attr"),
      val: real_character_fight_attribute,
      format: format(real_character_fight_attribute.value, character_fight_attribute.value),
      formatColor: formatColor(
        real_character_fight_attribute.value,
        character_fight_attribute.value,
      ),
    },
    {
      icon: getAssetsImage("icon-upgrade-energy.png"),
      desc: t("hp_attr"),
      val: real_basic_attribute_hp,
      format: format(real_basic_attribute_hp.value, basic_attribute_hp.value),
      formatColor: formatColor(real_basic_attribute_hp.value, basic_attribute_hp.value),
    },
    {
      icon: getAssetsImage("icon-upgrade-attack.png"),
      desc: t("atk_attr"),
      val: real_basic_attribute_atk,
      format: format(real_basic_attribute_atk.value, basic_attribute_atk.value),
      formatColor: formatColor(real_basic_attribute_atk.value, basic_attribute_atk.value),
    },
    {
      icon: getAssetsImage("icon-upgrade-defense.png"),
      desc: t("def_attr"),
      val: real_basic_attribute_def,
      format: format(real_basic_attribute_def.value, basic_attribute_def.value),
      formatColor: formatColor(real_basic_attribute_def.value, basic_attribute_def.value),
    },
  ];
});

watch(evolveVal, (nv, ov) => {
  const _nv = nv === "max" ? Number.MAX_VALUE : nv;
  const _ov = ov === "max" ? Number.MAX_VALUE : ov;
  showEvolveAnim(_nv > _ov);
});
</script>
<template>
  <!-- !TODO: i18n -->
  <div
    id="nikkes-upgrade"
    class="expandable-box expandable-box--nobtn p-10 nikkes-detail-item nikkes-detail-upgrade"
  >
    <div
      class="text-ink leading-normal text-center ff-tt-bold nikkes-detail-title"
      :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
    >
      {{ t("use_material_upgrade_nikke") }}
    </div>
    <div class="w-90 mx-auto mt-10 mb-10 border-top-gray nikkes-upgrade-info">
      <div
        v-for="basic_attr in attr_list"
        :key="basic_attr.desc"
        class="flex align-center justify-center pt-10"
        :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
      >
        <p
          class="w-[44%] pl-[30%] justify-start flex align-center text-black-80 leading-none text-nowrap whitespace-nowrap"
        >
          <img :src="basic_attr.icon" class="icon-upgrade mr-10 inline-block" alt="" />
          <span v-fontfix>{{ basic_attr.desc }}</span>
        </p>
        <p class="w-[56%] color-black ff-num-bold flex justify-end">
          {{ basic_attr.val ?? "-" }}
          <span
            class="text-left ml-[12px] min-w-[62%]"
            :class="[basic_attr.formatColor ? 'text-highlight-blue' : 'text-red']"
            >{{ basic_attr.format }}
          </span>
        </p>
      </div>
    </div>
    <div class="w-full bg-gray p-10 box-border border-radius-10 flex">
      <!-- 升级 -->
      <div class="w-half pt-10 pr-10 pb-10 box-border border-right-gray">
        <div class="w-full flex align-center mx-auto upgrade-bar">
          <p class="ff-num text-22 text-black mr-5">LV{{ mockStore.level }}</p>
          <span ref="upgradeArrowRef" class="upgrade-arrow"></span>
          <div ref="progressBar" class="level-pbar ml-5 cursor-pointer">
            <p :style="{ width: (mockStore.level / max_level) * 100 + '%' }"></p>
            <span
              v-if="level !== mockStore.level"
              :style="{ left: (level / max_level) * 100 + '%' }"
            ></span>
            <a
              ref="progressDot"
              class="block"
              :style="{ left: (mockStore.level / max_level) * 100 + '%' }"
            >
              <em class="ff-num"
                >{{ mockStore.level > level ? "+" : "" }}{{ mockStore.level - level }}</em
              >
            </a>
          </div>
        </div>
        <div class="ff-num flex justify-center mt-10 upgrade-btns">
          <a href="javascript:;" @click="change_level(mockStore.level - 10)">-10</a>
          <a href="javascript:;" @click="change_level(mockStore.level - 1)">-1</a>
          <a href="javascript:;" @click="change_level(mockStore.level + 1)">+1</a>
          <a href="javascript:;" @click="change_level(mockStore.level + 10)">+10</a>
        </div>
        <p class="text-18 text-black text-center border-top-gray pt-10 mt-20">
          {{ t("upgrade_material") }}
        </p>
        <div class="flex justify-center mt-10">
          <div
            v-for="source in Object.keys(upgrade_material)"
            :key="source"
            class="ml-10 mr-10 column-center"
          >
            <p class="flex justify-center upgrade-resbg mx-auto">
              <img
                v-if="icon_map[source]"
                :src="ICONS_URL({ path: 'material', name: icon_map[source] })"
                alt=""
              />
            </p>
            <p class="text-black text-14 text-center">
              <span class="text-16 font-num">{{ formatNum(upgrade_material[source]) }}</span>
            </p>
          </div>
        </div>
      </div>
      <!-- 核心强化材料 -->
      <div v-if="character_data.piece_detail" class="w-half p-10 box-border">
        <div class="flex align-center justify-center upgrade-evolve">
          <!-- <img
            v-for="i in mockStore.grade"
            :key="i"
            src="@/shiftyspad/assets/images/icon-nikke-star-gold.png"
            alt=""
          />
          <img
            v-for="i in max_stars - mockStore.grade"
            :key="i"
            src="@/shiftyspad/assets/images/icon-nikke-star.png"
            alt=""
          /> -->
          <p
            v-for="i in max_stars"
            :key="i"
            class="upgrade-star"
            :class="[i <= mockStore.grade ? 'gold' : '']"
          ></p>
          <p
            v-if="character_data.original_rare.toLowerCase() === 'ssr'"
            class="evolve ff-num leading-tight flex align-center justify-center"
            :class="{ 'evolve--max': mockStore.limit_break === max_limit_break }"
          >
            <i ref="evolveAnimRef"></i>
            <span>{{ evolveVal }}</span>
          </p>
        </div>
        <div class="ff-num flex justify-center mt-10 upgrade-btns">
          <a href="javascript:;" @click="change_limit_break(mockStore.limit_break - 1)">-1</a>
          <a href="javascript:;" @click="change_limit_break(mockStore.limit_break + 1)">+1</a>
        </div>
        <p class="text-18 text-black text-center border-top-gray pt-10 mt-20">
          {{ t("upgrade_core_material") }}
        </p>
        <div class="flex justify-center mt-10">
          <div class="ml-20 mr-20 column-center">
            <p class="flex justify-center upgrade-resbg mx-auto ele-piece-slot z-0">
              <img :src="SM_CHARACTER_URL({ resource_id: character_data.resource_id })" alt="" />
            </p>
            <p class="text-black text-14 text-center">
              <span class="text-16 font-num">
                {{ character_data.piece_detail?.name_localkey }} -
                {{ mockStore.limit_break - grade_info.limit_break }}</span
              >
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.upgrade-bar {
  height: px2rem(42);
  // width: px2rem(280);
  > p {
    width: 28%;
    text-align: right;
    flex: none;
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
  }
  > div {
    width: 72%;
    margin-left: px2rem(15);
    margin-right: px2rem(15);
    box-sizing: border-box;
  }
}
.upgrade-arrow {
  display: block;
  width: px2rem(12);
  height: px2rem(20);
  position: relative;
  span {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-repeat: no-repeat;
    background-size: 100%;
    &.upgrade-arrow-up {
      @include bgimg("@/shiftyspad/assets/images/level-up2.png");
      animation: ani-upgrade-arrow-up 1s ease-out both;
    }
    &.upgrade-arrow-down {
      @include bgimg("@/shiftyspad/assets/images/level-down2.png");
      animation: ani-upgrade-arrow-down 1s ease-out both;
    }
    @keyframes ani-upgrade-arrow-up {
      0% {
        transform: translate(0, 60%);
        opacity: 1;
      }
      40% {
        opacity: 1;
      }
      100% {
        transform: translate(0, -60%);
        opacity: 0;
      }
    }
    @keyframes ani-upgrade-arrow-down {
      0% {
        transform: translate(0, -60%);
        opacity: 1;
      }
      40% {
        opacity: 1;
      }
      100% {
        transform: translate(0, 60%);
        opacity: 0;
      }
    }
  }
}
.level-pbar {
  background: #b5b5b5;
  height: px2rem(8);
  position: relative;
  p {
    height: 100%;
    background: #12a8fe;
  }
  a,
  span {
    display: block;
    @include bgimg("@/shiftyspad/assets/images/icon-level-adjust.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(26);
    height: px2rem(26);
    position: absolute;
    top: 50%;
    left: 0%;
    margin-top: px2rem(-13);
    margin-left: px2rem(-13);
    em {
      display: block;
      font-size: px2rem(16);
      line-height: 1;
      text-align: center;
      position: absolute;
      top: px2rem(-20);
      left: 50%;
      transform: translate(-50%, 0);
      white-space: nowrap;
      color: #000;
    }
  }
  a {
    em {
      color: #12a8fe;
    }
  }
  span {
    filter: brightness(0);
    // pointer-events: none;
  }
}
.icon-upgrade {
  width: px2rem(24);
  height: px2rem(24);
  vertical-align: top;
}
.upgrade-btns {
  a {
    display: block;
    width: px2rem(50);
    height: px2rem(34);
    margin: 0 px2rem(5);
    background: #12a8fe;
    border-radius: px2rem(6);
    text-align: center;
    color: #fff;
    font-size: px2rem(18);
    line-height: px2rem(34);
  }
}
.upgrade-resbg {
  @include bgimg("@/shiftyspad/assets/images/nikkes/upgrade-itembg.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(62);
  height: px2rem(62);
  text-align: center;
  position: relative;
  &::after {
    display: block;
    content: "";
    height: 20%;
    width: 90%;
    position: absolute;
    left: 5%;
    top: 69%;
    border-bottom-left-radius: px2rem(5);
    border-bottom-right-radius: px2rem(5);
  }
  &--r::after {
    background: linear-gradient(to bottom, transparent 50%, #00b3ff 100%);
  }
  &--sr::after {
    background: linear-gradient(to bottom, transparent 50%, #c717ff 100%);
  }
  &--ssr::after {
    background: linear-gradient(to bottom, transparent 50%, #ffa910 100%);
  }
  &.lg {
    width: px2rem(94);
    height: px2rem(96);
  }
  img {
    width: 80%;
    height: 80%;
    object-fit: contain;
    position: relative;
    z-index: 2;
  }
}
.upgrade-evolve {
  height: px2rem(42);
  > * {
    margin: 0 px2rem(5);
  }
  .upgrade-star {
    width: px2rem(30);
    height: px2rem(30);
    // @include bgimg("@/shiftyspad/assets/images/icon-nikke-star.png");
    // background-size: 100%;
    position: relative;
    &::before {
      display: block;
      content: "";
      width: px2rem(120);
      height: px2rem(120);
      position: absolute;
      left: px2rem(-45);
      top: px2rem(-45);
      pointer-events: none;
      @include bgimg("@/shiftyspad/assets/images/upgrade-star.png");
      background-repeat: no-repeat;
      background-size: 2500% 100%;
      animation-name: ani-upgrade-star;
      animation-duration: 0.5s;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;
      animation-timing-function: steps(1);
    }
    &.gold {
      // @include bgimg("@/shiftyspad/assets/images/icon-nikke-star-gold.png");
      &::before {
        animation-name: ani-upgrade-star--gold;
        animation-duration: 1s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: steps(1);
      }
    }
  }

  @keyframes ani-upgrade-star--gold {
    0% {
      background-position: 0% 0;
    }
    6.25% {
      background-position: 4.166666% 0;
    }
    12.5% {
      background-position: 8.333332% 0;
    }
    18.75% {
      background-position: 12.499998000000001% 0;
    }
    25% {
      background-position: 16.666664% 0;
    }
    31.25% {
      background-position: 20.83333% 0;
    }
    37.5% {
      background-position: 24.999996000000003% 0;
    }
    43.75% {
      background-position: 29.166662000000002% 0;
    }
    50% {
      background-position: 33.333328% 0;
    }
    56.25% {
      background-position: 37.499994% 0;
    }
    62.5% {
      background-position: 41.66666% 0;
    }
    68.75% {
      background-position: 45.833326% 0;
    }
    75% {
      background-position: 49.999992000000006% 0;
    }
    81.25% {
      background-position: 54.166658000000005% 0;
    }
    87.5% {
      background-position: 58.333324000000005% 0;
    }
    93.75%,
    100% {
      background-position: 62.499990000000004% 0;
    }
  }
  @keyframes ani-upgrade-star {
    0% {
      background-position: 66.66666666666666% 0%;
    }
    12.5% {
      background-position: 70.83333333333334% 0%;
    }
    25% {
      background-position: 75% 0%;
    }
    37.5% {
      background-position: 79.16666666666666% 0%;
    }
    50% {
      background-position: 83.33333333333334% 0%;
    }
    62.5% {
      background-position: 87.5% 0%;
    }
    75% {
      background-position: 91.66666666666666% 0%;
    }
    87.5% {
      background-position: 95.83333333333334% 0%;
    }
    100% {
      background-position: 100% 0%;
    }
  }
  .evolve {
    // @include bgimg("@/shiftyspad/assets/images/icon-evolve.png");
    // background-repeat: no-repeat;
    // background-position: center top;
    // background-size: 100%;
    width: px2rem(40);
    height: px2rem(42);
    line-height: px2rem(34);
    box-sizing: border-box;
    font-size: px2rem(16);
    text-align: center;
    color: #fff;
    position: relative;
    &--max {
      padding-top: 0;
      line-height: 1;
    }
    span {
      position: relative;
      z-index: 2;
      margin-top: px2rem(-2);
    }
    i {
      display: block;
      pointer-events: none;
      width: px2rem(120);
      height: px2rem(120);
      position: absolute;
      left: px2rem(-40);
      top: px2rem(-40);
      pointer-events: none;
      @include bgimg("@/shiftyspad/assets/images/icon-evolve-spr.png");
      background-size: 1600% 100%;
      background-repeat: no-repeat;
      background-position: 0 0;
      &.ani-icon-evolve--up {
        @include bgimg("@/shiftyspad/assets/images/icon-evolve-spr.png");
        background-size: 1600% 100%;
        background-repeat: no-repeat;
        background-position: 0 0;
        animation-name: ani-icon-evolve-up;
        animation-duration: 1s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: steps(1);
      }
      &.ani-icon-evolve--down {
        @include bgimg("@/shiftyspad/assets/images/icon-evolve-spr2.png");
        background-size: 1300% 100%;
        background-repeat: no-repeat;
        background-position: 0 0;
        animation-name: ani-icon-evolve-down;
        animation-duration: 0.8s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: steps(1);
      }
    }
    @keyframes ani-icon-evolve-up {
      0% {
        background-position: 0 0;
      }

      6.25% {
        background-position: 6.666666666666667% 0%;
      }

      12.50% {
        background-position: 13.333333333333334% 0%;
      }

      18.75% {
        background-position: 20% 0%;
      }

      25.00% {
        background-position: 26.666666666666668% 0%;
      }

      31.25% {
        background-position: 33.33333333333333% 0%;
      }

      37.50% {
        background-position: 40% 0%;
      }

      43.75% {
        background-position: 46.666666666666664% 0%;
      }

      50.00% {
        background-position: 53.333333333333336% 0%;
      }

      56.25% {
        background-position: 60% 0%;
      }

      62.50% {
        background-position: 66.66666666666666% 0%;
      }

      68.75% {
        background-position: 73.33333333333333% 0%;
      }

      75.00% {
        background-position: 80% 0%;
      }

      81.25% {
        background-position: 86.66666666666667% 0%;
      }

      87.50% {
        background-position: 93.33333333333333% 0%;
      }

      93.75%,
      100% {
        background-position: 100% 0%;
      }
    }
    @keyframes ani-icon-evolve-down {
      0% {
        background-position: 0 0;
      }

      7.69% {
        background-position: 8.333333333333332% 0%;
      }

      15.38% {
        background-position: 16.666666666666664% 0%;
      }

      23.08% {
        background-position: 25% 0%;
      }

      30.77% {
        background-position: 33.33333333333333% 0%;
      }

      38.46% {
        background-position: 41.66666666666667% 0%;
      }

      46.15% {
        background-position: 50% 0%;
      }

      53.85% {
        background-position: 58.333333333333336% 0%;
      }

      61.54% {
        background-position: 66.66666666666666% 0%;
      }

      69.23% {
        background-position: 75% 0%;
      }

      76.92% {
        background-position: 83.33333333333334% 0%;
      }

      84.62% {
        background-position: 91.66666666666666% 0%;
      }

      92.31%,
      100% {
        background-position: 100% 0%;
      }
    }
  }
}
.ele-piece-slot {
  position: relative;
  &::before {
    display: block;
    content: "";
    @include bgimg("@/shiftyspad/assets/images/nikkes/ele_piece_slot.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(50);
    height: px2rem(50);
    position: absolute;
    left: 50%;
    top: 45%;
    margin: px2rem(-25) 0 0 px2rem(-25);
    z-index: 5;
  }
  img {
    width: px2rem(30);
    height: px2rem(30);
    margin-top: px2rem(13);
    border-bottom-left-radius: px2rem(10);
    border-bottom-right-radius: px2rem(10);
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .nikkes-upgrade-info {
    display: flex;
    flex-wrap: wrap;
    > div {
      width: 50%;
    }
    .icon-upgrade {
      width: px2rem(20);
      height: px2rem(20);
    }
  }
}
</style>
