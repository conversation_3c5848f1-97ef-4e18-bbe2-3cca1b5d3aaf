<template>
  <a
    href="javascript:;"
    class="voice-item bg-gray border-radius-5 flex align-center mt-10 row z-0 cursor-pointer"
    :class="{ active: playing }"
    @click="playVoice()"
  >
    <Spinner v-if="loading"></Spinner>
    <div
      class="ff-tt-bold pl-20 h-full text-ink border-radius-5 flex align-center"
      :class="{ 'text-23': isMobile, 'text-16': !isMobile, 'w-40': voice.speech_localkey }"
      style="z-index: 100"
    >
      <i class="icon-play flex-none mr-10" :class="{ paused: playing }"></i>
      <p class="w-full whitespace-nowrap overflow-hidden mr-20 box-border">
        <span v-automarquee>{{ voice.voice_description }}</span>
      </p>
    </div>
    <div class="bar border-radius-5 bg-gray-20" :style="{ width: `${progress_bar}%` }"></div>
    <p
      v-if="voice.speech_localkey"
      style="z-index: 100"
      class="w-60 text-black-50 ml-10"
      :class="{ 'text-23': isMobile, 'text-16': !isMobile }"
    >
      <span v-automarquee>{{ voice.speech_localkey }}</span>
    </p>
  </a>
</template>

<script setup lang="ts">
import { CharacterData } from "@/shiftyspad/types/character";
import { toRaw } from "vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import { useVoice } from "@/shiftyspad/composable/voice";
import { isMobileDevice } from "packages/utils/tools";

const isMobile = isMobileDevice();
const props = defineProps<{
  voice: CharacterData["character_dialog_group_list"][number];
}>();
const { voice } = toRaw(props);

const { loading, playing, playVoice, onCancel, progress_bar, player } = useVoice(voice.speech_id);

onCancel(() => {
  if (player.value) {
    loading.value = false;
  }
});
</script>

<style>
.voice-item {
  position: relative;
}
.bar {
  transition: width 0.2s;
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
}
</style>
