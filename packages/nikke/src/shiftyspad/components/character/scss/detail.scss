.nikkes-detail-basics {
  overflow: hidden;
}

.viewmode {
  height: px2rem(1030);
  position: relative;
  @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-charbg.png");
  background-repeat: no-repeat;
  background-position: center px2rem(600);
  background-size: 100% auto;

  &-btns {
    position: absolute;
    left: px2rem(30);
    top: px2rem(40);
    z-index: 5;
  }

  &-btn {
    display: block;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(91);
    height: px2rem(97);
    position: relative;
    z-index: 2;
  }

  &-spine {
    transform: translate(0, 0);
    transition: transform 0.3s ease;
  }

  &-list {
    @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-expandbg.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(88);
    height: px2rem(428);
    max-height: 0;
    transition:
      max-height 0.3s ease,
      padding-top 0.3s ease;
    overflow: hidden;
    margin-top: px2rem(-60);
    padding-top: 0;
    box-sizing: border-box;

    &.expanded {
      padding-top: px2rem(64);
      max-height: px2rem(450);
    }

    a {
      display: block;
      width: px2rem(81);
      height: px2rem(81);
      @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-iconbg.png");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      text-align: center;
      padding-top: px2rem(6);
      box-sizing: border-box;
      margin: px2rem(5) auto 0;

      img {
        display: block;
        width: px2rem(60);
        height: px2rem(60);
        object-fit: contain;
        margin: 0 auto;
      }

      &.on {
        @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-iconbg-on.png");
      }
    }
  }

  &-kv {
    width: px2rem(750);
    height: px2rem(1000);
    transform: translate(px2rem(-150), 0);
    will-change: transform;
    transition: transform 0.3s ease;

    img {
      width: 100%;
      object-fit: contain;
    }
  }
}

.icon-rarity {
  height: px2rem(24);

  &[src*="ele_grade_icon_001"] {
    padding-left: px2rem(44);
    box-sizing: border-box;
  }

  &[src*="ele_grade_icon_002"] {
    padding-left: px2rem(24);
    box-sizing: border-box;
  }

  &[src*="ele_grade_icon_003"] {
    padding-left: px2rem(8);
    box-sizing: border-box;
  }
}

.icon-love-lg {
  @include bgimg("@/shiftyspad/assets/images/icon-love-lg.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(44);
  height: px2rem(38);
  padding-top: px2rem(2);
  box-sizing: border-box;
}

.charinfo {
  width: px2rem(236);
  max-height: px2rem(968);
  background: rgba(255, 255, 255, 0.7);
  @include shadow0;
  border-radius: px2rem(6);
  position: absolute;
  right: px2rem(10);
  top: px2rem(40);
  padding: px2rem(30) px2rem(10) px2rem(10) px2rem(10);
  box-sizing: border-box;
  will-change: transform;
  transition:
    transform 0.5s ease,
    opacity 0.5s ease;

  &.hidden {
    display: none;
  }

  .charinfo-name {
    background: #323232;
    width: px2rem(216);
    height: px2rem(96);
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
  }

  .cv-name {
    margin-top: px2rem(-20);
    margin-left: px2rem(0);
    white-space: nowrap;
    max-width: 80%;
    overflow: hidden;
  }

  &-icons {
    .hex {
      width: px2rem(63);
      height: px2rem(73);
      text-align: center;
      position: relative;
    }

    img {
      object-fit: contain;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
    }
  }

  .charinfo-tname {
    display: block;
    max-width: px2rem(50);
    height: px2rem(20);
    line-height: px2rem(20);
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
  }

  .charinfo-more {
    height: px2rem(80);
  }

  .charinfo-more-btn {
    height: 100%;
    width: px2rem(28);
    height: px2rem(80);
    text-align: center;
    position: relative;
    border-top-right-radius: px2rem(10);
    border-bottom-right-radius: px2rem(10);
    font-size: 0;

    i {
      display: block;
      width: 0;
      height: 0;
      border: transparent solid px2rem(12);
      border-left-color: #fff;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: px2rem(6);
      transform: translate(-50%, -50%);
    }
  }

  .charinfo-desc {
    overflow: auto;
    padding: px2rem(10) 0;

    p {
      max-height: px2rem(220);
    }
  }
}

.viewmode-clean-backbtn {
  display: block;
  @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-clean-backbtn.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(26);
  height: px2rem(48);
  position: absolute;
  top: 50%;
  margin-top: px2rem(-24);
  right: px2rem(60);
  z-index: 10;
  opacity: 0;
  transition: all 0.5s ease;
  visibility: hidden;
}

.viewmode--clean {
  .charinfo,
  .charinfo-swiper {
    transform: translate(px2rem(200), 0);
    opacity: 0.4;
    pointer-events: none;
  }

  .viewmode-kv {
    transform: translate(0, 0);
  }

  .viewmode-clean-backbtn {
    visibility: visible;
    opacity: 1;
  }
}

.charinfo-swiper {
  width: px2rem(220);
  will-change: transform;
  transition:
    transform 0.5s ease,
    opacity 0.5s ease;
}

.charinfo-swiper :deep(.swiper) {
  width: px2rem(160);
  margin: 0 auto;
}

.charinfo-swiper :deep(.swiper-slide) {
  overflow: hidden;
  transition: transform 0.3s ease;
  will-change: transform;
  transform: scale(0.9);
  cursor: pointer;
}

.charinfo-swiper :deep(.swiper-slide-active) {
  transform: scale(1);
  cursor: default;

  img {
    box-shadow:
      0 0 px2rem(5px) 0 #ffd545,
      0 0 px2rem(10px) 0 #ffd545;
  }
}

.charinfo-swiper-item {
  width: px2rem(79);
  height: px2rem(150);
  padding-top: px2rem(5);

  img {
    width: px2rem(79);
    height: px2rem(140);
  }

  div {
    line-height: 1.1;
    bottom: px2rem(30);
    left: 0;
    z-index: 2;
  }

  .icon-lock {
    display: block;
    @include bgimg("@/shiftyspad/assets/images/nikkes/suit-lock.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(17);
    height: px2rem(23);
    position: absolute;
    left: 50%;
    margin-left: px2rem(-9);
    bottom: px2rem(2);
    z-index: 2;
    display: none;
  }

  &::after {
    display: block;
    content: "";
    width: px2rem(79);
    height: px2rem(140);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    position: absolute;
    left: 0;
    top: px2rem(5);
  }

  &.lock {
    .icon-lock {
      display: block;
    }
  }
}

.charinfo-swiper-prev,
.charinfo-swiper-next {
  display: block;
  @include bgimg("@/shiftyspad/assets/images/nikkes/viewmode-swiper-prev.png");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: px2rem(19);
  height: px2rem(29);
  position: absolute;
  top: 50%;
  margin-top: px2rem(-15);
  z-index: 2;
}

.charinfo-swiper-next {
  right: px2rem(0);
  transform: scale(-1, 1);
}

// .is-mobile {
//   .charinfo-tname > p {
//     transform: scale(0.9);
//   }
// }

.is-pc {
  .viewmode {
    &-kv {
      transform: translate(-10%, 0);
    }
  }

  .viewmode--clean {
    .charinfo,
    .charinfo-swiper {
      transform: translate(px2rem(330), 0);
      opacity: 1;
    }

    .charinfo {
      -webkit-mask: linear-gradient(
        to right,
        rgba(0, 0, 0, 1) 0%,
        rgba(0, 0, 0, 1) 5%,
        rgba(0, 0, 0, 0) 20%
      );
    }

    .viewmode-kv {
      transform: translate(0, 0);
    }
  }

  .viewmode-clean-backbtn {
    right: px2rem(65);
  }

  .charinfo {
    width: px2rem(382);
    max-height: px2rem(900);
    right: 0;

    &-icons {
      > div {
        > p {
          transform: none;
        }
      }
    }
  }

  .charinfo-name {
    width: px2rem(362);
    height: px2rem(95);
  }

  .charinfo-tname {
    max-width: px2rem(60);
    margin-left: auto;
    margin-right: auto;
  }

  .charinfo-icons {
    > div {
      width: 20%;
    }
  }

  .charinfo-more {
    height: px2rem(100);
  }

  .charinfo-more-btn {
    width: px2rem(80);
    height: px2rem(80);
    color: #fff;
    font-size: px2rem(16);
    line-height: 1.2;
    padding: px2rem(10);
    border-radius: px2rem(5);
    text-shadow: 0 1px 1px #0838b4;

    i {
      display: none;
    }
  }

  .charinfo-desc {
    font-size: px2rem(16);
    line-height: 1.5;
    max-height: px2rem(280);
  }

  .charinfo-swiper {
    width: px2rem(300);
  }

  .charinfo-swiper :deep(.swiper) {
    width: px2rem(180);
    margin: 0 auto;
  }

  .charinfo-swiper-item {
    width: px2rem(94);
    height: px2rem(188);
    padding-top: px2rem(10);

    img {
      width: px2rem(94);
      height: px2rem(168);
    }

    &::after {
      width: px2rem(94);
      height: px2rem(168);
      top: px2rem(10);
    }
  }

  // @at-root {
  //   @media screen and (min-width: 1600px) {
  //     .is-pc {
  //       .viewmode-kv {
  //         transform: translate(-10%, 0);
  //       }
  //     }
  //   }
  // }
  @at-root {
    .is-mobile {
      .viewmode-kv {
        &--small {
          :deep(.spine-player) {
            width: 130% !important;
            height: 100% !important;
            transform-origin: 30% 50%;
            transform: scale(0.75);
          }
        }
      }
    }
  }
}

html[lang="zh-TW"],
.lang-zh,
html[lang="zh-CN"],
.lang-cn,
html[lang="cn"],
.lang-cn {
  &.is-pc {
    .charinfo-more-btn {
      font-size: px2rem(20);
      padding: px2rem(15);
    }
  }
}

html[lang="ja"] {
  &.is-pc {
    .charinfo-more-btn {
      word-break: break-all;
    }
  }

  &.is-mobile {
    .charinfo-swiper-item {
      div {
        span {
          font-size: px2rem(12);
        }

        p {
          font-size: px2rem(16);
        }
      }
    }
  }
}
