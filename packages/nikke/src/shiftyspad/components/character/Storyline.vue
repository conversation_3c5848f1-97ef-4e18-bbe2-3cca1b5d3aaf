<script setup lang="ts">
import { useRouter } from "vue-router";
import { ref, toRaw, computed } from "vue";

import { isMobileDevice } from "packages/utils/tools";

import { CharacterData } from "@/shiftyspad/types/character";
import { MI_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { useExpandBox } from "@/shiftyspad/composable/interact/expandbox";
import { useRoleScene } from "@/shiftyspad/composable/role/role-scene";
import { RoutesName } from "@/router/routes";
import { t } from "@/locales";

const isMobile = isMobileDevice();
const { expandBoxRef, expandBtnRef, onItemTransitionEnter, onItemTransitionLeave } = useExpandBox();

const router = useRouter();
const props = defineProps<{
  character_data: CharacterData;
}>();
const expanded = ref(true);
const { character_data } = toRaw(props);
const { scenario_list, showBlock } = useRoleScene(character_data.resource_id);

const toScene = (scene: (typeof scenario_list)["value"][number]) => {
  if (scene.is_lock) {
    return showBlock();
  }

  const { attractive_scenario_group_id } = scene;
  router.push({
    name: RoutesName.SHIFTYSPAD_SCENE_ATTRACTIVE,
    params: { id: attractive_scenario_group_id },
  });
};

const list = computed(() => {
  if (expanded.value) {
    return scenario_list.value;
  } else {
    return scenario_list.value.slice(0, 2);
  }
});
</script>

<template>
  <div
    id="nikkes-storyline"
    ref="expandBoxRef"
    class="expandable-box p-15 box-border relative nikkes-detail-item nikkes-detail-storyline"
  >
    <p
      class="text-black ff-tt-bold text-center mb-15 nikkes-detail-title"
      :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
    >
      {{ t("attract_scene") }}
    </p>
    <TransitionGroup
      appear
      name="listfade"
      tag="ul"
      class="nikkes-detail-storyline-list"
      @enter="onItemTransitionEnter"
      @leave="onItemTransitionLeave"
    >
      <!-- class lock -->
      <li
        v-for="scene in list"
        :key="scene.id"
        class="flex flex-col"
        :class="{ lock: scene.is_lock }"
        @click="toScene(scene)"
      >
        <em class="ff-industry"><i></i>_Attraction</em>
        <div class="flex-1 flex flex-col">
          <div class="w-80 ff-tt-bold text-[20px]">
            {{ t("attractive_level_unlock", { level: scene.attractive_level }) }}
          </div>
          <p v-if="!scene.is_lock" class="w-80 ff-tt-bold text-[20px]">
            {{ scene.scenario_title_loacle }}
          </p>
        </div>

        <img
          :src="MI_CHARACTER_URL({ resource_id: character_data.resource_id })"
          height="100%"
          class="w-auto"
          alt=""
        />
        <!-- 红点 -->
        <!-- <span></span> -->
      </li>
    </TransitionGroup>
    <a
      ref="expandBtnRef"
      href="javascript:;"
      class="btn-toggle"
      :class="{ expanded: expanded }"
      @click="expanded = !expanded"
    >
      <i></i>
      <span class="ff-tt-bold">{{ expanded ? t("main_collapse") : t("main_expand") }}</span>
    </a>
  </div>
</template>

<style lang="scss" scoped>
.nikkes-detail-storyline-list {
  li {
    margin-top: px2rem(10);
    padding-left: px2rem(20);
    box-sizing: border-box;
    height: px2rem(140);
    background: #fff;
    box-shadow:
      0 0 2px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: px2rem(10);
    position: relative;
    cursor: pointer;
    em {
      color: #f7413b;
      font-size: px2rem(12);
      i {
        display: inline-block;
        width: px2rem(10);
        height: px2rem(10);
        @include bgimg("@/shiftyspad/assets/images/icon-love-red.png");
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100%;
      }
    }
    div {
      line-height: 1.2;
      color: #f7413b;
    }
    p {
      line-height: 1;
      color: #46484a;
    }
    span {
      @include bgimg("@/shiftyspad/assets/images/icon-redpoint.png");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      width: px2rem(30);
      height: px2rem(30);
      position: absolute;
      right: px2rem(0);
      top: px2rem(0);
    }
    img {
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
    }
    &.lock::after {
      display: block;
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 35%, rgba(0, 0, 0, 0.35) 35%);
      background-size: 100% px2rem(4px);
      border-radius: px2rem(10);
    }
    .lock-txt {
      color: #000;
      font-size: px2rem(24);
      background: #e8e8e8;
      border-radius: px2rem(10);
      height: px2rem(52);
      min-width: px2rem(260);
      text-align: center;
      color: #000;
      text-align: center;
      line-height: px2rem(52);
      padding: 0 px2rem(10);
      box-sizing: border-box;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
    }
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .nikkes-detail-storyline-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    li {
      width: calc(50% - px2rem(5));
    }
  }
}
</style>

<style lang="scss" scoped>
.is-pc {
  .nikkes-detail-storyline-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    li {
      width: calc(50% - px2rem(5));
      &:nth-child(1),
      &:nth-child(2) {
        margin-top: 0;
      }
    }
  }
}
</style>
