<!-- 角色台词 -->
<script setup lang="ts">
import { toRaw, watch, ref } from "vue";
import { CharacterData } from "@/shiftyspad/types/character";
import { t } from "@/locales";
// @ts-ignore
import Dropdown from "primevue/dropdown";
import Voiceitem from "@/shiftyspad/components/character/Voiceitem.vue";
import { useCV } from "@/shiftyspad/stores/role/cv";
import { useSkinStore } from "@/shiftyspad/stores/skin";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import { isMobileDevice } from "packages/utils/tools";
import { CVLang } from "packages/types/shiftyspad";
import SvgIcon from "@/components/common/svg-icon.vue";

const isMobile = isMobileDevice();
const props = defineProps<{
  character_data: CharacterData;
}>();
const { character_data } = toRaw(props);
const skinStore = useSkinStore();
const { skin_dialogues } = storeToRefs(skinStore);
const cv = useCV();
const route = useRoute();
watch(
  () => route.name,
  () => {
    cv.current_player?.pause();
    cv.setVoice(null);
  },
  { flush: "sync" },
);

skinStore.initCharacter(character_data);

const cvStore = useCV();
const isShow = ref(false);

const all_voice_list = [
  {
    lang: CVLang.JA,
    label: "cv_key_ja",
  },
  {
    lang: CVLang.KO,
    label: "cv_key_ko",
  },
  {
    lang: CVLang.EN,
    label: "cv_key_en",
  },
];
</script>

<template>
  <div class="expandable-box expandable-box--nobtn p-10 nikkes-detail-item nikkes-detail-dialogue">
    <slot></slot>
    <p
      class="text-black ff-tt-bold text-center mb-10 pb-5 border-b-[1px] border-[color:#dbdbdb]"
      :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
    >
      {{ t("dialogue") }}
    </p>
    <!-- <div class="card flex justify-content-center">
      <Dropdown
        v-if="dialogue_list.length > 1"
        v-model="skin_dialogue"
        :options="dialogue_list"
        option-label="name"
        class="w-full md:w-14rem"
      />
    </div> -->

    <div class="flex items-center">
      <div class="flex-1">
        <p
          class="text-dark ff-tt-bold pt-10 pl-5"
          :class="{ 'text-20': isMobile, 'text-16': !isMobile }"
        >
          {{ cv.use_cv_key(character_data) }}
        </p>
        <p
          class="text-dark ff-tt-bold leading-none pb-20 pl-5"
          :class="{ 'text-46': isMobile, 'text-32': !isMobile }"
        >
          {{ character_data.name_localkey }}
        </p>
      </div>
      <div class="relative flex items-center ml-[16px]">
        <!--   v-if="isShowAudio" -->
        <div class="flex items-center cursor-pointer" @click.stop="isShow = true">
          <svgIcon name="icon-voice" class="w-[24px] h-[24px] cursor-pointer"></svgIcon>
          <div class="text-[14px] leading-[16px] ml-[4px] mr-[4px]">
            {{ t(cvStore.cv_lang.label) }}
          </div>
          <svgIcon name="icon-polygon" class="w-[14px] h-[6px] cursor-pointer"></svgIcon>
        </div>
        <ul
          v-if="isShow"
          class="absolute w-full top-[34px] left-0 bg-[color:var(--op-fill-white)] z-50"
        >
          <li
            v-for="item in all_voice_list"
            :key="item.lang"
            class="flex items-center justify-center mb-[12px] cursor-pointer last-of-type:mb-[8px] first-of-type:pt-[4px]"
            :class="
              cvStore.cv_lang.lang === item.lang
                ? 'text-[color:var(--brand-1)]'
                : 'text-[color:#000]'
            "
            @click="
              () => {
                cvStore.switch(item);
                isShow = false;
              }
            "
          >
            <span class="text-[length:12px] leading-[14px] px-[8px]" :data-lang-val="item.lang">
              {{ t(item.label) }}
            </span>
          </li>
        </ul>
      </div>
    </div>

    <p
      class="bg-dark p-10 box-border leading-normal text-white"
      :class="{ 'text-22': isMobile, 'text-16': !isMobile }"
    >
      {{ t("dialogue_hint", { chapter_name: character_data.name_localkey }) }}
    </p>
    <div
      v-for="item in skin_dialogues"
      :key="item.group_id"
      class="mt-10"
      :class="{ expanded: item.expanded, lock: item.lock }"
    >
      <div
        class="collapse-title bg-dark flex align-center justify-between"
        @click="skinStore.onDialoguesCollapse(item)"
      >
        <p class="ff-tt-bold text-white" :class="{ 'text-18': isMobile, 'text-16': !isMobile }">
          <i class="icon-liststyle2"></i>{{ item.title }}
        </p>
        <i class="icon-arrow"></i>
        <p v-if="item.lock" class="absolute text-white text-22 text-center lock-txt">
          {{ t("wait_unlock") }}
        </p>
      </div>
      <Transition name="p-toggleable-content">
        <div v-if="item.expanded" class="collapse-body">
          <Voiceitem v-for="w in item.list" :key="w.id" :voice="w"></Voiceitem>
        </div>
      </Transition>
    </div>
  </div>
</template>
<style>
/* Toggleable Content */
.p-toggleable-content-enter-from,
.p-toggleable-content-leave-to {
  max-height: 0;
}

.p-toggleable-content-enter-to,
.p-toggleable-content-leave-from {
  max-height: 1000px;
}

.p-toggleable-content-leave-active {
  overflow: hidden;
  transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);
}

.p-toggleable-content-enter-active {
  overflow: hidden;
  transition: max-height 1s ease-in-out;
}
</style>
<style lang="scss">
.text-46 {
  font-size: px2rem(46);
}
.collapse {
  &.expanded {
    .collapse-title {
      .icon-arrow {
        transform: translate(0, px2rem(-5)) scale(1, -1);
      }
    }
    .collapse-body {
      margin-top: px2rem(5);
    }
  }
  &.lock {
    .collapse-title {
      pointer-events: none;
      position: relative;
      .lock-txt {
        display: block;
        content: "";
        background: rgba(0, 0, 0, 0.4);
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
      }
    }
  }
}
.collapse-title {
  height: px2rem(40);
  padding: 0 px2rem(10);
  box-sizing: border-box;
  border-radius: px2rem(5);
  cursor: pointer;
}
.collapse-body {
  overflow: hidden;
  padding: 0 2px;
  box-sizing: border-box;

  .row {
    height: px2rem(52);
    font-size: px2rem(23);
    box-sizing: border-box;
    &.active {
      // border: #fff solid 1px;
      // outline: #009cff solid 1px;
      box-shadow:
        0 0 0 1px #fff,
        0 0 0 2px #009cff;
      > p:first-child {
        color: #009cff;
        text-shadow:
          0 0 2px #fff,
          0 0 5px #fff;
      }
      .icon-play.paused {
        @include bgimg("@/shiftyspad/assets/images/icon-pause--active.png");
      }
    }
    .icon-play {
      display: inline-block;
      @include bgimg("@/shiftyspad/assets/images/icon-play.png");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%;
      width: px2rem(18);
      height: px2rem(22);
      &.paused {
        @include bgimg("@/shiftyspad/assets/images/icon-pause.png");
      }
    }
    > p {
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
.icon-liststyle2 {
  display: inline-block;
  vertical-align: top;
  @include bgimg("@/shiftyspad/assets/images/icon-liststyle2.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100%;
  width: px2rem(20);
  height: px2rem(20);
  margin-right: px2rem(10);
  margin-top: px2rem(4);
}
.icon-arrow {
  display: block;
  width: 0;
  height: 0;
  border: transparent solid px2rem(10);
  border-bottom-color: transparent;
  border-top-color: #fff;
  transform: translate(0, px2rem(5));
  transition: transform 0.3s ease;
}
</style>
<style lang="scss" scoped>
.is-pc {
  .collapse-body {
    .row {
      height: px2rem(44);
      :deep(.icon-play) {
        width: px2rem(13);
        height: px2rem(15);
      }
    }
  }
  .icon-arrow {
    border: transparent solid px2rem(8);
    border-top-color: #fff;
    transform: translate(0, px2rem(4));
  }
  &.expanded {
    .collapse-title {
      .icon-arrow {
        transform: translate(0, px2rem(-4)) scale(1, -1);
      }
    }
  }
}
</style>
