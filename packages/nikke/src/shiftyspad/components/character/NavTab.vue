<template>
  <div class="flex items-center justify-center">
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="[
        `flex-1 h-[28px] flex items-center justify-center border-[1px] leading-[16px] cursor-pointer mr-[8px] last-of-type:mr-0 relative z-[1]`,
        active === index
          ? `font-bold text-[color:#3EAFFF] bg-[#D8EFFF] text-[length:${active_font_size}] border-[#3EAFFF]`
          : `text-[color:#5A5A5B] bg-[#F4F4F4] text-[length:13px] border-transparent`,
      ]"
      @click="$emit('change', index)"
    >
      <i
        v-show="active === index"
        class="border-[2px] border-[color:#00B2FF_transparent_transparent_#00B2FF] absolute top-[2px] left-[2px] -z-[1]"
      ></i>
      <img
        v-show="active === index"
        src="@/shiftyspad/assets/images/nikkes/mask-tab.png"
        alt=""
        class="absolute bottom-[1px] left-[1px] w-[25px] h-[25px] -z-[1]"
      />
      <img
        v-show="active === index"
        src="@/shiftyspad/assets/images/nikkes/mask-tab.png"
        alt=""
        class="absolute bottom-[1px] right-[2px] w-[25px] h-[25px] -z-[1] -rotate-90"
      />
      {{ item.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    list: any;
    active: number;
    active_font_size?: string;
  }>(),
  {
    list: [],
    active: 0,
    active_font_size: "14px",
  },
);

defineEmits(["change"]);
</script>
