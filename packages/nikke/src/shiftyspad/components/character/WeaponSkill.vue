<template>
  <div
    class="mx-auto mt-15 box-border nikkes-detail-box nikkes-weapon-pane !w-auto"
    :class="{ 'p-10': isMobile, 'p-5': !isMobile }"
  >
    <div
      class="nikkes-weapon-res-left box-border flex"
      :class="{ 'p-10': isMobile, 'p-5': !isMobile }"
    >
      <div class="w-40 text-center pr-10 border-right-gray">
        <p
          class="text-ink font-bold leading-tight"
          :class="{ 'text-20': isMobile, 'text-18': !isMobile }"
        >
          {{ skill.name_localkey }}
        </p>
        <!-- <p class="text-black-50 pt-5 pb-5" :class="{'text-20': isMobile, 'text-16': !isMobile}">{{ skill.skill_type }}</p> -->
        <div class="border-transparent-gap relative" :class="{ enhanced: enhanced }">
          <img :src="ICONS_URL({ path: 'skill/char_skill', name: skill.icon })" alt="" />
          <p class="border-transparent-gap small absolute">
            <span class="text-20 text-white ff-num text-highlight-blue">{{
              mockStore[convert_key]
            }}</span>
          </p>
          <p ref="weaponSkillArrowRef" class="absolute weapon-skill-arrow"></p>
        </div>
        <div class="flex justify-center mt-10 upgrade-btns">
          <a
            href="javascript:;"
            :class="{ 'cursor-default': mockStore[convert_key] === 1 }"
            @click="change_level(mockStore[convert_key] - 10)"
            >min</a
          >
          <a
            href="javascript:;"
            :class="{ 'cursor-default': mockStore[convert_key] === 1 }"
            @click="change_level(mockStore[convert_key] - 1)"
            >-1</a
          >
          <a
            href="javascript:;"
            :class="{ 'cursor-default': mockStore[convert_key] === 10 }"
            @click="change_level(mockStore[convert_key] + 1)"
            >+1</a
          >
          <a
            href="javascript:;"
            :class="{ 'cursor-default': mockStore[convert_key] === 10 }"
            @click="change_level(mockStore[convert_key] + 10)"
            >max</a
          >
        </div>
        <div
          v-if="cool_time"
          class="flex items-center justify-center py-[5px] mt-[8px] mx-[6px] w-[calc(100% - 12px)] bg-[var(--color-5)] rounded-full"
        >
          <div class="w-[14px] h-[14px] mr-[4px]">
            <SvgIcon class="w-[20px] h-[20px]" name="icon-cooltime" />
          </div>
          <span class="text-[14px] text-[color:var(--text-3)] pb-[1px]">{{ cool_time }}</span>
        </div>
      </div>
      <div
        class="pl-10 w-60 text-black leading-relaxed flex flex-col justify-center nikkes-detail-weapon-skill-desc scroll-bar"
        :class="{ 'text-18': isMobile, 'text-16': !isMobile }"
      >
        <div v-safe-html="parseGameSkillDesc(skill, mockStore[convert_key] - 1)"></div>
      </div>
    </div>
    <div class="nikkes-weapon-res-right">
      <p
        v-if="costs.length"
        class="ff-tt-bold border-top-gray pt-10 text-20 text-black text-center"
      >
        {{ t("upgrade_material") }}
      </p>
      <div class="flex justify-center mt-10">
        <div
          v-for="detail in costs"
          v-show="detail.item_id !== 0"
          :key="detail.item_id"
          :class="{ 'ml-20 mr-20': isMobile, 'ml-5 mr-5': !isMobile }"
          class="z-0"
        >
          <p
            class="upgrade-resbg lg flex justify-center"
            :class="`upgrade-resbg--${detail.item_rare?.toLowerCase()}`"
          >
            <img
              v-if="detail.resource_id"
              :src="ICONS_URL({ path: 'material', name: detail.resource_id })"
              alt=""
            />
          </p>
          <div
            class="nikkes-detail-weapon-skill-material-desc text-black text-center"
            :class="{ 'text-20': isMobile, 'text-16': !isMobile }"
          >
            <!-- <p><span v-automarquee>{{ detail.name_localkey }}</span></p> -->
            <span
              class="block text-center font-num"
              :class="{ ' text-22': isMobile, 'text-17': !isMobile }"
            >
              {{ `${sign}${detail.item_value}` }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Skill, UltiSkill, SkillCost } from "@/shiftyspad/types/character";
import { ref, toRaw, watch } from "vue";
import { useMockDataStore } from "@/shiftyspad/stores/character";
import { max_skill_level } from "@/shiftyspad/const/setting";
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { parseGameSkillDesc } from "@/shiftyspad/utils/str";
import { computed } from "vue";
import { throttle } from "@/shiftyspad/utils/comm";
import { isMobileDevice } from "packages/utils/tools";
import { t } from "@/locales";
import { logger } from "@/shiftyspad/const";

import SvgIcon from "@/components/common/svg-icon.vue";

const isMobile = isMobileDevice();
const mockStore = useMockDataStore();
const props = defineProps<{
  skill: Skill;
  cost: SkillCost[];
  level_key: string;
  enhanced?: boolean;
}>();
const { skill, level_key, cost, enhanced } = toRaw(props);
const convert_key = level_key as "skill1_level" | "skill2_level" | "ulti_skill_level";

const sign = computed(() => {
  const level = mockStore[`real_${convert_key}`];
  const mock_level = mockStore[convert_key];
  return mock_level < level ? "-" : "";
});
const costs = computed(() => {
  const result = new Map<number, SkillCost["costs"][number]>();
  const level = mockStore[`real_${convert_key}`];
  const mock_level = mockStore[convert_key];
  for (let i = Math.min(level, mock_level); i < Math.max(level, mock_level); i++) {
    // i 级资源
    const content = cost[i - 1];
    content.costs.forEach((cost) => {
      if (!result.get(cost.item_id)) {
        result.set(cost.item_id, JSON.parse(JSON.stringify(cost)));
      } else {
        const target = result.get(cost.item_id);
        target!.item_value = target!.item_value + cost.item_value;
      }
    });
  }
  return Array.from(result.values());
});

const cool_time = computed(() => {
  if (convert_key !== "ulti_skill_level") return "";
  // @ts-expect-error
  const u_skill: UltiSkill = skill;
  const cool_time = u_skill.skill_cooltime_list?.[mockStore[convert_key] - 1];
  if (!cool_time) return "";
  return `${(cool_time / 100).toFixed(1)} s`;
});

const emit = defineEmits(["changeLevel"]);
const change_level = (val: number) => {
  mockStore.change({ key: convert_key, min: 1, max: max_skill_level, val });
  emit("changeLevel");
};

const weaponSkillArrowRef = ref<HTMLElement>();
const arrowStatus = ref<"" | "up" | "down">("");
function showArrowAnim() {
  (weaponSkillArrowRef.value as HTMLElement).innerHTML = "";
  const span = document.createElement("span");
  span.className = `level-${arrowStatus.value}`;
  span.style.position = "absolute";
  (weaponSkillArrowRef.value as HTMLElement).appendChild(span);
  setTimeout(() => {
    try {
      if ((weaponSkillArrowRef.value as HTMLElement)?.contains(span)) {
        (weaponSkillArrowRef.value as HTMLElement)?.removeChild(span);
      }
    } catch (err) {
      logger.error("capture error in removeChild");
    }
  }, 1200);
}
const arrowAnim = throttle(function () {
  showArrowAnim();
}, 100);
watch(
  () => mockStore[convert_key],
  (nv, ov) => {
    arrowStatus.value = nv > ov ? "up" : nv < ov ? "down" : "";
    arrowAnim();
  },
);
</script>

<style lang="scss">
.border-transparent-gap {
  &.enhanced {
    margin-top: 5px;
    box-shadow: 0 0 0 px2rem(3) gold !important;
  }
}

.nikkes-detail-weapon-skill-desc {
  height: px2rem(220);
  overflow: auto;
  > div {
    max-height: px2rem(220);
  }
}
.nikkes-detail-weapon-skill-material-desc {
  width: px2rem(94px);
  > p {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
  }
}
.weapon-skill-arrow {
  width: px2rem(26);
  height: px2rem(20);
  position: absolute;
  right: px2rem(-40);
  bottom: 0;
  z-index: 2;
}
</style>
<style lang="scss">
.is-pc {
  .nikkes-weapon-pane {
    display: flex;
    .nikkes-weapon-res-left {
      // flex: 0 1 62.5%;
      width: 62.5%;
    }
    .nikkes-weapon-res-right {
      // flex: 1 0 37.5%;
      width: 37.5%;
      padding: px2rem(10);
      position: relative;
      &::before {
        display: block;
        content: "";
        border-left: rgba(0, 0, 0, 0.2) solid px2rem(1);
        width: 0;
        height: calc(100% - px2rem(20));
        position: absolute;
        left: 0;
        top: px2rem(10);
      }
      p.border-top-gray {
        border-top-width: 0;
      }
    }
  }
}
@media screen and (max-width: 1500px) {
  .is-pc {
    .nikkes-weapon-pane {
      display: flex;
      .nikkes-weapon-res-left {
        width: 55%;
        // flex: 0 1 62.5%;
      }
      .nikkes-weapon-res-right {
        // flex: 1 0 37.5%;
        width: 45%;
      }
    }
  }
}
</style>
