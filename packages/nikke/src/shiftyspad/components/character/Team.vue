<!-- 女神部队 -->
<script setup lang="ts">
import { toRaw } from "vue";

// Import Swiper Vue.js components
import { CharacterData } from "@/shiftyspad/types/character";
import { MI_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { parseNewLine } from "@/shiftyspad/utils/str";
import { isMobileDevice } from "packages/utils/tools";
import { TeamMate } from "packages/types/shiftyspad";
import { RoutesName } from "@/router/routes";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import router from "@/router";

const isMobile = isMobileDevice();
const props = defineProps<{
  character_data: CharacterData;
}>();
const { character_data } = toRaw(props);

const toNikkeDetail = (nikke: TeamMate) => {
  if (nikke.resource_id == character_data.resource_id) {
    return;
  }
  router.replace({
    name: RoutesName.SHIFTYSPAD_NIKKE_DETAIL,
    query: { [COMMON_QUERY_KEYS.NikkeId]: nikke.resource_id },
  });
};

const getClass = (character: CharacterData["teammate_list"][number]) => {
  const { original_rare } = character;
  switch (original_rare.toLowerCase()) {
    case "ssr":
      return "nikkes-all-item-btm";
    case "sr":
      return "nikkes-all-item-btm--purple";
    case "r":
      return "nikkes-all-item-btm--blue";
  }
};
</script>

<template>
  <div
    id="nikkes-team"
    class="expandable-box expandable-box--nobtn p-10 nikkes-detail-item nikkes-detail-team"
  >
    <div
      class="text-black leading-normal text-center ff-tt-bold"
      :class="{ 'text-24': isMobile, 'text-20': !isMobile }"
    >
      {{ character_data.squad_detail.squad_name }}
    </div>
    <div class="mt-15 border-top-gray pt-5 pl-30 pr-30 box-border">
      <div
        class="w-full flex overflow-auto nikkes-detail-team-list"
        :class="{ 'justify-center': character_data.teammate_list.length <= 3 }"
      >
        <div
          v-for="character in character_data.teammate_list"
          :key="character.id"
          class="nikkes-all-item"
          :class="{ 'cursor-pointer': character.resource_id !== character_data.resource_id }"
          @click="toNikkeDetail(character)"
        >
          <img
            :src="MI_CHARACTER_URL({ resource_id: character.resource_id })"
            class="nikkes-all-item-img"
            alt=""
          />
          <div
            class="nikkes-all-item-btm text-white flex align-center justify-center text-20 pl-10 pr-10 box-border leading-tight text-center"
            :class="getClass(character)"
          >
            <span v-autofontsizeheight>{{ character.name_localkey }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-safe-html="parseNewLine(character_data.squad_detail.squad_description)"
      class="border-top-gray text-black-60 text-center pt-10 leading-tight"
      :class="{ 'text-20': isMobile, 'text-16': !isMobile }"
    ></div>
  </div>
</template>

<style lang="scss" scoped>
.nikkes-all-item {
  width: px2rem(160);
  border-radius: px2rem(8);
  margin: px2rem(7);
  overflow: hidden;
  box-shadow: 0 0 1px #f5a919;
  flex: none;
  &-img {
    display: block;
    width: 100%;
    height: px2rem(222);
    object-position: center top;
    object-fit: cover;
  }
  &-icon {
    @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-starbg.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100%;
    width: px2rem(40);
    height: px2rem(44);
    left: px2rem(4);
    top: px2rem(4);
    text-align: center;
    img {
      width: px2rem(22);
      margin-top: px2rem(11);
    }
  }
  &-btm {
    height: px2rem(59);
    @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-yellowbg.png");
    background-repeat: repeat-x;
    background-size: auto px2rem(59);
    &--yellow {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-yellowbg.png");
    }
    &--blue {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-bluebg.png");
    }
    &--purple {
      @include bgimg("@/shiftyspad/assets/images/nikkes/nikkes-purplebg.png");
    }
  }
}
</style>

<style lang="scss">
@media screen and (max-width: 1500px) {
  .is-pc {
    .nikkes-detail-team-list {
      justify-content: flex-start;
    }
  }
}
</style>
