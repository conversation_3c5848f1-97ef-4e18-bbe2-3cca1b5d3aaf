<script setup lang="ts">
import { isMobileDevice } from "packages/utils/tools";
import { report } from "packages/utils/tlog";
import { f } from "@/shiftyspad/utils/str";
import { t } from "@/locales";

import { MI_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { type CharacterData, WeaponSlotItemSize } from "@/shiftyspad/types/character";

import { useCV } from "@/shiftyspad/stores/role/cv";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useSkinStore } from "@/shiftyspad/stores/skin";
import { usePlayerRoleData } from "@/shiftyspad/composable/player";
import { useRecycle } from "@/shiftyspad/composable/recycledata";
import { useRoleDisplayState } from "@/shiftyspad/stores/role/display";
import { useUserRoleLevelData } from "@/shiftyspad/composable/character";
import { useCharacterIcon } from "@/shiftyspad/composable/icon";

import { Navigation, Pagination } from "swiper/modules";
import { Swiper as SwiperTypes } from "swiper/types";
import { Swiper, SwiperSlide } from "swiper/vue";
import { storeToRefs } from "pinia";
import { watch, toRaw, ref, computed, toRefs } from "vue";

import WeaponSlotItem from "./WeaponSlotItem.vue";
import AdditionPopup from "./AdditionPopup.vue";
import Display from "./animate/Display.vue";
import Weapon from "@/shiftyspad/components/character/Weapon.vue";
import Dialog from "@/shiftyspad/components/common/Dialog.vue";
import "swiper/css";
import { logger } from "@/shiftyspad/const";

const props = defineProps<{
  character_data: CharacterData;
}>();
const { character_data } = toRaw(props);

const skinStore = useSkinStore();
const shiftyUser = useUserStore();
const { shiftys_user } = toRefs(shiftyUser);
const { status } = storeToRefs(useRoleDisplayState());
const { skin, skin_list } = storeToRefs(skinStore);
const { user_battle_info, user_nikkelist_info } = toRefs(shiftyUser);
const { user_character } = usePlayerRoleData({
  character: character_data,
  shiftys_user: shiftys_user as any,
});
const { top2_level } = useRecycle({
  character_data,
  user_battle_info,
  user_nikkelist_info,
});
const { class_icon, rareness_icon, element_icon_asset, clean_corp_icon, burst_icon } =
  useCharacterIcon(character_data);

const isMobile = isMobileDevice();

const { character_fight_attribute, init_player_equip_data } = useUserRoleLevelData({
  shiftys_user,
  character_data,
  options: { use_mock: true },
});

// 确认已登录
const swiper_instance = ref();
const user_skin_index = computed(() => {
  const origin_index = skin_list.value?.findIndex((val) => val.costume_index === 0);
  const default_index = origin_index >= 0 ? 0 : origin_index;
  if (!user_character.value) return default_index;
  const skin_index = skin_list.value?.findIndex(
    (val) => val.id === user_character.value?.costume_id,
  );
  return skin_index >= 0 ? skin_index : default_index;
});

const swiperInit = () => {
  if (user_skin_index.value !== 0) {
    // swiper loop 的bug
    if (skin_list.value.length === 2 && swiper_instance.value?.realIndex !== 0) {
      swiper_instance.value?.slidePrev();
    } else {
      swiper_instance.value?.update();
      swiper_instance.value?.slideToLoop(user_skin_index.value);
    }
  }
  // @ts-ignore
  window.swiper = swiper_instance.value;
  skinStore.initCharacter(character_data, user_skin_index.value);
};

watch(
  () => [user_character.value, skin_list.value],
  () => {
    if (user_character.value && character_data) {
      // 设置皮肤
      init_player_equip_data();
      logger.info(
        `current skin_id: ${user_character.value.costume_id}, slide index : ${user_skin_index.value}`,
      );
    }
    swiperInit();
  },
  {
    immediate: true,
  },
);

const cv = useCV();
const showAdditionPopupFlag = ref(0);
const viewmodeCleanFlag = ref(false);

const desc = computed(() => {
  const c = (str: string) => str.replace(/\\n/g, "<br />");
  return skin.value
    ? c(skin.value.costume_description_locale ?? "")
    : c(character_data.description_localkey);
});

const onSwiper = (swiper: SwiperTypes) => {
  swiper_instance.value = swiper;
  swiperInit();
};
const prev = () => {
  if (skin_list.value.length === 2) {
    swiper_instance.value?.slidePrev();
  }
  swiper_instance.value?.slidePrev();
};
const next = () => {
  if (skin_list.value.length === 2) {
    swiper_instance.value?.slidePrev();
  }
  swiper_instance.value?.slideNext();
};
watch(skin, () => {
  const target_index = skin_list.value.findIndex((s) => s === skin.value);
  const index = swiper_instance.value?.realIndex ?? 0;
  const skin_index = index % skin_list.value.length;
  if (skin_index !== target_index) {
    swiper_instance.value?.slideToLoop(target_index);
  }
});

const changeSkinByIndex = () => {
  const index = swiper_instance.value?.realIndex ?? 0;
  const skin_index = index % skin_list.value.length;
  const switch_skin = skin_list.value[skin_index];
  if (skin_list.value[skin_index] !== skin.value) {
    skinStore.switchSkin(switch_skin);
  }
};
const swiperModules = [Pagination, Navigation];

// 加成详情点击事件
const bonusDetailsEvent = () => {
  showAdditionPopupFlag.value = 1;
  // 角色详情页-加成详情按钮点击埋点
  report.small_tool_nikke_role_detail_addition_btn.cm_click({});
};

const weaponDialog = ref(false);

const showWeaponPopus = () => {
  weaponDialog.value = true;
};
</script>
<template>
  <div
    id="nikkes-basics"
    class="relative nikkes-detail-item nikkes-detail-basics"
    :class="{ 'viewmode--clean': viewmodeCleanFlag }"
  >
    <Display
      v-if="character_data"
      :key="user_character?.costume_id ?? character_data.resource_id"
      :character_data="character_data"
      :viewmode-clean-flag="viewmodeCleanFlag"
      @toogle="
        (v: boolean) => {
          viewmodeCleanFlag = v;
        }
      "
    ></Display>
    <div
      :class="[`charinfo`, status !== 'none' ? '!-right-[15rem] transition-all duration-300' : ``]"
    >
      <p class="bg-gray-30 absolute text-20 pl-5 pr-5 cv-name">
        <span v-automarquee class="ff-tt-bold text-24">{{ cv.use_cv_key(character_data) }}</span>
      </p>
      <div class="pt-10 pb-10 text-center text-50 text-white charinfo-name">
        <span v-automarquee class="ff-tt-extra-bold">{{ f(character_data.name_localkey) }}</span>
      </div>
      <div class="flex justify-center pt-5 pb-5 shadow0">
        <p
          class="flex flex-col justify-center items-center w-half text-center text-20 text-ink-60 border-right-gray"
        >
          {{ t("rareness") }}<br />
          <img class="icon-rarity" :src="rareness_icon" alt="" />
        </p>
        <div class="w-half text-center text-20 text-ink-60">
          {{ t("team") }}
          <p class="text-black overflow-hidden whitespace-nowrap">
            <span v-automarquee>{{ f(character_data.squad_detail.squad_name) }}</span>
          </p>
        </div>
      </div>
      <div class="flex flex-wrap justify-center mt-20 charinfo-icons">
        <div class="w-33">
          <p class="hex mx-auto">
            <img class="w-full hex-border-dark" :src="element_icon_asset" alt="" />
          </p>
          <p class="text-black text-16 text-center whitespace-nowrap">
            <span v-autofontsize>{{ t("attr_code") }}</span>
          </p>
        </div>
        <div class="w-33" @click="showWeaponPopus">
          <WeaponSlotItem
            class="mx-auto cursor-pointer"
            :size="WeaponSlotItemSize.LARGE"
            :weapon="character_data.shot_detail.weapon_type"
          ></WeaponSlotItem>
          <p class="text-black text-16 text-center whitespace-nowrap">
            <span v-autofontsize>{{ t("weapon") }}</span>
          </p>
        </div>
        <div class="w-33">
          <p class="hex hex-border-white mx-auto">
            <img class="h-60 filter-black" :src="class_icon" alt="" />
          </p>
          <p class="text-black text-16 text-center whitespace-nowrap">
            <span v-autofontsize>{{ t("position") }}</span>
          </p>
        </div>
        <div class="w-33">
          <p class="hex hex-border-white mx-auto">
            <img class="h-60 filter-black" :src="clean_corp_icon" alt="" />
          </p>
          <p class="text-black text-16 text-center whitespace-nowrap">
            <span v-autofontsize>{{ t("coporation") }}</span>
          </p>
        </div>
        <div class="w-33">
          <p class="hex hex-border-dark mx-auto">
            <img class="h-50" :src="burst_icon" alt="" />
          </p>
          <p class="text-black text-16 text-center whitespace-nowrap">
            <span v-autofontsize>{{ t("brust_stage") }}</span>
          </p>
        </div>
      </div>
      <div
        class="bg-white flex flex-col mt-[8px] p-[4px] justify-center items-center shadow-[0px_1px_5px_0px_rgba(0,0,0,0.1)]"
      >
        <div class="text-[length:9px] leading-[1] flex font-bold text-[color:#5A5A5B]">POWER</div>
        <div
          class="text-[length:16px] font-[DINNextLTProBold] leading-[16px] flex font-bold text-[color:transparent] bg-clip-text bg-gradient-to-r from-[#FFB621] to-[#FF5D1C]"
        >
          <span class="mt-[2px]">{{ character_fight_attribute }}</span>
        </div>
      </div>
      <div
        v-if="user_character"
        class="flex justify-between border-radius-10 bg-white box-border mt-10 shadow1 charinfo-more"
      >
        <div
          v-if="character_data.original_rare.toLowerCase() !== 'r'"
          class="w-25 flex-none box-border border-right-gray"
          :class="{
            'mt-10 mb-10 pl-5 pr-5': isMobile,
            'border-radius-5 pt-5 ml-[2px] mr-5': !isMobile,
          }"
        >
          <p class="text-16 text-center text-red">RANK</p>
          <p
            class="w-[21px] h-[18px] icon-love-lg text-[12px] mx-auto text-center text-white ff-num"
          >
            {{ user_character?.attractive_level || 1 }}
          </p>
        </div>
        <div
          class="flex-1 flex pt-10 box-border"
          :class="{ 'border-radius-5 pl-5 pr-5': !isMobile }"
        >
          <div
            v-for="l in top2_level"
            v-show="l.val >= 0"
            :key="l.key"
            class="mr-[2px] w-50 text-center"
          >
            <div
              class="bg-dark border-radius-10 pl-5 pr-5 box-content text-white text-[8px] whitespace-nowrap charinfo-tname"
            >
              <p class="w-full h-full flex justify-center items-center">
                <span v-autofontsize v-fontfix>{{ t(l.key) }}</span>
              </p>
            </div>
            <p class="text-[8px] leading-tight">LEVEL</p>
            <p class="ff-num text-black text-[16px] leading-[16px]">{{ l.val || 0 }}</p>
          </div>
        </div>
        <a
          class="ff-tt-bold block flex-none bg-blue border-box cursor-pointer flex align-center justify-center box-border charinfo-more-btn"
          @click="bonusDetailsEvent"
        >
          {{ t("buff_detail") }}
          <i></i>
        </a>
      </div>
      <div class="text-18 text-ink leading-tight mt-10 charinfo-desc">
        <p v-safe-html="desc"></p>
      </div>
      <div
        v-if="skin_list?.length"
        class="mx-auto mt-20 relative charinfo-swiper"
        :class="{ hidden: viewmodeCleanFlag }"
      >
        <swiper
          v-if="skin_list?.length"
          direction="horizontal"
          :modules="swiperModules"
          :space-between="0"
          :loop="true"
          :initial-slide="user_skin_index"
          :slides-per-view="'auto'"
          :slide-to-clicked-slide="true"
          :centered-slides="skin_list?.length === 1"
          :navigation="{ prevEl: '.charinfo-swiper-prev', nextEl: '.charinfo-swiper-next' }"
          @swiper="onSwiper"
          @slide-change="changeSkinByIndex"
        >
          <swiper-slide
            v-for="skin in skin_list"
            :key="skin.id"
            :class="{ lock: skin.is_hidden }"
            class="relative charinfo-swiper-item"
          >
            <img
              :src="
                MI_CHARACTER_URL({
                  resource_id: character_data.resource_id,
                  skin_index: skin.costume_index,
                })
              "
              class="w-full"
              :alt="`${character_data.resource_id}_${skin.id}_${skin.costume_index}`"
            />
            <div class="absolute w-full text-white text-center">
              <span
                :class="{ 'text-14': isMobile, 'text-16': !isMobile }"
                :style="[!isMobile ? { zoom: 0.85 } : '']"
              >
                {{ skin.costume_name_locale }}
              </span>
              <p v-autofontsize :class="{ 'text-18': isMobile, 'text-16': !isMobile }">
                {{ t((skin.costume_grade_id ?? "").toLowerCase()) }}
              </p>
            </div>
            <i v-if="skin.is_hidden" class="icon-lock"></i>
          </swiper-slide>
        </swiper>
        <a
          v-if="skin_list?.length > 1"
          href="javascript:;"
          class="charinfo-swiper-prev"
          @click="prev"
        ></a>
        <a
          v-if="skin_list?.length > 1"
          href="javascript:;"
          class="charinfo-swiper-next"
          @click="next"
        ></a>
      </div>
    </div>
    <!-- (viewmodeBtnExpandFlag = false) -->
    <a
      href="javascript:;"
      class="viewmode-clean-backbtn !z-0"
      @click="viewmodeCleanFlag = false"
    ></a>
  </div>
  <Teleport to="body">
    <Dialog v-if="weaponDialog" :title="t('weapon')" @close="weaponDialog = false">
      <Weapon :character_data="character_data" active="weapon" />
    </Dialog>
    <AdditionPopup
      v-if="showAdditionPopupFlag"
      :character_data="character_data"
      :attractive_level="user_character?.attractive_level"
      @close="showAdditionPopupFlag = 0"
    ></AdditionPopup>
  </Teleport>
</template>
<style lang="scss" scoped>
@import url("./scss/detail.scss");
</style>
