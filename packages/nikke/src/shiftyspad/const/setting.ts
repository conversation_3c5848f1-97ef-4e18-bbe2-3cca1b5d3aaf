import { RarityType } from "packages/types/shiftyspad";

export const default_grade = 0;
export const default_level = 1;
export const max_skill_level = 10;

export const broke_skins = [180, 300, 301, 305, 306];

/** 系数 */
export const settings_attribute_def = 100; // 防御倍数
export const settings_attribute_hp = 0.7; // 生命倍数
export const settings_attribute_attack_ratio = 18; // 攻击倍数
export const settings_attribute_skill1 = 0.01;
export const settings_attribute_skill2 = 0.01;
export const settings_attribute_burst = 0.02;
export const settings_attribute_base_skll = 1.3;
export const settings_equip_bounus = 0.4;
// configgame EquipIncreaseStatBonus
export const settings_equip_increase_bouns = 0.1;
export const settings_equip_corp_bounus = 0.3;

/** 数值含义 */
export const NO_VALUE_OR_ZERO = -99;
export const VALUE_IS_HIDDEN = -9999;

export const format_level = (l?: number) => {
  if (!l) return 0;
  if (l < 0) return 0;
  return l;
};

export const equip_tier_color = {
  3: "#006819",
  4: "#01CA00",
  5: "#0043A8",
  6: "#0077FF",
  7: "#8711BC",
  8: "#CD00FF",
  9: "#FF8900",
  10: "#FF2A76",
};

export const get_grade_core_id = (character: {
  original_rare: RarityType;
  limit_break: number;
}) => {
  if (character.original_rare === "R") {
    return character.limit_break + 201;
  }
  if (character.original_rare === "SR") {
    return character.limit_break + 101;
  }
  if (character.original_rare === "SSR") {
    return character.limit_break + 1;
  }
};
