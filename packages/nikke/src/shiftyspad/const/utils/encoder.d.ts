/**
 * @description 将具体的一个路径, '/xx/xx/xx/xx/id.ext' 按规则转化.
 *
 * 具体规则:
 * 一个路径的具体组成由 目录(多级) + 文件名 组成. 对此我们采取以下规则:
 *
 * 普通文件
 * 1. 目录采用 {alpha * 2}-{number * 2}, 不同层级的目录采用不同的素数.
 * 2. id.ext 文件名走 hash
 *
 * spine animation
 * 不包含文件名的全路径取余(why? see https://esotericsoftware.com/spine-atlas-format)
 *
 */
export declare function obfuscatedPath(path: string): string;
/**
 * 文件名 md5 化 (used in spine image)
 */
export declare function file2md5(filename: string): string;
