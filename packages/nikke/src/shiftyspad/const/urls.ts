import { getLang } from "@/shiftyspad/const";
import { patch } from "@/shiftyspad/utils/str";
import { isWebp } from "@/shiftyspad/const";
import { supportsAvif } from "@/shiftyspad/utils/img";
import { getNikkeStaticGameResouce } from "@/shiftyspad/utils/assets";
import { obfuscatedPath } from "@/shiftyspad/const/utils/encoder.js";

import { CVLang } from "packages/types/shiftyspad";

export const data_lang = getLang().toLowerCase();
const suffixPngOrWep = isWebp ? "webp" : "png";

let support_avif: boolean;
supportsAvif().then((val) => (support_avif = val));

type AniParams = {
  action?: string;
  skin_index?: number;
  type?: "stand" | "combat";
  combat?: "aim" | "cover";
  resource_id: number;
};

/** @description 游戏内资源路径转线上路径 */
export const getIngameResourceUrl = (p: string) => {
  const CDN_URL = getNikkeStaticGameResouce("");
  const origin_path = p.replace(CDN_URL, "").replace(/^\//, "");
  if (import.meta.env.VITE_APP_DISABLE_OBSPATH === "1") {
    return `${CDN_URL}/${origin_path}`;
  }
  const obfuscated_path = obfuscatedPath(origin_path);
  return `${CDN_URL}/${obfuscated_path}`;
};

/** @description 获取动画数据 */
export const GET_SPINE_URL = (params: AniParams) => {
  const { skin_index = 0, resource_id, type = "stand", combat = "aim" } = params;
  const avif_flag = support_avif ? "_avif" : "";
  const r_id = patch(resource_id, 3);
  const s_id = patch(skin_index, 2);
  const ani = (() => {
    if (type === "stand") return `c${r_id}_${s_id}`;
    return `c${r_id}_${combat}_${s_id}`;
  })();
  const prefix = (() => {
    if (type === "stand") return `/spine/${type}/c${r_id}/${s_id}/${ani}`;
    return `/spine/${type}/c${r_id}/${s_id}/${combat}/${ani}`;
  })();
  const atlas = `${prefix}${avif_flag}.atlas.txt`;
  const skel = `${prefix}.skel.bytes`;
  return { atlas: getIngameResourceUrl(atlas), skel: getIngameResourceUrl(skel) };
};

/** @description 获取游戏表格数据 */
export const GET_TABLE_URL = (
  type:
    | "attractive"
    | "recycle"
    | "equip"
    | "level-cost"
    | "character-map"
    | "equip-options"
    | "avatar"
    | "character_scene_setting"
    | "guild_icon",
) => {
  return {
    equip: getIngameResourceUrl(`/equip/ItemEquipTable-${data_lang}.json`),
    "equip-options": getIngameResourceUrl(`/equip/EquipmentOptionTable-${data_lang}.json`),
    "character-map": getIngameResourceUrl(`/character/character_id_map.json`),
    "level-cost": getIngameResourceUrl(`/character/CharacterLevelTable.json`),
    recycle: getIngameResourceUrl(`/character/RecycleResearchStatTable.json`),
    attractive: getIngameResourceUrl(`/character/AttractiveLevelTable.json`),
    avatar: getIngameResourceUrl(`/character/character_avatar_map.json`),
    guild_icon: getIngameResourceUrl(`/guild/guild_emblem.json`),
    character_scene_setting: getIngameResourceUrl(`/character/scene_characeter_list_v2.json`),
  }[type];
};

// character sd
/** @description SD - 3d 模型 */
export const SD_URL = (params: AniParams) => {
  const { action, skin_index = 0, resource_id } = params;
  const ani = `c${patch(resource_id, 3)}_${patch(skin_index, 2)}_${action}`;
  return getIngameResourceUrl(`/sd/${ani}.glb`);
};

/** @description 获取角色全身照 */
export const FULL_CHARACTER_URL = (params: AniParams) => {
  const { skin_index = 0, resource_id } = params;
  const ani = `c${patch(resource_id, 3)}_${patch(skin_index, 2)}`;
  return getIngameResourceUrl(`/character/full/${ani}.${suffixPngOrWep}`);
};

/** @description 获取角色半身照 */
export const MI_CHARACTER_URL = (params: AniParams) => {
  const { skin_index = 0, resource_id } = params;
  const ani = `c${patch(resource_id, 3)}_${patch(skin_index, 2)}`;
  return getIngameResourceUrl(`/character/mi/mi_${ani}_s.${suffixPngOrWep}`);
};

/** @description 获取角色头像照 */
export const SM_CHARACTER_URL = (params: AniParams) => {
  const { skin_index = 0, resource_id } = params;
  const ani = `c${patch(resource_id, 3)}_${patch(skin_index, 2)}`;
  return getIngameResourceUrl(`/character/si/si_${ani}_s.${suffixPngOrWep}`);
};

// NIKKE_CONTENTS\Assets\GraphicAssetsHD\UI\icon
export const ICONS_URL = (params: { path: string; name: string }) => {
  if (!params.name) return "";
  return getIngameResourceUrl(
    `/icon/${params.path.replace(/^\//, "")}/${params.name}.${suffixPngOrWep}`,
  );
};

/** @description 获取背景图 */
export const BG_URL = (params: { path: string; name: string }) => {
  return getIngameResourceUrl(`/background/${params.path}/${params.name}.${suffixPngOrWep}`);
};

/** @description 获取角色数据 */
export const CHARACTER_DATA_URL = (role_id: number | string) => {
  return getIngameResourceUrl(`/roledata/${role_id}-v2-${data_lang}.json`);
};

/** @description 获取音频url */
export const GET_VOICE_URL = (params: {
  cv_lang: CVLang;
  role_id?: number;
  speech_id: string;
  format?: string;
}) => {
  const { cv_lang, speech_id, format } = params;
  const type = format || "wav";
  return getIngameResourceUrl(`/voice/${cv_lang}/${speech_id}.${type}`);
};

/** @description 获取好感度剧情url */
export const GET_ATTRACT_SCENT = (params: { scene_id: string }) => {
  const { scene_id } = params;
  return getIngameResourceUrl(`/attractscene/${scene_id}-${data_lang}.json`);
};
