import { ViewModePose, WeaponType } from "@/shiftyspad/types/character";

export const ViewIconMap = {
  [ViewModePose.STATIC]: "",
  [ViewModePose.NORMAL]: "stand", // 站姿
  [ViewModePose.SHOOT]: "squat2", // 蹲姿
  [ViewModePose.HALF_FACE]: "squat", // 蹲姿
};

export const WeaponIconMap: Record<WeaponType, string> = {
  [WeaponType.ASSAULT_RIFLE]: "assault_rifle",
  [WeaponType.MACHINE_GUN]: "machine_gun",
  [WeaponType.SHOT_GUN]: "shot_gun",
  [WeaponType.ROCKET_LAUNCHER]: "rocket_launcher",
  [WeaponType.SUB_MACHINE_GUN]: "sub_machine_gun",
  [WeaponType.SNIPER_RIFLE]: "sniper_rifle",

  [WeaponType.MACHINE_GUN_NEW]: "machine_gun",
  [WeaponType.ASSAULT_RIFLE_NEW]: "assault_rifle",
  [WeaponType.SHOT_GUN_NEW]: "shot_gun",
  [WeaponType.ROCKET_LAUNCHER_NEW]: "rocket_launcher",
  [WeaponType.SUB_MACHINE_GUN_NEW]: "sub_machine_gun",
  [WeaponType.SNIPER_RIFLE_NEW]: "sniper_rifle",
};
