/**
 * 中转层: 使得原业务可以执行
 */
import { getStandardizedLang } from "packages/utils/standard";
import { support_format_webp } from "../utils/img";
import { Logger, LogLevel } from "m-web-logger";
import { useCms } from "@/composables/use-cms";
import { isGameLogin } from "packages/utils/tools";

export { getSys, isMobile, getQuery, ENV, CmsHelper } from "@tencent/pa-cms-utils";
export const ingame = {
  isInIntlBrowser: isGameLogin(),
};
export const getLang = () => {
  if (getStandardizedLang() === "zh") {
    return "zh-TW";
  }
  return getStandardizedLang();
};
export const isWebp = support_format_webp();
export const logger = new Logger({
  label: "common",
  level: import.meta.env.DEV ? LogLevel.all : undefined,
});
export const { cms_helper } = useCms();
