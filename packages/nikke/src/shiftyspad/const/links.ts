import { getLang } from "@/shiftyspad/const";

export const IOSDownload = {
  de: "https://apps.apple.com/de/app/goddess-of-victory-nikke/id1585915174?platform=ipad",
  en: "https://apps.apple.com/us/app/goddess-of-victory-nikke/id1585915174",
  fr: "https://apps.apple.com/app/goddess-of-victory-nikke/id1585915174",
  ja: "https://apps.apple.com/jp/app/goddess-of-victory-nikke/id1585915174",
  ko: "https://apps.apple.com/kr/app/goddess-of-victory-nikke/id1585915174",
  sea: "https://apps.apple.com/th/app/goddess-of-victory-nikke/id1585915174?l=th",
  "zh-TW": "https://apps.apple.com/tw/app/id1630883882",
}[getLang()];

export const AndroidDownload = {
  de: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=de",
  en: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=EN_US&gl=us",
  fr: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=fr",
  ja: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=ja&gl=jp",
  ko: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=ko&gl=kr",
  sea: "https://play.google.com/store/apps/details?id=com.proximabeta.nikke&hl=th",
  "zh-TW": "https://play.google.com/store/apps/details?id=com.gamamobi.nikke",
}[getLang()];

export const Twitter = {
  de: "https://twitter.com/NIKKE_en",
  en: "https://twitter.com/NIKKE_en",
  fr: "https://twitter.com/NIKKE_en",
  ja: "https://twitter.com/NIKKE_japan",
  ko: "https://twitter.com/NIKKE_kr",
  sea: "",
  "zh-TW": "",
}[getLang()];

export const Facebook = {
  de: "https://facebook.com/nikke.global",
  en: "https://facebook.com/nikke.global",
  fr: "https://facebook.com/nikke.global",
  ja: "",
  ko: "https://www.facebook.com/nikke.kr.official/",
  sea: "https://www.facebook.com/nikke.th.official",
  "zh-TW": "https://www.facebook.com/TW.NIKKE/",
}[getLang()];

export const Youtube = {
  de: "https://www.youtube.com/c/NIKKEEN",
  en: "https://www.youtube.com/c/NIKKEEN",
  fr: "https://www.youtube.com/c/NIKKEEN",
  ja: "https://www.youtube.com/channel/UCvi81mltkpbF2UEvC_e864g",
  ko: 'https://www.youtube.com/c/nikkekr"',
  sea: "https://www.youtube.com/channel/UCqy4ixXAiY51xoRJS8Aj2mQ",
  "zh-TW": "https://www.youtube.com/channel/UCLI0h6wX_RpEbDcB11UV_RA",
}[getLang()];

export const Instagram = {
  de: "https://www.instagram.com/nikke_global/",
  en: "https://www.instagram.com/nikke_global/",
  fr: "https://www.instagram.com/nikke_global/",
  ja: "",
  ko: "",
  sea: "",
  "zh-TW": "https://www.instagram.com/tw.nikke/",
}[getLang()];

export const Discord = {
  de: "https://discord.gg/nikke-en",
  en: "https://discord.gg/nikke-en",
  fr: "https://discord.gg/nikke-en",
  ja: "",
  ko: "",
  sea: "https://discord.gg/AMVnSxp5ym",
  "zh-TW": "",
}[getLang()];

// @ts-ignore
export const Naver = {
  ko: "https://game.naver.com/lounge/nikke/board/11",
}[getLang()];

// @ts-ignore
export const Line = {
  ja: "https://lin.ee/5xX41lC",
}[getLang()];
