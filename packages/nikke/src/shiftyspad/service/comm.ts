import { injectScript } from "packages/utils/tools";
import Spine4028Js from "@/shiftyspad/assets/js/spine-player-4.0.28.js?url";
import Spine4120Js from "@/shiftyspad/assets/js/spine-player-4.1.20.js?url";

export type SpineVersion = "4.1.20" | "4.0.1" | "4.0.28";

export async function loadSpinePlayer(version: SpineVersion) {
  if (version === "4.1.20") {
    if (!window.spine4120) {
      await injectScript(Spine4120Js);
      window.spine4120 = window.spine;
    }
    return Promise.resolve(window.spine4120.SpinePlayer);
  } else if (version === "4.0.28") {
    if (!window.spine4028) {
      await injectScript(Spine4028Js);
      window.spine4028 = window.spine;
    }
    return Promise.resolve(window.spine4028.SpinePlayer);
  } else {
    const { SpinePlayer } = await import("spine-player401");
    return Promise.resolve(SpinePlayer);
  }
}

export function appendStyle(href: string, extraAttr?: Record<string, string>) {
  return new Promise((resolve, reject) => {
    const head = document.getElementsByTagName("head")[0];
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.type = "text/css";
    link.href = href;
    if (extraAttr) {
      Object.keys(extraAttr).forEach((key) => {
        link.setAttribute(key, extraAttr[key]);
      });
    }
    head.appendChild(link);

    link.onload = resolve;
    link.onerror = reject;
  });
}
