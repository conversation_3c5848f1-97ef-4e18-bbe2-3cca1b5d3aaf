import {
  data_lang,
  getIngameResourceUrl,
  SD_URL,
  GET_TABLE_URL,
  SM_CHARACTER_URL,
  GET_ATTRACT_SCENT,
  CHARACTER_DATA_URL,
} from "@/shiftyspad/const/urls";
import { CharacterSceneSettingItem } from "packages/types/game-scene";
import { CharacterData, EquipData, LevelCost } from "packages/types/shiftyspad";
import { getJson } from "./ls-data";

type AniParams = { action: string; skin_index: number; resource_id: number };

export const getSdCdn = (params: AniParams) => {
  return SD_URL(params);
};

export async function getAvatar(avatar_id: number) {
  const avatar_json: { resource_id: number; costume_index: number; id: number }[] = await getJson(
    GET_TABLE_URL("avatar"),
  );
  const target = avatar_json.find((avatar) => avatar_id === avatar.id);
  if (!target) return "";
  return SM_CHARACTER_URL({ skin_index: target.costume_index, resource_id: target.resource_id });
}

export async function getCharacterData(role_id: string): Promise<CharacterData> {
  return await getJson(CHARACTER_DATA_URL(role_id));
}

export async function queryIdByCode(params: { name_code: number; grade_core_id: number }) {
  const { name_code, grade_core_id } = params;
  const cmap = await getJson(GET_TABLE_URL("character-map"));
  const res = cmap.find((v: any) => {
    return v.name_code === name_code && v.grade_core_id === grade_core_id;
  });
  return res.id;
}

export async function queryIdsByResourceId(resource_id: number): Promise<number[]> {
  const cmap = await getJson(GET_TABLE_URL("character-map"));
  return cmap
    .filter((item: any) => Number(item.resource_id) === Number(resource_id))
    .map((item: any) => item.id);
}

export async function queryResouceIdByCode(params: { name_code: number }) {
  const { name_code } = params;
  const cmap = await getJson(GET_TABLE_URL("character-map"));
  const res = cmap?.find((v: any) => {
    return v.name_code === name_code;
  });
  // 如果是新角色这里可能会没数据
  return res ? res.resource_id : 0;
}

export async function getLevelCost(): Promise<LevelCost[]> {
  return (await getJson(GET_TABLE_URL("level-cost")))["records"];
}

export async function getEquipData(): Promise<EquipData[]> {
  return (await getJson(GET_TABLE_URL("equip")))["records"];
}

export async function getScene(params: { lang: any; scene_id: string }) {
  return (await getJson(GET_ATTRACT_SCENT(params)))["records"];
}

export async function getRecycleData() {
  return (await getJson(GET_TABLE_URL("recycle")))["records"];
}

export async function getEquipmentOptions() {
  return (await getJson(GET_TABLE_URL("equip-options")))["records"];
}

export async function getCubeByCubeId(cube_id: number) {
  return await getJson(getIngameResourceUrl(`/equip/${data_lang}/cube_${cube_id}.json`));
}

export async function getFavoriteItemId(favorite_item_id: number) {
  return await getJson(
    getIngameResourceUrl(`/equip/${data_lang}/favorite_${favorite_item_id}.json`),
  );
}

export async function getAttractiveData() {
  return (await getJson(GET_TABLE_URL("attractive")))["records"];
}

export function getSceneCharacterSettings(): Promise<CharacterSceneSettingItem[]> {
  return getJson(GET_TABLE_URL("character_scene_setting")) ?? [];
}
