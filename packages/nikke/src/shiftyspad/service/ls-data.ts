// import { useIndexDB } from "packages/utils/storage";
// import { STORAGE_INDEXDB_HISTORY_GAMEDATA } from "packages/configs/storage";

// const { staleWhileRevalidate } = useIndexDB();

// export const getJson = (() => {
//   const cacheData: Map<
//     string,
//     {
//       promise: any;
//       data: any;
//     }
//   > = new Map();
//   return async (json_path: string) => {
//     const req = cacheData.get(json_path);
//     const file_name = json_path.split("/").pop();
//     if (req?.data) {
//       return req.data;
//     }
//     if (req?.promise) {
//       await cacheData.get(json_path)?.promise;
//     }
//     if (!req) {
//       const cache_key = `${STORAGE_INDEXDB_HISTORY_GAMEDATA}${file_name}`;
//       const promise = staleWhileRevalidate(cache_key, {
//         async handler() {
//           const res = await fetch(json_path);
//           const data = await res.json();
//           if (res.status.toString().startsWith("4")) {
//             throw new Error("load json failed");
//           }
//           return data;
//         },
//         callback(value) {
//           // @ts-expect-error this is not important
//           window[cache_key] = value;
//         },
//       })();
//       cacheData.set(json_path, {
//         promise,
//         data: null,
//       });
//       try {
//         const data = await promise;
//         cacheData.set(json_path, { promise, data });
//       } catch (err) {
//         console.error(err);
//         cacheData.delete(json_path);
//       }

//       if (!cacheData.get(json_path)?.data) {
//         cacheData.delete(json_path);
//         return null;
//       }

//       return cacheData.get(json_path)!.data;
//     }
//   };
// })();

import { LRUCache } from "packages/utils/cache";

// TODO: refactor
// wrap with same mode
export const getJson = (() => {
  const cacheData = new LRUCache<string, { promise: any; data: any }>(30);
  return async function (url: string) {
    const req = cacheData.get(url);
    if (req?.data) {
      return req.data;
    }
    if (!req) {
      const promise = fetch(url)
        .then(async (res) => {
          if (res.status.toString().startsWith("4")) {
            throw new Error("load json failed");
          }
          const data = await res.json();
          cacheData.put(url, { promise, data });
          return data;
        })
        .catch((err) => {
          cacheData.delete(url);
          console.error("[load-json-failed]:", url, err);
          // aegis.error("[load-json-failed]:", url, err);
          throw err;
        });
      cacheData.put(url, {
        promise,
        data: null,
      });
      await promise;
    } else if (!req?.data && req?.promise) {
      await cacheData.get(url)?.promise;
    }
    if (!cacheData.get(url)?.data) {
      cacheData.delete(url);
      return null;
    } else {
      return cacheData.get(url)!.data;
    }
  };
})();
