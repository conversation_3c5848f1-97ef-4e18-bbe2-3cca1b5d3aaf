<template>
  <div class="about">
    <div class="error-box">
      <p class="error-desc">UNAUTHORIZED</p>
      <h1 class="error-401">401</h1>
    </div>
    <a href="javascript:;" class="error-goback" @click="router.back()">{{ t("back") }}</a>
  </div>
</template>

<script setup lang="ts">
import { useLoadingState } from "@/shiftyspad/composable/state/loading";
import { t } from "@/locales";
import { useRouter } from "vue-router";

const router = useRouter();
useLoadingState();
</script>
