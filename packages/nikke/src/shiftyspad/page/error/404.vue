<template>
  <div class="about">
    <div class="error-box">
      <p class="error-desc">PAGE NOT FOUND</p>
      <h1 class="error-404">404</h1>
    </div>
    <a href="javascript:;" class="error-goback" @click="back">{{ t("back") }}</a>
  </div>
</template>

<script setup lang="ts">
import { useLoadingState } from "@/shiftyspad/composable/state/loading";
import { Routes } from "@/router/routes";
import { useRouter } from "vue-router";
import { t } from "@/locales";

useLoadingState();

const router = useRouter();
const back = () => {
  router.push({
    name: Routes.HOME,
  });
};
</script>
