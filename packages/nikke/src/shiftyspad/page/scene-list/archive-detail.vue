<!-- 剧情-档案馆详情 -->
<script setup lang="ts">
import { ref, toRefs } from "vue";
import { t } from "@/locales";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useArchive } from "@/shiftyspad/composable/archive";
import { computed } from "vue";
import { ICONS_URL } from "@/shiftyspad/const/urls";
import { report } from "packages/utils/tlog";
import { useLoadingState } from "@/shiftyspad/composable/state/loading";
import Nodata from "@/components/common/nodata.vue";

useLoadingState();
const props = defineProps<{
  id?: string;
}>();
const { id } = toRefs(props);
const cid = ref(Number(id.value));
const router = useRouter();

const { showArchiveList } = useArchive();
const itemData = computed(() => {
  return showArchiveList.value.find((s) => Number(s.id) === Number(cid.value));
});

// 是否多剧情模式
const multiFlag = computed(() => {
  if (itemData.value) {
    return itemData.value.record_main_archive_event_id.event.child.value.length > 1;
  } else {
    return false;
  }
});

function goDetail(index: number | string) {
  // 剧情图鉴--档案馆--档案-子档案按钮点击埋点
  report.small_tool_plot_archives_archives_sub_btn.cm_click({
    archives_id: String(id.value),
    sub_archives_id: String(index),
  });
  if (multiFlag.value) {
    router.push({
      name: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE_DETAIL_DETAIL,
      params: { id: id.value, index: index },
    });
  } else {
    router.push({ name: RoutesName.SHIFTYSPAD_SECTION, params: { id: index } });
  }
}

const showStory = computed(() => {
  // 返回第一个story
  if (itemData.value) {
    return itemData.value.record_main_archive_event_id.event.child?.value?.[0]?.story.album.scene;
  } else {
    return null;
  }
});

const showScene = computed(() => {
  if (showStory.value?.sub_category_id) {
    return showStory.value.sub_category_id.scenes.value || [];
  } else {
    return [];
  }
});

const showSceneName = computed(() => {
  if (showStory.value?.sub_category_name_localkey) {
    return showStory.value.sub_category_name_localkey.chapter_name;
  } else {
    return "";
  }
});

const showChild = computed(() => {
  if (itemData.value) {
    return itemData.value.record_main_archive_event_id.event.child.value.map((v) => {
      return Object.assign(v, {
        thumb: ICONS_URL({
          path: "/album/outpost",
          name: v.story.album.scene.sub_category_thumbnail,
        }),
        total: v.story.album.scene.sub_category_id.scenes.value.length,
      });
    });
  } else {
    return [];
  }
});
</script>
<template>
  <div class="relative archive-detail">
    <!-- <BackBtn mode="black"></BackBtn> -->
    <div class="archive-detail-bd">
      <div class="flex align-center mt-20 c-tips">
        <i class="icon-tip"></i>{{ t("archive_tip") }}
      </div>
      <div class="mx-auto mt-20 archive-banner">
        <img v-if="itemData" :src="itemData.bg" alt="" />
      </div>
      <!-- 单剧情 -->
      <template v-if="!multiFlag">
        <div
          v-for="(scene, index) in showScene"
          :key="index"
          class="cursor-pointer mx-auto mt-30 flex align-center justify-between chapter-section-item"
          @click="goDetail(scene.value.scenario_group_id)"
        >
          <p
            class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
          >
            <i class="chapter-section-icon-etc flex-none"></i>{{ showSceneName }}
          </p>
          <p class="w-60 flex-none flex align-center chapter-section-item-name">
            <i class="chapter-section-icon-divider flex-none"></i
            >{{ scene.scenario_name_localkey.scenario_name }}
          </p>
          <!-- <span class="chapter-section-icon-star"></span> -->
        </div>
        <Nodata v-if="!showScene.length" />
        <!-- 占位元素，使得列表看上去左对齐 -->
        <div
          style="visibility: hidden"
          class="flex align-center justify-between chapter-section-item"
        >
          <p
            class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
          >
            <i class="chapter-section-icon-etc flex-none"></i>
          </p>
          <p class="w-60 flex-none flex align-center chapter-section-item-name">
            <i class="chapter-section-icon-divider flex-none"></i>
          </p>
          <span class="chapter-section-icon-star"></span>
        </div>
      </template>
      <!-- 多剧情 -->
      <div v-else class="mx-auto mt-30 flex flex-wrap section-albums">
        <div
          v-for="(chapter, index) in showChild"
          :key="chapter.story.value.id"
          class="cursor-pointer mt-10 flex relative chapter-section-album"
          @click="goDetail(index)"
        >
          <i class="absolute chapter-section-album-line"></i>
          <!-- <img class="chapter-section-album-img" :src="chapter.thumb" alt="" /> -->
          <img class="chapter-section-album-img" :src="itemData?.img" alt="" />
          <div>
            <p class="flex justify-between align-center chapter-section-album-name">
              <i class="chapter-section-album-name-arrow"></i
              >{{ (chapter.story as any)?.name_value ?? "-" }}
            </p>
            <!-- <p class="chapter-section-album-prog"><span>2</span>/3</p> -->
            <p class="ff-num chapter-section-album-prog">
              <span>{{ chapter.total }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.archive-detail {
  padding-top: px2rem(88);
  background: #d4d2d1 url(@/shiftyspad/assets/images/empty-bgdeco.png) no-repeat center px2rem(900);
  background-size: px2rem(701) px2rem(349);
  padding-bottom: px2rem(50);
  min-height: 100vh;
}
.c-tips {
  color: #8e8c8b;
  font-size: px2rem(20);
  padding-left: px2rem(30);
}
.icon-tip {
  display: block;
  background: url(@/shiftyspad/assets/images/icon-tip.png) no-repeat center top / 100%;
  width: px2rem(20);
  height: px2rem(20);
  margin-right: px2rem(10);
}
.archive-banner {
  width: px2rem(690);
  height: px2rem(164);
  border-radius: px2rem(8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: px2rem(8);
  }
}
.section-albums {
  width: px2rem(690);
  padding-left: px2rem(30);
  box-sizing: border-box;
}
.chapter-section-album {
  width: px2rem(660);
  height: px2rem(100);
  background: #f0f0f0;
  border-radius: px2rem(8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  padding: px2rem(8) px2rem(10) px2rem(8) px2rem(18);
  box-sizing: border-box;
  &-line {
    background: url(@/shiftyspad/assets/images/storyline/archive-deco-line.png) no-repeat center top /
      100%;
    width: px2rem(42);
    height: px2rem(15);
    left: px2rem(-30);
    top: 50%;
    margin-top: px2rem(-8);
    pointer-events: none;
  }
  &::before {
    display: block;
    content: "";
    height: px2rem(100);
    width: px2rem(1);
    background: #7e7e7e;
    position: absolute;
    left: px2rem(-24);
    top: px2rem(50);
  }
  &:last-child::before {
    display: none;
  }
  &-img {
    display: block;
    width: px2rem(100);
    height: px2rem(84);
    border-radius: px2rem(8);
    margin-right: px2rem(15);
    object-fit: cover;
  }
  &-name {
    background: url(@/shiftyspad/assets/images/storyline/archive-itembg.png) no-repeat center top /
      100%;
    width: px2rem(522);
    height: px2rem(46);
    color: #fff;
    font-size: px2rem(22);
    padding: 0 px2rem(12) 0 px2rem(8);
    box-sizing: border-box;
    text-align: right;
    &-arrow {
      display: block;
      width: 0;
      height: 0;
      border: transparent solid px2rem(9);
      border-left-color: #f00;
    }
  }
  &-prog {
    text-align: right;
    color: #84817d;
    font-size: px2rem(22);
    span {
      font-size: px2rem(26);
      color: #2d2d2f;
    }
  }
}
</style>
<style lang="scss">
.is-mobile {
  .archive-detail {
    min-height: 70vh !important;
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .archive-detail {
    padding-top: px2rem(56);
    padding-bottom: 0;
    .archive-detail-bd {
      min-height: 100vh;
    }
    .c-tips {
      width: px2rem(1028px);
      margin-left: auto;
      margin-right: auto;
      padding-left: 0;
    }
  }
  .archive-banner {
    width: px2rem(1028px);
    img {
      object-fit: cover;
    }
  }
  .section-albums {
    width: px2rem(1028px);
  }
  .chapter-section-album {
    width: px2rem(1028px);
  }
  .chapter-section-album-name {
    width: px2rem(860px);
    background-size: 100% 100%;
    background-color: #2d2d2f;
    border-radius: px2rem(8);
  }
}
</style>
