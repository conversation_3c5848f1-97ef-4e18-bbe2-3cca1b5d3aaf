<!-- 剧情-档案馆详情 -->
<script setup lang="ts">
import { ref, toRefs } from "vue";
import { t } from "@/locales";
import { useRouter } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useArchive } from "@/shiftyspad/composable/archive";
import { computed } from "vue";
import { useLoadingState } from "@/shiftyspad/composable/state/loading";

useLoadingState();
const props = defineProps<{
  id?: string;
  index?: string;
}>();
const { id, index } = toRefs(props);
const cid = ref(Number(id.value));
const router = useRouter();

const { showArchiveList } = useArchive();
const itemData = computed(() => {
  const targetIndex = showArchiveList.value.findIndex((s) => s.id === cid.value);
  return showArchiveList.value[targetIndex];
});

// 是否多剧情模式
const multiFlag = false;

function goDetail(id: number) {
  router.push({ name: RoutesName.SHIFTYSPAD_SECTION, params: { id: id } });
}

const showStory = computed(() => {
  // 返回第一个story
  if (itemData.value) {
    // @ts-ignore
    return itemData.value.record_main_archive_event_id.event.child.value[index.value].story.album
      .scene;
  } else {
    return {};
  }
});

const showScene = computed(() => {
  if (showStory.value.sub_category_id) {
    return showStory.value.sub_category_id.scenes.value || [];
  } else {
    return [];
  }
});

const showSceneName = computed(() => {
  if (showStory.value.sub_category_name_localkey) {
    return showStory.value.sub_category_name_localkey.chapter_name;
  } else {
    return "";
  }
});
</script>
<template>
  <div class="relative archive-detail">
    <!-- <BackBtn mode="black"></BackBtn> -->
    <div class="archive-detail-bd">
      <div class="flex align-center mt-20 c-tips">
        <i class="icon-tip"></i>{{ t("archive_tip") }}
      </div>
      <div class="mx-auto mt-20 archive-banner">
        <img v-if="itemData" :src="itemData.bg" alt="" />
      </div>
      <!-- 单剧情 -->
      <template v-if="!multiFlag">
        <div
          v-for="(scene, index) in showScene"
          :key="index"
          class="cursor-pointer mx-auto mt-30 flex align-center justify-between chapter-section-item"
          @click="goDetail(scene.value.scenario_group_id)"
        >
          <p
            class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
          >
            <i class="chapter-section-icon-etc flex-none"></i>{{ showSceneName }}
          </p>
          <p class="w-60 flex-none flex align-center chapter-section-item-name">
            <i class="chapter-section-icon-divider flex-none"></i
            >{{ scene.scenario_name_localkey.scenario_name }}
          </p>
          <!-- <span class="chapter-section-icon-star"></span> -->
        </div>
        <!-- 占位元素，使得列表看上去左对齐 -->
        <div
          style="visibility: hidden"
          class="flex align-center justify-between chapter-section-item"
        >
          <p
            class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
          >
            <i class="chapter-section-icon-etc flex-none"></i>
          </p>
          <p class="w-60 flex-none flex align-center chapter-section-item-name">
            <i class="chapter-section-icon-divider flex-none"></i>
          </p>
          <span class="chapter-section-icon-star"></span>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss">
.archive-detail {
  padding-top: px2rem(88);
  background: #d4d2d1;
  padding-bottom: px2rem(50);
  min-height: 100vh;
}
.c-tips {
  color: #8e8c8b;
  font-size: px2rem(20);
  padding-left: px2rem(30);
}
.icon-tip {
  display: block;
  background: url(@/shiftyspad/assets/images/icon-tip.png) no-repeat center top / 100%;
  width: px2rem(20);
  height: px2rem(20);
  margin-right: px2rem(10);
}
.archive-banner {
  width: px2rem(690);
  height: px2rem(164);
  border-radius: px2rem(8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: px2rem(8);
  }
}
.section-albums {
  width: px2rem(690);
  padding-left: px2rem(30);
  box-sizing: border-box;
}
.chapter-section-album {
  width: px2rem(660);
  height: px2rem(100);
  background: #f0f0f0;
  border-radius: px2rem(8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  padding: px2rem(8) px2rem(10) px2rem(8) px2rem(18);
  box-sizing: border-box;
  &-line {
    background: url(@/shiftyspad/assets/images/storyline/archive-deco-line.png) no-repeat center top /
      100%;
    width: px2rem(42);
    height: px2rem(15);
    left: px2rem(-30);
    top: 50%;
    margin-top: px2rem(-8);
    pointer-events: none;
  }
  &::before {
    display: block;
    content: "";
    height: px2rem(100);
    width: px2rem(1);
    background: #7e7e7e;
    position: absolute;
    left: px2rem(-24);
    top: px2rem(50);
  }
  &:last-child::before {
    display: none;
  }
  &-img {
    display: block;
    width: px2rem(100);
    height: px2rem(84);
    border-radius: px2rem(8);
    margin-right: px2rem(15);
    object-fit: cover;
  }
  &-name {
    background: url(@/shiftyspad/assets/images/storyline/archive-itembg.png) no-repeat center top /
      100%;
    width: px2rem(522);
    height: px2rem(46);
    color: #fff;
    font-size: px2rem(22);
    padding: 0 px2rem(12) 0 px2rem(8);
    box-sizing: border-box;
    text-align: right;
    &-arrow {
      display: block;
      width: 0;
      height: 0;
      border: transparent solid px2rem(9);
      border-left-color: #f00;
    }
  }
  &-prog {
    text-align: right;
    color: #84817d;
    font-size: px2rem(22);
    span {
      font-size: px2rem(26);
      color: #2d2d2f;
    }
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .archive-detail {
    padding-top: px2rem(56);
    padding-bottom: 0;
    .archive-detail-bd {
      min-height: 100vh;
    }
    .c-tips {
      width: px2rem(1028px);
      margin-left: auto;
      margin-right: auto;
      padding-left: 0;
    }
  }
  .archive-banner {
    width: px2rem(1028px);
    img {
      object-fit: cover;
    }
  }
  .section-albums {
    width: px2rem(1028px);
  }
  .chapter-section-album {
    width: px2rem(1028px);
  }
  .chapter-section-album-name {
    width: px2rem(860px);
    background-size: 100% 100%;
    background-color: #2d2d2f;
    border-radius: px2rem(8);
  }
}
</style>
