<!-- 剧情详情-剧情文本 -->
<script setup lang="ts">
import { useRoute } from "vue-router";
import { computed, toRefs } from "vue";

import Loading from "@/shiftyspad/components/common/Loading.vue";
import VoiceItem from "@/shiftyspad/components/scene/attr-voice-item.vue";
import { useAttractiveScene } from "@/shiftyspad/composable/scene/attractive";
import { useUserStore } from "@/shiftyspad/stores/user";
import { getLang } from "@/shiftyspad/const";

const route = useRoute();
const { user_role_info } = toRefs(useUserStore());
const { shown_list, is_loading, jump } = useAttractiveScene(route.params.id as string);

const commander_name = computed(() => {
  const map = {
    de: "Kommandant",
    en: "Commander",
    fr: "Commandant",
    ja: "指揮官",
    "zh-TW": "指揮官",
    sea: "ผู้บัญชาการ",
    ko: "지휘관",
  } as any;
  return user_role_info.value?.role_name ?? map[getLang() as any] ?? map["en"];
});
</script>

<template>
  <div class="storyline-section">
    <!-- <BackBtn></BackBtn> -->
    <Loading v-if="is_loading" :loading="is_loading"></Loading>
    <div class="pl-30 pr-30 storyline-section-bd">
      <VoiceItem
        v-for="talk in shown_list"
        :key="talk.id"
        :scene="talk"
        :selected="talk.selected"
        :commander_name="commander_name"
        @click="jump(talk)"
      ></VoiceItem>
    </div>
  </div>
</template>

<style lang="scss">
.storyline-section {
  min-height: 70vh;
  padding-top: px2rem(108);
  background: #000 url(@/shiftyspad/assets/images/storyline/section-bg.jpg) no-repeat center top /
    100%;
  background-attachment: fixed;
  padding-bottom: px2rem(170);
  &-item {
    background: url(@/shiftyspad/assets/images/storyline/section-itembg.png) no-repeat center top /
      100% 100%;
    &--inner {
      background: url(@/shiftyspad/assets/images/storyline/section-itembg--inner.png) no-repeat
        right top / 100% 100%;
      margin-left: px2rem(40);
    }
  }
}
.icon-sound-play {
  background: url(@/shiftyspad/assets/images/icon-sound-play.png) no-repeat center top / 100%;
  width: px2rem(39);
  height: px2rem(31);
}
.storyline-section :deep(.on) {
  color: var(--brand-1) !important;
}
.section-item-left {
  margin-right: px2rem(20);
  padding-left: px2rem(80);
  position: relative;
}
.section-avatar {
  width: px2rem(70) !important;
  height: px2rem(70) !important;
  position: absolute !important;
  left: 0 !important;
  top: 50% !important;
  transform: translate(0, -50%) !important;
  object-fit: cover !important;
}
.section-spinner {
  min-width: px2rem(100);
  &.v-spinner {
    position: relative;
    top: unset;
    left: unset;
    transform: unset;
    z-index: 111;
  }
  :deep(.v-pulse) {
    width: px2rem(15) !important;
    height: px2rem(15) !important;
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .storyline-section-bd {
    width: px2rem(1028);
    margin-left: auto;
    margin-right: auto;
  }
  .storyline-section-item {
    background: url(@/shiftyspad/assets/images/storyline/pc/section-itembg.png) no-repeat center top /
      100% 100%;
    &--inner {
      background: url(@/shiftyspad/assets/images/storyline/pc/section-itembg--inner.png) no-repeat
        right top / 100% 100%;
      &:hover {
        background-image: url(@/shiftyspad/assets/images/storyline/pc/section-itembg--inner--hover.png);
      }
    }
    .icon-sound-play {
      width: px2rem(24);
      height: px2rem(20);
      background-size: auto 100%;
    }
  }
  .section-spinner {
    min-width: px2rem(80);
  }
}
</style>
