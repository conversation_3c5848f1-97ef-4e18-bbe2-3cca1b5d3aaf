<!-- 剧情详情-剧情文本 -->
<script setup lang="ts">
import { report } from "packages/utils/tlog";
import { findValidScene } from "@/shiftyspad/composable/scene/scene";
import { isMobileDevice } from "packages/utils/tools";
import { NO_VALUE_OR_ZERO } from "@/shiftyspad/const/setting";
import { GET_VOICE_URL, SM_CHARACTER_URL } from "@/shiftyspad/const/urls";
import { SceneDetailSectionData, StageData, SceneListData } from "packages/types/game-scene";

import { useCV } from "@/shiftyspad/stores/role/cv";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useVoiceStore } from "@/shiftyspad/stores/voice";
import { useJsonStore } from "@/shiftyspad/stores/json";

import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { onBeforeRouteLeave, useRouter } from "vue-router";
import { computed, toRaw, toRefs, ref, watch, onUnmounted } from "vue";

import Spinner from "@/shiftyspad/components/common/spinner.vue";
import Loading from "@/components/common/loading.vue";
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";

const isMobile = isMobileDevice();
const router = useRouter();

// 能进入剧情都有登录态
const user = useUserStore();
const voice = useVoiceStore();

const { self_shifty_user } = toRaw(user);
const { user_battle_info } = toRefs(self_shifty_user);
const { player, loading } = storeToRefs(voice);
const {
  addCanplayListener,
  addEndedListener,
  addErrorListener,
  removeCanplayListener,
  removeEndedListener,
  removeErrorListener,
} = voice;
const props = defineProps<{
  id?: string;
}>();

const { id } = toRefs(props);

const { t } = useI18n();
// 注册回调
const canplayCbIndex = ref(-1);
const errorCbIndex = ref(-1);
const endedCbIndex = ref(-1);
canplayCbIndex.value = addCanplayListener(() => {
  player.value.play();
});
errorCbIndex.value = addErrorListener(() => {});
endedCbIndex.value = addEndedListener(() => {});

const scene_loading = ref(false);
const cv = useCV();
const setVoiceState = () => {
  const has_voice = showList.value?.some((talk: any) => Boolean(talk.has_voice));
  cv.setVoiceExistState(has_voice);
};

onUnmounted(() => {
  cv.setVoiceExistState(false);
});
const json = useJsonStore();
const {
  scene_detail,
  vocie_map,
  characterSetting,
  stage_list,
  scene_main_list,
  is_character_setting_loading,
  is_stage_loading,
  is_scene_list_loading,
} = storeToRefs(json);

const showLoading = computed(
  () =>
    is_character_setting_loading.value ||
    is_stage_loading.value ||
    is_scene_list_loading.value ||
    scene_loading.value,
);

init();

const showBlock = () => {
  NoticePopup.info({
    title: t("notice"),
    message: t("scene_unlock_tip"),
    confirm: t("confirm"),
    onOk: () => {
      router.back();
    },
    onClose: () => {
      router.back();
    },
  });
};

async function init() {
  try {
    scene_loading.value = true;
    await self_shifty_user.initUserBattleInfo();
  } catch (err) {
    showBlock();
    throw err;
  }
  // 检查是否能看到该主线剧情
  if (id.value?.indexOf("d_main") === 0) {
    // 主线类型才处理
    const unlock = user_battle_info.value?.normal_progress ?? NO_VALUE_OR_ZERO;
    const isLock = isValidMainScene(id.value, unlock, stage_list.value, scene_main_list.value);
    if (isLock) {
      showBlock();
      return;
    }
  }
  await Promise.all([
    json.GetSceneDetail(id.value || ""),
    json.GetSceneVoiceMap(id.value || ""),
  ]).finally(() => {
    setVoiceState();
    scene_loading.value = false;
  });
}
function isValidMainScene(
  curScene: String,
  progress: number,
  stage_list: StageData[],
  scene_main_list: SceneListData[],
) {
  if (progress === NO_VALUE_OR_ZERO || progress === 0) {
    // 没有进度的新号
    return true;
  }
  const stage = findValidScene(progress, stage_list) as StageData;
  // 这里要注意，stage_list的chapter_id!==scene_list里的，相差1
  let unlock_chapter = stage ? stage.chapter_id - 1 : 1;
  // exit和enter都不一定有，也有可能只有其中一个
  const unlock_section = stage ? stage.exit_scenario || stage.enter_scenario : "";
  const mainSceneReg = /d_main_([0-9]+)/;
  const matchScene = curScene.match(mainSceneReg);
  if (matchScene) {
    const curChapter = Number(matchScene[1]);
    if (unlock_chapter > curChapter) {
      // 章节解锁
      return false;
    } else if (unlock_chapter < curChapter) {
      return true;
    } else {
      let lock = true;
      for (let i = 0; i < scene_main_list.length; i++) {
        const scene = scene_main_list[i];
        if (scene.sub_category_id.value < unlock_chapter) {
          continue;
        } else {
          let unlock_section_index = scene.sub_category_id.scenes.value.findIndex((s) => {
            return s.value.scenario_group_id === unlock_section;
          });
          let cur_section_index = scene.sub_category_id.scenes.value.findIndex((s) => {
            return s.value.scenario_group_id === curScene;
          });

          if (stage.name_localkey && stage.name_localkey.name.match(/BOSS$/)) {
            // 以boss结尾的通关了表示这一章解锁完毕，后边同一章节有些 d_main_xx_af_xx的after章节直接解锁
            unlock_section_index = scene.sub_category_id.scenes.value.length - 1;
          }
          lock = unlock_section_index < cur_section_index;
          break;
        }
      }
      return lock;
    }
  } else {
    return true;
  }
}

const showList = computed(() => {
  let list = scene_detail.value.scenario_group_id
    ? scene_detail.value.scenario_group_id.records.value
    : [];
  return list.map((item: SceneDetailSectionData) => {
    let index = -1;
    if (
      item.value.speech_window?.toLowerCase() === "speech" ||
      item.value.speech_window === item.speaker.value
    ) {
      index = characterSetting.value.findIndex((c: any) => c.id === item.speaker.value);
    }
    return Object.assign(item, {
      has_voice: vocie_map.value.indexOf(item.value.id) > -1,
      hide: false,
      avatar:
        index > -1
          ? SM_CHARACTER_URL({
              resource_id: characterSetting.value[index].resource_id,
            })
          : "",
    });
  });
});

watch(showList, () => {
  if (showList.value.length) {
    cur_choices.value = [];
    shown_list.value = [];
    scene_list.value = showList.value;
    stop_index.value = 0;
    insertSpeech();
  }
});

const stop_index = ref<number>(0);
const selected = ref<Set<number>>(new Set());
const cur_choices = ref<number[]>([]);
const scene_list = ref<
  (SceneDetailSectionData & {
    has_voice: boolean;
    avatar: string;
    hide: boolean;
  })[]
>([]);
const shown_list = ref<
  (SceneDetailSectionData & {
    has_voice: boolean;
    hide: boolean;
    selected: boolean;
    avatar: string;
  })[]
>([]);
const insertSpeech = () => {
  for (let i = stop_index.value; i < showList.value.length; i++) {
    const cur: any = showList.value[i];
    cur.selected = false;
    cur.hide = false;
    shown_list.value.push(cur);
    if (cur.value.speech_window === "Choice") {
      cur_choices.value.push(i);
    }
    if (
      cur.value.speech_window === "Choice" &&
      showList.value[i + 1]?.value.speech_window !== "Choice"
    ) {
      stop_index.value = i;
      break;
    }
    if (cur.value.jump_target && cur.value.speech_window !== "Choice") {
      i = showList.value.findIndex((v: any) => v.value.id === cur.value.jump_target);
      // for循环最后会+1，这里要-1
      i--;
    }
  }
};
const jump = (
  talk: SceneDetailSectionData & {
    has_voice: boolean;
    hide: boolean;
    selected: boolean;
    avatar: string;
  },
) => {
  if (!talk.value.jump_target || talk.selected) {
    return;
  }
  const selected_index = scene_list.value.findIndex((v) => v.value.id === talk.value.id);
  selected.value.add(selected_index);
  talk.selected = true;
  cur_choices.value
    .filter((i) => !selected.value.has(i))
    .forEach((i) => (scene_list.value[i].hide = true));
  const next_index = scene_list.value.findIndex((v) => v.value.id === talk.value.jump_target);
  stop_index.value = next_index;
  insertSpeech();
};

let nextTimer: any = null;

const playVoice = (voiceId: string, index: number) => {
  report.small_tool_plot_main_line_chapter_sub_item.cm_click({
    voice_id: voiceId,
    chapter_id: String(id.value ?? "-"),
  });
  playingIndex.value = index;
  if (nextTimer) {
    clearTimeout(nextTimer);
    nextTimer = null;
  }
  const url = GET_VOICE_URL({
    cv_lang: cv.cv_lang.lang,
    speech_id: voiceId,
    format: "mp3",
  });
  if (playing.value === url) {
    // 暂停 or 继续
    if (!player.value.paused) {
      player.value.pause();
      return;
    } else if (!loading.value) {
      player.value.play();
    }
    return;
  }

  playing.value = url;
  if (player.value.src) {
    // 停止播放上一条
    player.value.pause();
    player.value.src = "";
  }

  // 播放语音
  loading.value = true;
  player.value.src = url;
};

onBeforeRouteLeave(() => {
  // 离开页面销毁player
  player.value.pause();
  player.value.src = "";
  clearTimeout(nextTimer);
  removeCanplayListener(canplayCbIndex.value);
  removeEndedListener(endedCbIndex.value);
  removeErrorListener(errorCbIndex.value);
});

const isSelfSection = (talk: SceneDetailSectionData) => {
  const speaker = talk.value.speaker?.toLowerCase();
  const speech_window = talk.value.speech_window?.toLowerCase() ?? "";
  return speech_window === "self" || speaker === "self";
};

// 特殊处理名字展示
function speakerHandler(talk: SceneDetailSectionData) {
  const speech_window = talk.value.speech_window?.toLowerCase() ?? "";
  if (["narration", "monologue", "subtitles"].includes(speech_window)) {
    return "";
  }
  if (speech_window === "choice") {
    return t("choice");
  }
  if (isSelfSection(talk)) {
    return user.user_role_info?.role_name;
  }
  // speech window -> speech
  return talk.speaker.name_localkey.character_name || talk.value.speaker;
}
function textHandler(str: string) {
  return (str ? str.replace(/@UserName/g, user.user_role_info?.role_name ?? "Player") : "")
    .replace(/\\n/g, "")
    .replace(/\\\\n/g, "");
}

function removeAvatar(index: number) {
  showList.value[index].avatar = "";
}

const playing = ref("");
const playingIndex = ref(-1);
</script>
<template>
  <div v-if="showLoading" class="flex flex-1 justify-center items-center min-h-[60vh]">
    <Loading></Loading>
  </div>
  <div v-else class="storyline-section">
    <!-- <BackBtn></BackBtn> -->
    <div class="pl-30 pr-30 storyline-section-bd">
      <div
        v-for="(talk, index) in shown_list"
        v-show="!talk.hide"
        :key="talk.value.id"
        :data-key="talk.value.id"
        :class="{
          'storyline-section-item--inner cursor-pointer':
            talk.value.speech_window === 'Choice' && !talk.selected,
        }"
        class="p-20 bg-black mt-20 flex justify-between align-center storyline-section-item"
        @click="jump(talk)"
      >
        <div class="section-item-left">
          <!-- <RoleAvatar
            v-if="isSelfSection(talk)"
            width="100%"
            scene="section"
            class="section-avatar bg-transparent"
            :loading_size="'3px'"
            :avatar_id="user_role_info?.icon ?? 0"
          ></RoleAvatar> -->
          <img
            v-if="talk.avatar && !isSelfSection(talk)"
            :src="talk.avatar"
            class="section-avatar"
            width="100%"
            @error="removeAvatar(index)"
          />
          <p class="text-white-60" :class="{ 'text-24': isMobile, 'text-16': !isMobile }">
            {{ speakerHandler(talk) }}
          </p>
          <p
            class="text-white"
            :class="{
              'text-28': isMobile,
              'text-20': !isMobile,
              on: playingIndex === index,
            }"
          >
            {{ textHandler(talk.quest_name) }}
          </p>
        </div>
        <Spinner v-if="playingIndex === index && loading == true" class="section-spinner"></Spinner>
        <span
          v-if="talk.has_voice"
          v-show="playingIndex !== index || loading == false"
          class="flex-none ml-10 icon-sound-play cursor-pointer"
          :class="{ on: playingIndex === index, choice: talk.value.speech_window === 'Choice' }"
          @click="playVoice(talk.value.id, index)"
        ></span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.storyline-section {
  padding-top: px2rem(108);
  background: #000 url(@/shiftyspad/assets/images/storyline/section-bg.jpg) no-repeat center top /
    100%;
  background-attachment: fixed;
  padding-bottom: px2rem(170);
  &-item {
    background: url(@/shiftyspad/assets/images/storyline/section-itembg.png) no-repeat center top /
      100% 100%;
    user-select: none;
    &--inner {
      background: url(@/shiftyspad/assets/images/storyline/section-itembg--inner.png) no-repeat
        right top / 100% 100%;
      margin-left: px2rem(40);
    }
  }
}
.icon-sound-play {
  background: url(@/shiftyspad/assets/images/icon-sound-play.png) no-repeat center top / 100%;
  width: px2rem(39);
  height: px2rem(31);
  min-width: px2rem(39);
  &.on {
    background-image: url(@/shiftyspad/assets/images/icon-sound-play-on.png);
  }
}
.storyline-section :deep(.on) {
  color: var(--brand-1) !important;
}
.choice {
  cursor: pointer;
  text-decoration: underline;
  color: #008fd3;
}
.section-item-left {
  margin-right: px2rem(20);
  padding-left: px2rem(80);
  position: relative;
}
.section-avatar {
  width: px2rem(70) !important;
  height: px2rem(70) !important;
  position: absolute !important;
  left: 0 !important;
  top: 50% !important;
  transform: translate(0, -50%) !important;
  object-fit: cover !important;
}
.section-spinner {
  min-width: px2rem(100);
  &.v-spinner {
    position: relative;
    top: unset;
    left: unset;
    transform: unset;
    z-index: 111;
  }
  :deep(.v-pulse) {
    width: px2rem(15) !important;
    height: px2rem(15) !important;
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .storyline-section {
    padding-top: px2rem(82);
  }
  .storyline-section-bd {
    width: px2rem(1028);
    margin-left: auto;
    margin-right: auto;
  }
  .storyline-section-item {
    background: url(@/shiftyspad/assets/images/storyline/pc/section-itembg.png) no-repeat center top /
      100% 100%;
    &--inner {
      background: url(@/shiftyspad/assets/images/storyline/pc/section-itembg--inner.png) no-repeat
        right top / 100% 100%;
      &:hover {
        background-image: url(@/shiftyspad/assets/images/storyline/pc/section-itembg--inner--hover.png);
      }
    }
    .icon-sound-play {
      width: px2rem(24);
      height: px2rem(20);
      background-size: auto 100%;
    }
  }
  .section-spinner {
    min-width: px2rem(80);
  }
}
</style>
