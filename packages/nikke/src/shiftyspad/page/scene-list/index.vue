<!-- 剧情图鉴 -->
<script setup lang="ts">
import { onMounted, ref } from "vue";
import StorylineMain from "@/shiftyspad/components/scene-list/Main.vue";
import StorylineEvent from "@/shiftyspad/components/scene-list/Event.vue";
import StorylineArchive from "@/shiftyspad/components/scene-list/Archive.vue";
import { RoutesName } from "@/router/routes";
import { useRoute, useRouter } from "vue-router";
import { report } from "packages/utils/tlog";
import { useLoadingState } from "@/shiftyspad/composable/state/loading";
import { t } from "@/locales/index";
import TabScroll from "@/components/common/tabs/tab-scroll.vue";

useLoadingState();
const router = useRouter();
const route = useRoute();
enum ListTab {
  MAIN = "main",
  EVENT = "sudden",
  ARCHIVE = "archive",
}

const pathToTab = {
  [RoutesName.SHIFTYSPAD_SCENE_MAIN]: ListTab.MAIN,
  [RoutesName.SHIFTYSPAD_SCENE_SUDDEN]: ListTab.EVENT,
  [RoutesName.SHIFTYSPAD_SCENE_ARCHIVE]: ListTab.ARCHIVE,
};

// @ts-ignore
const curTab = ref(pathToTab[route.name] || ListTab.MAIN);

const tabs = ref([
  {
    id: ListTab.MAIN,
    key: ListTab.MAIN,
    name: "scene_tab_main",
    path: RoutesName.SHIFTYSPAD_SCENE_MAIN,
    label: ` ${t("scene_tab_main")}`,
    icon: "icon-story",
    value: ListTab.MAIN,
  },
  {
    id: ListTab.EVENT,
    key: ListTab.MAIN,
    name: "scene_tab_event",
    path: RoutesName.SHIFTYSPAD_SCENE_SUDDEN,
    label: ` ${t("scene_tab_event")}`,
    icon: "icon-encounter",
    value: ListTab.EVENT,
  },
  {
    id: ListTab.ARCHIVE,
    key: ListTab.MAIN,
    name: "scene_tab_archive",
    path: RoutesName.SHIFTYSPAD_SCENE_ARCHIVE,
    label: ` ${t("scene_tab_archive")}`,
    icon: "icon-archives",
    value: ListTab.ARCHIVE,
  },
]);

function switchTab(id: ListTab, path: RoutesName) {
  // 埋点
  const name = {
    main: "small_tool_plot_main_line_btn",
    sudden: "small_tool_plot_break_btn",
    archive: "small_tool_plot_archives_btn",
  } as const;
  report[name[id]]?.cm_click({});
  curTab.value = id;
  router.replace({ name: path });
}

onMounted(() => {
  report.small_campaign_list_page.cm_vshow({ tab_name: curTab.value });
});

const onChangeTab = (id: number | string) => {
  curTab.value = id;
  const tab = tabs.value.find((item) => {
    return item.value === curTab.value;
  });
  switchTab(tab!.id, tab!.path);
};
</script>

<template>
  <div class="relative">
    <!-- <BackBtn></BackBtn> -->
    <TabScroll
      class="tab-scroll relative z-[2]"
      :tabs="tabs"
      :active_id="curTab"
      @change="onChangeTab"
    ></TabScroll>
    <div class="storyline-bd">
      <StorylineMain v-if="curTab == ListTab.MAIN"></StorylineMain>
      <StorylineEvent v-else-if="curTab == ListTab.EVENT"></StorylineEvent>
      <StorylineArchive v-else-if="curTab == ListTab.ARCHIVE"></StorylineArchive>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.storyline {
  padding-top: px2rem(88 + 42);
  height: 100%;
  background: #eff2f6 url(@/shiftyspad/assets/images/storyline/storyline-bg.jpg) no-repeat center
    top / 100%;
}
.storyline-tab {
  height: px2rem(63);
  a {
    width: px2rem(220);
    height: 100%;
    background: #2b2b2e;
    color: #fff;
    font-size: px2rem(22);
    line-height: 1.5;
    i {
      display: block;
      background: url(@/shiftyspad/assets/images/storyline/storyline-icons.png) no-repeat;
      background-size: px2rem(114) px2rem(68);
      width: px2rem(24);
      height: px2rem(30);
      margin-right: px2rem(10);
    }
    &:nth-child(1) {
      i {
        background-position: 0 0;
      }
    }
    &:nth-child(2) {
      i {
        background-position: px2rem(-38) 0;
      }
    }
    &:nth-child(3) {
      i {
        width: px2rem(30);
        background-position: px2rem(-84) 0;
      }
    }
    &.on {
      background: linear-gradient(
        to bottom,
        #16abf5 0%,
        #16abf5 px2rem(5px),
        #eff2f6 px2rem(5px),
        #eff2f6 100%
      );
      color: #303235;
      i {
        background-position-y: px2rem(-38);
      }
    }
  }
}
.storyline-bd {
}
</style>

<style lang="scss" scoped>
.is-pc {
  .storyline {
    padding-top: px2rem(82);
    background-size: 100% px2rem(136);
    padding-bottom: 0;
  }
  .storyline-bd {
    width: px2rem(1200);
    margin: 0 auto;
  }
  .storyline-tab {
    height: px2rem(54);
    a {
      width: px2rem(209);
      font-size: px2rem(18);
    }
  }
}
</style>
