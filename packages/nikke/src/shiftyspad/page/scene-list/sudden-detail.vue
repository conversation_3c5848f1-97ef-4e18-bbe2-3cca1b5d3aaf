<!-- 主线剧情详情 -->
<script setup lang="ts">
import { toRefs, ref } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, Pagination, EffectCoverflow } from "swiper/modules";
import "swiper/css";
import { useScene } from "@/shiftyspad/composable/scene/scene";
import { computed } from "vue";
import { RoutesName } from "@/router/routes";
import { useRouter } from "vue-router";
import { useLoadingState } from "@/shiftyspad/composable/state/loading";

useLoadingState();
const router = useRouter();
const props = defineProps<{
  id?: string;
}>();
const { id } = toRefs(props);
const cid = ref(Number(id.value));
const { showSuddenList } = useScene();
const swiperModules = [EffectCoverflow, Navigation, Pagination];

const onSwiperSlideChange = (swiper: any) => {
  const index: number = swiper.activeIndex;
  cid.value = showSuddenList.value[index]?.id;
};
const itemData = computed(() => {
  const targetIndex = showSuddenList.value.findIndex((s) => s.id === cid.value);
  return showSuddenList.value[targetIndex];
});
const initialIndex = computed(() => {
  const targetIndex = showSuddenList.value.findIndex((s) => s.id === cid.value);
  return targetIndex || 0;
});
const swiperList = computed(() => {
  const list = showSuddenList.value.filter((s) => !s.locked);
  return list;
});

const sections = computed(() => {
  const list = itemData.value?.sections || [];
  return list.filter((s) => !s.locked);
});

const toDetail = (id: number) => {
  router.push({ name: RoutesName.SHIFTYSPAD_SCENE_SUDDEN_DETAIL, params: { id: cid.value } });
  // fix me 特殊处理tab切换后返回要定位到对应的tab
  setTimeout(() => {
    router.push({ name: RoutesName.SHIFTYSPAD_SECTION, params: { id } });
  }, 100);
};
</script>
<template>
  <div class="relative storyline-detail">
    <!-- <BackBtn mode="black"></BackBtn> -->
    <div class="relative detail-swiper">
      <swiper
        ref="storylineDetailSwiper"
        direction="horizontal"
        :centered-slides="true"
        :space-between="12"
        :initial-slide="initialIndex"
        :slides-per-view="'auto'"
        :effect="'coverflow'"
        :slide-to-clicked-slide="true"
        :coverflow-effect="{ slideShadows: false, rotate: 0, depth: 0, scale: 0.9 }"
        :navigation="{ prevEl: '.detail-swiper-prev', nextEl: '.detail-swiper-next' }"
        :modules="swiperModules"
        @slide-change="onSwiperSlideChange"
      >
        <swiper-slide v-for="item in swiperList" :key="item.id">
          <div class="storyline-mitem detail-swiper-item" :class="{ locked: item.locked }">
            <img :key="item.img" v-lazy="item.img" class="chapter-img" alt="" />
            <p class="chapter-index">
              <span>{{ item.order }}</span>
            </p>
            <div class="flex justify-between">
              <!-- <p class="chapter-name">{{ item.name }}</p> -->
              <p class="chapter-name">
                <span v-automarquee class="ff-tt-bold">{{ item.name }}</span>
              </p>
              <span class="chapter-prog">{{ item.sectionTotal }}</span>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <a href="javascript:;" class="detail-swiper-prev"></a>
      <a href="javascript:;" class="detail-swiper-next"></a>
      <div class="absolute detail-swiper-bg"></div>
    </div>
    <div class="flex justify-center flex-wrap detail-sections">
      <div
        v-for="section in sections"
        :key="section.scenario_group_id"
        class="flex align-center justify-between chapter-section-item cursor-pointer"
        @click="toDetail(section.scenario_group_id as number)"
      >
        <p
          class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
        >
          <i class="chapter-section-icon-etc flex-none"></i>{{ itemData.name }}
        </p>
        <p class="w-60 flex-none flex align-center chapter-section-item-name">
          <i class="chapter-section-icon-divider flex-none"></i>{{ section.name }}
        </p>
        <!-- <span class="chapter-section-icon-star"></span> -->
      </div>
      <!-- 占位元素，使得列表看上去左对齐 -->
      <div
        style="visibility: hidden"
        class="flex align-center justify-between chapter-section-item"
      >
        <p
          class="w-40 flex-none flex align-center chapter-section-item-chapter-name pr-10 box-border"
        >
          <i class="chapter-section-icon-etc flex-none"></i>
        </p>
        <p class="w-60 flex-none flex align-center chapter-section-item-name">
          <i class="chapter-section-icon-divider flex-none"></i>
        </p>
        <span class="chapter-section-icon-star"></span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.storyline-detail {
  background: #d4d2d1;
  padding-top: px2rem(88);
  padding-bottom: px2rem(50);
  min-height: calc(100vh - 9rem);
}
.detail-swiper {
  .swiper {
    z-index: 2;
  }
  .swiper-slide {
    width: px2rem(224);
    height: px2rem(340);
    // transition: transform .3s ease, opacity .3s ease;
    // opacity: 0;
  }
  .swiper-slide-prev,
  .swiper-slide-next {
    filter: brightness(0.75);
    // transform: scale(0.9);
    // opacity: 1;
  }
  .swiper-slide-active {
    filter: none;
    // transform: scale(1);
    // opacity: 1;
  }
}
.detail-swiper-prev,
.detail-swiper-next {
  // display: none;
  width: px2rem(24);
  height: px2rem(34);
  background: url(@/shiftyspad/assets/images/icon-swiper-prev.png) no-repeat center top / 100%;
  position: absolute;
  top: px2rem(205);
  z-index: 2;
  display: none;
}
.detail-swiper-prev {
  left: px2rem(10);
}
.detail-swiper-next {
  background-image: url(@/shiftyspad/assets/images/icon-swiper-next.png);
  right: px2rem(10);
}
.detail-swiper-bg {
  width: px2rem(722);
  height: px2rem(211);
  background: #2b2b2e url(@/shiftyspad/assets/images/icon-xxx.png) no-repeat 97% 95%;
  background-size: px2rem(81);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0);
}
.detail-sections {
  margin-top: px2rem(10);
}
.swiper-slide-active .storyline-mitem {
  cursor: default;
}
.storyline-mitem {
  width: px2rem(224);
  height: px2rem(320);
  cursor: pointer;
  .chapter-img {
    width: px2rem(211);
    height: px2rem(211);
  }
  .chapter-name {
    width: px2rem(140);
    font-size: px2rem(24);
    white-space: nowrap;
    overflow: hidden;
  }
  .chapter-prog {
    font-size: px2rem(22);
    em {
      font-size: px2rem(24);
    }
  }
  &::after {
    display: none !important;
  }
}
</style>
<style lang="scss" scoped>
.is-pc {
  .storyline-detail {
    padding-top: px2rem(56);
    padding-bottom: 0;
  }
  .detail-swiper {
    width: px2rem(700);
    margin-left: auto;
    margin-right: auto;
  }
  .detail-swiper-bg {
    width: px2rem(1028);
  }
  .detail-swiper-prev,
  .detail-swiper-next {
    display: block;
  }
  .detail-swiper-prev {
    left: px2rem(-50);
  }
  .detail-swiper-next {
    right: px2rem(-50);
  }
  .detail-sections {
    width: px2rem(1028);
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
