<!-- author: laytonmo -->
<script setup lang="ts">
import { useUserStore } from "@/shiftyspad/stores/user";
import { useSortNikkeList, FilterType, FilterItem } from "@/shiftyspad/composable/nikke-filter";
import {
  getCleanIconByCorp,
  getClassAssetIcon,
  getElementAssetIcon,
  getWeaponAssetIcon,
} from "@/shiftyspad/composable/icon";

import { t } from "@/locales/index";
import { RoutesName } from "@/router/routes";
import { report } from "packages/utils/tlog";
import { COMMON_QUERY_KEYS } from "@/configs/const";

import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { computed, onMounted, onUnmounted, ref } from "vue";

import { Switch } from "@/components/ui/switch";
import Loading from "@/components/common/loading.vue";
import AllItem from "@/shiftyspad/components/nikke-list/all-item.vue";
import PlayerItem from "@/shiftyspad/components/nikke-list/player-item.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import Btn from "@/components/common/btn/index.vue";

const { user_nikkelist_info, has_role } = storeToRefs(useUserStore());
const {
  // sort
  resetSort,
  setSort,
  sort_config,

  // filter
  setFilterValue,
  resetFilter,
  filters,

  // search
  resetSearch,
  name_search,

  // obtained
  obtainer_filter,
  changeFilter,

  is_nikke_list_loading,
  filter_player_nikke_list,
  filter_combine_nikke_list,
} = useSortNikkeList({ user_nikkelist_info });
const router = useRouter();

const nikke_list = computed(() =>
  obtainer_filter.value
    ? filter_player_nikke_list.value.slice().map((v) => Object.assign({ is_obtained: true }, v))
    : filter_combine_nikke_list.value,
);

const filterShownFlag = ref(false);
const hideFilter = (e: any) => {
  if (filterShownFlag.value && !document.querySelector(".filter-panel-wrap")?.contains(e.target)) {
    filterShownFlag.value = false;
  }
};
onMounted(() => {
  report.standalonesite_nikkepedia_page.cm_vshow({});
  document.body.addEventListener("touchstart", hideFilter);
  document.body.addEventListener("click", hideFilter);
});
onUnmounted(() => {
  document.body.removeEventListener("touchstart", hideFilter);
  document.body.removeEventListener("click", hideFilter);
});

// 里外两种筛选
const filters_keys_arr = computed(() => [FilterType.burst, FilterType.class]);
const outerIndex = ref(filters_keys_arr.value.findIndex((i) => i === FilterType.burst) ?? 0);
const outerType = computed<FilterType>(
  () => filters_keys_arr.value[outerIndex.value] as FilterType,
);
const setOuterFiled = (filter: FilterItem) => {
  setFilterValue(outerType.value, filter.value);
  if (outerFilter.value.every((item) => item.selected === true)) {
    resetFilter(outerType.value);
  }
};
const outerAllActive = computed(() => {
  return outerFilter.value.every((item) => item.selected === false);
});
const outerFilter = computed(() => {
  return filters.value[outerType.value]!;
});
const switchOuterFilter = () => {
  const all_length = filters_keys_arr.value.length;
  outerIndex.value = (outerIndex.value + 1) % all_length;
};
const getFilterImage = (filter: { value: string }) => {
  switch (outerType.value) {
    case FilterType.class: {
      return getClassAssetIcon(filter.value.toLowerCase() as any);
    }
    case FilterType.coperation: {
      return getCleanIconByCorp(filter.value.toLowerCase() as any);
    }
    case FilterType.weaponType: {
      return getWeaponAssetIcon(filter.value.toUpperCase() as any);
    }
    case FilterType.element: {
      return getElementAssetIcon(filter.value as any);
    }
    default:
    case FilterType.burst: {
      return;
    }
  }
};
const innerFilter = computed(() => {
  return Object.entries(filters.value)
    .filter(([key]) => {
      return key !== outerType.value;
    })
    .map(([key, val]) => ({ name: key as FilterType, list: val }));
});

const reset = () => {
  resetSort();
  resetSearch();
  resetFilter();
};
function goToNikkeDetail(nikke: { resource_id: number; name_localkey: { name: string } }) {
  router.push({
    name: RoutesName.SHIFTYSPAD_NIKKE_DETAIL,
    query: {
      [COMMON_QUERY_KEYS.PageSource]: "list",
      [COMMON_QUERY_KEYS.NikkeId]: nikke.resource_id,
    },
  });
}
</script>

<template>
  <Loading v-if="is_nikke_list_loading" />
  <div v-if="!is_nikke_list_loading" class="relative bg-[var(--fill-3)]">
    <div class="relative border-t-[1px] border-[color:var(--line-1)]">
      <div style="min-height: 70vh">
        <div class="p-[12px] bg-[var(--op-fill-white)]">
          <div
            class="flex items-center h-[34px] bg-[#F4F4F4] border-[length:0.5px] border-solid border-[color:var(--line-1)] px-[5px] box-border"
          >
            <SvgIcon
              name="icon-search"
              color="var(--text-1)"
              class="w-[16px] h-[16px] mr-[5px]"
            ></SvgIcon>
            <input
              v-model="name_search"
              type="text"
              :placeholder="t('filter_name_placeholder')"
              class="!font-[Inter] flex-1 text-[14px] leading-[16px] text-[color:var(--text-1)] placeholder:text-[color:var(--text-3)] appearance-none bg-none bg-transparent m-0 p-0 focus:border-0"
            />
          </div>
        </div>
        <div class="flex items-center justify-between px-[12px] mt-[13px]">
          <div class="flex items-center justify-start gap-x-[8px] flex-1 overflow-x-auto">
            <Btn
              class="min-w-[38px] h-[28px]"
              text="ALL"
              data-btn-id="1"
              data-btn-name="ALL"
              :active="outerAllActive"
              @click="resetFilter(outerType)"
            ></Btn>
            <Btn
              v-for="(filter, index) in outerFilter"
              :key="filter.value"
              class="min-w-[38px] h-[28px]"
              :text="t(filter.label)"
              :data-btn-id="index + 2"
              :data-btn-name="filter.value"
              :active="filter.selected"
              @click="setOuterFiled(filter)"
            >
              <template v-if="getFilterImage(filter)" #icon>
                <img
                  :src="getFilterImage(filter)"
                  :alt="filter.value"
                  class="h-[18px] object-contain"
                  :class="{
                    'bg-gray-400': [FilterType.weaponType, FilterType.coperation].includes(
                      outerType,
                    ),
                  }"
                />
              </template>
            </Btn>
            <Btn
              class="w-[28px] h-[28px] border-[0.5px] border-solid box-border"
              data-btn-id="5"
              data-btn-name="refresh"
              @click="switchOuterFilter"
            >
              <template #icon>
                <SvgIcon
                  name="icon-switch3"
                  color="var(--text-3)"
                  class="w-[20px] h-[20px]"
                ></SvgIcon>
              </template>
            </Btn>
          </div>
          <SelectHead
            :text="t('filter')"
            :type="2"
            class="h-[28px]"
            data-btn-id="6"
            data-btn-name="filter_btn"
            @click.stop="filterShownFlag = !filterShownFlag"
            @touchstart.stop
          />
        </div>
        <div class="flex justify-center flex-wrap mt-[10px] nikkes-list">
          <template v-for="nikke in nikke_list">
            <AllItem
              v-if="!nikke.is_obtained"
              :key="nikke.resource_id"
              :nikke="nikke"
              :lock_style="has_role"
              @click="goToNikkeDetail(nikke)"
            ></AllItem>
            <PlayerItem
              v-if="nikke.is_obtained"
              :key="nikke.resource_id"
              :nikke="nikke"
              :screenshot="false"
              @click="goToNikkeDetail(nikke)"
            ></PlayerItem>
          </template>
          <div
            v-for="i in 4 - (nikke_list.length % 4)"
            :key="i"
            class="max-h-[180px] w-[102px] max-w-[22%] my-[4.5px] mx-[5px]"
          ></div>
          <div v-if="nikke_list.length === 0" class="nodata-empty w-[100%]">
            <p>{{ t("no_data") }}</p>
          </div>
        </div>
      </div>
      <div
        v-if="filterShownFlag"
        class="filter-panel-wrap z-10 absolute w-[calc(100%-22px)] top-[108px] left-1/2 -translate-x-1/2 p-[12px] bg-[var(--op-fill-white)] border-[0.5px] border-solid border-[var(--line-1)] box-border shadow-[0px_2px_10px_0px_rgba(0,0,0,0.05)]"
      >
        <a
          href="javascript:;"
          class="font-bold text-[11px] leading-[14px] text-[var(--brand-1)] no-underline absolute top-[12px] right-[12px]"
          @click.stop="reset()"
        >
          {{ t("reset_filter") }}
        </a>
        <p class="font-bold text-[14px] leading-[16px] text-[color:var(--text-1)] mb-[10px]">
          {{ t("sort") }}
        </p>
        <div
          class="flex flex-wrap gap-[8px] pb-[10px] mb-[12px] border-b-[0.5px] border-solid border-[var(--line-1)]"
        >
          <a
            v-for="sort in sort_config"
            :key="sort.name"
            href="javascript:;"
            class="flex items-center text-22 justify-center min-w-[75px] h-[28px] rounded-[2px] font-normal !text-[11px] leading-[13px] text-[color:var(--text-2)] box-border"
            :class="[
              sort.selected
                ? 'bg-[#D8EFFF] border-[0.5px] border-solid border-[var(--brand-1)]'
                : 'bg-[var(--fill-2)]',
            ]"
            style="position: relative"
            @click="setSort(sort.name)"
            >{{ t(sort.name) }}
            <div class="arrow" :class="[sort.sort ? 'down' : 'up']"></div>
            <SvgIcon
              :name="sort.sort ? 'icon-arrow-down' : 'icon-arrow-up'"
              class="w-[7px] h-[8px] ml-[4px]"
              color="var(--text-1)"
            ></SvgIcon>
          </a>
        </div>
        <div class="flex items-center justify-between">
          <p class="font-bold text-[14px] leading-[16px] text-[color:var(--text-1)]">
            {{ t("filter") }}
          </p>
          <div v-if="has_role" class="flex items-center">
            <div class="font-[Inter] text-[11px] leading-[13px] text-[color:#5A5A5B] mr-[6px]">
              {{ t("only_see_obtained_nikke") }}
            </div>
            <!-- @update:checked="handleChange" -->
            <Switch :disabled="false" :checked="obtainer_filter" @update:checked="changeFilter" />
          </div>
        </div>
        <div v-for="item in innerFilter" :key="item.name" class="mt-[12px]">
          <p class="font-normal text-[11px] leading-[13px] text-[color:var(--text-1)] mb-[6px]">
            {{ t(item.name.toLowerCase()) }}
          </p>
          <div class="flex flex-wrap gap-[8px]">
            <a
              v-for="u in item.list"
              :key="u.value"
              href="javascript:;"
              class="flex items-center text-22 justify-center min-w-[75px] h-[28px] rounded-[2px] font-normal !text-[11px] leading-[13px] text-[color:var(--text-2)] box-border"
              :class="[
                u.selected
                  ? 'bg-[#D8EFFF] border-[0.5px] border-solid border-[var(--brand-1)]'
                  : 'bg-[var(--fill-2)]',
              ]"
              @click="setFilterValue(item.name, u.value)"
              >{{ t(u.label) }}</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("@/shiftyspad/assets/scss/page/nikke/list.scss");
</style>
