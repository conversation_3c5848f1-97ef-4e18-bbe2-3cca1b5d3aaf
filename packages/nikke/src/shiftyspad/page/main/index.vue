<script setup lang="ts">
import { t } from "@/locales";
import { Routes } from "@/router/routes";

import { useUser } from "@/store/user";
import { useUserStore } from "@/shiftyspad/stores/user";

import { report } from "packages/utils/tlog";
import { useLSStorage } from "packages/utils/storage";
import { STORAGE_LS_SHIFTYS_HINT } from "packages/configs/storage";

import { storeToRefs } from "pinia";
import { ref, onMounted } from "vue";

import UserInfo from "@/shiftyspad/components/main/UserGameInfo.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
// @ts-ignore
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import UserBaseInfo from "@/shiftyspad/components/main/UserBaseInfo.vue";
import { NoticePopup } from "@/shiftyspad/components/common/popups/NoticePopup";
// import Footer from '@/shiftyspad/components/common/Footer.vue'

import "swiper/css";

const { logined, is_client } = storeToRefs(useUserStore());
const { getStorage, setStorage } = useLSStorage();
const { user_info } = storeToRefs(useUser());

const boxHeight = ref(0);

onMounted(() => {
  const footer = document.querySelector(".footer");
  boxHeight.value = footer?.clientHeight || 0;

  if (!getStorage(STORAGE_LS_SHIFTYS_HINT)) {
    setStorage(STORAGE_LS_SHIFTYS_HINT, 1);
    NoticePopup.info({
      title: t("notice"),
      textAlign: "left",
      confirm: t("confirm"),
      message: t("data_sync_tip"),
    });
  }

  report.standalonesite_shiftypad_gamerecordpage.cm_vshow({
    openid: user_info.value?.intl_openid,
    is_client: is_client.value,
  });
});
</script>

<template>
  <div
    id="user-shiftypad-main"
    class="w-full flex flex-col relative bg-[url(@/assets/imgs/common/topic-default.png)] bg-no-repeat bg-[length:100%_auto] bg-[center_top] bg-[var(--fill-3)] pt-[32px] box-border"
    :style="{ minHeight: `calc(100vh - 44px - ${boxHeight}px)` }"
  >
    <i
      class="absolute top-0 left-0 w-full h-[211px] !bg-[#000] opacity-[0.7] pointer-events-none"
    ></i>
    <UserBaseInfo class="relative"></UserBaseInfo>
    <div
      class="relative flex-1 bg-[url('@/assets/imgs/shiftyspad/home/<USER>')] bg-no-repeat bg-[length:100%_auto] bg-[center_top] pt-[25px] mt-[8px]"
    >
      <UserInfo class="relative"></UserInfo>
      <div v-if="!is_client" class="px-[12px] pb-[20px]">
        <div class="font-bold text-[18px] leading-[22px] text-[color:var(--text-1)]">
          {{ t("nav_scene_list") }}
        </div>
        <div class="mt-[3px] flex items-center gap-x-[8px]">
          <router-link
            :to="Routes.SHIFTYSPAD_SCENE_MAIN"
            class="flex flex-1 flex-col items-center justify-center w-[111px] h-[91px] bg-[url(@/assets/imgs/shiftyspad/home/<USER>"
            :class="[!logined ? 'pointer-events-none opacity-40' : '']"
            @click="report.small_tool_main_story_btn.cm_click({})"
          >
            <SvgIcon
              name="icon-story"
              color="var(--color-white)"
              class="w-[36px] h-[36px]"
            ></SvgIcon>
            <span
              class="font-normal text-[13px] leading-[13px] text-[color:var(--color-white)] mt-[8px]"
            >
              {{ t("scene_tab_main") }}
            </span>
          </router-link>
          <router-link
            :to="Routes.SHIFTYSPAD_SCENE_SUDDEN"
            class="flex flex-1 flex-col items-center justify-center w-[111px] h-[91px] bg-[url(@/assets/imgs/shiftyspad/home/<USER>"
            :class="[!logined ? 'pointer-events-none opacity-40' : '']"
            @click="report.small_tool_brief_encounter_btn.cm_click({})"
          >
            <SvgIcon
              name="icon-encounter"
              color="var(--color-white)"
              class="w-[36px] h-[36px]"
            ></SvgIcon>
            <span
              class="font-normal text-[13px] leading-[13px] text-[color:var(--color-white)] mt-[8px]"
            >
              {{ t("scene_tab_event") }}
            </span>
          </router-link>
          <router-link
            :to="Routes.SHIFTYSPAD_SCENE_ARCHIVE"
            class="flex flex-1 flex-col items-center justify-center w-[111px] h-[91px] bg-[url(@/assets/imgs/shiftyspad/home/<USER>"
            :class="[!logined ? 'pointer-events-none opacity-40' : '']"
            @click="report.small_tool_plot_archives_btn.cm_click({})"
          >
            <SvgIcon
              name="icon-archives"
              color="var(--color-white)"
              class="w-[36px] h-[36px]"
            ></SvgIcon>
            <span
              class="font-normal text-[13px] leading-[13px] text-[color:var(--color-white)] mt-[8px]"
            >
              {{ t("scene_tab_archive") }}
            </span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("@/shiftyspad/assets/scss/page/main/index.scss");
</style>
