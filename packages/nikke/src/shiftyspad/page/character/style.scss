.nikkes-side-btn-toggle {
  display: block;
  width: px2rem(60);
  height: px2rem(78);
  border-radius: px2rem(4);
  background: #12a8fe;
  box-shadow:
    inset 0 -1px 1px 0 #0a70d7,
    0 0 2px 2px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  position: fixed;
  top: 55%;
  margin-top: px2rem(-39);
  // right: 0;
  transition: right 0.5s ease;
  &.opened {
    // right: px2rem(240);
    i {
      border-left-color: #fff;
      border-right-color: transparent;
      margin-left: px2rem(8);
    }
  }
  i {
    display: block;
    width: 0;
    height: 0;
    border: transparent solid px2rem(16);
    border-right-color: #fff;
    border-left-color: transparent;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: px2rem(-8);
    transform: translate(-50%, -50%);
  }
}
.is-pc {
  .nikkes-side-btn-toggle {
    top: 45%;
    &.opened {
      right: px2rem(180);
    }
  }
  .nikkes-side {
    &::before {
      display: block;
      content: "";
      height: 100%;
      width: 0;
      background: #fff;
      position: absolute;
      // right: 0;
      top: 50%;
    }
    .nikkes-side-inner,
    &::before {
      visibility: hidden;
      transform: translate(100%, -50%);
      opacity: 0;
      transition: all 0.5s ease;
    }
    &.opened {
      &::before {
        width: px2rem(180);
      }
      .nikkes-side-inner,
      &::before {
        visibility: visible;
        transform: translate(0, -50%);
        opacity: 1;
      }
    }
  }
}
.is-mobile {
  .nikkes-side {
    background: #fff;
    width: px2rem(250);
    height: 100%;
    position: fixed;
    top: 0;
    // right: 0;
    transform: translate(0, 0);
    // z-index: 200;
    visibility: hidden;
    transform: translate(100%, 0);
    opacity: 0;
    transition: all 0.5s ease;
    &.opened {
      visibility: visible;
      transform: translate(0, 0);
      opacity: 1;
    }
  }
  .nikkes-side-inner {
    top: 40%;
    right: px2rem(30);
    a {
      max-width: px2rem(180);
      font-size: px2rem(22);
      margin-top: px2rem(90);
      // &.on {
      //   font-size: px2rem(24);
      // }
      &:first-child {
        margin-top: 0;
      }
    }
  }
}
@media screen and (min-aspect-ratio: 768 / 1024) {
  .is-mobile {
    .nikkes-side-inner {
      a {
        margin-top: px2rem(50);
      }
    }
  }
}
.is-pc-scroll {
  #nikkes-upgrade,
  #nikkes-weapon,
  #nikkes-skill,
  #nikkes-storyline,
  #nikkes-dialogue {
    scroll-margin-top: px2rem(80);
  }
  #nikkes-skill {
    scroll-margin-top: px2rem(100);
  }
  #nikkes-equip {
    scroll-margin-top: px2rem(50);
  }
}
.is-mobile-scroll {
  #nikkes-upgrade,
  #nikkes-weapon,
  #nikkes-storyline,
  #nikkes-dialogue {
    scroll-margin-top: px2rem(100);
  }
  #nikkes-skill {
    scroll-margin-top: px2rem(120);
  }
  #nikkes-equip {
    scroll-margin-top: px2rem(80);
  }
}

.is-pc {
  .nikkes-detail {
    padding-top: px2rem(56);
    padding-bottom: 0;
  }
  .nikkes-detail-item {
    width: px2rem(1200);
    margin-left: auto;
    margin-right: auto;
  }
  .nikkes-detail-box,
  .nikkes-detail-box-width {
    width: px2rem(1180);
  }
  .nikkes-row-half {
    width: px2rem(1200px);
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    > .nikkes-detail-item {
      width: calc(50% - px2rem(10));
      margin: 0;
    }
  }
  // @at-root {
  //   @media screen and (max-width: 1500px) {
  //     .is-pc {
  //       .nikkes-row-half {
  //         width: px2rem(1000px);
  //       }
  //       .nikkes-detail-item {
  //         width: px2rem(1000);
  //       }
  //       .nikkes-detail-box, .nikkes-detail-box-width {
  //         width: px2rem(980);
  //       }
  //     }
  //   }
  //   @media screen and (min-width: 1800px) {
  //     .is-pc {
  //       .nikkes-row-half {
  //         width: px2rem(1400px);
  //       }
  //       .nikkes-detail-item {
  //         width: px2rem(1400);
  //       }
  //       .nikkes-detail-box, .nikkes-detail-box-width {
  //         width: px2rem(1380);
  //       }
  //     }
  //   }
  // }
}
.nikkes-detail {
  // padding-top: px2rem(88);
  padding-bottom: px2rem(55);
  background: url(@/shiftyspad/assets/images/nikkes/nikkes-detail-bg.png) repeat-y center top;
  background-size: 100% auto;
  &-box-width {
    width: px2rem(690);
  }
  &-box {
    width: px2rem(690);
    background: #fff;
    box-shadow:
      0 1px 4px 0px rgba(0, 0, 0, 0.1),
      0 2px 6px 0px rgba(0, 0, 0, 0.2);
    border-radius: px2rem(10);
  }
}
.nikkes-side-inner {
  position: fixed;
  top: 40%;
  right: px2rem(10);
  transform: translate(0, -50%);
  z-index: 210;
  &::before {
    display: block;
    content: "";
    height: 100%;
    width: 1px;
    background: #000;
    position: absolute;
    top: 0;
    // right: 0;
    opacity: 0.3;
  }
  a {
    display: block;
    margin-top: px2rem(40);
    margin-right: px2rem(10);
    position: relative;
    text-align: center;
    font-size: px2rem(18);
    // height: px2rem(32);
    max-width: px2rem(120);
    line-height: 1;
    padding: px2rem(4);
    box-sizing: border-box;
    transition: all 0.3s ease;
    &:first-child {
      margin-top: 0;
    }
    &.on {
      color: #000;
      // font-size: px2rem(20);
      background:
        linear-gradient(to left, #12a8fe, #12a8fe) left top no-repeat,
        linear-gradient(to bottom, #12a8fe, #12a8fe) left top no-repeat,
        linear-gradient(to left, #12a8fe, #12a8fe) right top no-repeat,
        linear-gradient(to bottom, #12a8fe, #12a8fe) right top no-repeat,
        linear-gradient(to left, #12a8fe, #12a8fe) left bottom no-repeat,
        linear-gradient(to bottom, #12a8fe, #12a8fe) left bottom no-repeat,
        linear-gradient(to left, #12a8fe, #12a8fe) right bottom no-repeat,
        linear-gradient(to left, #12a8fe, #12a8fe) right bottom no-repeat;
      background-size:
        px2rem(2px) px2rem(10px),
        px2rem(10px) px2rem(2px),
        px2rem(2px) px2rem(10px),
        px2rem(10px) px2rem(2px);
      &::after {
        display: block;
        content: "";
        width: px2rem(4);
        height: 100%;
        background: #12a8fe;
        position: absolute;
        top: 0;
        right: px2rem(-12);
      }
    }
  }
}
.nikkes-side-top {
  display: block;
  width: px2rem(180);
  height: px2rem(40);
  background: #12a8fe;
  border-radius: px2rem(6);
  box-shadow: inset 0 -1px 1px 0 #0a70d7;
  position: absolute;
  bottom: calc(10% + px2rem(20));
  left: 50%;
  margin-left: px2rem(-90);
  z-index: 5;
  font-size: px2rem(22);
  color: #fff;
  text-align: center;
  padding-top: px2rem(20);
  box-sizing: content-box;
  i {
    display: block;
    width: 0;
    height: 0;
    border: transparent solid px2rem(12);
    border-bottom-color: #fff;
    position: absolute;
    left: 50%;
    top: 0;
    margin-top: px2rem(6);
    transform: translate(-50%, -50%);
  }
}
.head-nav {
  z-index: 99;
}
.is-pc-open-side-btn {
  right: calc(50% - (px2rem(370)));
  // z-index: 200;
}
.is-pc-close-side-btn {
  right: calc(50% - (px2rem(140)));
  // z-index: 200;
}
.is-pc-side {
  right: calc(50% - (px2rem(390)));
}
