<script setup lang="ts">
import { isMobileDevice } from "packages/utils/tools";
import { report } from "packages/utils/tlog";
import { Routes } from "@/router/routes";

import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { ref, computed, provide, watch, toRaw } from "vue";

import { useUserStore } from "@/shiftyspad/stores/user";
import { useCharacterData } from "@/shiftyspad/composable/character";
import { useRoleDisplayState } from "@/shiftyspad/stores/role/display";

import BasicDetail from "@/shiftyspad/components/character/BasicDetail.vue";
import Upgrade from "@/shiftyspad/components/character/Upgrade.vue";
import Team from "@/shiftyspad/components/character/Team.vue";
import Dialogue from "@/shiftyspad/components/character/Dialogue.vue";
import Storyline from "@/shiftyspad/components/character/Storyline.vue";
import Equip from "@/shiftyspad/components/character/equip/Equip.vue";
import Skill from "@/shiftyspad/components/character/Skill.vue";
import Loading from "@/components/common/loading.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import NavTab from "@/shiftyspad/components/character/NavTab.vue";
import Rubikscube from "@/shiftyspad/components/character/Rubikscube.vue";
import Collection from "@/shiftyspad/components/character/Collection.vue";

const props = defineProps<{
  character_id: string;
}>();
const router = useRouter();
const isMobile = isMobileDevice();
const displayStatus = useRoleDisplayState();

const { t } = useI18n();
const { character_id } = toRaw(props);
const { status } = storeToRefs(displayStatus);
const { user_nikkelist_info } = storeToRefs(useUserStore());

const { character_data, is_character_data_loading } = useCharacterData(String(character_id), {
  onError: () => {
    router.push(Routes.NOTFOUND);
    return false;
  },
});

watch(
  () => character_data.value,
  () => {
    report.small_tool_nikke_role_detail_page.cm_vshow({
      role_id: String(character_id),
      role_name: character_data.value?.name_localkey ?? "-",
    });
  },
);

const user_character = computed(() => {
  return user_nikkelist_info.value.find(
    (v: any) => v.name_code === character_data.value?.name_code,
  );
});

const show_storyline = computed(() => {
  return (
    character_data.value?.original_rare.toLowerCase() !== "r" &&
    (character_data.value?.attractive_scenario_list?.length ?? 0) > 0
  );
});

const comps = ["equipment", "skill", "favorite_item", "cube"] as const;
const equip_exist_status = computed<Record<(typeof comps)[number], boolean>>(() => {
  return {
    equipment: Boolean(user_character.value?.player_equips?.length),
    cube: Number(user_character.value?.cube_id) > 0,
    favorite_item: Number(user_character.value?.item_id) > 0,
    skill: true,
  };
});

const tabs = computed(() =>
  comps
    .map((v) => ({
      id: v,
      name: t(v),
    }))
    .filter((val) => equip_exist_status.value[val.id]),
);

const active = ref(0);
const cur_tab = computed(() => tabs.value[active.value].id);
// used in Equip row
provide("character", { get_character: () => character_data.value });
</script>

<template>
  <div class="relative nikkes-detail" :class="[!isMobile ? 'is-pc-scroll' : 'is-mobile-scroll']">
    <!-- <BackBtn mode="black"></BackBtn> -->
    <div
      v-show="status === 'voice'"
      class="absolute top-[10px] left-0 flex items-center justify-center z-[5] px-[12px] w-full"
    >
      <i class="flex-1 mr-[17px] h-[1px] bg-[#959596]"></i>
      <div
        class="font-[Inter] text-[length:9px] font-bold text-[color:#FFFFFF] bg-[#444649] h-[16px] flex items-center justify-center px-[4px]"
      >
        {{ t("voice_mode") }}
      </div>
      <i class="flex-1 ml-[17px] h-[1px] bg-[#959596]"></i>
    </div>
    <template v-if="character_data">
      <BasicDetail :character_data="character_data" />
      <!-- 角色台词 -->
      <Dialogue
        v-show="status === 'voice'"
        class="mb-20 relative -mt-[65px]"
        :character_data="character_data"
      >
        <div
          class="w-[28px] h-[28px] border-[1px] border-[#DBDBDB] rotate-180 absolute top-[-36px] right-0 z-[5] flex items-center justify-center cursor-pointer bg-[#fff] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.1)]"
          @click="displayStatus.changeStatus('none')"
        >
          <SvgIcon name="icon-arrow-top" color="#141416" class="w-[15px] h-[8px]"></SvgIcon>
        </div>
      </Dialogue>
      <div v-show="status !== 'voice'">
        <!-- 升级信息 -->
        <Upgrade :character_data="character_data" :class="{ 'order-2': !isMobile }"></Upgrade>
        <div id="3in1" class="expandable-box mt-20 !p-[12px]">
          <NavTab
            :list="tabs"
            :active="active"
            active_font_size="13px"
            @change="(i) => (active = i)"
          ></NavTab>
          <!-- 装备 -->
          <Equip
            v-if="equip_exist_status.equipment && cur_tab === 'equipment'"
            :character_data="character_data"
          />
          <!-- 技能 -->
          <Skill v-show="cur_tab === 'skill'" :character_data="character_data" />
          <!-- 藏品 -->
          <Collection
            v-show="equip_exist_status.favorite_item && cur_tab === 'favorite_item'"
            :character_data="character_data"
          />
          <!--  魔方 -->
          <Rubikscube
            v-show="equip_exist_status.cube && cur_tab === 'cube'"
            :character_data="character_data"
          />
        </div>
        <!-- 好感度剧情 -->
        <Storyline
          v-if="character_data && show_storyline"
          :character_data="character_data"
          class="mt-20"
        />
        <!-- 女神部队 -->
        <Team class="mt-20" :character_data="character_data"></Team>
      </div>
    </template>
    <Loading v-if="is_character_data_loading" class="h-[50vh]"></Loading>
  </div>
</template>

<style lang="scss">
@import url("./style.scss");
</style>
