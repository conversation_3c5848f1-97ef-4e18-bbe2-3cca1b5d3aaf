<template>
  <template v-if="filter_combine_nikke_list.length > 1 && is_current_nikke_in_list">
    <swiper
      v-if="!loading"
      direction="horizontal"
      :initial-slide="cur_index"
      :modules="swiperModules"
      :space-between="0"
      :loop="true"
      :virtual="true"
      :allow-touch-move="isMobileDevice()"
      :navigation="{ prevEl: '.detail-swiper-next', nextEl: '.detail-swiper-prev' }"
      @swiper="onSwiper"
      @slide-change="changeCompIndex"
    >
      <swiper-slide
        v-for="(item, index) in filter_combine_nikke_list"
        :key="item.resource_id"
        :virtual-index="index"
      >
        <CharacterDetail
          v-if="filter_combine_nikke_list[cur_index] === item"
          :character_id="String(item.resource_id)"
        />
        <Skeleton v-else :character_id="String(item.resource_id)" />
      </swiper-slide>
      <div
        class="fade-element z-10 fixed cursor-pointer top-[50vh] w-[55px] h-[55px] detail-swiper-prev"
        :class="{
          'right-[calc((100vw_-_var(--max-pc-w))/2)]': !isMobileDevice(),
          'right-0': isMobileDevice(),
        }"
      >
        <SvgIcon name="icon-arrow-right4"></SvgIcon>
      </div>

      <div
        class="fade-element z-10 cursor-pointer fixed top-[50vh] w-[55px] h-[55px] detail-swiper-next"
        :class="{
          'left-[calc((100vw_-_var(--max-pc-w))/2)]': !isMobileDevice(),
          'left-0': isMobileDevice(),
        }"
      >
        <SvgIcon name="icon-arrow-left4"></SvgIcon>
      </div>
    </swiper>
    <Loading v-else class="h-[70vh]" />
  </template>
  <template v-else>
    <CharacterDetail :key="active_resource_id" :character_id="String(active_resource_id)" />
  </template>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { Navigation, Pagination, Virtual } from "swiper/modules";
import { Swiper as SwiperTypes } from "swiper/types";
import { Swiper, SwiperSlide } from "swiper/vue";
import { computed, onUnmounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { debounce } from "lodash-es";

import "swiper/css";

import { Routes } from "@/router/routes";
import { useUserStore } from "@/shiftyspad/stores/user";
import { useSortNikkeList } from "@/shiftyspad/composable/nikke-filter";
import { useJsonStore } from "@/shiftyspad/stores/json";
import { isMobileDevice, sleep } from "packages/utils/tools";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import { logger } from "@/shiftyspad/const";

import Skeleton from "./detail-skeleton.vue";
import CharacterDetail from "./detail.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import Loading from "@/components/common/loading.vue";

const router = useRouter();
const route = useRoute();
const user = useUserStore();
const swiperModules = [Pagination, Navigation, Virtual];

const { query } = useRoute();
const { user_nikkelist_info } = storeToRefs(user);
const { nikke_list } = storeToRefs(useJsonStore());
const { filter_combine_nikke_list } = useSortNikkeList({
  user_nikkelist_info,
  disable_ss: route.query?.[COMMON_QUERY_KEYS.PageSource] !== "list",
});

const initial_resource_id = String(query?.[COMMON_QUERY_KEYS.NikkeId]);
if (!initial_resource_id) {
  router.push(Routes.NOTFOUND);
}
const cur_index = ref(-1);
const active_resource_id = ref(Number(initial_resource_id));

const swiper_instance = ref();
const onSwiper = (swiper: SwiperTypes) => {
  swiper_instance.value = swiper;
};

const loading = ref(true);

const changeSwiperByRid = (id: number | string) => {
  const old_index = swiper_instance.value?.realIndex;
  const index = filter_combine_nikke_list.value.findIndex(
    (item) => item.resource_id === Number(id),
  );

  logger.warn(`changing swiper by route id, id: ${id}, old_index: ${old_index}, index: ${index}`);

  active_resource_id.value = Number(id);

  if (index < 0) {
    return router.replace({
      query: {
        ...route.query,
        [COMMON_QUERY_KEYS.NikkeId]: id,
      },
    });
  }
  if (index !== old_index) {
    swiper_instance.value?.slideToLoop(index);
  }
};

const changeCompIndex = () => {
  const index = swiper_instance.value?.realIndex;

  logger.warn(`slide-change - index: ${index}`);

  if (index >= 0) {
    logger.warn(`changing index by swiper change, index: ${index}`);

    cur_index.value = index;

    router.replace({
      query: {
        ...route.query,
        [COMMON_QUERY_KEYS.NikkeId]: String(filter_combine_nikke_list.value[index].resource_id),
      },
    });
  }
};

const init = async () => {
  try {
    await user.initUserNikkeInfo();
  } catch (e) {
    logger.error(e);
  }

  await sleep(100);

  const index = filter_combine_nikke_list.value.findIndex(
    (item) => item.resource_id === Number(active_resource_id.value),
  );

  if (index >= 0) {
    cur_index.value = index;
    logger.info(`init character detail:  ${active_resource_id.value} - index ${cur_index.value}`);
  } else {
    logger.error(`${active_resource_id.value} not exist`);
  }

  await sleep(100);

  loading.value = false;
};

const is_current_nikke_in_list = computed(
  () =>
    filter_combine_nikke_list.value.findIndex(
      (item) => item.resource_id === Number(active_resource_id.value),
    ) >= 0,
);

const aborter = new AbortController();
const initScrollHide = () => {
  const container = document.querySelector("#layout-content");
  container?.addEventListener(
    "scroll",
    debounce(() => {
      const windowHeight = window.innerHeight;
      const scrollY = container?.scrollTop ?? 0;
      const progress = 1 - scrollY / (windowHeight / 2);
      document.querySelectorAll(".fade-element").forEach((element) => {
        if (element && element instanceof HTMLElement) {
          element.style.opacity = Math.min(Math.max(progress, 0), 1) + "";
        }
      });
    }),
    { signal: aborter.signal },
  );
};

initScrollHide();
onUnmounted(() => aborter.abort());

watch(
  () => route.query,
  () => {
    logger.error("route.query changed: ", String(route.query[COMMON_QUERY_KEYS.NikkeId]));
    changeSwiperByRid(String(route.query[COMMON_QUERY_KEYS.NikkeId]));
  },
);

watch(
  () => nikke_list.value,
  () => {
    if (nikke_list.value.length) init();
  },
  { immediate: true },
);
</script>
