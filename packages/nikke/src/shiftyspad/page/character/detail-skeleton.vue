<script setup lang="ts">
import { isMobileDevice } from "packages/utils/tools";
import Loading from "@/components/common/loading.vue";

const isMobile = isMobileDevice();
</script>

<template>
  <div class="relative nikkes-detail" :class="[!isMobile ? 'is-pc-scroll' : 'is-mobile-scroll']">
    <!-- copied -->
    <div role="status" class="w-full animate-pulse">
      <div
        class="flex w-full p-[30px] space-y-8 animate-pulse md:space-y-0 md:space-x-8 rtl:space-x-reverse md:flex"
      >
        <div
          class="flex items-center justify-center w-[400px] h-[659px] bg-gray-300 rounded-sm sm:w-96 dark:bg-gray-700"
        >
          <svg
            class="w-10 h-10 text-gray-200 dark:text-gray-600"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 18"
          >
            <path
              d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z"
            />
          </svg>
        </div>
        <div class="flex-1 flex flex-col items-end">
          <div class="h-[50px] bg-gray-300 rounded-md dark:bg-gray-600 w-48 mb-4"></div>
          <div class="h-[659px] bg-gray-300 rounded-md dark:bg-gray-600 w-48 mb-4"></div>
        </div>
        <span class="sr-only">Loading...</span>
      </div>
    </div>

    <Loading class="h-[50vh]"></Loading>
  </div>
</template>

<style lang="scss">
@import url("./style.scss");
</style>
