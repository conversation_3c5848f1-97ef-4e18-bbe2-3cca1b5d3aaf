<!-- author: laytonmo -->
<script setup lang="ts">
import { computed } from "vue";
import PlayerItem from "@/shiftyspad/components/nikke-list/player-item.vue";
import Spinner from "@/shiftyspad/components/common/spinner.vue";
import Head from "@/components/common/head/index.vue";
import Button from "@/components/common/btns/index.vue";
import { useLoadingState } from "@/shiftyspad/composable/state/loading";
import { t } from "@/locales/index";
import { Nikke } from "packages/types/shiftyspad";
import { useRouter } from "vue-router";
import { useCustomSortNikkeList } from "@/shiftyspad/composable/nikke-filter";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/shiftyspad/stores/user";

useLoadingState();

const router = useRouter();
const { user_nikkelist_info } = storeToRefs(useUserStore());
const {
  filter_player_nikke_list,
  is_loading_self_sort,
  default_sort_list,
  is_nikke_list_loading,
  reach_max,
  updateSettings,
  changeSort,
} = useCustomSortNikkeList({ user_nikkelist_info, is_client: false, uid: "" });
const no_data = computed(() => filter_player_nikke_list.value.length <= 0);

const loading = computed(() => is_loading_self_sort.value || is_nikke_list_loading.value);
const setSort = (nikke: Nikke) => changeSort(nikke);
const isSelected = (nikke: Nikke) => default_sort_list.value.includes(nikke.resource_id);
const getIndex = (nikke: Nikke) => {
  const i = default_sort_list.value.indexOf(nikke.resource_id);
  if (i >= 0) return i + 1;
  return "";
};
const onSubmit = async () => {
  await updateSettings(default_sort_list.value.map((v) => ({ resource_id: v })));
  router.back();
};
</script>

<template>
  <Head
    class="z-[40]"
    :title="t('editing')"
    color="text-[color:var(--color-black)]"
    bg="bg-[var(--color-white)]"
    @goback="router.back"
  >
    <template #icon>
      <Button
        size="s"
        type="primary"
        :disabled="false"
        :text="t('save')"
        @click="onSubmit"
      ></Button>
    </template>
  </Head>
  <div class="h-[44px]"></div>
  <div class="relative">
    <div style="min-height: 70vh">
      <Spinner v-if="loading"></Spinner>
      <div v-if="!no_data && !loading" class="flex justify-center flex-wrap mt-[10px] nikkes-list">
        <PlayerItem
          v-for="nikke in filter_player_nikke_list"
          :key="nikke.resource_id"
          :nikke="nikke"
          :screenshot="false"
          @click="setSort(nikke)"
        >
          <div
            :class="{
              'bg-white/[.6]': reach_max && !isSelected(nikke),
              'bg-[var(--color-black-55)]': isSelected(nikke),
            }"
            class="absolute w-full h-full z-10"
          >
            <div
              :class="[isSelected(nikke) ? 'bg-[var(--brand-1)]' : 'first:bg-transparent']"
              class="absolute right-[5px] top-[5px] flex items-center text-[color:var(--color-white)] justify-center flex-shrink-0 w-[20px] h-[20px] first:rounded-[50%] cursor-pointer"
            >
              {{ getIndex(nikke) }}
              <i
                :class="{
                  'bg-[var(--color-black-55)]': !reach_max && !isSelected(nikke),
                }"
                class="absolute top-0 left-0 w-full h-full rounded-[50%] border-[length:1px] border-solid border-[color:var(--color-white)]"
              ></i>
            </div>
          </div>
        </PlayerItem>

        <div
          v-for="i in 4 - (filter_player_nikke_list.length % 4)"
          :key="i"
          class="max-h-[180px] w-[102px] max-w-[22%] my-[4.5px] mx-[5px]"
        ></div>
      </div>
    </div>
  </div>
</template>
