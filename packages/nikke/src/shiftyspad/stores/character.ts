import { default_grade, default_level } from "@/shiftyspad/const/setting";
import { defineStore } from "pinia";

type State = {
  grade: number;
  level: number;
  core: number;
  limit_break: number;

  ulti_skill_level: number;
  skill1_level: number;
  skill2_level: number;

  // 懒得改字段了, 上边的都是模拟点的, 下边的是用户的（不变的真实的）
  real_grade: number;
  real_level: number;
  real_core: number;
  real_limit_break: number;

  real_ulti_skill_level: number;
  real_skill1_level: number;
  real_skill2_level: number;
};

const getState = (): State => {
  return {
    core: default_grade,
    grade: default_grade,
    level: default_level,
    limit_break: default_grade,

    ulti_skill_level: default_level,
    skill1_level: default_level,
    skill2_level: default_level,

    // 懒得改字段了, 上边的都是模拟点的, 下边的是用户的（不变的真实的）

    real_core: default_grade,
    real_grade: default_grade,
    real_level: default_level,
    real_limit_break: default_grade,

    real_ulti_skill_level: default_level,
    real_skill1_level: default_level,
    real_skill2_level: default_level,
  };
};

export const useMockDataStore = defineStore("character_mock", {
  // 用户侧的数据
  state: getState,
  actions: {
    init_state(params: Partial<State>) {
      Object.keys(params).forEach((key: string) => {
        // @ts-ignore
        this[key] = params[key];
      });
    },
    change(params: { val: number; min: number; max: number; key: keyof State }) {
      const { val, max, min, key } = params;
      if (val < min) this[key] = min;
      else if (val > max) this[key] = max;
      else this[key] = val;
    },
  },
});
