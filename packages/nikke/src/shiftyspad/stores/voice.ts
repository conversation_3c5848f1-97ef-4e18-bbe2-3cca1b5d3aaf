import { defineStore } from "pinia";
import { ref } from "vue";
import { Howl } from "howler";
import { GET_VOICE_URL } from "@/shiftyspad/const/urls";
import { CVLang } from "packages/types/shiftyspad";

export const useVoiceStore = defineStore("voice", () => {
  // 全局共用一个audio，不然会导致内存溢出和多个audio播放
  const player = ref<HTMLAudioElement>(new Audio());
  const loading = ref(false);
  player.value.addEventListener("loadedmetadata", (event) => {
    loading.value = false;
    handleCanplay(event);
    player.value.play();
  });

  player.value.addEventListener("error", (event) => {
    loading.value = false;
    handleError(event);
  });

  player.value.addEventListener("ended", (event) => {
    handleEnded(event);
  });

  // 回调
  const canPlayCb = ref<any[]>([]);
  function handleCanplay(event: any) {
    canPlayCb.value.forEach((cb) => {
      if (typeof cb.callback === "function") {
        cb.callback(event);
      }
    });
  }
  function addCanplayListener(cb: Function) {
    const ts = new Date().getTime() + Math.random();
    canPlayCb.value.push({
      key: ts,
      callback: cb,
    });
    return ts;
  }
  function removeCanplayListener(key: number) {
    const index = canPlayCb.value.findIndex((cb) => {
      return cb.key === key;
    });
    if (index > -1) {
      canPlayCb.value.splice(index, 1);
    }
  }
  const errorCb = ref<any[]>([]);
  function handleError(event: any) {
    errorCb.value.forEach((cb) => {
      if (typeof cb.callback === "function") {
        cb.callback(event);
      }
    });
  }
  function addErrorListener(cb: Function) {
    const ts = new Date().getTime() + Math.random();
    errorCb.value.push({
      key: ts,
      callback: cb,
    });
    return ts;
  }
  function removeErrorListener(key: number) {
    const index = errorCb.value.findIndex((cb) => {
      return cb.key === key;
    });
    if (index > -1) {
      errorCb.value.splice(index, 1);
    }
  }
  const endedCb = ref<any[]>([]);
  function handleEnded(event: any) {
    endedCb.value.forEach((cb) => {
      if (typeof cb.callback === "function") {
        cb.callback(event);
      }
    });
  }
  function addEndedListener(cb: Function) {
    const ts = new Date().getTime() + Math.random();
    endedCb.value.push({
      key: ts,
      callback: cb,
    });
    return ts;
  }
  function removeEndedListener(key: number) {
    const index = endedCb.value.findIndex((cb) => {
      return cb.key === key;
    });
    if (index > -1) {
      endedCb.value.splice(index, 1);
    }
  }

  const howlPlayer = ref<Howl>();
  const current_play = ref("");
  const playHowelVoice = (cv_lang: CVLang, speech_id: string) => {
    const voice_url = GET_VOICE_URL({
      cv_lang,
      speech_id,
    });
    if (!current_play.value || current_play.value !== voice_url) {
      loading.value = true;
      current_play.value = voice_url;
      howlPlayer.value = new Howl({
        src: voice_url,
        autoplay: true,
      });
    }
    if (howlPlayer.value?.playing()) {
      howlPlayer.value.pause();
      return;
    } else if (!loading.value) {
      howlPlayer.value?.play();
    }

    howlPlayer.value?.on("loaderror", (err: any) => {
      loading.value = false;
      errorCb.value.forEach((cb) => cb(err));
    });
    howlPlayer.value?.on("play", () => {
      loading.value = false;
    });
    howlPlayer.value?.on("load", () => {
      loading.value = false;
      howlPlayer.value?.play();
    });
  };

  return {
    player,
    howlPlayer,
    loading,
    addCanplayListener,
    removeCanplayListener,
    addEndedListener,
    removeEndedListener,
    addErrorListener,
    removeErrorListener,
    playHowelVoice,
  };
});
