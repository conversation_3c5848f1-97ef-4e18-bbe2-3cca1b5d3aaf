import {
  StageData,
  SceneListData,
  SceneArchiveData,
  SceneDetailData,
} from "packages/types/game-scene";
import { NikkeListData } from "packages/types/shiftyspad";

import { getServerTs } from "@/api/shiftyspad";
import { getLang } from "@/shiftyspad/const";
import { getIngameResourceUrl } from "@/shiftyspad/const/urls";
import { getSceneCharacterSettings } from "@/shiftyspad/service/character";
import { getJson } from "@/shiftyspad/service/ls-data";

import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useQuery } from "@tanstack/vue-query";

/**
 * utils: request & json path parse
 */
const getLangUrl = (url: string) => {
  const lang = getLang();
  // 多语言转换资源url
  // ko韩语文件不带后缀，特殊处理
  let str = url.replace(/\{lang\}/g, lang);
  if (lang === "ko") {
    str = str.replace(/_ko/g, "");
  }
  return str;
};

const rawUrlGetJson = (json_url: string) => {
  const real_url = getIngameResourceUrl(getLangUrl(json_url));
  return getJson(real_url);
};

/**
 * @description nikke 游戏内数据的列表相关内容
 * - 剧情列表 (scene)
 * - 角色列表 ()
 * - 关卡列表 (stage)
 */
export const useJsonStore = defineStore("json", () => {
  const { data: stage_data, isLoading: is_stage_loading } = useQuery({
    queryKey: ["stage_list"],
    queryFn: async () => {
      const data: StageData[] = await rawUrlGetJson("/stage/stage_list.json");
      if (!data) return [] as StageData[];
      return data.filter((j) => {
        return !j.name_localkey?.name.match(/^EX/);
      });
    },
  });
  const stage_list = computed<StageData[]>(
    () => stage_data.value?.filter((val) => val.chapter_mod === "Normal") ?? [],
  );
  const stage_list_hard = computed<StageData[]>(
    () => stage_data.value?.filter((val) => val.chapter_mod === "Hard") ?? [],
  );

  const nikke_list = computed(() => nikke_query_list.value ?? []);
  const { data: nikke_query_list, isLoading: is_nikke_list_loading } = useQuery({
    queryKey: ["nikke_list"],
    queryFn: async (): Promise<NikkeListData[]> => {
      return (await rawUrlGetJson("/character/{lang}/nikke_list_{lang}_v2.json")) ?? [];
    },
  });

  const scene_main_list = computed(() => main_list_query.value ?? []);
  const { data: main_list_query, isLoading: is_scene_list_loading } = useQuery({
    queryKey: ["scene_list"],
    queryFn: async (): Promise<SceneListData[]> => {
      return (await rawUrlGetJson("/scene/{lang}/scene_list_{lang}.json")) ?? [];
    },
  });

  const scene_sudden_list = computed(() => sudden_list.value ?? []);
  const { data: sudden_list, isLoading: is_sudden_list_loading } = useQuery({
    queryKey: ["sudden_list"],
    queryFn: async (): Promise<SceneListData[]> => {
      return (await rawUrlGetJson("/scene/{lang}/sudden_list_{lang}.json")) ?? [];
    },
  });

  const scene_archive_list = computed(() => {
    if (svr_ts.value) {
      return (archive_list.value ?? []).filter(
        (val) =>
          !val.record_activate_date || val.record_activate_date < Number(svr_ts.value) * 1000,
      );
    }
    return (archive_list.value ?? []).filter((val) => !val.record_activate_date);
  });
  const { data: svr_ts } = useQuery({
    queryKey: ["ts"],
    queryFn: async (): Promise<number> => {
      const res = await getServerTs();
      return res?.server_time ?? 0;
    },
  });
  const { data: archive_list, isLoading: is_archive_list_loading } = useQuery({
    queryKey: ["archive_list"],
    queryFn: async (): Promise<SceneArchiveData[]> => {
      return (await rawUrlGetJson("/archive/{lang}/archive_list_{lang}.json")) ?? [];
    },
  });

  const scene_detail = ref<SceneDetailData>({} as any);
  async function GetSceneDetail(id: string) {
    scene_detail.value = {} as any;
    scene_detail.value = await rawUrlGetJson(`/scene/{lang}/scene_detail_${id}_{lang}.json`);
  }

  const vocie_map = ref<string[]>([]);
  function isVoiceScene(id: string) {
    // 不是所有对话都有语音，需要核外辅助的文件map来判断
    // 目前只有两个档案和主线有语音，突发剧情，好感度剧情都没有，直接隐藏语音交互
    const mainSceneReg = /d_main_([0-9]+)/;
    const matchScene = id.match(mainSceneReg);
    if (matchScene) {
      // 对应语音map文件
      return `d_main_${matchScene[1]}.json`;
    }
    const eventSceneReg = /event_(miraclesnow|overzone)/;
    const matchEvent = id.match(eventSceneReg);
    if (matchEvent) {
      return `event_${matchEvent[1]}.json`;
    }
    return false;
  }
  async function GetSceneVoiceMap(id: string) {
    // 清空
    vocie_map.value = [];
    const has_voice = isVoiceScene(id);
    if (!has_voice) {
      return;
    } else {
      vocie_map.value = await rawUrlGetJson(`/scene/voice_map/${has_voice}`);
    }
  }

  const characterSetting = computed(() => settings.value ?? []);
  const { data: settings, isLoading: is_character_setting_loading } = useQuery({
    queryKey: ["character_setting"],
    queryFn: async () => {
      return (await getSceneCharacterSettings()) ?? [];
    },
  });

  return {
    nikke_list,

    // scene
    scene_detail,
    scene_main_list,
    scene_sudden_list,
    scene_archive_list,

    // stage
    stage_list,
    stage_list_hard,

    // loading state
    is_stage_loading,
    is_sudden_list_loading,
    is_archive_list_loading,
    is_nikke_list_loading,
    is_scene_list_loading,
    is_character_setting_loading,

    // todo:
    GetSceneDetail,
    vocie_map,
    GetSceneVoiceMap,
    characterSetting,
  };
});
