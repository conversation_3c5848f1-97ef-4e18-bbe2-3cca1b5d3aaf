import { CharacterData } from "@/shiftyspad/types/character";
import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";

type Skin = CharacterData["character_costume_list"][number];

interface DialogueItem {
  title: string;
  group_id: number;
  expanded: boolean;
  lock?: boolean;
  list: CharacterData["character_dialog_group_list"][number][];
}

export const useSkinStore = defineStore("skin", () => {
  const { t } = useI18n();
  const character_data = ref<CharacterData>();
  const initCharacter = (c: CharacterData, index = 0) => {
    character_data.value = c;
    skin_dialogue_index.value = 0;
    skin_index.value = index;
    init_dialogues();
  };
  const skin_index = ref(0);
  // 插个原皮
  const skin_list = computed<Partial<Skin>[]>(() => {
    return [
      {
        id: 0,
        is_hidden: false,
        costume_index: 0,
        costume_grade_id: "default",
        costume_name_locale: character_data.value?.name_localkey,
        costume_description_locale: character_data.value?.description_localkey,
      },
    ]
      .concat(character_data.value?.character_costume_list ?? [])
      .filter((v) => !v.is_hidden && Boolean(v));
  });
  const switchSkin = (new_skin: Partial<Skin>) => {
    const index = skin_list.value.findIndex((v) => v.costume_index === new_skin.costume_index);
    skin_index.value = index ?? 0;
    updateDialogue(new_skin.costume_index ?? 0);
  };
  const skin = computed({
    set(val: any) {
      const target_skin = skin_list.value.findIndex((v) => v.costume_index === val.costume_index);
      if (target_skin >= 0) {
        skin_index.value = target_skin;
      }
    },
    get() {
      if (skin_index.value) {
        return skin_list.value[skin_index.value];
      }
      return skin_list.value[0] ?? null;
    },
  });

  /**
   * 联动
   */
  const updateDialogue = (costume_index: number) => {
    if (dialogue_list.value.length <= 1) {
      return;
    }
    const target_dialogue_index = dialogue_list.value.findIndex(
      (v) => v.costume_index === costume_index,
    );
    if (target_dialogue_index >= 0) {
      skin_dialogue_index.value = target_dialogue_index;
    } else {
      skin_dialogue_index.value = 0;
    }
    init_dialogues();
  };
  const updateSkin = (costume_index: number) => {
    const target_skin_index = skin_list.value.findIndex((v) => v.costume_index === costume_index);
    if (target_skin_index >= 0) {
      skin_index.value = target_skin_index;
    }
  };

  /**
   * 角色台词
   */
  const scene_map = {
    1: t("game_outpost_lobby"),
    2: t("game_outpost_forepost"),
    3: t("game_outpost_grow"),
    4: t("game_outpost_wild"),
    5: t("game_outpost_battle"),
  };

  const skin_dialogue_index = ref(0);
  const dialogue_list = computed(() => {
    const v = character_data.value?.dialog_group_list ?? [];
    return v.map((skin_dialog, index) => {
      if (skin_dialog.voice_type === "Character") {
        return Object.assign({}, skin_dialog, { name: t("default") });
      } else {
        const skin_detail = skin_list.value.find(
          (s) => s.costume_index === skin_dialog.costume_index,
        );
        return Object.assign({}, skin_dialog, {
          name: skin_detail?.costume_name_locale ?? skin_dialog.voice_type + index,
        });
      }
    });
  });
  const skin_dialogue = computed({
    set(val: any) {
      const index = dialogue_list.value.findIndex((v) => v.id === val.id);
      const dialog_group = dialogue_list.value.find((v) => v.id === val.id);
      skin_dialogue_index.value = index;
      init_dialogues();
      updateSkin(dialog_group?.costume_index ?? 0);
    },
    get() {
      return dialogue_list.value[skin_dialogue_index.value] ?? null;
    },
  });
  const skin_dialogues = ref<DialogueItem[]>([]);
  const init_dialogues = () => {
    const result: DialogueItem[] = [];
    Object.keys(scene_map).forEach((scene_group) => {
      const speak_list =
        character_data.value?.character_dialog_group_list
          .slice()
          .filter(
            (v) =>
              v.category_group === Number(scene_group) &&
              v.speech_group_id === skin_dialogue.value?.id,
          ) ?? [];
      const scene_list = {
        expanded: false,
        group_id: Number(scene_group),
        // @ts-ignore
        title: scene_map[scene_group] as string,
        list: speak_list,
      };
      result.push(scene_list);
    });
    skin_dialogues.value = result;
  };

  const onDialoguesCollapse = (item: DialogueItem) => {
    item.expanded = !item.expanded;
  };

  return {
    skin_index,
    skin_list,
    dialogue_list,
    skin,
    skin_dialogues,
    skin_dialogue,
    initCharacter,
    switchSkin,
    onDialoguesCollapse,
  };
});
