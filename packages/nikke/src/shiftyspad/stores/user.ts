// 用户相关store 包括登录方法 检查登录 用户信息
import { useUser } from "@/store/user";
import { RoutesName } from "@/router/routes";
import { COMMON_QUERY_KEYS } from "@/configs/const";

import { logger } from "@/shiftyspad/const";
import { useLoadingStore } from "@/shiftyspad/stores/loading";
import { createShiftysUser } from "@/shiftyspad/composable/game-data";

import { patch } from "@/utils/str";
import { base64Decode } from "packages/utils/encrypt.ts";

import { computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { defineStore, storeToRefs } from "pinia";
import { sleep } from "packages/utils/tools";

export const useUserStore = defineStore("shiftys_user", () => {
  const loadingStore = useLoadingStore();

  const router = useRouter();
  const route = useRoute();
  const user = useUser();
  const { is_login, user_info } = storeToRefs(user);

  const logined = computed(() => is_login.value);
  const shown_role_id = computed(() => {
    if (!shiftys_user.value.user_role_info.value?.role_id) {
      return "-";
    }
    const id = shiftys_user.value.user_role_info.value?.role_id ?? "-";
    return patch(id, 8);
  });

  const encrypt_uid = computed(() => {
    if (!route.query[COMMON_QUERY_KEYS.EncodedUid] && !route.query[COMMON_QUERY_KEYS.OpenId])
      return "";
    return route.query[COMMON_QUERY_KEYS.EncodedUid] || route.query[COMMON_QUERY_KEYS.OpenId] || "";
  });
  const uid = computed(() => {
    if (!encrypt_uid.value) return "";
    try {
      const real_id = base64Decode(String(encrypt_uid.value));
      logger.info(`real_uid: ${real_id}`);
      return real_id;
    } catch (err) {
      console.error(err);
      return "";
    }
  });

  const self_user_id = computed(() => user_info.value?.intl_openid);

  const is_self = computed(() => {
    if (uid.value) {
      return uid.value === self_user_id.value;
    }
    return !uid.value;
  });
  const is_client = computed(() => {
    return Boolean(uid.value) && uid.value !== self_user_id.value;
  });

  const client = createShiftysUser({ is_client: true, uid: uid.value });
  const self = createShiftysUser({ is_client: false, uid: self_user_id.value });
  const shiftys_user = computed(() => {
    if (is_client.value) return client;
    return self;
  });

  const userInfoWrap = (cb: () => Promise<any>) =>
    initUser()
      .then(() => cb())
      .catch(console.error);

  const initUser = async () => {
    await user.waitLoginCheckFinish();
    if (self_user_id.value) {
      return Promise.resolve();
    }
    if (user.is_login) {
      await user.waitingGetUserInfoFinish();
    }
    if (is_client.value) {
      client.updateState({ is_client: true, uid: uid.value });
    } else {
      self.updateState({ is_client: false, uid: self_user_id.value });
    }
    if (!user.is_login) throw new Error("login failed.");
  };

  const initAllUserData = async () => {
    await initUser();
    // is_client 的 watch 似乎时序会有影响, 因此sleep
    await sleep(1);
    logger.info(`[init-user-data]: client - ${is_client.value}, uid - ${uid.value}`);
    if (is_client.value) {
      client.updateState({ is_client: true, uid: uid.value });
    } else {
      self.updateState({ is_client: false, uid: self_user_id.value });
    }
    if (!self_user_id.value) throw new Error("user info not ready.");

    // update data.
    await (is_client.value ? client : self).initAllData();
    loadingStore.showLoading(false);
  };

  // method
  const liPassLogin = () => {
    return router.push({
      name: RoutesName.LOGIN,
      query: { ...route.query, to: route.path },
    });
  };

  logger.info("[store-loaded]");

  /**
   * 主态初始化
   */
  watch(
    () => user_info.value,
    () => {
      self.updateState({ is_client: false, uid: self_user_id.value });
    },
    { immediate: true },
  );

  /**
   * 切换客态
   */
  watch(
    [uid, is_client],
    () => {
      if (!self_user_id.value || !uid.value) return;
      client.resetUserInfo();
      client.updateState({ is_client: true, uid: uid.value });
      logger.info(
        `[reset-user-info-by-uid-change]: is_client ${is_client.value}, uid ${uid.value}`,
      );
    },
    {
      immediate: true,
    },
  );

  return {
    uid,
    is_self,
    is_client,
    logined,
    user_info,
    encrypt_uid,
    shown_role_id,
    shiftys_user,
    self_user_id,
    self_shifty_user: self,

    has_role: computed(() => shiftys_user.value.has_role.value),
    user_basic_info: computed(() => shiftys_user.value.user_basic_info.value),
    user_role_info: computed(() => shiftys_user.value.user_role_info.value),
    user_battle_info: computed(() => shiftys_user.value.user_battle_info.value),
    user_nikkelist_info: computed(() => shiftys_user.value.user_nikkelist_info.value),

    initUser,
    liPassLogin,
    initAllUserData,
    // 前置条件
    initSelfRole: () => userInfoWrap(() => self.initRoleInfo()),
    initRoleInfo: () => userInfoWrap(() => shiftys_user.value.initRoleInfo()),
    initUserBasicInfo: () => userInfoWrap(() => shiftys_user.value.initUserBasicInfo()),
    initUserBattleInfo: () => userInfoWrap(() => shiftys_user.value.initUserBattleInfo()),
    initUserNikkeInfo: () => userInfoWrap(() => shiftys_user.value.initUserNikkeInfo()),
  };
});
