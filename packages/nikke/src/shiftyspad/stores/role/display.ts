import { safeJSONParse } from "packages/utils/tools";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useRoleDisplayState = defineStore("nikke_display", () => {
  const status = ref<"voice" | "none" | "display">("none");

  function changeStatus(value: "voice" | "none" | "display") {
    status.value = value;
  }

  // 内存缓存态
  const filter_status: Map<string, any> = new Map();
  const getValue = (key: string) => {
    return filter_status.get(key);
  };

  const setValue = (key: string, value: any) => {
    if (value) {
      filter_status.set(key, safeJSONParse(JSON.stringify(value)));
    } else {
      filter_status.delete(key);
    }
  };

  return {
    status,
    changeStatus,

    getValue,
    setValue,
  };
});
