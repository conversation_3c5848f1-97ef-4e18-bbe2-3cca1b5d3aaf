import { getLang } from "@/shiftyspad/const";
import { CVLang } from "packages/types/shiftyspad";
import { CharacterData } from "@/shiftyspad/types/character";
import { defineStore } from "pinia";

const default_cv =
  {
    ja: CVLang.JA,
    ko: CVLang.KO,
    en: CVLang.EN,
    th: CVLang.EN,
    fr: CVLang.EN,
    de: CVLang.EN,
    zh: CVLang.JA,
    sea: CVLang.EN,
    "zh-TW": CVLang.JA,
  }[getLang()] ?? CVLang.EN;
const getState = (): State => {
  return {
    has_voice: false,
    aborter: null,
    current_play: "",
    current_player: null,
    cv_lang: all_voice_list.find((v) => v.lang === default_cv) ?? all_voice_list[0],
  };
};

type State = {
  cv_lang: {
    label: string;
    lang: CVLang;
  };
  has_voice: boolean;
  aborter: Function | null;
  current_play: string;
  current_player: HTMLAudioElement | null;
};

export const all_voice_list = [
  {
    lang: CVLang.JA,
    label: "cv_key_ja",
  },
  {
    lang: CVLang.KO,
    label: "cv_key_ko",
  },
  {
    lang: CVLang.EN,
    label: "cv_key_en",
  },
];

export const useCV = defineStore("cvstore", {
  state: getState,
  getters: {
    // @ts-ignore
    other_lang: (state) => {
      return all_voice_list.filter((v) => v.lang !== state.cv_lang.lang);
    },
    use_cv_key: (state) => {
      return (character: CharacterData) => {
        if (state.cv_lang.lang === CVLang.JA) {
          return character.cv_localkey_ja;
        }
        if (state.cv_lang.lang === CVLang.EN) {
          return character.cv_localkey_en;
        }
        return character.cv_localkey_ko || character.cv_localkey;
      };
    },
  },
  actions: {
    switch(lang: (typeof all_voice_list)[number]) {
      this.cv_lang = lang;
    },
    setVoiceExistState(exist: boolean) {
      this.has_voice = exist;
    },
    setVoice(params: null | { player: HTMLAudioElement; url: string; abortcontrol: Function }) {
      if (!params) {
        this.current_play = "";
        this.current_player = null;
        this.aborter = null;
        return;
      }
      const { player, url, abortcontrol } = params;
      this.current_play = url;
      this.current_player = player;
      this.aborter = abortcontrol;
    },
  },
});
