import { defineStore } from "pinia";
import { ref } from "vue";
import { t } from "@/locales";

export const useLoadingStore = defineStore("loading", () => {
  const loading = ref(true);
  const content = ref("loading...");
  function showLoading(value: boolean, text?: string) {
    loading.value = value;
    content.value = text ? text : `${t("loading")}...`;
  }
  return {
    loading,
    content,
    showLoading,
  };
});
