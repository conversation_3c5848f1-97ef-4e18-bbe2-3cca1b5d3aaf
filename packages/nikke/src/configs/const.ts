// export const TWITTER_EVENT_HOST =
//   "https://aigc-yuqing-prod-1312254802.cos.ap-singapore.myqcloud.com/twitter/twitter.html";
export const TWITTER_EVENT_HOST = `${window.location.origin}/twitter.html`;
export const TWITTER_EVENT_HOST2 =
  "https://syndication.twitter.com/srv/timeline-profile/screen-name";

export const TWITTER_LANGUAGES: Record<string, string> = {
  en: "en",
  ja: "japan",
  ko: "kr",
};

export const COMPOSE_MAX_IMAGE_LEN = 20;
export const COMPOSE_MAX_TAG_LEN = 10;
export const COMPOSE_COMMENT_LEN = 1000;

export const EDITOR_MAX_LEN = 10000;

export const EDITOR_EMOJI_IMAGE_HEIGHT = 60;
export const EDITOR_EMOJI_IMAGE_WIDTH = 60;

export const EMOJI_IMAGE_SYMBOL = "imgtype=emoji";
export const EMOJI_HISTORY_MAX = 24;

export const MAX_USER_NAME_LEN = 20;

export const COMMON_QUERY_KEYS = {
  EncodedUid: "uid",
  OpenId: "openid",
  NikkeId: "nikke",
  PageSource: "from",
};

// 可分享的url query 白名单
export const SHARE_WHITE_LIST_QUERY_KEYS = [...Object.values(COMMON_QUERY_KEYS)];
