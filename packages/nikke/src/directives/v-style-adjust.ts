import { getStandardizedLang } from "packages/utils/standard";
import { DirectiveBinding } from "vue";

interface Element extends HTMLElement {}

const sys_lang = getStandardizedLang();

/**
 * v-style-adjust="{
 *  langs: ['zh', 'hk', 'tw'],
 *  style: {
 *    height: '0.56rem',
 *  },
 *  th: {
 *    height: '0.88rem',
 *  },
 *  fr: {
 *    height: '0.88rem',
 *  },
 * }"
 *
 * or
 *
 * v-style-adjust
 */

/**
 * @description 元素的文本调整，主要针对某些国家下，样式需要做特定的调整
 * @param   {[type]}  app  [app description]
 * @return  {[type]}       [return description]
 */
const onMounted = (el: Element, binding: DirectiveBinding) => {
  const { langs, style } = binding.value || {};

  if (langs?.includes(sys_lang) && style) {
    Object.assign(el.style, style);
  }

  binding.value &&
    Object.keys(binding.value)
      .filter((key) => !["langs", "style"].includes(key))
      .forEach((lang) => {
        if (lang === sys_lang) {
          Object.assign(el.style, binding.value[lang]);
        }
      });
};

const onUnmount = () => {};

export const StyleAdjustDirective = {
  // vue3
  mounted: onMounted,
  unmounted: onUnmount,
};
