// utils
import { isFunction } from "lodash-es";
import { Directive, DirectiveBinding } from "vue";
import { useInterceptor } from "@/composables/use-interceptor";
import { isSelected } from "packages/utils/tools";

const onBeforeMount = (el: HTMLElement & any, binding: DirectiveBinding) => {
  const { value: cb, modifiers } = binding;
  const {
    //
    stop,
    prevent,
    capture,
    passive,
    once,
    need_login,
    mute,
    sign_privacy,
    check_user_adult,
  } = modifiers || {};

  const onClick = async (event: Event) => {
    if (isSelected()) {
      return;
    }
    stop && event.stopPropagation();
    prevent && event.preventDefault();

    const {
      //
      needLoginInterceptor,
      muteInterceptor,
      signPrivacyInterceptor,
      checkUserAdultInterceptor,
    } = useInterceptor();

    if (need_login) {
      await needLoginInterceptor();
    }

    if (mute) {
      await muteInterceptor();
    }

    if (sign_privacy) {
      await signPrivacyInterceptor();
    }

    if (check_user_adult) {
      await checkUserAdultInterceptor();
    }

    const execute = () => {
      // 调用原始的点击事件处理程序
      isFunction(cb) && cb(event);
    };

    execute();
  };

  // 注册原生点击事件
  el.addEventListener("click", onClick, { capture, passive, once });
  // 为了在 unbind 钩子中清除事件监听器，将 onClick 函数存储在元素上
  el.__click_interceptor__ = onClick;
};

const onBeforeUnmount = (el: HTMLElement & any) => {
  // console.log("[onBeforeUnmount] called");
  el.removeEventListener("click", el.__click_interceptor__);
  delete el.__click_interceptor__;
};

export const ClickInterceptorDirective: Directive = {
  // vue3
  beforeMount: onBeforeMount,
  beforeUnmount: onBeforeUnmount,
};
