import { ClickInterceptorDirective } from "./v-click-interceptor";
import { StickyDirective } from "./v-sticky";
import { StyleAdjustDirective } from "./v-style-adjust";
import { SafeHtmlDirective } from "./v-safe-html";
import { App } from "vue";

const DIRECTIVE_NAMES = {
  "click-interceptor": "click-interceptor",
  sticky: "sticky",
  "style-adjust": "style-adjust",
  "safe-html": "safe-html",
};

const checkDirectiveExist = (app: App, name: string) => {
  if (app._context.directives[DIRECTIVE_NAMES[name as keyof typeof DIRECTIVE_NAMES]]) {
    return true;
  }
  return false;
};

export const useClickInterceptorDirective = (app: App) => {
  !checkDirectiveExist(app, DIRECTIVE_NAMES["click-interceptor"]) &&
    app?.directive(DIRECTIVE_NAMES["click-interceptor"], ClickInterceptorDirective);
};

export const useStickyDirective = (app: App) => {
  !checkDirectiveExist(app, DIRECTIVE_NAMES.sticky) &&
    app?.directive(DIRECTIVE_NAMES.sticky, StickyDirective);
};

export const useStyleAdjustDirective = (app: App) => {
  !checkDirectiveExist(app, DIRECTIVE_NAMES["style-adjust"]) &&
    app?.directive(DIRECTIVE_NAMES["style-adjust"], StyleAdjustDirective);
};

export const useSafeHtmlDirective = (app: App) => {
  !checkDirectiveExist(app, DIRECTIVE_NAMES["safe-html"]) &&
    app?.directive(DIRECTIVE_NAMES["safe-html"], SafeHtmlDirective);
};

export const useDirectives = (app: App) => {
  useClickInterceptorDirective(app);
  useStickyDirective(app);
  useStyleAdjustDirective(app);
  useSafeHtmlDirective(app);
};
