import { debounce } from "lodash-es";
import { DirectiveBinding } from "vue";
import { StickyPositoin, StickyType } from "packages/types/directives";

interface Element extends HTMLElement {
  $onUnmount: Function;
}

const win = window;

const onMounted = (el: Element, binding: DirectiveBinding) => {
  const {
    sticky_position,
    sticky_z_index,
    sticky_type,
    validate,
    callback,
    observer_options,
    scroll_debounce,
    scroll_container,
  } = Object.assign(
    {
      scroll_container: document.getElementById("layout-content"),
    },
    binding.value,
  );

  if (validate && !validate()) {
    return;
  }

  /**
   * @link https://github.com/w3c/IntersectionObserver/issues/296
   */
  const is_support_intersection_observer = "IntersectionObserver" in win;

  let current_position = StickyPositoin.static;
  let observer: any;

  const isUseObserver = (): boolean =>
    sticky_type
      ? is_support_intersection_observer && sticky_type === StickyType.observer
      : is_support_intersection_observer;

  const handler = (bool: boolean, options?: any) => {
    if (
      (bool && current_position === StickyPositoin.sticky) ||
      (!bool && current_position === StickyPositoin.static)
    ) {
      return;
    }

    callback?.(bool, options);

    current_position = bool ? StickyPositoin.sticky : StickyPositoin.static;

    Object.assign(
      el.style,
      bool
        ? {
            position: "sticky",
            top: `${sticky_position || 0}px`,
            zIndex: sticky_z_index || 10,
          }
        : {
            position: "static",
          },
    );
  };

  const onObserver = () => {
    if (!is_support_intersection_observer) {
      console.warn("IntersectionObserver is not supported");
      return;
    }

    let observer_element: any;
    let temp_el = el;

    while (!observer_element && temp_el && temp_el.tagName !== "BODY") {
      observer_element = temp_el.previousElementSibling;
      // if not found sibling element, go to parent element
      if (!observer_element) {
        temp_el = temp_el.parentElement as Element;
      }
    }

    if (!observer_element) {
      console.warn("Can not find sibling element");
      return;
    }

    observer = new IntersectionObserver(
      (entries) => {
        const bool = !entries[0].isIntersecting;
        handler(bool, { observer_element, el });
      },
      Object.assign(
        {
          threshold: 0.1,
        },
        observer_options || {},
      ),
    );

    observer.observe(observer_element);
  };

  const onScroll = debounce(() => {
    const rect = el.getBoundingClientRect();
    const bool = Math.floor(rect.top) <= sticky_position;
    handler(bool);
  }, scroll_debounce || 50);

  isUseObserver() ? onObserver() : scroll_container.addEventListener("scroll", onScroll);

  el.$onUnmount = () => {
    isUseObserver()
      ? observer?.disconnect()
      : scroll_container.removeEventListener("scroll", onScroll);
  };
};

const onUnmount = (el: Element) => {
  el.$onUnmount?.();
};

export const StickyDirective = {
  // vue3
  mounted: onMounted,
  unmounted: onUnmount,
};
