import DOMPurify from "dompurify";
import { DirectiveBinding } from "vue";

type CustomElement = HTMLElement & { __pre_exist_el__: string | null };

const beforeMount = (el: CustomElement, binding: DirectiveBinding) => {
  // console.log("[v safe html] beforeMount");
  const { value, modifiers } = binding;
  const { merge } = modifiers;
  if (merge) {
    el.__pre_exist_el__ = el.innerHTML;
    el.innerHTML = el.__pre_exist_el__ + DOMPurify.sanitize(value);
  } else {
    el.innerHTML = DOMPurify.sanitize(value);
  }
};
const updated = (el: CustomElement, binding: DirectiveBinding) => {
  // console.log("[v safe html] updated");
  const { value, modifiers } = binding;
  const { merge } = modifiers;

  if (merge) {
    const bool = el.innerHTML !== el.__pre_exist_el__ + value;
    // console.log(`merge bool`, bool);

    if (bool) {
      el.innerHTML = el.__pre_exist_el__ + DOMPurify.sanitize(value);
    }
    return;
  }

  const bool = el.innerHTML !== value;
  // console.log(`not merge bool`, bool);
  if (bool) {
    el.innerHTML = DOMPurify.sanitize(value);
    return;
  }
};

const onBeforeUnmount = (el: CustomElement) => {
  if (el && "__pre_exist_el__" in el) {
    el.__pre_exist_el__ = null;
    delete (el as any).__pre_exist_el__;
  }
};

export const SafeHtmlDirective = {
  beforeMount,
  onBeforeUnmount,
  updated,
};
