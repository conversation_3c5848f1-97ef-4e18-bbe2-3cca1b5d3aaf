import { createI18n } from "vue-i18n";
import { getStandardizedLang } from "packages/utils/standard";
import { loadLanguageAsync } from "packages/utils/i18n";

export const i18n = createI18n({
  legacy: false,
  locale: getStandardizedLang(),
  silentTranslationWarn: true,
  missingWarn: false,
  globalInjection: true,
  warnHtmlMessage: false,
});

loadLanguageAsync(i18n);

export const t = i18n.global.t;
