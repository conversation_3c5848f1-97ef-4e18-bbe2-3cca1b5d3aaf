// import { createMutation, createQuery } from ".";
import { Commodity } from "packages/types/commodity";
import {
  API_COMMODITY_LIST,
  API_USER_COMMODITY_LIST,
  API_COMMODITY_DETAIL,
  API_COMMODITY_EXCHANGE,
  API_COMMODITY_CAN_EXCHANGE,
} from "packages/configs/api";
import { Role } from "packages/types/games";
import { http } from "./axios";
/**
 * @description 获取商品列表
 * @see https://yapi.intlgame.com/project/1634/interface/api/100119
 */
/*
export const useList = createQuery<
  {
    game_id_list?: string[];
    page_num?: number;
    page_size?: number;
  },
  Commodity
>({ method: "post", url: "/api/rewards/proxy/commodity/Commodity/GetCommodityList" }); */

/**
 * @description 获取商品列表
 * @see https://yapi.intlgame.com/project/1634/interface/api/100119
 */
export const getList = (params: {
  game_id_list?: string[];
  page_num?: number;
  page_size?: number;
  is_bind_lip?: boolean;
}): Promise<{ commodity_list: Commodity[]; total_num: number }> => {
  console.log("getList", params);
  return http.post(params.is_bind_lip ? API_USER_COMMODITY_LIST : API_COMMODITY_LIST, params);
};

/**
 * @description 商品详情
 * @see https://yapi.intlgame.com/project/1634/interface/api/100116
 */
export const getDetail = (id: string): Promise<Commodity> => {
  console.log("getDetail", id);
  return http.post(API_COMMODITY_DETAIL, { exchange_commodity_id: id });
};

/**
 * @description 兑换商品
 * @see https://yapi.intlgame.com/project/1634/interface/api/100113
 */
export const exchangeCommodity = (payload: {
  exchange_commodity_id: string;
  exchange_commodity_price: number;
  role_info: Role;
  save_role: boolean;
}): Promise<{ order_id: string }> => {
  console.log("exchangeCommodity", payload);
  //@ts-ignore
  return http.post(API_COMMODITY_EXCHANGE, payload, { ignore_toast: true });
};

/**
 * @description 能否兑换商品
 */
export const checkUserCanExchange = (id: string): Promise<{ can: boolean; has_num: number }> => {
  console.log("checkUserCanExchange", id);
  return http.post(
    API_COMMODITY_CAN_EXCHANGE,
    { exchange_commodity_id: id },
    //@ts-ignore
    { ignore_toast: true },
  );
};
