import { <PERSON>Info, PageParam } from "packages/types/common";
import { createMutation, createQuery } from ".";
import {
  API_SS_UGC_USER_GET_MESSAGE,
  API_SS_UGC_USER_READ_MESSAGE,
  API_SS_UGC_USER_READ_MESSAGE_ALL,
  API_SS_UGC_USER_UNREAD_MESSAGE,
} from "packages/configs/api";
import { ReadMessageAllType } from "@/types/user";
export type BaseNotification = {
  Receiver_user_intl_openid: string;
  area_id: string;
  brief: string;
  cms_msg_id: string;
  comment_content: string;
  comment_del: number;
  comment_uuid: string;
  content: string;
  created_on: string;
  ext_info: string;
  game_id: string;
  id: string;
  is_follow: boolean;
  is_read: number;
  is_star: boolean;
  modified_on: string;
  original_data: string;
  original_del: number;
  content_del: number;
  post_uuid: string;
  reply_del: number;
  reply_to_reply_uuid: string;
  reply_uuid: string;
  send_user_info: any;
  sender_user_intl_openid: string;
  type: number;
  img_url?: string;
};

/**
 * @deprecated 跟 packages/nikke/src/api/user.ts 定义重复，可以合并成一个
 * @description 获取消息列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111002
 */
export const useGetMessages = createQuery<
  PageParam & { type: ReadMessageAllType },
  { list: BaseNotification[]; page_info: PageInfo }
>({
  method: "post",
  url: API_SS_UGC_USER_GET_MESSAGE,
});

/**
 * @deprecated 跟 packages/nikke/src/api/user.ts 定义重复，可以合并成一个
 * @description 消息未读数
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111006
 */
export const useGetUnReadCount = createQuery<{}, { count: number }>({
  method: "post",
  url: API_SS_UGC_USER_UNREAD_MESSAGE,
});

/**
 * @deprecated 跟 packages/nikke/src/api/user.ts 定义重复，可以合并成一个
 * @description 未读消息全部已读
 * https://yapi.gpts.woa.com/project/1728/interface/api/111004
 */
export const useSetReadAll = createMutation<{}, {}>({
  method: "post",
  url: API_SS_UGC_USER_READ_MESSAGE_ALL,
});

/**
 * 某条消息已读
 * https://yapi.gpts.woa.com/project/1728/interface/api/111008
 */
export const useSetRead = createMutation<{ id: string }, {}>({
  method: "post",
  url: API_SS_UGC_USER_READ_MESSAGE,
});
