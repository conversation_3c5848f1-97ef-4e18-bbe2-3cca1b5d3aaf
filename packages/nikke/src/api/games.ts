/**
 * @description 游戏相关接口
 */
import { http } from "./axios";
import { API_GAME_ROLE, API_GAME_REGION } from "packages/configs/api";
import { Role, GameRegion } from "packages/types/games";
import { createQuery } from ".";
import { aegis } from "@/shiftyspad/service/rum";

type baseRoleParams = {
  area_id: number;
  game_id: string;
  plat_id?: string;
  zone_id?: string | number;
};

/**
 * @description (主态)查询角色列表
 * @see https://yapi.intlgame.com/project/1634/interface/api/100125
 */
export const getRoleInfo = (() => {
  const promise_cache = new Map<number, any>();
  const result_cache = new Map<number, any>();
  return (payload: baseRoleParams): Promise<{ role_list: Role[] }> => {
    if (!payload.area_id) {
      aegis.error("[invalid-role-req]", location.pathname);
    }
    const { area_id } = payload;
    const key = Number(area_id);
    if (result_cache.has(key)) {
      return Promise.resolve(result_cache.get(key)!);
    }
    if (promise_cache.has(key)) {
      return promise_cache.get(key);
    }
    promise_cache.set(key, http.post(API_GAME_ROLE, payload));
    return promise_cache.get(key);
  };
})();

/**
 * @description 查询已保存角色列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109652
 */
export const getSavedRoleInfo = (): Promise<{ role_info: Role; has_saved_role_info: boolean }> => {
  console.log("getSavedRoleInfo");
  return http.post("game/proxy/Game/GetSavedRoleInfo", {});
};

export type saveRoleParams = baseRoleParams & {
  game_name?: string;
  role_id: string;
};
/**
 * @description 保存角色
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109654
 */
export const saveRoleInfo = (payload: saveRoleParams): Promise<{ role_list: Role[] }> => {
  console.log("saveRoleInfo", payload);
  return http.post("game/proxy/Game/SaveRoleInfo", payload);
};

/**
 * @description 查询游戏区服
 * @see https://yapi.intlgame.com/project/1634/interface/api/102078
 */
export const useRegionList = createQuery<{ game_id: string }, { area_list: GameRegion[] }>(
  { method: "get", url: API_GAME_REGION },
  { staleTime: 5 * 60 * 1000 },
);
