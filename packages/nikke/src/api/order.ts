// configs
import { API_COMMODITY_ORDER_DETAIL } from "packages/configs/api";

// types
import { OrderDetail } from "packages/types/order";

// utils
import { createQuery } from ".";

/**
 * @description 订单详情
 * @see https://yapi.intlgame.com/project/1634/interface/api/102066
 * @return  {Promise<OrderDetail>}[return description]
 */
export const useOrderDetail = createQuery<
  {
    order_id: string;
  },
  { order_detail: OrderDetail }
>({
  method: "post",
  url: API_COMMODITY_ORDER_DETAIL,
});
