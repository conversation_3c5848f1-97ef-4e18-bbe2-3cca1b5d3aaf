// configs
import { API_SS_UGC_GET_ALL_EMOTICONS } from "packages/configs/api.ts";

// types
import { GetAllEmoticonsResponse } from "packages/types/emojis.ts";

// utils
import { createQuery } from "./index.ts";

/**
 * @description 查询全量表情信息
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112670
 *
 * @var {[type]}
 */
export const useGetAllEmoticons = createQuery<{}, GetAllEmoticonsResponse>({
  method: "post",
  url: API_SS_UGC_GET_ALL_EMOTICONS,
});
