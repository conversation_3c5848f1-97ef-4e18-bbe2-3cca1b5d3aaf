import {
  API_SS_UGC_DIRECT_DYNAMICS_GET_RECENT_TASKS,
  API_SS_UGC_DYNAMICS_GET_MY_SUBMISSION,
  API_SS_UGC_GET_CREATOR_HUB_LIST,
  API_SS_UGC_USER_BIND_CREATOR_HUB_ACCOUNT,
  API_SS_UGC_USER_CHANGE_SYNC_STATUS,
  API_SS_UGC_USER_GET_CREATOR_HUB_USER_INFO,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";
import { PageInfo } from "packages/types/common";

/**
 * 获取creatorhub活动
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/115600
 */
export const useGetCreatorHubTaskList = createQuery<
  {
    plate_id: number;
    previous_page_cursor?: string;
    next_page_cursor?: string;
    limit: number;
  },
  {
    list: {
      id: number;
      /** 活动图片 */
      image_url: string;
      /** 活动发布时间 */
      publish_time: number;
      /** 赛道 */
      ranks: {
        /** 赛道id */
        id: number;
        /** 赛道名称 */
        rank_name: string;
      }[];
      /** 活动id */
      task_id: number;
      /** 活动名称 */
      task_name: string;
      /** 活动状态0-已发布1-等待发布2-已经取消3-已经结束4-等待审批 */
      task_status: number;
      task_page_url?: string;
    }[];
    page_info: PageInfo;
  }
>({ method: "post", url: API_SS_UGC_GET_CREATOR_HUB_LIST });

export enum ChannelType {
  /** 未知 */
  Unknown = 0,
  /** YouTube */
  YouTube = 1,
  /** Facebook */
  Facebook = 2,
  /** Twitter */
  Twitter = 3,
  /** Twitch */
  Twitch = 4,
  /** YouTube Shorts */
  YouTubeShorts = 5,
  /** TikTok */
  TikTok = 6,
  /** Facebook Live */
  FacebookLive = 7,
  /** YouTube Live */
  YouTubeLive = 8,
  /** TikTok Live */
  TikTokLive = 9,
  /** Trovo */
  Trovo = 10,
  /** Pixiv */
  Pixiv = 11,
  /** Instagram */
  Instagram = 12,
  /** Chzzk */
  Chzzk = 13,
  /** Soop */
  Soop = 14,
}

export interface ThirdChannel {
  /** 渠道名称 */
  channel_name: string;
  /** 渠道类型 */
  channel_type: ChannelType;
}

export enum CreatorHubAccountStatus {
  /** 未完成注册 */
  Unregistered = 0,
  /** 审核中 */
  UnderReview = 1,
  /** 正常 */
  Normal = 2,
  /** 审核不通过 */
  Rejected = 3,
  /** 账号冻结 */
  Frozen = 4,
  /** 永久冻结 */
  PermanentlyFrozen = 5,
  /** 特殊封禁 */
  SpecialBan = 6,
  /** 仅允许提现 */
  WithdrawalOnly = 7,
}

/** Creatorhub绑定账号信息 */
export interface CreatorHubUserInfo {
  /** 邮箱 */
  email: string;
  /** 状态 */
  status: CreatorHubAccountStatus;
  /** 第三方渠道信息 */
  third_channels: ThirdChannel[];
  /** 用户ID */
  user_id: string;
  /** 用户名 */
  user_name: string;
  /** 是否开启自动同步 */
  is_auto_sync: number;
  /** 账号是否冻结 */
  is_freezed: number;
}

/**
 * 拉取Creatorhub绑定账号信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/125752
 */
export const useGetCreatorHubUserInfo = createQuery<{}, CreatorHubUserInfo>(
  { method: "post", url: API_SS_UGC_USER_GET_CREATOR_HUB_USER_INFO },
  { ignore_toast: true },
);

export interface EventTaskInfo {
  /** 任务结束时间 */
  end_time: number;
  /** 图片URL */
  image_url: string;
  /** 游戏ID */
  lip_gameid: string;
  /** 任务名称 */
  name: string;
  /** 任务发布时间 */
  publish_time: number;
  /** 任务模型类型 */
  task_model_type: number;
  /** 任务状态 */
  task_status: number;
  /** 任务类型 */
  task_type: number;
  /** 缩略图URL */
  thumbnail_url: string;
  /** 任务URL */
  task_url: string;
}

export interface RecentTasksResponse {
  /** 下一个偏移量 */
  next_offset: number;
  /** 任务列表 */
  task_list: EventTaskInfo[];
}

/**
 * 拉取RecentEvent
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/125754
 */
export const useGetRecentEvents = createQuery<
  { limit: number; offset: number },
  RecentTasksResponse
>({
  method: "post",
  url: API_SS_UGC_DIRECT_DYNAMICS_GET_RECENT_TASKS,
});

/**
 * 设置自动同步
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/125758
 */
export const useChangeSyncStatus = createMutation<
  {
    /** 0 关闭 1 开启 */
    status: number;
  },
  {}
>({ method: "post", url: API_SS_UGC_USER_CHANGE_SYNC_STATUS });

export interface MediaInfo {
  /** 资源ID */
  media_id: string;
  /** 资源类型，photo video */
  media_type: string;
  /** 原始资源URL */
  original_url: string;
  /** 预览图URL */
  preview_image_url: string;
}

export interface SubmissionItem {
  is_published_post: number;
  post_content: {
    content: string;
    content_summary: string;
    ext_info: string;
    id: number;
    is_original: number;
    language: string;
    order: number;
    original_reprint: number;
    original_url: string;
    pic_urls: any[];
    platform: string;
    title: string;
  };
  tag_id: string;
  work_content: {
    uid: string;
    work_id: string;
    work_publish_time: number;
  };
}

export interface GetMySubmissionResponse {
  next_idx: number;
  /** 提交作品列表 */
  list: SubmissionItem[];
}

/**
 * 拉取我的提交作品列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/125756
 */
export const useGetMySubmission = createQuery<
  {
    /** 每页数量 */
    limit: number;
    next_idx: number;
  },
  GetMySubmissionResponse
>({ method: "post", url: API_SS_UGC_DYNAMICS_GET_MY_SUBMISSION });

/**
 * 绑定Creatorhub账号
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/125750
 */
export const useBindCreatorHubAccount = createMutation<
  {
    /** creatorhub uid */
    uid: string;
    /** creatorhub_token */
    token: string;
  },
  { success: boolean }
>({ method: "post", url: API_SS_UGC_USER_BIND_CREATOR_HUB_ACCOUNT }, { ignore_toast: true });
