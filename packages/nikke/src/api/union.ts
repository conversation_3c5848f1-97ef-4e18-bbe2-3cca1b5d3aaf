import {
  API_GAME_GET_GUILD_DETAIL,
  API_GAME_GET_MY_GUILD_INFO,
  API_GAME_JOIN_GUILD,
  API_GAME_PUBLISH_GUILD_CARD,
  API_GAME_QUERY_GUILD_CARD_SUPPORTERS_BY_TOURIST,
  API_GAME_QUERY_GUILD_CARDS,
  API_GAME_QUERY_GUILD_CARDS_BY_TOURIST,
  API_GAME_QUERY_GUILD_HOT_POST,
  API_GAME_SUPPORT_GUILD,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";
import { PostDetail, PostItem } from "packages/types/post";
import { UnionCard, SupportUserInfo, UnionJoinType, UnionRank } from "packages/types/union";
import { CODE_ALL_CONFIGS } from "packages/configs/code";

export { type UnionCard, type SupportUserInfo, UnionJoinType, UnionRank };

export type UnionDetail = UnionCard & {
  /** 公会招募帖子 */
  post_info?: PostDetail;
  /** 是否为应援用户 */
  is_supporter?: boolean;
  /** 公会是否已经发布到广场 */
  is_published: boolean;
  /** 应援用户信息列表 */
  support_user_infos: SupportUserInfo[];
};

/**
 * 查询公会卡片列表（游客态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126124
 */
export const useQueryGuildCardsByTourist = createQuery<
  {},
  {
    /** 追溯id */
    trace_id: string;
    /** 公会卡片列表 */
    cards: UnionCard[];
  }
>({
  method: "post",
  url: API_GAME_QUERY_GUILD_CARDS_BY_TOURIST,
});

/**
 * 查询公会卡片列表（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126126
 */
export const useQueryGuildCards = createQuery<
  {
    /** nikke区服id */
    nikke_area_id?: number;
    /** 搜索内容 */
    search_keyword?: string;
    /** 公会rank */
    guild_rank?: number;
  },
  {
    /** 追溯id */
    trace_id: string;
    /** 公会卡片列表 */
    cards: UnionCard[];
  }
>({
  method: "post",
  url: API_GAME_QUERY_GUILD_CARDS,
});

/**
 * 查询我的公会详情（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126128
 */
export const useGetMyGuildInfo = createQuery<
  { ignore_toast?: boolean },
  { card: UnionDetail; trace_id: string }
>(
  {
    method: "post",
    url: API_GAME_GET_MY_GUILD_INFO,
  },
  {
    ignore_toast: (_data, params) => {
      return params.ignore_toast ?? true;
    },
  },
);

/**
 * 查询目标公会详情（登录态）(和查看我的公会接口一样，但是参数不同)
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126128
 */
export const useUserGuildInfo = createQuery<
  {
    /** 目标nikke区服id 例如84表示GLOBAL, 91表示HMT */
    target_nikke_area_id: number;
    /** 目标用户 intl_open_id(原生openid，无game_id和横杠前缀) */
    target_intl_open_id: string;
  },
  { card: UnionDetail; trace_id: string }
>({ method: "post", url: API_GAME_GET_MY_GUILD_INFO }, { ignore_toast: true });

/**
 * 根据公会id查询公会详情信息（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126132
 */
export const useGetGuildDetail = createQuery<
  { guild_id: string; nikke_area_id: string },
  { guild_detail: UnionDetail }
>({
  method: "post",
  url: API_GAME_GET_GUILD_DETAIL,
});

export interface JoinGuildRequest {
  /** 必填 nikke区服id 例如84表示GLOBAL, 91表示HMT */
  nikke_area_id: number;
  /** 公会id */
  guild_id: string;
  /** 公会名称(用于前端展示结果提示) */
  guild_name?: string;
}

/**
 * 加入公会（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126134
 */
export const useJoinGuild = createMutation<JoinGuildRequest, {}>(
  {
    method: "post",
    url: API_GAME_JOIN_GUILD,
  },
  {
    ignore_toast(data) {
      // 需要审批
      if (data.code === CODE_ALL_CONFIGS.JoinGuildNeedApproval) return true;
      return false;
    },
  },
);
export interface PublishGuildCardRequest {
  /** 必填 nikke区服id 例如84表示GLOBAL, 91表示HMT */
  nikke_area_id: number;
  /** 公会id */
  guild_id: string;
}

/**
 * 发布公会卡片到公会广场（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126136
 */
export const usePublishGuildCard = createMutation<PublishGuildCardRequest, {}>({
  method: "post",
  url: API_GAME_PUBLISH_GUILD_CARD,
});

export interface SupportGuildRequest {
  /** 必填 nikke区服id 例如84表示GLOBAL, 91表示HMT */
  nikke_area_id: number;
  /** 公会id */
  guild_id: string;
  /** 应援类型 1:应援, 2:取消应援 */
  support_type: 1 | 2;
}

/**
 * 应援/取消应援 公会（登录态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126140
 */
export const useSupportGuild = createMutation<SupportGuildRequest, {}>({
  method: "post",
  url: API_GAME_SUPPORT_GUILD,
});

/**
 * 查询公会应援用户信息列表（游客态）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126142
 */
export const useQueryGuildCardSupportersByTourist = createQuery<
  {
    /** 必填 nikke区服id 例如84表示GLOBAL, 91表示HMT */
    nikke_area_id: number;
    /** 公会卡片uuid */
    guild_card_uuid: string;
  },
  {
    /** 应援用户信息列表 */
    support_user_infos: SupportUserInfo[];
    /** 追溯id */
    trace_id: string;
  }
>({
  method: "post",
  url: API_GAME_QUERY_GUILD_CARD_SUPPORTERS_BY_TOURIST,
});

/**
 * 查询公会广场热门帖子
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126114
 */
export const useQueryGuildHotPost = createQuery<
  {
    guild_id: string;
    nikke_area_id: number;
  },
  { post_info?: PostItem }
>({
  method: "post",
  url: API_GAME_QUERY_GUILD_HOT_POST,
});
