import { createMutation, createQuery } from ".";

/**
 * @description 礼包是否有剩余
 * @see https://doc.weixin.qq.com/doc/w3_ANMAPQYLACoCN0PcWYdbcR5m1WLXG?scode=AJEAIQdfAAoHK6OAcNANMAPQYLACo
 */
export const useGiftCardHasLeft = createQuery<
  {},
  {
    present_group_left: { "1": boolean; "2": boolean; "3": boolean; "4": boolean };
  }
>({ method: "post", url: "/lip/flow_direct/Wand-20250408150128-29c74237d07f9" });

/**
 * @description 用户领取礼包数量
 * @see https://doc.weixin.qq.com/doc/w3_ANMAPQYLACoCN0PcWYdbcR5m1WLXG?scode=AJEAIQdfAAoHK6OAcNANMAPQYLACo
 */
export const useUserGiftCardExchangeCount = createQuery<{}, { user_has_present_count: number }>({
  method: "post",
  url: "/lip/flow/Wand-20250408192434-1774b17ca8581",
});

/**
 * @description 奖励兑换
 * @see https://doc.weixin.qq.com/doc/w3_ANMAPQYLACoCN0PcWYdbcR5m1WLXG?scode=AJEAIQdfAAoHK6OAcNANMAPQYLACo
 */
export const useGiftCardExchange = createMutation<
  {
    tag_id: string;
    game_id: string;
    area_id: number;
    role_id: string;
    _headers: {
      /**
       * 验证码 JSON 字符串
       * - 格式：{ randstr: string, ticket: string }
       */
      "X-Captcha": string;
    };
  },
  { order_serial_number: string }
>({ method: "post", url: "/lip/flow/Wand-20250408193200-dbef92163aaef" }, { ignore_toast: true });
