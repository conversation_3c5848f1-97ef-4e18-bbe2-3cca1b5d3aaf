// configs
import {
  API_GAME_RECORD_CDK_REDEMPTION,
  API_GAME_GET_CDK_REDEMPTION_HISTORY,
} from "packages/configs/api";

// types
import { CdkRedemptionListItem } from "packages/types/cdk.ts";

// utils
import { createMutation, createQuery } from ".";

/**
 * @description 记录CDK兑换信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/118724
 */
export const useApiGetCdkRedemptionHistory = createQuery<
  {
    page_num: number;
    page_size: number;
  },
  {
    cdk_redemption_list: Array<CdkRedemptionListItem>;
    total: number;
    is_last_page: boolean;
  }
>({ method: "post", url: API_GAME_GET_CDK_REDEMPTION_HISTORY }, { ignore_toast: true });

/**
 * @description 记录CDK兑换信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/118724
 */
export const useApiRecordCdkRedemption = createMutation<{ cdkey: string }, { code: number }>(
  {
    method: "post",
    url: API_GAME_RECORD_CDK_REDEMPTION,
  },
  { ignore_toast: true },
);
