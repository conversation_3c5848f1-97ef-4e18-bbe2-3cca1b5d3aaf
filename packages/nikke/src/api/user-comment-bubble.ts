import {
  API_USER_GET_ALL_COMMENT_BUBBLE_LIST,
  API_USER_GET_COMMENT_BUBBLE_LIST,
  API_USER_SET_COMMENT_BUBBLE,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";
import { useGetMyProfile, useGetUserProfile } from "./user";
import { UserCommentBubble } from "packages/types/user";
import { PageInfo } from "packages/types/common";

/**
 * 获取用户评论气泡列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122202
 */
export const useGetUserCommentBubbleList = createQuery<
  {
    limit?: number;
    next_page_cursor?: string;
  },
  {
    user_comment_bubbles: UserCommentBubble[];
    page_info: PageInfo;
  }
>({ method: "post", url: API_USER_GET_COMMENT_BUBBLE_LIST });

/**
 * 设置评论气泡
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122206
 */
export const useSetUserCommentBubble = createMutation<
  {
    /** 挂件id */
    comment_bubble_id: string;
    /** 0 取消穿戴； 1 穿戴 */
    set_wear_status: 0 | 1;
  },
  {}
>(
  { method: "post", url: API_USER_SET_COMMENT_BUBBLE },
  { refetch: [useGetUserProfile, useGetMyProfile] },
);

/**
 * 获取所有气泡列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122464
 */
export const useGetAllCommentBubbleList = createQuery<
  {},
  {
    comment_bubbles: {
      id: string;
      /** 挂件地址 */
      comment_bubble: string;
      /** 背景色 */
      bg_color: string;
    }[];
  }
>({ method: "post", url: API_USER_GET_ALL_COMMENT_BUBBLE_LIST });
