/**
 * @description 积分相关请求
 */
import {
  API_LIPASS_BIND_PRESENT_INFO,
  API_LIPASS_BIND_PRESENT,
  API_REWARDS_GET_POINTS,
  API_REWARDS_DAILY_CHECK_IN,
  API_REWARDS_MIDAS_TASK,
  API_REWARDS_GET_USER_COLLECTION,
  API_REWARDS_USER_COMPLETE_COLLECTION,
} from "packages/configs/api";
import { Task, Status, CollectionStatus } from "packages/types/rewards";
import { http } from "./axios";
import { Role } from "packages/types/games";
import { createQuery } from ".";
import { useGetTaskListV2, useGetTaskListWithStatusV2 } from "./mission";
import { getStandardizedGameId } from "packages/utils/standard";

/**
 * @description 查询用户总积分
 * @see https://yapi.intlgame.com/project/1634/interface/api/99954
 */
export const getPoints = () => {
  //@ts-ignore
  return http.get(API_REWARDS_GET_POINTS, { ignore_toast: true });
};

/**
 * @description 查询任务列表-区分用户态
 * @see https://yapi.intlgame.com/project/1634/interface/api/99951
 * @see https://yapi.intlgame.com/project/1634/interface/api/100185
 */
export const getTasks = (is_bind_lip: boolean): Promise<{ tasks: Task[] }> => {
  const gameid = getStandardizedGameId();
  return (is_bind_lip ? useGetTaskListWithStatusV2 : useGetTaskListV2)
    .run({ get_top: true, intl_game_id: gameid })
    .then((res) => {
      return {
        tasks: res.tasks.map((i) => ({ ...i, ...i.reward_infos?.[0] })),
      };
    });

  // return http.get(is_bind_lip ? API_REWARDS_GET_TASKS_LOGIN : API_REWARDS_GET_TASKS, {
  //   //@ts-ignore
  //   ignore_toast: true,
  // });
};

/**
 * @description 每日签到加积分
 * @see https://yapi.intlgame.com/project/1634/interface/api/99999
 */
export const dailyCheckIn = (id: string): Promise<{ status: Status }> => {
  return http.post(API_REWARDS_DAILY_CHECK_IN, { task_id: id }, { ignore_toast: true } as any);
};

export const useDailyCheckIn = createQuery<{ task_id: string }, {}>(
  {
    method: "post",
    url: API_REWARDS_DAILY_CHECK_IN,
  },
  { ignore_toast: true } as any,
);

/**
 * @description 获取米大师任务完成状态
 * @see https://yapi.intlgame.com/project/1634/interface/api/101133
 */
export const getMidasTaskStatus = (id: string): Promise<{ is_completed: boolean }> => {
  return http.get(API_REWARDS_MIDAS_TASK, {
    params: {
      task_id: id,
    },
  });
};

/**
 * @description 获取用户是否完成该任务
 * @see https://yapi.gpts.woa.com/project/1634/interface/api/892
 */
export const getUserCollection = (params: {
  task_id: string;
}): Promise<{ status: CollectionStatus }> => {
  return http.get(API_REWARDS_GET_USER_COLLECTION, { params });
};

/**
 * @description 获取用户是否完成该任务
 * @see https://yapi.gpts.woa.com/project/1634/interface/api/892
 */
export const setUserCompleteCollection = (params: { task_id: string }): Promise<void> => {
  return http.post(API_REWARDS_USER_COMPLETE_COLLECTION, params);
};

/**
 * @description 获取Lip绑定礼包
 * @see https://yapi.gpts.woa.com/project/1344/interface/api/112608
 */
export const useGetBindPresent = createQuery<
  { role_info: Role },
  {
    code: number;
    msg: string;
  }
>({
  method: "post",
  url: API_LIPASS_BIND_PRESENT,
});

/**
 * @description 绑定账号成功后, 领取奖励
 * @see https://yapi.gpts.woa.com/project/1344/interface/api/112610
 */
export const useQueryUserAward = createQuery<
  {},
  {
    can_sent: boolean;
    is_sent: boolean;
  }
>(
  {
    method: "post",
    url: API_LIPASS_BIND_PRESENT_INFO,
  },
  { ignore_toast: true },
);
