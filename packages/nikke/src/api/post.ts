/**
 * @description 动态相关接口
 */
// configs
import {
  API_SS_UGC_CREATE_POST,
  API_SS_UGC_CREATE_POST_NEW,
  API_SS_UGC_GET_POST,
  API_SS_UGC_GET_POST_COLLECTION,
  API_SS_UGC_GET_POST_LIST,
  API_SS_UGC_GET_POST_STAR,
  API_SS_UGC_POST_COLLECTION,
  API_SS_UGC_POST_STAR,
  API_SS_UGC_GET_VIDEO_INFO_BY_URL,
  API_SS_UGC_DELETE_POST,
  API_SS_UGC_POST_FORWARD,
  API_SS_UGC_GET_USER_POST_LIST,
  API_SS_UGC_GET_USER_POST_COLLECTION_LIST,
  API_SS_UGC_CONTENT_REPORT,
  API_SS_UGC_MOVE_POST,
  API_SS_UGC_POST_SHARE_URL,
  API_SS_UGC_TOPIC_SHARE_URL,
  API_SS_UGC_TRANSLATE_CONTENT,
  API_SS_UGC_CREATE_UPDATE_POST,
  API_SS_UGC_SEND_ADD_FRIEND_REQUEST,
  API_SS_UGC_DYNAMICS_POST_CHANGE_TAG_BIND,
  API_SS_UGC_DYNAMICS_UPDATE_STATEMENT,
  API_SS_UGC_SEND_ADD_FRIEND_REQUEST_WITH_PRIVACY_PERMISSION,
} from "packages/configs/api.ts";

// types
import {
  PostItem,
  PostsParams,
  GetVideoInfoByURLRequestParams,
  GetVideoInfoByURLResponse,
  ComposeRequestParams,
  ComposeResopnse,
  PostDetail,
  ContentReportRequestParams,
  MovePostRequestParams,
  TranslateContentRequestParams,
  ComposeNewRequestParams,
  ComposeNewResopnse,
  PostDeleteReason,
  PostChangeTagBindRequest,
  PostChangeTagBindResponse,
  UpdateStatementRequest,
} from "packages/types/post.ts";
import { PageInfo } from "packages/types/common.ts";

// utils
import { createMutation, createQuery } from "./index.ts";
import { LikeType, StanceType } from "packages/types/stance.ts";

/**
 * 更新创作声明
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126130
 */
export const useUpdateStatement = createMutation<UpdateStatementRequest, {}>({
  method: "post",
  url: API_SS_UGC_DYNAMICS_UPDATE_STATEMENT,
});

/**
 * @description 移动帖子话题
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126116
 */
export const usePostChangeTagBind = createMutation<
  PostChangeTagBindRequest,
  PostChangeTagBindResponse
>({
  method: "post",
  url: API_SS_UGC_DYNAMICS_POST_CHANGE_TAG_BIND,
});

/**
 * @description 更新动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/116892
 *
 * @return  {[type]}  [return description]
 */
export const useUpdatePost = createMutation<ComposeNewRequestParams, ComposeNewResopnse>(
  {
    method: "post",
    url: API_SS_UGC_CREATE_UPDATE_POST,
  },
  {
    // 如果帖子不存在，也不要跳到 404
    fe_ignore_not_found_jump: true,
  },
);

/**
 * @description 创建动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/116890
 *
 * @return  {[type]}  [return description]
 */
export const useCreatePostNew = createMutation<ComposeNewRequestParams, ComposeNewResopnse>({
  method: "post",
  url: API_SS_UGC_CREATE_POST_NEW,
});

/**
 * @description 翻译接口
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/115598
 *
 * @var {[type]}
 */
export const useTranslateContent = createMutation<
  TranslateContentRequestParams,
  { title: string; content: string }
>({
  method: "post",
  url: API_SS_UGC_TRANSLATE_CONTENT,
});

/**
 * @description 迁移动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112688
 *
 * @var {[type]}
 */
export const useMovePost = createQuery<MovePostRequestParams, {}>({
  method: "post",
  url: API_SS_UGC_MOVE_POST,
});

/**
 * @description 动态/评论举报
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112686
 *
 * @var {[type]}
 */
export const useContentReport = createMutation<ContentReportRequestParams, {}>({
  method: "post",
  url: API_SS_UGC_CONTENT_REPORT,
});

/**
 * @description 获取视频信息
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112600
 *
 * @var {[type]}
 */
export const useGetVideoInfoByURL = createQuery<
  GetVideoInfoByURLRequestParams,
  GetVideoInfoByURLResponse
>({
  method: "post",
  url: API_SS_UGC_GET_VIDEO_INFO_BY_URL,
});

/**
 * @description 创建动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112512
 *
 * @return  {[type]}  [return description]
 */
export const useCreatePost = createMutation<ComposeRequestParams, ComposeResopnse>({
  method: "post",
  url: API_SS_UGC_CREATE_POST,
});

/**
 * @description 获取动态列表
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112344
 *
 * @var {[type]}
 */
export const usePostList = createQuery<
  PostsParams,
  {
    list: PostItem[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_GET_POST_LIST,
});

/**
 * @description 获取我的动态列表
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112344
 *
 * @var {[type]}
 */
export const useMyPostList = createQuery<
  PostsParams & { intl_openid: string },
  {
    list: PostItem[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_GET_USER_POST_LIST,
});

/**
 * @description 获取我的动态列表
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112344
 *
 * @var {[type]}
 */
export const useMyCollectionPostList = createQuery<
  { intl_openid: string; page_type: number; limit: string; nextPageCursor?: string },
  {
    list: PostItem[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_GET_USER_POST_COLLECTION_LIST,
});

/**
 * @description  获取单个动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112346
 */
export const useGetPost = createQuery<
  {
    post_uuid: string;
    // 是否全量语言数据0-否1-是
    is_all_language?: 0 | 1;
    /** 1-浏览帖子 2-其他操作 */
    browse_post: 1 | 2;
    fe_ignore_not_found_jump?: boolean;
    /** 是否返回原始内容： 0-正常返回 1-只返回原文 */
    original_content: 0 | 1;
  },
  PostDetail
>({
  method: "post",
  url: API_SS_UGC_GET_POST,
});

/**
 * @description  获取动态点赞状态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112524
 */
export const getPostStar = createQuery<{ post_uuid: string }, { Status: number }>({
  method: "post",
  url: API_SS_UGC_GET_POST_STAR,
});

/**
 * @description  动态点赞&取消点赞
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112526
 */
export const postStar = createMutation<
  { post_uuid: string; type: StanceType; like_type?: LikeType },
  { Status: number }
>({
  method: "post",
  url: API_SS_UGC_POST_STAR,
});

/**
 * @description  获取动态收藏状态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112520
 */
export const getPostCollection = createQuery<{ id: string }, { Status: number }>({
  method: "post",
  url: API_SS_UGC_GET_POST_COLLECTION,
});

/**
 * @description 动态收藏&取消收藏
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112522
 */
export const postCollection = createQuery<{ post_uuid: string }, { Status: number }>({
  method: "post",
  url: API_SS_UGC_POST_COLLECTION,
});

/**
 * @description 删除动态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112518
 */
export const useDeletePost = createMutation<
  { post_uuid: string; del_reason?: PostDeleteReason },
  {}
>({
  method: "post",
  url: API_SS_UGC_DELETE_POST,
});

/**
 * @description 动态转发上报
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112684
 */
export const usePostForward = createMutation<{ post_uuid: string }, { forward_count: number }>({
  method: "post",
  url: API_SS_UGC_POST_FORWARD,
});

/**
 * @description 动态转发链接
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112788
 */
export const postShareUrl = API_SS_UGC_POST_SHARE_URL;

/**
 * @description 话题详情分享链接
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112910
 */
export const topicShareUrl = API_SS_UGC_TOPIC_SHARE_URL;

/**
 * @description 发起NIKKE添加好友请求
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/122240
 */
export const useSendAddFriendRequestByFriendCard = createMutation<{ post_uuid: string }, {}>({
  method: "post",
  url: API_SS_UGC_SEND_ADD_FRIEND_REQUEST,
});

/**
 * @description 发送好友请求带隐私权限
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/129752
 */
export const useSendAddFriendRequestWithPrivacyPermission = createMutation<
  { friend_uid: string },
  {}
>({ method: "post", url: API_SS_UGC_SEND_ADD_FRIEND_REQUEST_WITH_PRIVACY_PERMISSION });
