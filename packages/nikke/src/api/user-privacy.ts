import {
  API_SS_UGC_USER_GET_USER_PRIVACY_SETTING,
  API_SS_UGC_USER_USER_PRIVACY_SET,
  API_USER_SET_USER_SHIFTYSPAD_PRIVACY_SETTINGS,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";
import { PrivacySettings, ShiftyspadSetting } from "packages/types/setting";
/**
 * 查询隐私配置
 * 1.6 ShiftyspadSetting 和 PrivacySettings 融合了, 但是写接口不融合
 * https://yapi.gpts.woa.com/project/1728/interface/api/112592
 */
export const useGetPrivacySetting = createQuery<
  { intl_openid: string },
  PrivacySettings & ShiftyspadSetting
>({ method: "post", url: API_SS_UGC_USER_GET_USER_PRIVACY_SETTING }, { staleTime: 60 * 1000 });

/**
 * 设置隐私
 * https://yapi.gpts.woa.com/project/1728/interface/api/112594
 */
export const useModifyPrivacySetting = createMutation<{ type: number; is_off: number }, any>({
  method: "post",
  url: API_SS_UGC_USER_USER_PRIVACY_SET,
});

/**
 * 设置shiftyspad 隐私
 * https://yapi.gpts.woa.com/project/1344/interface/api/118616
 */
export const useModifyShiftyspadPrivacySetting = createMutation<
  ShiftyspadSetting,
  { is_ok: boolean }
>({
  method: "post",
  url: API_USER_SET_USER_SHIFTYSPAD_PRIVACY_SETTINGS,
});
