// configs
import {
  API_GET_SAVED_ROLE,
  API_TOOLS_GET_USER_ROLE,
  API_TOOLS_GET_PLAYER_GAME_INFO,
  API_TOOLS_GET_BATTLE_ROLE,
  API_TOOLS_GET_EQUIPS,
  API_TOOLS_GET_PLAYER_NIKKES,
  API_TOOL_GET_PLAYER_SORT_LIST,
  API_TOOL_SET_SELF_SORT_LIST,
  API_TOOL_GET_SELF_SORT_LIST,
  // flow
  API_GET_BONUS,
  API_QUERY_BONUS_STATUS,
  API_TOOLS_GET_CLIENT_PLAYER_GAME_INFO,
} from "packages/configs/api";
import {
  UserBattleInfo,
  UserBasicInfo,
  EquipFunctions,
  RoleInfo,
  Nikke,
} from "packages/types/shiftyspad";
import { Role } from "packages/types/games";
// import { getApiBaseUrl } from "packages/utils/tools";

import { createMutation, createQuery } from ".";

export const getServerTs = (): Promise<{ server_time: number }> => {
  return Promise.resolve({
    server_time: Math.ceil(Date.now() / 1000),
  });
  // return fetch(
  //   `${getApiBaseUrl(import.meta.env.MODE, true)}${API_GET_SERVER_TS.replace(/^\//, "")}`,
  // ).then((val) => val.json());
};

/**
 * @description 查询玩家角色基础信息(游戏进度, 有多少nikke等)
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109636
 */
export const useUserRole = createQuery<
  {},
  {
    has_saved_role_info: boolean;
    tag_id: string;
    player_level: number;
    role_info: RoleInfo;
    player_base_info: UserBasicInfo;
  }
>({
  method: "post",
  url: API_TOOLS_GET_USER_ROLE,
});

/**
 * @description 【详情页】获取用户玩家基本信息(角色信息卡等, 独立站专用)
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110960
 */
export const useUserBaiscGameInfo = createQuery<{} | Role, UserBasicInfo>(
  {
    method: "post",
    url: API_TOOLS_GET_PLAYER_GAME_INFO,
  },
  {
    ignore_toast: true,
    use_non_zero_ret: true,
  },
);

/**
 * @description 【详情页】获取用户玩家基本信息(角色信息卡等, 独立站专用)
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110960
 */
export const useClientVisitInfo = createQuery<{ uid: string }, { role_info: Role; icon: number }>(
  {
    method: "get",
    url: API_TOOLS_GET_CLIENT_PLAYER_GAME_INFO,
  },
  {
    ignore_toast: true,
    use_non_zero_ret: true,
  },
);

/**
 * @description 查询玩家角色battle信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109638
 */
export const useUserBattleInfo = createQuery<{} | Role, UserBattleInfo>(
  {
    method: "post",
    url: API_TOOLS_GET_BATTLE_ROLE,
  },
  {
    ignore_toast: true,
    use_non_zero_ret: true,
  },
);

/**
 * @description 批量查询玩家妮姬装备词条信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109640
 */
export const useUserEquipContent = createQuery<
  {
    character_ids: number[];
    role_info?: Role;
  },
  {
    result: number;
    player_equip_contents: {
      character_id: number;
      equip_contents: EquipFunctions[];
    }[];
  }
>(
  {
    method: "post",
    url: API_TOOLS_GET_EQUIPS,
  },
  {
    ignore_toast: true,
    use_non_zero_ret: true,
  },
);

/**
 * @description 获取玩家角色的全量妮姬数据
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109642
 */
export const useUserNikkesInfo = createQuery<
  {} | Role,
  {
    player_nikkes: Nikke[];
  }
>(
  {
    method: "post",
    url: API_TOOLS_GET_PLAYER_NIKKES,
  },
  {
    ignore_toast: true,
    use_non_zero_ret: true,
  },
);

export const queryBindAccountBonusStatus = () =>
  createQuery<
    {},
    {
      mission_has_done: boolean;
    }
  >({
    method: "post",
    url: API_QUERY_BONUS_STATUS,
  });

export const getBindAccountBonus = () =>
  createQuery<{ tag_id: string }, { code: number; msg: string }>({
    method: "post",
    url: API_GET_BONUS,
  });

/**
 * @description 查询玩家角色保存的角色信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109638
 */
export const useUserSavedInfo = createQuery<
  {},
  { icon: number; role_info: Role; has_saved_role_info: boolean }
>({
  method: "get",
  url: API_GET_SAVED_ROLE,
});

/**
 * @description 读取用户nikke自定义排序
 */
export const getUserSortSetting = createQuery<
  {},
  {
    list: number[];
    code?: number;
  }
>(
  {
    method: "post",
    url: API_TOOL_GET_PLAYER_SORT_LIST,
  },
  {
    use_non_zero_ret: true,
  },
);

export const getSelfSortSetting = createQuery<
  {},
  {
    list: number[];
  }
>({
  method: "post",
  url: API_TOOL_GET_SELF_SORT_LIST,
});

/**
 * @description 设置用户nikke自定义排序
 */
export const setUserSortSetting = createMutation<
  {
    list: number[];
  },
  {}
>({
  method: "post",
  url: API_TOOL_SET_SELF_SORT_LIST,
});
