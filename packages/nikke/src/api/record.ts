// configs
import { API_REWARDS_RECORD_LIST } from "packages/configs/api";

// types
import { RecordListFilter, RecordListItem } from "packages/types/record";

// utils
import { createQuery } from ".";

/**
 * @description 查询用户积分流水
 * @see https://yapi.intlgame.com/project/1634/interface/api/100161
 * @return  {Promise<Array><RecordListItem>}[return description]
 */
export const useRecordList = createQuery<
  RecordListFilter,
  {
    points: Array<RecordListItem>;
    total_count: number;
  }
>({
  method: "post",
  url: API_REWARDS_RECORD_LIST,
});
