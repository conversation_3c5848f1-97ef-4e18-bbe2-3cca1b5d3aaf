import {
  API_MISSION_SHIFTYPAD_BIND_GAME_MISSION_STATUS,
  API_MISSION_HAS_FINISH_ONBOARDING_MISSION_LIST,
  API_MISSION_ADD_FINISH_ONBOARDING_MISSION,
  API_MISSION_ONBOARDING_MISSION_GIFT_COLLECTION,
  API_REWARDS_GET_TASK_LIST_V2,
  API_REWARDS_GET_TASK_LIST_WITH_STATUS_V2,
  API_MISSION_GET_FOLLOW_TASK_OFFICIAL_ACCOUNTS,
  API_MISSION_QUICKLY_FOLLOW_ALL_OFFICIAL_ACCOUNTS,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";
import { UserInfo } from "packages/types/user";

/**
 * Shiftypad绑定游戏角色任务完成状态
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122512
 */
export const useShiftypadBindGameMissionStatus = createQuery<
  {},
  {
    /** 任务状态 未完成,已领取 */
    mission_has_done: boolean;
  }
>({ method: "post", url: API_MISSION_SHIFTYPAD_BIND_GAME_MISSION_STATUS });

/**
 * 任务完成状态列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122480
 */
export const useHasFinishOnboardingMissionList = createQuery<
  {},
  {
    /** 是否完成任务列表 */
    mission_done_list: {
      /** 任务状态 0未完成,1已完成,2已领取 */
      mission_status: MissionStatus;
      /** 任务标识 */
      tag_id: MissionTagId;
    }[];
  }
>({ method: "post", url: API_MISSION_HAS_FINISH_ONBOARDING_MISSION_LIST }, { ignore_toast: true });

/**
 * 记录任务完成
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122478
 */
export const useAddFinishOnboardingMission = createMutation<
  {
    /** 任务标识 */
    tag_id: MissionTagId;
  },
  {}
>({ method: "post", url: API_MISSION_ADD_FINISH_ONBOARDING_MISSION }, { refetch: [] });

/**
 * 礼包领取
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/122508
 */
export const useOnboardingMissionGiftCollection = createMutation<
  {
    /** 任务标识 */
    tag_id: MissionTagId;
  },
  {}
>({ method: "post", url: API_MISSION_ONBOARDING_MISSION_GIFT_COLLECTION }, { refetch: [] });

export enum MissionTagId {
  LOGIN = "1",
  BIND_LIPASS = "2",
  SHIFTYPAD_BIND_GAME = "3",
  ADD_TO_DESKTOP = "4",
  FOLLOW_NIKKE = "5",
}

export enum MissionStatus {
  /** 未完成 */
  UNFINISHED = 0,
  /** 已完成 */
  FINISHED = 1,
  /** 已领取 */
  COLLECTED = 2,
}

export type TaskV2 = {
  details: string;
  list_head_pic_url: string;
  reward_infos: {
    amount: string;
    is_completed: boolean;
    points: number;
    stage: number;
    stage_completed_pic_url: string;
    stage_pic_url: string;
    need_completed_times: number;
    completed_times: number;
  }[];
  task_completed_pic_url: string;
  task_id: string;
  task_name: string;
  task_pc_completed_pic_url: string;
  task_pc_pic_url: string;
  task_pic_url: string;
  task_type: number;
};

/**
 * 查询任务列表V2
 * @see https://yapi.gpts.woa.com/project/1634/interface/api/122518
 */
export const useGetTaskListV2 = createQuery<
  {
    get_top: boolean;
    intl_game_id: string;
    language?: string;
  },
  {
    /** 任务列表 */
    tasks: TaskV2[];
  }
>({ method: "get", url: API_REWARDS_GET_TASK_LIST_V2 });

/**
 * 获取积分任务详情+用户任务完成状态V2
 * @see https://yapi.gpts.woa.com/project/1634/interface/api/122520
 */
export const useGetTaskListWithStatusV2 = createQuery<
  {
    /** 是否是获取顶部的活动，是的话返回配置在顶部的活动，否则返回全部活动 */
    get_top: boolean;
    intl_game_id: string;
    language?: string;
  },
  {
    /** 任务列表 */
    tasks: TaskV2[];
  }
>({ method: "get", url: API_REWARDS_GET_TASK_LIST_WITH_STATUS_V2 });

/**
 * 获取官方账号关注任务 账号列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/123374
 */
export const useGetFollowTaskOfficialAccounts = createQuery<
  {},
  {
    users: {
      id: string;
      intl_openid: string;
      is_follow: boolean;
      is_mutual_follow: boolean;
      to_intl_openid: string;
      user_info: UserInfo;
    }[];
  }
>({ method: "get", url: API_MISSION_GET_FOLLOW_TASK_OFFICIAL_ACCOUNTS });

/**
 * 一键关注官方账号
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/123374
 */
export const useQuicklyFollowAllOfficialAccounts = createMutation<{}, {}>({
  method: "post",
  url: API_MISSION_QUICKLY_FOLLOW_ALL_OFFICIAL_ACCOUNTS,
});
