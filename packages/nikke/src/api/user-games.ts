import { GetUserGamePlayerInfoResponse } from "packages/types/user";
import { createMutation, createQuery } from ".";
import { API_SS_UGC_USER_PLAYER_INFO, API_SS_UGC_USER_SET_GAME_TAG } from "packages/configs/api";

/**
 * 获取用户游戏卡片信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112814
 */
export const useGetUserGamePlayerInfo = createQuery<
  { intl_openid: string },
  GetUserGamePlayerInfoResponse
>(
  { method: "post", url: API_SS_UGC_USER_PLAYER_INFO },
  { ignore_toast: true, staleTime: 60 * 1000 },
);

/**
 * 设置用户游戏Tag
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112812
 */
export const useSetUserGameTag = createMutation<
  {
    /** 游戏标签，1=Tower；2=Battle(Normal)；3=Battle(Hard)；4=Nikkes；5=Avatar Frames；6=Costumes */
    game_tag: number;
  },
  {}
>({ method: "post", url: API_SS_UGC_USER_SET_GAME_TAG }, { refetch: [] });
