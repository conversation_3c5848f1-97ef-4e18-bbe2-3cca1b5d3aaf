// cpnts
import { useToast } from "@/components/ui/toast";

// configs
import { CMS_INTL_GAME_ID_MAP_CMS_NIKKE_GAME_CONFIG } from "packages/configs/cms";
import { CODE_ALL_CONFIGS, CODE_MESSAGE_MAP, CODES_IGNORE_TOAST } from "packages/configs/code";

// utils
import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { getApiBaseUrl, getClientType, isInGame, safeParse } from "packages/utils/tools";
import { get } from "lodash-es";
import { Client } from "packages/utils/cos";
import { useUser } from "@/store/user";
import { useInterceptor } from "@/composables/use-interceptor";
import { t } from "@/locales";
import { useAuth } from "@/composables/use-auth";
import type { IgnoreToast } from ".";

const { checkPermission } = useAuth();
const { show: toast } = useToast();
const is_ingame = isInGame();
const client_type = getClientType();

export const http = axios.create({
  withCredentials: true,
  baseURL: getApiBaseUrl(import.meta.env.MODE),
});

const originHttpRequest = http.request;

const request = async <T = any, R = AxiosResponse<T>, D = any>(
  config: AxiosRequestConfig<D>,
): Promise<R> => {
  const { axiosRequestInterceptor } = useInterceptor();
  await axiosRequestInterceptor(config);

  return originHttpRequest(config);
};

http.request = request;

http.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  const intl_game_id = getStandardizedGameId();
  const language = getStandardizedLang();

  if (config.headers) {
    const cms_game_config = get(CMS_INTL_GAME_ID_MAP_CMS_NIKKE_GAME_CONFIG, intl_game_id, {
      game_id: "",
      area_id: "",
    });
    const x_common_params = {
      game_id: cms_game_config.game_id,
      area_id: cms_game_config.area_id,
      source: client_type,
      intl_game_id,
      language,
      // 写死对接后台正式环境登录态，因为 nikke 独立站只对接 lip 登录器正式环境 sdk
      env: "prod",

      // 数据统计
      data_statistics_scene: is_ingame ? "inner_game" : "outer",
      data_statistics_page_id: location.href,
      data_statistics_client_type: client_type,
      data_statistics_lang: language,
    };
    // console.log(`x_common_params`, x_common_params);
    config.headers["x-common-params"] = JSON.stringify(x_common_params);
    config.headers["x-language"] = language;
    config.headers["x-channel-type"] = "2"; // 1: LIP 2: NIKKE， 这里固定 2 就好
  }
  return config;
});

http.interceptors.response.use(
  async (response) => {
    const data = response.data as { code: number; msg: string; data: any };
    const config = response.config as any;
    const error_code = response.data.code;
    const user_store = useUser();

    if (data.code === 0) {
      return data.data;
    }

    // 异常码逻辑处理.
    if (config.use_non_zero_ret) {
      return Object.assign({}, data.data, { code: data.code });
    }

    // 权限校验
    if (
      !(await checkPermission({
        code: error_code,
        api: config.url,
        config,
      }))
    ) {
      throw { ...data, message: data.msg };
    }

    if (error_code === CODE_ALL_CONFIGS.NOT_BOUND_LIP && user_store.user_had_bound_lip) {
      user_store.user_had_bound_lip = false;
    }
    const ignore_toast = config?.["ignore_toast"] as IgnoreToast | undefined;
    const is_ignore_toast =
      typeof ignore_toast === "function"
        ? (ignore_toast(data, safeParse(config.data) ?? {}) as boolean)
        : (ignore_toast ?? false);

    !(CODES_IGNORE_TOAST.includes(error_code) || is_ignore_toast) &&
      toast({
        text:
          (CODE_MESSAGE_MAP[error_code] &&
            CODE_MESSAGE_MAP[error_code] !== t(CODE_MESSAGE_MAP[error_code]) &&
            t(CODE_MESSAGE_MAP[error_code])) ||
          t("default_error_tips"),
        type: "error",
      });

    throw { ...data, message: data.msg };
  },
  (error) => {
    return Promise.reject(error);
  },
);

let upload_client: Client | null = null;

export const getUploadClientInstance = async () => {
  if (upload_client) {
    return upload_client;
  }
  const { Client } = await import("packages/utils/cos");
  return (upload_client = new Client(http));
};
