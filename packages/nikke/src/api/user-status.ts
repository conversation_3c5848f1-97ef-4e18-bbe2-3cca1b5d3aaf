import { API_SS_UGC_USER_SET_USER_MOOD } from "packages/configs/api";
import { createMutation } from ".";

export type UserStatus = {
  id: string;
  label: string;
  url: string;
};

// /**
//  * 拉取心情列表配置
//  * TODO:需要等接口补齐协议
//  */
// export const useGetStatusList = createQuery<{}, { list: UserStatus }>({
//   method: "post",
//   url: API_SS_UGC_USER_SET_USER_MOOD,
// });

/**
 * 设置心情
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112678
 */
export const useModifyStatus = createMutation<{ mood: string }, any>({
  method: "post",
  url: API_SS_UGC_USER_SET_USER_MOOD,
});
