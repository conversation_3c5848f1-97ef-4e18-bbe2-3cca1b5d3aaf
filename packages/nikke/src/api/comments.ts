import { PageInfo } from "packages/types/common";
import { createMutation, createQuery } from ".";
import {
  API_SS_UGC_COMMENT_STAR,
  API_SS_UGC_DELETE_POST_COMMENT,
  API_SS_UGC_GET_POST_COMMENT_REPLIES,
  API_SS_UGC_GET_POST_COMMENTS,
  API_SS_UGC_GET_POST_COMMENT,
  API_SS_UGC_POST_COMMENT,
  API_SS_UGC_GET_USER_COMMENT_LIST,
  API_SS_UGC_SET_COMMENT_TOP_OR_BOTTOM,
  API_SS_UGC_GET_POST_COMMENTS_V2,
  API_SS_UGC_BATCH_GET_POST_COMMENT_REPLIES,
} from "packages/configs/api";
import {
  ComposeCommentParams,
  GetPostCommentsParams,
  GetPostCommentsResponseItem,
  GetPostCommentRepliesParams,
  GetPostCommentRepliesResponseItem,
  CommentStarRequestParams,
  GetMyCommentItem,
  TopBottomStatus,
  BatchGetPostCommentRepliesResponse,
} from "packages/types/comments";
import { PostDeleteReason } from "packages/types/post";

/**
 * @description 设置置顶置底状态
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/126064
 */
export const useSetCommentTopOrBottom = createQuery<
  {
    comment_uuid: string;
    top_bottom_status: TopBottomStatus;
  },
  {}
>({
  method: "post",
  url: API_SS_UGC_SET_COMMENT_TOP_OR_BOTTOM,
});

/**
 * @description 获取动态评论信息
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112682
 */
export const useGetPostComment = createQuery<
  { comment_uuid: string },
  { comment: GetPostCommentsResponseItem }
>({
  method: "post",
  url: API_SS_UGC_GET_POST_COMMENT,
});

/**
 * 回复/评论点赞和取消
 * https://yapi.gpts.woa.com/project/1728/interface/api/112532
 */
export const useCommentStar = createMutation<CommentStarRequestParams, { status: 0 | 1 }>({
  method: "post",
  url: API_SS_UGC_COMMENT_STAR,
});

/**
 * @description 获取动态评论列表
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112528
 */
export const useGetCommentsByPost = createQuery<
  GetPostCommentsParams,
  { list: GetPostCommentsResponseItem[]; page_info: PageInfo }
>({
  method: "post",
  url: API_SS_UGC_GET_POST_COMMENTS,
});

/**
 * @description 获取动态评论列表V2
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/129756
 */
export const useGetCommentsByPostV2 = createQuery<
  Omit<GetPostCommentsParams, "comment_reply_limit">,
  { list: GetPostCommentsResponseItem[]; page_info: PageInfo }
>({
  method: "post",
  url: API_SS_UGC_GET_POST_COMMENTS_V2,
});

/**
 * @description 批量获取评论回复
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/129758
 */
export const useBatchGetPostCommentReplies = createQuery<
  {
    comment_uuids: string[];
    limit: number;
  },
  BatchGetPostCommentRepliesResponse
>({
  method: "post",
  url: API_SS_UGC_BATCH_GET_POST_COMMENT_REPLIES,
});

/**
 * 获取我的评论列表
 * https://yapi.gpts.woa.com/project/1728/interface/api/112528
 */
export const useGetMyComments = createQuery<
  {},
  {
    list: GetMyCommentItem[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_GET_USER_COMMENT_LIST,
});

/**
 * 获取动态回复列表
 * https://yapi.gpts.woa.com/project/1728/interface/api/112530
 */
export const useGetCommentReplies = createQuery<
  GetPostCommentRepliesParams,
  { list: GetPostCommentRepliesResponseItem[]; page_info: PageInfo }
>({
  method: "post",
  url: API_SS_UGC_GET_POST_COMMENT_REPLIES,
});

/**
 * @description 删除评论/回复
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112536
 */
export const useDeleteComment = createMutation<
  { comment_uuid: string; del_reason?: PostDeleteReason },
  { status: string }
>({
  method: "post",
  url: API_SS_UGC_DELETE_POST_COMMENT,
});

/**
 * @description 发布评论/回复
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112534
 */
export const usePostComment = createMutation<
  ComposeCommentParams,
  { comment: GetPostCommentsResponseItem }
>({
  method: "post",
  url: API_SS_UGC_POST_COMMENT,
});
