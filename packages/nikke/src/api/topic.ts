/**
 * @description 主题相关接口
 */

import {
  API_SS_UGC_DYNAMICS_GET_ACTIVITY_POST_TAGS,
  API_SS_UGC_GET_TAG,
  API_SS_UGC_SEARCH_TAG,
} from "packages/configs/api.ts";
import { createQuery } from "./index";
import { Tag } from "packages/types/home";
import { PageInfo, PageParam } from "packages/types/common";

// 获取单个话题
export const getTag = createQuery<{ id: string }, Tag>({
  method: "post",
  url: API_SS_UGC_GET_TAG,
});

/*
 * 搜索话题
 * https://yapi.gpts.woa.com/project/1728/interface/api/118614
 */
export const useSearchTopic = createQuery<
  {
    tag_name: string;
  } & PageParam,
  {
    list: Tag[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_SEARCH_TAG,
});

export interface ActivityPostTag {
  /** 话题英文唯一标识 */
  tag_code: "creatorhub_post" | "guild_post";
  /** 话题id */
  tag_id: number;
}

export interface ActivityPostTagsResponse {
  /** 话题列表 */
  tags: ActivityPostTag[];
}

/**
 * 获取活动话题列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126186
 */
export const useGetActivityPostTags = createQuery<{}, ActivityPostTagsResponse>({
  method: "get",
  url: API_SS_UGC_DYNAMICS_GET_ACTIVITY_POST_TAGS,
});
