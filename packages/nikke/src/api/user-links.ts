import {
  API_SS_UGC_USER_GET_USER_LINKS,
  API_SS_UGC_USER_SET_USER_LINKS,
} from "packages/configs/api";
import { createMutation, createQuery } from ".";

export type ChannelLink = {
  channel_name: string;
  url: string;
  icon: string;
};
/**
 * 拉取 第三方渠道主页配置信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112612
 *
 * @deprecated 改为使用用户信息中的json字段
 */
export const useGetLinks = createQuery<{}, { links: ChannelLink[] }>({
  method: "post",
  url: API_SS_UGC_USER_GET_USER_LINKS,
});

/**
 * 设置
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112614
 */
export const useModifyLink = createMutation<{ links: Omit<ChannelLink, "icon">[] }, any>({
  method: "post",
  url: API_SS_UGC_USER_SET_USER_LINKS,
});
