import { AxiosInstance, AxiosRequestConfig } from "axios";
import {
  useQuery,
  UseQueryOptions,
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/vue-query";
import { computed, Ref, unref } from "vue";
import { http } from "./axios";
import { omit } from "lodash-es";
// import { useToast } from "@/components/ui/toast";
// import { t } from "@/locales";

export type IgnoreToast =
  | boolean
  | ((data: { code: number; msg: string }, params: Record<string, any>) => boolean);

export interface CreateQueryConfig {
  axios?: AxiosInstance;
  /** 不展示错误提示 */
  ignore_toast?: IgnoreToast;
  /** 指定缓存垃圾回收时间 */
  gcTime?: number;
  /** 指定缓存过期时间，有效期内不会自动刷新 */
  staleTime?: number;
  /** 任何报错都避免跳到 404 */
  fe_ignore_not_found_jump?: boolean;
  /** 返回码不为成功依然返回结果, 不抛错; 用途示例: 隐私设置 */
  use_non_zero_ret?: boolean;
}

export interface CreateMutationConfig {
  axios?: AxiosInstance;
  /** 不展示错误提示 */
  ignore_toast?: IgnoreToast;
  /** 自动刷新指定的查询 */
  refetch?: CreateQueryResultLike[] | (() => CreateQueryResultLike[]);
  /** 任何报错都避免跳到 404 */
  fe_ignore_not_found_jump?: boolean;
}

export type ApiRunOptions = {
  /** 不展示错误提示, 默认展示 */
  ignore_toast?: IgnoreToast;
  /** 任何报错都避免跳到 404 */
  fe_ignore_not_found_jump?: boolean;
};

export type ApiDebounceRunOptions = ApiRunOptions & {
  /** 是否启用接口防抖，默认开启 */
  debounce?: boolean;
};

export type CreateQueryResultLike = { run: Function; symbol: Symbol };

/** 定义查询类接口 */
export function createQuery<T extends Record<string, any>, R>(
  params: AxiosRequestConfig<any>,
  config?: CreateQueryConfig & { default_params?: Partial<T> },
) {
  const run = (data: T, options?: ApiRunOptions) => {
    const request_params = {
      ...params,
      ["use_non_zero_ret"]: config?.use_non_zero_ret,
      ["ignore_toast" as any]: options?.ignore_toast || config?.ignore_toast || undefined,
      ["fe_ignore_not_found_jump" as any]:
        options?.fe_ignore_not_found_jump || config?.fe_ignore_not_found_jump || undefined,
    };

    Object.assign(request_params, params.method === "get" ? { params: data } : { data });

    return (config?.axios ?? http).request(request_params) as Promise<R>;
  };

  // const debounceRun = debounceMutation(run, {
  //   duration: 500,
  //   name: params.url!,
  //   default_debounce: false,
  // });

  const symbol = Symbol();
  const useQueryItem = <Options extends Partial<UseQueryOptions<any, any, any, any, any>>>(
    data: T | Ref<T>,
    options?: Options,
  ) => {
    const keys = computed(() => JSON.stringify(unref(data)));

    return useQuery({
      queryKey: [symbol, params.url, params.method, keys],
      queryFn: (_ctx) => run({ ...config?.default_params, ...unref(data) }),
      ...options,
      staleTime:
        (options && "value" in options ? options.value?.staleTime : null) ??
        config?.staleTime ??
        undefined,
      gcTime:
        (options && "value" in options ? options.value?.gcTime : null) ??
        config?.gcTime ??
        undefined,
    });
  };
  // 将原始方法挂载到 useMutationItem 上，方便自定义场景使用
  useQueryItem.run = run;
  // 暴露唯一键，便于自动刷新
  useQueryItem.symbol = symbol;
  return useQueryItem;
}

/**
 * 定义变更类接口
 * - 接口调用默认开启防抖
 */
export function createMutation<T extends Record<string, any> | FormData, R>(
  params: AxiosRequestConfig<any>,
  config?: CreateMutationConfig & { default_params?: Partial<T> },
) {
  const run = (data: T, options?: ApiRunOptions) => {
    return (config?.axios ?? http).request({
      ...params,
      data: { ...config?.default_params, ...omit(data ?? {}, ["_headers"]) },
      headers: { ...params.headers, ...((data as any)?._headers ?? {}) },
      ["ignore_toast" as any]: options?.ignore_toast || config?.ignore_toast || undefined,
      ["fe_ignore_not_found_jump" as any]:
        options?.fe_ignore_not_found_jump || config?.fe_ignore_not_found_jump || undefined,
    }) as Promise<R>;
  };

  const debounceRun = debounceMutation(run, {
    duration: 500,
    name: params.url!,
    default_debounce: true,
  });

  const useMutationItem = <Options extends UseMutationOptions<any, any, any, any>>(
    options?: Options,
  ) => {
    const queryClient = useQueryClient();
    return useMutation({
      mutationFn: debounceRun as (data: T) => Promise<R>,
      ...options,
      onSuccess: (...args) => {
        (options as any)?.onSuccess?.(...args);
        // 自动刷新指定的查询
        const refetch = typeof config?.refetch === "function" ? config?.refetch() : config?.refetch;
        refetch?.forEach((item) => queryClient.invalidateQueries({ queryKey: [item.symbol] }));
      },
    });
  };
  // 将原始方法挂载到 useMutationItem 上，方便自定义场景使用
  useMutationItem.run = debounceRun;
  return useMutationItem;
}

// TODO: 分页类接口、无限滚动类接口，暂未封装，可以使用 TanStack Query 提供的方法自行实现

/**
 * 防抖查询，避免频繁请求
 * - 频繁的请求，会复用上一次的请求结果
 */
const debounceQuery = function <T extends (data: any, options?: ApiRunOptions) => any>(
  fn: T,
  options: { name: string; duration: number; default_debounce?: boolean },
) {
  const map = new Map<
    string,
    {
      start: number;
      end?: number;
      data?: any;
      loading: boolean;
      callbacks: { resolve: Function; reject: Function }[];
    }
  >();
  return async (data: Parameters<T>[0], setting?: ApiDebounceRunOptions) => {
    if (!(setting?.debounce ?? options.default_debounce)) {
      return fn(data, setting);
    }
    const { duration, name } = options;
    const key = JSON.stringify(data);
    const last = map.get(key);
    if (last) {
      if (last.loading) {
        console.warn(`debounce pending query: ${name}`);
        return new Promise((resolve, reject) => {
          last.callbacks.push({ resolve, reject });
        });
      } else {
        console.warn(`debounce query: ${name}`);
        return last.data;
      }
    }

    map.set(key, { loading: true, callbacks: [], start: Date.now() });
    try {
      const res = await fn(data, setting);
      const last = map.get(key)!;
      last.data = res;
      last.loading = false;
      last.callbacks.forEach((cb) => cb.resolve(res));
      setTimeout(() => map.delete(key), duration);
      return res;
    } catch (error) {
      const last = map.get(key)!;
      last.callbacks.forEach((cb) => cb.reject(error));
      map.delete(key);
      throw error;
    }
  };
};

/**
 * 防抖变更，避免频繁请求
 * - 频繁的请求，会拦截报错
 */
function debounceMutation<T extends (data: any, options?: Record<string, any>) => any>(
  fn: T,
  options: { name: string; duration: number; default_debounce?: boolean },
) {
  const { duration, name } = options;
  const map = new Map<string, { start: number; end?: number }>();
  return async (data: Parameters<T>[0], setting?: ApiDebounceRunOptions) => {
    if (!(setting?.debounce ?? options.default_debounce)) {
      return fn(data, setting) as ReturnType<T>;
    }
    const key = JSON.stringify(data);
    const last = map.get(key);
    if (last) {
      // const { show } = useToast();
      // show({ text: t("action_frequency_tips"), type: "error" });
      throw new Error(`frequency mutation: ${name}`);
    }
    map.set(key, { start: Date.now() });
    try {
      const res: ReturnType<T> = await fn(data, setting);
      map.set(key, { start: map.get(key)!?.start, end: Date.now() });
      window.setTimeout(() => map.delete(key), duration);
      return res;
    } catch (e) {
      map.delete(key);
      throw e;
    }
  };
}
