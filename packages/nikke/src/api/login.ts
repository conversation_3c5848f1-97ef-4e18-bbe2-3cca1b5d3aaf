// configs
import {
  API_USER_LOGIN,
  API_USER_LOGINOUT,
  API_USER_CHECK_LOGIN,
  API_USER_CHECK_HAS_LIP_ACCOUNT,
  // API_USER_GET_INFO,
} from "packages/configs/api";

// types
import { LoginApiParams } from "packages/types/login";

// utils
import { createMutation, createQuery } from ".";

/**
 * @description 登录
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109612
 */
export const useApiLogin = createMutation<LoginApiParams, { code: number }>({
  method: "post",
  url: API_USER_LOGIN,
});

/**
 * @description 退出登录
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109614
 */
export const useApiLogout = createMutation<{}, {}>({
  method: "post",
  url: API_USER_LOGINOUT,
});

/**
 * @description 检查登录态
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/109616
 */
export const useApiCheckLogin = createQuery<{}, {}>(
  { method: "post", url: API_USER_CHECK_LOGIN },
  { ignore_toast: true },
);

/**
 * @description 检查是否有绑定 lip 账号
 */
export const useCheckHasLipAccount = createQuery<{}, { has_lip_account: boolean }>(
  { method: "post", url: API_USER_CHECK_HAS_LIP_ACCOUNT },
  { ignore_toast: true },
);
