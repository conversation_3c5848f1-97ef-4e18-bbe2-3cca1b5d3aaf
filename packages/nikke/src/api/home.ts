/**
 * @description 首页相关接口
 */

import {
  API_SS_UGC_GET_PLATE_LIST,
  API_SS_UGC_GET_TAG_LIST,
  API_SS_UGC_GET_DISTRICT_LIST,
} from "packages/configs/api.ts";
import { createQuery } from "./index";
import { DistrictParams, District, Plate, Tag, TagParams } from "packages/types/home";
import { PageParam, PageInfo } from "packages/types/common";

// 板块接口
export const getPlates = createQuery<PageParam, { list: Plate[]; page_info: PageInfo }>({
  method: "post",
  url: API_SS_UGC_GET_PLATE_LIST,
});

/**
 * @description 获取标签列表
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112588
 */
export const getTags = createQuery<TagParams, { list: Tag[]; page_info: PageInfo }>({
  method: "post",
  url: API_SS_UGC_GET_TAG_LIST,
});

// 获取金刚区配置
export const getDistrictList = createQuery<
  DistrictParams,
  { list: District[]; page_info: PageInfo }
>({
  method: "post",
  url: API_SS_UGC_GET_DISTRICT_LIST,
});

// CreatorHub banner TODO
export const getCreatorHubBanner = () => {};

// 活动下拉筛选配置 TODO
export const creatorHubEvents = () => {};
