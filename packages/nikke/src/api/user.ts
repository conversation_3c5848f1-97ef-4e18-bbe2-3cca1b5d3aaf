import { createMutation, createQuery } from ".";
import { <PERSON><PERSON>ara<PERSON>, PageInfo } from "packages/types/common";
import { Follower, UnReadMessageCounts, ReadMessageAllType } from "@/types/user";
import { UserInfo, UserParentCertificateStatus, UserGameAdultStatus } from "packages/types/user";
import { BasicUserAuthInfo } from "packages/types/intl";
import {
  API_SS_UGC_USER_GET_MESSAGE,
  API_SS_UGC_USER_GET_USER_FOLLOW,
  API_SS_UGC_USER_GET_USER_INFO_NEW,
  API_SS_UGC_USER_GET_INTL_GAME_USER_STATUS,
  API_SS_UGC_USER_GET_USER_PROFILE,
  API_SS_UGC_USER_MODIFY_INFO,
  API_SS_UGC_USER_READ_MESSAGE_ALL,
  API_SS_UGC_USER_UNREAD_MESSAGE,
  API_SS_UGC_USER_AVATARS,
  API_SS_UGC_USER_FOLLOW,
  API_USER_GET_LOGIN_INFO,
  API_SS_UGC_USER_GET_USER_FANS,
  API_SS_UGC_USER_SIGN_PRIVACY_PROTOCOL,
  API_USER_GET_AVATAR_PENDANT_LIST,
  API_USER_SET_USER_AVATAR_PENDANT,
  API_SS_UGC_USER_SEARCH_USER,
  API_SS_UGC_USER_SET_BLACK_USER,
  API_SS_UGC_USER_GET_BLACK_USER_LIST,
  API_SS_UGC_USER_SET_USER_REGIONS,
} from "packages/configs/api";

/**
 * @description intlgame 用户信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/126002
 */
export const useGetIntlGameUserStatus = createQuery<
  {},
  {
    adult_check_status: UserGameAdultStatus;
    parent_certificate_status: UserParentCertificateStatus;
  }
>({
  method: "post",
  url: API_SS_UGC_USER_GET_INTL_GAME_USER_STATUS,
});

/**
 * 用户签署隐私协议
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/112792
 */
export const useSignPrivacyProtocol = createQuery<{}, {}>({
  method: "post",
  url: API_SS_UGC_USER_SIGN_PRIVACY_PROTOCOL,
});

/**
 * 获取用户关注列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110972
 */
export const useGetUserFollow = createQuery<
  PageParam & { intl_openid: string },
  {
    list: Follower[];
    page_info: PageInfo;
  }
>({ method: "post", url: API_SS_UGC_USER_GET_USER_FOLLOW });

/**
 * 获取用户粉丝列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110974
 */
export const useGetUserFans = createQuery<
  PageParam & { intl_openid: string },
  {
    list: Follower[];
    page_info: PageInfo;
  }
>({ method: "post", url: API_SS_UGC_USER_GET_USER_FANS });

/**
 * 查询用户信息(别人和自己的都可以查)
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110976
 */
export const useGetUserProfile = createQuery<{ intl_openid?: string }, { info: UserInfo }>(
  { method: "post", url: API_SS_UGC_USER_GET_USER_PROFILE },
  { staleTime: 60 * 1000 },
);

/**
 * 查询主态信息（只能查自己的）
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110998
 */
export const useGetMyProfile = createQuery<
  {},
  {
    info: UserInfo;
  }
>({ method: "post", url: API_SS_UGC_USER_GET_USER_INFO_NEW });

/**
 * 修改用户信息
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111000
 */
export const useModifyInfo = createMutation<
  {
    /** 用户昵称 */
    username?: string;
    /** 头像 */
    avatar?: string;
    /** 签名 */
    remark?: string;
  },
  {}
>({ method: "post", url: API_SS_UGC_USER_MODIFY_INFO }, { refetch: [useGetUserProfile] });

/**
 * 获取消息列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111002
 */
export const useGetMessage = createQuery<
  {},
  {
    list: {
      /** 游戏大区 */
      AreaId: string;
      /** 信息摘要 */
      Brief: string;
      /** cms配置的消息id */
      CmsMsgID: string;
      Content: string;
      /** 创建时间 */
      CreatedOn: string;
      ExtInfo: string;
      /** 游戏id */
      GameId: string;
      /** 是否已读0-未读1-已读 */
      IsRead: number;
      /** 更新时间 */
      ModifiedOn: string;
      /** 关联的动态id */
      PostID: string;
      ReceiverUserID: string;
      /** 接收者用户openid */
      ReceiverUserIntlOpenid: string;
      /** 发送者openid */
      SenderUserIntlOpenid: string;
      /** 消息类型，详见messageEnum */
      Type: number;
      /** 消息id */
      id: string;
      /** 发送者用户信息 */
      send_user_info: {
        all_post_num: string;
        avatar: string;
        fans_num: string;
        follow_num: string;
        home_page_url: string;
        id: string;
        intl_openid: string;
        is_admin: boolean;
        language: string;
        post_num: string;
        remark: string;
        status: number;
        titles: null;
        username: string;
      };
      sender_user_id: string;
    }[];
    /** 总数 */
    total_rows: string;
  }
>({ method: "post", url: API_SS_UGC_USER_GET_MESSAGE });

/**
 * 未读消息全部已读
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111004
 */
export const useReadMessageAll = createMutation<{ type: ReadMessageAllType }, {}>(
  { method: "post", url: API_SS_UGC_USER_READ_MESSAGE_ALL },
  { refetch: [] },
);

/**
 * 消息未读数
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/111006
 */
export const useUnReadMessage = createMutation<{}, UnReadMessageCounts>(
  { method: "post", url: API_SS_UGC_USER_UNREAD_MESSAGE },
  { refetch: [] },
);

/**
 * 获取用户头像列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/110998
 */
export const useUserAvatars = createQuery<
  {},
  {
    /** 通用头像列表 */
    currency: string[];
    /** 绑定游戏列表 */
    game_avatar: string[];
  }
>({ method: "post", url: API_SS_UGC_USER_AVATARS });

export const useQueryUserLoginInfo = createQuery<{}, BasicUserAuthInfo>(
  {
    method: "post",
    url: API_USER_GET_LOGIN_INFO,
  },
  { ignore_toast: true },
);

/**
 * @description 关注用户
 * @link https://yapi.gpts.woa.com/project/1728/interface/api/112520
 */
export const useFollowUser = createMutation<
  { intl_openid: string },
  { is_follow: boolean; is_mutual_follow: boolean }
>({ method: "post", url: API_SS_UGC_USER_FOLLOW }, { refetch: [useGetUserProfile] });

/**
 * 获取用户头像挂件列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/116900
 */
export const useGetUserAvatarPendantList = createQuery<
  {
    limit: number;
    next_page_cursor?: string;
  },
  {
    userAvatarPendants: {
      id: string;
      /** 挂件地址 */
      avatar_pendant: string;
      title: string;
      /** 条件 */
      condition: string;
      /** 跳转链接 */
      jump_url: string;
      /** 是否穿戴 */
      is_weared: number;
      /** 开始时间 */
      valid_begin_at: number;
      /** 有效截止时间 */
      valid_end_at: number;
      /** 是否拥有 */
      is_owned: number;
      /** 0 非永久 1永久 */
      is_permanent: 0;
    }[];
    page_info: PageInfo;
  }
>({ method: "post", url: API_USER_GET_AVATAR_PENDANT_LIST });

/**
 * 设置头像挂件
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/116902
 */
export const useSetUserAvatarPendant = createMutation<
  {
    /** 挂件id */
    avatar_pendant_id: number;
    /** 0: 取消穿戴；1: 穿戴 */
    set_wear_status: 0 | 1;
  },
  {}
>(
  { method: "post", url: API_USER_SET_USER_AVATAR_PENDANT },
  { refetch: [useGetUserProfile, useGetMyProfile] },
);

/**
 * 搜索用户
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/118612
 */
export const useSearchUser = createQuery<
  {
    user_name: string;
  } & PageParam,
  {
    list: UserInfo[];
    page_info: PageInfo;
  }
>({
  method: "post",
  url: API_SS_UGC_USER_SEARCH_USER,
});

/**
 * 设置拉黑/取消拉黑用户
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/118614
 */
export const useSetBlackUser = createMutation<
  {
    to_intl_openid: string;
    /** 0: 拉黑；1: 取消拉黑 */
    operate_type: 0 | 1;
  },
  {}
>({ method: "post", url: API_SS_UGC_USER_SET_BLACK_USER });

/**
 * 获取拉黑用户列表
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/118616
 */
export const useGetBlackUserList = createQuery<
  {
    limit: number;
    next_page_cursor?: string;
  },
  { list: { user_info: UserInfo; id: number; blacking_on: string }[]; page_info: PageInfo }
>({ method: "post", url: API_SS_UGC_USER_GET_BLACK_USER_LIST });

/**
 * 设置用户选择的区域
 * @see https://yapi.gpts.woa.com/project/1728/interface/api/129754
 */
export const useSetUserRegions = createMutation<{ regions: string[] }, {}>({
  method: "post",
  url: API_SS_UGC_USER_SET_USER_REGIONS,
});
