<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">

  <meta property="og:type" content="website" />
  <meta property="og:title" content="Blablalink" />
  <meta property="og:description" content="Nikke blabla link" />
  <meta property="og:url" content="http://www.blablalink.com/" />
  <meta property="og:image" content="http://www.blablalink.com/assets/nikke/mask-icon.png" />

  <meta name="twitter:card" content="summary" />
  <meta name="twitter:title" content="Blablalink" />
  <meta name="twitter:description" content="Nikke blabla link" />

  <meta name="apple-mobile-web-app-title" content="Blablalink">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="color-scheme" content="light dark">

  <link rel="icon" href="/favicon.ico">
  <link rel="mask-icon" href="/mask-icon.png" />
  <link href="/apple-touch-icon.png" rel="apple-touch-icon" />
  <link href="/apple-touch-icon.png" rel="apple-touch-icon-precomposed">

  <!--preconnetlinks-->

  <title>Blablalink</title>

  <style>
    :root {
      --vh: 1vh;
    }
  </style>

  <script type="module">
    window.STANDALONE_SITE_VERSION = import.meta.env.VITE_VERSIOIN || "1.0.0";
    window.ERROR_OPERATION_INTERCEPTE_MESSAGE = "Exception operation was intercepted";
    window.APP_BUILD_TIME = +import.meta.env.VITE_APP_BUILD_TIME || undefined;

    window.setCSSRootVHVariable = () => {
      const vh = window.innerHeight / 100
      document.documentElement.style.setProperty('--vh', vh + 'px')
    }
    window.setCSSRootVHVariable()

    const isInArr = (arr, target) => {
      if (!arr || !arr.length || !target) {
        return
      }
      return arr.includes(target)
    }

    /* for cookie banner */
    function getLanguage(url) {
      const regex = /[?&](?:(?:lang|language|lang_type|sLanguage|s_language)|lang[^=]*)=([^&]+)/;
      const match = url.match(regex);

      if (match) {
        return match[1];
      }

      // 从 cookie 中获取名为 __ss_storage_cookie_cache_lang__ 的语言设置
      const cookie_match = document.cookie.match(/(?:^|;\s*)__ss_storage_cookie_cache_lang__=([^;]*)/);

      if (cookie_match) {
        return cookie_match[1]; // 返回 cookie 中的语言值
      }

      return 'en';
    }
    const lang = getLanguage(location.href)
    document.documentElement.setAttribute("lang", lang);

    const rtl_langs = ['ar', '20'];
    document.documentElement.setAttribute('dir', isInArr(rtl_langs, lang) ? 'rtl' : 'ltr');
  </script>


  <!--cookiebanner-->
  <!--aegis-->

  <script type="module">
    const IS_DEV = import.meta.env.DEV

    const env = {
      test: "test",
      pre: "pre",
      prod: "production",
    }[import.meta.env.MODE] || "others"

    window.aegis = new Aegis({
      id: 'DvZ6rUEwoWdZO2RkO4',
      reportApiSpeed: true,
      reportAssetSpeed: true,
      version: window.STANDALONE_SITE_VERSION,
      env,
      spa: true,
      hostUrl: 'https://rumt-sg.com',
      api: {
        apiDetail: true,
      },
      beforeReport: (log) => {
        if (IS_DEV) {
          return false;
        }
        return log;
      },
      beforeRequest: function (data) {
        if (data.logType === 'log' && data.logs) {
          const msg = data.logs.msg
          const errorMsg = data.logs.errorMsg
          const level = data.logs.level

          if (
            msg.indexOf(window.ERROR_OPERATION_INTERCEPTE_MESSAGE) > -1 ||
            msg.indexOf('must be called with a user gesture') > -1
          ) {
            return false
          }

          // 这种情况 sdk 会设置 errorMsg 为 null，请求 api 的时候会报错
          if (typeof errorMsg === 'undefined' && ['16', '1024'].includes(level)) {
            data.logs.errorMsg = ''
          }
        }

        return data
      },
      onBeforeRequest: (log) => {
        if (IS_DEV) {
          return false;
        }
        return log;
      },
    });
  </script>
</head>

<body class="overflow-hidden">
  <div class="w-screen overflow-hidden h-screen" id="app"></div>
  <script type="module" src="./src/main.ts"></script>
</body>

</html>