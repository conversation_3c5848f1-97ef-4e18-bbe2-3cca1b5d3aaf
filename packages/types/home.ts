import { PageParam } from "./common";

// 板块配置
export interface Plate {
  area_id: string; // 大区id
  game_id: string; // 游戏id
  id: number; // 板块id
  language_data: string; // 置顶多语言数据
  order: number; // 顺序值
  plate_name: string; // 板块名称---跟随cookie中语言返回
  label: string; // 展示的板块名称
  status: number; // 状态：1、不可见，2、可见
  type: string; // feed流类型：1 双列瀑布流，2 单列卡片流
  unique_identifier: string; // 板块唯一标识
}

export interface PlateLangItem {
  language: string;
  content_msgs: { url: string; order: number; talk_name: string }[];
}

// 公告
export interface Notice {
  content: string;
  link: string;
  new: boolean;
}

// 获取金刚区配置参会
export interface DistrictParams extends PageParam {
  plate_id: number;
}

// 金刚区配置
export interface District {
  id: number;
  icon: string; // 图片链接
  jump_url: string; // 跳转链接
  order: string; // 顺序值
  tool_name: string; // 工具名
  ext_info?: string;
}

// tag
export interface Tag {
  id: string; // 主键id
  tag_name: string; // 主题名称
  area_id?: string; // 大区id
  comment_num?: string; // 评论数
  fans_num?: string; // 粉丝数
  game_id?: string; // 游戏id
  hot_num?: string; // 热度
  kudos_num?: string; // 点赞量
  pic_url?: string; // icon
  power_num?: string; // 权重
  quote_num?: string; // 引用数
  read_num?: string; // 阅读数
  post_num?: string; // 帖子总数
  recommend_deadline?: string; // 推荐截止时间(0:永久,其他按时间戳推断状态)
  type?: number; /// 话题类型：1 活动话题，2 普通话题
}

export interface TagParams extends PageParam {
  plate_id?: number;
}
