/**
 * @description nikke 游戏内数据格式
 * @see {link https://git.woa.com/iegg_distribution/officialwebsite/nikke-data-uploader}
 */

export type UserRoleInfo = {
  game_id: string;
  area_id: number;
  zone_id: number;
  plat_id: number;
  role_id: string;
  role_name: string;
  game_name: string;
};
export interface RoleInfoReq {
  area_id: number;
  token: string;
  req_common: {
    intl_game_id: string;
    version?: string;
    channel_id: number;
    os?: number;
  };
}

export type UserGameInfo = {
  has_saved_role_info: boolean;
  tag_id: string;
  player_level: number;
  role_info: RoleInfo;
  player_base_info: UserBasicInfo;
};

export type EquipFunctions = {
  equip_id: number;
  equip_modify_effect: string;
  equip_effects: {
    id: number;
    state_effect_group_id: number;
    description_localkey: string;
    equipment_option_id: string;
    function_details: {
      buff: "BuffEtc";
      buff_icon?: string;
      duration_type: "Battles";
      duration_value: number;
      function_standard: string;
      function_target: "Self";
      function_battlepower: number;
      function_type: "IncElementDmg";
      function_value: number;
      function_value_type: "Percent" | "Integer";
      id: number;
      level: number;
      name_localvalues: Record<string, string>;
    }[];
  }[];
};

// 用户基本信息--首页展示
export type UserBasicInfo = {
  player_level: number;
  player_activity: number;
  player_nikkes: CharacterData[]; // @NOTE: this is deprecated.
  outpost_defense: number; // 当前基地防御积累奖励(百分比数据)
  remain_interception: number; // 剩余特殊拦截次数
  used_rookie_arena: number;
  used_special_arena: number;
  remain_rookie_arena: number; // 剩余新人竞技场次数
  remain_special_arena: number; // 剩余特殊竞技场次数
  avatar_frame: number; // 拥有头像框数量
  material_detail: {
    gem: number; // 珠宝(钻石)数量
    credit: number; // 信用点
    battle_data_set: number; // 战斗数据
    core_dust: number; // 芯尘(红球)
    recruit_voucher: number; // 招募券(普池票)
    advanced_recruit_voucher: number; // 高级招募券(UP票)
  };
  gold_mileage_ticket: number; // 黄金积分券(金票)
  silver_mileage_ticket: number; // 白银积分券(黑票)
  own_nikke_cnt: number; // 拥有尼姬总数
};

// 用户Nikke数据
export type Nikke = {
  resource_id: number;
  name_code: number;
  level: number;
  skill1_level: number;
  skill2_level: number;
  skill_burst_level: number;
  player_equips: NikkeEquip[];
  attractive_level: number; // 好感度
  limit_break: number; // 星际|突破
  grade_core_id: number;
  power: number; // 战力
  costume_id: number; // 皮肤
  cube_id: number; // 魔方id
  cube_level: number; // 魔方等级
  cube_stars: number; // 魔方星级(ssr)
  cube_type_id: number; // 魔方typeid
  item_id: number; // 藏品
  item_level: number; // 藏品 等级
  item_rare: number; // 藏品系有度(ssr)
};

export type CubeItem = {
  harmonycube_skill_group: Skill[];
} & CubeMapItem &
  OriginCubeData;

export type OriginCubeData = {
  id: number;
  name_localkey: string;
  description_localkey: string;
  location_id: number;
  location_localkey: string;
  order: number;
  resource_id: number;
  bg: string;
  bg_color: string;
  item_type: string;
  item_sub_type: string;
  item_rare: RarityType;
  class: string;
  level_enhance_id: number;
  harmonycube_skill_group: {
    skill_group_id: number;
  }[];
};

export type CubeMapItem = {
  id: number;
  atk: number[];
  hp: number[];
  def: number[];
  level1: number[];
  level2: number[];
  level3: number[];
  powers: number[];
};

export type CubeLevelData = {
  id: number;
  level: number;
  skill_levels: {
    skill_level: number;
  }[];
  level_enhance_id: number;
  harmonycube_stats: {
    stat_rate: number;
    stat_type: "Atk" | "Defence" | "Hp";
  }[];
};

export type FavoriteItem = {
  name_code?: number;
  collection_skill_group_data: BatchSkillInfo[];
  favoriteitem_skill_group_data: {
    info: BatchSkillInfo;
    skill_change_slot: number;
  }[];
} & FavouriteMapItem &
  OriginFavouriteData;

type OriginFavouriteData = {
  id: number;
  slot: number;
  level_enhance_id: number;
  name_localkey: string;
  description_localkey: string;

  prop_resource_id: string;
  img_resource_id: string;
  icon_resource_id: string;

  favorite_rare: string;
  favorite_type: string;
  weapon_type: string;
  max_level: number;

  favoriteitem_skill_group_data: {
    favorite_skill_id: number;
    skill_table: string;
    skill_change_slot: number;
  }[];
  collection_skill_group_data: {
    collection_skill_id: number;
  }[];
};

export type FavouriteMapItem = {
  id: number;
  atk: number[];
  grade: number[];
  hp: number[];
  def: number[];
  level1: number[];
  level2: number[];
  powers: number[];
};

// 用户Nikke装备数据
export type NikkeEquip = {
  equip_id: number;
  equip_manufacturer_bonus: number;
  equip_tier: number;
  equip_level: number;
  equip_modify_effects: string;
};

// 用户通关数据
export type UserBattleInfo = {
  normal_progress: number; // 普通战役进度
  hard_progress: number; // 困难战役进度
  tower_floor: number; // 塔层
  last_login_time: string; // 最后一次登录秒级时间戳
  costume: number; // 时装数量
  outpost_detail: {
    recyle_level: number; // 循环室研究等级
    elysion_level: number; // 企业【极乐净土】等级
    tetra_level: number; // 企业【泰特拉】等级
    missills_level: number; // missills_level 错误拼写
    missilis_level: number; // 正确拼写
    pilgrim_level: number; // 企业【朝圣者】等级
    abnormal_level: number; // 企业【反常】等级
    attacker_level: number; // 类型【火力型】等级
    defender_level: number; // 类型【防御型】等级
    supporter_level: number; // 类型【辅助型】等级
    sychro_level: number; // 同步等级
  };
};

// 查询领奖
export interface queryRewardRes {
  code: number;
  data: {
    user_has_present_list?: string[];
  };
}

// 游戏角色信息
export interface RoleInfo {
  game_id: string;
  area_id: number | string;
  zone_id: number;
  plat_id: number;
  role_id: string;
  role_name: string;
  game_name: string;
}

/**
 * PLAYER END
 */

/**
 * ASSETS
 */
// https://nikke-goddess-of-victory-international.fandom.com/wiki/
// https://nikke.gg/characters/

// 武器
export enum WeaponType {
  ASSAULT_RIFLE = "assault_rifle", // 步枪
  SUB_MACHINE_GUN = "sub_machine_gun", // 冲锋枪
  SHOT_GUN = "shot_gun", // 霰弹枪
  SNIPER_RIFLE = "sniper_rifle", // 狙击步枪
  ROCKET_LAUNCHER = "rocket_launcher", // 发射器
  MACHINE_GUN = "machine_gun", // 机枪

  // breaking change: 2024-09
  SUB_MACHINE_GUN_NEW = "SMG",
  ROCKET_LAUNCHER_NEW = "RL",
  ASSAULT_RIFLE_NEW = "AR", // 步枪
  SHOT_GUN_NEW = "SG", // 霰弹枪
  SNIPER_RIFLE_NEW = "SR", // 狙击步枪
  MACHINE_GUN_NEW = "MG", // 机枪
}

export enum WeaponSlotItemSize {
  SMALL = "small",
  NORMAL = "normal",
  LARGE = "large",
}
export enum WeaponSlotItemMode {
  HEX = "hex", // 六边形样式
  CIRCLE = "circle", // 圆形样式
}

// 属性
export enum CodeType {
  FIRE = "character_type_fire", // Code H.S.T.A. (Fire)燃烧
  WATER = "character_type_water", // Code P.S.I.D. (Water)水冷
  IRON = "character_type_iron", // Code D.M.T.R. (Iron)铁甲
  WIND = "character_type_wind", // Code A.N.M.I. (Wind)风压
  ELECTRIC = "character_type_electric", // Code Z.E.U.S. (Electric)电击
}

// 职业
export enum JobType {
  ATTACK = "attacker", // 火力型
  DEFENCE = "defencer", // 防御型
  SUPPORT = "supporter", // 辅助型
}

// 阶段
export enum BurstType {
  FIRST = "1", // 一阶
  SECOND = "2", // 二阶
  THIRD = "3", // 三阶
  GENERAL = "p", // 通用
}

// 企业
export enum ManufacturerType {
  Tetra_Line = "Tetra_Line", // 泰特拉
  Missilis_Industry = "Missilis_Industry", // 米西利斯
  Elysion = "Elysion", // 极乐净土
  Pilgrim = "Pilgrim", // 朝圣者
  Abnormal = "Abnormal", // 反常
}

export enum CVLang {
  EN = "en",
  JA = "ja",
  KO = "ko",
}

export enum RarityType {
  EMPTY = "",
  R = "R",
  SR = "SR",
  SSR = "SSR",
}

export enum BurstField {
  Step1 = "Step1",
  Step2 = "Step2",
  Step3 = "Step3",
  AllStep = "AllStep",
}

export const BurstMap = {
  [BurstField.Step1]: BurstType.FIRST,
  [BurstField.Step2]: BurstType.SECOND,
  [BurstField.Step3]: BurstType.THIRD,
  [BurstField.AllStep]: BurstType.GENERAL,
};

export enum ElementField {
  Electronic = "Electronic",
  Fire = "Fire",
  Wind = "Wind",
  Water = "Water",
  Iron = "Iron",
}

export enum ClassField {
  Attacker = "Attacker",
  Defender = "Defender",
  Supporter = "Supporter",
}

export const ClassMap = {
  [ClassField.Attacker]: JobType.ATTACK,
  [ClassField.Supporter]: JobType.SUPPORT,
  [ClassField.Defender]: JobType.DEFENCE,
};

export enum CoperationField {
  ELYSION = "ELYSION", // 极乐净土
  MISSILIS = "MISSILIS", // 米西利斯
  TETRA = "TETRA", // 泰特拉
  PILGRIM = "PILGRIM", // 朝圣者
  ABNORMAL = "ABNORMAL", // 反常
}

export const CoperationMap = {
  [CoperationField.ELYSION]: ManufacturerType.Elysion,
  [CoperationField.MISSILIS]: ManufacturerType.Missilis_Industry,
  [CoperationField.ABNORMAL]: ManufacturerType.Abnormal,
  [CoperationField.PILGRIM]: ManufacturerType.Pilgrim,
  [CoperationField.TETRA]: ManufacturerType.Tetra_Line,
};

export const RarityColorMap = {
  [RarityType.EMPTY]: "",
  [RarityType.R]: "blue",
  [RarityType.SR]: "purple",
  [RarityType.SSR]: "yellow",
};

/**
 * ASSETS END
 */

/**
 * CHARACTER
 */

export enum ViewModePose {
  STATIC = "STATIC",
  NORMAL = "NORMAL", // pic
  SHOOT = "SHOOT", // 2d spine
  HALF_FACE = "HALF_FACE",
  // SD = 'SD' // 背姿， sd
}

export type Speech = {
  id: string;
  group_id: string;
  speech_window: string;
  speaker: string;
  scenario_localkey: string;
  play_lipSync: boolean;
  characters: {}[];
  set_background: string;
  set_start_camera: string;
  set_end_camera: string;
  set_camera_duration: number;
  play_sound: string;
  play_bgm: string;
  effects: {}[];
  jump_target: string;
  selected?: boolean;
  speaker_detail?: {
    id: string;
    name_localkey: string;
    resource_id: number;
    skin_id: string;
    address: string;
    sd_resource_address: string;
  };
};

export type CharacterData = {
  id: number;
  resource_id: number;
  name_localkey: string;
  name_code: number;
  cv_localkey: string;
  cv_localkey_ja: string;
  cv_localkey_en: string;
  cv_localkey_ko: string;
  description_localkey: string;
  original_rare: string;
  squad: string;
  additional_skins?: string[];

  corporation: string;
  class: string;
  use_burst_skill: string;

  piece_detail: {
    name_localkey: string;
    description_localkey: string;
    resource_id: number;
    item_type: string;
    item_sub_type: string;
    item_rare: Rare;
    corporation: string;
    class: string;
    use_type: string;
    use_id: number;
    use_value: number;
    use_limit_count: boolean;
    stack_max: number;
  };

  squad_detail: {
    id: number;
    squad: string;
    squad_name: string;
    squad_description: string;
    resource_id: string;
  };
  element_details: {
    id: number;
    element: ElementField;
    group_id: number;
    weak_element_id: number;
    element_name_localekey: string;
    element_code_name_localekey: string;
    element_desc_localekey: string;
    element_icon: string;
  }[];
  character_costume_list: {
    id: number;
    costume_theme_group_id: number;
    resource_id: number;
    costume_index: number;
    costume_grade_id: string;
    costume_name_locale: string;
    costume_description_locale: string;
    is_hidden: boolean;
    is_unlokced: boolean; // tlog
  }[];

  shot_detail: {
    id: number;
    name_localkey: string;
    // ?? enum
    description_localkey: "Locale_Skill:character_shot_description_localkey_ar_down";
    camera_work: "camera_work_number1";
    weapon_type: WeaponType;
    attack_type: "Metal";
    counter_enermy: "Energy_Type";
    prefer_target: "TargetAR";
    prefer_target_condition: "None";
    shot_timing: "Concurrence";
    fire_type: "Instant";
    input_type: "DOWN";
    ShakeType: "Fire_AR";

    // end
    is_targeting: boolean;
    damage: number;
    shot_count: number;
    multi_target_count: number;
    center_shot_count: number;
    max_ammo: number;
    maintain_fire_stance: number;
    uptype_fire_timing: number;
    reload_time: number;
    reload_bullet: number;
    reload_start_ammo: number;
    rate_of_fire_reset_time: number;
    rate_of_fire: number;
    end_rate_of_fire: number;
    rate_of_fire_change_pershot: number;
    burst_energy_pershot: number;
    target_burst_energy_pershot: number;
    spot_first_delay: number;
    spot_last_delay: number;
    start_accuracy_circle_scale: number;
    end_accuracy_circle_scale: number;
    accuracy_change_pershot: number;
    accuracy_change_speed: number;
    auto_start_accuracy_circle_scale: number;
    auto_end_accuracy_circle_scale: number;
    auto_accuracy_change_pershot: number;
    auto_accuracy_change_speed: number;
    zoom_rate: number;
    multi_aim_range: number;
    spot_projectile_speed: number;
    charge_time: number;
    full_charge_damage: number;
    spot_radius_object: number;
    spot_radius: number;
    spot_explosion_range: number;
    core_damage_rate: number;
    penetration: number;
    use_function_id_list: number[];
    hurt_function_id_list: number[];
    shake_id: number;
    ShakeWeight: number;
  };

  ulti_skill_detail: Skill & UltiSkill;
  skill1_detail: Skill;
  skill2_detail: Skill;

  critical_ratio: number;
  critical_damage: number;

  stat_enhance_detail: {
    grade_ratio: number;
    grade_hp: number;
    grade_attack: number;
    grade_defence: number;
    grade_energy_resist: number;
    grade_metal_resist: number;
    grade_bio_resist: number;
    core_hp: number;
    core_attack: number;
    core_defence: number;
    core_energy_resist: number;
    core_metal_resist: number;
    core_bio_resist: number;
  };

  skill1_cost_detail: SkillCost[];
  skill2_cost_detail: SkillCost[];
  ulti_skill_cost_detail: SkillCost[];

  teammate_list: TeamMate[];

  dialog_group_list: {
    id: number;
    voice_type: "Character" | "Costume";
    resource_id: number;
    costume_index: number;
  }[];

  character_dialog_group_list: {
    id: number;
    speech_group_id: number;
    category_group: 1;
    order: number;
    is_teaser: boolean;
    voice_description: string;
    condition_attractive_level: number;
    speech_id: string;
    speech_localkey: string;
  }[];

  character_level_hp_list: number[];
  character_level_defence_list: number[];
  character_level_attack_list: number[];

  attractive_scenario_list: {
    id: number;
    name_code: number;
    attractive_level: number;
    scenario_title_loacle: string;
    attractive_scenario_group_id: string;
    reward_id: number;
    costume: number;
  }[];
};

export type TeamMate = {
  id: number;
  name_localkey: string;
  resource_id: number;
  name_code: number;
  original_rare: string;
};

export type SkillCost = {
  id: number;
  costs: {
    item_type: "Item" | "None";
    item_id: number;
    item_value: number;

    id: 7091001;
    name_localkey: string;
    description_localkey: string;
    resource_id: string;
    item_sub_type: string;
    item_rare: Rare;
    material_type: string;
    material_value: number;
    stack_max: number;
  }[];
};

export enum Rare {
  R = "R",
  SR = "SR",
  SSR = "SSR",
}

export type BatchSkillInfo = {
  id: number;
  group_id: number;
  skill_level: number;
  next_level_id: number;
  level_up_cost_id: number;
  icon: string;
  name_localkey: string;
  description_localkey: string;
  info_description_localkey: string;

  // example:
  // "description_value_list": [
  // {
  //   "description_value": [
  //     "8.65",
  //     "9.31",
  //     "9.98",
  //     "10.64",
  //     "11.31",
  //     "11.98",
  //     "12.64",
  //     "13.31",
  //     "13.97",
  //     "14.64"
  //   ]
  // },
  // ...
  //]
  description_value_list: {
    description_value?: string[];
  }[];
};

export type Skill = BatchSkillInfo & {
  // ulti skill has this
  skill_type?: string;
};

export type EquipData = {
  id: number;
  name_localkey: string;
  description_localkey: string;
  resource_id: string;
  item_type: "Equip";
  item_sub_type: "Module_A" | "Module_B" | "Module_C" | "Module_D";
  class: "All" | "Supporter" | "Attacker";
  item_rare: string; // T + n
  grade_core_id: number;
  grow_grade: number;
  stat: {
    stat_type: "None" | "Atk" | "Defence" | "Hp";
    stat_value: number;
  }[];
  option_slot: {
    option_slot: number;
    option_slot_success_ratio: number;
  }[];
  option_cost: number;
  option_change_cost: number;
  option_lock_cost: number;
};

export type LevelCost = {
  level: number;
  type: "CharacterLevel" | "SynchroLevel";
  need_synchro_lv_min: number;
  gold: number;
  character_exp: number;
  character_exp_2: number;
};

export type EquipOptions = {
  id: number;
  description_localkey: string;
  state_effect_group_id: number;
  state_effect_list: {
    state_effect_id: number;
  };
};

export type LevelInfo = {
  level: number;
  level_hp: number;
  level_attack: number;
  level_defence: number;
  // id: number;
  // group: number;
  // level_energy_resist: number;
  // level_metal_resist: number;
  // level_bio_resist: number;
};

export type UltiSkill = {
  id: number;
  skill_cooltime: number;
  skill_cooltime_list: number[];
  attack_type: string;
  counter_type: string;
  prefer_target: string;
  prefer_target_condition: string;
  skill_type: string;
  skill_value_data: {
    skill_value_type: "Percent" | "Integer" | "None";
    skill_value: number;
  }[];
  duration_type: string;
  duration_value: number;
  before_use_function_id_list: [number];
  before_hurt_function_id_list: [number];
  after_use_function_id_list: [number];
  after_hurt_function_id_list: [number];
  resource_name: string;
  icon: string;
  shake_id: number;
};

export type AniParams = {
  resource_id: number;
  skin_index?: number;
  action?: string;
  additional_skins?: string[];
};

/**
 * CHARACTER END
 */
export type NikkeListData = {
  id: number;
  resource_id: number;
  order: number;
  original_rare: RarityType;
  class: ClassField;
  use_burst_skill: BurstField;
  name_code: number;
  grade_core_id: number;
  corporation: CoperationField;
  name_localkey: {
    name: string;
  };
  element_id: {
    element: {
      element: ElementField;
    };
  };
  shot_id: {
    element: {
      weapon_type: WeaponType;
    };
  };
  costumes: {
    id: number;
    costume_index: number;
  }[];
  is_visible: boolean;
};
