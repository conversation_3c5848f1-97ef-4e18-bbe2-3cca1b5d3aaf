export interface ActionItem {
  value: string;
  label: string;
}

export interface ActionSheet {
  visible?: boolean;
  actions?: ActionItem[];
  active_id?: string | number;
  title?: string;
  text?: string;
  confirm_text?: string;
  cancel_text?: string;
  click_mask_close?: boolean;
}

export interface ActionSheetEmits {
  (e: "confirm"): void;
  (e: "cancel"): void;
  (e: "close"): void;
  (e: "change", v: ActionItem): void;
}
