export enum EmojisType {
  history = "history",
  list = "list",
}

export interface IconListItem {
  id: string;
  name: string;
  pic_url: string;
}

export interface ListItem {
  id: string;
  name: string;
  pic_url: string;
  icon_list: IconListItem[];
}

export interface GetAllEmoticonsResponse {
  list: ListItem[];
  page_info: {
    previous_page_cursor: string;
    next_page_cursor: string;
    is_finish: boolean;
  };
}
