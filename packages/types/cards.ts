export interface BaseMediaTextCardProps {
  src?: string[];
  is_video?: boolean;
  title?: string;
  time?: string;
  source?: string;
  desc?: string;
}

export interface LeftMediaRightTextCardProps extends BaseMediaTextCardProps {
  left_style?: any;
}

export interface TopMediaBottomTextCardProps extends BaseMediaTextCardProps {}

export interface IntegratedMediaTextCardProps extends BaseMediaTextCardProps {
  has_pagination?: boolean;
}

export interface RecommendedVideoCardProps extends BaseMediaTextCardProps {
  avatar?: string;
  label?: {
    label_id: string;
    label_name: string;
  };
}

export interface PanelCardProps {
  title?: string;
}
