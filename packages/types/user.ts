import { PageParams } from "./common";
import { ComposeItem, ComposeReplyReponse, MessageType } from "./content";
import { ComposeCommentResponse } from "./comments";
import { PopShellProps } from "./pop";

export enum UserMediaTab {
  post = "post",
  follower = "follower",
  following = "following",
}

export enum UserLoginStatus {
  unlogin = "login",
  logined = "logined",
  token_expired = "token_expired",
}

export enum UserContentTab {
  post = "post",
  comment = "comment",
  collection = "collection",
}

export enum UserFollowTabType {
  follower = "follower",
  following = "following",
}

export interface RegisterIntlRequestParams {
  avatar: string; // 头像路径
  username: string; // 用户名称
  openid: string; // intl openid
  verification_code: string; // 131通道 自建号需要邮箱
  email: string; // 邮箱地址
}

export interface ModifyUserInfoRequestParams {
  avatar: string; // 头像
  username: string; // 用户名
  remark?: string; // 备注
}

export enum AdultCheckStatus {
  NotAdult = -1,
  NotSet = 0,
  Adult = 1,
}

export interface SaveUserInfoRequestParams extends ModifyUserInfoRequestParams {}

export interface RegisterIntlResponse {
  id: number; // 用户id
  username: string; // 用户名称
}

export interface UserTitle {
  id: number; // 绑定id
  intl_openid: string;
  title_id: number; // 称号id
  status: number;
  game_id: string;
  area_id: string;
  title: {
    id: number;
    avatar: string; // 称号头像
    up_time: number;
    down_time: number;
    init_hot: number;
    possess_num: number;
    status: number;
    game_id: string;
    area_id: string;
    language: {
      id: number;
      language: string; // 称号语言 zh en
      title: string; // 称号标题
      introduce: string; // 称号介绍
    }; // 称号语言
  }; // 称号对象
}

export enum UserAuthType {
  official = 1,
  creator = 2,
  institution = 3,
}

export enum UserGameAdultStatus {
  not_adult = -1,
  not_set = 0,
  adult = 1,
}

export enum UserParentCertificateStatus {
  /** 家长拒绝 */
  parent_rejected = -1,
  /** 未设置 */
  parent_not_set = 0,
  /** 家长通过 */
  parent_passed = 1,
  /** 家长认证中 */
  parent_authing = 10,
}

export interface UserInfo {
  gender: number; // 性别
  username_on: number;
  is_follow: boolean; // 是否关注
  intl_openid: string; // openid
  titles?: UserTitle; // 称号数组 最新三个
  avatar: string; // 头像地址
  avatar_on: number; // 修改头像时间
  balance: number; // 用户余额
  id: number; // 用户id
  is_admin: boolean; // 是否管理员
  like_num: number; // 喜欢数量
  nickname: string; // 昵称
  nickname_on: number; // 用户名修改时间
  email: string; // 邮箱
  post_num: number; // 发布帖子数量
  remark: string; // 介绍
  remark_on: number; // 修改介绍时间
  status: number; // 状态
  username: string; // 用户名
  is_first_register: boolean; // 是否第一次注册 1 是 0不是
  follow_num: number; // 我的关注数
  fans_num: number; // 我的粉丝数
  audit_username: string; // 名称审核
  audit_remark: string; // 备注审核
  audit_avatar: string; // 头像审核
  is_audit_username: boolean; // 名称是否审核
  is_audit_remark: boolean; // 备注是否审核
  is_audit_avatar: boolean; // 头像是否审核
  adult_check_status: AdultCheckStatus; // 是否成年
  has_sign_privacy: boolean; // 是否同意隐私协议
  is_bind_lip?: boolean; // 是否绑定Lip账号
  need_send_gift?: boolean; // 是否需要发送绑定奖励
  in_game?: boolean; // 是否游戏内环境
  need_third_bind?: boolean; // 第三方账号需要绑定
  /** 总数量的动态 */
  all_post_num: string;
  /** 头像 */
  home_page_url: string;
  /** 用户第三方主页链接（JSON） */
  home_page_links?: string;
  /** 语言 */
  language: string;
  mood: string; // 心情
  tag_id: string;
  is_mine: boolean;
  is_mute: boolean; // 互相关注
  /** 用户设置的tag */
  game_tag: number;
  /** 用户tag对应的值 */
  game_tag_num: number;
  /** 是否修改过用户名 */
  had_modified_username: boolean;
  /** 1: 官方认证，2: 创作认证, 3: 机构认证 */
  auth_type: UserAuthType;
  auth_desc: string;
  /** 前端使用判断是否有页面权限 */
  fe_user_no_permission?: boolean;
  /** 头像挂件地址 */
  avatar_pendant?: string;
  /** 客态访问时，此用户是否被登录用户关注 */
  is_followed: number;
  /** 是否互相关注 */
  is_mutual_follow: 0 | 1;
  /** 成年状态：-1：未成年，0：未设置，1：已成年 */
  game_adult_status: UserGameAdultStatus;
  /** 拉黑时间 */
  blacking_on: number;
  /** 是否拉黑 0: 未拉黑 1: 拉黑 */
  is_black: number;
  /** 用户展示语言列表 */
  regions: string[];
}

export interface UserListRequestParams extends PageParams {
  intl_openid: string;
}

export interface FollowUser {
  id: number;
  created_on: number;
  modified_on: number;
  deleted_on: number;
  is_del: number;
  nickname: string;
  remark: string;
  remark_on: number;
  username: string;
  email: string;
  username_on: number;
  phone: string;
  password: string;
  salt: string;
  status: number;
  avatar: string;
  avatar_on: number;
  intl_openid: string;
  balance: number;
  is_admin: boolean;
  follow_num: number;
  fans_num: number;
  titles: UserTitle;
}

export interface UserFollowItem {
  id: number; // 自增id
  is_follow: boolean; // 是否关注
  follow_user?: FollowUser; // 关注用户信息对象
  fans_user?: FollowUser; // 关注用户信息对象
  intl_openid: string; // 用户id
  to_intl_openid: string; // 被关注用户id
}

export interface UserFollowResponse {
  list: UserFollowItem[];
  pager: PageParams;
}

export interface MessageItemSendUser {
  id: number;
  nickname: string;
  username: string;
  status: number;
  avatar: string;
  is_admin: boolean;
  titles: UserTitle; // 称号数组 最新三个
  intl_openid: string;
}

export interface UserMessageItem {
  id: number; // 消息id
  sender_intl_openid: string; // 发送消息用户id
  sender_user: MessageItemSendUser; // 发送消息用户信息对象
  receiver_intl_openid: string; // 接收用户信息id
  type: MessageType;
  brief: string; // 摘要说明
  content: string; // 详细内容
  post_id: number; // 动态ID
  post: ComposeItem; // 动态内容信息
  comment_id: number; // 评论ID
  comment: ComposeCommentResponse & {
    contents: {
      id: number; // 内容id
      created_on: number;
      modified_on: number;
      deleted_on: number;
      is_del: number;
      comment_id: number; // 评论id
      intl_openid: string; // 用户id
      content: string; // 内容
      type: number; // 消息类型
      sort: number; // 排序
    }[];
  }; // 评论对象信息
  reply_id: number; // 回复ID
  reply: ComposeReplyReponse; // 回复对象信息
  is_read: number; // 是否已读
  created_on: number;
  modified_on: number;
  game_id: string;
  area_id: string;
  community_post_id: string;
  ext_info: string;
}

export interface UserMessageResponse {
  list: UserMessageItem[];
  pager: PageParams;
}

export interface ModifyUserFollowParams {
  intl_openid: string;
  is_follow: boolean;
}

export interface RequestLogin {
  lip_user_name: string;
  lip_channelid: number;
  lip_token: string;
  lip_openid: string;
  lip_adult_status: string;
  lip_expire_time: number;
  lip_email?: string;
}

export interface UserInfoPanelProps {
  tlog?: any;
  router?: any;
  user_info: UserInfo;
  hide_logout?: boolean;
  hide_account?: boolean;
  hide_midas?: boolean;
  hide_rewards?: boolean;
  hide_media_analysis?: boolean;
  hide_link_game_account?: boolean;
}

export enum UserInfoPanelCallbackType {
  logout = "logout",
  user_center = "user_center",
  account = "account",
  media_analysis = "media_analysis",
  rewards = "rewards",
}
export enum UserInfoPanelCallbackValue {
  // 下面三个同 UserMediaTab
  post = "post",
  follower = "follower",
  following = "following",
  logout = "logout",
  user_center = "user_center",
  account_management = "account_management",
  account_transaction_record = "account_transaction_record",
  account_payment_method = "account_payment_method",
  account_link_game_account = "account_link_game_account",
}

export interface GetAvatarsResponse {
  list: {
    currency: string[];
    game_avatar: string[];
  };
  pager: PageParams;
}

/** 修改用户名 弹框 */
export interface ModifyUserNameProps extends PopShellProps {}

export interface UserCommentBubble {
  id: string;
  /** 挂件地址 */
  comment_bubble: string;
  /** 背景色 */
  bg_color: string;
  title: string;
  /** 条件 */
  condition: string;
  /** 跳转链接 */
  jump_url: string;
  /** 是否穿戴 */
  is_weared: number;
  /** 开始时间 */
  valid_begin_at: number;
  /** 有效截止时间 */
  valid_end_at: number;
  /** 是否拥有 */
  is_owned: number;
  /** 0 非永久 1永久 */
  is_permanent: number;
}

export interface GetUserGamePlayerInfoResponse {
  /** 是否有保存角色 */
  has_saved_role_info: boolean;
  /** 大区id */
  area_id: string;
  /** 角色名 */
  role_name: string;
  /** 玩家等级 */
  player_level: number;
  /** 塔层 */
  tower_floor: number;
  /** 普通战役进度 */
  normal_progress: number;
  /** 困难战役进度 */
  hard_progress: number;
  /** 拥有妮姬数量 */
  own_nikke_cnt: number;
  /** 拥有头像框数量 */
  avatar_frame: number;
  /** 时装数量 */
  costume: number;
  /** 头像 id */
  icon: number;
  /** 战力 */
  team_combat: number;
}

export enum FriendCardStatus {
  disabled = "disabled",
  canrequest = "canrequest",
  requested = "requested",
}

export enum UnionCardStatus {
  disabled = "disabled",
  canrequest = "canrequest",
}
