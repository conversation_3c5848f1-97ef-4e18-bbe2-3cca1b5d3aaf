import { UserInfo } from "./user";

export enum Gender {
  unknow = 0,
  male = 1,
  female = 2,
}

export interface GameIdMap {
  [key: string | number]: string | number;
}

export enum UploadFileType {
  image = "public/image",
  avatar = "public/avatar",
}

export interface SaveBindEmailRequestParams {
  email: string;
  verification_code: string;
}

export enum Channel {
  youtube = 1,
  twitter = 3,
  twitch = 4,
  youtubeshort = 5,
  tiktok = 6,
}

export enum Platform {
  youtu = "youtube",
  youtube = "youtube",
  facebook = "facebook",
  twitch = "twitch",
  twitter = "twitter",
  tiktok = "tiktok",
  youtubeshort = "youtubeshort",
  discord = "discord",
  instagram = "instagram",
  reddit = "reddit",
  line = "line",
  whatsapp = "whatsapp",
}

export type PlatformTypes = keyof typeof Platform;

export type PageParams = {
  page: number;
  page_size: number;
  total_rows?: number;
  start_seq?: string;
  next_seq?: string;
  is_finish?: number;
};

export type CMSResponsePageParams = {
  next_seq: string; // 滚动分页，下一页的游标位置
  total_rows: number; // 总条数
  is_finish: number; // 是否最后一页，0为未到最后一页，1为已到最后一页
};

export enum ImageFormatType {
  avif = "/format/avif",
  webp = "/format/webp",
}

/**
 * @link https://cloud.tencent.com/document/product/436/44884
 */
export enum ImageQualityTransformType {
  quality = "/quality/60",
  rquality = "/rquality/60",
  lquality = "/lquality/60",
  ignore_error = "/ignore-error/1",
}

export enum CosCloudType {
  tencent = "tencentcloud",
  awss3 = "awss3",
}

export enum PopCallbackValue {
  close = "close",
  cancel = "cancel",
  confirm = "confirm",
}

export enum Size {
  "sm" = "sm",
  "md" = "md",
  "lg" = "lg",
  "xl" = "xl",
  "xxl" = "xxl",
}
export type SizeType = keyof typeof Size;

export enum Direction {
  vertical = "vertical",
  horizontal = "horizontal",
}

export type DirectionType = keyof typeof Direction;

export interface ShareComponentCommonProps {
  tlog?: any;
  router?: any;
  route?: any;
  user_info?: UserInfo;
  game_code?: string;
}

export enum Position {
  left = "left",
  right = "right",
  top = "top",
  bottom = "bottom",
}

export enum Zoom {
  in = "in",
  out = "out",
}

// 组件风格，用于描述组件不同的应用场景
export enum Theme {
  default = "default",
  primary = "primary",
  warning = "warning",
  danger = "danger",
  success = "success",
}
// 风格变体
export enum Variant {
  dark = "dark",
  light = "light",
  outline = "outline",
}
// 外形
export enum Shape {
  square = "square",
  normal = "normal",
  round = "round",
  mark = "mark",
}

export interface PageCursor {
  next_page_cursor?: string; // 下一页游标
  previous_page_cursor?: string; // 上一页游标
}

export interface PageParam extends PageCursor {
  page_type?: number; // 分页类型0-下翻页1-上翻页
  limit?: number; // 数量
}

// 分页参数
export interface PageInfo extends PageCursor {
  is_finish: boolean; // 是否结束
  total?: number;
}

export enum Status {
  undo = "undo",
  loading = "loading",
  error = "error",
  success = "success",
}

export enum PlatId {
  recommend = "recommend",
  outpost = "outpost",
  nikkeart = "nikkeart",
  creatorhub = "creatorhub",
  event = "event",
  official = "official",
  guides = "guides",
}
