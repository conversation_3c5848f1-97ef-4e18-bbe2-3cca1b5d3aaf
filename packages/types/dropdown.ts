// export interface DropdownItem {
//   value: string | number;
//   name: string;
// }

export type DropdownItem<T = {}> = {
  name: string;
  value: string | number;
} & T;

export interface Dropdown {
  side?: "bottom" | "top" | "right" | "left" | undefined;
  align?: "center" | "start" | "end" | undefined;
  list?: DropdownItem[];
  active?: string | number; // 这里的active对应item中的value
  open?: boolean;
  sideOffset?: number;
  type?: "customize";
  icon_color?: string;
  isAnniversary?: boolean;
  border?: boolean;
}

export interface DropdownEmits {
  (e: "change", v: DropdownItem): void;
  (e: "open", v: boolean): void;
  (e: "close"): void;
}
