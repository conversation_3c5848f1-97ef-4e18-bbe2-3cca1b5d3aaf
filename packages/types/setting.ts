/**
 * 通知相关的设置
 */
export type NotificationsType = {
  msg_comment_notify: number;
  msg_like_notify: number;
  msg_follow_notify: number;
  msg_system_notify: number;
  msg_activity_notify: number;
};
/**
 * 展示相关的设置
 */
export type ShowType = {
  show_my_posts: number;
  show_my_comment: number;
  show_my_collection: number;
  show_my_follow: number;
  show_my_fans: number;
  receive_tweet_email: number;

  /** shiftyspad 相关setting */
  // 总开关
  show_my_game_card: 0 | 1;
  allow_friend_request_via_game_card: 0 | 1;
};

export enum ShiftyspadSettingValue {
  // * 0 - NoneVisible - 所有人不可见
  // * 1 - EveryoneVisible - 所有人可见
  // * 2 - AlliesVisible - 盟友可见
  // * 3 - FriendsVisible - 好友可见
  NoneVisible = 0,
  EveryoneVisible = 1,
  AlliesVisible = 2,
  FriendsVisible = 3,
}

export type ShiftyspadSetting = {
  show_daily_info: ShiftyspadSettingValue;
  show_outpost_info: ShiftyspadSettingValue;
  show_resource_info: ShiftyspadSettingValue;
  show_nikke_info: ShiftyspadSettingValue;

  show_union_info: ShiftyspadSettingValue;
};

/**
 * 隐私相关的全部（因为后端是一个接口返回）
 */
export type PrivacySettings = ShowType & NotificationsType;

export type SettingItem<T = PrivacySettings> = { name: keyof T; on: number; desc: string };
export interface Setting {
  notifications: NotificationsType;
  privacy_shows: ShowType;
}
