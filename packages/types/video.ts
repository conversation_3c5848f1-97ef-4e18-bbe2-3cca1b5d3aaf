import { IFullDetail } from "@tencent/pa-cms-utils";
import { CmsListCpntCommonProps, CmsConfig } from "./cms";

export interface IVideoMeta {
  duration: string;
}

export interface PlayerCommonProps {
  muted?: boolean;
  autoplay?: boolean;
  controls?: boolean;
  loop?: boolean;
  poster?: string;
  src?: string;
  vid?: string;
  retry?: number;
  fake?: boolean;
  width?: number | string;
  height?: number | string;
}

export interface YoutubePlayerProps extends PlayerCommonProps {
  platform?: string;
}

export interface BsHomeRecommendedVideoCardListProps extends CmsListCpntCommonProps {}

export interface BsVideoDetailRecommendedVideosProps extends CmsListCpntCommonProps {
  content_id: string;
  secondary_label_id?: any;
  detail?: IFullDetail;
}

export interface BsVideoFeedsProps {
  cmsConfig?: CmsConfig;
  primaryColumn: string;
  anchor: number;
  anchorPositionY?: number;
  scrollContainer: string;
}
