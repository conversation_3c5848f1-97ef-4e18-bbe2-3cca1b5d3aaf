import { CMSResponsePageParams, PageInfo, ShareComponentCommonProps } from "./common";
import { PostTranslateStatus } from "./post";
import { UserInfo } from "./user";

export enum BizId {
  info = 3,
  dynamic = 4,
}
export type BizIdType = BizId.dynamic | BizId.info;

export enum CommentMode {
  "short" = "short",
  "full" = "full",
}

export interface CommentProps extends ShareComponentCommonProps, GetCommentParams {
  input_placeholder?: string;
  input_right_text?: string;
  user_avatar?: string;
  scroll_to_comment_area?: boolean;
  limit?: number;
  mode?: keyof typeof CommentMode;
}

export enum CommentAction {
  "load" = "load",
  "more" = "more",
  "compose_comment" = "compose_comment",
  "delete_comment" = "delete_comment",
  "compose_reply" = "compose_reply",
  "delete_reply" = "delete_reply",
}
export type CommentActionType = keyof typeof CommentAction;

/**
 * @description 获取评论列表参数
 */
export interface GetCommentParams {
  // 动态id
  id: string;
  // biz_id 3=资讯评论 4=动态评论
  biz_id: BizId;
  // 分页参数，第一页为空，第二页用第一页查询的返回体的值
  start_seq?: number | string;
}

/**
 * @description 删除评论参数
 */
export interface DeleteCommentParams {
  // 评论id
  comment_id: string;
  // biz_id 3=资讯评论 4=动态评论
  biz_id: BizIdType;
}

/**
 * @description 点赞评论参数
 */
export interface LikeCommentParams {
  // 评论 id
  comment_id: string;
}

/**
 * @description 一条评论中的用户信息
 */
export interface CommentItemUser {
  id: number;
  username: string;
  status: number;
  avatar: string;
  is_admin: boolean;
  intl_openid: string;
  remark: string;
  home_page_url: string;
  titles: {
    id: number;
    title: {
      avatar: string;
    };
    user_id: number;
    title_id: number;
    status: number;
    game_id: string;
    area_id: string;
  };
}

/**
 * @description 一条评论的信息
 */
export interface CommentItem {
  comment_id: string; // 评论id
  post_id: string; // 父内容id
  post_url: string; // 父内容跳转链接
  user_id: number;
  user: CommentItemUser; // 用户信息
  content_text: string; // 评论文字内容
  upvote_count: number; // 评论点赞数
  game_id: string;
  area_id: string;
  is_audit: number; // 是否已审核 1是 2不是
  is_star: boolean; // 当前用户是否点赞
  can_delete: boolean; // 当前用户是否可以删除
  ip_loc: string;
  created_on: number;
  modified_on: number;
  can_report: number; // 是否可以举报
}

/**
 * @description 评论发布后的返回结果
 */
export interface ComposeCommentResponse extends CommentItem {}

/**
 * @description 获取评论的返回参数
 */
export interface GetCommentResponse {
  list: CommentItem[];
  pager: CMSResponsePageParams;
}

export enum CommentType {
  comment = 1,
  reply = 2,
  info_comment = 3,
  info_reply = 4,
}

export interface ComposeCommentParams {
  post_uuid?: string;
  comment_uuid?: string;
  type: CommentType;
  content: string;
  pic_urls: string[];
  users: string[];
  comment_bubble_id: number | undefined;
}

export interface GetPostCommentsParams {
  post_uuid: string; // 动态唯一id
  page_type: number; // 查询页码类型0-下一页1-上一页
  previous_page_cursor?: string; // 上一页游标
  next_page_cursor?: string; // 下一页游标
  limit?: number; // 数量
  comment_reply_limit?: number; // 第一页评论的回复数量
}

export enum TopBottomStatus {
  unset = "unset",
  top = "top",
  bottom = "bottom",
}

export interface GetPostCommentsResponseItem {
  id: number; // 主键id
  can_delete: boolean; // 是否可以删除
  can_report: boolean; // 是否可以举报
  post_uuid: string; // 动态唯一id
  user: UserInfo;
  comment_uuid: string; // 评论的唯一id
  type: string; // 评论类型1-评论2-回复
  reply_uuid: string; // 回复id
  content: string; // 评论内容
  pic_urls: string[]; // 评论图片
  at_intl_openid: string; // @用户intl_openid
  upvote_count: number; // 点赞数
  created_on: string;
  is_star: boolean;
  replies: {
    data_list: GetPostCommentRepliesResponseItem[]; // 回复列表
    page_info: PageInfo;
  };
  is_mine: boolean;
  is_deleted: boolean;
  is_author: boolean;

  original_title?: string;
  original_content?: string;
  translate_title?: string;
  translate_content?: string;
  has_translated?: boolean;
  translate_status?: PostTranslateStatus;
  /** 评论气泡id */
  comment_bubble_id?: number;
  can_top: boolean;
  can_bottom: boolean;
  can_copy_comment_id: boolean;
  top_bottom_status: TopBottomStatus;
}

export interface BatchGetPostCommentRepliesResponse {
  replies_map: {
    [key: string]: {
      data_list: GetPostCommentRepliesResponseItem[];
      page_info: PageInfo;
    };
  };
}

export interface GetMyCommentItem {
  area_id: string;
  can_delete: boolean;
  can_report: boolean;
  comment_context: string;
  comment_del: number;
  comment_uuid: string;
  content: string;
  created_on: string;
  game_id: string;
  intl_openid: string;
  is_author: boolean;
  is_mine: boolean;
  modified_on: string;
  pic_urls: string[];
  post_context: string;
  post_del: number;
  post_title: string;
  post_uuid: string;
  reply_to_reply_uuid: string;
  reply_uuid: string;
  title: string;
  type: number;
  upvote_count: string;
  user: UserInfo;
  plate_name: string;
}

export interface GetPostCommentRepliesParams {
  comment_uuid: string; // 评论的唯一id
  page_type: number; // 分页查询类型0-下一页1-上一页
  previous_page_cursor: string; // 上一页的游标
  next_page_cursor?: string; // 下一页的游标
  limit: number; // 数量
}

export interface GetPostCommentRepliesResponseItem {
  id: number; // 主键id
  can_delete: boolean; // 是否可以删除
  can_report: boolean; // 是否可以举报
  post_uuid: string; // 动态唯一id
  user: UserInfo; // 发布动态用户信息
  comment_uuid: string; // 评论的唯一id
  type: string; // 评论类型1-评论2-回复
  reply_uuid: string; // 回复id
  content: string; // 评论内容
  pic_urls: string[]; // 评论图片
  at_intl_openid: string; // @用户intl_openid
  upvote_count: number; // 点赞数
  is_star: boolean; // 是否有点赞
  at_user: {
    all_post_num: string; // 所有动态数量
    avatar: string; // 头像
    fans_num: string; // 粉丝数
    follow_num: string; // 关注数
    home_page_url: string; // 主页url
    id: string; // 主键id
    intl_openid: string; // 用户openid
    is_admin: boolean; // 是否是管理员
    language: string; // 语言
    post_num: string; // 动态数量--过审
    remark: string; // 签名
    status: number; // 状态
    titles: {
      area_id: string; // 区域id
      game_id: string; // 游戏id
      id: number; // 绑定称号id
      status: number; // 是否生效1：生效2：失效
      title: {
        area_id: string; // 区域id
        avatar: string; // 称号icon
        down_time: string; // 下架时间
        game_id: string; // 游戏id
        id: number; // 称号id
        init_host: string; // 初始热度
        language: {
          id: string; // 语言id
          introduce: string; // 描述
          language: string; // 语言
          title: string; // 称号
          title_id: number; // 称号id
        }; // 语言
        possess_num: number; // 拥有数量
        status: number; // 状态 1:待上架 2:已上架
        up_time: number; // 上架时间
      }; // 称号信息
    }; // 称号
    username: string; // 昵称
    mood: string; // 用户心情
  }; // 发布动态用户信息
  created_on: string;
  is_deleted: boolean;
  is_author: boolean;
  is_mine: boolean;
  translate_content?: string;
  has_translated?: boolean;
  translate_status?: PostTranslateStatus;
  can_top: boolean;
  can_bottom: boolean;
  can_copy_comment_id: boolean;
  top_bottom_status: TopBottomStatus;
}

export interface CommentStarRequestParams {
  comment_uuid: string;
  type: CommentType;
}
