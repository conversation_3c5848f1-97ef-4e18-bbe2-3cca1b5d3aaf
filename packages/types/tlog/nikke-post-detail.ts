import type { DefineTlogConfig } from ".";

/** 帖子详情页 Tlog 配置 */
export type PostDetailTlogConfig = DefineTlogConfig<{
  /** 【曝光】帖子详情页页面曝光 */
  standalonesite_news_detail_page: {
    cm_vshow: {
      content_id: string;
      content_type: any;
      label_id: any;
      label_name: any;
    };
  };

  /** 【停留】帖子详情页停留时长 */
  standalonesite_news_detail_page_stay: {
    cm_lvtm: {
      du: number;
    };
  };

  /** 【点击】关注帖子作者按钮点击（通用） */
  standalonesite_news_follow_btn: {
    cm_click: {
      dst_open_id: string;
      location: number;
      content_id: string;
      is_follow: number;
      label_id: number;
      label_name: string;
    };
  };

  /** 【点击】评论按钮点击 */
  standalonesite_news_comment_btn: {
    cm_click: {
      location: number;
      content_id: string;
      label_id: number;
      label_name: string;
    };
  };

  /** 【点击】评论发送按钮点击 */
  standalonesite_news_comment_submit_btn: {
    cm_click: {
      location: number;
      content_id: string;
      comment_id: string;
      label_id: number;
      label_name: string;
    };
  };

  /** 【点击】点赞按钮点击 */
  standalonesite_news_praise_btn: {
    cm_click: {
      location: number;
      content_id: string;
      comment_id?: string;
      is_praise: number;
      label_id: number;
      label_name: string;
    };
  };

  /** 【点击】分享按钮点击 */
  standalonesite_news_share_btn: {
    cm_click: {
      location: number;
      content_id: string;
      label_id: number;
      label_name: string;
    };
  };

  /** 【点击】点击第三方分享渠道 */
  standalonesite_share_channel_btn: {
    cm_click: {
      channel_name: string;
      content_id?: string;
      url: string;
    };
  };

  /** 【点击】帖子收藏按钮点击 */
  standalonesite_news_collect_btn: {
    cm_click: {
      location: number;
      content_id: string;
      label_id: number;
      label_name: string;
    };
  };
}>;
