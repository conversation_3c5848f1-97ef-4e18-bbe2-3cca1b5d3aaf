import type { DefineTlogConfig } from ".";

/** 用户中心的 Tlog 配置 */
export type UserCenterTlogConfig = DefineTlogConfig<{
  /** 个人主页曝光 */
  standalonesite_usercenter_page: { cm_vshow: { openid: string } };
  /** 签到按钮点击 */
  standalonesite_usercenterpage_checkin: { cm_click: void };
  /** 配置个人标签按钮点击 */
  standalonesite_usercenter_tag_btn: { cm_click: void };
  /** 个人心情按钮点击 */
  standalonesite_usercenter_emo_btn: { cm_click: void };
  /** link按钮点击 */
  standalonesite_usercenter_link_btn: { cm_click: void };
  /** Play Game按钮点击 */
  standalonesite_usercenter_playgame_btn: { cm_click: void };
  /** 积分中心曝光 */
  standalonesite_usercenter_reward_page: { cm_vshow: void };
  /** banner曝光 */
  standalonesite_usercenter_reward_banner_item_expose: {
    cm_vshow: {
      /**  位置从0开始 */
      location: number;
      /** 内容ID */
      content_id: string;
      /** banner标题 */
      title: string;
      /** 跳转url */
      dst_url: string;
    };
  };
  /** banner点击 */
  standalonesite_usercenter_reward_banner_item_exposeclick: {
    cm_vshow: {
      /** 位置从0开始 */
      location: 0;
      /** 内容ID */
      content_id: string;
      /** banner标题 */
      title: string;
      /** 跳转url */
      dst_url: string;
    };
  };

  /** 商品item点击 */
  standalonesite_usercenter_reward_pruduct_item: { cm_click: { product_id: string } };
  /** 点击兑换按钮 */
  standalonesite_usercenter_reward_redmeet: { cm_click: { product_id: string } };
  /** 商品兑换结果 */
  standalonesite_usercenter_reward_redmeet_ret: { cm_click: { product_id: string; ret: any } };
  /** 规则按钮点击 */
  standalonesite_usercenter_reward_rule_btn: { cm_click: void };
  /** 兑换记录按钮点击 */
  standalonesite_usercenter_reward_history_btn: { cm_click: void };
  /** 分享按钮点击 */
  standalonesite_usercenter_reward_share_btn: { cm_click: void };
  /** 编辑个人信息按钮点击 */
  standalonesite_usercenter_edit_btn: { cm_click: void };
  /** 个人信息编辑页面曝光 */
  standalonesite_profilepage: { cm_vshow: void };
  /** 名称页面曝光 */
  standalonesite_nicknamesettingpage: { cm_vshow: void };
  /** 输入新名称页曝光 */
  standalonesite_nicknameInitializepage: { cm_vshow: void };
  /** 提交名称按钮点击 */
  standalonesite_nickname_change_finish_btn: { cm_click: { sig: string } };
  /** 头像编辑页面曝光 */
  standalonesite_avatarsettingpage: { cm_vshow: void };
  /** 签名页曝光 */
  standalonesite_signaturesettingpage: { cm_vshow: void };
  /** 签名提交按钮点击 */
  standalonesite_signaturesettingpage_submit_btn: { cm_click: { sig: string } };
  /** 个人中心子tab点击 */
  standalonesite_usercenter_sub_tab: { cm_click: { tab_name: string } };
}>;
