import type { DefineTlogConfig } from ".";

export interface CmClickItem {
  location: number; // 位置从0开始
  content_id: number | string; // 内容ID
  label_id: number; // 子tab的id
  label_name: string; // 子tab的名称
}

/** Home 模块的 Tlog 配置 */
export type HomeTlogConfig = DefineTlogConfig<{
  /** 【曝光】独立站初始化上报（用来做来源跟踪） */
  standalonesite_cm_init: { cm_vshow: void };
  /** 【曝光】首页曝光 */
  standalonesite_home_page: { cm_vshow: { red_tag: boolean } };
  /** 【曝光】首页真正加载完成(有open_id)之后  */
  standalonesite_realfinish_page: { cm_vshow: { red_tag: boolean } };
  /** 【停留】首页停留时长，单位ms */
  standalonesite_home_stay: { cm_lvtm: { du: number } };
  /** 【停留】独立站心跳上报(每10s上报一次) */
  standalonesite_heart_beat: { cm_click: void };

  /** 【点击】语言切换按钮点击 **/
  standalonesite_lang_btn: { cm_click: { cur_lang: string; dst_name: string } };
  /** 【点击】搜索框点击 **/
  standalonesite_search_btn: { cm_click: { content: string } };
  /** 【点击】添加到桌面按钮点击 **/
  standalonesite_add_desktop_btn: { cm_click: void };
  /** 【点击】确认添加到桌面按钮点击 **/
  standalonesite_add_desktop_confirm_btn: { cm_click: void };
  /** 【点击】消息按钮点击 **/
  standalonesite_message_center_btn: { cm_click: { red_tag: boolean } };
  /** 【点击】个人头像点击 **/
  standalonesite_personal_btn: { cm_click: void };
  /** 【曝光】签到按钮曝光 **/
  standalonesite_sign_window: { cm_vshow: void };
  /** 【曝光】签到按钮点击  **/
  standalonesite_sign_btn: { cm_click: void };

  /** 【曝光】顶部banner曝光 */
  standalonesite_banner_expose: {
    cm_vshow: { location: number; content_id: string; title: string };
  };
  /** 【曝光】顶部banner点击 */
  standalonesite_banner_click: {
    cm_click: { location: number; content_id: string; title: string };
  };
  /** 【曝光】页面子Tab曝光 */
  standalonesite_sub_tab_page: { cm_vshow: { label_id: number; label_name: string } };
  /** 【点击】页面子Tab点击 */
  standalonesite_sub_tab_btn: { cm_click: { label_id: number; label_name: string } };
  /** 【曝光】新的资讯newss跑马灯曝光 */
  standalonesite_newss_hint_item_expose: { cm_vshow: { content_id: string; dst_url: string } };
  /** 【点击】新的资讯newss跑马灯点击 */
  standalonesite_newss_hint_item_click: { cm_click: { content_id: string; dst_url: string } };
  /** 【点击】小工具模块点击 */
  standalonesite_small_tools_item: { cm_click: { tool_id: number; tool_name: string } };
  /** 【点击】Posts按钮点击 */
  standalonesite_posts_btn: { cm_click: void };
  /** 【点击】筛选按钮点击 */
  standalonesite_filter_btn: {
    cm_click: { btn_name: string; label_id: number; label_name: string };
  };
  /** 【点击】标签筛选按钮点击 */
  standalonesite_tag_filter_btn: {
    cm_click: { tag_name: string; label_id: number; label_name: string };
  };
  /** 【曝光】帖子曝光（通用） */
  standalonesite_news_item_expose: { cm_vshow: CmClickItem };
  /** 【点击】帖子点击（通用） */
  standalonesite_news_item_click: {
    cm_click: {
      location: number; // 位置从0开始
      content_id: number | string; // 内容ID
      label_id: number | string; // 子tab的id
      label_name: string; // 子tab的名称
    };
  };
  /** 【点击】视频播放按钮点击 */
  standalonesite_news_video_btn: { cm_click: CmClickItem };
  /** 【点击】 All Region筛选按钮点击 */
  standalonesite_all_region_btn: { cm_click: { state: number } };
  /** 【点击】Twitter按钮点击 */
  standalonesite_twitter_btn: { cm_click: void };
  /** 【点击】YouTube按钮点击 */
  standalonesite_youtube_btn: { cm_click: void };

  /**【加载】首页首屏加载时长 */
  standalonesite_home_page_loading: { cm_vshow: { du: number } };
  /**【加载】首页子页面加载时长 */
  standalonesite_sub_page_loading: { cm_vshow: { du: number; plate: string } };
}>;
