import { HomeTlogConfig } from "./nikke-home";
import { PostTlogConfig } from "./nikke-post";
import { UserCenterTlogConfig } from "./nikke-usercenter";
import { LoginTlogConfig } from "./nikke-login";
import { searchTlogConfig } from "./nikke-search";
import { messageTlogConfig } from "./nikke-message";
import { PostComposeTlogConfig } from "./nikke-post-compose";
import { ShiftysPadTlogConfig, UtilsTlogConfig } from "./nikke-shiftyspad";
import { PostDetailTlogConfig } from "./nikke-post-detail";
import { TopticDetailTlogConfig } from "./nikke-topic";
import { CdkTlogConfig } from "./nikke-cdk";
import { UserTlogConfig } from "./nikke-user";
import { missionTlogConfig } from "./nikke-mission";
import { UnionTlogConfig } from "./nikke-union";
import { PointsTlogConfig } from "./nikke-points";

export type DefineTlogConfig<T extends Record<string, Record<string, void | Record<string, any>>>> =
  T;

/** 汇总各个子模块的上报配置 */
export type TlogConfig = UserCenterTlogConfig &
  LoginTlogConfig &
  HomeTlogConfig &
  PostTlogConfig &
  searchTlogConfig &
  messageTlogConfig &
  PostComposeTlogConfig &
  ShiftysPadTlogConfig &
  UtilsTlogConfig &
  PostDetailTlogConfig &
  TopticDetailTlogConfig &
  CdkTlogConfig &
  UserTlogConfig &
  missionTlogConfig &
  UnionTlogConfig &
  PointsTlogConfig;
