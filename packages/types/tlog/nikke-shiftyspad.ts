import type { DefineTlogConfig } from ".";

export type ShiftysPadTlogConfig = DefineTlogConfig<{
  standalonesite_shiftypad_gamerecordpage: {
    cm_vshow: {
      openid: string;
      is_client: boolean;
    };
  };

  standalonesite_nikkepedia_page: {
    cm_vshow: {};
  };

  small_tool_home_page: {
    cm_vshow: {};
  };
  small_tool_login_btn: {
    cm_click: {};
  };
  small_tool_login_page: {
    cm_vshow: {};
  };
  small_tool_Apple_Sore_btn: {
    cm_click: {};
  };
  small_tool_Google_Play_btn: {
    cm_click: {};
  };
  small_tool_nikke_role_player_btn: {
    cm_click: {};
  };
  small_tool_nikke_role_nikke_role_btn: {
    cm_click: {};
  };

  small_tool_nikke_role_filter_btn: {
    cm_click: {
      btn_id: string;
      btn_name: string;
      scene: "player" | "role";
    };
  };
  small_tool_nikke_role_avatar_btn: {
    cm_click: {
      role_id: string;
      role_name: string;
      scene: "player" | "role";
    };
  };
  small_tool_nikke_role_detail_page: {
    cm_vshow: {
      role_id: string;
      role_name: string;
    };
  };

  small_tool_nikke_role_detail_addition_btn: {
    cm_click: {};
  };
  small_tool_plot_main_line_btn: {
    cm_click: {};
  };
  small_tool_plot_break_btn: {
    cm_click: {};
  };
  small_tool_plot_main_line_chapter_btn: {
    cm_click: {
      chapter_id: number;
    };
  };
  small_tool_plot_main_line_chapter_sub_btn: {
    cm_click: {
      chapter_id: number;
      sub_chapter_id: number;
    };
  };
  small_tool_plot_archives_btn: {
    cm_click: {};
  };
  small_tool_plot_archives_archives_btn: {
    cm_click: {
      archives_id: number;
    };
  };
  small_tool_plot_archives_archives_sub_btn: {
    cm_click: {
      archives_id: string;
      sub_archives_id: string;
    };
  };
  small_tool_plot_main_line_chapter_sub_item: {
    cm_click: {
      voice_id: string;
      chapter_id: string;
      // sub_chapter_id: number;
    };
  };

  small_tool_main_story_btn: {
    cm_click: {};
  };
  small_tool_brief_encounter_btn: {
    cm_click: {};
  };
  small_tool_achives_btn: {
    cm_click: {};
  };
  small_campaign_list_page: {
    cm_vshow: {
      tab_name: string;
    };
  };
  standalonesite_user_bind_ret: {
    cm_click: {
      ret: number;
      roleid: string;
    };
  };
}>;

export type UtilsTlogConfig = DefineTlogConfig<{
  standalonesite_share_window: {
    cm_vshow: { url: string; dst_url: string };
  };
}>;
