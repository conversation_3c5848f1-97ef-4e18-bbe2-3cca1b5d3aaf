export enum GrayscaleLogicType {
  openid = "openid",
  openid_last_digits = "openid_last_digits",
  timestamp = "timestamp",
  url_no_keyword = "url_no_keyword",
}

export enum GrayscaleLogicRelation {
  and = "and",
  or = "or",
  contains = "contains",
  not_contains = "not_contains",
  equal = "eq",
  greater_than = "gt",
  greater_than_or_equal = "gte",
  less_than = "lt",
  less_than_or_equal = "lte",
  not_equal = "neq",
  between = "between",
}

export interface GrayscaleRuleItem {
  value?: number | string;
  min?: number;
  max?: number;
}

export interface GrayscaleLogic {
  // 逻辑类型
  type: GrayscaleLogicType;
  // 逻辑关系
  relation: GrayscaleLogicRelation;
  // 位数，针对 LogicType.openid_last_digits 有效
  digits?: number;
  rules: Array<GrayscaleRuleItem> | Array<string>;
}

export enum GrayscaleKey {
  challenge = "challenge",
  vconsole = "vconsole",
  system = "system",
  // 周年活动
  annual_event = "annual_event",
  // laboratory
  laboratory = "laboratory",
}

export type Grayscale = {
  [K in GrayscaleKey]: {
    relation: GrayscaleLogicRelation;
    logics: Array<GrayscaleLogic>;
  };
};
