import { IFullDetail } from "@tencent/pa-cms-utils";
import { ShareComponentCommonProps } from "./common";

export interface CmsDetailProps extends ShareComponentCommonProps {
  app_name: any;
  content_id: string;
  cms_config?: any;
  header_visible?: boolean;
}

export interface Detail extends IFullDetail {
  like_num: number;
}

export enum CmsContentClass {
  info = 0,
  banner = 1,
  announcement = 2,
}

export interface CmsListCpntLoadParams {
  content_class?: CmsContentClass;
  with_off?: boolean;
  get_num?: number;
  offset?: number;
  content_id?: string;
}

export interface CmsListCpntLoadOptions {
  primary: string;
  second: string;
}

export interface CmsListCpntCommonProps extends CmsListCpntLoadParams {
  cms_config?: {
    cms_gameid?: string;
    cms_areaid?: string;
    custom_cms_url?: string;
  };
  primary_column_name?: string;
  second_column_name?: string;
  game_code?: string;
  env?: string;
  list?: any;
  // 如果是咨询组需要传递
  group_id?: string;
  callback?: Function;
}

export enum CmsVideoPlatform {
  url = "url",
  youtube = "youtube",
}

export interface CmsVideoInfoParams {
  has_video: boolean; // 是否有视频
  is_embed?: boolean; // 是否embed视频内容
  platform?: CmsVideoPlatform;
  vid?: string; // embed id
  video_cover?: string; // embed 封面
}

export interface CmsConfig {
  cms_gameid?: string;
  cms_areaid?: string;
  cms_lang?: string;
  source_type?: string;
  custom_cms_url?: string;
}

export interface UseCmsOptions {
  is_log?: boolean;
}

export interface GameRulesItem {
  desc: string;
  name: string;
  value: string;
}

export interface IBannerExtInfo {
  thumbnail: string[];
  hide_rule?: {
    name: string;
    value: string;
    desc: string;
  };
  intl_gameid?: {
    value: string;
  };
  inner_game_rules?: Array<GameRulesItem>;
  outer_game_rules?: Array<GameRulesItem>;
}
