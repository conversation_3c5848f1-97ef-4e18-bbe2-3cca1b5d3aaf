export enum OrderStatus {
  order_not_begin = 0,
  deduct_points_err = -1,
  send_commodity_err = -2,
  send_commodity_ing = 1,
  gift_package_distribution_completed = 2,
  deduct_points_failed_has_rollback = 3,
  send_commodity_failed_has_rollback = 4,
  rollback_points_error = -3,
  send_zero_price_commodity_err = -4,
}

export interface OrderDetail {
  commodity_price?: number;
  order_id?: string;
  status?: OrderStatus;
  point_expired?: boolean;
  commodity_name?: string;
  order_time?: number;
  commodity_discount_price?: number;
  commodity_is_discount?: boolean;
  send_detail?: OrderDetailSendDetail;
  send_type?: OrderDetailSendType;
  commodity_type?: OrderDetailCommodityType;
  commodity_sub_type?: OrderDetailCommoditySubType;
}

/** 商品类型 */
export enum OrderDetailCommodityType {
  /** 礼品卡 */
  card = 1,
  /** 一级代币 */
  token = 2,
  /** 游戏道具 */
  prop = 3,
  /** 实物 */
  real = 4,
}

export interface OrderDetailSendDetail {
  Card?: string;
  Password?: string;
  cdkey?: string;
}

/**发货类型; */
export enum OrderDetailSendType {
  /** 直接到账 */
  Prop = 0,
  /** Cdkey */
  Cdkey = 1,
  /** 卡密 */
  CardPassword = 2,
}

/** 二级商品类型; */
export enum OrderDetailCommoditySubType {
  /** 通用礼品卡 */
  Common = 0,
  Steam = 1,
  Amazon = 2,
  Apple = 3,
  Google = 4,
}
