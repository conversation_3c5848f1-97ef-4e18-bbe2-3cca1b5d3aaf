export interface UnionCard {
  /** nikke区服id */
  nikke_area_id: number;
  /** intl_open_id 发布卡片者 */
  intl_open_id: string;
  /** 公会卡片唯一uuid */
  guild_card_uuid: string;
  /** 公会id */
  guild_id: string;
  /** 公会名称 */
  guild_name: string;
  /** 公会描述 */
  guild_description: string;
  /** 公会icon */
  guild_icon: number;
  /** 公会等级 */
  guild_level: number;
  /** 公会rank */
  guild_rank: UnionRank;
  /** 公会活跃度 */
  guild_activity: number;
  /** 公会locale */
  guild_locale: string;
  /** 公会entry_level */
  guild_entry_level: number;
  /** 公会join_type 0:直接加入, 1:需要申请, 2:禁止加入 */
  guild_join_type: UnionJoinType;
  /** 是否为应援用户 */
  is_supporter?: boolean;
  /** 公会是否已经发布到广场 */
  is_published?: boolean;
}

export enum UnionJoinType {
  /** 直接加入 */
  DIRECT_JOIN = 0,
  /** 需要申请 */
  APPLY_JOIN = 1,
  /** 禁止加入 */
  FORBIDDEN_JOIN = 2,
}

/**
 * 公会rank
 */
export enum UnionRank {
  /** 王者 */
  CHALLENGER = 0,
  /** 钻石 */
  DIA = 1,
  /** 白金 */
  PLATINUM = 2,
  /** 黄金 */
  GOLD = 3,
  /** 白银 */
  SILVER = 4,
  /** 铜牌 */
  BRONZE = 5,
  /** 新手 */
  BEGINNER = 6,
}

export interface SupportUserInfo {
  /** intl_open_id */
  intl_open_id: string;
  /** 用户社区头像 */
  icon: string;
  /** 是否展示好友卡片详情 */
  show_friend_card_detail?: boolean;
}
