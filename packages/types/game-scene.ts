export type SceneArchiveData = {
  id: number;
  record_slot_bg_addressable: string;
  record_unlock_bg_addressable: string;
  record_list_order: number;
  record_title_locale: {
    value: string;
    chapter_name: string;
  };
  record_main_archive_event_id: {
    value: number;
    event: {
      value: {
        event_system_type: string;
        archive_story_progress_group_order: number;
        event_start_date: string;
        event_end_date: string;
      };
      child: {
        value: SceneArchiveChildData[];
      };
    };
  };

  // 3.27 解锁时间
  record_activate_date?: number;
};

export type SceneArchiveChildData = {
  value: {
    archive_story_progress_group_order: number;
  };
  story: SceneArchiveStoryData;
};

export type SceneArchiveStoryData = {
  value: {
    id: number;
    name: string;
    album_category_group: number;
  };
  album: SceneArchiveGroupData;
};

export type SceneArchiveGroupData = {
  value: {
    id: number;
  };
  scene: SceneListData;
};

export type SceneDetailData = {
  id: number;
  scene_name: string;
  scenario_group_id: {
    value: string;
    records: {
      value: SceneDetailSectionData[];
    };
  };
};

export type SceneDetailSectionData = {
  value: {
    id: string;
    speaker: string;
    scenario_localkey: string;
    speech_window?: string;
    jump_target?: string;
  };
  quest_name: string;
  speaker: {
    value: string;
    name_localkey: { value: string; character_name: string };
  };
};

export type CharacterSceneSettingItem = {
  id: string;
  resource_id: number;
};

export type StageData = {
  id: number;
  chapter_id: number;
  chapter_mod: string;
  name_localkey: {
    name: string;
  };
  enter_scenario?: string; // 对应章节，不一定有
  exit_scenario?: string;
};

export type SceneData = {
  value: {
    id: number;
    scenario_group_id: string;
  };
  scenario_name_localkey: {
    scenario_name: string;
  };
};

export type SceneListData = {
  id: number;
  sub_category_id: {
    value: number;
    scenes: {
      value: SceneData[];
    };
  };
  sub_category_name_localkey: {
    chapter_name: string;
  };
  sub_category_thumbnail: string;
};

export interface StroylineMainItem {
  id: number;
  img: string;
  order: string;
  name: string;
  sectionCur: number;
  sectionTotal: number;
  locked: boolean | number;
  sections?: Partial<StorylineSection>[];
}
export interface StorylineSection {
  name: string;
  locked: boolean;
  scenario_group_id: number | string;
}
