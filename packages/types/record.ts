export enum PointsType {
  increase = 1, // 增加积分
  consume = 2, // 消费积分
  rollback = 3, // 回退消费加积分
  expire = 4, // 过期积分
  zero_points_increase = 5, // 零积分兑换商品先加积分
  zero_points_consumption = 6, // 零积分兑换商品消费积分
  lottery_consumption = 7, // 抽奖活动消费积分
  lottery_increase = 8, // 抽奖活动增加积分
}

export enum RecordListFilterType {
  all = 0,
  unused = 1,
  redeemed = 2,
  expired = 3,
}

export interface RecordListFilter {
  filter: RecordListFilterType;
  limit: number;
  page: number;
}

export interface RecordListItem {
  // id: any;
  name: string;
  create_time: string;
  points: number;
  is_details?: boolean;
  type: PointsType;
  order_no: string;
}

export interface RecordProps {
  item: RecordListItem;
}
