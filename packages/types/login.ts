import { IntlChannel } from "./intl";
import type Api from "@intlsdk/account-api";

export interface AccountSdkConfig {
  env: string;
  appID: string;
  gameID: string;
  webID?: string;

  // optional
  hostThird?: string;
  hostINTL?: string;
  accountPlatType?: IntlChannel;

  // ...
}

/**
 * @likn https://docs.playernetwork.intlgame.com/docs/zh/Level-Infinite-Pass/Integration/WebConfig/
 */
export interface LoginPopConfig extends AccountSdkConfig {
  common_config?: {
    use_debugger: boolean;
  };
  config: {
    isMobile: boolean;
    langType: string;
    renderMode: string;
    loginWithCode: {
      enable: boolean;
      registerType: string;
    };
    loginWithPwd?: {
      enable: boolean;
      registerType: string;
    };
    procedureSwitch: {
      region: boolean;
      adultStatus: boolean;
      agreement: boolean;
      registerPassword: string;
    };
    socialList: any; // Replace 'any' with the appropriate type if known
    theme?: {
      token: {
        colorPrimary: string;
        colorSecondary: string;
        colorPrimaryBg: string;
        colorSecondaryBg: string;
        colorIcon: string;
        colorInput: string;
        fontSize: string;
        colorPrimaryText: string;
        colorTextBase: string;
        colorTextButton: string;
        colorError: string;
      };
      algorithm: string;
    };
  };
}

export interface LoginApiParams {
  game_openid?: string; // 游戏Openid
  game_channelid: number; // 游戏Channelid
  game_token?: string; // 游戏Token
  game_id: string; // 游戏Id
  game_expire_time?: number; // 过期时间
  game_encodeparam?: string; // 游戏内传值，由此值可以不传game_openid 和 game_token

  game_uid?: string; // 游戏Uid
  game_user_name?: string; // 游戏UserName
  game_user_region?: string; // 游戏UserRegion
  game_adult_status?: number; // 游戏AdultStatus
  game_email?: string;
}

export type IntlRes<T> = Promise<T & { ret: number; msg: string }>;

export type BindedAccountInfo = {
  channelid: number;
  channel_info: any;
  is_link: 0 | 1;
  is_primary: 0 | 1; // 是否主账号
  picture_url: string;
  user_name: string;
};

export interface IntlSdkInstance extends Api {
  getBindChannelsByOpenID: (params: {}) => IntlRes<{
    bind_list: BindedAccountInfo[];
  }>;
}

export interface LoginRes {
  ret: number;
  openid: string;
  token: string;
  token_expire_time: number;
  user_name: string;
  birthday: string;
  channel_info: {
    account: string;
    openid: string;
    token: string;
    channelId: number;
    expire_ts: number;
  };
  extra_json: {
    get_status_rsp: {
      adult_check_status: number;
      region: string;
    };
  };
}
