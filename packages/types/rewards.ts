import { GameRegion } from "./games";
import { OptionsItem } from "./select";
import { PopShellProps } from "./pop";
// import { SocialMediaSharePopProps } from "./share";

export enum TaskType {
  // 签到
  DailyCheckIn = 1,
  // 游戏登录
  GameLogin = 2,
  // 充值支付
  Shop = 3,
  // 分享
  ShareProduction = 4,
  // 关注社媒
  SocialMediaAttention = 5,
  // 敬请期待
  Other = -1,
}

export enum Status {
  Success = 0,
  FAILURE = 1,
}

export interface Task {
  details: string;
  is_completed: boolean;
  task_id: string;
  task_type: TaskType;
  points: number;
  amount: string;
  task_pic_url: string;
  task_completed_pic_url: string;
  task_pic_checked?: string;
  task_name: string;
  task_desc?: string;
  task_pc_completed_pic_url: string;
  task_pc_pic_url: string;
}

export enum RewardConfirmStatus {
  "in-complete" = "in-complete",
  "complete" = "complete",
  "success" = "success",
}

export interface RewardConfirmProps {
  show?: boolean;
  duration?: number;
  autoClose?: boolean;
  title: string;
  status: RewardConfirmStatus;
  coins?: number;
  desc?: string;
  onConfirm?: () => void;
  onClose?: () => void;
  onCancel?: () => void;
  onDesc?: () => void;
}

export interface RedeemConfirmProps extends PopShellProps {
  title: string;
  price: number;
  available: number;
  game_region: GameRegion[];
  default_area_id?: number | string;
  default_zone_id?: number | string;
  default_role_id?: number | string;
  commodity_id?: string;
  game_id?: string;
  getRoles: (server: string, zone: number | string) => Promise<OptionsItem[]>;
  onSubmit: (params: {
    server: string;
    zone: number | string;
    role: string | number;
    auto_bind?: boolean;
  }) => Promise<void>;
  onClose?: () => void;
  onCancel?: () => void;
}

export interface RedeemTipProps extends PopShellProps {
  type: "success" | "error";
  title: string;
  content: string;
  confirm_btn_text?: string;
  share_options?: {
    text?: string;
    url?: string;
  };
}

export enum DisposableTaskType {
  sign_in = 1,
  game_login = 2,
  payment = 3,
  share = 4,
  follow_social_media = 5,
}

export interface DisposableTaskItem {
  amount: string;
  details: string;
  is_completed: boolean; // 是否完成
  points: any; // 积分
  task_completed_pic_url: string; // 移动端完成图标
  task_id: string; // 任务id
  task_name: string; // 任务名称
  task_pc_completed_pic_url: string; // pc端完成图标
  task_pc_pic_url: string; // pc端未完成图标
  task_pic_url: string; // 移动端未完成图标
  task_type: number; // 任务类型，详见 DisposableTaskType
}

export interface GetDisposableTaskResponse {
  tasks: DisposableTaskItem[];
}

export enum CollectionStatus {
  "in-complete" = 0,
  "complete" = 1,
}
