import { PopShellProps } from "./pop";

export interface ImageProps {
  src: string;
  alt?: string;
  lazy?: boolean;
  placeholder?: any;
  loading?: boolean;
  thumbnail?: boolean;
  thumbnail_quality?: number;
  /** 是否采用原图，默认为 false，则会处理成 webp 格式 */
  original?: boolean;
  cover?: boolean;
  image_class?: string;
  image_style?: any;
  /** 是否在缩略图之后展示原图，loading => 缩略图 => 原图 */
  auto_dethumbnail?: boolean;
}

export interface PopImagesProps extends PopShellProps {
  images: Array<{ src: string }>;
  index?: number;
}
