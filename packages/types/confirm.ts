import { PopShellProps } from "./pop";

export enum ConfirmType {
  alert = "alert",
  confirm = "confirm",
}

export interface ConfirmProps extends PopShellProps {
  hide_close?: boolean;
  title?: string;
  content?: string;
  hide_cancel?: boolean;
  confirm_text?: string;
  cancel_text?: string;
  z_index?: number;
}

export interface LinkAccountConfirmProps extends ConfirmProps, PopShellProps {
  game_id: string;
  intl_game_id: string;
  channel_id: string;
  encode_param: string;
  onConfirm?: Function;
  onCancel?: Function;
  onRetry?: Function;
}

export interface SendGiftConfirmProps extends ConfirmProps, PopShellProps {
  game_id: string;
  default_area_id?: number | string;
  default_zone_id?: number | string;
  default_role_id?: number | string;
  onConfirm?: Function;
  onCancel?: Function;
}

// 定义 confirm 函数的类型
export interface ConfirmFunction {
  (options?: any): void;
  info: (options: ConfirmProps) => void;
}
/** Dialog Emits */
export interface DialogEmits {
  (e: "confirm"): void;
  (e: "cancel"): void;
  (e: "close"): void;
}
