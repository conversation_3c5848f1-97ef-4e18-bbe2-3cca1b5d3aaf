import { PlatformTypes, PageParams } from "./common";
import { MessageItemSendUser, UserTitle } from "./user";

export enum AuditType {
  done = 1,
  doing = 2,
}

export enum SortBy {
  latest = "latest",
  hot = "hot",
}

export enum ComposeContentType {
  // 帖子，富文本富文本
  richtext = 1,
  // 图片
  image = 2,
  // 视频
  video = 3,
}

export enum ContentType {
  post = "post",
  image = "image",
  video = "video",
}

export enum ComposeCategories {
  topic = "topic",
  image = "image",
  video = "video",
}

export enum ReportType {
  ad = 1,
  swiping = 2,
  vulgar = 3,
  fake = 4,
  trade = 5,
  others = 6,
}

export enum ReportContentType {
  content = 1,
  comment = 2,
  reply = 3,
}

export interface GetVideoInfoRequestParams {
  video_url: string; // 视频链接，如：https://www.youtube.com/watch?v=nAc6sGXLueY
}

export interface GetVideoInfoResponse {
  platform: PlatformTypes; // 视频链接对应的社媒平台
  video_cover: string; // 视频封面图
  video_desc: string; // 视频描述
  video_id: string; // 视频id
  video_title: string; // 视频标题
}

export enum ComposeContentItemType {
  title = 1,
  text = 2,
  img = 3,
  video = 4,
  audio = 5,
  link = 6,
  attachment_resource = 7,
  charging_resources = 8,
  rich = 9,
}

export interface ComposeContentItem {
  id?: number; // 内容id
  post_id?: number | string; // 动态id
  content: string; // 内容
  type: ComposeContentItemType; // 类型，1标题，2文字段落，3图片地址，4视频地址，5语音地址，6链接地址，7附件资源，8收费资源
  sort?: number; // 排序，越小越靠前

  created_on?: number;
  modified_on?: number;
  deleted_on?: number;
  is_del?: number;
  comment_id?: number;
  intl_openid?: number;
}

export enum IsOriginal {
  yes = 1,
  no = 2,
}

export enum OriginalReprint {
  yes = 1,
  no = 2,
}

export enum Visibility {
  public = 0,
  private = 1,
  friend = 3,
}

export interface ContentItemUser {
  id: number;
  nickname: string;
  username: string;
  status: number;
  avatar: string;
  is_admin: boolean;
  titles: UserTitle; // 称号
  intl_openid: string;
  home_page_url: string;
}

export interface ComposeItem {
  id: number; // 动态id
  intl_openid: string; // 用户id
  user: ContentItemUser; // 用户信息集合
  comment_content: string;
  contents: ComposeContentItem[]; // 内容
  comment_count: number; // 评论数
  collection_count: number; // 收藏数
  upvote_count: number; // 点赞数
  browse_count: number; // 浏览数
  visibility: number;
  is_follow: boolean; // 是否关注
  is_top: number; // 是否置顶
  is_hot: number; // 是否热门
  is_essence: number; // 是否精华
  is_lock: number; // 是否锁定
  type: number; // 类型  1帖子(富文本) 2图文 3 视频文字
  is_star: boolean; // 我是否点赞
  is_comment: boolean; // 我是否评论
  is_collection: boolean; // 我是否收藏
  is_original: number; // 是否原创
  is_audit: AuditType;
  original_url: string; // 原创url
  original_reprint: number; // 是否允许转载
  latest_replied_on: number; // 最新回复时间
  created_on: number; // 创建时间
  modified_on: number; // 修改时间
  tags: {}; // 标签 map类型
  attachment_price: number; // 附件价格(分)
  ip_loc: string; // IP城市地址
  is_del: number; // 是否被删除
  platform: PlatformTypes; // 社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
  game_id: string; // 游戏 id
  area_id: string; // 游戏区 id
  game_name: string; // 游戏名称
  can_delete: boolean; // 是否可以删除
}

export interface ContentRequestParams extends PageParams {
  query?: string;
  type?: string;
  orderby?: SortBy;
}

export interface ContentResponse {
  list: ComposeItem[];
  pager: PageParams;
}

export interface ComposeCommentRequestParams {
  contents: ComposeContentItem[];
  post_id: number; // 动态id
}

export interface ComposeCommentResponse {
  id: number;
  created_on: number;
  modified_on: number;
  deleted_on: number;
  is_del: number;
  post_id: number;
  intl_openid: string;
  is_audit: AuditType;
  game_id: string;
  area_id: string;
  ip: string;
  ip_loc: string;
  content: string;
}

export interface ReplyItem {
  id: number;
  comment_id: number;
  intl_openid: string;
  user: ContentItemUser;
  at_intl_openid: string;
  at_user: ContentItemUser;
  upvote_count: number;
  content: string;
  ip_loc: string;
  is_star: boolean;
  created_on: number;
  modified_on: number;
  is_audit: AuditType;
  reply2reply_id: number;
}

export interface CommentItem {
  id: number; // 评论id
  post_id: number; // 动态id
  intl_openid: string; // 用户id
  user: ContentItemUser; // 用户对象
  contents: ComposeContentItem[]; // 动态内容数组
  replies: {
    list: ReplyItem[]; // 内容回复列表
    pager: PageParams;
  };
  upvote_count: number;
  ip_loc: string;
  is_star: boolean;
  is_audit: AuditType;
  created_on: number;
  modified_on: number;
  // 评论所属的页数
  page?: number;
}
export interface CommentResponse {
  list: CommentItem[];
  pager: PageParams;
}

export interface RepliesResponse {
  list: ReplyItem[];
  pager: PageParams;
}

export interface ComposeReplyRequestParams {
  comment_id: number; // 父评论id
  content: string; // 内容
  at_intl_openid?: string; // @用户id   非必填
  reply_id?: number; // 回复的id
}

export interface ComposeReplyReponse {
  comment_id: number;
  intl_openid: string;
  is_audit: AuditType;
  at_intl_openid: string;
  content: string;
  game_id: string;
  area_id: string;
  ip: string;
  ip_loc: string;
  id: number;
  created_on: number;
  modified_on: number;
  deleted_on: number;
  is_del: number;
  is_parent_del: number;
  reply2reply_id: number;
}

export enum TagType {
  hot = "hot",
  new = "new",
  recommended = "recommended",
}

export interface TopicRequestParams extends PageParams {
  type: TagType;
}

export interface TopciItem {
  id?: number; // 话题id
  intl_openid: string; // 创建用户id
  user?: ContentItemUser; // 创建用户对象
  tag: string; // 话题名称
  quote_num?: number; // 话题引用数
  fans_num?: string; // 话题粉丝数
  introduction?: string;
  hot_num: number; // 话题热度

  game_id?: string; // 游戏id
  area_id?: string; // 大区id
  game?: {
    id: number; // 游戏自增id
    language: {
      id: number;
      game_id: number;
      language: string;
      name: string;
      introduce: string;
    }; // 游戏语言
    game_id: string; // 游戏id
    en_abbreviation: string; // 游戏英文标识
    avatar: string; // 游戏头像
    bg_image_pc: string; // 游戏pc背景
    bg_image_h5: string; // 游戏h5背景
    user_number: number; // 用户数
  }; // 游戏对象
}
export interface TopicResponse {
  list: TopciItem[];
  pager: PageParams;
}

export enum MessageType {
  post = 1, // 动态
  comment = 2, // 动态评论
  reply = 3, // 回复
  message = 4, // 私信
  system = 99, // 系统通知
  like = 5, // 点赞动态
  follow = 6, // 关注
  system_delete_post = 7, // 官方删除动态
  system_delete_comment = 8, // 官方删除动态评论
  like_comment = 9, // 点赞动态评论
  like_reply = 10, // 回复点赞
  system_delelte_info_comment = 12, // 官方删除资讯评论
  like_info_comment = 13, // 资讯评论点赞
  system_ignore_username = 14, // 官方忽略昵称
  system_ignore_remark = 15, // 官方忽略签名
  system_ignore_avatar = 16, // 官方忽略头像
  system_ignore_dynamic = 17, // 官方忽略动态
  system_ignore_comment = 18, // 官方忽略评论
  system_block_comment = 19, // 官方对用户评论封禁
  system_block_dynamic = 20, // 官方对用户动态封禁
  system_block_user = 21, // 官方对用户封禁
  system_delete_username = 22, // 官方删除用户昵称
  system_delete_remark = 23, // 官方删除用户签名
  system_delete_avatar = 24, // 官方删除用户头像
  system_lip_first_add_points = 25, // 官方通知用户首次加积分
  system_letter = 26, // 站内信
}

export interface UserCommentItem {
  id: number; // 评论表id
  post_id: number; // 评论的动态
  intl_openid: string; // 评论的用户
  type: number; // 评论类型 1: 动态评论;2:动态回复;3:资讯评论;4:资讯回复
  post: ComposeItem; // 评论的动态对象 是否关注等状态非真实
  user: MessageItemSendUser; // 发布评论的用户信息
  contents: {
    id: number;
    created_on: number;
    modified_on: number;
    deleted_on: number;
    is_del: number;
    comment_id: number;
    intl_openid: string;
    content: string; // 评论内容
    type: number;
    sort: number;
  }[]; // 评论内容对象[]; // 评论内容
  reply_id: number; // 回复表id 是回复的是否存在值
  reply: {
    id: number;
    created_on: number;
    modified_on: number;
    deleted_on: number;
    is_del: number;
    comment_id: number;
    intl_openid: string;
    is_audit: AuditType;
    at_intl_openid: string;
    content: string; // 回复内容
    game_id: string;
    area_id: string;
    ip: string;
    ip_loc: string;
    is_parent_del: number;
    reply2reply_id: number;
  }; // 回复内容对象
  game_id: string; // 游戏id
  area_id: string; // 大区id
  ip_loc: string;
  created_on: number; // 创建时间
  modified_on: number; // 修改时间
  is_audit: AuditType; // 审批状态
  post_url: string;
  content_text: string;
}

export interface GetCommentParams extends PageParams {
  id: number;
  comment_reply_limit?: number; // 每条评论展示多少条回复数据
}

export interface GetRepliesParams extends PageParams {
  comment_id: number;
}

export interface ReportParams {
  content_id: number;
  content_type: ReportContentType;
  report_type: ReportType;
  reason?: string;
}
