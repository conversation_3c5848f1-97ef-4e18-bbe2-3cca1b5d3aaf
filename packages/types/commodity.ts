export enum CommodityType {
  GiftCard = 1, // 礼品卡
  PrimaryToken = 2, // 一级代币
  GameItem = 3, // 游戏道具
  RealGoods = 4, // 实物
}

export enum CommodityTag {
  None = 0,
  New = 1,
  Hot = 2,
}

export interface Commodity {
  commodity_desc: string;
  commodity_name: string; // 商品名
  commodity_pic_url: string; // 商品图
  commodity_price: number; // 商品价格
  commodity_type: CommodityType; // 商品类型
  commodity_tag: CommodityTag; // 商品角标
  exchange_commodity_id: string; // 商品兑换ID
  commodity_is_discount?: boolean; // 商品是否开启折扣
  commodity_discount_price?: number; // 商品折扣价格
  commodity_left_num?: number; // 商品库存
  commodity_has_left: boolean; // 是否有库存
  commodity_discount_end_time?: number; // 商品折扣结束时间
  game_id: string;
  has_exchange_num: number;
  account_exchange_limit: {
    limit_num: number; // 限制数量
    limit_type: LimitType;
  };
  has_collect: boolean;
  /** 兑换商品类型：0：积分商品，1：钻石商品 */
  exchange_commodity_type: 0 | 1;
  multiple_game_id_list?: string[];
}

/**
 * 限购类型
 * - "gift-card-events" 仅针对礼品卡活动，此数据为本地数据，非后端返回
 */
export type LimitType = "day" | "week" | "month" | "gift-card-events";
