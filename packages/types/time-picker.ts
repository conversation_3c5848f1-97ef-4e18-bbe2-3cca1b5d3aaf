export enum ColumnType {
  year = "year",
  month = "month",
  day = "day",
  hour = "hour",
  minute = "minute",
  second = "second",
}

export interface Column {
  type: ColumnType;
  title: string;
  columnScrollContainerRef: (
    column_scroll_container: HTMLElement,
    options: {
      type: ColumnType;
      date_obj: DateObj;
      columns: Column[];
    }
  ) => any;
  // column_scroll_container: HTMLElement | null;
  items: number[];
}

export interface DateObj {
  year: number;
  month: number;
  day: number;
  hour: number;
  minute: number;
  second: number;
}
