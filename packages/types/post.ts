import { Status } from "./common";
import { ComposeContentType } from "./content";
import { StanceType } from "./stance";
import { UserInfo } from "./user";
import { UnionCard } from "./union";
export interface PostReqParams {
  type: ComposeContentType; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
  visibility: number; // 可见性 0公开 1私密 2好友可见
  tab_id: number; // 板块id
  original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
  is_original: number; // 是否原创 1是 2不是
  original_url: string; // 非原创内容引用的链接
  title: string; // 标题
  content: string; // 内容
  pic_urls: string[]; // 图片
  users: string[]; // @用户
  tags: string[]; // 标签id
}

export interface ComposeNewContentItem {
  language: string; // 帖子语言
  title: string; // 帖子标题
  content: string; // 帖子内容
  ext_info: string; // 帖子扩展内容， json字符串
  platform: string; // 平台
  pic_urls: string[]; // 图片
  content_summary: string; // 内容简介

  // 前端字段
  fe_video_parse_status: Status;
  fe_video_parse_info: GetVideoInfoByURLResponse;
}

export enum ComposeNewAuthoringStatementType {
  /** 无声明 */
  no = 0b0000,
  /**  搬运内容  */
  transport_content = 0b0001,
  /** 原创-不允许转载 */
  repost_prohibited = 0b0010,
  /** 原创-允许转载 */
  repost_allowed = 0b0011,
  /** 全选，不过这里目前是单选，all 只是作为条件判断使用 */
  all = 0b1111,
}

export enum ComposeNewAuthoringStatementRiskRemindType {
  /** 无提醒 */
  no = 0b0000,
  /** 剧透风险 */
  reveal_the_plot_risk = 0b0001,
  /** 内容风险 */
  risk_content = 0b0010,
  /**  */
  /** 待扩展的选项 */
  /**  */
  /** 全选 */
  all = 0b1111,
}

export enum ComposeNewAuthoringStatementAIContentType {
  /** 非AI内容 */
  no = 0,
  /** AI内容  */
  yes = 1,
}

export interface ComposeNewAuthoringStatement {
  creator_statement_type: ComposeNewAuthoringStatementType;
  risk_remind_type: ComposeNewAuthoringStatementRiskRemindType;
  ai_content_type: ComposeNewAuthoringStatementAIContentType;
  original_url?: string;
}

export interface UpdateStatementRequest {
  /** 帖子UUID */
  post_uuid: number;
  /** 创作者声明类型 */
  creator_statement_type: number;
  /** 风险提醒类型 */
  risk_remind_type: number;
  /** AI内容类型 */
  ai_content_type: number;
  /** 原始URL */
  original_url: string;
}

export interface ComposeNewRequestParams {
  plate_id: number; // 平台id
  type: number; //   1帖子(富文本) 2图文 3 外部平台视频动态
  contents: ComposeNewContentItem[]; // 帖子内容
  tags: number[]; // 话题
  publish_on: number; // 定时发布时间
  post_uuid: string;
  need_friend_card: boolean;
  /** 编辑的时候：是否需要更新好友卡，0=不变更；1=需要更新为最新保存角色数据；2=删除好友卡	 */
  need_refresh_friend_card?: number;
  need_guild_card: boolean;
  /** 编辑的时候：是否需要更新公会卡，0=不变更；1=需要更新为最新数据；2=删除公会卡	 */
  need_refresh_guild_card?: number;
  from: number; // 0: 本站；1: nikke 活动
  /** 网红作品转发需要带 ID */
  ch_work_id?: number;
  creator_statement_type: ComposeNewAuthoringStatementType;
  risk_remind_type: ComposeNewAuthoringStatementRiskRemindType;
  ai_content_type: ComposeNewAuthoringStatementAIContentType;
  original_url: string;
}

export interface ComposeNewResopnse {
  post_data: {
    post_uuid: string; // 动态唯一id
    user: {
      all_post_num: string; // 所有动态数量
      avatar: string; // 头像
      fans_num: string; // 粉丝数
      follow_num: string; // 关注数
      home_page_url: string; // 主页url
      id: string; // 主键id
      intl_openid: string; // 用户openid
      is_admin: boolean; // 是否是管理员
      language: string; // 语言
      post_num: string; // 动态数量--过审
      remark: string; // 签名
      status: number; // 状态
      titles: {
        area_id: string; // 区域id
        game_id: string; // 游戏id
        id: number; // 绑定称号id
        status: number; // 是否生效1：生效2：失效
        title: {
          area_id: string; // 区域id
          avatar: string; // 称号icon
          down_time: string; // 下架时间
          game_id: string; // 游戏id
          id: number; // 称号id
          init_host: string; // 初始热度
          language: {
            id: string; // 语言id
            introduce: string; // 描述
            language: string; // 语言
            title: string; // 称号
            title_id: number; // 称号id
          }; // 语言
          possess_num: number; // 拥有数量
          status: number; // 状态 1:待上架 2:已上架
          up_time: number; // 上架时间
        }; // 称号信息
      }; // 称号
      username: string; // 昵称
    }; // 发布动态用户信息
    power_num: number; // 权重
    comment_count: number; // 评论数量
    collection_count: number; // 收藏数量
    upvote_count: number; // 点赞数
    browse_count: number; // 浏览数
    is_top: number; // 是否置顶
    top_sort: number; // 置顶顺序值
    top_on: number; // 置顶生效时间
    is_star: number; // 是否点赞
    is_comment: number; // 是否评论
    is_follow: number; // 是否关注
    is_essence: number; // 是否精华
    essence_on: number; // 精华生效时间
    type: string; //  1帖子(富文本) 2图文 3 外部平台视频动态
    can_delete: boolean; // 是否可以删除
    is_original: number; // 是否原创
    original_url: string; // 非原创内容引用的链接
    original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
    latest_replied_on: number; // 最后回复时间
    created_on: number; // 创建时间
    modified_on: number; // 更新时间
    game_id: string; // 游戏id
    game_name: string; // 游戏名称
    area_id: string; // 区域id
    platform: string; // 平台
    title: string; // 标题
    content: string; // 内容
    pic_urls: string; // 图片链接，逗号分隔
    tags: {
      id: number; // 标签id
      name: string; // 标签名称
    }[]; // 标签
    is_official: number; // 是否官方帖子
    publish_on: number; // 定时发布时间
  };
}

export interface ComposeRequestParams {
  type: ComposeContentType; // 帖子类型：  1帖子(富文本) 2图文 3 外部平台视频动态
  plate_id: number; // 板块id
  language: string; // 语言，如en
  visibility: number; // 可见性 0公开 1私密 2好友可见
  original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
  is_original: number; // 是否原创 1是 2不是
  original_url: string; // 非原创内容引用的链接
  title: string; // 标题
  content: string; // 内容
  platform?: string; // 视频类型动态时填写。社媒平台渠道：lip，youtube，youtubeshort，facebook，twitter，tiktok
  pic_urls: string[]; // 图片
  users: string[]; // @用户（预留字段，目前用不上）
  tags: number[]; // 标签id列表
  ext_info: string;
  content_summary: string;
}

export interface ComposeResopnse {
  post_data: {
    post_uuid: string; // 动态唯一id
    user: {
      all_post_num: string; // 所有动态数量
      avatar: string; // 头像
      fans_num: string; // 粉丝数
      follow_num: string; // 关注数
      home_page_url: string; // 主页url
      id: string; // 主键id
      intl_openid: string; // 用户openid
      is_admin: boolean; // 是否是管理员
      language: string; // 语言
      post_num: string; // 动态数量--过审
      remark: string; // 签名
      status: number; // 状态
      titles: {
        area_id: string; // 区域id
        game_id: string; // 游戏id
        id: number; // 绑定称号id
        status: number; // 是否生效1：生效2：失效
        title: {
          area_id: string; // 区域id
          avatar: string; // 称号icon
          down_time: string; // 下架时间
          game_id: string; // 游戏id
          id: number; // 称号id
          init_host: string; // 初始热度
          language: {
            id: string; // 语言id
            introduce: string; // 描述
            language: string; // 语言
            title: string; // 称号
            title_id: number; // 称号id
          }; // 语言
          possess_num: number; // 拥有数量
          status: number; // 状态 1:待上架 2:已上架
          up_time: number; // 上架时间
        }; // 称号信息
      }; // 称号
      username: string; // 昵称
    }; // 发布动态用户信息
    power_num: number; // 权重
    comment_count: number; // 评论数量
    collection_count: number; // 收藏数量
    upvote_count: number; // 点赞数
    browse_count: number; // 浏览数
    is_top: number; // 是否置顶
    top_sort: number; // 置顶顺序值
    top_on: number; // 置顶生效时间
    is_star: number; // 是否点赞
    is_comment: number; // 是否评论
    is_follow: number; // 是否关注
    is_essence: number; // 是否精华
    essence_on: number; // 精华生效时间
    type: string; //  1帖子(富文本) 2图文 3 外部平台视频动态
    can_delete: boolean; // 是否可以删除
    is_original: number; // 是否原创
    original_url: string; // 非原创内容引用的链接
    original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
    latest_replied_on: number; // 最后回复时间
    created_on: number; // 创建时间
    modified_on: number; // 更新时间
    game_id: string; // 游戏id
    game_name: string; // 游戏名称
    area_id: string; // 区域id
    platform: string; // 平台
    title: string; // 标题
    content: string; // 内容
    pic_urls: string; // 图片链接，逗号分隔
    tags: {
      id: number; // 标签id
      name: string; // 标签名称
    }[]; // 标签
  };
}

export enum SearchType {
  time, // 默认按时间降序
  tag, // 根据tag搜索
  hot, // 根据热度搜索
  keyword, // 根据关键字搜索
}

export enum OrderBy {
  time = 1, // 时间排序
  hot = 2, // 热度排序
}

export interface PostsParams {
  search_type?: SearchType;
  plate_id?: number; // 板块id
  plate_unique_id?: string; // 板块唯一标识
  tag_id?: number; // 标签id
  need_all_region?: boolean; // 是否需要全部国家，false则会查询当前用户语言对应的动态数据
  keyword?: string; // 搜索字符，只有search_type=SearchType.keyword的时候才会生效
  order_by: OrderBy;
  page_type?: number; // 查询页面类型0-下一页1-上一页
  previousPageCursor?: string; // 上一页游标
  nextPageCursor?: string; // 下一页游标
  limit: string; // 一页多少条数据，默认10条
  /** 仅获取 youtube event 时传递 */
  platform?: "youtube";
  /** creatorhub赛道id */
  rank_id?: number;
  /** creatorhub活动任务id */
  task_id?: number;
  /** 仅获取指定国家动态 */
  regions: string[] | undefined;
}

export interface Tag {
  id: number;
  name: string;
}

export interface PostItem {
  intl_openid: string; // 用户openid
  post_uuid: string; // 动态唯一id
  power_num?: number; // 权重
  comment_count: number; // 评论数量
  collection_count: number; // 收藏数量
  upvote_count: number; // 点赞数
  browse_count: number; // 浏览数
  forward_count: number; // 转发数量
  is_top: number; // 是否置顶
  top_sort: number; // 置顶顺序值
  top_on: number; // 置顶生效时间
  is_collection: boolean; // 是否收藏
  is_star: boolean; // 是否点赞
  is_comment: boolean; // 是否评论
  is_follow: boolean; // 是否关注
  is_mutual_follow: boolean; // 是否互关
  is_essence: boolean; // 是否精华
  is_mine: boolean; // 是否是自己创建的帖子
  is_audit: PostAuditStatus; // 是否审核通过
  essence_on: number; // 精华生效时间
  type: number; // 1帖子(富文本) 2图文 3 外部平台视频动态
  can_delete: boolean; // 是否可以删除
  is_original: number; // 是否原创
  original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
  latest_replied_on: number; // 最后回复时间
  created_on: number; // 创建时间
  modified_on: number; // 更新时间
  game_id: string; //游戏id
  game_name: string; // 游戏名称
  area_id: string; // 区域id
  platform: string; // 平台
  title: string; // 标题
  plate_id: number; // 板块id
  content: string; // 内容
  content_summary: string; // 动态摘要，前端使用
  pic_urls: string[]; // 图片链接，逗号分隔
  ext_info: string; // 扩展字段
  user: UserInfo;
  tags: Tag[];
  is_deleted?: boolean; // 前端控制是否删除
  is_exposed?: boolean; // 前端控制是否曝光
  upvote_map: {
    [key: number]: number;
  };
  my_upvote: {
    is_star: boolean;
    upvote_type: StanceType;
  };
  rank_info?: { id: number; rank_name: string };
  task_info?: { id: number; task_name: string; task_id: number };
  plate_name?: string;
  /** 是否官方帖子 */
  is_official: boolean;
  /** 发布时间 */
  publish_on: number;
  /** 前端自定义字段：图片信息 */
  fe_image_info: {
    width: number;
    height: number;
    url: string;
  };
  can_edit?: boolean;
  can_edit_statement: boolean;
  creator_statement_type: ComposeNewAuthoringStatementType;
  risk_remind_type: ComposeNewAuthoringStatementRiskRemindType;
  ai_content_type: ComposeNewAuthoringStatementAIContentType;
  original_url: string;
  show_vote_icon: boolean;
  show_guild_icon: boolean;
  show_friend_icon: boolean;
  language: string;
}

export enum PostAuditStatus {
  success = 1, // 审核通过
  pending = 2, // 未审核
}

export interface GetVideoInfoByURLRequestParams {
  video_url: string; // 视频链接
  platform: string; // // 视频链接类型：youtube、tiktok
}

export interface GetVideoInfoByURLResponse {
  video_id: string;
  video_title: string;
  video_desc: string;
  video_cover: string;
  platform: string;
}

export interface PostDetail {
  post_uuid: string; // 动态唯一id
  user: UserInfo; // 发布动态用户信息
  power_num: number; // 权重
  comment_count: number; // 评论数量
  collection_count: number; // 收藏数量
  upvote_count: number; // 点赞数
  browse_count: number; // 浏览数
  is_top: number; // 是否置顶
  top_sort: number; // 置顶顺序值
  top_on: number; // 置顶生效时间
  is_collection: boolean; // 是否收藏
  is_mine: boolean; // 是否是自己的动态
  is_star: boolean; // 是否点赞
  is_comment: boolean; // 是否评论
  is_follow: boolean; // 是否关注
  is_mutual_follow: boolean; // 是否互相关注
  is_essence: number; // 是否精华
  essence_on: number; // 精华生效时间
  type: ComposeContentType; //  1帖子(富文本) 2图文 3 外部平台视频动态
  can_delete: boolean; // 是否可以删除
  can_report: boolean; // 是否可以举报
  can_move: boolean; // 是否可以移动
  can_update_tags: boolean; // 是否可以编辑标签
  is_original: number; // 是否原创
  original_reprint: number; // 原创是否允许转载 1 允许 2 不允许
  latest_replied_on: number; // 最后回复时间
  created_on: number; // 创建时间
  modified_on: number; // 更新时间
  game_id: string; // 游戏id
  game_name: string; // 游戏名称
  area_id: string; // 区域id
  platform: string; // 平台
  title: string; // 标题
  content: string; // 内容
  intl_openid: string; // 用户openid
  pic_urls: Array<string>; // 图片链接，逗号分隔
  tags: {
    id: number; // 标签id
    name: string; // 标签名称
  }[]; // 标签
  ext_info: string;
  forward_count: number;
  is_forward: boolean;
  upvote_map: {
    [key: number]: number;
  };
  my_upvote: {
    is_star: boolean;
    upvote_type: StanceType;
  };
  is_audit: number;
  plate_id: number;
  original_title?: string;
  original_content?: string;
  translate_title?: string;
  translate_content?: string;
  has_translated?: boolean;
  language: string;
  content_languages: {
    content: string; // 帖子内容
    content_summary: string; // 帖子简介
    ext_info: string; // 帖子扩展，json字符串
    is_original: number; // 是否原创
    language: string; // 语言
    original_reprint: number; // 是否可以转发
    original_url: string; // 帖子原文链接
    pic_urls: string[]; // 帖子图片
    platform: string; // 平台
    title: string; // 帖子标题
    id: number; // 主键id--更新需要带上
  }[];
  /** 是否可以编辑 */
  can_edit: boolean;
  /** 是否是官方账号发布 */
  is_official: boolean;
  friend_card: {
    role_name: string; // 角色名
    area_id: string; // 区服id
    icon: number; // 头像id
    is_send_friend_request: boolean; // 是否发起过好友请求，true=已发起，false=未发起；限制24小时加一次
    show_friend_card: boolean; // 是否配置了该帖子展示加好友的游戏名片信息。ture=展示；false=不展示
    player_level: number; // 游戏等级
    team_combat: number; // 战斗力
    show_friend_card_detail: boolean; // 是否能点击用户好友卡打开shiftypad抽屉页面
  };
  guild_card?: UnionCard;
  plate_name: string;
  can_edit_statement: boolean;
  creator_statement_type: ComposeNewAuthoringStatementType;
  risk_remind_type: ComposeNewAuthoringStatementRiskRemindType;
  ai_content_type: ComposeNewAuthoringStatementAIContentType;
  original_url: string;
  is_original_content: boolean;
  original_language: string;
}

export enum ActionType {
  report = "report",
  edit = "edit",
  delete = "delete",
  move = "move",
  top_comment = "top_comment",
  bottom_comment = "bottom_comment",
  copy_comment_id = "copy_comment_id",
  hashtag_manage = "hashtag_manage",
  authoring_statement = "authoring_statement",
}

export interface ActionItem {
  icon: string;
  text: string;
  type: ActionType;
}

export enum ReportContentType {
  content = 1,
  comment = 2,
  reply = 3,
}

export enum ReportType {
  ad = 1,
  swiping = 2,
  vulgar = 3,
  fake = 4,
  trade = 5,
  others = 6,
}
export interface ReporListItem {
  text: string;
  type: ReportType;
}

export interface ContentReportRequestParams {
  content_uuid: string; // 举报内容id，包括动态id、动态评论id、评论回复的id
  content_type: number; // 举报的内容类型：1是动态，2是评论，3是评论回复
  report_type: number; // 举报的类型：1是广告，2是涉嫌刷屏，3涉嫌低俗言论，4涉嫌虚假消息，5涉嫌游戏服务或交易，6其他
  reason?: string;
}

export interface MovePostRequestParams {
  post_uuid: string;
  language: string;
  plate_id?: string;
}

export enum TranslateContentType {
  post = 1,
  comment = 2,
}

export interface TranslateContentRequestParams {
  content_uuid: string;
  type: TranslateContentType;
  language: string;
}

export enum PostTranslateStatus {
  un_translate = "un_translate",
  translating = "translating",
  translated = "translated",
}

export interface DeleteListItem {
  text: string;
  type: PostDeleteReason;
}

export enum PostDeleteReason {
  GarbageContent = 1, // 垃圾内容（与妮姬无关的内容/重复性内容等）
  DisruptiveCommunication = 2, // 破坏社区和谐沟通（人身攻击/引战/涉政/歧视等）
  IllegalContent = 3, // 违法、暴力、色情等各种违法或恶意内容
  GameBalanceImpact = 4, // 涉及BUG利用/游戏破解、修改等影响游戏平衡的内容
  UnverifiedInfo = 5, // 解包内容或散步未经证实的更新资讯、虚假内容等
  AccountTrading = 6, // 账号转让/账号买卖/账号估价/代练等交易内容
  Advertising = 7, // 广告行为内容
  CopyrightViolation = 8, // 内容违反社区原创/转载相关规定
  OtherViolations = 9, // 其他违规内容
}

export interface PostChangeTagBindRequest {
  /** 话题id数组 */
  tag_ids: number[];
  /** 帖子UUID */
  post_uuid: string;
}

export interface PostChangeTagBindResponse {
  /** 状态码 */
  code: number;
  /** 状态码类型 */
  code_type: number;
  /** 消息 */
  msg: string;
  /** 数据 */
  data: {};
}
