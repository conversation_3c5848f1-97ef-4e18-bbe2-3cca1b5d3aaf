export enum IntlRenderMode {
  inline = "inline",
  modal = "modal",
}

export interface BasicUserAuthInfo {
  open_id: string;
  token: string;
  channel_id: number;
  game_id: string; // intl gameid
}

export enum INTL_RET_CODE {
  SUCCESS = 0,
  OPEN_ID_IS_BOUND = 1403,
  USER_CANCEL_POPUP = 808098100,
}

export enum IntlChannel {
  Facebook = 4,
  Google = 6,
  Twitter = 9,
  Line = 14,
  Apple = 15,
  Lip = 131,
}
