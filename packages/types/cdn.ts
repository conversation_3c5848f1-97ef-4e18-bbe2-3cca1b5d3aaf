import { Grayscale } from "./grayscale";
import { JumpUrlWhiteList } from "./jump-url-white-list";

export interface CDNGameItem {
  game: string;
  label: string;
  value: string;
  game_i18n_key: string;
  label_i18n_key: string;
  intl_sdk_env: {
    prod: string;
  };
  cms_game_id: number;
}

export interface CDNConfigs {
  games: Array<CDNGameItem>;
  grayscale: Grayscale;
  performance: {
    lcp: {
      images: Array<string>;
    };
  };
  jump_url_white_list: JumpUrlWhiteList;
  laboratory: {
    midas: {
      openids: Array<string>;
      url: Record<string, string>;
    };
  };
  image: {
    thumbnail_quality: number;
  };
  annual_event: {
    value: string;
    configs: Array<{
      value: string;
      bg_config: {
        bg_image: string;
        bg_color: string;
      };
    }>;
  };
}

export interface VersionCDNConfigs {
  values: Array<{
    label: string;
    value: string;
  }>;
}
