// import { ConfirmProps } from "./confirm";

export interface Role {
  area_id: number; // 大区id
  game_id: string; // 游戏id
  game_name: string; // 游戏名称
  icon?: number; // 游戏内 头像id
  plat_id?: string; // 平台id
  role_id: string; // 角色id
  role_name: string; // 角色名
  zone_id?: string; // 小区id
}
export interface GameRegion {
  area_id: number;
  area_name: string;
  zone_list?: {
    zone_id: number;
    zone_name: string;
    plat_list?: {
      plat_id: string;
      plat_name: string;
    }[];
  }[];
}

/* export interface GamesFilterProps extends ConfirmProps {
  games: Array<{
    game: string;
    label: string;
    value: string;
    intl_sdk_env: {
      prod: string;
    };
    cms_game_id: number;
    avatar: string;
  }>;
  selected_game_id_list: string[]; // 选中的游戏 id
  title_text?: string;
  selected_text?: string;
  region_text?: string;
} */
