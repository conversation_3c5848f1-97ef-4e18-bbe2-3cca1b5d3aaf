// types
import { UploadFileType, CosCloudType } from "../../types/common";
import { CosSts } from "../../types/cos";

// configs
import { API_SS_UGC_COS_STS } from "../../configs/api";

// utils
import { Tencent } from "./tencent";
import { Aws } from "./aws";
import { AxiosInstance } from "axios";

export class Client {
  private type: CosCloudType | undefined;
  private config: CosSts | undefined;
  private client: Tencent | Aws | null = null;
  private http: AxiosInstance;

  constructor(http: AxiosInstance) {
    this.http = http;
  }

  public async getToken() {
    const res: CosSts = await this.http.get(API_SS_UGC_COS_STS);

    this.config = res;
    this.type = res.cloud_type;
    this.createClient();
  }

  public uploadFile(params: { file: File; type: UploadFileType }) {
    return this.client?.uploadFile(params);
  }

  private createClient() {
    if (!this.config || this.client) {
      return;
    }

    this.client =
      this.type === CosCloudType.awss3
        ? //
          new Aws(this.config!)
        : //
          new Tencent(this.config!);
  }
}
