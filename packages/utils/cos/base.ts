// types
import { UploadFileType } from "../../types/common";

// utils
import { v4 as uuidv4 } from "uuid";

export class Base {
  private base = "standalonesite/ugc";

  constructor(prefix = "") {
    this.base = `${prefix}${this.base}`;
  }

  /**
   * @description 生成文件上传路径
   */
  protected getObjectKey(params: { file: File; type: UploadFileType }): string {
    const dir = `${this.base}/${params.type}`;
    // 获取文件后缀名
    const ext = params.file.type.split("/")[1];
    const file_name = `${dir}/${uuidv4()}.${ext}`;
    return file_name;
  }
}
