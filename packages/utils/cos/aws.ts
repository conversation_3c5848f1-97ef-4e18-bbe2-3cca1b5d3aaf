// types
import { CosSts } from "../../types/cos";
import { UploadFileType } from "../../types/common";

// utils
import { S3Client, HeadObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { Base } from "./base";

export class Aws extends Base {
  private client: S3Client | undefined;
  private config: CosSts;

  constructor(config: CosSts) {
    super();
    this.config = config;
  }

  private async createClient() {
    // 存在客户端且 sts 未过期
    if (this.client && !this.checkIfExpired()) {
      return;
    }
    this.client = new S3Client({
      credentials: {
        accessKeyId: this.config.tmp_secret_id,
        secretAccessKey: this.config.tmp_secret_key,
        sessionToken: this.config.token,
      },
      region: this.config.region,
    });
  }

  // 上传文件
  public async uploadFile(params: { file: File; type: UploadFileType }): Promise<string | void> {
    await this.createClient();
    const key = this.getObjectKey(params);
    // const is_exit = await this.checkIfExists(key);
    // if (is_exit) {
    //   throw new Error("File exits");
    // }
    const command = new PutObjectCommand({
      Body: params.file,
      Bucket: this.config.bucket,
      Key: key,
      ContentType: params.type,
    });
    await this.client?.send(command);
    return `https://${this.config.cdn_domain}/${key}`;
  }

  public async checkIfExists(key: string): Promise<boolean> {
    if (!this.config || !this.client) {
      return false;
    }
    const command = new HeadObjectCommand({
      Bucket: this.config.bucket,
      Key: key,
    });
    try {
      await this.client.send(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  // 判断密钥是否过期
  public checkIfExpired(): boolean {
    if (!this.config) {
      return true;
    }
    const expiration = new Date(this.config.expiration).getTime();
    const now = Date.now();
    return expiration <= now + 10000;
  }
}
