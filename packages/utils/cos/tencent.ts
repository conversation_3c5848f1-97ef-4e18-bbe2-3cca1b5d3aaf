// types
import { CosSts } from "../../types/cos";
import { UploadFileType } from "../../types/common";

// utils
import COS from "cos-js-sdk-v5";
import { Base } from "./base";

export class Tencent extends Base {
  private config: CosSts;
  private client: COS | null = null;

  constructor(config: CosSts) {
    super("/");
    this.config = config;
    this.createClient();
  }

  private createClient() {
    this.client = new COS({
      Domain: this.config.bucket_domain || this.config.cdn_domain,
      Protocol: "https:",
      getAuthorization: async (_, callback) => {
        callback({
          TmpSecretId: this.config.tmp_secret_id,
          TmpSecretKey: this.config.tmp_secret_key,
          XCosSecurityToken: this.config.token,
          StartTime: this.config.start_time,
          ExpiredTime: this.config.expired_time,
        });
      },
    });
  }

  // 上传文件
  public async uploadFile(params: { file: File; type: UploadFileType }): Promise<string | void> {
    const key = this.getObjectKey(params);
    // const is_exit = await this.checkIfExists(key);
    // if (is_exit) {
    //   throw new Error("File exits");
    // }
    const res = await this.client?.uploadFile({
      Bucket: this.config.bucket,
      Region: this.config.region,
      Key: key,
      Body: params.file,
      SliceSize: 1024 * 1024 * 5,
      Headers: {
        "Content-Disposition": "attachment",
      },
    });
    if (res?.statusCode !== 200) {
      throw new Error("uploadFile failed");
    }
    return `https://${this.config.cdn_domain}${key}`;
  }

  public async checkIfExists(key: string): Promise<boolean> {
    if (!this.config || !this.client) {
      return false;
    }
    return new Promise((resolve, _reject) => {
      this.client?.headObject(
        {
          Bucket: this.config.bucket,
          Region: this.config.region,
          Key: key,
        },
        (err) => {
          if (err) {
            resolve(false);
          } else {
            resolve(true);
          }
        }
      );
    });
  }
}
