import axios, { AxiosRequestConfig } from "axios";
import { get } from "lodash-es";
import { useLSLoginMetaStorage } from "./storage";
import {
  getClientType,
  isInIntlBrowser,
  // getApiBaseUrl,
  // getEnv
} from "./tools";
import { urlSearchObjectify } from "./qs";

import { getStandardizedGameId, getStandardizedLang } from "./standard";
import { TlogConfig } from "../types/tlog";
import { INTL_GLOBAL_GAME_ID } from "../configs/intl";
import { STORAGE_LS_TLOG_EVENTS_QUEUE } from "../configs/storage";

const { getOpenID } = useLSLoginMetaStorage();

const http = axios.create({
  // getApiBaseUrl(getEnv()),
  // TODO: 先写死
  baseURL: "https://na-report.playerinfinite.com/api",
  headers: {
    "X-Areaid": "na",
  },
});

const onRequest = (config: AxiosRequestConfig): any => {
  const query = urlSearchObjectify();
  const lang = getStandardizedLang();

  if (config.headers) {
    Object.assign(config.headers, {
      "X-Language": lang,
      "X-Source": "lip",
      "X-Gameid": "30035",
    });
  }

  const intl_game_id = getStandardizedGameId() || INTL_GLOBAL_GAME_ID;

  const default_ext_content = {
    open_id: getOpenID(),
    client_type: getClientType(),
    from: query.from,
    lang,
    url: window.location.href,
    scene: isInIntlBrowser() ? "inner_game" : "outer",
    platform: get(navigator, "userAgentData.platform") || get(navigator, "platform"),
    user_agent: navigator.userAgent,
    ref_url: document.referrer,
    intl_game_id,
  };

  // 批量上报
  if (config.url?.includes("ReportStatInfoBatch")) {
    config.data = Object.assign({ original_game_id: intl_game_id }, config.data);
    config.data.reports = config.data.reports.map((report: any) => {
      return {
        ...report,
        ext_content: JSON.stringify({ ...default_ext_content, ...report.ext_content }),
      };
    });
  } else {
    // 单个上报
    config.data = Object.assign({ original_game_id: intl_game_id, ext_content: {} }, config.data);
    config.data.ext_content = JSON.stringify({
      ...default_ext_content,
      ...config.data.ext_content,
    });
  }

  return config;
};

http.interceptors.request.use(onRequest);

/**
 * 日志事件接口
 */
interface TlogEvent {
  action: string;
  sub_action: string;
  original_game_id?: string;
  ext_content?: Record<string, any>;
  /** 时间戳，单位：秒 */
  timestamp: number;
}

/**
 * Tlog 管理器，负责事件队列的管理和批量上报
 */
class TlogManager {
  private queue: TlogEvent[] = [];
  private timer: number | null = null;
  private is_flushing = false;
  private readonly storage_key = STORAGE_LS_TLOG_EVENTS_QUEUE;
  private readonly flush_interval = 1000; // 1 秒
  private readonly max_batch_size = 10; // 每批最多 10 个事件
  private readonly max_retention_time = 7 * 24 * 60 * 60 * 1000; // 7 天

  constructor() {
    this.initializeManager();
  }

  private initializeManager(): void {
    this.loadQueue();
    this.startTimer();
    this.setupEventListeners();
  }

  private startTimer(): void {
    if (this.timer === null) {
      this.timer = window.setInterval(() => this.flush(), this.flush_interval);
    }
  }

  private stopTimer(): void {
    if (this.timer !== null) {
      window.clearInterval(this.timer);
      this.timer = null;
    }
  }

  private setupEventListeners(): void {
    window.addEventListener("pagehide", () => {
      this.stopTimer();
      this.saveQueue();
    });

    window.addEventListener("visibilitychange", () => {
      if (document.visibilityState === "hidden") {
        this.flush();
      }
    });
  }

  public addEvent(event: Omit<TlogEvent, "timestamp">): void {
    const eventWithTimestamp: TlogEvent = {
      ...event,
      timestamp: Math.floor(Date.now() / 1000),
    };
    this.queue.push(eventWithTimestamp);

    // 如果队列长度达到了最大批量大小，立即触发上报
    if (this.queue.length >= this.max_batch_size) {
      this.flush();
    }
  }

  public async flush(): Promise<void> {
    if (this.is_flushing || this.queue.length === 0) {
      return;
    }

    try {
      this.is_flushing = true;

      // 清理过期的事件
      this.cleanExpiredEvents();

      // 如果清理后队列为空，停止处理
      if (this.queue.length === 0) {
        this.is_flushing = false;
        return;
      }

      // 取出当前队列中的所有事件
      const events_to_send = [...this.queue];

      // 发送批量请求
      const { host } = window.location;
      if (/localhost/i.test(host) || /127\.0\.0\.1/.test(host)) {
        // 本地环境不发送请求，但清空队列
        this.queue = [];
        this.saveQueue();
        this.is_flushing = false;
        return;
      }

      // 发送批量上报请求
      await http.post("/gpts_community.report_svr.ReportSvr/ReportStatInfoBatch", {
        reports: events_to_send,
      });

      // 上报成功，从队列中移除已上报的事件
      this.queue = this.queue.filter(
        (event) => !events_to_send.some((sent) => sent.timestamp === event.timestamp)
      );

      // 保存更新后的队列
      this.saveQueue();
    } catch (error) {
      console.error("Failed to flush tlog events:", error);
      // 上报失败，保留队列中的事件，下次继续尝试
    } finally {
      this.is_flushing = false;
    }
  }

  private cleanExpiredEvents(): void {
    const now = Date.now();
    this.queue = this.queue.filter(
      (event) => now - event.timestamp * 1000 < this.max_retention_time
    );
  }

  public saveQueue(): void {
    try {
      localStorage.setItem(this.storage_key, JSON.stringify(this.queue));
    } catch (error) {
      console.error("Failed to save tlog events queue to localStorage:", error);
    }
  }

  public loadQueue(): void {
    try {
      const saved_queue = localStorage.getItem(this.storage_key);
      if (saved_queue) {
        this.queue = JSON.parse(saved_queue);
        // 清理可能的过期事件
        this.cleanExpiredEvents();
      }
    } catch (error) {
      console.error("Failed to load tlog events queue from localStorage:", error);
      // 如果加载失败，创建新的空队列
      this.queue = [];
    }
  }
}

// 创建 TlogManager 单例
const tlog_manager = new TlogManager();

/**
 * 手动触发日志事件上报
 * @returns 返回一个 Promise，上报完成后 resolve
 */
export const flushTlogEvents = (): Promise<void> => {
  return tlog_manager.flush();
};

/**
 * tlog 上报
 */
export const tlog = async (params: {
  action: string;
  sub_action: string;
  original_game_id?: string;
  ext_content?: Record<string, any>;
}) => {
  const { host } = window.location;
  if (/localhost/i.test(host) || /127\.0\.0\.1/.test(host)) {
    return;
  }

  // 单个上报
  // await http.post("/gpts_community.report_svr.ReportSvr/ReportStatInfo", params);

  // 批量上报: 将事件添加到队列中，而不是直接发送请求
  tlog_manager.addEvent(params);
};

/**
 * 通用上报（批量上报）
 *
 * @description 先在 types/tlog.ts 中添加配置，然后在业务中通过方法直接调用
 * - 如果需要立即触发上报，请调用 `report.flush()`
 */
export const report = new Proxy(
  {} as {
    readonly [Action in keyof TlogConfig]: {
      readonly [SubAction in keyof TlogConfig[Action]]: void extends TlogConfig[Action][SubAction]
        ? () => Promise<void>
        : (ext_content: TlogConfig[Action][SubAction]) => Promise<void>;
    };
  } & {
    /** 立即触发上报 */
    flush: () => Promise<void>;
  },
  {
    get(_target, action) {
      if (action === "flush") {
        return flushTlogEvents;
      }
      return new Proxy(
        {},
        {
          get(_target, sub_action) {
            return async (ext_content: any) => {
              if (typeof action === "symbol" || typeof sub_action === "symbol") return;
              await tlog({ action, sub_action, ext_content });
            };
          },
        }
      );
    },
  }
);
