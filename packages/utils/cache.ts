/**
 * 实现LRU（Least Recently Used）缓存策略的键值对存储结构
 * @class
 * @template K - 键的类型
 * @template V - 值的类型
 *
 * @description
 * 该缓存通过组合哈希表(Map)和双向链表实现O(1)时间复杂度操作：
 * 1. 哈希表提供快速键值查找能力[2,3](@ref)
 * 2. 双向链表维护访问顺序，尾部为最近访问节点，头部为最久未使用节点[2,7](@ref)
 *
 * 主要特性：
 * - 容量限制：当缓存达到容量上限时自动淘汰最久未使用的条目
 * - 访问更新：每次get/put操作都会将对应节点移动到链表尾部
 * - 自动淘汰：插入新元素时若超出容量，自动移除链表头部节点[2,7](@ref)
 */
export class LRUCache<K, V> {
  private map: Map<K, ListNode<K, V>> = new Map();
  private tail: ListNode<K, V> | null = null;
  private head: ListNode<K, V> | null = null;
  private count = 0;

  constructor(readonly capacity: number) {}

  get(key: K): V | null {
    if (this.map.has(key)) {
      const node = this.map.get(key)!;
      this.remove(node);
      this.toTail(node);
      return node.val;
    }
    return null;
  }

  delete(key: K): void {
    if (this.map.has(key)) {
      const node = this.map.get(key)!;
      this.count -= 1;
      this.remove(node);
      this.map.delete(node.key);
    }
  }

  put(key: K, val: V): void {
    if (!this.map.has(key)) {
      const new_node = this.createNode(key, val);
      this.count += 1;
      this.toTail(new_node);
      this.map.set(key, new_node);
      if (this.count > this.capacity) {
        this.map.delete(this.head!.key);
        this.remove(this.head!);
        this.count -= 1;
      }
    } else {
      // update cache
      const node = this.map.get(key);
      node!.val = val;
      this.remove(node!);
      this.toTail(node!);
    }
  }

  private createNode(key: K, val: V) {
    return {
      pre: null,
      next: null,
      val,
      key,
    };
  }

  private remove(node: ListNode<K, V>) {
    if (node.pre) {
      node.pre.next = node.next;
      if (!node.next) {
        this.tail = node.pre;
      }
    } else {
      this.head = node.pre;
    }
    if (node.next) {
      node.next.pre = node.pre;
      if (!node.pre) {
        this.head = node.next;
      }
    } else {
      this.tail = node.pre;
    }
    node.pre = null;
    node.next = null;
  }

  private toTail(node: ListNode<K, V>) {
    if (this.tail) {
      node.next = null;
      node.pre = this.tail;
      this.tail.next = node;
      this.tail = node;
    } else {
      this.head = node;
      this.tail = node;
      this.tail.next = null;
    }
  }
}

type ListNode<K, V> = {
  pre: ListNode<K, V> | null;
  next: ListNode<K, V> | null;
  val: V;
  key: K;
};
