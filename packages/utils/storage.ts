import {
  STORAGE_LS_LOGIN_META,
  // STORAGE_LS_VERSION,
  STORAGE_SS_INDEX_DB_NAME,
  STORAGE_SS_OBJECT_STORE_NAME,
} from "../configs/storage";
import { StoreItem, IndexDBCacheItem } from "../types/storage";
import { noop } from "lodash-es";
import { isIndexedDBSupported, safeJSONParse } from "./tools";

export const useSSStorage = <T>(key: string) => {
  const getStorage = (): T | null => safeJSONParse(sessionStorage.getItem(key)!, null);
  const setStorage = (value: T) => sessionStorage.setItem(key, JSON.stringify(value));
  const removeStorage = () => sessionStorage.removeItem(key);

  return {
    getStorage,
    setStorage,
    removeStorage,
  };
};

export const useLSStorage = () => {
  const getStorage = (key: string) => localStorage.getItem(key);
  const setStorage = (key: string, value: any) => localStorage.setItem(key, value);
  const removeStorage = (key: string) => localStorage.removeItem(key);

  // 带期限的缓存
  const setItemWithExpiry = <T>(key: string, value: T, expiry: Date) => {
    const item: StoreItem<T> = {
      value: value,
      expiry: expiry.getTime(),
    };
    setStorage(key, JSON.stringify(item));
  };

  const getItemWithExpiry = <T>(key: string): T | null => {
    const item_str = getStorage(key);
    if (!item_str) {
      return null;
    }
    const item: StoreItem<T> = JSON.parse(item_str);
    const now = new Date();
    if (now.getTime() > item.expiry) {
      removeStorage(key);
      return null;
    }
    return item.value;
  };

  return {
    getStorage,
    setStorage,
    removeStorage,

    setItemWithExpiry,
    getItemWithExpiry,
  };
};

export const useLSLoginMetaStorage = () => {
  const { getStorage, setStorage } = useLSStorage();

  const setLoginMeta = (value: string | object) => {
    setStorage(STORAGE_LS_LOGIN_META, typeof value === "string" ? value : JSON.stringify(value));
  };

  const getLoginMeta = () => {
    const storage_meta = getStorage(STORAGE_LS_LOGIN_META);
    if (storage_meta) {
      return safeJSONParse(storage_meta, {});
    }
    return {};
  };

  const getOpenID = (): string | undefined => {
    const meta = getLoginMeta();
    return (
      meta?.openid ||
      // 游戏内登录接口返回字段
      meta?.open_id
    );
  };

  const getToken = (): string | undefined => {
    const meta = getLoginMeta();
    return meta.token;
  };

  return {
    setLoginMeta,
    getLoginMeta,

    getOpenID,
    getToken,
  };
};

export const useIndexDB = (db_name: string = STORAGE_SS_INDEX_DB_NAME) => {
  const openIndexedDB = async (db_name: string): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(db_name);
      request.onupgradeneeded = () => {
        const db = request.result;
        const store = db.createObjectStore(STORAGE_SS_OBJECT_STORE_NAME, {
          keyPath: "id",
          autoIncrement: true,
        });
        store.createIndex("key", "key", { unique: true });
        store.createIndex("value", "value", { unique: false });
        store.createIndex("create_time", "create_time", { unique: false });
      };
      request.onsuccess = () => {
        resolve(request.result);
      };
      request.onerror = () => {
        reject(request.error);
      };
    });
  };

  const getCache = async (key: string): Promise<IndexDBCacheItem> => {
    const db = await openIndexedDB(db_name);
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(STORAGE_SS_OBJECT_STORE_NAME, "readwrite");
      const store = transaction.objectStore(STORAGE_SS_OBJECT_STORE_NAME);
      const request = store.index("key").get(key);
      request.onsuccess = (evt: any) => {
        const result = evt.target.result;
        if (typeof result == "undefined") {
          console.warn("No matching record found");
        }
        resolve(result);
      };
      request.onerror = (event) => {
        reject(event);
      };
    });
  };

  const updateCache = async (optoins: {
    id?: number;
    key: string;
    value: any;
    create_time?: number;
  }) => {
    const cache: IndexDBCacheItem = await getCache(optoins.key);
    if (cache?.id) {
      Object.assign(optoins, { id: cache.id });
    }
    setCache(optoins);
  };

  const setCache = async (cache: {
    id?: number;
    key: string;
    value: any;
    create_time?: number;
  }) => {
    const db = await openIndexedDB(db_name);
    const transaction = db.transaction(STORAGE_SS_OBJECT_STORE_NAME, "readwrite");
    const store = transaction.objectStore(STORAGE_SS_OBJECT_STORE_NAME);
    store.put(Object.assign({ create_time: Date.now() }, cache));
  };

  const staleWhileRevalidate = (
    key: string,
    optoins: {
      handler?: (...args: any[]) => Promise<any>;
      callback?: (...args: any[]) => any;
      interval?: number;
    }
  ) => {
    const { handler, callback, interval } = Object.assign(
      { handler: noop, callback: noop, interval: 5000 },
      optoins
    );

    return async (...args: any[]) => {
      // 不支持 indexDB，直接返回执行结果
      if (!isIndexedDBSupported()) {
        return await handler(...args);
      }

      const cache: IndexDBCacheItem = await getCache(key);

      const doHandler = async () => {
        const result = await handler(...args);
        // 缓存结果
        const data = {
          key,
          value: result,
          create_time: Date.now(),
          app_version: window.STANDALONE_SITE_VERSION,
          app_build_time: window.APP_BUILD_TIME,
        };
        if (cache?.id) {
          Object.assign(data, { id: cache.id });
        }
        await setCache(data);
        return result;
      };

      const is_cache_valid =
        !!cache?.id &&
        cache?.app_version === window.STANDALONE_SITE_VERSION &&
        cache?.app_build_time === window.APP_BUILD_TIME;

      if (is_cache_valid) {
        setTimeout(() => {
          // 异步运行 doHandler()，不阻塞返回结果
          doHandler();
        }, interval);
        // 对于外部需要快速获取结果的场景，直接返回缓存
        callback(cache.value);
        return cache.value;
      }

      const result = await doHandler();

      return result;
    };
  };

  return {
    getCache,
    setCache,
    updateCache,
    staleWhileRevalidate,
  };
};
