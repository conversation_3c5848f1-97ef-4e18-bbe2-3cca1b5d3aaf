import qs from "query-string";

/**
 * @description 规范化
 * @param   {string}  search  [search description]
 * @return  {string}          [return description]
 */
const normalize = (search: string): string =>
  // 把多余的 ? 替换成 &
  search.replace(/\?/gi, "&").replace(/^&/, "");

/**
 * @description 处理 hash 的情况
 * @param   {string}  search  [search description]
 * @return  {string}          [return description]
 */
const hashing = (search: string): string => search.replace(/#\/.*\?/, "");

export const checkSearchMultipleQuestionMark = (search: undefined | string = location.search) => {
  const count = (search.match(/\?/g) || []).length;
  return count > 1;
};

export const format = (search: string): string => {
  const decode = decodeURIComponent(search);
  return normalize(
    //
    hashing(decode)
  );
};

/**
 * @description URL参数对象化
 * @param   {string}  search  [search description]
 * @return  {object}          [return description]
 */
export const urlSearchObjectify = (
  search: string = window.parent.location.search || window.location.search
) => {
  const location_search = new URLSearchParams(format(search));

  let obj: any = {};
  location_search.forEach((value, key) => {
    if (obj[key]) {
      console.warn(`URL中参数 ${key} 重复出现，值为：${obj[key]}`);
      return;
    }
    obj[key] = value;
  });

  return obj;
};

/**
 * [replaceUrlParam 替换 URL 中的 xx参数，不添加新的历史记录]
 *
 * @param   {Object}  params  [替换的参数，eg: { keyword: "uu" }]
 *
 * @return  {void}            [return description]
 */
export const replaceUrlParams = (params: Record<string, string>): void => {
  const urlParams = new URLSearchParams(window.location.search);
  for (const key in params) {
    urlParams.set(key, params[key]); // 设置参数
  }
  const newSearch = `?${urlParams.toString()}`;

  // 使用 history.replaceState 替换当前历史记录
  window.history.state.current = window.location.pathname + newSearch; // 修改当前state
  history.replaceState(window.history.state, "", newSearch);
};

export const addParamToCurrentUrl = (params: Record<string, string>): void => {
  // 获取当前的 URL
  const win = window.parent || window;
  const current_url = win.location.href;

  try {
    const parsed_url = new URL(current_url);
    const parsed_query = qs.parse(parsed_url.search);
    Object.assign(parsed_query, params);
    const new_query_string = qs.stringify(parsed_query);
    parsed_url.search = new_query_string;

    win.history.replaceState({}, "", parsed_url.toString());
  } catch (error) {
    console.error("[addParamToCurrentUrl] Invalid URL provided!", error);
  }
};

export const removeKeysFromCurrentUrl = (keys: Array<string>): void => {
  try {
    const url_params = new URLSearchParams(window.location.search);
    keys.forEach((key) => url_params.delete(key));
    const new_search = `?${url_params.toString()}`;

    window.history.state.current = window.location.pathname + new_search; // 修改当前state
    history.replaceState(window.history.state, "", new_search);
  } catch (error) {
    console.error("[removeKeysFromCurrentUrl] Invalid URL provided!", error);
  }
};
