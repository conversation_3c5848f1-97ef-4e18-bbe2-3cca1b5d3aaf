import { STANDARD_SS_SUPPORT_LANG } from "../configs/standard";
import { getStandardizedLang } from "./standard";
import { decodeHTMLEntities } from "./tools";

const current_url_lang = getStandardizedLang(STANDARD_SS_SUPPORT_LANG);

type Attribute = { name: string; value: string };

export const setElementAttributes = (el: HTMLElement, attr: Attribute | Attribute[]) => {
  const attrs = Array.isArray(attr) ? attr : [attr];

  attrs.forEach(({ name, value }) => {
    el.setAttribute(name, value);
  });
};

export const setHtmlAttributes = (attr: Attribute | Attribute[]) => {
  const html = document.querySelector("html") as HTMLElement;
  setElementAttributes(html, attr);
};

export const getDir = (): string => {
  const rtl_langs = ["ar"];
  return rtl_langs.includes(current_url_lang) ? "rtl" : "ltr";
};

export const setHtmlLang = () => {
  setHtmlAttributes({ name: "lang", value: current_url_lang });
};

export const setHtmlDir = () => {
  setHtmlAttributes({ name: "dir", value: getDir() });
};

export const createElement = (tag: string, attr: Attribute | Attribute[]) => {
  const el = document.createElement(tag);
  setElementAttributes(el, attr);
  return el;
};

/**
 * @description 提取 HTML 中的图片链接
 * @description safari 下，这种图片匹配不到 <img width="144" height="144" alt="A cartoon of a child holding a poster<br/><br/>AI-generated content may be incorrect." src="https://sg-cdn.blablalink.com/standalonesite/ugc/public/image/8c784d88-5b7e-427b-a7d2-d3ad65073044.png?height=144&amp;width=144">
 * @deprecated 使用 extractImageSrcV2
 * @param   {string[]}  html  [html description]
 *
 * @return  {string[]}              [return description]
 */
export const extractImageSrc = (html: string): string[] => {
  const regx = /<img[^>]+src="([^">]+)"/g;
  const result: string[] = [];
  let match: RegExpExecArray | null;

  while ((match = regx.exec(html)) !== null) {
    result.push(decodeHTMLEntities(match[1]));
  }

  return result;
};

/**
 * @description 提取 HTML 中的图片链接
 * @param   {string[]}  html  [html description]
 * @return  {string[]}              [return description]
 */
export const extractImageSrcV2 = (html: string): string[] => {
  const doc = getDocument(html);
  const images = doc.querySelectorAll("img");
  const list: string[] = [];
  images.forEach((img) => {
    list.push(img.getAttribute("src") || "");
  });
  return list.filter(Boolean);
};

/**
 * @description 提取 HTML 中的文本
 *
 * @param   {string}  html  [html description]
 *
 * @return  {string}              [return description]
 */
export const extractTextFromHtml = (html: string): string => {
  const temp_div = document.createElement("div");
  temp_div.innerHTML = html;
  return temp_div.innerText || temp_div.textContent || "";
};

export const filterTagFromHtml = (html: string, tag_name: string): string => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  const elements = doc.querySelectorAll(tag_name.toLocaleLowerCase());
  elements.forEach((element) => element.remove());
  return doc.body.innerHTML;
};

/**
 * @description 判断给定的 src 是否为 Base64 编码的图片。
 * @param src - 图片的 src 属性值。
 * @returns 如果是 Base64 编码的图片则返回 true，否则返回 false。
 */
export function isBase64Image(src: string): boolean {
  // 检查 src 是否以 data:image/ 开头，并且包含逗号分隔的数据部分
  return /^data:image\/[^;]+;base64,/.test(src);
}

/**
 * @description 从 Base64 图片的 src 中提取文件扩展名。
 * @param src - 图片的 src 属性值。
 * @returns 文件扩展名（如 'png'），如果无法确定则返回空字符串。
 */
export function getImageExtensionByBase64(src: string): string {
  const matches = /^data:image\/([a-zA-Z0-9]+);base64,/.exec(src);
  if (matches && matches[1]) {
    return matches[1].toLowerCase() || "";
  }
  return "";
}

/**
 * @description 解析给定的 HTML 字符串并返回对应的 Document 对象。
 *
 * @param   {string}    html  [html description]
 *
 * @return  {Document}        [return description]
 */
export function getDocument(html: string): Document {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  return doc;
}

/**
 * @description 将 HTML 字符串中的所有 <img> 标签替换为指定的占位符。
 * @param html - 原始的 HTML 字符串。
 * @param placeholder - 用于替换的占位符内容。
 * @returns 替换后的 HTML 字符串。
 */
export function replaceAllImagesWithPlaceholder(
  html: string,
  placeholder: string = "<p></p>"
): string {
  const doc = getDocument(html);
  const images = doc.querySelectorAll("img");

  images.forEach((img) => {
    const fragment = document.createRange().createContextualFragment(placeholder);
    while (fragment.firstChild) {
      img.replaceWith(fragment.firstChild);
    }
  });

  return doc.body.innerHTML;
}

/**
 * @description 移除 HTML 字符串中所有超链接的 href 属性，并用一个空标签替换超链接。
 *
 * @param   {string}  html  [html description]
 *
 * @return  {string}        [return description]
 */
export function removeHyperLink(html: string): string {
  const whitelist: string[] = [];
  const doc = getDocument(html);
  const links: NodeListOf<HTMLAnchorElement> = doc.querySelectorAll("a");

  links.forEach((link: HTMLAnchorElement) => {
    const href: string | null = link.getAttribute("href");
    if (href && !whitelist.some((url) => href.toLowerCase().startsWith(url.toLowerCase()))) {
      const span = doc.createElement("span");
      span.textContent = link.textContent || "";
      link.parentNode?.replaceChild(span, link);
    }
  });

  return doc.body.innerHTML;
}
