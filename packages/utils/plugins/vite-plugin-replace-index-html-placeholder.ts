import axios from "axios";
import { ENV_DEVELOPMENT, ENV_PRE, ENV_PROD } from "../../configs/env";

interface Options {
  mode: string;
  inline?: boolean;
  placeholder: string;
  callback: (options: Options) => string | Promise<string>;
}

const request = async (url: string) => axios.get(url);

const inlineScriptSrc = async (html: string): Promise<string> => {
  const originalHtml = html;
  const scriptPromises: Promise<string>[] = [];
  const placeholder: Array<string> = [];
  let isError = false;

  // 使用正则表达式匹配 <script> 标签
  html = html.replace(
    /<script\s*(.*?)src="(.*?)"\s*(.*?)><\/script>/g,
    (_match, before, src, after) => {
      // 返回一个占位符，稍后会被替换
      const ret = `<!-- script:${src} -->`;
      // 使用 axios 获取远程 JavaScript 文件的内容
      const promise = request(src).then((response) => {
        // 将获取到的内容内联到 <script> 标签中
        return `<script ${before} ${after}>${response.data}</script>`;
      });

      scriptPromises.push(promise);
      placeholder.push(ret);

      return ret;
    }
  );

  try {
    // 等待所有异步请求完成
    const resolvedScripts = await Promise.all(scriptPromises);

    // 替换占位符
    resolvedScripts.forEach((script, index) => {
      html = html.replace(placeholder[index], script);
    });
  } catch (error) {
    isError = true;
    console.error("Error fetching script:", error);
  }

  return isError ? originalHtml : html;
};

export const VitePluginReplaceIndexHtmlPlaceholder = (options: Options | Array<Options>) => {
  return {
    name: "vite-plugin-replace-index-html-placeholder",
    enforce: "pre",
    async transformIndexHtml(html: string) {
      // handler
      const handler = async (str: string, op: Options) => {
        const { placeholder, callback } = op;
        const regex = new RegExp(placeholder, "g");
        const replacement = await callback(op);

        return str.replace(regex, () => replacement);
      };

      if (Array.isArray(options)) {
        let index = 0;
        while (index < options.length) {
          html = await handler(html, options[index]);
          index++;
        }
        return html;
      }
      return await handler(html, options);
    },
  };
};

export const injectAegis = async (options: Options) => {
  const { inline } = options;
  const str = `<script type="text/javascript" src="https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js"></script>`;
  return inline ? await inlineScriptSrc(str) : str;
};

export const injectCookieBanner = async (options: Options) => {
  const { mode, inline } = options;

  if (mode === ENV_DEVELOPMENT) {
    return "";
  }

  if ([ENV_PRE, ENV_PROD].includes(mode)) {
    const OtAutoBlock = `<script crossorigin="anonymous" type="text/javascript" src="https://cdn-apac.onetrust.com/consent/01929455-37af-7464-af17-0d95aaa3561f/OtAutoBlock.js" ></script>`;
    return `
      ${inline ? await inlineScriptSrc(OtAutoBlock) : OtAutoBlock}
      <script crossorigin="anonymous" src="https://cdn-apac.onetrust.com/scripttemplates/otSDKStub.js" async data-document-language="true" type="text/javascript" charset="UTF-8" data-domain-script="01929455-37af-7464-af17-0d95aaa3561f" ></script>
      <script type="text/javascript">
        function OptanonWrapper() { }
      </script>
    `;
  }

  const OtAutoBlock = `<script crossorigin="anonymous" type="text/javascript" src="https://cdn-apac.onetrust.com/consent/01929455-37af-7464-af17-0d95aaa3561f-test/OtAutoBlock.js" ></script>`;
  return `
    ${inline ? await inlineScriptSrc(OtAutoBlock) : OtAutoBlock}
    <script crossorigin="anonymous" src="https://cdn-apac.onetrust.com/scripttemplates/otSDKStub.js" async data-document-language="true" type="text/javascript" charset="UTF-8" data-domain-script="01929455-37af-7464-af17-0d95aaa3561f-test" ></script>
    <script type="text/javascript">
      function OptanonWrapper() { }
    </script>
  `;
};
