import fs from "node:fs";
import { Plugin, ResolvedConfig } from "vite";
import { resolve } from "node:path";
import { minify } from "csso";

const CRITICAL_TYPE = {
  link: "link",
  script: "script",
};

const regex = {
  [CRITICAL_TYPE.link]: /<link\s+rel="stylesheet"\s+[^>]*href="([^"]+\.css)"\s*[^>]*>/g,
  [CRITICAL_TYPE.script]: /<script(?:\s+[^>]*)?src="([^"]+)"(?:\s+[^>]*)?><\/script>/gi,
};

const VitePluginInlineCritical = (options?: {
  htmlFilePath?: string;
  critical_type?: keyof typeof CRITICAL_TYPE;
  ignore_chunks?: string[];
}): Plugin => {
  let config: ResolvedConfig;

  const { htmlFilePath, critical_type } = Object.assign(
    { htmlFilePath: "index.html", critical_type: CRITICAL_TYPE.link },
    options
  );

  return {
    name: "vite-plugin-inline-critical",
    enforce: "post",
    configResolved(resolvedConfig) {
      config = resolvedConfig;
    },
    closeBundle() {
      const htmlFile = resolve(config.root, config.build.outDir, htmlFilePath);
      let htmlContent = fs.readFileSync(htmlFile, "utf-8");

      htmlContent = htmlContent.replace(regex[critical_type], (match, src) => {
        if (src.startsWith(config.base)) {
          const url = src.split("/").pop() || "";
          const distFile = resolve(config.root, config.build.outDir, `./assets/${url}`);

          if (fs.existsSync(distFile)) {
            const fileContent = fs.readFileSync(distFile, "utf-8");
            return critical_type === CRITICAL_TYPE.link
              ? `<style>${minify(fileContent).css}</style>`
              : `<script type="module">${fileContent}</script>`;
          }
        }
        return match;
      });

      fs.writeFileSync(htmlFile, htmlContent);
    },
  };
};

export default VitePluginInlineCritical;
