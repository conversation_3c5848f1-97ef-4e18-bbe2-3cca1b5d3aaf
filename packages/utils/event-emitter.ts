import { onMounted, onUnmounted } from "vue";
import { PostDetail, PostItem } from "../types/post";

const EVENT_NAMES = {
  /** 帖子详情评论列表头部滚动到视窗 */
  post_detail_comment_head_scroll_into_view: "post_detail_comment_head_scroll_into_view",
  /** 帖子详情评论列表重置 */
  post_detail_reset_comment_list: "post_detail_reset_comment_list",
  /** 帖子详情评论列表滚动到视窗 */
  post_detail_comment_list_scroll_into_view: "post_detail_comment_list_scroll_into_view",
  /** 在话题详情页中重置列表 */
  topic_detail_reset_list: "topic_detail_reset_list",
  /** 帖子列表页特定项数据刷新（点赞、收藏、评论、分享等） */
  refresh_post_list_item_info: "refresh_post_list_item_info",
  /** 拉黑、接触拉黑用户 */
  user_status_change: "user_status_change",
  /** 回到顶部 */
  page_scroll_to_top: "page_scroll_to_top",
} as const;

type EventPayload = {
  [EVENT_NAMES.post_detail_comment_head_scroll_into_view]: undefined;
  [EVENT_NAMES.post_detail_reset_comment_list]: undefined;
  [EVENT_NAMES.post_detail_comment_list_scroll_into_view]: undefined;
  [EVENT_NAMES.topic_detail_reset_list]: undefined;
  [EVENT_NAMES.refresh_post_list_item_info]: PostItem | PostDetail;
  [EVENT_NAMES.user_status_change]: {
    intl_openid: string;
    is_black?: 0 | 1;
    is_followed?: 0 | 1;
    is_mutual_follow?: 0 | 1;
  };
  [EVENT_NAMES.page_scroll_to_top]: undefined;
};

type EventNames = keyof typeof EVENT_NAMES;

class EventEmitter {
  private listeners: Map<EventNames, Array<Function>>;

  constructor() {
    this.listeners = new Map();
  }

  on<T extends EventNames>(event_name: T, fn: (payload: EventPayload[T]) => void) {
    if (!this.listeners.has(event_name)) {
      this.listeners.set(event_name, []);
    }
    this.listeners.get(event_name)!.push(fn);
  }

  off<T extends EventNames>(event_name: T, fn: (payload: EventPayload[T]) => void) {
    if (!this.listeners.has(event_name)) {
      return;
    }
    const index = this.listeners.get(event_name)!.indexOf(fn);
    if (index === -1) {
      return;
    }
    this.listeners.get(event_name)!.splice(index, 1);
  }

  emit<T extends EventNames>(
    event_name: T,
    ...args: EventPayload[T] extends undefined ? [] : [payload: EventPayload[T]]
  ) {
    if (!this.listeners.has(event_name)) {
      return;
    }
    const payload = args[0] as EventPayload[T];
    this.listeners.get(event_name)!.forEach((fn) => fn(payload));
  }
}

const event_emitter = new EventEmitter();

export const onEventEmitter = <T extends EventNames>(
  event_name: T,
  callback: (payload: EventPayload[T]) => void
) => {
  onMounted(() => {
    event_emitter.on(event_name, callback);
  });
  onUnmounted(() => {
    event_emitter.off(event_name, callback);
  });
};

export { EVENT_NAMES, type EventPayload, event_emitter };
