// types
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GrayscaleLogicType } from "../types/grayscale";

// utils
import { useCDNConfigs } from "./cdn";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>id<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Url<PERSON>o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "./checker";

const { getCDNConfigs } = useCDNConfigs();

export const useGrayscale = <T extends { grayscale: Grayscale }>(cdn_config?: T) => {
  const getGrayscale = async (
    grayscale_key: Grayscale<PERSON><PERSON>,
    options: {
      openid?: string;
    }
  ): Promise<boolean> => {
    if (!grayscale_key) {
      throw new Error("[getGrayscale] grayscale_key is required");
    }

    const { grayscale } = cdn_config || getCDNConfigs();
    const grayscale_configs = grayscale[grayscale_key] || {};

    console.log(
      `[getGrayscale] grayscale_key: ${grayscale_key} grayscale_configs`,
      grayscale_configs,
      options
    );

    const checkers: Checker[] = [];

    grayscale_configs.logics?.forEach((logic: GrayscaleLogic) => {
      switch (logic.type) {
        case GrayscaleLogicType.url_no_keyword:
          checkers.push(new UrlNoKeywordChecker(grayscale_key));
          break;
        case GrayscaleLogicType.timestamp:
          checkers.push(new TimestampChecker(logic));
          break;
        case GrayscaleLogicType.openid_last_digits:
          checkers.push(new OpenidLastDigitsChecker(logic, options?.openid || ""));
          break;
        case GrayscaleLogicType.openid:
          checkers.push(new OpenidChecker(logic, options?.openid || ""));
          break;
        default:
      }
    });

    const checker_chain = new LogicCheckerChain(checkers, grayscale_configs.relation);
    const check_res = await checker_chain.doCheck();

    return check_res;
  };

  return {
    getGrayscale,
  };
};
