/** base64 加密 */
export function base64Encode<T extends string | undefined>(input: T) {
  return (input === undefined ? undefined : btoa(encodeURIComponent(input))) as T;
}

/** base64 解密 */
export function base64Decode<T extends string | undefined>(input: T) {
  return (input === undefined ? undefined : decodeURIComponent(atob(input))) as T;
}

/** 是否是未加密的 openid */
export function isOriginalOpenId(id: string) {
  if (!id) return true;
  return !!id.match(/^[0-9]{5}-[0-9]{9,30}$/);
}
