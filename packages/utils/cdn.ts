// configs
import { ENV_TEST, ENV_DEVELOPMENT } from "../configs/env";
import {
  STORAGE_INDEXDB_CDN_CONFIGS,
  STORAGE_INDEXDB_VERSION_CONFIGS,
  STORAGE_WINDOW_CDN_CONFIGS,
  STORAGE_WINDOW_VERSION_CONFIGS,
} from "../configs/storage";
// types
import { CDNConfigs, VersionCDNConfigs } from "../types/cdn";
// utils
import { useIndexDB } from "./storage";

const { staleWhileRevalidate } = useIndexDB();
const win: any = window.parent || window;

const fetchFile = async (env: any, app_name: string) => {
  if (!env || env === ENV_DEVELOPMENT) {
    env = ENV_TEST;
  }

  const timestamp = Date.now();
  const url = `//sg-lipcommunity.playerinfinite.com/standalone-site-frontend/configure/${app_name}-${env}.json?t=${timestamp}`;
  const res = await fetch(url);
  return res.json();
};

const useCDN = <T = any>(options: { window_key: string; index_db_key: string }) => {
  const { window_key, index_db_key } = options || {
    window_key: STORAGE_WINDOW_CDN_CONFIGS,
    index_db_key: STORAGE_INDEXDB_CDN_CONFIGS,
  };

  const loadCDNConfigs = staleWhileRevalidate(index_db_key, {
    async handler(env: string, app_name: string | undefined = "configs"): Promise<T> {
      try {
        win[window_key] = await fetchFile(env, app_name);
      } catch (error) {
        console.error("fetch configs error", error);
      }
      return win[window_key];
    },
    callback(value) {
      win[window_key] = value;
    },
  });

  const getCDNConfigs = (): T => {
    if (!win[window_key]) {
      console.error(`win.${window_key} not found, please call loadCDNConfigs first`);
    }
    return win[window_key] || {};
  };

  return {
    loadCDNConfigs,
    getCDNConfigs,
  };
};

export const useCDNConfigs = () => {
  return useCDN<CDNConfigs>({
    window_key: STORAGE_WINDOW_CDN_CONFIGS,
    index_db_key: STORAGE_INDEXDB_CDN_CONFIGS,
  });
};

export const useVersionCDNConfigs = () => {
  return useCDN<VersionCDNConfigs>({
    window_key: STORAGE_WINDOW_VERSION_CONFIGS,
    index_db_key: STORAGE_INDEXDB_VERSION_CONFIGS,
  });
};
