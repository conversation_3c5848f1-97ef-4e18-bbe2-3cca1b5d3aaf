import { API_PRE_REQUEST_URL, API_PROD_REQUEST_URL, API_TEST_REQUEST_URL } from "../configs/api";
import { ENV_DEVELOPMENT, ENV_PRE, ENV_PROD, ENV_TEST } from "../configs/env";
import { INTL_SOCIAL_LIST } from "../configs/intl";
import dayjs from "dayjs";
import { urlSearchObjectify } from "./qs";
import { STANDARD_GAME_ID_KEY } from "../configs/standard";
import { Platform } from "../types/common";
import { get, isUndefined, set } from "lodash-es";
import qs from "query-string";

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * 安全地 JSON 解析，如果解析失败返回 undefined
 * @param str 待解析的 JSON 字符串
 * @returns 解析结果，如果解析失败返回 undefined
 */
export const safeParse = <T>(str: string | undefined) => {
  if (!str) return undefined;
  try {
    return JSON.parse(str) as T;
  } catch (err) {
    return undefined;
  }
};

/**
 * @description 是否是 RTL 布局
 * @return  {[boolean]}
 */
export const isRTL = (): boolean => document.documentElement.getAttribute("dir") === "rtl";

/**
 * @description 判断浏览器是否为 safari
 */
export const isSafari = (): boolean => {
  return /^((?!chrome|android|crios).)*safari/i.test(navigator.userAgent);
};
/**
 * @description 判断url是否跟当前域名一致
 *
 * @param   {string}   url  [url description]
 *
 * @return  {boolean}       [return description]
 */
export const isSameOrigin = (url: string): boolean => {
  if (!url) {
    return false;
  }
  const url_object = new URL(url);
  return url_object.origin === window.location.origin;
};

/**
 * @description 安全的JSON解析
 */
export const safeJSONParse = (str: string, error_default_value?: any) => {
  try {
    return JSON.parse(str);
  } catch (_) {
    return error_default_value || str;
  }
};

export const isIndexedDBSupported = () => "indexedDB" in window;

export const getApiBaseUrl = (env?: string, online?: boolean) => {
  return {
    [ENV_DEVELOPMENT]: online ? `${API_TEST_REQUEST_URL}/api/` : "/api/",
    [ENV_TEST]: `${API_TEST_REQUEST_URL}/api/`,
    [ENV_PRE]: `${API_PRE_REQUEST_URL}/api/`,
    [ENV_PROD]: `${API_PROD_REQUEST_URL}/api/`,
  }[env || getEnv()];
};

/**
 * 判断是否为 intlBrowser
 */
export const isInIntlBrowser = () =>
  window.navigator?.userAgent.toLowerCase().indexOf("intlbrowser") >= 0;

/**
 * @description 是否是移动端设备
 *
 * @return  {boolean} [return description]
 */
export const isMobileDevice = (): boolean => {
  const ua = navigator.userAgent;
  // 使用正则表达式匹配移动设备相关的关键字
  const reg_exp = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  return reg_exp.test(ua);
};

/**
 * @description 判断设备是否为横屏
 */
export const isLandscape = (): boolean => {
  return isMobileDevice() && window.matchMedia("(orientation: landscape)").matches;
};

/**
 * @description 是否为 iOS
 */
export const isIOS = () => {
  const ua = navigator.userAgent;
  return /iPad|iPhone|iPod/.test(ua);
};

export const getClientType = (): "Android" | "iOS" | "pc_web" | string => {
  const ua = navigator.userAgent;
  // 使用正则表达式匹配移动设备相关的关键字
  if (!isMobileDevice()) {
    return "pc_web";
  }
  if (/Android/i.test(ua)) {
    return "Android";
  }
  if (/iPhone|iPad|iPod/i.test(ua)) {
    return "iOS";
  }
  return ua;
};

/**
 * @description 判断新数组中的项是否存在旧数据里面，如果有则过滤掉
 *
 * @param   {Array<any><new_arr>}     old_arr  [old_arr description]
 * @param   {Array<any><k>}           new_arr  [new_arr description]
 * @param   {string}                  k        [k description]
 *
 * @return  {<any><new_arr><any><k>}           [return description]
 */
export const getUniqueArr = (old_arr: Array<any>, new_arr: Array<any>, k?: string) => {
  const key = k || "id";
  const unique_arr =
    new_arr?.filter((new_item) => !old_arr.some((old_item) => old_item[key] === new_item[key])) ||
    [];

  return unique_arr;
};

/**
 * @description 获取当前环境：development/test/pre/prod等
 *
 * @return  {[type]}  [return description]
 */
export const getEnv = () => {
  let env = "";

  if (import.meta.env.DEV) {
    env = import.meta.env.MODE;
  } else {
    if (location.hostname.match(/^test[\.-]/)) return ENV_TEST;
    if (location.hostname.match(/^pre[\.-]/)) return ENV_PRE;

    let [url_env, hostname] = window.location.hostname.split("-");
    if (!hostname) {
      url_env = ENV_PROD;
    }
    env = url_env;
  }

  // console.log("[getEnv] env", env);
  return env;
};

/**
 * @description 注入脚本
 * @param {*} src
 * @returns
 */
export const injectScript = async (
  src: string,
  container?: HTMLElement,
  attributes?: Record<string, string>
) => {
  if (!src) {
    return "href empty!";
  }
  container = container || document.head;
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = src;

    // 应用额外的属性
    if (attributes) {
      for (const [key, value] of Object.entries(attributes)) {
        script.setAttribute(key, value);
      }
    }

    script.onload = resolve;
    script.onerror = reject;
    container.appendChild(script);
  });
};

/**
 * @description 注入 link
 * @param {*} href
 */
export const injectLink = async (href: string) => {
  if (!href) {
    return "href empty!";
  }
  return new Promise((resolve, reject) => {
    const resource = document.createElement("link");
    resource.setAttribute("rel", "stylesheet");
    resource.setAttribute("href", href);
    resource.setAttribute("type", "text/css");
    resource.onload = resolve;
    resource.onerror = reject;
    document.head.appendChild(resource);
  });
};

/**
 * @description 注入 CSS
 * @param {*} href
 */
export const injectCss = async (css: string) => {
  if (!css) {
    return "style empty!";
  }

  const style = document.createElement("style");
  style.setAttribute("type", "text/css");

  document.head.appendChild(style);
  style.appendChild(document.createTextNode(css));
};

/**
 * @description 其他应用端内，需要隐藏部分功能
 *
 * @return  {[type]}  [return description]
 */
export function getSpecialBrowserType() {
  const uaTester = (regs: RegExp[]) => Boolean(regs.find((reg) => reg.test(navigator.userAgent)));
  if (uaTester([/fbav/i, /fban/i])) {
    return "facebook";
  }
  if (uaTester([/naver/i, /fban/i])) {
    return "naver";
  }
  if (uaTester([/bytelo/i, /bytedance/i])) {
    return "tictok";
  }
  if (uaTester([/microm/i, /wechat/i])) {
    return "wechat";
  }
  if (uaTester([/vivobrowser/i, /vivo/i])) {
    return "vivo";
  }
  if (uaTester([/mibrowser/i, /xiaomi/i, /miui/i])) {
    return "mi";
  }
  if (uaTester([/discord/i])) {
    return "discord";
  }
  if (uaTester([/kakao/i, /kakaotalk/i])) {
    return "kakao";
  }
  if (uaTester([/line/i])) {
    return "line";
  }
  if (uaTester([/instagram/i])) {
    return "ins";
  }
  return "";
}

export const getIntlSocailList = () => {
  const browser = getSpecialBrowserType();
  const is_mobile_device = isMobileDevice();
  return ["facebook", "line", "ins", "kakao", "naver"].includes(browser)
    ? []
    : INTL_SOCIAL_LIST.filter((social: string) => {
        if (is_mobile_device) return social !== "line";
        return true;
      });
};
/**
 * @description 返回 ymd 格式
 *
 * @param   {number}  time  [time description]
 *
 * @return  {[type]}        [return description]
 */
export const ymd = (time: number) => {
  return dayjs(time * 1000).format("YYYY-MM-DD");
};
/**
 * @description 格式化时长
 * @param seconds 秒数
 */
export const formatDuration = (seconds: number): string => {
  const h = Math.floor(seconds / 3600)
    .toString()
    .padStart(2, "0");
  const m = Math.floor((seconds % 3600) / 60)
    .toString()
    .padStart(2, "0");
  const s = Math.floor(seconds % 60)
    .toString()
    .padStart(2, "0");

  if (h === "00") {
    return `${m}:${s}`;
  }

  return `${h}:${m}:${s}`;
};

/**
 * @link https://stackoverflow.com/questions/2685911/is-there-a-way-to-round-numbers-into-a-reader-friendly-format-e-g-1-1k
 *
 * @param   {number}  decPlaces  [decPlaces description]
 *
 * @return  {[type]}             [return description]
 */
export const abbrNumn = (num: number, dec_places?: number): string => {
  if (!num) {
    return "0";
  }
  let ori_num = num;
  let ret = "" + num;
  // 2 decimal places => 100, 3 => 1000, etc
  const d_p = Math.pow(10, dec_places || 2);
  // Enumerate number abbreviations
  const abbrev = ["K", "M", "B", "T"];
  // Go through the array backwards, so we do the largest first
  for (let i = abbrev.length - 1; i >= 0; i--) {
    // Convert array index to "1000", "1000000", etc
    const size = Math.pow(10, (i + 1) * 3);
    // If the number is bigger or equal do the abbreviation
    if (size <= ori_num) {
      // Here, we multiply by decPlaces, round, and then divide by decPlaces.
      // This gives us nice rounding to a particular decimal place.
      ori_num = Math.round((ori_num * d_p) / size) / d_p;
      // Handle special case where we round up to the next abbreviation
      if (ori_num == 1000 && i < abbrev.length - 1) {
        ori_num = 1;
        i++;
      }
      // Add the letter for the abbreviation
      ret = ori_num + "" + abbrev[i];
      // We are done... stop
      break;
    }
  }

  return ret;
};

/**
 * @description 处理英语中的复数
 */
export const hanlePlural = (num: number, postfix: string = "s"): string => {
  const qs_url_search = urlSearchObjectify();
  const lang = (qs_url_search.lang || "en").toLocaleLowerCase();
  const is_en = lang === "en";
  let ret = "";
  if (num > 1 && is_en) {
    ret += postfix;
  }
  return ret;
};

/** 原地移除对象中的空值（null/undefined） */
export const removeObjectEmptyKey = (obj: Record<string, any>) => {
  Object.keys(obj).forEach((key) => {
    if (obj[key] == null) {
      delete obj[key];
    }
  });
  return obj;
};

// 定义登录来源类型
export enum LoginSource {
  IN_GAME = "in_game", // 游戏内直接打开
  WEB_CREDENTIAL = "web_credential", // 票据登录
  NORMAL = "normal", // 普通网页打开
}

export const isInGame = () => {
  const query = urlSearchObjectify();
  return isInIntlBrowser() && (query[STANDARD_GAME_ID_KEY] || (query.openid && query.token));
};

/**
 * @description 获取登录来源
 */
export const getLoginSource = (): LoginSource => {
  const query = urlSearchObjectify();

  // 检查是否是游戏内打开
  if (isInGame()) {
    return LoginSource.IN_GAME;
  }

  // 检查是否是登录器打开
  if (query.web_credential) {
    return LoginSource.WEB_CREDENTIAL;
  }

  return LoginSource.NORMAL;
};

/**
 * @description 判断是否游戏相关登录场景
 */
export const isGameLogin = (): boolean => {
  const loginSource = getLoginSource();
  return loginSource === LoginSource.IN_GAME;
};

/**
 * @description 获取视频url的来源
 * @param   {string}    url  [url description]
 * @return  {Platform}       [return description]
 */
export const getVideoUrlPlatform = (url: string): Platform => {
  let platform = null as any as Platform;
  try {
    const { host } = new URL(url);

    Object.keys(Platform).forEach((key) => {
      if (host.indexOf(key) > -1 && !platform) {
        platform = Platform[key as Platform];
      }
    });

    return platform;
  } catch (error) {
    return platform;
  }
};

/**
 * @description 把 \n 替换成 <br/>
 * @param   {string}  str  [str description]
 * @return  {string}       [return description]
 */
export const replaceNewLineWithBr = (str?: string, regex: undefined | RegExp = /\n/g): string => {
  if (!str) {
    return "";
  }
  return str.replace(regex, "<br/>");
};

export function getYouTubeVideoId(url: string) {
  const regs = [
    /https:\/\/youtu\.be\/(\w+)/,
    /https:\/\/m\.youtube\.com\/watch\?v=(\w+)/,
    /https:\/\/www\.youtube\.com\/embed\/(\w+)/,
    /https:\/\/www\.youtube\.com\/watch\?v=(\w+)/,
  ];
  for (const reg of regs) {
    const [, id] = url.match(reg) || [];
    if (id) return id;
  }
}

export function getTikTokVideoId(url: string) {
  const regs = [
    /https:\/\/(?:www|t)\.tiktok\.com\/video\/(\w+)/,
    /https:\/\/(?:www|t)\.tiktok\.com\/[^\/]+\/video\/(\w+)/,
    /https:\/\/(?:www|t)\.tiktok\.com\/share\/video\/(\w+)/,
    /https:\/\/(?:www|t)\.tiktok\.com\/i18n\/share\/video\/(\w+)/,
  ];
  for (const reg of regs) {
    const [, id] = url.match(reg) || [];
    if (id) return id;
  }
}

export async function getYouTubeVideoData(url: string) {
  const id = getYouTubeVideoId(url);
  return {
    id,
    embed: `https://www.youtube.com/embed/${id}`,
    /**
     * | Type     | File Name         | Size     | Ratio |
     * | -------- | ----------------- | -------- | ----- |
     * | default  | default.jpg       | 120x90   | 5:4   |
     * | medium   | mqdefault.jpg     | 320x180  | 16:9  |
     * | high     | hqdefault.jpg     | 480x360  | 5:4   |
     * | standard | sddefault.jpg     | 640x480  | 5:4   |
     * | maxres   | maxresdefault.jpg | 1280x720 | 16:9  |
     */
    cover: `https://i.ytimg.com/vi/${id}/hqdefault.jpg`,
  };
}

/**
 * @see {@link https://developers.tiktok.com/doc/embed-videos/}
 */
export async function getTikTokVideoData(url: string): Promise<{
  version: string;
  type: string;
  html: string;
  title: string;
  width: string;
  height: string;
  author_url: string;
  author_name: string;
  thumbnail_url: string;
  thumbnail_width: number;
  thumbnail_height: number;
  provider_url: string;
  provider_name: string;
}> {
  const res = await fetch(`https://www.tiktok.com/oembed?url=${url}`);
  const json = await res.json();
  return json;
}

/**
 * @description 针对取状态的处理，比如点赞状态和点赞数量
 *
 * @param   {any}     item        [item description]
 * @param   {string}  status_key  [status_key description]
 * @param   {string}  count_key   [count_key description]
 *
 * @return  {[type]}              [return description]
 */
export const itemStatusAndCountHandler = (
  item: any,
  status_key: string,
  count_key: string | Array<string>
) => {
  if (isUndefined(get(item, status_key))) {
    set(item, status_key, false);
  }

  set(item, status_key, !get(item, status_key));

  const count_keys = typeof count_key === "string" ? [count_key] : count_key;

  count_keys.forEach((count_key_item: string) => {
    if (isUndefined(get(item, count_key_item))) {
      set(item, count_key_item, 0);
    }
    set(
      item,
      count_key_item,
      get(item, status_key)
        ? +get(item, count_key_item) + 1
        : Math.max(+get(item, count_key_item) - 1, 0)
    );
  });
};

export const dataURLToBlob = (data_uRL: string): Blob => {
  // 解码 Base64 字符串
  const byteString = atob(data_uRL.split(",")[1]);
  // 获取 MIME 类型
  const mime = data_uRL.split(",")[0].split(":")[1].split(";")[0];

  // 创建一个 ArrayBuffer
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);

  // 将字符转换为字节
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  // 返回 Blob 对象
  return new Blob([ab], { type: mime });
};

/**
 * @description 判断cms列表是否显示分割线
 *
 * @return  {[type]}  [return description]
 */
export const isCmsListSplitLineVisible = (
  list: Array<{ content_type: number }>,
  index: number
): boolean => {
  if (index === list.length - 1) {
    return false;
  }
  const current = list[index];

  const next_index = index + 1;
  const next = list[next_index];

  return next.content_type !== current.content_type;
};

/**
 * @description 判断是否是合法的 URL
 *
 * @param   {string}   url  [url description]
 *
 * @return  {boolean}       [return description]
 */
export const isValidURL = (url: string): boolean => {
  // 正则表达式匹配URL（包括无协议的情况）
  const url_pattern = /^(https?:\/\/)?([a-z0-9-]+\.)+[a-z]{2,6}(\/[^\s]*)?$/i;
  return url_pattern.test(url);
};

/**
 * @description 串行执行多个异步函数
 *
 * @return  {[type]}  [return description]
 */
export const safeExecutePromisesSequentially = async <T>(
  promises: Array<() => Promise<T>>
): Promise<Array<T | { error: any }>> => {
  const results: (T | { error: any })[] = [];

  for (const promiseFunc of promises) {
    try {
      const result = await promiseFunc();
      results.push(result);
    } catch (error) {
      console.error("Rejected:", error);
      results.push({ error });
    }
  }

  return results; // 返回所有结果
};

export const getNavigatorLang = () => {
  if (navigator.languages != undefined) return navigator.languages[0];
  return navigator.language;
};

export const removeUrlKeys = (url: string, kyes: string[]): string => {
  const url_obj = new URL(url);
  const search_params = url_obj.searchParams;

  kyes.forEach((key) => {
    search_params.delete(key);
  });

  url_obj.search = search_params.toString();
  return url_obj.toString();
};

export const onForcedDarkMode = (callback: () => void): void => {
  const img = new Image();
  img.onload = function () {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    ctx.drawImage(img, 0, 0);
    const imageData = ctx.getImageData(0, 0, 1, 1);
    const [r, g, b] = imageData.data;
    if ((r & b & g) < 255) {
      // dark mode callback
      callback();
    }
  };
  img.src =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IndoaXRlIi8+PC9zdmc+";
};

/**
 * @description 处理分享链接
 *
 * @param   {Record<string><string>}  params  [params description]
 *
 * @return  {<string><string>}                [return description]
 */
export const resovledShareUrl = (params: Record<string, string>): string => {
  const parse_url = qs.parseUrl(location.href);
  Object.assign(parse_url.query, { from: "share" }, params);
  return qs.stringifyUrl(parse_url);
};

/**
 * @description 加载图片
 * @param url
 * @returns
 */
async function loadImage(url: string): Promise<HTMLImageElement> {
  if (!url) {
    throw new Error("[loadImage] image url is required!");
  }
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.onload = () => resolve(img);
    img.onerror = (error) => reject(error);
  });
}

/**
 * @description 加载图片信息
 * @param   {string<width>}  url  [url description]
 * @return  {number}                    [return description]
 */
export const loadImageSize = async (url: string): Promise<{ width: number; height: number }> => {
  if (!url) {
    throw new Error("image url is required!");
  }

  const img = await loadImage(url);
  return { width: img.width, height: img.height };
};

export const removeUrlTrailingSlash = (url: string): string => {
  if (!url) {
    throw new Error("url is required!");
  }

  // 使用正则表达式去掉末尾的斜杠
  return url.replace(/\/+$/, "");
};

export const ensureUrlKeepProtocol = (url: string) => {
  if (!url) {
    throw new Error("url is required!");
  }

  const protocol = window.location.protocol;

  // 检查 URL 是否包含协议
  if (/^https?:\/\//i.test(url)) {
    // URL 已包含协议，直接返回
    return url;
  } else if (/^\/\//.test(url)) {
    // URL 是相对协议，使用当前页面的协议
    return protocol + url;
  } else {
    // URL 没有协议，使用当前页面的协议加上 URL
    return protocol + "//" + url;
  }
};

/**
 * @description 检查 url 是否为同源
 *
 * @param   {string}   url  [url description]
 *
 * @return  {boolean}       [return description]
 */
export const checkUrlIsSameOrigin = (url: string): boolean => {
  if (!url) {
    return false;
  }

  url = ensureUrlKeepProtocol(url);

  try {
    const url_object = new URL(url);
    return url_object.origin === window.location.origin;
  } catch (error) {
    // 如果 URL 仍然无效，返回 false
    console.error("Invalid URL:", url);
    return false;
  }
};

/**
 * @description 获取默认打开 url 的 target
 * @param   {string}  url  [url description]
 * @return  {[type]}       [return description]
 */
export const getDefaultOpenUrlTarget = (url: string) => {
  if (checkUrlIsSameOrigin(url)) {
    return "_self";
  }
  return "_blank";
};

/**
 * @description 打开链接, 自动判断是否同域, 同域则原页面打开, 跨域则新页面打开
 * @param url 目标链接
 * @param target 强制指定打开方式
 * @param window_features 新页面打开的参数
 */
export const openUrl = (
  url: string,
  target: undefined | string = getDefaultOpenUrlTarget(url),
  window_features: undefined | string = "noopener,noreferrer"
) => {
  // isSafari() ? window.open(url, target) :
  window.open(url, target, window_features);
};

/**
 * 根据背景色计算最佳文本颜色（黑色或白色）
 * @param backgroundColor - 背景色（支持 hex、rgb、rgba 格式）
 * @param theme - 主题模式 'light' | 'dark'，默认为 'light'
 * @returns 返回 "black" 或 "white"
 */
export const getTextColorByBackground = (
  backgroundColor: string,
  theme: "light" | "dark" = "light"
): "black" | "white" => {
  // 将颜色转换为 RGBA 值
  const getRGBA = (color: string): [number, number, number, number] => {
    // 处理 hex 格式
    if (color.startsWith("#")) {
      const hex = color.replace("#", "");
      if (hex.length === 8) {
        // #RRGGBBAA 格式
        return [
          parseInt(hex.slice(0, 2), 16),
          parseInt(hex.slice(2, 4), 16),
          parseInt(hex.slice(4, 6), 16),
          parseInt(hex.slice(6, 8), 16) / 255,
        ];
      } else if (hex.length === 6) {
        // #RRGGBB 格式
        return [
          parseInt(hex.slice(0, 2), 16),
          parseInt(hex.slice(2, 4), 16),
          parseInt(hex.slice(4, 6), 16),
          1,
        ];
      }
    }

    // 处理 rgb/rgba 格式
    const matches = color.match(/\d*\.?\d+/g);
    if (!matches) return [255, 255, 255, 0]; // 默认透明背景

    const rgba = matches.map(Number);
    // 如果是 RGB 格式,添加不透明度 1
    if (rgba.length === 3) rgba.push(1);
    // 确保 alpha 值在 0-1 之间
    if (rgba[3] > 1) rgba[3] = rgba[3] / 255;

    return rgba as [number, number, number, number];
  };

  const [r, g, b, a] = getRGBA(backgroundColor);

  // 根据主题选择混合的底色
  const baseColor = theme === "light" ? 255 : 0;

  // 根据透明度混合背景色和底色
  const mix = (c: number, a: number) => Math.round(c * a + baseColor * (1 - a));

  // 混合后的实际 RGB 值
  const finalR = mix(r, a);
  const finalG = mix(g, a);
  const finalB = mix(b, a);

  // 使用 YIQ 算法计算亮度
  // YIQ = (R * 299 + G * 587 + B * 114) / 1000
  const yiq = (finalR * 299 + finalG * 587 + finalB * 114) / 1000;

  // YIQ >= 128 认为是浅色背景，使用黑色文本
  // YIQ < 128 认为是深色背景，使用白色文本
  return yiq >= 128 ? "black" : "white";
};

export const moveArrayItem = <T>(array: T[], from_index: number, to_index: number): T[] => {
  if (from_index < 0 || from_index >= array.length || to_index < 0 || to_index >= array.length) {
    throw new Error("Invalid index");
  }
  const [item] = array.splice(from_index, 1);
  array.splice(to_index, 0, item);
  return array;
};

/**
 * 数值格式化工具函数
 * @param num 需要格式化的数字
 * @returns 符合业务要求的格式化字符串
 */
export function formatNumber(num: number): string {
  const K = 1000; // 千单位
  const M = K * K; // 百万单位

  num = Number(num);

  // 边界条件处理（包含所有可能的数值类型）
  if (typeof num !== "number" || Number.isNaN(num) || !num) return "";
  const n = Math.floor(num); // 处理浮点数

  // 0 值直接返回
  if (n === 0) return "0";

  // 从最大单位开始判断
  if (n >= 1000 * M) {
    // [1000M, +∞)
    return "999m+";
  } else if (n >= 10 * M) {
    // [10M, 1000M)
    return `${Math.floor(n / M)}m`;
  } else if (n >= M) {
    // [1M, 10M)
    const value = Math.floor(n / (M / 10)) / 10; // 保留一位小数
    return `${value.toFixed(1).endsWith(".0") ? value.toFixed(0) : value.toFixed(1)}m`;
  } else if (n >= 10 * K) {
    // [10K, 1M)
    return `${Math.floor(n / K)}k`;
  } else if (n >= K) {
    // [1K, 10K)
    const value = Math.floor(n / (K / 10)) / 10;
    return `${value.toFixed(1).endsWith(".0") ? value.toFixed(0) : value.toFixed(1)}k`;
  } else {
    // (0, 1K)
    return `${n}`;
  }
}

export const decodeHTMLEntities = (html: string) => {
  return html
    .replace(/&amp;/g, "&")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
};

/**
 * 获取当前选中的文本内容（跨浏览器兼容）
 * @returns 选中的文本字符串，若未选中则返回空字符串
 */
export const isSelected = (): boolean => {
  if (window.getSelection) {
    return Boolean(window.getSelection()?.toString() || "");
  } else if (document.getSelection) {
    return Boolean(document.getSelection()?.toString() || "");
  } else {
    const selection = (document as any).selection?.createRange();
    return Boolean(selection?.text?.toString() || "");
  }
};

export const checkCameraExists = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const bool = devices.some((device) => device.kind === "videoinput");
    return bool;
  } catch (err) {
    return false;
  }
};

/**
 * 深度检查两个对象键值类型一致性
 * 支持嵌套对象、数组、日期等复杂类型判断
 * @param objA 对象A
 * @param objB 对象B
 * @returns 类型结构是否一致
 */
export function isTypeStructureEqual(objA: unknown, objB: unknown): boolean {
  // 处理 null/undefined 的边界情况
  if (objA === null || objB === null) return objA === objB;
  if (typeof objA !== typeof objB) return false;

  // 处理非对象类型
  if (typeof objA !== "object" || typeof objB !== "object") {
    return typeof objA === typeof objB;
  }

  // 处理数组类型
  if (Array.isArray(objA) !== Array.isArray(objB)) return false;

  // 获取类型标记（处理特殊对象类型）
  const getTypeTag = (obj: object) => {
    if (obj instanceof Date) return "Date";
    if (obj instanceof RegExp) return "RegExp";
    return Object.prototype.toString.call(obj).slice(8, -1);
  };
  if (getTypeTag(objA) !== getTypeTag(objB)) return false;

  // 递归检查对象键值类型
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);

  if (keysA.length !== keysB.length) return false;

  return keysA.every((key) => {
    if (!(key in (objB as object))) return false;
    return isTypeStructureEqual(
      (objA as Record<string, unknown>)[key],
      (objB as Record<string, unknown>)[key]
    );
  });
}
