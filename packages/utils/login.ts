// types
import { LoginPopConfig, AccountSdkConfig, IntlSdkInstance } from "../types/login";
import { LoginRes } from "../types/login";

// configs
import {
  INTL_GLOBAL_ACCOUNT_APP_ID,
  INTL_GLOBAL_ENV,
  INTL_GLOBAL_GAME_ID,
  INTL_HMT_ACCOUNT_APP_ID,
  INTL_HMT_ENV,
  INTL_HMT_GAME_ID,
  INTL_ENV,
  INTL_ENV_SG,
  INTL_LIP_COMMUNITY_APP_ID,
  INTL_LIP_COMMUNITY_GAME_ID,
} from "../configs/intl";
import { STANDARD_INTL_LOGIN_SDK_LANG_MAP } from "../configs/standard";
import { ENV_DEVELOPMENT, ENV_PRE, ENV_PROD, ENV_TEST } from "../configs/env";
import { IntlLogin } from "@tencent/pa-cms-utils";

// utils
import { getEnv, injectScript } from "./tools";
import { urlSearchObjectify } from "./qs";
import { cloneDeep, get } from "lodash-es";
import { getStandardizedLang } from "./standard";
import IntlgameAccountApi from "@intlsdk/account-api";

//
const url_search_obyject = urlSearchObjectify();

export const getWebid = (options: { gameid: string; env?: string }) => {
  const { gameid, env } = options;
  let config_env = env || url_search_obyject.env;

  if (config_env === ENV_DEVELOPMENT) {
    config_env = ENV_TEST;
  }

  const config = {
    [INTL_GLOBAL_GAME_ID]: {
      [ENV_TEST]: "224ae6b9-8327-48e0-a1fe-4c824c00707b",
      // [ENV_TEST]: "0e02c79c-da29-468a-9b63-a2b6a7624a44",
      [ENV_PRE]: "0e02c79c-da29-468a-9b63-a2b6a7624a44",
      [ENV_PROD]: "0e02c79c-da29-468a-9b63-a2b6a7624a44",
    },
    [INTL_HMT_GAME_ID]: {
      [ENV_TEST]: "2a1212cb-a7d1-4226-ab3e-c955a7c74039",
      // [ENV_TEST]: "a417cc07-98a5-42f8-b6e1-6ab44bb06c5c",
      [ENV_PRE]: "a417cc07-98a5-42f8-b6e1-6ab44bb06c5c",
      [ENV_PROD]: "a417cc07-98a5-42f8-b6e1-6ab44bb06c5c",
    },
  }[gameid];

  return get(config, config_env, get(config, ENV_PROD));
};

const getGameConifg = () =>
  ({
    [INTL_GLOBAL_ENV]: {
      env: getLoginPopEnv(INTL_GLOBAL_ENV),
      gameID: INTL_GLOBAL_GAME_ID,
      appID: INTL_GLOBAL_ACCOUNT_APP_ID,
      webID: getWebid({ gameid: INTL_GLOBAL_GAME_ID }),
    },
    [INTL_HMT_ENV]: {
      env: getLoginPopEnv(INTL_HMT_ENV),
      gameID: INTL_HMT_GAME_ID,
      appID: INTL_HMT_ACCOUNT_APP_ID,
      webID: getWebid({ gameid: INTL_HMT_GAME_ID }),
    },
    [INTL_ENV]: {
      env: INTL_ENV_SG,
      gameID: INTL_LIP_COMMUNITY_GAME_ID,
      appID: INTL_LIP_COMMUNITY_APP_ID,
    },
  } as Record<string, AccountSdkConfig>);

export const getAccountConfigByGameId = (game_id: string) => {
  const config = getGameConifg();
  for (const [_, account_config] of Object.entries(config)) {
    if (account_config.gameID === game_id) {
      return account_config;
    }
  }
  return config[INTL_GLOBAL_ENV];
};

export const getAccountConfig = (area: string | number) => {
  return getGameConifg()[area || INTL_GLOBAL_ENV] as AccountSdkConfig;
};

export const getLoginPopEnv = (area?: string) => {
  let intl_login_env = url_search_obyject.env;
  if (intl_login_env) {
    return intl_login_env;
  }

  const env = getEnv();
  intl_login_env = area === INTL_HMT_ENV ? INTL_HMT_ENV : INTL_GLOBAL_ENV;

  // console.log(`[getLoginPopEnv] intl_login_env`, intl_login_env, area, env);

  return (
    {
      [ENV_TEST]: intl_login_env,
      [ENV_PRE]: intl_login_env,
      [ENV_PROD]: intl_login_env,
    }[env] || intl_login_env
  );
};

export function useAccount(config: AccountSdkConfig) {
  const ensureAccountApiExist = async () => {
    if (!window.IntlgameAccountApi) {
      await injectScript("https://common-web.intlgame.com/sdk-cdn/account-api/latest/index.umd.js");
    }
  };
  const getAccountSdk = async (params?: {
    accountPlatType?: number;
    env?: string;
  }): Promise<IntlSdkInstance> => {
    await ensureAccountApiExist();
    const account_config = Object.assign({}, config, params, { gameID: Number(config.gameID) });
    return new IntlgameAccountApi!(account_config) as IntlSdkInstance;
  };
  return {
    getAccountSdk,
  };
}

export enum Step {
  loading = "loading",
  rendered = "rendered",
  logined = "logined",
  registered = "registered",
}

export const useLogin = (config: LoginPopConfig) => {
  let login_pop_config: LoginPopConfig = config;

  const setLoginPopConfig = (new_login_pop_config: Partial<LoginPopConfig>) => {
    Object.assign(login_pop_config, new_login_pop_config);
  };

  const login = async (target: string, emits: (step: Step, value: null | LoginRes) => void) => {
    console.log(`[login] login_pop_config`, JSON.stringify(login_pop_config));

    const current_lang = getStandardizedLang() as keyof typeof STANDARD_INTL_LOGIN_SDK_LANG_MAP;
    const login_helper: any = new IntlLogin(cloneDeep(login_pop_config));
    emits(Step.rendered, null);

    login_helper.on("onRegister", (registered_res: any) => {
      console.log("[onRegister] registered_res", registered_res);
      emits(Step.registered, registered_res);
    });

    const { sdk } = await login_helper.loadSDK();
    const sdk_lang =
      login_pop_config.config.langType || STANDARD_INTL_LOGIN_SDK_LANG_MAP[current_lang];

    console.log(`[login] sdk_lang`, sdk_lang);
    sdk.changeLanguage(sdk_lang);
    // console.log(`[login] login_helper`, login_helper);
    const login_res = (await login_helper.login(target)) as LoginRes;
    emits(Step.logined, login_res);
  };

  return {
    login,
    setLoginPopConfig,
  };
};

export const getToLoginQuery = (
  append_query: undefined | Record<string, any> = urlSearchObjectify()
) => {
  return {
    to: location.pathname,
    ...append_query,
  };
};
