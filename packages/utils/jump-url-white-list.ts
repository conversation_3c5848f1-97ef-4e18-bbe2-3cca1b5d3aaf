// types

// utils
import { get } from "lodash-es";
import { JumpUrlWhiteListItem, JumpUrlWhiteListType } from "../types/jump-url-white-list";
import { useCDNConfigs } from "./cdn";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "./checker";

const { getCDNConfigs } = useCDNConfigs();

export const useJumpUrlWhiteListCheck = () => {
  const check = async (url: string): Promise<boolean> => {
    const { jump_url_white_list } = getCDNConfigs();
    const configs = get(jump_url_white_list, "configs.values", []);

    console.log("[useJumpUrlWhiteListCheck][check] configs", configs);

    // 木有配置
    if (!configs.length) {
      return false;
    }

    const checkers: Checker[] = [];

    configs?.forEach((item: JumpUrlWhiteListItem) => {
      switch (item.type) {
        case JumpUrlWhiteListType.full_path:
          checkers.push(new FullPathChecker(url, item.values));
          break;
        case JumpUrlWhiteListType.domain:
          checkers.push(new DomainChecker(url, item.values));
          break;
        case JumpUrlWhiteListType.domain_regex:
          checkers.push(new DomainRegexChecker(url, item.values));
          break;
        default:
      }
    });

    const checker_chain = new CheckerChain(checkers);
    const check_res = await checker_chain.doCheck();

    console.log("[useJumpUrlWhiteListCheck][check] check_res", check_res);
    return check_res;
  };

  return {
    check,
  };
};
