import { STANDARD_CMS_LANG_MAP, STANDARD_SS_SUPPORT_LANG } from "../configs/standard";
import { getStandardizedLang } from "./standard";
import { setHtmlDir, setHtmlLang } from "./dom";
import { useIndexDB } from "./storage";
import { STORAGE_INDEXDB_I18N_CONFIGS, STORAGE_WINDOW_I18N_CONFIGS } from "../configs/storage";

const win: any = window;
const current_url_lang = getStandardizedLang(STANDARD_SS_SUPPORT_LANG);
const cache_key = `${STORAGE_INDEXDB_I18N_CONFIGS}${current_url_lang}`;

const { staleWhileRevalidate } = useIndexDB();

export const loadLanguageConfig = staleWhileRevalidate(cache_key, {
  async handler(lang?: string) {
    lang = lang || current_url_lang;

    const en = () => import("../static/js/i18n/merged/src/locales/lang/en");
    const ja = () => import("../static/js/i18n/merged/src/locales/lang/ja");
    const ko = () => import("../static/js/i18n/merged/src/locales/lang/ko");
    const zh = () => import("../static/js/i18n/merged/src/locales/lang/zh");
    const tw = () => import("../static/js/i18n/merged/src/locales/lang/zh-TW");
    const lang_pkg =
      {
        [STANDARD_CMS_LANG_MAP.en]: en,
        [STANDARD_CMS_LANG_MAP.ja]: ja,
        [STANDARD_CMS_LANG_MAP.zh]: zh,
        [STANDARD_CMS_LANG_MAP.tw]: tw,
        [STANDARD_CMS_LANG_MAP.ko]: ko,
      }[lang] || en;

    const [lang_config] = await Promise.all([lang_pkg()]);
    const res = Object.assign(lang_config.default);

    return res;
  },
  callback(value) {
    win[STORAGE_WINDOW_I18N_CONFIGS] = value;
  },
});

export const loadLanguageAsync = async (
  i18n: any,
  options?: {
    lang?: string;
  }
) => {
  const lang = options?.lang || current_url_lang;

  // console.log(
  //   `[loadLanguageAsync] win[STORAGE_WINDOW_I18N_CONFIGS]`,
  //   win[STORAGE_WINDOW_I18N_CONFIGS]
  // );

  const res = win[STORAGE_WINDOW_I18N_CONFIGS] || (await loadLanguageConfig(lang));

  i18n.global.setLocaleMessage(lang, res);
  i18n.global.locale.value = lang;

  setHtmlLang();
  setHtmlDir();
};
