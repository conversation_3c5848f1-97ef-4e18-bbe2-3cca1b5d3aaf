import { get, isEmpty } from "lodash-es";
import {
  CMS_TEST_HOST,
  CMS_PROD_HOST,
  CMS_PRE_SG_HOST,
  CMS_GAME_ID,
  CMS_AREA_ID,
  CMS_SOURCE_TYPE,
} from "../configs/cms";
import { ENV_TEST, ENV_PRE, ENV_PROD, ENV_DEVELOPMENT } from "../configs/env";
import { getEnv, isInIntlBrowser, isSameOrigin, openUrl, safeParse } from "./tools";
import { IDetail } from "@tencent/pa-cms-utils";
import qs from "query-string";
import { urlSearchObjectify } from "./qs";
import { GameRulesItem, IBannerExtInfo } from "../types/cms";
import { getStandardizedGameId, getStandardizedLang } from "./standard";

export const getCMSHost = (env: string): string => {
  if (env === ENV_PROD) {
    return `https://${CMS_PROD_HOST}`;
  }
  if (env === ENV_PRE) {
    return `https://${CMS_PRE_SG_HOST}`;
  }
  if (env === ENV_TEST) {
    return `https://${CMS_TEST_HOST}`;
  }
  // return location.origin;
  return `https://${CMS_TEST_HOST}`;
};

export const resovledJumpUrl = (url: string): string => {
  if (!isSameOrigin(url)) {
    return url;
  }
  const parse_url = qs.parseUrl(url, { parseFragmentIdentifier: true });
  const qs_url_search = urlSearchObjectify();
  Object.assign(parse_url.query, qs_url_search);
  return qs.stringifyUrl(parse_url);
};

/**
 * @description 解析CMS数据调整到详情
 *
 * @return  {[type]}  [return description]
 */
export const parseCMSJumpDetail = (
  item: IDetail,
  /** 自定义的打开方法，可以传入统一的携带登录态跳转的方法 */
  openUrlFn?: typeof openUrl
): {
  jump_url?: string;
  external_link?: boolean;
  content_id?: string;
  father_content_id?: string;
  is_group_content?: boolean;
  not_jump?: boolean;
} => {
  const {
    jump_link_info,
    content_id,
    content_type,
    // content_class,
    video_info,
    content_source,
    is_group_content,
  } = item;

  const { jump_url = "", jump_type } = jump_link_info || get(item, "jump_link", {});
  const is_video = [5, 6].includes(content_type as any) && !!video_info;
  const source = content_source?.source_name || "";

  if (
    // 外链（自建外链或者爬虫链接）
    jump_url &&
    jump_type === 2 &&
    // 且 不是爬虫视频
    !is_video &&
    // 且 不是 YouTube， Twitter等爬虫链接
    !["YouTube", "Twitter"].includes(source)
  ) {
    const window_features = "noopener,noreferrer";
    const resolved_jumped_url = resovledJumpUrl(jump_url);

    (openUrlFn ?? openUrl)(resolved_jumped_url, undefined, window_features);

    return {
      jump_url,
      external_link: true,
    };
  }

  // 外链 3-跳转文章详情 （文章系列id 和 文章id）
  if (jump_type === 3) {
    // 从 jump_url 解析
    const [key, value] = jump_url.split("=").map((item: string) => `${item}`.trim());

    if (!key || !value) {
      console.error("[parseCMSJumpDetail] jump_url 不合法", jump_url);
      return {
        content_id,
        is_group_content,
      };
    }

    const content_id_key = key === "parentId" ? "father_content_id" : "content_id";

    return {
      is_group_content,
      [content_id_key]: value,
    };
  }

  // 静态图，仅展示，不跳转
  if (jump_type === 4) {
    return {
      not_jump: true,
    };
  }

  return {
    content_id,
    is_group_content,
  };
};

export const cmsInfoVisible = (info: IDetail, intl_game_id: string) => {
  const DISTINGUISH_GAME_VALUE = "0";
  const game_id = getStandardizedGameId();

  const ext_info = get(info, "ext_info");
  const ext: IBannerExtInfo = ext_info ? JSON.parse(ext_info) : { thumbnail: [] };

  const inner_game_rules: any = get(ext, "inner_game_rules");
  const outer_game_rules: any = get(ext, "outer_game_rules");
  // console.log(`inner_game_rules`, inner_game_rules);
  // console.log(`outer_game_rules`, outer_game_rules);

  // 是否为游戏内
  const isInGame = () => {
    return isInIntlBrowser() && game_id;
  };

  // 配置为空，统一屏蔽
  const isEmptyGameRules = (game_rules: Array<GameRulesItem>) => !game_rules || isEmpty(game_rules);

  // 游戏内不区分游戏
  const isNotDistinguishGame = (game_rules: Array<GameRulesItem>) => {
    return game_rules.some((item: GameRulesItem) => item.value === DISTINGUISH_GAME_VALUE);
  };

  // 判断当前的游戏 id 是否在配置规则
  const isGameIdInGameRules = (game_rules: Array<GameRulesItem>) => {
    return game_rules.some((item: GameRulesItem) => item.value === game_id);
  };

  // 游戏外 + LIP 官网
  const isLIPOfficialSite = (game_rules: Array<GameRulesItem>) => {
    return game_rules.some((item: GameRulesItem) => item.value === DISTINGUISH_GAME_VALUE);
  };

  // 游戏内
  const handleInnerGameRules = (game_rules: Array<GameRulesItem>): boolean => {
    console.log(`[handleInnerGameRules] game_rules`, game_rules);
    if (isEmptyGameRules(game_rules)) {
      return false;
    }
    if (isNotDistinguishGame(game_rules)) {
      return true;
    }
    return isGameIdInGameRules(game_rules);
  };

  // 游戏外
  const handleOuterGameRules = (game_rules: Array<GameRulesItem>): boolean => {
    console.log(`[handleOuterGameRules] game_rules`, game_rules);

    if (isEmptyGameRules(game_rules)) {
      return false;
    }
    if (isLIPOfficialSite(game_rules)) {
      const is_only_one_rules = game_rules.length === 1;
      if (is_only_one_rules) return game_id ? false : true;
      return game_id ? isGameIdInGameRules(game_rules) : true;
    }
    return isGameIdInGameRules(game_rules);
  };

  // 配置了新规则，使用新规则
  if (typeof inner_game_rules !== "undefined" || typeof outer_game_rules !== "undefined") {
    return isInGame()
      ? handleInnerGameRules(inner_game_rules)
      : handleOuterGameRules(outer_game_rules);
  }

  //////////////////////////////////////////////////////////////////////////////////////////////////////
  // 旧规则
  const hide_rule = get(ext, "hide_rule.value", "");
  const cms_config_intl_gameid = get(ext, "intl_gameid", []);
  // 是否是游戏内
  const in_game = intl_game_id && isInIntlBrowser();

  /**
   * 1、关联游戏+游内屏蔽---这个游戏游内展示，其他游戏内不展示，游外展示
   * 2、关联游戏+游外屏蔽---这个游戏游内展示，其他游戏游内不展示，所有游戏外都屏蔽
   * 3、关联游戏+永不屏蔽---选中的游戏游外游内都展示，其他游戏都不展示
   * 4、不关联游戏+游内屏蔽---所有游内都屏蔽，游外展示
   * 5、不关联游戏+游外屏蔽---所有游外都屏蔽，游内展示
   * 6、不关联游戏+永不屏蔽---所有游内游外都展示
   */

  // 关联游戏
  const isBoolean = (cms_config_intl_gameid?: any, gameid?: string) => {
    const ids = [];
    if (Array.isArray(cms_config_intl_gameid)) {
      for (const item of cms_config_intl_gameid) {
        if (item.value) {
          ids.push(item.value);
        }
      }
    } else {
      cms_config_intl_gameid.value && ids.push(cms_config_intl_gameid.value);
    }

    // 配置屏蔽规则
    if (hide_rule) {
      // 游戏内屏蔽
      if (hide_rule === "in_game") {
        // 游戏内
        if (in_game) return ids.includes("0") ? false : ids.includes(intl_game_id);
        // 游戏外
        return true;
      }

      // 游戏外屏蔽
      if (hide_rule === "out_game") {
        // 游戏外
        if (!in_game) return false;
        // 游戏内
        return ids.includes("0") ? true : ids.includes(intl_game_id);
      }

      // // 不屏蔽：close
      // // 不区分游戏，游戏内、外都展示
      // if (ids.includes("0")) return true;
      // // 关联游戏，必须是游戏内
      // return in_game && ids.includes(intl_game_id);

      // 不屏蔽：close
      return ids.includes("0")
        ? // 都展示
          true
        : // 关联游戏，只在关联游戏展示（游戏内、游戏外）
          ids.includes(intl_game_id);
    }

    return isEmpty(ids) || !gameid || ids.includes("0") || ids.includes(gameid);
  };

  return isBoolean(cms_config_intl_gameid, intl_game_id);
};

/**
 * @description 兼容某些场景
 *
 * @param   {string}  content_id  [content_id description]
 *
 * @return  {[type]}              [return description]
 */
export const resolvedCmsContentId = (content_id: string) => {
  const { id, parentId, father_content_id } = urlSearchObjectify();

  return content_id || id || parentId || father_content_id;
};

/** 从 CMS 富文本中获取纯文本 */
export const getTextFromCmsRichText = (html: string) => {
  const doc = new DOMParser().parseFromString(html, "text/html");
  return doc.body.textContent;
};

export const resovledCmsConfig = (cms_config?: any) =>
  Object.assign(
    {
      cms_gameid: CMS_GAME_ID,
      cms_areaid: CMS_AREA_ID,
      source_type: CMS_SOURCE_TYPE,
      cms_lang: getStandardizedLang(),
      custom_cms_url: getCMSHost(getEnv()),
    },
    cms_config
  );

export const useNoCookie = () => {
  const { usecookie } = urlSearchObjectify();
  // console.log(usecookie);
  // 先默认关闭
  if ([ENV_DEVELOPMENT, ENV_TEST, ENV_PRE].includes(getEnv()) && !usecookie) {
    return true;
  }
  return false;
};

/**
 * 根据CMS端配置的ext_info信息，在前端对数据进行过滤（目前用于各banner和主页的金刚区）
 * - game_inner_range: 支持的游戏ID列表
 * - game_outer_range: 支持的语言列表
 */
export const filterCmsItem = (info: {
  ext_info: string | undefined;
  lang: string;
  game_id: string;
}) => {
  const in_game = isInIntlBrowser();
  const ext_info = safeParse<{
    game_inner_range?: { value: string }[];
    game_outer_range?: { value: string }[];
  }>(info.ext_info);
  if (!ext_info) return !in_game; // 如果没有配置ext_info，在游戏内不展示，在游戏外不限制
  if (in_game && !ext_info.game_inner_range?.find((i) => i.value == info.game_id)) return false;
  if (ext_info.game_outer_range && !ext_info.game_outer_range?.find((i) => i.value == info.lang))
    return false;
  return true;
};
