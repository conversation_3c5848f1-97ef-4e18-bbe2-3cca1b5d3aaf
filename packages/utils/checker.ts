// types
import { Gray<PERSON>leLogic, GrayscaleLogicRelation, GrayscaleRuleItem } from "../types/grayscale";

// utils
import { has } from "lodash-es";
import { urlSearchObjectify } from "./qs";
import { ensureUrlKeepProtocol, removeUrlTrailingSlash } from "./tools";

const qs_url_search = urlSearchObjectify();

const compareMinAndMax = (target: number, min?: number, max?: number): boolean => {
  return (
    (!min || target >= min) && // 如果没有设置 min，则始终为 true，否则检查 target 是否大于等于 min
    (!max || target <= max) // 如果没有设置 max，则始终为 true，否则检查 target 是否小于等于 max
  );
};

const isEmptyConfigValues = (config_values: Array<string>) => {
  return !config_values?.length;
};

export interface Checker {
  /**
   * 验证是否通过
   * @return {boolean} true-校验通过 false-校验不通过
   */
  check(): Promise<boolean>;
}

export class LogicCheckerChain {
  private checkers: Checker[];
  private relation: GrayscaleLogicRelation;

  constructor(checkers: Checker[], relation: GrayscaleLogicRelation) {
    this.checkers = checkers;
    this.relation = relation;
  }

  async doCheck(): Promise<boolean> {
    const arr: Array<boolean> = [];

    for (const checker of this.checkers) {
      const checked = await checker.check();
      arr.push(checked);
    }

    return this.relation === GrayscaleLogicRelation.and
      ? arr.every((bool: boolean) => bool)
      : arr.some((bool: boolean) => bool);
  }
}

export class CheckerChain {
  private checkers: Checker[];

  constructor(checkers: Checker[]) {
    this.checkers = checkers;
  }

  async doCheck(): Promise<boolean> {
    const arr: Array<boolean> = [];

    for (const checker of this.checkers) {
      const checked = await checker.check();
      arr.push(checked);
    }

    return arr.some((bool: boolean) => bool);
  }
}

export class UrlNoKeywordChecker implements Checker {
  private keyword: string;

  constructor(keyword: string) {
    this.keyword = keyword;
  }
  async check(): Promise<boolean> {
    const bool = !has(qs_url_search, `no_${this.keyword}`);
    console.log(`[UrlNoKeywordChecker] bool ${bool}`);
    return bool;
  }
}

export class TimestampChecker implements Checker {
  private logic: GrayscaleLogic;

  constructor(logic: GrayscaleLogic) {
    this.logic = logic;
  }

  async check(): Promise<boolean> {
    const { rules, relation } = this.logic;

    if (!rules?.length) {
      console.log(`[TimestampChecker] bool: `, false);
      return false;
    }

    const checkers = rules.map((rule_item: GrayscaleRuleItem) => {
      return new (class RuleItemChecker implements Checker {
        async check(): Promise<boolean> {
          const now = Date.now() / 1000;
          const { min, max } = rule_item;
          return compareMinAndMax(now, min, max);
        }
      })();
    });
    const checker_chain = new LogicCheckerChain(checkers, relation || GrayscaleLogicRelation.or);
    const bool = await checker_chain.doCheck();

    console.log(`[TimestampChecker] bool: `, bool);
    return bool;
  }
}

export class OpenidLastDigitsChecker implements Checker {
  private logic: GrayscaleLogic;
  private openid: string;

  constructor(logic: GrayscaleLogic, openid: string) {
    this.logic = logic;
    this.openid = `${openid}`;
  }

  async check(): Promise<boolean> {
    const { rules, relation, digits } = this.logic;

    if (!this.openid || !rules?.length) {
      console.log(`[OpenidLastDigitsChecker] bool ${false}`);
      return false;
    }

    const compare = +this.openid.slice(-(digits || 2));

    const checkers = rules.map((rule_item: GrayscaleRuleItem) => {
      return new (class RuleItemChecker implements Checker {
        async check(): Promise<boolean> {
          const { min, max } = rule_item;
          return compareMinAndMax(compare, min, max);
        }
      })();
    });

    const checker_chain = new LogicCheckerChain(checkers, relation || GrayscaleLogicRelation.or);
    const bool = await checker_chain.doCheck();

    console.log(`[OpenidLastDigitsChecker] bool ${bool}`);
    return bool;
  }
}

export class OpenidChecker implements Checker {
  private logic: GrayscaleLogic;
  private openid: string;

  constructor(logic: GrayscaleLogic, openid: string) {
    this.logic = logic;
    this.openid = `${openid}`;
  }

  async check(): Promise<boolean> {
    const { rules, relation } = this.logic;

    if (!rules?.length || !this.openid) {
      console.log(`[OpenidChecker] bool ${false}`);
      return false;
    }

    let bool = rules.includes(this.openid);
    if (relation === GrayscaleLogicRelation.not_contains) {
      bool = !bool;
    }
    console.log(`[OpenidChecker] bool ${bool}`);
    return bool;
  }
}

export class FullPathChecker implements Checker {
  private url: string;
  private config_values: Array<string>;

  constructor(url: string, config_values: Array<string>) {
    this.url = url;
    this.config_values = config_values || [];
  }

  async check(): Promise<boolean> {
    if (isEmptyConfigValues(this.config_values)) {
      return false;
    }

    return this.config_values.includes(removeUrlTrailingSlash(ensureUrlKeepProtocol(this.url)));
  }
}

export class DomainChecker implements Checker {
  private url: string;
  private config_values: Array<string>;

  constructor(url: string, config_values: Array<string>) {
    this.url = url;
    this.config_values = config_values || [];
  }

  async check(): Promise<boolean> {
    if (isEmptyConfigValues(this.config_values)) {
      return false;
    }

    try {
      // 创建一个 URL 对象以便于提取域名
      const parsed_url = new URL(this.url);
      const hostname = parsed_url.hostname;

      // 检查域名是否在白名单中
      return this.config_values.includes(hostname);
    } catch (error) {
      console.error("[DomainChecker][check] catch an error: ", error);
      return false;
    }
  }
}

export class DomainRegexChecker implements Checker {
  private url: string;
  private config_values: Array<string>;

  constructor(url: string, config_values: Array<string>) {
    this.url = url;
    this.config_values = config_values || [];
  }

  async check(): Promise<boolean> {
    if (isEmptyConfigValues(this.config_values)) {
      return false;
    }

    try {
      // 创建一个 URL 对象以便于提取域名
      const parsed_url = new URL(this.url);
      const hostname = parsed_url.hostname;

      // 将通配符转换为正则表达式
      const regexes = this.config_values.map((pattern: string) => {
        // 将 '*' 替换为 '.*'，并添加开始和结束锚点
        const regex_pattern = pattern.replace(/\*/g, ".*");
        return new RegExp(`^${regex_pattern}$`);
      });

      // 检查域名是否匹配任意一个正则表达式
      return regexes.some((regex) => regex.test(hostname));
    } catch (error) {
      console.error("[DomainRegexChecker][check] catch an error: ", error);
      return false;
    }
  }
}
