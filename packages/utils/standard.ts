import {
  STANDARD_URL_KEY,
  STANDARD_LANG_KEY,
  STANDARD_GAME_ID_KEY,
  STANDARD_CMS_LANG_MAP,
  StandardCmsLangMapType,
  STANDARD_SS_SUPPORT_LANG,
} from "../configs/standard";
import {
  STORAGE_COOKIE_GAME_ID,
  STORAGE_COOKIE_LANG,
  STORAGE_LS_GAME_ID,
  STORAGE_LS_LANG,
} from "../configs/storage";
import { urlSearchObjectify } from "./qs";
import { useLSStorage } from "./storage";
import { getNavigatorLang } from "./tools";
import Cookies from "js-cookie";

const { getStorage } = useLSStorage();

let cache_lang: string = "";

export const getStandardizedCookie = (name: string) => Cookies.get(name);

export const setStandardizedCookie = (
  name: string,
  value: string,
  attributes?: Cookies.CookieAttributes
) =>
  Cookies.set(
    name,
    value,
    Object.assign(
      {
        path: "/",
        // domain: location.host.split(".").slice(1).join("."),
        domain: location.host,
        expires: 365,
      },
      attributes
    )
  );

/**
 * @description 标准化从 url 上获取参数值
 */
export const getStandardUrlParamValue = (param: string): string => {
  const qs_url = urlSearchObjectify();

  let value: string = "";

  const entries = Object.entries(STANDARD_URL_KEY).filter((item) => item[1] === param);

  entries.forEach((item) => {
    const key: string = item[0] || "";
    const to_lower_key: string = key?.toLowerCase() || "";

    if (!value && key) {
      value = qs_url[key] || qs_url[to_lower_key];
    }
  });

  return value;
};

/**
 * @description 标准化获取URL语言值
 */
export const getStandardizedUrlLang = (): string => {
  type UrlLangValue = keyof typeof STANDARD_CMS_LANG_MAP;

  const url_param_lang_value = getStandardUrlParamValue(STANDARD_LANG_KEY) as UrlLangValue;
  const to_lower_url_param_lang_value = url_param_lang_value?.toLowerCase() as UrlLangValue;

  const lang =
    STANDARD_CMS_LANG_MAP[to_lower_url_param_lang_value] ||
    STANDARD_CMS_LANG_MAP[url_param_lang_value];

  return lang;
};

/**
 * @description 标准化获取语言值
 */
export const getStandardizedLang = (support_list?: StandardCmsLangMapType[]): string => {
  support_list = support_list || STANDARD_SS_SUPPORT_LANG;

  if (cache_lang) {
    return cache_lang;
  }

  /**
   * @description url => storage => navigator => default
   */
  const lang =
    // url
    getStandardizedUrlLang() ||
    // cookie
    getStandardizedCookie(STORAGE_COOKIE_LANG) ||
    // storage
    getStorage(STORAGE_LS_LANG) ||
    // navigator
    getStandardNavigatorLang() ||
    // default
    STANDARD_CMS_LANG_MAP.en;

  return (cache_lang =
    //
    support_list?.includes(lang) ? lang : STANDARD_CMS_LANG_MAP.en);
};

/**
 * @description 标准化获取 URL game id
 */
export const getStandardizedUrlGameId = (): string => {
  const value = getStandardUrlParamValue(STANDARD_GAME_ID_KEY);

  return value;
};

/**
 * @description 标准化获取 game id
 */
export const getStandardizedGameId = (): string => {
  const value =
    getStandardizedUrlGameId() ||
    // cookie
    getStandardizedCookie(STORAGE_COOKIE_GAME_ID) ||
    // storage
    getStorage(STORAGE_LS_GAME_ID) ||
    "";

  return value;
};

export const getStandardNavigatorLang = () => {
  // @ts-ignore
  return STANDARD_CMS_LANG_MAP[getNavigatorLang()];
};
