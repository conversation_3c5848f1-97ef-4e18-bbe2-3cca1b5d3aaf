# Li Pass

[TOC]

## 项目说明

### 1. 项目介绍

NIKKE 独立站（blabla link），是 NIKKE 玩家进行游戏讨论交流的 UGC 社区，用户群体为 NIKKE 游戏玩家，通过 blabla-Link 把玩家和 NIKKE“连接”到了一起，为广大 NIKKE 游戏玩家提供有效的沟通渠道。除内容社区外，blabla link 还提供了游戏工具、优惠充值等服务，帮助游戏玩家能更好的在社区中进行交流

### 2. 快速体验

| 环境 | 地址                           | COS 桶                    |
| ---- | ------------------------------ | ------------------------- |
| 测试 | <https://test.blablalink.com/> | ss-assets-dev-1312254802  |
| 预发 | <https://pre.blablalink.com/>  | ss-assets-pre-1312254802  |
| 正式 | <https://www.blablalink.com/>  | ss-assets-prod-1312254802 |

### 3. 技术选型

- [Vue](https://vuejs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [TailwindCSS](https://tailwindcss.com/)

### 4. 相关地址

#### Nikke 独立站

- [Git: standalone-site-frontend](https://git.woa.com/iegg_distribution/Standalone-Site/standalone-site-frontend)
- [iWiki](https://iwiki.woa.com/p/4012459392)
- [发版记录](https://doc.weixin.qq.com/sheet/e3_AAcASAYHACki3LuNgObT767n1f7lb?scode=AJEAIQdfAAosJOw4KWAAcASAYHACk&tab=BB08J2)
- [重构对齐](https://doc.weixin.qq.com/sheet/e3_AAcASAYHACkA48Kx1U8QOyIo13mqz?scode=AJEAIQdfAAogCsEELaAAcASAYHACk&tab=BB08J2)
- [多语言文档](https://docs.qq.com/sheet/DYkpnWUxBZ0ltdE5y?tab=BB08J2)
- [YApi 接口地址](https://yapi.gpts.woa.com/project/1728/interface/api)
- [独立站 VS LIP 官网对齐](https://doc.weixin.qq.com/sheet/e3_AAcASAYHACkp3vqQiECQr6qCFtdlG?scode=AJEAIQdfAAoGO3BsRjAAcASAYHACk&tab=BB08J2)
- [数据上报](https://doc.weixin.qq.com/sheet/e3_AD8AtgZ1ACcBV0Ih7jdRSOZeUAvH7?scode=AJEAIQdfAAoYp5m46bAD8AtgZ1ACc&tab=gu1ha4)
- [代码扫描](https://codedog.woa.com/code-analysis/repos/710156/projects/3032350/scan-history)
- [Nikke 独立站 openid 白名单](https://doc.weixin.qq.com/sheet/e3_AAcASAYHACkUPyo0SlWSae8HkoD38?scode=AJEAIQdfAAoOyVrzJFAAcASAYHACk&tab=BB08J2)

#### LIP 登录器

- [LIP 登录器换肤接入文档](https://doc.weixin.qq.com/doc/w3_AVUA8gY-AHEPTk4oRfdSQy081CJ2z?scode=AHMA0gcpAAoqljX34yARYAEgYGAEA)

## 上手开发&发布流程

详见：[CONTRIBUTING](./CONTRIBUTING.md)

## 开发约定

详见：[CONVENTIONS](./CONVENTIONS.md)

## FAQ

详见：[FAQ](./FAQ.md)
