# 快速上手

[TOC]

## 安装依赖

> ⚠️⚠️⚠️⚠️ pnpm 的版本控制在 8.15 左右

- 安装所有依赖

```bash
pnpm install
```

外部合作伙伴需要先登录到 [wandnpm register](https://wandnpm.levelinfiniteapps.com/) 再执行安装操作，执行流程如下：

```bash
pnpm login --registry https://wandnpm.levelinfiniteapps.com/
# 然后输入账号密码
pnpm i  --registry https://wandnpm.levelinfiniteapps.com/
```

## 启动应用

> 可以进入到各自是项目执行 **dev** 脚本命令

- 启动所有应用

```bash
pnpm dev
```

- **如果需要**启动某个应用：`pnpm --filter [name] dev`

```bash
# 启动 nikke
pnpm --filter nikke dev
```

## 代理

### 先决条件

- [Whistle](https://wproxy.org/whistle/install.html)
- [Proxy SwitchyOmega](https://chrome.google.com/webstore/detail/proxy-switchyomega/padekgcemlokbadohgkifijomclgjgif)

  - 新增 `PAC` 模式：`work`，配置如下

  ```js
  const ignore = [];
  function FindProxyForURL(url, host) {
    if (ignore.some((item) => host.includes(item))) {
      return "PROXY 127.0.0.1:12639";
    }
    return "PROXY 127.0.0.1:8899";
  }
  ```

### Whistle 配置

```bash
test.blablalink.com 127.0.0.1:9173
```

在浏览器端选择 `SwitchyOmega` 的 `work` 的代理，然后访问 `https://test.blablalink.com/` 即可。

### 修改默认代理地址

- 接口默认会被代理到 test 环境的域名: `https://test-api.blablalink.com`
- 如果希望更改默认代理域名, 在项目的 [`.env.development` 文件](./packages/nikke/env/.env.development)中调整变量 `VITE_PROXY_URL`

## 开发

### 创建分支

按照 [CONVENTIONS](./CONVENTIONS.md) 中的 **版本迭代** 创建分支进行开发

## 多语言

- 在线文档地址：[多语言](https://docs.qq.com/sheet/DYkpnWUxBZ0ltdE5y?tab=BB08J2)（需要申请编辑权限）
- 采用 [@tencent/i18n-cli](https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Fi18n-cli&search_label=package_name&search_value=i18n-cli&page_num=1) 工具进行 `push/pull` 的对齐——本地多语言配置和在线多语言配置的同步

```bash
# 会翻译更新所有应用的多语言
pnpm i18n
```

### 多语言更新流程

> ⚠️⚠️⚠️⚠️ **多语言的修改不要在本地修改**，统一到线上文档修改后拉回到本地覆盖

#### 开发阶段

- 明确是在哪个应用进行多语言的开发，比如：nikke
- 新增某个多语言的 `key`，比如：`purchase_limit_day`，给这个 `key` 加上某种语言翻译，比如 `zh`：`每日限购`
- 本地执行 `pnpm i18n` 进行翻译和本地语言文件的同步

#### 运营更改多语言

> 前提开发阶段的多语言依赖 AI 翻译，需要运营以更贴切的翻译

- 当我们定义好开发阶段的所需所有 `key` 之后，通知运营同学去做语言翻译
- 运营同学之后会在多语言文档做更新并告知开发
- 开发本地执行 `pnpm i18n`

## 发布

### 代码发布

#### 测试环境

代码提交到某个分支，比如 `dev` 分支，然后到蓝盾 **测试流水线** 进行发布即可

- ~~[【前端 TEST-GPTS】Nikke 独立站](https://devops.woa.com/console/pipeline/wgwebglobal/p-b458e042df8840f98e127b7f52946ea4/history/history/6?page=1&pageSize=20)~~
- [【前端 TEST-GPTS】Nikke 独立站](https://devops.woa.com/console/pipeline/wgwebglobal/p-030776b86a9e4fc8a8f0ad99abbee0c7/history/history/27?page=1&pageSize=20)

#### 预发布

把代码合入到 `pre` 分支，然后到蓝盾 **预发布流水线** 进行发布即可

- [~~【前端 PRE-GPTS】Nikke 独立站~~](https://devops.woa.com/console/pipeline/wgwebglobal/p-203150eec0ed4d148416074c76ae39b7/history/history/10?page=1&pageSize=20)
- [【前端 PRE-GPTS】Nikke 独立站](https://devops.woa.com/console/pipeline/wgwebglobal/p-8d25750fb9b44f4ca3f429e1d1019d2f/history/history/4?page=1&pageSize=20)

#### 正式环境发布

把 `pre` 分支的代码合入到 `master` 分支，打版本 `tag`，然后到蓝盾 **正式环境流水线** 进行发布即可

##### 修改项目版本 & 打 tag

```bash
# 1.0.0 根据实际情况调整
pnpm bump -v 1.0.0
```

##### 执行流水线构建发布

- [~~【！！！前端 PROD-GPTS！！！】独立站~~](https://devops.woa.com/console/pipeline/wgwebglobal/p-a2d8228e1ff34369a8193d6342cc1e51/history/history/5?page=1&pageSize=20)
- [【！！！前端 PROD-GPTS！！！】独立站](https://devops.woa.com/console/pipeline/wgwebglobal/p-123c758ca14e45acb860262602f05590/history/history/5?page=1&pageSize=20)

### 配置文件发布

- [【前端 CDN 配置】独立站前端 CDN 配置](https://devops.woa.com/console/pipeline/wgwebglobal/p-471d817bd8ed4061bc89b3b1bc176416/history/history/4?page=1&pageSize=20)
