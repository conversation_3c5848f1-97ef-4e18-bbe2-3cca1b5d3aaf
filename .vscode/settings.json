{
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "files.eol": "\n",
  "editor.rulers": [120],
  "editor.formatOnSave": true,
  "eslint.format.enable": true,
  "eslint.workingDirectories": [
    // Monorepo directories
    { "pattern": "./packages/*/" }
  ],
  "eslint.options": {
    "configFile": "./**/.eslintrc.js"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.guides.bracketPairs": true,
  "editor.bracketPairColorization.enabled": true,
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"],

  // @link https://github.com/lokalise/i18n-ally/wiki/FAQ#-advanced-folder-directory-configurations
  "i18n-ally.localesPaths": ["./packages/**/**/locales/lang"],
  "i18n-ally.pathMatcher": "{locale}.ts",
  "i18n-ally.enabledFrameworks": ["vue", "vue-sfc"],
  "i18n-ally.enabledParsers": ["js", "ts"],
  "i18n-ally.keystyle": "nested",

  "[postcss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "javascript.validate.enable": false,
  "[ini]": {
    "editor.defaultFormatter": "lkrms.inifmt"
  },
  "todohighlight.keywords": [
    {
      "text": "TODO:",
      "color": "#ffffff",
      "backgroundColor": "#ffbd29"
    },
    {
      "text": "FIXME:",
      "color": "#ffffff",
      "backgroundColor": "#f06293"
    },
    {
      "text": "NOTE:",
      "color": "#ffffff",
      "backgroundColor": "#40affe"
    }
  ]
}
